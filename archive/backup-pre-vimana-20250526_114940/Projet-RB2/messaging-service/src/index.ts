import express, { Response as ExpressResponse, NextFunction, Request } from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { setupMessageHandlers } from "./handlers/messageHandlers.js";
import { setupAuthHandlers } from "./handlers/authHandlers.js";
import { setupGroupHandlers } from "./handlers/groupHandlers.js";
import { setupFileHandlers } from "./handlers/fileHandlers.js";
import { verifyToken, AuthenticatedRequest } from "./middleware/auth.js";
import { rateLimiter } from "./middleware/rateLimiter.js";
import { logger } from "./utils/logger.js";
import { connectDatabase } from "./config/database.js";
import { SocialIntegrationService } from "./services/SocialIntegrationService.js";

dotenv.config();

const app = express();
const httpServer = createServer(app);

// Security middleware;
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());
app.use(rateLimiter);

// WebSocket server setup with security configurations;
const io = new Server(httpServer, {
  cors: {
    origin: process.env.ALLOWED_ORIGINS?.split(',') || 'http://localhost:3000',
    methods: ['GET', 'POST'],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  transports: ['websocket', 'polling']
});

// Socket.IO middleware for authentication;
io.use((socket, next) => {
  const token = socket.handshake.auth.token;
  try {
    // Create a mock request object for verifyToken;
    const mockReq = { headers: { authorization: `Bearer ${token}` } } as AuthenticatedRequest;
    const mockRes = {
      status: () => ({ json: () => ({}) })
    } as unknown as ExpressResponse;

    // Create a custom next function that matches the NextFunction signature;
    const customNext: NextFunction = (err?: any) => {
      if (err) {
        next(new Error(err.message || 'Authentication error'));
      } else {
        // Assign the authenticated user to socket data;
        socket.data.user = mockReq.user;
        next();
      }
    };

    verifyToken(mockReq, mockRes, customNext);

  } catch(error) {
    next(new Error('Authentication error'));
  }
});

// Set up WebSocket event handlers;
io.on('connection', (socket) => {
  // Securely log user connection
  if (socket.data.user && socket.data.user.id) {
    logger.info(`User connected: ${socket.data.user.id}`);
  } else {
    logger.warn('User connected with incomplete data or authentication might have failed.');
    // Optionally, you might want to disconnect if authentication is strictly required and failed:
    // socket.disconnect(true);
    // return;
  }

  // Set up message, group, file, and authentication handlers;
  setupMessageHandlers(io, socket);
  setupGroupHandlers(io, socket);
  setupFileHandlers(io, socket);
  setupAuthHandlers(socket); // Fix: setupAuthHandlers only expects one argument;
  socket.on('disconnect', () => {
    logger.info(`User disconnected: ${socket.data.user.id}`);
  });

  socket.on('error', (error) => {
    logger.error('Socket error:', error);
  });
});

// Health check endpoint;
app.get('/health', (req: express.Request, res: express.Response): void => {
  res.status(200).json({ status: 'ok', });
});

// Initialize the Social Integration Service
const socialIntegrationService = SocialIntegrationService.getInstance();
socialIntegrationService.initialize(app);

// Subscribe to social integration events
const socialEventEmitter = socialIntegrationService.getEventEmitter();

// Forward social events to Socket.IO clients
socialEventEmitter.on('livestream:notification', (notification) => {
  io.emit('livestream:notification', notification);
  logger.info(`Broadcasting livestream notification: ${notification.eventType}`);
});

socialEventEmitter.on('livestream:message', (message) => {
  io.emit('livestream:message', message);
  logger.info(`Broadcasting livestream message from: ${message.userName}`);
});

socialEventEmitter.on('user:notification', ({ userId, notification }) => {
  // Find all sockets for this user and send them the notification
  const userSockets = Array.from(io.sockets.sockets.values())
    .filter(socket => socket.data.user && socket.data.user.id === userId);

  userSockets.forEach(socket => {
    socket.emit('notification', notification);
  });

  logger.info(`Sent notification to user ${userId} on ${userSockets.length} sockets`);
});

// Global error handler for Express (should be after all routes and middleware)
app.use((err: Error, req: Request, res: ExpressResponse, next: NextFunction) => {
  logger.error('Unhandled Express error:', {
    message: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method
  });

  // Avoid sending stack trace to client in production for security reasons
  const isProduction = process.env.NODE_ENV === 'production';

  // Check if headers have already been sent
  if (res.headersSent) {
    return next(err); // Delegate to default Express error handler
  }

  res.status((err as any).statusCode || 500).json({
    message: (err as any).expose || isProduction ? err.message : 'An unexpected error occurred on the server.',
    // Optionally include stack in development
    ...( !isProduction && err.stack && { stack: err.stack } )
  });
});

// Connect to MongoDB and start server;
const PORT = process.env.PORT || 3002;

connectDatabase().then(() => {
  httpServer.listen(PORT, () => {
    logger.info(`Server is running on port ${PORT}`);
  });
}).catch((error: Error) => {
  logger.error('Failed to start server:', error);
  process.exit(1);
});

// Graceful shutdown;
process.on('SIGTERM', () => {
  logger.info('SIGTERM received. Shutting down gracefully...');
  httpServer.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});