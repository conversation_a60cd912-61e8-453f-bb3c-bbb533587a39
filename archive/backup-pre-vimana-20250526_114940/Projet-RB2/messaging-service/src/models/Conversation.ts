import mongoose, { Schema, Document } from 'mongoose';

export interface IConversation extends Document {
  participants: string[];
  lastMessage?: Schema.Types.ObjectId;
  lastActivity: Date;
  isGroup: boolean;
  groupName?: string;
  groupAvatar?: string;
  createdBy: string;
  unreadCounts: Map<string, number>;
}

const conversationSchema = new Schema<IConversation>(;
  {
    participants: [{
      type: String,
      required: true;
    }],
    lastMessage: {
      type: Schema.Types.ObjectId,
      ref: 'Message'
    },
    lastActivity: {
      type: Date,
      default: Date.now;
    },
    isGroup: {
      type: Boolean,
      default: false;
    },
    groupName: {
      type: String;
    },
    groupAvatar: {
      type: String;
    },
    createdBy: {
      type: String,
      required: true;
    },
    unreadCounts: {
      type: Map,
      of: Number,
      default: new Map()
    }
  },
  {
    timestamps: true;
  }
);

// Indexes for efficient querying;
conversationSchema.index({ participants: 1 });
conversationSchema.index({ lastActivity: -1 });

// Method to update unread count for a participant;
conversationSchema.methods.incrementUnreadCount = function(participantId: string) {
  const currentCount = this.unreadCounts.get(participantId) || 0;
  this.unreadCounts.set(participantId, currentCount + 1);
  return this.save();
};

// Method to reset unread count for a participant;
conversationSchema.methods.resetUnreadCount = function(participantId: string) {
  this.unreadCounts.set(participantId, 0);
  return this.save();
};

export const Conversation = mongoose.model<IConversation>('Conversation', conversationSchema);