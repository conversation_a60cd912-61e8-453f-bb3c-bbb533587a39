import { Router } from 'express';
import { SecureMessagingController } from '../controllers/SecureMessagingController';

const router = Router();
const controller = new SecureMessagingController();

// Routes pour l'initialisation des utilisateurs
router.post('/users/:userId/initialize', controller.initializeUser);
router.delete('/users/:userId', controller.deleteUser);

// Routes pour les sessions
router.post('/sessions', controller.establishSession);

// Routes pour les messages
router.post('/messages', controller.sendMessage);
router.get('/messages/:messageId/users/:userId', controller.receiveMessage);

// Routes pour les conversations
router.post('/conversations', controller.createConversation);
router.get('/conversations/users/:userId', controller.getConversations);
router.get('/conversations/:conversationId/users/:userId/messages', controller.getConversationMessages);
router.delete('/conversations/:conversationId', controller.deleteConversation);

export default router;
