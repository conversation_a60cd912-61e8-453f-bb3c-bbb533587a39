import { Request, Response } from 'express';
import { SecureMessagingService, Message, Conversation } from '../services/SecureMessagingService';
import { logger } from '../utils/logger';

/**
 * Contrôleur pour la messagerie sécurisée
 */
export class SecureMessagingController {
  private messagingService: SecureMessagingService;

  constructor() {
    this.messagingService = SecureMessagingService.getInstance();
  }

  /**
   * Initialise un utilisateur
   * @param req Requête HTTP
   * @param res Réponse HTTP
   */
  public initializeUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      
      if (!userId) {
        res.status(400).json({ error: 'User ID is required' });
        return;
      }
      
      const publicKeys = this.messagingService.initializeUser(userId);
      
      res.status(200).json({
        message: 'User initialized successfully',
        publicKeys
      });
    } catch (error) {
      logger.error('Error initializing user:', error);
      res.status(500).json({ error: 'Failed to initialize user' });
    }
  };

  /**
   * Établit une session sécurisée entre deux utilisateurs
   * @param req Requête HTTP
   * @param res Réponse HTTP
   */
  public establishSession = async (req: Request, res: Response): Promise<void> => {
    try {
      const { senderId, recipientId, recipientPublicKeys } = req.body;
      
      if (!senderId || !recipientId || !recipientPublicKeys) {
        res.status(400).json({ error: 'Sender ID, recipient ID, and recipient public keys are required' });
        return;
      }
      
      const sessionId = this.messagingService.establishSession(
        senderId,
        recipientId,
        recipientPublicKeys
      );
      
      res.status(200).json({
        message: 'Secure session established successfully',
        sessionId
      });
    } catch (error) {
      logger.error('Error establishing session:', error);
      res.status(500).json({ error: 'Failed to establish secure session' });
    }
  };

  /**
   * Envoie un message sécurisé
   * @param req Requête HTTP
   * @param res Réponse HTTP
   */
  public sendMessage = async (req: Request, res: Response): Promise<void> => {
    try {
      const { senderId, recipientId, content, type, metadata, attachments } = req.body;
      
      if (!senderId || !recipientId || !content) {
        res.status(400).json({ error: 'Sender ID, recipient ID, and content are required' });
        return;
      }
      
      const message = await this.messagingService.sendMessage(
        senderId,
        recipientId,
        content,
        type || 'text',
        metadata,
        attachments
      );
      
      res.status(200).json({
        message: 'Message sent successfully',
        data: message
      });
    } catch (error) {
      logger.error('Error sending message:', error);
      res.status(500).json({ error: 'Failed to send message' });
    }
  };

  /**
   * Récupère un message
   * @param req Requête HTTP
   * @param res Réponse HTTP
   */
  public receiveMessage = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId, messageId } = req.params;
      
      if (!userId || !messageId) {
        res.status(400).json({ error: 'User ID and message ID are required' });
        return;
      }
      
      const message = await this.messagingService.receiveMessage(userId, messageId);
      
      if (!message) {
        res.status(404).json({ error: 'Message not found' });
        return;
      }
      
      res.status(200).json({
        message: 'Message received successfully',
        data: message
      });
    } catch (error) {
      logger.error('Error receiving message:', error);
      res.status(500).json({ error: 'Failed to receive message' });
    }
  };

  /**
   * Récupère les messages d'une conversation
   * @param req Requête HTTP
   * @param res Réponse HTTP
   */
  public getConversationMessages = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId, conversationId } = req.params;
      
      if (!userId || !conversationId) {
        res.status(400).json({ error: 'User ID and conversation ID are required' });
        return;
      }
      
      const messages = await this.messagingService.getConversationMessages(userId, conversationId);
      
      res.status(200).json({
        message: 'Conversation messages retrieved successfully',
        data: messages
      });
    } catch (error) {
      logger.error('Error getting conversation messages:', error);
      res.status(500).json({ error: 'Failed to get conversation messages' });
    }
  };

  /**
   * Récupère les conversations d'un utilisateur
   * @param req Requête HTTP
   * @param res Réponse HTTP
   */
  public getConversations = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      
      if (!userId) {
        res.status(400).json({ error: 'User ID is required' });
        return;
      }
      
      const conversations = this.messagingService.getConversations(userId);
      
      res.status(200).json({
        message: 'Conversations retrieved successfully',
        data: conversations
      });
    } catch (error) {
      logger.error('Error getting conversations:', error);
      res.status(500).json({ error: 'Failed to get conversations' });
    }
  };

  /**
   * Crée une nouvelle conversation
   * @param req Requête HTTP
   * @param res Réponse HTTP
   */
  public createConversation = async (req: Request, res: Response): Promise<void> => {
    try {
      const { participants, metadata } = req.body;
      
      if (!participants || !Array.isArray(participants) || participants.length < 2) {
        res.status(400).json({ error: 'At least two participants are required' });
        return;
      }
      
      const conversation = this.messagingService.createConversation(participants, metadata);
      
      res.status(201).json({
        message: 'Conversation created successfully',
        data: conversation
      });
    } catch (error) {
      logger.error('Error creating conversation:', error);
      res.status(500).json({ error: 'Failed to create conversation' });
    }
  };

  /**
   * Supprime une conversation
   * @param req Requête HTTP
   * @param res Réponse HTTP
   */
  public deleteConversation = async (req: Request, res: Response): Promise<void> => {
    try {
      const { conversationId } = req.params;
      
      if (!conversationId) {
        res.status(400).json({ error: 'Conversation ID is required' });
        return;
      }
      
      this.messagingService.deleteConversation(conversationId);
      
      res.status(200).json({
        message: 'Conversation deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting conversation:', error);
      res.status(500).json({ error: 'Failed to delete conversation' });
    }
  };

  /**
   * Supprime un utilisateur
   * @param req Requête HTTP
   * @param res Réponse HTTP
   */
  public deleteUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      
      if (!userId) {
        res.status(400).json({ error: 'User ID is required' });
        return;
      }
      
      this.messagingService.deleteUser(userId);
      
      res.status(200).json({
        message: 'User deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting user:', error);
      res.status(500).json({ error: 'Failed to delete user' });
    }
  };
}
