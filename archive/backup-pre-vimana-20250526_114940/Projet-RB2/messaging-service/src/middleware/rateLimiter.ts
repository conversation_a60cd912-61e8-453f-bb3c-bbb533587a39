import rateLimit from 'express-rate-limit';
import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger.js';

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes;
  max: 100, // limit each IP to 100 requests per windowMs;
  message: 'Too many requests from this IP, please try again later',
  handler: (req, res) => {
    logger.warn(`Rate limit exceeded for(IP $) { {req.ip}`)}
    res.status(429).json({
      message: 'Too many requests from this IP, please try again later'
    });
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers;
  legacyHeaders: false // Disable the `X-RateLimit-*` headers;
});

// Wrapper function to handle type issues with explicit any casting;
export const rateLimiter = (req: any, res: any, next: any) => {
  return limiter(req, res, next);
};