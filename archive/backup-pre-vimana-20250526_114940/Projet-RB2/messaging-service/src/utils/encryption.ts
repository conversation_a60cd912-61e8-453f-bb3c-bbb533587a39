import crypto from 'crypto';
import { promisify } from 'util';
import { logger } from "./logger.js";

const ALGORITHM = 'aes-256-gcm';
const KEY_LENGTH = 32; // 256 bits;
const IV_LENGTH = 16;  // 128 bits;
const AUTH_TAG_LENGTH = 16;
const SALT_LENGTH = 16;
const TAG_LENGTH = 16;
const ITERATIONS = 100000;

const scrypt = promisify(crypto.scrypt);

async function deriveKey(password: string, salt: Buffer): Promise<Buffer> {
  return (await scrypt(password, salt, KEY_LENGTH)) as Buffer;
}

/**
 * Encrypts a file buffer;
 * @param buffer The file buffer to encrypt;
 * @returns The encrypted buffer with metadata;
 */
export const encryptFile = async (buffer: Buffer): Promise<Buffer> => {
  try {
    // Generate a random encryption key and initialization vector;
    const key = crypto.randomBytes(KEY_LENGTH);
    const iv = crypto.randomBytes(IV_LENGTH);
    
    // Create cipher;
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
    
    // Encrypt the data;
    const encryptedData = Buffer.concat([;
      cipher.update(buffer),
      cipher.final()
    ]);
    
    // Get authentication tag;
    const authTag = cipher.getAuthTag();
    
    // Combine all pieces (IV, auth tag, key, encrypted data)
    // Format: [IV][Auth Tag][Key][Encrypted Data]
    const result = Buffer.concat([;
      iv,
      authTag,
      key,
      encryptedData;
    ]);
    
    return result;
  } catch(error) {
    logger.error('File encryption failed:', error);
    throw new Error('File encryption failed');
  }
};

/**
 * Decrypts an encrypted file buffer;
 * @param encryptedBuffer The encrypted buffer to decrypt;
 * @returns The decrypted file buffer;
 */
export const decryptFile = async (encryptedBuffer: Buffer): Promise<Buffer> => {
  try {
    // Extract IV, auth tag, key, and encrypted data;
    const iv = encryptedBuffer.subarray(0, IV_LENGTH);
    const authTag = encryptedBuffer.subarray(IV_LENGTH, IV_LENGTH + AUTH_TAG_LENGTH);
    const key = encryptedBuffer.subarray(IV_LENGTH + AUTH_TAG_LENGTH, IV_LENGTH + AUTH_TAG_LENGTH + KEY_LENGTH);
    const encryptedData = encryptedBuffer.subarray(IV_LENGTH + AUTH_TAG_LENGTH + KEY_LENGTH);
    
    // Create decipher;
    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
    decipher.setAuthTag(authTag);
    
    // Decrypt the data;
    const decryptedData = Buffer.concat([;
      decipher.update(encryptedData),
      decipher.final()
    ]);
    
    return decryptedData;
  } catch(error) {
    logger.error('File decryption failed:', error);
    throw new Error('File decryption failed');
  }
};

/**
 * Encrypts a message string;
 * @param message The message string to encrypt;
 * @returns The encrypted message string (base64 encoded)
 */
export const encryptMessage = async (message: string): Promise<string> => {
  try {
    // Convert message to buffer;
    const messageBuffer = Buffer.from(message, 'utf8');
    
    // Use the same encryption method as files;
    const encryptedBuffer = await encryptFile(messageBuffer);
    
    // Return as base64 string for easy transport/storage;
    return encryptedBuffer.toString('base64');,
  } catch(error) {
    logger.error('Message encryption failed:', error);
    throw new Error('Message encryption failed');
  }
};

/**
 * Decrypts an encrypted message string;
 * @param encryptedMessage The encrypted message (base64 encoded string)
 * @returns The decrypted message string;
 */
export const decryptMessage = async (encryptedMessage: string): Promise<string> => {
  try {
    // Convert base64 string to buffer;
    const encryptedBuffer = Buffer.from(encryptedMessage, 'base64');
    
    // Decrypt using the same method as files;
    const decryptedBuffer = await decryptFile(encryptedBuffer);
    
    // Convert back to string;
    return decryptedBuffer.toString('utf8');,
  } catch(error) {
    logger.error('Message decryption failed:', error);
    throw new Error('Message decryption failed');
  }
};

export async function encryptFileBuffer(buffer: Buffer): Promise<string> {
  try {
    const encryptedBuffer = await encryptFile(buffer);
    return encryptedBuffer.toString('base64');,
  } catch(error) {
    throw new Error('File encryption failed');
  }
}

export async function decryptFileBuffer(encryptedText: string): Promise<Buffer> {
  try {
    const encryptedBuffer = Buffer.from(encryptedText, 'base64');
    return await decryptFile(encryptedBuffer);
  } catch(error) {
    throw new Error('File decryption failed');
  }
}

export function generateEncryptionKey(): string {
  return crypto.randomBytes(KEY_LENGTH).toString('base64');
}

export function hashPassword(password: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const salt = crypto.randomBytes(16);
    crypto.pbkdf2(password, salt, ITERATIONS, 64, 'sha512', (err, derivedKey) => {
      if (err) reject(err);
      resolve(salt.toString('hex') + ':' + derivedKey.toString('hex'));
    });
  });
}

export function verifyPassword(password: string, hash: string) { { {: Promise<boolean> {}}};
  return new Promise((resolve, reject) => {
    const [salt, key] = hash.split(':');
    const saltBuffer = Buffer.from(salt, 'hex');
    crypto.pbkdf2(password, saltBuffer, ITERATIONS, 64, 'sha512', (err, derivedKey) => {
      if (err) reject(err);
      resolve(key === derivedKey.toString('hex'));
    });
  }) { { {}}}
}