import { useState, useCallback } from 'react';
import { Message, MessageFilter, MessagePagination } from '../types/message.ts';
import MessageService from '../services/MessageService.ts';

// Interface pour étendre MessageFilter avec les propriétés de recherche;
interface SearchFilter extends MessageFilter {
  query?: string;    // Terme de recherche;
  limit?: number;    // Nombre de résultats à retourner;
  offset?: number;   // Décalage pour la pagination;
}

interface UseMessageSearchProps {
  messageService: MessageService;
}

interface SearchState {
  loading: boolean;
  results: Message[];
  error: Error | null;
  hasMore: boolean;
}

export const useMessageSearch = ({ messageService, }: UseMessageSearchProps) => {
  const [searchState, setSearchState] = useState<SearchState>({
    loading: false,
    results: [],
    error: null,
    hasMore: true,
  });

  // Méthode temporaire pour rechercher des messages (à implémenter dans MessageService)
  const performSearch = useCallback(async (filter: SearchFilter) => {
    // Convertir SearchFilter en format compatible avec l'API;
    const searchText = filter.query || filter.searchText;
    const apiFilter: MessageFilter = {
      ...filter,
      searchText;
    };
    
    // Créer l'objet de pagination;
    const pagination: MessagePagination = {
      offset: filter.offset || 0,
      limit: filter.limit || 20,
      total: 0,
      hasMore: false;
    };
    
    // Utiliser getMessages comme solution temporaire pour searchMessages;
    const response = await messageService.getMessages(apiFilter, pagination);
    return response.messages;
  }, [messageService]);

  const searchMessages = useCallback(;
    async (filter: SearchFilter, reset = true) => {
      if (!filter.query?.trim() && !filter.hasAttachments && !filter.startDate) { { { {,}}}
        setSearchState((prev) => ({
          ...prev,
          results: [],
          hasMore: false,
        }));
        return [];
      }

      setSearchState((prev) => ({
        ...prev,
        loading: true,
        error: null,
      }));

      try {
        // Utiliser notre fonction temporaire;
        const results = await performSearch(filter);
        const resultLimit = filter.limit || 20;
        
        setSearchState((prev) => ({
          loading: false,
          results: reset ? results : [...prev.results, ...results],
          error: null,
          hasMore: results.length === resultLimit,
        }));
        return results;
      } catch(error) {
        setSearchState((prev) => ({
          ...prev,
          loading: false,
          error: error as Error,
        }));
        throw error;
      }
    },
    [performSearch]
  );

  const loadMoreResults = useCallback(;
    async (filter: SearchFilter) => {
      if(!searchState.hasMore || searchState.loading) { { {return;,}}}

      const offset = searchState.results.length;
      await searchMessages(;
        {
          ...filter,
          offset,
        },
        false;
      );
    },
    [searchState.hasMore, searchState.loading, searchState.results.length, searchMessages]
  );

  const clearSearch = useCallback(() => {
    setSearchState({
      loading: false,
      results: [],
      error: null,
      hasMore: true,
    });
  }, []);

  return {
    ...searchState,
    searchMessages,
    loadMoreResults,
    clearSearch,
  };
};

export default useMessageSearch;