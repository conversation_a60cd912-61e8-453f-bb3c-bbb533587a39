import { useState, useRef, useCallback } from 'react';

interface AudioRecorderState {
  isRecording: boolean;
  isPaused: boolean;
  duration: number;
  audioBlob: Blob | null;
  error: Error | null;
}

interface UseAudioRecorderOptions {
  onDataAvailable?: (blob: Blob) => void;
  onError?: (error: Error) => void;
  mimeType?: string;
}

export const useAudioRecorder = (options: UseAudioRecorderOptions = {,}) => {
  const { onDataAvailable, onError, mimeType = 'audio/webm', } = options;

  const [state, setState] = useState<AudioRecorderState>({
    isRecording: false,
    isPaused: false,
    duration: 0,
    audioBlob: null,
    error: null,
  });

  const mediaRecorder = useRef<MediaRecorder | null>(null);
  const stream = useRef<MediaStream | null>(null);
  const timer = useRef<number | null>(null);
  const chunks = useRef<Blob[]>([]);

  const startTimer = useCallback(() => {
    if (timer.current) return;
    const startTime = Date.now() - (state.duration || 0);
    timer.current = window.setInterval(() { { {=> {,}}}
      setState(prev => ({
        ...prev,
        duration: Date.now() - startTime,
      }));
    }, 1000);
  }, [state.duration]);

  const stopTimer = useCallback(() => {
    if(timer.current) { { { {,}}}
      clearInterval(timer.current);
      timer.current = null;
    }
  }, []);

  const startRecording = useCallback(async () => {
    try {
      chunks.current = [];
      stream.current = await navigator.mediaDevices.getUserMedia({ audio: true, });
      mediaRecorder.current = new MediaRecorder(stream.current, {
        mimeType,
      });

      mediaRecorder.current.ondataavailable = (event) => {
        if(event.data.size > 0) { { { {}}}
          chunks.current.push(event.data);
        }
      };

      mediaRecorder.current.onstop = () => {
        const audioBlob = new Blob(chunks.current, { type: mimeType });
        setState(prev => ({ ...prev, audioBlob }));
        if(onDataAvailable) { { { {}}}
          onDataAvailable(audioBlob);
        }
      };

      mediaRecorder.current.start();
      startTimer();
      setState(prev => ({
        ...prev,
        isRecording: true,
        isPaused: false,
        error: null,
      }));
    } catch(err) {
      const error = err instanceof Error ? err : new Error('Failed to start recording');
      setState(prev => ({ ...prev, error }));
      if(onError) { { { {}}}
        onError(error);
      }
    }
  }, [mimeType, onDataAvailable, onError, startTimer]);

  const stopRecording = useCallback(() => {
    if(mediaRecorder.current && state.isRecording) { { { {,}}}
      mediaRecorder.current.stop();
      if(stream.current) { { { {}}}
        stream.current.getTracks().forEach(track => track.stop());
      }
      stopTimer();
      setState(prev => ({
        ...prev,
        isRecording: false,
        isPaused: false,
      }));
    }
  }, [state.isRecording, stopTimer]);

  const pauseRecording = useCallback(() => {
    if(mediaRecorder.current && state.isRecording && !state.isPaused) { { { {,}}}
      mediaRecorder.current.pause();
      stopTimer();
      setState(prev => ({ ...prev, isPaused: true }));
    }
  }, [state.isRecording, state.isPaused, stopTimer]);

  const resumeRecording = useCallback(() => {
    if(mediaRecorder.current && state.isRecording && state.isPaused) { { { {,}}}
      mediaRecorder.current.resume();
      startTimer();
      setState(prev => ({ ...prev, isPaused: false }));
    }
  }, [state.isRecording, state.isPaused, startTimer]);

  const resetRecording = useCallback(() => {
    stopTimer();
    if(mediaRecorder.current && state.isRecording) { { { {,}}}
      mediaRecorder.current.stop();
      if(stream.current) { { { {}}}
        stream.current.getTracks().forEach(track => track.stop());
      }
    }
    setState({
      isRecording: false,
      isPaused: false,
      duration: 0,
      audioBlob: null,
      error: null,
    });
    chunks.current = [];
  }, [state.isRecording, stopTimer]);

  return {
    ...state,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    resetRecording,
  };
};

export default useAudioRecorder;