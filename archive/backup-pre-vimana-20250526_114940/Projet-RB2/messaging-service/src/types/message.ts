export interface User {
  id: string;
  name: string;
  avatar?: string;
  status: 'online' | 'offline' | 'away';
  lastSeen?: Date;
}

export enum MessageStatus {
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
}

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  recipientId: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'audio' | 'voice';
  status: MessageStatus;
  createdAt: Date;
  updatedAt?: Date;
  attachments?: MessageAttachment[];
  replyTo?: string;
  reactions?: MessageReaction[];
  voiceMessageUrl?: string;
  voiceMessageDuration?: number;
  transcription?: string;
}

export interface MessageAttachment {
  id: string;
  type: 'image' | 'file' | 'audio' | 'voice';
  url: string;
  name: string;
  size: number;
  mimeType: string;
  thumbnailUrl?: string;
  duration?: number;
  waveform?: number[];
}

export interface MessageReaction {
  userId: string;
  emoji: string;
  createdAt: Date;
}

export interface Conversation {
  id: string;
  type: 'direct' | 'group';
  name?: string;
  participants: User[];
  lastMessage?: Message;
  unreadCount: number;
}

export interface ConversationMember {
  userId: string;
  conversationId: string;
  role: 'admin' | 'member';
  joinedAt: Date;
  lastReadMessageId?: string;
}

export interface MessageDraft {
  conversationId: string;
  content: string;
  attachments?: File[];
  replyToMessageId?: string;
  type?: Message['type'];
  voiceMessageUrl?: string;
  voiceMessageDuration?: number;
  recipientPublicKey?: string;
  encryption?: {
    iv: string;
    authTag: string;
  };
}

export interface MessageFilter {
  conversationId?: string;
  senderId?: string;
  startDate?: Date;
  endDate?: Date;
  type?: Message['type'];
  hasAttachments?: boolean;
  searchText?: string;
}

export interface ConversationFilter {
  type?: Conversation['type'];
  participantId?: string;
  searchText?: string;
  isArchived?: boolean;
  isPinned?: boolean;
}

export interface MessagePagination {
  offset: number;
  limit: number;
  total: number;
  hasMore: boolean;
}
