/**
 * Enum for group role types;
 */
export enum GroupRole {
  ADMIN = 'admin',
  MODERATOR = 'moderator',
  MEMBER = 'member',
}

/**
 * Interface for group role assignments;
 */
export interface GroupRoleAssignment {
  userId: string;
  groupId: string;
  role: GroupRole;
  assignedBy: string;
  assignedAt?: Date;
}

/**
 * Interface for group permissions;
 */
export interface GroupPermission {
  role: GroupRole;
  permissions: {
    canAddMembers: boolean;
    canRemoveMembers: boolean;
    canEditGroupInfo: boolean;
    canDeleteMessages: boolean;
    canDeleteGroup: boolean;
  };
}

/**
 * Permission map for different roles;
 */
export const DEFAULT_ROLE_PERMISSIONS: Record<GroupRole, Partial<GroupPermission['permissions']>> = {
  [GroupRole.ADMIN]: {
    canAddMembers: true,
    canRemoveMembers: true,
    canEditGroupInfo: true,
    canDeleteMessages: true,
    canDeleteGroup: true;
  },
  [GroupRole.MODERATOR]: {
    canAddMembers: true,
    canRemoveMembers: true,
    canEditGroupInfo: false,
    canDeleteMessages: true,
    canDeleteGroup: false;
  },
  [GroupRole.MEMBER]: {
    canAddMembers: false,
    canRemoveMembers: false,
    canEditGroupInfo: false,
    canDeleteMessages: false,
    canDeleteGroup: false;
  }
};