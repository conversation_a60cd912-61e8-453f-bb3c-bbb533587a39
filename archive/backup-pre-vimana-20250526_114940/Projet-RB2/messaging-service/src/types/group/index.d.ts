export interface Group {
  id: string;
  name: string;
  description?: string;
  members: string[];
  admins: string[];
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface GroupCreateRequest {
  name: string;
  description?: string;
  members: string[];
}

export interface GroupUpdateRequest {
  name?: string;
  description?: string;
  members?: string[];
  admins?: string[];
}