/**
 * Enum for file status in the messaging system;
 */
export enum FileStatus {
  PENDING = 'pending',
  UPLOADING = 'uploading',
  UPLOADED = 'uploaded',
  FAILED = 'failed',
  DELETED = 'deleted',
}

/**
 * Interface for file messages in the messaging system;
 */
export interface FileMessage {
  id: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  senderId: string;
  receiverId: string;
  conversationId: string;
  status: FileStatus;
  encryptedData?: string;
  thumbnailData?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface for file upload progress in the messaging system;
 */
export interface FileProgress {
  fileId: string;
  progress: number; // 0-100;
  status: FileStatus;
}