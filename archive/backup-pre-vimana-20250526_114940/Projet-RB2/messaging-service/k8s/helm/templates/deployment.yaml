apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}-messaging
  labels:
    app.kubernetes.io/name: {{ .Release.Name }}-messaging
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/part-of: messaging
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ .Release.Name }}-messaging
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "true"
      labels:
        app.kubernetes.io/name: {{ .Release.Name }}-messaging
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      securityContext:
        {{- toYaml .Values.securityContext | nindent 8 }}
      containers:
        - name: messaging
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
            - name: websocket
              containerPort: {{ add .Values.service.port 1 }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /ready
              port: http
            initialDelaySeconds: 5
            periodSeconds: 5
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            - name: REDIS_HOST
              value: {{ .Release.Name }}-redis-master
            - name: REDIS_PORT
              value: "6379"
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-redis
                  key: redis-password
            - name: MONGODB_URI
              value: mongodb://{{ .Release.Name }}-mongodb:27017/messagingdb
            - name: MONGODB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-mongodb
                  key: mongodb-root-password
            - name: MONGODB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-mongodb
                  key: mongodb-root-password
            {{- range .Values.env }}
            - name: {{ .name }}
              {{- if .value }}
              value: {{ .value | quote }}
              {{- else if .valueFrom }}
              valueFrom:
                {{- toYaml .valueFrom | nindent 16 }}
              {{- end }}
            {{- end }}