# Default values for messaging-service
replicaCount: 2

image:
  repository: messaging-service
  tag: latest
  pullPolicy: IfNotPresent

security:
  # Enable encryption key rotation
  keyRotation:
    enabled: true
    interval: 24h
  # Secret management
  secretsManager:
    enabled: true
    provider: vault

# Pod Security Context
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  fsGroup: 2000

# Container Security Context
containerSecurityContext:
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true
  capabilities:
    drop:
      - ALL

# Network Policies
networkPolicy:
  enabled: true
  ingressRules:
    - from:
        - podSelector:
            matchLabels:
              app: frontend

# Service configuration
service:
  type: ClusterIP
  port: 3001

# Resource limits
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 200m
    memory: 256Mi

# MongoDB configuration
mongodb:
  enabled: true
  auth:
    enabled: true
  persistence:
    enabled: true
    size: 10Gi
  metrics:
    enabled: true

# Redis configuration
redis:
  enabled: true
  auth:
    enabled: true
  master:
    persistence:
      enabled: true
  metrics:
    enabled: true

# Ingress configuration
ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod

# Monitoring and logging
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true

# Autoscaling configuration
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80