apiVersion: v2
name: messaging-service
description: A Helm chart for the secure messaging service
type: application
version: 0.1.0
appVersion: "1.0.0"

maintainers:
  - name: DevOps Team
    email: <EMAIL>

keywords:
  - messaging
  - websocket
  - security

dependencies:
  - name: mongodb
    version: "12.x.x"
    repository: https://charts.bitnami.com/bitnami
    condition: mongodb.enabled
  - name: redis
    version: "17.x.x"
    repository: https://charts.bitnami.com/bitnami
    condition: redis.enabled