#!/bin/bash

# Script de restructuration du projet
echo "Début de la restructuration du projet..."

# Créer des dossiers temporaires pour la migration si nécessaire
mkdir -p temp_migration

# Vérifier que les dossiers cibles existent
if [ ! -d "frontend/src" ]; then
  echo "Création du dossier frontend/src"
  mkdir -p frontend/src
fi

if [ ! -d "backend/src" ]; then
  echo "Création du dossier backend/src"
  mkdir -p backend/src
fi

# Créer les sous-dossiers nécessaires s'ils n'existent pas
mkdir -p frontend/src/styles
mkdir -p frontend/src/services
mkdir -p frontend/src/security
mkdir -p frontend/src/utils

mkdir -p backend/src/services
mkdir -p backend/src/security
mkdir -p backend/src/cache
mkdir -p backend/src/database
mkdir -p backend/src/config
mkdir -p backend/src/utils
mkdir -p backend/src/analyzer
mkdir -p backend/src/logging
mkdir -p backend/src/storage
mkdir -p backend/src/common

# Déplacer les dossiers de src/ vers backend/src/
echo "Déplacement des dossiers vers backend/src/..."

# Déplacer les fichiers de sécurité vers backend
cp -r src/security/* backend/src/security/

# Déplacer les services vers backend
cp -r src/services/* backend/src/services/

# Déplacer les autres dossiers vers backend
cp -r src/cache/* backend/src/cache/
cp -r src/database/* backend/src/database/
cp -r src/config/* backend/src/config/
cp -r src/analyzer/* backend/src/analyzer/
cp -r src/logging/* backend/src/logging/
cp -r src/storage/* backend/src/storage/
cp -r src/common/* backend/src/common/

# Vérifier s'il y a des fichiers spécifiques au frontend dans src/
echo "Recherche de fichiers frontend dans src/..."
find src -type f -name "*.tsx" -o -name "*.jsx" -o -name "*.css" -o -name "*.scss" | while read file; do
  # Extraire le nom du fichier et le chemin relatif
  filename=$(basename "$file")
  relative_path=$(dirname "$file" | sed 's|^src/||')
  
  # Créer le dossier cible si nécessaire
  mkdir -p "frontend/src/$relative_path"
  
  # Copier le fichier
  cp "$file" "frontend/src/$relative_path/"
  echo "Copié $file vers frontend/src/$relative_path/"
done

# Mettre à jour les imports dans les fichiers
echo "Mise à jour des imports dans les fichiers..."

# Frontend
find frontend/src -type f -name "*.ts*" -exec sed -i '' 's|from "../src/|from "../|g' {} \;
find frontend/src -type f -name "*.ts*" -exec sed -i '' 's|from "../../src/|from "../../|g' {} \;

# Backend
find backend/src -type f -name "*.ts" -exec sed -i '' 's|from "../src/|from "../|g' {} \;
find backend/src -type f -name "*.ts" -exec sed -i '' 's|from "../../src/|from "../../|g' {} \;

# Vérifier que tout a été copié correctement avant de supprimer
echo "Vérification de la migration..."
echo "Nombre de fichiers dans src/: $(find src -type f | wc -l)"
echo "Nombre de fichiers copiés dans backend/src/: $(find backend/src -type f | wc -l)"
echo "Nombre de fichiers copiés dans frontend/src/: $(find frontend/src -type f | wc -l)"

echo "IMPORTANT: Vérifiez que tous les fichiers ont été correctement copiés avant de supprimer le dossier src/"
echo "Pour supprimer le dossier src/ après vérification, exécutez: rm -rf src/"

echo "Restructuration terminée. Veuillez vérifier les fichiers avant de supprimer le dossier src/"