/**
 * Script pour corriger les erreurs de syntaxe courantes
 * 
 * Ce script parcourt les fichiers source et corrige les erreurs de syntaxe
 * que nous avons rencontrées précédemment.
 */
const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

// Configuration
const SOURCE_DIRS = [
  'Backend/src',
  'frontend/src'
];

// Fonction pour trouver tous les fichiers source
async function findSourceFiles() {
  const patterns = SOURCE_DIRS.map(dir => `${dir}/**/*.{js,ts,tsx}`);
  const files = [];
  
  for (const pattern of patterns) {
    try {
      const matches = await glob(pattern);
      files.push(...matches);
    } catch (error) {
      console.error(`Erreur lors de la recherche des fichiers source: ${error.message}`);
    }
  }
  
  return files;
}

// Fonction pour corriger les erreurs de syntaxe dans un fichier
function fixSyntaxErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Corriger les accolades superflues
    const bracePattern = /\{\s*\{\s*\}\s*\}/g;
    if (bracePattern.test(content)) {
      content = content.replace(bracePattern, '{}');
      modified = true;
    }
    
    // Corriger les accolades multiples
    const multipleBracesPattern = /\{\s*\{\s*\{\s*\{\s*\{\s*\{\s*\}\}\}\}\}\}/g;
    if (multipleBracesPattern.test(content)) {
      content = content.replace(multipleBracesPattern, '{');
      modified = true;
    }
    
    // Corriger les virgules superflues
    const commaPattern = /,\s*\}/g;
    if (commaPattern.test(content)) {
      content = content.replace(commaPattern, '}');
      modified = true;
    }
    
    const commaLinePattern = /,\s*$/gm;
    if (commaLinePattern.test(content)) {
      content = content.replace(commaLinePattern, '');
      modified = true;
    }
    
    // Corriger les points-virgules à la fin des fichiers
    const semiPattern = /;\s*$/;
    if (semiPattern.test(content)) {
      content = content.replace(semiPattern, '');
      modified = true;
    }
    
    // Corriger les erreurs de syntaxe spécifiques
    if (content.includes('(Object.create;')) {
      content = content.replace('(Object.create;', '(Object.create');
      modified = true;
    }
    
    if (content.includes('var c = arguments.length,;')) {
      content = content.replace('var c = arguments.length,;', 'var c = arguments.length,');
      modified = true;
    }
    
    // Corriger les erreurs de syntaxe dans les boucles for
    const forLoopPattern = /for\s*\(\s*[^)]+\)\s*\{\s*\{\s*\}/g;
    if (forLoopPattern.test(content)) {
      content = content.replace(forLoopPattern, match => {
        return match.replace(/\{\s*\{\s*\}/, '{');
      });
      modified = true;
    }
    
    // Corriger les erreurs de syntaxe dans les conditions if
    const ifPattern = /if\s*\([^)]+\)\s*\{\s*\{\s*\{\s*\{\s*\{\s*\{\s*\}\}\}\}\}\}/g;
    if (ifPattern.test(content)) {
      content = content.replace(ifPattern, match => {
        return match.replace(/\{\s*\{\s*\{\s*\{\s*\{\s*\{\s*\}\}\}\}\}\}/, '{');
      });
      modified = true;
    }
    
    // Corriger les erreurs de syntaxe dans les exports
    const exportPattern = /exports\.[^ ]+ = [^ ]+ = __decorate\([^;]+;\s*\);/g;
    if (exportPattern.test(content)) {
      content = content.replace(exportPattern, match => {
        return match.replace(/;\s*\);/, '\n);');
      });
      modified = true;
    }
    
    // Corriger les erreurs de syntaxe dans les commentaires
    const commentPattern = /\/\/# sourceMappingURL=[^;]+;/g;
    if (commentPattern.test(content)) {
      content = content.replace(commentPattern, match => {
        return match.replace(/;$/, '');
      });
      modified = true;
    }
    
    // Enregistrer les modifications
    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`Fichier corrigé: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Erreur lors de la correction du fichier ${filePath}: ${error.message}`);
    return false;
  }
}

// Fonction principale
async function main() {
  console.log('Recherche des fichiers source...');
  const sourceFiles = await findSourceFiles();
  console.log(`${sourceFiles.length} fichiers source trouvés.`);
  
  let fixedFiles = 0;
  
  console.log('Correction des erreurs de syntaxe...');
  for (const file of sourceFiles) {
    if (fixSyntaxErrors(file)) {
      fixedFiles++;
    }
  }
  
  console.log('\n=== Résumé ===');
  console.log(`Fichiers corrigés: ${fixedFiles}/${sourceFiles.length}`);
  console.log('===============\n');
  
  console.log('Terminé!');
}

// Exécuter le script
main().catch(error => {
  console.error(`Erreur lors de l'exécution du script: ${error.message}`);
  process.exit(1);
});
