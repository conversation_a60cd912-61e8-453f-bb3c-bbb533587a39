stages:
  - test
  - build
  - deploy
  - monitor

variables:
  DOCKER_REGISTRY: ${CI_REGISTRY}
  KUBE_CONFIG: ${KUBE_CONFIG_DATA}

.test_template: &test_definition
  stage: test
  script:
    - npm ci
    - npm run lint
    - npm run test:coverage
  coverage: '/Lines\s*:\s*([0-9.]+)%/'

test:backend:
  <<: *test_definition
  variables:
    SERVICE_NAME: backend

test:analyzer:
  <<: *test_definition
  variables:
    SERVICE_NAME: analyzer

test:storage:
  <<: *test_definition
  variables:
    SERVICE_NAME: decentralized-storage

build:
  stage: build
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker-compose build
    - docker-compose push
  only:
    - main
    - staging

deploy:production:
  stage: deploy
  script:
    - kubectl apply -f k8s/production/
    - ./scripts/deploy-all.sh production
  environment:
    name: production
  only:
    - main
  when: manual

monitoring:
  stage: monitor
  script:
    - ./scripts/check-deployment-health.sh
    - ./scripts/alert-slack-on-failure.sh
  after_script:
    - ./scripts/collect-metrics.sh