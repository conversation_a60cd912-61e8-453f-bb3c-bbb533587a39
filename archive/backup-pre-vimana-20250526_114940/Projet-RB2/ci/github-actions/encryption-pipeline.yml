name: Encryption Services Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Run SAST with SonarQube
        uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      
      - name: Run OWASP Dependency Check
        uses: dependency-check/Dependency-Check_Action@main
        with:
          project: 'Encryption Services'
          path: '.'
          format: 'HTML'
          
  performance-tests:
    runs-on: ubuntu-latest
    needs: security-scan
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup k6
        uses: grafana/k6-action@v0.2.0
      
      - name: Run Performance Tests
        run: k6 run tests/performance/encryption-load-test.js
        
      - name: Archive Performance Results
        uses: actions/upload-artifact@v3
        with:
          name: performance-report
          path: k6-report.json

  deploy-staging:
    runs-on: ubuntu-latest
    needs: performance-tests
    if: github.ref == 'refs/heads/develop'
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-1
          
      - name: Deploy to EKS Staging
        run: |
          aws eks update-kubeconfig --name staging-cluster
          kubectl apply -f k8s/encryption-services/
          
      - name: Run Integration Tests
        run: npm run test:integration
        
  deploy-production:
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/main'
    environment:
      name: production
      url: https://api.production.example.com
    steps:
      - name: Deploy to Production
        uses: aws-actions/eks-deploy@v1
        with:
          cluster-name: production-cluster
          manifest: k8s/encryption-services/
          
      - name: Verify Deployment
        run: |
          ./scripts/verify-deployment.sh
          ./scripts/run-smoke-tests.sh