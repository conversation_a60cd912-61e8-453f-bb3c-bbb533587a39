"""
Script pour exécuter l'interface d'administration.
"""

import uvicorn
import os
from src.database.connection import init_db
from src.admin.admin_app import create_admin_app

if __name__ == "__main__":
    # Initialiser la base de données
    init_db()
    
    # Créer l'application
    app = create_admin_app()
    
    # Exécuter l'application
    uvicorn.run(
        "src.admin.admin_app:create_admin_app",
        host="0.0.0.0",
        port=int(os.environ.get("ADMIN_PORT", 8001)),
        reload=True,
        factory=True
    )
