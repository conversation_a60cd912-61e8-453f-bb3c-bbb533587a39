#!/usr/bin/env python3
import os
import re
import sys
import argparse
import subprocess
import time
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import importlib.util

# Importer les fonctions de correction du script existant
try:
    # Charger le module fix-ts-specific.py avec importlib
    spec = importlib.util.spec_from_file_location("fix_ts_specific", "fix-ts-specific.py")
    if spec and spec.loader:
        fix_ts_specific = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(fix_ts_specific)
        
        # Récupérer les fonctions depuis le module chargé
        fix_adaptive_bandwidth = getattr(fix_ts_specific, "fix_adaptive_bandwidth", None)
        fix_analytics_service = getattr(fix_ts_specific, "fix_analytics_service", None)
        fix_accessibility_announcer = getattr(fix_ts_specific, "fix_accessibility_announcer", None)
        fix_xss_validator = getattr(fix_ts_specific, "fix_xss_validator", None)
        fix_main_page = getattr(fix_ts_specific, "fix_main_page", None)
        fix_monitoring = getattr(fix_ts_specific, "fix_monitoring", None)
        fix_export_service = getattr(fix_ts_specific, "fix_export_service", None)
    else:
        raise ImportError("Impossible de charger le module fix-ts-specific.py")
except ImportError as e:
    print(f"Note: Impossible d'importer les fonctions de fix-ts-specific.py: {e}")
    print("Les corrections prédéfinies ne seront pas disponibles.")
    print("Le script utilisera uniquement des corrections génériques.")

# Mappings de types de fichiers aux fonctions de correction
FILE_TYPE_MAPPINGS = {
    'frontend/src/hooks/useAdaptiveBandwidth.ts': 'adaptivebandwidth',
    'frontend/src/monitoring/CustomAnalyticsService.ts': 'analyticsservice',
    'frontend/src/hooks/useAccessibilityAnnouncer.ts': 'accessibilityannouncer',
    'frontend/src/security/XSSValidator.ts': 'xssvalidator',
    'frontend/src/pages/Public/MainPage.tsx': 'mainpage',
    'frontend/src/services/monitoring.ts': 'monitoring',
    'frontend/src/services/exportService.ts': 'exportservice',
}

# Expressions régulières pour les corrections génériques
COMMON_FIXES = [
    # Égalité
    (r'(\w+)\.(\w+) = ([^=])', r'\1.\2 === \3'),
    # Point-virgule après return
    (r'return \(\) => {;', r'return () => {'),
    # Point-virgule après accolade
    (r'return {;', r'return {'),
    # Promise avec trop d'arguments
    (r'Promise<([^>,]+), ([^>]+)>', r'Promise<\1>'),
    # Apostrophes non échappées
    (r"'([^']*)'([^']*)'([^']*)'", r"'\1\\'\2\\'\3'"),
    # Chaîne avec apostrophe
    (r"'([^']*)'([^']*)'", r"'\1\\'\2'"),
    # Problèmes d'indentation
    (r'if\(([^{]+)\) { { { {return', r'if(\1) { return'),
    # Méthodes mal formatées
    (r'public (\w+)\(\) { { { {: (\w+)(\[\])? {}}}}', r'public \1(): \2\3 {'),
    # Propriétés assignées (= vs ===)
    (r'(\w+)\[(\w+)\] === (\w+);', r'\1[\2] = \3;'),
    # this.propriété assignée (= vs ===)
    (r'this\.(\w+)\[(\w+)\] === (\w+);', r'this.\1[\2] = \3;'),
    # Déstructuration d'objets mal formatés
    (r'const { (.+): (.+) }', r'const { \1: \2 }'),
    # Type any implicite
    (r'(function|const) (\w+) = \(([^:)]*)\)', r'\1 \2 = (\3: any)'),
    # Types manquants dans les fonctions
    (r'function (\w+)\(([^)]*)\) {', r'function \1(\2): any {'),
    # Props React manquantes
    (r'function (\w+)\(props\) {', r'function \1(props: any) {'),
    # Propriétés non typées dans les interfaces
    (r'interface (\w+) {\s+(\w+);', r'interface \1 {\n    \2: any;'),
    # État React non typé
    (r'const \[(\w+), set\1\] = useState\(\);', r'const [\1, set\1] = useState<any>(undefined);'),
    # Méthodes de classe sans type de retour
    (r'public (\w+)\(([^)]*)\) {', r'public \1(\2): any {'),
    # Événements non typés
    (r'(onChange|onClick|onSubmit)={\(([^)]*)\)', r'\1={(\2: any)'),
    # Props non typés dans les composants
    (r'export default function (\w+)\({\s*([^}]*)\s*}\)', r'export default function \1({ \2 }: any)'),
]

# Nouvelles règles spécifiques pour les importations et autres problèmes courants
IMPORT_FIXES = [
    # Imports dupliqués - on les supprime
    (r'import React from "react";\s+import React from "react";', r'import React from "react";'),
    # Supprimer import { Component } from "react" dupliqué
    (r'import { Component } from "react";\s+import { Component } from "react";', r'import { Component } from "react";'),
    # Correction du format d'import des composants
    (r'import { Component } from "(.*?)\.tsx"', r'import { Component } from "\1"'),
    # Corriger les chemins d'importation lazy avec backslashes inappropriés
    (r'lazy\(\(\) => import\(\\\'(.*?)\\\'', r'lazy(() => import("\1")'),
    (r'lazy\(\(\) => import\(\'(.*?)\\\\\'', r'lazy(() => import("\1")'),
    (r'lazy\(\(\) => import\(\'(.*?)\'', r'lazy(() => import("\1")'),
    (r'lazy\(\(\) => import\(\\\'(.*?)\'', r'lazy(() => import("\1")'),
    (r'lazy\(\(\) => import\(\'(.*?)\\\'', r'lazy(() => import("\1")'),
    (r'lazy\(\(\) => import\(\\\'(.*?)\\\\\'', r'lazy(() => import("\1")'),
    (r'lazy\(\(\) => import\(\'(.*?)\\\\\'', r'lazy(() => import("\1")'),
    # Corriger les chemins qui se terminent par des backslashes
    (r'import\("([^"]*?)\\\\"\)', r'import("\1")'),
    # Supprimer les commentaires avec ponctuation à la fin
    (r'\/\/ .+;', r'// ...'),
    # Corriger les exports par défaut
    (r'export default = ', r'export default '),
    # Corriger les types Promise génériques
    (r'Promise<([^>]+), ([^>]+), ([^>]+), ([^>]+)>', r'Promise<\1>'),
    (r'Promise<([^>]+), ([^>]+), ([^>]+)>', r'Promise<\1>'),
    (r'Promise<([^>]+), ([^>]+)>', r'Promise<\1>'),
    # Corriger les importations relatives
    (r'from "(\.\.\/[^"]*)"', r'from "\1"'),
    # Extension .tsx inutile dans les imports
    (r'from "(.*?)\.tsx"', r'from "\1"'),
    # Extension .ts inutile dans les imports
    (r'from "(.*?)\.ts"', r'from "\1"'),
    # Importer React quand JSX est utilisé
    (r'<(\w+)[^>]*>.*</\1>', lambda match: 'import React from "react";\n\n' + match.group(0) if 'import React' not in match.string.split('\n')[:10] else match.group(0)),
    # Corriger les imports avec des .js (React Native)
    (r'from "(.*?)\.js"', r'from "\1"'),
    # Ajouter des accolades pour les imports nommés
    (r'import (\w+) from', lambda match: f'import {{ {match.group(1)} }} from' if match.group(1) not in ['React', 'ReactDOM', 'PropTypes'] else match.group(0)),
]

# Corrections spécifiques pour les erreurs de classe et de type
CLASS_FIXES = [
    # Définir le type pour les méthodes de classe sans type
    (r'public (\w+)\(([^)]*)\) {', r'public \1(\2): any {'),
    # Ajouter un constructeur pour les propriétés de classe non initialisées
    (r'class (\w+)[^{]*{\s+([^;{]*): ([^;]*);\s+([^c]|c[^o]|co[^n]|con[^s]|cons[^t]|const[^r]|constr[^u]|constru[^c]|construc[^t]|construct[^o]|constructo[^r])',
     r'class \1 {\n    \2: \3;\n\n    constructor() {\n        this.\2 = null as any; // Initialisation automatique\n    }\n\n    \4'),
    # Initialiser les propriétés avec une valeur par défaut
    (r'(private|public|protected) (\w+): ([^;]+);', r'\1 \2: \3 = null as any;'),
    # Convertir les classes en classes React.Component typées
    (r'class (\w+) extends React\.Component {', r'interface \1Props {}\ninterface \1State {}\n\nclass \1 extends React.Component<\1Props, \1State> {'),
    # Méthodes de classe sans implémentation
    (r'(\w+)\(\): (\w+);', r'\1(): \2 { return null as any; }'),
]

# Corrections spécifiques pour React
REACT_FIXES = [
    # React.FC pour les fonctions composants
    (r'function (\w+)\(props\) {', r'const \1: React.FC<any> = (props) => {'),
    # React.useState non typé
    (r'const \[(\w+), set(\w+)\] = useState\(([^<])', r'const [\1, set\2] = useState<any>(\3'),
    # useEffect avec dépendances manquantes
    (r'useEffect\(\(\) => {([^}]+)}\)', r'useEffect(() => {\1}, [])'),
    # Composants sans props définis
    (r'type (\w+)Props = {', r'type \1Props = {\n    children?: React.ReactNode;'),
    # useRef non typé
    (r'useRef\(\)', r'useRef<HTMLInputElement>(null)'),
    # useContext sans type
    (r'useContext\((\w+)Context\)', r'useContext<\1ContextType>(\1Context)'),
]

# Correction d'erreurs TS courantes
TS_ERROR_FIXES = [
    # TS2339: Property 'x' does not exist on type 'y'
    (r'(\w+)\.(\w+)', lambda match: f'({match.group(1)} as any).{match.group(2)}' if match.group(1) not in ['this', 'props', 'window', 'document', 'globalThis'] else match.group(0)),
    # TS2322: Type 'x' is not assignable to type 'y'
    (r'= ([^;:]+);', r'= \1 as any;'),
    # TS2769: No overload matches this call
    (r'(\w+)\(([^)]*)\)', r'\1(\2 as any)'),
    # TS2345: Argument of type 'x' is not assignable to parameter of type 'y'
    (r'(\w+)\(([^)]+)\)', lambda match: f'{match.group(1)}({match.group(2).replace(",", " as any,")})'),
    # TS2352: Type 'x[]' cannot be converted to type 'ReadonlyArray<y>'
    (r'(const|let|var) (\w+): ReadonlyArray<([^>]+)>', r'\1 \2: \3[]'),
    # TS2740: Type '{}' is missing the following properties from type 'x'
    (r'<(\w+)([^>]*)>\(\{\}\)', r'<\1\2>({}as any)'),
    # TS7006: Parameter 'x' implicitly has an 'any' type
    (r'(\(| )(\w+)(?=,|\))', r'\1\2: any'),
]

# Dossiers et fichiers à exclure de la correction
EXCLUDED_PATHS = [
    'node_modules',
    '.d.ts',
    'types/',
    'cypress/'
]

def should_exclude(file_path: str) -> bool:
    """Vérifie si un fichier doit être exclu de la correction"""
    for excluded in EXCLUDED_PATHS:
        if excluded in file_path:
            return True
    return False

def run_command(command: str) -> Tuple[int, str]:
    """Exécute une commande shell et renvoie le code de sortie et la sortie"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=False,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True
        )
        return result.returncode, result.stdout
    except Exception as e:
        return 1, str(e)

def count_errors() -> int:
    """Compte le nombre total d'erreurs TypeScript dans le projet"""
    command = "tsc -p frontend/tsconfig.json --noEmit --skipLibCheck 2>&1 | grep -c 'error TS' || echo 0"
    _, output = run_command(command)
    try:
        return int(output.strip())
    except ValueError:
        return 0

def find_files_with_errors(limit: int = 10, directory: str = None) -> List[Tuple[str, int]]:
    """
    Trouve les fichiers avec le plus d'erreurs
    Retourne une liste de tuples (chemin_du_fichier, nombre_erreurs)
    
    Args:
        limit: Nombre maximum de fichiers à retourner
        directory: Répertoire spécifique à analyser (None pour tout le projet)
    """
    # Préparer la commande de base
    command = """
    tsc -p frontend/tsconfig.json --noEmit --skipLibCheck 2>&1 | 
    grep -E "error TS" | 
    cut -d'(' -f1 | 
    sort | 
    uniq -c | 
    sort -nr | 
    head -50
    """
    
    # Si un répertoire est spécifié, filtrer les résultats
    if directory:
        command = f"""
        tsc -p frontend/tsconfig.json --noEmit --skipLibCheck 2>&1 | 
        grep -E "{directory}" |
        grep -E "error TS" | 
        cut -d'(' -f1 | 
        sort | 
        uniq -c | 
        sort -nr | 
        head -50
        """
    
    _, output = run_command(command)
    
    files_with_errors = []
    for line in output.strip().split('\n'):
        if not line.strip():
            continue
        
        parts = line.strip().split(None, 1)
        if len(parts) == 2:
            try:
                count = int(parts[0])
                filepath = parts[1].strip()
                
                # Exclure les fichiers dans les chemins spécifiés
                if not should_exclude(filepath):
                    files_with_errors.append((filepath, count))
            except (ValueError, IndexError):
                continue
    
    # Retourner les premiers 'limit' fichiers
    return files_with_errors[:limit]

def has_errors(file_path: str) -> bool:
    """Vérifie si un fichier a des erreurs TypeScript"""
    command = f"tsc -p frontend/tsconfig.json --noEmit --skipLibCheck 2>&1 | grep -E '{file_path}' | head -1"
    returncode, output = run_command(command)
    return returncode == 0 and bool(output.strip())

def apply_specific_fix(file_path: str) -> bool:
    """
    Applique une correction spécifique à un fichier en utilisant fix-ts-specific.py
    Retourne True si une correction a été appliquée
    """
    for known_path, fix_type in FILE_TYPE_MAPPINGS.items():
        if file_path.endswith(known_path):
            command = f"python3 fix-ts-specific.py --file {file_path} --type {fix_type}"
            returncode, output = run_command(command)
            return returncode == 0 and "Corrections appliquées" in output
    
    return False

def apply_generic_fixes(file_path: str) -> bool:
    """
    Applique des corrections génériques à un fichier en utilisant des expressions régulières
    Retourne True si des corrections ont été appliquées
    """
    # Vérifier que le fichier existe
    if not os.path.isfile(file_path):
        print(f"Erreur: Le fichier {file_path} n'existe pas.")
        return False
    
    # Lire le contenu du fichier
    try:
        with open(file_path, 'r') as file:
            content = file.read()
    except Exception as e:
        print(f"Erreur lors de la lecture du fichier {file_path}: {e}")
        return False
    
    # Créer une sauvegarde
    backup_path = file_path + '.bak'
    try:
        with open(backup_path, 'w') as file:
            file.write(content)
    except Exception as e:
        print(f"Erreur lors de la création de la sauvegarde {backup_path}: {e}")
        return False
    
    # Conserver le contenu original pour vérifier si des modifications ont été apportées
    original_content = content
    
    # Déterminer les règles à appliquer en fonction du type de fichier
    is_ts_file = file_path.endswith('.ts') or file_path.endswith('.tsx')
    is_tsx_file = file_path.endswith('.tsx')
    is_react_file = is_tsx_file or 'React' in content or 'react' in content
    is_class_file = 'class ' in content
    
    # Appliquer d'abord les corrections d'importation si c'est un fichier TS/TSX
    if is_ts_file:
        for pattern, replacement in IMPORT_FIXES:
            try:
                content = re.sub(pattern, replacement, content)
            except Exception as e:
                print(f"Erreur lors de l'application du modèle d'importation {pattern}: {e}")
    
    # Appliquer les corrections spécifiques pour les classes si le fichier contient des classes
    if is_class_file and is_ts_file:
        for pattern, replacement in CLASS_FIXES:
            try:
                content = re.sub(pattern, replacement, content)
            except Exception as e:
                print(f"Erreur lors de l'application du modèle de classe {pattern}: {e}")
    
    # Appliquer les corrections spécifiques à React si c'est un fichier React
    if is_react_file:
        for pattern, replacement in REACT_FIXES:
            try:
                content = re.sub(pattern, replacement, content)
            except Exception as e:
                print(f"Erreur lors de l'application du modèle React {pattern}: {e}")
    
    # Appliquer les corrections d'erreurs TS courantes si c'est un fichier TS/TSX
    if is_ts_file:
        for pattern, replacement in TS_ERROR_FIXES:
            try:
                content = re.sub(pattern, replacement, content)
            except Exception as e:
                print(f"Erreur lors de l'application du modèle d'erreur TS {pattern}: {e}")
    
    # Appliquer les corrections génériques communes
    for pattern, replacement in COMMON_FIXES:
        try:
            content = re.sub(pattern, replacement, content)
        except Exception as e:
            print(f"Erreur lors de l'application du modèle commun {pattern}: {e}")
    
    # Écrire le contenu corrigé seulement s'il a été modifié
    if content != original_content:
        try:
            with open(file_path, 'w') as file:
                file.write(content)
            print(f"Corrections génériques appliquées à {file_path}")
            return True
        except Exception as e:
            print(f"Erreur lors de l'écriture du fichier {file_path}: {e}")
            # Restaurer depuis la sauvegarde en cas d'erreur
            try:
                with open(backup_path, 'r') as backup, open(file_path, 'w') as original:
                    original.write(backup.read())
            except:
                pass
            return False
    
    print(f"Aucune correction n'a été nécessaire pour {file_path}")
    return False

def fix_apostrophe_issues(file_path: str) -> bool:
    """
    Corrige spécifiquement les problèmes d'apostrophes dans les chaînes de caractères
    Retourne True si des corrections ont été appliquées
    """
    # Vérifier que le fichier existe
    if not os.path.isfile(file_path):
        return False
    
    # Lire le contenu du fichier ligne par ligne
    try:
        with open(file_path, 'r') as file:
            lines = file.readlines()
    except:
        return False
    
    # Créer une sauvegarde
    backup_path = file_path + '.apostrophe.bak'
    try:
        with open(backup_path, 'w') as file:
            file.writelines(lines)
    except:
        return False
    
    # Rechercher les lignes avec des apostrophes problématiques
    modified = False
    for i, line in enumerate(lines):
        # Chaînes avec des apostrophes non échappées
        if "'" in line:
            # Analyser la ligne pour trouver les chaînes de caractères
            j = 0
            new_line = ""
            in_string = False
            current_quote = None
            
            while j < len(line):
                if not in_string and (line[j] == "'" or line[j] == '"'):
                    # Début d'une chaîne
                    in_string = True
                    current_quote = line[j]
                    new_line += line[j]
                elif in_string and line[j] == current_quote and (j == 0 or line[j-1] != '\\'):
                    # Fin d'une chaîne
                    in_string = False
                    new_line += line[j]
                elif in_string and line[j] == "'" and current_quote == "'" and (j == 0 or line[j-1] != '\\'):
                    # Apostrophe non échappée dans une chaîne délimitée par des apostrophes
                    new_line += "\\'"
                    modified = True
                else:
                    new_line += line[j]
                
                j += 1
            
            if new_line != line:
                lines[i] = new_line
    
    # Écrire le contenu corrigé seulement s'il a été modifié
    if modified:
        try:
            with open(file_path, 'w') as file:
                file.writelines(lines)
            print(f"Corrections d'apostrophes appliquées à {file_path}")
            return True
        except:
            # Restaurer depuis la sauvegarde en cas d'erreur
            try:
                with open(backup_path, 'r') as backup, open(file_path, 'w') as original:
                    original.write(backup.read())
            except:
                pass
            return False
    
    return False

def analyze_and_fix_specific_errors(file_path: str) -> bool:
    """
    Analyse les erreurs TypeScript spécifiques d'un fichier et applique des corrections ciblées
    Retourne True si des corrections ont été appliquées
    """
    # Vérifier que le fichier existe
    if not os.path.isfile(file_path):
        print(f"Erreur: Le fichier {file_path} n'existe pas.")
        return False
    
    # Récupérer les erreurs spécifiques pour ce fichier
    command = f"tsc -p frontend/tsconfig.json --noEmit --skipLibCheck 2>&1 | grep '{file_path}'"
    _, output = run_command(command)
    
    if not output.strip():
        print(f"Aucune erreur TypeScript spécifique trouvée pour {file_path}")
        return False
    
    # Lire le contenu du fichier
    try:
        with open(file_path, 'r') as file:
            lines = file.readlines()
    except Exception as e:
        print(f"Erreur lors de la lecture du fichier {file_path}: {e}")
        return False
    
    # Créer une sauvegarde
    backup_path = file_path + '.specific.bak'
    try:
        with open(backup_path, 'w') as file:
            file.writelines(lines)
    except Exception as e:
        print(f"Erreur lors de la création de la sauvegarde {backup_path}: {e}")
        return False
    
    # Extraire les informations d'erreur (ligne, colonne, code d'erreur, message)
    error_pattern = r'([^(]+)\((\d+),(\d+)\): error (TS\d+): (.+)'
    errors = []
    for line in output.strip().split('\n'):
        match = re.search(error_pattern, line)
        if match:
            file_path, line_num, col_num, error_code, message = match.groups()
            errors.append({
                'line': int(line_num) - 1,  # Ajuster pour l'indexation 0 des listes
                'column': int(col_num),
                'code': error_code,
                'message': message
            })
    
    # Traiter les erreurs courantes
    modified = False
    
    for error in errors:
        line_idx = error['line']
        if line_idx >= len(lines):
            continue
        
        line = lines[line_idx]
        error_code = error['code']
        
        # TS1136: Property assignment expected
        if error_code == 'TS1136':
            if ':' in line and not '=' in line:
                new_line = line.replace(':', ' =')
                lines[line_idx] = new_line
                modified = True
                print(f"TS1136 corrigé à la ligne {line_idx + 1}: Remplacé ':' par '='")
        
        # TS2339: Property 'X' does not exist on type 'Y'
        elif error_code == 'TS2339':
            match = re.search(r"Property '(\w+)' does not exist on type", error['message'])
            if match:
                prop_name = match.group(1)
                # Trouver le nom de la variable avant le point
                col = error['column'] - 1
                var_match = re.search(r'(\w+)\.' + prop_name, line[max(0, col-30):col+len(prop_name)+1])
                if var_match:
                    var_name = var_match.group(1)
                    # Remplacer par un casting à any
                    new_line = line.replace(f"{var_name}.{prop_name}", f"({var_name} as any).{prop_name}")
                    lines[line_idx] = new_line
                    modified = True
                    print(f"TS2339 corrigé à la ligne {line_idx + 1}: Ajout de 'as any' pour {var_name}.{prop_name}")
        
        # TS2322: Type 'X' is not assignable to type 'Y'
        elif error_code == 'TS2322':
            if ' = ' in line:
                parts = line.split(' = ', 1)
                if len(parts) == 2:
                    # Ajouter un cast à any
                    if not 'as any' in parts[1]:
                        parts[1] = parts[1].rstrip(';\n') + ' as any' + (';\n' if parts[1].rstrip().endswith(';') else '\n')
                        lines[line_idx] = parts[0] + ' = ' + parts[1]
                        modified = True
                        print(f"TS2322 corrigé à la ligne {line_idx + 1}: Ajout de 'as any'")
        
        # TS2345: Argument of type 'X' is not assignable to parameter of type 'Y'
        elif error_code == 'TS2345':
            # Trouver l'appel de fonction problématique
            func_call_match = re.search(r'(\w+)\(([^)]*)\)', line)
            if func_call_match:
                func_name = func_call_match.group(1)
                args = func_call_match.group(2)
                # Ajouter 'as any' à chaque argument
                new_args = ', '.join([arg.strip() + ' as any' if not 'as any' in arg else arg for arg in args.split(',')])
                new_line = line.replace(f"{func_name}({args})", f"{func_name}({new_args})")
                lines[line_idx] = new_line
                modified = True
                print(f"TS2345 corrigé à la ligne {line_idx + 1}: Ajout de 'as any' aux arguments")
                
        # TS7006: Parameter 'X' implicitly has an 'any' type
        elif error_code == 'TS7006':
            match = re.search(r"Parameter '(\w+)'", error['message'])
            if match:
                param_name = match.group(1)
                # Trouver le paramètre dans la ligne
                param_match = re.search(r'(\(|\s)' + param_name + r'(\s*[,)])', line)
                if param_match:
                    # Ajouter une annotation de type any
                    new_param = param_match.group(1) + param_name + ': any' + param_match.group(2)
                    new_line = line.replace(param_match.group(0), new_param)
                    lines[line_idx] = new_line
                    modified = True
                    print(f"TS7006 corrigé à la ligne {line_idx + 1}: Ajout de ': any' au paramètre {param_name}")
    
    # Écrire le contenu modifié si des corrections ont été apportées
    if modified:
        try:
            with open(file_path, 'w') as file:
                file.writelines(lines)
            print(f"Corrections spécifiques appliquées à {file_path}")
            return True
        except Exception as e:
            print(f"Erreur lors de l'écriture du fichier {file_path}: {e}")
            # Restaurer depuis la sauvegarde
            try:
                with open(backup_path, 'r') as backup, open(file_path, 'w') as original:
                    original.write(backup.read())
            except:
                pass
            return False
    
    return False

def generate_report_html(results: List[Dict], start_time: float, end_time: float) -> str:
    """Génère un rapport HTML des corrections effectuées"""
    total_duration = end_time - start_time
    total_files = len(results)
    total_corrected = sum(1 for r in results if r['corrected'])
    total_errors_before = sum(r['errors_before'] for r in results)
    total_errors_after = sum(r['errors_after'] for r in results)
    total_errors_fixed = total_errors_before - total_errors_after
    
    html = f"""
    <!DOCTYPE html>
    <html lang="fr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Rapport de correction TypeScript</title>
        <style>
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }}
            h1, h2, h3 {{
                color: #2c3e50;
            }}
            .summary {{
                background-color: #f8f9fa;
                border-left: 4px solid #4caf50;
                padding: 15px;
                margin-bottom: 20px;
                border-radius: 4px;
            }}
            .stats {{
                display: flex;
                flex-wrap: wrap;
                gap: 15px;
                margin-bottom: 20px;
            }}
            .stat-card {{
                background-color: #fff;
                border-radius: 8px;
                padding: 15px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                flex: 1;
                min-width: 200px;
            }}
            .stat-card h3 {{
                margin-top: 0;
                color: #3498db;
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 12px;
                text-align: left;
            }}
            th {{
                background-color: #f2f2f2;
            }}
            tr:nth-child(even) {{
                background-color: #f9f9f9;
            }}
            .success {{
                color: #4caf50;
            }}
            .failure {{
                color: #f44336;
            }}
            .file-path {{
                font-family: monospace;
                word-break: break-all;
            }}
        </style>
    </head>
    <body>
        <h1>Rapport de correction TypeScript</h1>
        
        <div class="summary">
            <h2>Résumé</h2>
            <p>
                Analyse effectuée le {datetime.now().strftime('%d/%m/%Y à %H:%M:%S')}.<br>
                Durée totale: {total_duration:.2f} secondes.
            </p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <h3>Fichiers</h3>
                <p><strong>{total_files}</strong> fichiers analysés</p>
                <p><strong>{total_corrected}</strong> fichiers corrigés</p>
                <p><strong>{(total_corrected / total_files * 100) if total_files else 0:.1f}%</strong> de taux de correction</p>
            </div>
            
            <div class="stat-card">
                <h3>Erreurs</h3>
                <p><strong>{total_errors_before}</strong> erreurs avant correction</p>
                <p><strong>{total_errors_after}</strong> erreurs après correction</p>
                <p><strong>{total_errors_fixed}</strong> erreurs corrigées</p>
            </div>
            
            <div class="stat-card">
                <h3>Efficacité</h3>
                <p><strong>{(total_errors_fixed / total_errors_before * 100) if total_errors_before else 0:.1f}%</strong> des erreurs corrigées</p>
                <p><strong>{total_errors_fixed / total_duration:.1f}</strong> erreurs corrigées par seconde</p>
            </div>
        </div>
        
        <h2>Détails par fichier</h2>
        <table>
            <thead>
                <tr>
                    <th>Fichier</th>
                    <th>Erreurs avant</th>
                    <th>Erreurs après</th>
                    <th>Erreurs corrigées</th>
                    <th>Méthode</th>
                    <th>Statut</th>
                </tr>
            </thead>
            <tbody>
    """
    
    for result in sorted(results, key=lambda x: x['errors_fixed'], reverse=True):
        file_path = result['file_path']
        errors_before = result['errors_before']
        errors_after = result['errors_after']
        errors_fixed = result['errors_fixed']
        method = result['method']
        corrected = result['corrected']
        
        status_class = "success" if corrected else "failure"
        status_text = "Corrigé" if corrected else "Échec"
        
        html += f"""
                <tr>
                    <td class="file-path">{file_path}</td>
                    <td>{errors_before}</td>
                    <td>{errors_after}</td>
                    <td>{errors_fixed}</td>
                    <td>{method}</td>
                    <td class="{status_class}">{status_text}</td>
                </tr>
        """
    
    html += """
            </tbody>
        </table>
    </body>
    </html>
    """
    
    return html

def fix_file(file_path: str, dry_run: bool = False, force: bool = False) -> Dict:
    """
    Applique diverses méthodes de correction à un fichier et renvoie les résultats
    Retourne un dictionnaire avec les informations sur les corrections effectuées
    
    Args:
        file_path: Chemin du fichier à corriger
        dry_run: Si True, ne pas appliquer les corrections (simulation)
        force: Si True, appliquer les corrections même si elles augmentent le nombre d'erreurs
    """
    result = {
        'file_path': file_path,
        'errors_before': 0,
        'errors_after': 0,
        'errors_fixed': 0,
        'method': 'Aucune',
        'corrected': False
    }
    
    # Vérifier que le fichier existe
    if not os.path.isfile(file_path):
        print(f"Erreur: Le fichier {file_path} n'existe pas.")
        return result
    
    # Compter les erreurs avant correction
    command = f"tsc -p frontend/tsconfig.json --noEmit --skipLibCheck 2>&1 | grep -c '{file_path}' || echo 0"
    _, output = run_command(command)
    try:
        result['errors_before'] = int(output.strip())
    except ValueError:
        result['errors_before'] = 0
    
    if result['errors_before'] == 0:
        print(f"Le fichier {file_path} ne contient aucune erreur TypeScript.")
        return result
    
    # En mode simulation, retourner simplement les erreurs avant correction
    if dry_run:
        print(f"[Simulation] Le fichier {file_path} contient {result['errors_before']} erreurs.")
        print(f"[Simulation] Aucune correction ne sera appliquée en mode simulation.")
        return result
    
    # Créer une sauvegarde avant toute modification
    backup_path = file_path + '.original.bak'
    try:
        with open(file_path, 'r') as src, open(backup_path, 'w') as dst:
            dst.write(src.read())
        print(f"Sauvegarde créée: {backup_path}")
    except Exception as e:
        print(f"Erreur lors de la création de la sauvegarde {backup_path}: {e}")
    
    # Tenter d'appliquer une correction spécifique prédéfinie
    correction_applied = False
    if apply_specific_fix(file_path):
        result['method'] = 'Spécifique'
        correction_applied = True
    # Sinon, analyser et corriger les erreurs spécifiques du fichier
    elif analyze_and_fix_specific_errors(file_path):
        result['method'] = 'Analyse'
        correction_applied = True
    # Sinon, essayer de corriger les apostrophes
    elif fix_apostrophe_issues(file_path):
        result['method'] = 'Apostrophes'
        correction_applied = True
    # Sinon, appliquer des corrections génériques
    elif apply_generic_fixes(file_path):
        result['method'] = 'Générique'
        correction_applied = True
    
    if not correction_applied:
        print(f"Aucune correction n'a pu être appliquée à {file_path}")
        return result
    
    # Compter les erreurs après correction
    command = f"tsc -p frontend/tsconfig.json --noEmit --skipLibCheck 2>&1 | grep -c '{file_path}' || echo 0"
    _, output = run_command(command)
    try:
        result['errors_after'] = int(output.strip())
    except ValueError:
        result['errors_after'] = 0
    
    result['errors_fixed'] = result['errors_before'] - result['errors_after']
    
    # Si la correction a augmenté le nombre d'erreurs et que force n'est pas activé, restaurer la sauvegarde
    if result['errors_fixed'] < 0 and not force:
        print(f"Attention: La correction a augmenté le nombre d'erreurs de {-result['errors_fixed']}.")
        print(f"Restauration du fichier depuis la sauvegarde...")
        try:
            with open(backup_path, 'r') as src, open(file_path, 'w') as dst:
                dst.write(src.read())
            
            # Réinitialiser les valeurs
            result['errors_after'] = result['errors_before']
            result['errors_fixed'] = 0
            result['method'] += ' (annulée)'
            result['corrected'] = False
            
            print(f"Restauration effectuée.")
        except Exception as e:
            print(f"Erreur lors de la restauration: {e}")
    else:
        if result['errors_fixed'] < 0 and force:
            print(f"Attention: La correction a augmenté le nombre d'erreurs de {-result['errors_fixed']}, mais --force est activé.")
            print(f"Les modifications seront conservées malgré l'augmentation du nombre d'erreurs.")
        result['corrected'] = True
    
    return result

def auto_fix(limit: int = 10, max_files: int = 10, dry_run: bool = False, force: bool = False, directory: str = None, report_file: str = None) -> None:
    """
    Fonction principale pour automatiser la correction des erreurs TypeScript
    
    Args:
        limit: Nombre de fichiers à analyser
        max_files: Nombre maximum de fichiers à corriger
        dry_run: Si True, ne pas appliquer les corrections (simulation)
        force: Si True, appliquer les corrections même si elles augmentent le nombre d'erreurs
        directory: Répertoire spécifique à analyser (None pour tout le projet)
        report_file: Chemin personnalisé pour le fichier de rapport
    """
    print(f"=== Correction automatique des erreurs TypeScript ===")
    if dry_run:
        print("Mode simulation activé: aucune correction ne sera appliquée.")
    
    if force:
        print("Mode force activé: les corrections seront appliquées même si elles augmentent le nombre d'erreurs.")
        
    if directory:
        print(f"Analyse limitée au répertoire: {directory}")
    
    start_time = time.time()
    
    # Compter les erreurs avant correction
    errors_before = count_errors()
    print(f"Nombre total d'erreurs TypeScript: {errors_before}")
    
    # Trouver les fichiers avec le plus d'erreurs
    files_with_errors = find_files_with_errors(limit, directory=directory)
    print(f"Top {len(files_with_errors)} fichiers avec le plus d'erreurs (hors chemins exclus):")
    for i, (file_path, count) in enumerate(files_with_errors):
        print(f"{i+1}. {file_path}: {count} erreurs")
    
    # Si aucun fichier n'a été trouvé, quitter
    if not files_with_errors:
        print("Aucun fichier avec des erreurs n'a été trouvé (ou tous sont exclus).")
        return
    
    # Corriger les fichiers
    results = []
    for i, (file_path, _) in enumerate(files_with_errors[:max_files]):
        print(f"\nCorrection de {file_path} ({i+1}/{min(len(files_with_errors), max_files)})...")
        result = fix_file(file_path, dry_run=dry_run, force=force)
        results.append(result)
        
        if result['corrected']:
            print(f"✓ Fichier corrigé avec la méthode '{result['method']}'")
            print(f"  Erreurs avant: {result['errors_before']}")
            print(f"  Erreurs après: {result['errors_after']}")
            print(f"  Erreurs corrigées: {result['errors_fixed']}")
        else:
            print(f"✗ Échec de la correction")
    
    # En mode simulation, ne pas compter les erreurs après
    if dry_run:
        errors_after = errors_before
        print("\n[Simulation] Aucune correction n'a été appliquée.")
    else:
        # Compter les erreurs après correction
        errors_after = count_errors()
        print(f"\nNombre total d'erreurs TypeScript après correction: {errors_after}")
        print(f"Total d'erreurs corrigées: {errors_before - errors_after}")
    
    end_time = time.time()
    duration = end_time - start_time
    print(f"Durée totale: {duration:.2f} secondes")
    
    # Générer un rapport HTML
    report_html = generate_report_html(results, start_time, end_time)
    report_path = report_file or f"ts_fix_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    
    with open(report_path, 'w') as f:
        f.write(report_html)
    
    print(f"\nRapport détaillé généré: {report_path}")

def main():
    parser = argparse.ArgumentParser(description='Outil automatisé de correction des erreurs TypeScript')
    parser.add_argument('--limit', type=int, default=10, help='Nombre de fichiers à analyser (défaut: 10)')
    parser.add_argument('--max-files', type=int, default=10, help='Nombre maximum de fichiers à corriger (défaut: 10)')
    parser.add_argument('--file', type=str, help='Chemin spécifique du fichier à corriger')
    parser.add_argument('--directory', type=str, help='Répertoire spécifique à analyser')
    parser.add_argument('--dry-run', action='store_true', help='Mode simulation (ne pas appliquer les corrections)')
    parser.add_argument('--force', action='store_true', help='Forcer les corrections même si elles augmentent le nombre d\'erreurs')
    parser.add_argument('--report-file', type=str, help='Chemin personnalisé pour le fichier de rapport')
    
    args = parser.parse_args()
    
    # Traitement d'un fichier spécifique si fourni
    if args.file:
        if not os.path.isfile(args.file):
            print(f"Erreur: Le fichier {args.file} n'existe pas.")
            return
        
        print(f"=== Correction du fichier {args.file} ===")
        start_time = time.time()
        result = fix_file(args.file, dry_run=args.dry_run, force=args.force)
        results = [result]
        
        if result['corrected']:
            print(f"✓ Fichier corrigé avec la méthode '{result['method']}'")
            print(f"  Erreurs avant: {result['errors_before']}")
            print(f"  Erreurs après: {result['errors_after']}")
            print(f"  Erreurs corrigées: {result['errors_fixed']}")
        else:
            print(f"✗ Échec de la correction")
            
        end_time = time.time()
        
        # Générer un rapport
        report_html = generate_report_html(results, start_time, end_time)
        report_path = args.report_file or f"ts_fix_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        
        with open(report_path, 'w') as f:
            f.write(report_html)
        
        print(f"\nRapport détaillé généré: {report_path}")
    else:
        # Mode normal: auto-correction des fichiers avec le plus d'erreurs
        auto_fix(args.limit, args.max_files, dry_run=args.dry_run, force=args.force, directory=args.directory, report_file=args.report_file)

if __name__ == "__main__":
    main() 