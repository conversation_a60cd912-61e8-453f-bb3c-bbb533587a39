# Intégration Frontend-Backend - Retreat And Be

Ce document résume les étapes réalisées pour finaliser l'intégration entre Front-Audrey-V1-Main-main, Backend-NestJS et la base de données schema.prisma.

## Composants implémentés

### Backend (NestJS)

1. **Contrôleurs et services**
   - UsersController et UsersService
   - RetreatsController et RetreatsService
   - BookingsController et BookingsService

2. **DTOs (Data Transfer Objects)**
   - CreateRetreatDto, UpdateRetreatDto, RetreatResponseDto
   - CreateBookingDto, UpdateBookingDto, BookingResponseDto

3. **Sécurité**
   - JwtAuthGuard pour l'authentification
   - RolesGuard pour l'autorisation basée sur les rôles
   - Décorateurs CurrentUser, Roles et Public

### Frontend (React)

1. **Services API**
   - apiClient.ts : Client HTTP avec intercepteurs
   - apiConfig.ts : Configuration des endpoints API
   - authService.ts : Service d'authentification
   - userService.ts : Service de gestion des utilisateurs
   - retreatService.ts : Service de gestion des retraites
   - bookingService.ts : Service de gestion des réservations

2. **Composants UI**
   - RetreatList : Liste des retraites avec filtrage et pagination
   - RetreatCard : Carte affichant les informations d'une retraite
   - FilterBar : Barre de filtrage pour les retraites
   - Pagination : Composant de pagination
   - Spinner : Indicateur de chargement

## Modifications apportées

### Backend

1. **Création des contrôleurs et services**
   - Implémentation des endpoints REST pour les utilisateurs, retraites et réservations
   - Validation des données avec class-validator
   - Documentation Swagger des API

2. **Sécurité**
   - Mise en place de l'authentification JWT
   - Contrôle d'accès basé sur les rôles
   - Protection des routes sensibles

3. **Base de données**
   - Génération du client Prisma
   - Configuration des relations entre les modèles

### Frontend

1. **Services API**
   - Centralisation des endpoints API dans apiConfig.ts
   - Mise à jour des services pour utiliser ces endpoints
   - Gestion des erreurs et des tokens d'authentification

2. **Composants UI**
   - Création de composants réutilisables
   - Intégration avec les services API
   - Animations avec Framer Motion

3. **Types**
   - Définition d'interfaces TypeScript pour les modèles de données
   - Typage des réponses API

## Configuration

### Variables d'environnement

#### Backend (.env)
```
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/retreatdb"
JWT_SECRET="your-jwt-secret-key-here"
JWT_REFRESH_SECRET="your-jwt-refresh-secret-key-here"
JWT_EXPIRATION="1h"
JWT_REFRESH_EXPIRATION="7d"
PORT=3000
NODE_ENV=development
API_PREFIX="/api/v1"
CORS_ORIGIN="http://localhost:5173"
```

#### Frontend (.env.development)
```
REACT_APP_API_URL=http://localhost:3000/api
REACT_APP_SECURITY_URL=http://localhost:3001/api
REACT_APP_AGENT_IA_URL=http://localhost:3002/api
REACT_APP_SOCIAL_URL=http://localhost:3003/api
REACT_APP_FINANCIAL_URL=http://localhost:3004/api
```

## Démarrage de l'application

### Backend
```bash
cd Backend-NestJS
npm install
npx prisma generate
npm run start:dev
```

### Frontend
```bash
cd Front-Audrey-V1-Main-main
npm install
npm start
```

## Fonctionnalités implémentées

1. **Gestion des retraites**
   - Liste des retraites avec filtrage et pagination
   - Détail d'une retraite
   - Création, modification et suppression de retraites

2. **Réservations**
   - Création de réservation
   - Annulation et confirmation de réservation
   - Liste des réservations de l'utilisateur

3. **Utilisateurs**
   - Authentification (login, register, logout)
   - Gestion du profil utilisateur

## Prochaines étapes

1. **Tests**
   - Écriture de tests unitaires pour le backend
   - Écriture de tests unitaires pour le frontend
   - Tests d'intégration

2. **Déploiement**
   - Configuration Docker pour le déploiement
   - Intégration avec Kubernetes

3. **Fonctionnalités additionnelles**
   - Système de paiement
   - Notifications en temps réel
   - Système de messagerie

## Conclusion

L'intégration entre Front-Audrey-V1-Main-main et Backend-NestJS est maintenant fonctionnelle. Les utilisateurs peuvent s'inscrire, se connecter, parcourir les retraites, effectuer des réservations et gérer leur profil. Le backend fournit une API RESTful sécurisée avec authentification JWT et contrôle d'accès basé sur les rôles. La base de données PostgreSQL avec Prisma ORM assure la persistance des données.
