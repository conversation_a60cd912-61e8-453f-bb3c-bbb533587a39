# Architecture des Microservices Retreat And Be

Ce document décrit l'architecture des microservices de la plateforme Retreat And Be, leur déploiement avec Docker et Kubernetes, et la communication entre eux.

## Vue d'ensemble

L'architecture se compose de trois microservices principaux :

1. **Agent-RB** : Service principal pour la gestion des retraites et des partenaires
2. **superagent** : Service d'intelligence artificielle pour l'orchestration des agents
3. **Agent IA** : Service d'intelligence artificielle spécialisé

Ces microservices sont déployés dans des conteneurs Docker et orchestrés avec Kubernetes pour assurer la scalabilité, la résilience et la facilité de déploiement.

## Architecture des Microservices

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│    Agent-RB     │◄────┤   superagent    │◄────┤    Agent IA     │
│                 │     │                 │     │                 │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                                                                 │
│                     Services Partagés                           │
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐  │
│  │             │    │             │    │                     │  │
│  │  PostgreSQL │    │    Redis    │    │  Service de Logging │  │
│  │             │    │             │    │                     │  │
│  └─────────────┘    └─────────────┘    └─────────────────────┘  │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### Responsabilités des Microservices

#### Agent-RB
- Gestion des retraites
- Gestion des partenaires
- Gestion des utilisateurs
- API REST pour les clients frontend

#### superagent
- Orchestration des agents IA
- Traitement des workflows
- Génération de contenu
- Analyse de données

#### Agent IA
- Traitement du langage naturel
- Recommandations personnalisées
- Analyse de sentiment
- Génération de réponses

## Communication entre les Microservices

Les microservices communiquent entre eux via des API REST. Chaque microservice expose des endpoints que les autres peuvent appeler.

### Exemples de Communication

1. **Agent-RB → superagent**
   ```
   GET http://superagent-service:5001/workflows/retreat-planning
   ```

2. **superagent → Agent IA**
   ```
   POST http://agent-ia-service:5002/analyze
   Content-Type: application/json
   
   {
     "text": "Je cherche une retraite de yoga en France en juillet"
   }
   ```

3. **Agent IA → Agent-RB**
   ```
   GET http://agent-rb-service:5000/api/retreats?type=yoga&location=France&month=7
   ```

## Déploiement

### Prérequis
- Docker
- Kubernetes (minikube pour le développement local)
- kubectl

### Déploiement Local avec Docker Compose

Pour le développement local, vous pouvez utiliser Docker Compose :

```bash
docker-compose up
```

Cela démarrera tous les microservices et les services partagés (PostgreSQL, Redis).

### Déploiement sur Kubernetes

Pour déployer sur Kubernetes, utilisez le script `deploy-k8s.sh` :

```bash
./deploy-k8s.sh
```

Ce script effectue les actions suivantes :
1. Construit les images Docker
2. Applique les configurations Kubernetes (ConfigMaps, Secrets)
3. Déploie les microservices
4. Configure l'Ingress pour exposer les services

## Configuration

### Variables d'Environnement

Chaque microservice utilise des variables d'environnement pour sa configuration :

#### Agent-RB
- `ENVIRONMENT` : Environnement d'exécution (development, production)
- `SUPERAGENT_SERVICE_URL` : URL du service superagent
- `AGENT_IA_SERVICE_URL` : URL du service Agent IA

#### superagent
- `ENVIRONMENT` : Environnement d'exécution (development, production)
- `AGENT_RB_SERVICE_URL` : URL du service Agent-RB
- `AGENT_IA_SERVICE_URL` : URL du service Agent IA

#### Agent IA
- `ENVIRONMENT` : Environnement d'exécution (development, production)
- `AGENT_RB_SERVICE_URL` : URL du service Agent-RB
- `SUPERAGENT_SERVICE_URL` : URL du service superagent

### Secrets

Les informations sensibles sont stockées dans des secrets Kubernetes :
- `API_KEY` : Clé API pour les services externes
- `DATABASE_PASSWORD` : Mot de passe de la base de données
- `JWT_SECRET` : Secret pour la génération des JWT

## Surveillance et Logging

Tous les microservices envoient leurs logs à un service de logging centralisé. Les métriques sont également collectées pour la surveillance des performances.

### Endpoints de Santé

Chaque microservice expose des endpoints de santé :
- `/health` : Vérifie l'état général du service
- `/ready` : Vérifie si le service est prêt à recevoir des requêtes

## Développement

### Structure des Projets

Chaque microservice suit une structure similaire :

```
microservice/
├── Dockerfile
├── requirements.txt
├── app.py
├── config/
│   └── settings.py
├── api/
│   └── routes.py
├── models/
│   └── ...
├── services/
│   └── ...
└── utils/
    └── ...
```

### Ajout d'un Nouveau Microservice

Pour ajouter un nouveau microservice :

1. Créer un dossier pour le microservice
2. Créer un Dockerfile
3. Créer les fichiers de configuration Kubernetes
4. Mettre à jour l'Ingress
5. Mettre à jour le script de déploiement

## Dépannage

### Problèmes Courants

1. **Les services ne peuvent pas communiquer entre eux**
   - Vérifier que les noms de service sont corrects
   - Vérifier que les ports sont correctement configurés
   - Vérifier que les services sont dans le même namespace

2. **Les pods ne démarrent pas**
   - Vérifier les logs des pods : `kubectl logs <pod-name>`
   - Vérifier les événements : `kubectl get events`
   - Vérifier les ressources disponibles

3. **Erreurs d'authentification**
   - Vérifier que les secrets sont correctement configurés
   - Vérifier que les variables d'environnement sont correctement définies

## Conclusion

Cette architecture de microservices permet une grande flexibilité et scalabilité pour la plateforme Retreat And Be. Chaque microservice peut être développé, déployé et mis à l'échelle indépendamment, tout en maintenant une communication efficace entre eux.
