{"mcpServers": {"memory": {"command": "npx", "args": ["mcp-server-memory"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "YBSAVEOkwp7NbllbI4BIBK3FsrDSD3xz"}}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}}, "shell": {"command": "uvx", "args": ["mcp-shell-server", "--host", "127.0.0.1", "--port", "5001"], "env": {"ALLOW_COMMANDS": "ls,cat,pwd,grep,wc,touch,find,bash,deploy.sh"}}}, "agents": {"production_manager": {"type": "automation", "trigger": "on_code_change", "watch_files": ["*.js", "*.ts", "*.tsx", "*.py", "*.html", "*.css", "*.sh", "*.bat", "*.json", "*.yaml", "*.yml", "*.toml", "*.md", "*.env", ".giti<PERSON>re", "*.dock<PERSON><PERSON><PERSON>"], "action": [{"command": ["windsurf", "validate"], "directory": "/Users/<USER>/Desktop/project final/Project-Final", "timeout": 30}, {"command": ["windsurf", "execute"], "directory": "/Users/<USER>/Desktop/project final/Project-Final", "timeout": 60}, {"command": ["bash", "deploy.sh"], "directory": "/Users/<USER>/Desktop/project final/Project-Final", "timeout": 90}, {"command": ["git", "add", "."], "directory": "/Users/<USER>/Desktop/project final/Project-Final"}, {"command": ["git", "commit", "-m", "Auto-commit by Claude <PERSON>"], "directory": "/Users/<USER>/Desktop/project final/Project-Final"}, {"command": ["git", "push"], "directory": "/Users/<USER>/Desktop/project final/Project-Final"}], "instructions": {"context": "Architecture microservices complexe avec 24 services containerisés déployés sur Kubernetes via Helm", "architecture": {"microservices": {"Analyzer": {"docker_image": "analyzer:latest", "dockerfile": "./Analyzer/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/analyzer"}, "Decentralized-Storage": {"docker_image": "decentralized-storage:latest", "dockerfile": "./Decentralized-Storage/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/decentralized-storage"}, "Security": {"docker_image": "security:latest", "dockerfile": "./Security/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/security"}, "GPS-Service": {"docker_image": "gps-service:latest", "dockerfile": "./GPS-Service/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/gps-service", "requires": ["Security"]}, "Hotel-Booking": {"docker_image": "hotel-booking:latest", "dockerfile": "./Hotel-Booking/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/hotel-booking", "depends_on": ["Security", "GPS-Service"]}, "Marketplace": {"docker_image": "marketplace:latest", "dockerfile": "./Marketplace/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/marketplace", "depends_on": ["Security"]}, "Messaging-Service": {"docker_image": "messaging-service:latest", "dockerfile": "./messaging-service/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/messaging-service"}, "Retreat-Stream": {"docker_image": "retreat-stream:latest", "dockerfile": "./Retreat-Stream/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/retreat-stream"}, "Retreat-Pro-Matcher": {"docker_image": "retreat-pro-matcher:latest", "dockerfile": "./Retreat-Pro-Matcher/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/retreat-pro-matcher"}, "Website-Creator": {"docker_image": "website-creator:latest", "dockerfile": "./Website-Creator/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/website-creator"}, "VR": {"docker_image": "vr-service:latest", "dockerfile": "./VR/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/vr"}, "Transport-Booking": {"docker_image": "transport-booking:latest", "dockerfile": "./Transport-Booking/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/transport-booking"}, "Car-Rental": {"docker_image": "car-rental:latest", "dockerfile": "./Car-Rental/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/car-rental"}, "Compare-Insurance": {"docker_image": "compare-insurance:latest", "dockerfile": "./Compare-Insurance/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/compare-insurance"}, "Education": {"docker_image": "education-service:latest", "dockerfile": "./Education/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/education"}, "Expense-Sharing": {"docker_image": "expense-sharing:latest", "dockerfile": "./Expense-Sharing/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/expense-sharing"}, "Financial-Management": {"docker_image": "financial-management:latest", "dockerfile": "./Financial-Management/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/financial-management"}, "Flight-Finder": {"docker_image": "flight-finder:latest", "dockerfile": "./Flight-Finder/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/flight-finder"}, "RandB-Loyalty-Program": {"docker_image": "loyalty-program:latest", "dockerfile": "./RandB-Loyalty-Program/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/randb-loyalty-program"}, "search-transport-service": {"docker_image": "search-transport:latest", "dockerfile": "./search-transport-service/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/search-transport-service"}, "web3-nft-service": {"docker_image": "nft-service:latest", "dockerfile": "./web3-nft-service/Dockerfile", "helm_chart": true, "helm_chart_path": "./charts/web3-nft-service"}}, "docker_commands": {"build_all": "docker compose -f docker-compose.build.yml build", "push_all": "docker compose -f docker-compose.build.yml push"}, "kubernetes_commands": {"deploy_full": "helm upgrade --install full-stack ./charts", "monitoring_stack": ["helm install prometheus prometheus-community/kube-prometheus-stack", "helm install grafana grafana/grafana"]}}, "deployment_strategy": {"order": ["Security", "Decentralized-Storage", "GPS-Service", "Hotel-Booking", "Marketplace", "VR", "Transport-Booking", "Website-Creator", "messaging-service", "Flight-Finder", "Retreat-Stream", "Retreat-Pro-Matcher"], "health_checks": {"timeout": "300s", "retries": 3}}, "cascade_base_workflow": ["S<PERSON>quence de déploiement:", "1. Valider la configuration avec 'windsurf validate'", "2. Construire les images avec 'docker compose build'", "3. <PERSON><PERSON><PERSON><PERSON><PERSON> les services critiques (Security, Storage)", "4. <PERSON><PERSON><PERSON><PERSON>er les services dépendants par vagues", "5. <PERSON>écuter les tests d'intégration complets", "6. Synchroniser l'état avec Git"]}}}}