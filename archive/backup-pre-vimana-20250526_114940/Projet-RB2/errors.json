[{"resource": "/Users/<USER>/Desktop/Projet-RB2/Backend/src/logging/LoggingService.ts", "owner": "typescript", "code": "2306", "severity": 8, "message": "File \"/Users/<USER>/Desktop/node_modules/@nestjs/common/index.d.ts\" is not a module.", "source": "ts", "startLineNumber": 1, "startColumn": 28, "endLineNumber": 1, "endColumn": 44, "modelVersionId": 1}, {"resource": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/useAdaptiveBandwidth.ts", "owner": "typescript", "code": "2322", "severity": 8, "message": "Type \"number\" is not assignable to type \"boolean\".", "source": "ts", "startLineNumber": 116, "startColumn": 9, "endLineNumber": 116, "endColumn": 15, "modelVersionId": 27}]