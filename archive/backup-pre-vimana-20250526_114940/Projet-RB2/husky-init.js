/**
 * Script d'initialisation pour <PERSON>sky
 * Ce script configure <PERSON><PERSON> pour exécuter les hooks Git
 */
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Créer le répertoire .husky s'il n'existe pas
const huskyDir = path.join(__dirname, '.husky');
if (!fs.existsSync(huskyDir)) {
  fs.mkdirSync(huskyDir, { recursive: true });
}

// Créer le hook pre-commit
const preCommitPath = path.join(huskyDir, 'pre-commit');
const preCommitContent = `#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# Exécuter lint-staged
npx lint-staged
`;

fs.writeFileSync(preCommitPath, preCommitContent);
fs.chmodSync(preCommitPath, 0o755); // Rendre le fichier exécutable

// <PERSON><PERSON><PERSON> le hook pre-push
const prePushPath = path.join(huskyDir, 'pre-push');
const prePushContent = `#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# Exécuter les tests
echo "Exécution des tests avant le push..."
npm run test:minimal
`;

fs.writeFileSync(prePushPath, prePushContent);
fs.chmodSync(prePushPath, 0o755); // Rendre le fichier exécutable

// Créer le fichier _/husky.sh
const huskyShDir = path.join(huskyDir, '_');
if (!fs.existsSync(huskyShDir)) {
  fs.mkdirSync(huskyShDir, { recursive: true });
}

const huskyShPath = path.join(huskyShDir, 'husky.sh');
const huskyShContent = `#!/bin/sh
if [ -z "$husky_skip_init" ]; then
  debug () {
    if [ "$HUSKY_DEBUG" = "1" ]; then
      echo "husky (debug) - $1"
    fi
  }

  readonly hook_name="$(basename "$0")"
  debug "starting $hook_name..."

  if [ "$HUSKY" = "0" ]; then
    debug "HUSKY env variable is set to 0, skipping hook"
    exit 0
  fi

  if [ -f ~/.huskyrc ]; then
    debug "sourcing ~/.huskyrc"
    . ~/.huskyrc
  fi

  export readonly husky_skip_init=1
  sh -e "$0" "$@"
  exitCode="$?"

  if [ $exitCode != 0 ]; then
    echo "husky - $hook_name hook exited with code $exitCode (error)"
  fi

  exit $exitCode
fi
`;

fs.writeFileSync(huskyShPath, huskyShContent);
fs.chmodSync(huskyShPath, 0o755); // Rendre le fichier exécutable

console.log('Husky hooks configurés avec succès!');

// Mettre à jour package.json pour ajouter le script prepare
try {
  const packageJsonPath = path.join(__dirname, 'package.json');
  const packageJson = require(packageJsonPath);
  
  if (!packageJson.scripts) {
    packageJson.scripts = {};
  }
  
  packageJson.scripts.prepare = 'node husky-init.js';
  
  if (!packageJson['lint-staged']) {
    packageJson['lint-staged'] = {
      '*.{js,ts,tsx}': [
        'eslint --fix',
        'npm run test:minimal -- --findRelatedTests'
      ]
    };
  }
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log('package.json mis à jour avec succès!');
} catch (error) {
  console.error('Erreur lors de la mise à jour de package.json:', error.message);
}
