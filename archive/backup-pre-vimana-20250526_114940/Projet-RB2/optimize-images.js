const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Vérifier si les dépendances nécessaires sont installées
try {
  require.resolve('sharp');
} catch (e) {
  console.log('Installing sharp package...');
  execSync('npm install sharp');
}

const sharp = require('sharp');

// Configuration
const config = {
  // Dossiers à scanner pour les images
  directories: [
    './frontend/public/images',
    './frontend/src/assets',
  ],
  // Extensions d'image à optimiser
  extensions: ['.jpg', '.jpeg', '.png', '.webp'],
  // Tailles d'image à générer
  sizes: [400, 800, 1200],
  // Qualité de compression (0-100)
  quality: 80,
  // Convertir en WebP
  convertToWebP: true,
  // Dossier de sortie pour les images optimisées
  outputDir: 'optimized',
};

// Fonction pour optimiser une image
async function optimizeImage(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  if (!config.extensions.includes(ext)) {
    return;
  }

  const fileName = path.basename(filePath, ext);
  const dirName = path.dirname(filePath);
  const outputDirPath = path.join(dirName, config.outputDir);

  // Créer le dossier de sortie s'il n'existe pas
  if (!fs.existsSync(outputDirPath)) {
    fs.mkdirSync(outputDirPath, { recursive: true });
  }

  // Charger l'image
  const image = sharp(filePath);
  const metadata = await image.metadata();

  // Optimiser l'image originale
  await image
    .resize({
      width: metadata.width,
      height: metadata.height,
      fit: 'inside',
      withoutEnlargement: true,
    })
    .jpeg({ quality: config.quality })
    .toFile(path.join(outputDirPath, `${fileName}-original${ext}`));

  // Générer des versions redimensionnées
  for (const size of config.sizes) {
    // Ignorer les tailles plus grandes que l'original
    if (size > metadata.width) {
      continue;
    }

    // Redimensionner en JPEG
    await sharp(filePath)
      .resize({
        width: size,
        height: null,
        fit: 'inside',
        withoutEnlargement: true,
      })
      .jpeg({ quality: config.quality })
      .toFile(path.join(outputDirPath, `${fileName}-${size}${ext}`));

    // Convertir en WebP si demandé
    if (config.convertToWebP) {
      await sharp(filePath)
        .resize({
          width: size,
          height: null,
          fit: 'inside',
          withoutEnlargement: true,
        })
        .webp({ quality: config.quality })
        .toFile(path.join(outputDirPath, `${fileName}-${size}.webp`));
    }
  }

  console.log(`Optimized: ${filePath}`);
}

// Fonction pour scanner un dossier récursivement
async function scanDirectory(directory) {
  if (!fs.existsSync(directory)) {
    console.log(`Directory not found: ${directory}`);
    return;
  }

  const files = fs.readdirSync(directory);

  for (const file of files) {
    const filePath = path.join(directory, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && file !== config.outputDir) {
      await scanDirectory(filePath);
    } else if (stat.isFile()) {
      const ext = path.extname(filePath).toLowerCase();
      if (config.extensions.includes(ext)) {
        await optimizeImage(filePath);
      }
    }
  }
}

// Fonction principale
async function main() {
  console.log('Starting image optimization...');

  for (const directory of config.directories) {
    console.log(`Scanning directory: ${directory}`);
    await scanDirectory(directory);
  }

  console.log('Image optimization completed!');
}

// Exécuter le script
main().catch(error => {
  console.error('Error optimizing images:', error);
  process.exit(1);
});
