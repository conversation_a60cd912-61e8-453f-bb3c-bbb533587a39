# Stratégie de Déploiement - Retreat And Be

## Introduction

Ce document définit la stratégie de déploiement complète pour le projet Retreat And Be. Il couvre les environnements, les processus, les outils et les meilleures pratiques pour assurer des déploiements fiables, sécurisés et efficaces.

## Objectifs

1. Minimiser les temps d'arrêt lors des déploiements
2. Réduire les risques liés aux déploiements
3. Permettre des déploiements fréquents et prévisibles
4. Faciliter le rollback en cas de problème
5. Assurer la traçabilité et l'auditabilité des déploiements

## Architecture de Déploiement

### Infrastructure Cloud

Le projet Retreat And Be est déployé sur une infrastructure cloud avec les caractéristiques suivantes :

- **Provider** : AWS (Amazon Web Services)
- **Régions** : Multi-région (Europe, Amérique du Nord, Asie)
- **Services principaux** :
  - EKS (Elastic Kubernetes Service) pour l'orchestration des conteneurs
  - RDS pour les bases de données PostgreSQL
  - ElastiCache pour Redis
  - S3 pour le stockage d'objets
  - CloudFront pour le CDN
  - Route53 pour le DNS

### Environnements

#### 1. Développement
- **Objectif** : Développement et tests initiaux
- **Infrastructure** : Cluster Kubernetes dédié, ressources limitées
- **Accès** : Restreint aux développeurs
- **Données** : Jeux de données de test, pas de données sensibles
- **URL** : dev.retreatandbe.com

#### 2. Test (QA)
- **Objectif** : Tests fonctionnels, tests d'intégration
- **Infrastructure** : Cluster Kubernetes dédié, similaire à la production
- **Accès** : Équipes de développement et QA
- **Données** : Jeux de données de test complets
- **URL** : test.retreatandbe.com

#### 3. Staging
- **Objectif** : Validation finale avant production
- **Infrastructure** : Identique à la production
- **Accès** : Équipes de développement, QA et product owners
- **Données** : Copie anonymisée des données de production
- **URL** : staging.retreatandbe.com

#### 4. Production
- **Objectif** : Environnement utilisateur final
- **Infrastructure** : Haute disponibilité, multi-région
- **Accès** : Très restreint, uniquement via processus approuvés
- **Données** : Données réelles des utilisateurs
- **URL** : retreatandbe.com

## Stratégie de Déploiement

### 1. Déploiement Bleu-Vert

Pour les mises à jour majeures et les changements critiques, nous utilisons une stratégie de déploiement bleu-vert :

1. Deux environnements identiques sont maintenus (bleu et vert)
2. Les utilisateurs sont dirigés vers l'environnement actif (ex: bleu)
3. Les nouvelles versions sont déployées sur l'environnement inactif (ex: vert)
4. Tests et validation sur l'environnement inactif
5. Bascule du trafic de l'environnement bleu vers l'environnement vert
6. L'ancien environnement actif (bleu) reste disponible pour un rollback rapide

**Avantages** :
- Temps d'arrêt quasi nul
- Possibilité de rollback instantané
- Tests complets avant exposition aux utilisateurs

**Utilisé pour** :
- Mises à jour majeures de l'architecture
- Changements de schéma de base de données importants
- Nouvelles fonctionnalités critiques

### 2. Déploiement Canary

Pour les fonctionnalités non critiques et les améliorations progressives :

1. Déploiement de la nouvelle version pour un petit pourcentage d'utilisateurs (5-10%)
2. Surveillance des métriques et des erreurs
3. Augmentation progressive du pourcentage d'utilisateurs exposés à la nouvelle version
4. Déploiement complet une fois la stabilité confirmée

**Avantages** :
- Exposition limitée aux risques
- Détection précoce des problèmes
- Validation progressive

**Utilisé pour** :
- Nouvelles fonctionnalités non critiques
- Améliorations de l'interface utilisateur
- Optimisations de performance

### 3. Déploiement Rolling

Pour les mises à jour mineures et les correctifs :

1. Mise à jour progressive des instances, une par une
2. Vérification de la santé de chaque instance avant de passer à la suivante
3. Maintien de la disponibilité du service pendant tout le processus

**Avantages** :
- Simplicité
- Pas de ressources supplémentaires requises
- Déploiement continu

**Utilisé pour** :
- Correctifs de bugs
- Mises à jour mineures
- Ajustements de configuration

## Pipeline CI/CD

### Outils

- **Gestion de Code Source** : GitHub
- **CI/CD** : GitHub Actions
- **Conteneurisation** : Docker
- **Orchestration** : Kubernetes
- **Gestion de Configuration** : Helm
- **Monitoring** : Prometheus, Grafana
- **Logging** : ELK Stack (Elasticsearch, Logstash, Kibana)

### Flux de Travail

#### 1. Développement
- Les développeurs travaillent sur des branches de fonctionnalités
- Tests unitaires et linting exécutés localement
- Pull requests créées pour la revue de code

#### 2. Intégration Continue
- Déclenchée par les push sur les branches et les pull requests
- Exécution des tests unitaires et d'intégration
- Analyse statique du code
- Construction des images Docker
- Publication des images dans le registre Docker

#### 3. Déploiement Continu
- Déploiement automatique vers l'environnement de développement pour les branches principales
- Déploiement manuel vers les environnements de test et staging
- Déploiement en production après approbation

### Exemple de Pipeline GitHub Actions

```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
      - name: Install dependencies
        run: npm ci
      - name: Run linting
        run: npm run lint
      - name: Run tests
        run: npm test

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build and push
        uses: docker/build-push-action@v3
        with:
          push: true
          tags: retreatandbe/backend:${{ github.sha }}

  deploy-dev:
    needs: build
    if: github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Kubectl
        uses: azure/setup-kubectl@v3
      - name: Set up Helm
        uses: azure/setup-helm@v3
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-1
      - name: Update kubeconfig
        run: aws eks update-kubeconfig --name retreatandbe-dev
      - name: Deploy to development
        run: |
          helm upgrade --install backend ./charts/backend \
            --set image.tag=${{ github.sha }} \
            --namespace development
```

## Gestion des Versions

### Versionnement Sémantique

Le projet utilise le versionnement sémantique (SemVer) avec le format MAJOR.MINOR.PATCH :

- **MAJOR** : Changements incompatibles avec les versions précédentes
- **MINOR** : Ajout de fonctionnalités rétrocompatibles
- **PATCH** : Corrections de bugs rétrocompatibles

### Branches Git

- **main** : Code en production
- **develop** : Code de développement intégré
- **feature/*** : Branches de fonctionnalités
- **release/*** : Branches de préparation des releases
- **hotfix/*** : Corrections urgentes pour la production

### Tags Git

Chaque release est taguée avec son numéro de version :

```bash
git tag -a v1.2.3 -m "Version 1.2.3"
git push origin v1.2.3
```

## Déploiement des Microservices

### Dépendances entre Services

Les dépendances entre microservices sont gérées via :

1. **Helm Charts** : Définition des dépendances et de l'ordre de déploiement
2. **Health Checks** : Vérification de la disponibilité des services dépendants
3. **Circuit Breakers** : Gestion des défaillances temporaires

### Ordre de Déploiement

1. Services d'infrastructure (base de données, cache, etc.)
2. Services fondamentaux (authentification, utilisateurs)
3. Services métier (retraites, réservations, etc.)
4. Services frontaux (API Gateway, applications web/mobile)

## Stratégies de Migration de Base de Données

### Migrations Automatisées

Les migrations de schéma sont gérées via Prisma Migrate :

1. Création des migrations dans l'environnement de développement
2. Test des migrations dans les environnements de test et staging
3. Application des migrations en production avant le déploiement du code

### Stratégies de Compatibilité

Pour assurer la compatibilité entre les versions :

1. **Migrations additives uniquement** : Ajout de colonnes et tables sans supprimer les existantes
2. **Déploiement en deux phases** pour les changements incompatibles :
   - Phase 1 : Ajout du nouveau schéma, code compatible avec les deux schémas
   - Phase 2 : Suppression de l'ancien schéma après migration complète

## Tests de Déploiement

### Tests de Smoke

Exécutés immédiatement après le déploiement pour vérifier les fonctionnalités de base :

```bash
# Exemple de script de smoke test
#!/bin/bash
set -e

echo "Testing API health endpoint..."
curl -f https://api.retreatandbe.com/health

echo "Testing authentication..."
TOKEN=$(curl -s -X POST https://api.retreatandbe.com/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"testpassword"}' \
  | jq -r '.token')

if [ -z "$TOKEN" ]; then
  echo "Authentication failed"
  exit 1
fi

echo "Testing retreats endpoint..."
curl -f -H "Authorization: Bearer $TOKEN" https://api.retreatandbe.com/retreats

echo "All smoke tests passed!"
```

### Tests de Santé

Vérification continue de la santé du système après déploiement :

- Surveillance des métriques clés (latence, taux d'erreur, etc.)
- Comparaison avec les valeurs de référence
- Alertes en cas d'anomalies

## Rollback

### Stratégie de Rollback

En cas de problème détecté après déploiement :

1. **Rollback automatique** si les tests de smoke échouent
2. **Rollback manuel** déclenché par l'équipe d'exploitation
3. **Rollback partiel** pour les microservices spécifiques affectés

### Procédure de Rollback

```bash
# Exemple de commande de rollback avec Helm
helm rollback backend 1 --namespace production

# Exemple de rollback bleu-vert
kubectl apply -f kubernetes/ingress-blue.yaml
```

## Sécurité du Déploiement

### Gestion des Secrets

- Utilisation de AWS Secrets Manager pour les secrets de production
- Kubernetes Secrets pour les environnements de développement et test
- Rotation régulière des secrets

### Contrôle d'Accès

- Principe du moindre privilège pour tous les accès
- Authentification multi-facteurs pour les déploiements en production
- Audit logging de toutes les actions de déploiement

## Monitoring et Alerting

### Métriques Clés

- **Métriques système** : CPU, mémoire, disque, réseau
- **Métriques applicatives** : Latence, taux d'erreur, requêtes par seconde
- **Métriques métier** : Taux de conversion, sessions utilisateur, réservations

### Alertes

- Alertes automatiques en cas d'anomalies post-déploiement
- Canaux de notification : Slack, email, SMS
- Procédures d'escalade définies

## Documentation et Communication

### Documentation des Déploiements

Chaque déploiement est documenté avec :

- Version déployée
- Changements inclus
- Résultats des tests
- Problèmes rencontrés et solutions

### Communication

- Annonce des déploiements planifiés via Slack et email
- Mise à jour du statut pendant le déploiement
- Rapport post-déploiement

## Plan d'Amélioration Continue

### Court Terme (3 mois)

1. Automatiser complètement les déploiements vers les environnements de test et staging
2. Améliorer les tests de smoke pour couvrir plus de scénarios
3. Mettre en place des métriques de déploiement (fréquence, temps, taux d'échec)

### Moyen Terme (6 mois)

1. Implémenter le déploiement canary pour tous les services
2. Mettre en place des tests de chaos pour valider la résilience
3. Automatiser les rollbacks basés sur les métriques

### Long Terme (12 mois)

1. Déploiement multi-région synchronisé
2. Mise en place d'un système de déploiement progressif géographique
3. Implémentation de l'observabilité avancée pour les déploiements

## Conclusion

Cette stratégie de déploiement fournit un cadre complet pour assurer des déploiements fiables, sécurisés et efficaces pour le projet Retreat And Be. En suivant ces pratiques, l'équipe pourra livrer des fonctionnalités de manière fréquente et prévisible, tout en minimisant les risques et les temps d'arrêt.

La stratégie sera révisée trimestriellement pour s'adapter à l'évolution du projet et intégrer les nouvelles meilleures pratiques en matière de déploiement.
