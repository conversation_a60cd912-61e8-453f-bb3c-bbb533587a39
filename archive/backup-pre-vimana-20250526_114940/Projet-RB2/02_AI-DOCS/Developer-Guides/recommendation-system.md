# Guide du Système de Recommandation - Retreat And Be

Ce document décrit le système de recommandation de la plateforme Retreat And Be, ses composants, ses fonctionnalités et comment l'utiliser efficacement.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Architecture](#architecture)
3. [Composants Frontend](#composants-frontend)
4. [Composants Backend](#composants-backend)
5. [Modération de contenu avec IA](#modération-de-contenu-avec-ia)
6. [Analyse et rapports](#analyse-et-rapports)
7. [Scripts utilitaires](#scripts-utilitaires)
8. [Bonnes pratiques](#bonnes-pratiques)
9. [Dépannage](#dépannage)

## Vue d'ensemble

Le système de recommandation de Retreat And Be est conçu pour suggérer des retraites, des partenaires et des cours pertinents aux utilisateurs en fonction de leurs préférences, de leur historique d'interactions et du contexte. Le système utilise plusieurs stratégies de recommandation et intègre des fonctionnalités de modération de contenu basées sur l'IA pour garantir la qualité et la pertinence des recommandations.

### Objectifs clés

- Améliorer l'engagement des utilisateurs en suggérant du contenu pertinent
- Augmenter les conversions en recommandant des retraites adaptées aux intérêts des utilisateurs
- Assurer la diversité des recommandations pour éviter les bulles de filtrage
- Garantir la qualité du contenu grâce à la modération automatisée et manuelle
- Fournir des analyses détaillées pour mesurer l'efficacité des recommandations

## Architecture

Le système de recommandation est composé de plusieurs microservices qui interagissent entre eux :

1. **Service de recommandation** : Génère des recommandations en utilisant différentes stratégies
2. **Service de modération** : Filtre le contenu inapproprié à l'aide de l'IA
3. **Service d'analyse** : Collecte et analyse les métriques de performance
4. **Frontend** : Affiche les recommandations et les analyses aux utilisateurs

### Flux de données

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Utilisateur │────▶│   Frontend  │────▶│  Service de │
└─────────────┘     └─────────────┘     │recommandation│
                          ▲              └──────┬──────┘
                          │                     │
                          │              ┌──────▼──────┐
                          │              │  Service de │
                          │              │  modération │
                          │              └──────┬──────┘
                          │                     │
                          │              ┌──────▼──────┐
                          └──────────────│  Service    │
                                         │  d'analyse  │
                                         └─────────────┘
```

## Composants Frontend

### Tableau de bord d'analyse

Le tableau de bord d'analyse permet aux créateurs de contenu de visualiser les performances de leurs retraites et recommandations.

#### Utilisation

```jsx
import { AnalyticsDashboard } from './components/analytics/AnalyticsDashboard';

const CreatorDashboard = ({ creatorId }) => {
  return (
    <div>
      <h1>Tableau de bord</h1>
      <AnalyticsDashboard creatorId={creatorId} />
    </div>
  );
};
```

#### Onglets disponibles

- **Vue d'ensemble** : Résumé des métriques clés
- **Engagement** : Métriques détaillées sur l'engagement des utilisateurs
- **Audience** : Informations sur l'audience et les followers
- **Revenus** : Analyse des revenus générés
- **Contenu** : Performance du contenu publié
- **Prévisions** : Prévisions d'engagement, d'audience et de revenus
- **Benchmarks** : Comparaison avec d'autres créateurs

### Tableau de bord de modération

Le tableau de bord de modération permet aux administrateurs et modérateurs de gérer le contenu de la plateforme.

#### Utilisation

```jsx
import { ModerationDashboard } from './components/moderation/ModerationDashboard';

const AdminDashboard = ({ userRole }) => {
  return (
    <div>
      <h1>Administration</h1>
      <ModerationDashboard userRole={userRole} />
    </div>
  );
};
```

#### Onglets disponibles

- **Signalements** : Liste des contenus signalés par les utilisateurs
- **Règles** : Configuration des règles de modération
- **Statistiques** : Métriques de modération

### Formulaire de modération de contenu

Le formulaire de modération de contenu permet de tester la modération de contenu en temps réel.

#### Utilisation

```jsx
import { ContentModerationForm } from './components/moderation/ContentModerationForm';

const TestModeration = () => {
  const handleModerationComplete = (result) => {
    console.log('Résultat de la modération:', result);
  };

  return (
    <div>
      <h1>Test de modération</h1>
      <ContentModerationForm onModerationComplete={handleModerationComplete} />
    </div>
  );
};
```

## Composants Backend

### Service de recommandation

Le service de recommandation génère des recommandations en utilisant différentes stratégies :

- **Basé sur le contenu** : Recommande du contenu similaire à celui que l'utilisateur a apprécié
- **Collaboratif** : Recommande du contenu apprécié par des utilisateurs similaires
- **Hybride** : Combine les approches basées sur le contenu et collaboratives
- **Contextuel** : Prend en compte le contexte de l'utilisateur (localisation, saison, etc.)
- **Social** : Recommande du contenu apprécié par les amis de l'utilisateur

### Service de modération

Le service de modération utilise l'IA pour détecter et filtrer le contenu inapproprié. Il prend en charge :

- **Modération de texte** : Détecte le langage inapproprié, les insultes, etc.
- **Modération d'image** : Détecte le contenu visuel inapproprié
- **Modération de vidéo** : Analyse les vidéos pour détecter le contenu inapproprié

## Modération de contenu avec IA

La modération de contenu utilise le microservice Agent IA pour analyser le contenu et détecter les violations des règles de modération.

### Intégration avec Agent IA

Le service `aiModerationService` intègre le microservice Agent IA pour la modération de contenu :

```typescript
import { AIService } from '../../../Agent IA/src/services/ai/AIService';

// Initialiser le service IA
const aiService = AIService.getInstance();
await aiService.setProvider('openai');

// Modérer du contenu
const result = await aiService.generateResponse(prompt);
```

### Règles de modération

Les règles de modération sont configurables via le tableau de bord de modération. Chaque règle comprend :

- **Nom** : Nom descriptif de la règle
- **Description** : Description détaillée de la règle
- **Type** : Type de contenu (texte, image, vidéo)
- **Sévérité** : Niveau de sévérité (faible, moyen, élevé)
- **Motif** : Motif de détection (pour le texte)
- **Catégorie** : Catégorie de contenu inapproprié (pour les images et vidéos)
- **Seuil** : Seuil de confiance pour déclencher la règle

## Analyse et rapports

Le système d'analyse collecte et analyse les métriques de performance du système de recommandation.

### Métriques clés

- **Engagement** : Vues, likes, commentaires, partages, etc.
- **Conversion** : Inscriptions, achats, etc.
- **Performance** : Temps de réponse, taux d'erreur, etc.
- **Qualité** : Précision, diversité, couverture, etc.

### Exportation de données

Les données d'analyse peuvent être exportées dans différents formats :

- **CSV** : Pour l'analyse dans des tableurs
- **JSON** : Pour l'intégration avec d'autres systèmes
- **XML** : Pour l'échange de données structurées
- **Excel** : Pour l'analyse avancée
- **PDF** : Pour les rapports formels

## Scripts utilitaires

### Rapport de performance

Le script `recommendation-performance-report.js` génère un rapport détaillé sur les performances du système de recommandation.

#### Utilisation

```bash
node recommendation-performance-report.js [options]
```

#### Options

- `--format=json|pdf|html` : Format de sortie (défaut: json)
- `--output=<path>` : Chemin du fichier de sortie
- `--days=<number>` : Nombre de jours d'historique à analyser (défaut: 7)
- `--detailed` : Inclure des informations détaillées
- `--api-url=<url>` : URL de l'API (défaut: http://localhost:3000)
- `--token=<jwt>` : Token JWT pour l'authentification

### Planification des rapports

Le script `schedule-recommendation-reports.js` planifie l'exécution automatique des rapports de performance et leur envoi par email.

#### Utilisation

```bash
node schedule-recommendation-reports.js [options]
```

#### Options

- `--schedule=<cron>` : Expression cron pour la planification (défaut: "0 0 * * 0")
- `--recipients=<emails>` : Liste d'emails séparés par des virgules
- `--format=<format>` : Format du rapport (défaut: html)
- `--days=<number>` : Nombre de jours d'historique à analyser (défaut: 7)
- `--detailed` : Inclure des informations détaillées
- `--run-now` : Exécuter immédiatement en plus de planifier

## Bonnes pratiques

### Modération de contenu

- Configurer des règles de modération adaptées à votre audience
- Réviser régulièrement les décisions de modération automatique
- Ajuster les seuils de confiance en fonction des faux positifs et faux négatifs

### Recommandations

- Équilibrer la précision et la diversité des recommandations
- Tester différentes stratégies de recommandation pour différents segments d'utilisateurs
- Analyser régulièrement les performances des recommandations

### Analyse

- Définir des KPIs clairs pour mesurer le succès
- Comparer les performances avant et après les modifications
- Utiliser les prévisions pour anticiper les tendances

## Dépannage

### Problèmes courants

#### Les recommandations ne s'affichent pas

- Vérifier que le service de recommandation est en cours d'exécution
- Vérifier les logs pour détecter d'éventuelles erreurs
- Vérifier que l'utilisateur a suffisamment d'interactions pour générer des recommandations

#### La modération est trop stricte ou trop permissive

- Ajuster les seuils de confiance des règles de modération
- Ajouter des exceptions pour les faux positifs courants
- Enrichir les données d'entraînement du modèle IA

#### Les rapports de performance échouent

- Vérifier les permissions d'accès aux données
- Vérifier la connectivité avec la base de données
- Vérifier l'espace disque disponible pour les rapports
