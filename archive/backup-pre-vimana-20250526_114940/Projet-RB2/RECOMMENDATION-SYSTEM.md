# Retreat And Be - Système de Recommandation

Ce document présente le système de recommandation de la plateforme Retreat And Be, implémenté dans le cadre du Sprint 4.

## Structure du projet

- `Backend-NestJS/` : Backend du système de recommandation (NestJS)
- `Front-Audrey-V1-Main-main/` : Frontend de la plateforme (React)

## Fonctionnalités principales

### Sprint 4 - Transparence et Feedback

- **Explications améliorées** : Explications détaillées et personnalisées des recommandations
- **Système de feedback** : Collecte et analyse du feedback utilisateur sur les recommandations
- **Intégration avec la modération** : Filtrage des recommandations inappropriées
- **Intégration avec l'analytics** : Suivi des interactions utilisateur
- **Visualisations** : Représentations graphiques des facteurs de recommandation

## Installation

### Backend

```bash
cd Backend-NestJS
npm install
```

### Frontend

```bash
cd Front-Audrey-V1-Main-main
npm install
```

## Démarrage

### Backend

```bash
cd Backend-NestJS
npm run start:dev
```

### Frontend

```bash
cd Front-Audrey-V1-Main-main
npm start
```

## Tests

### Tests unitaires

```bash
cd Backend-NestJS
npm run test
```

### Tests d'intégration

```bash
cd Backend-NestJS
./scripts/run-integration-tests.sh
```

### Tests de performance

```bash
cd Backend-NestJS
./scripts/run-performance-tests.sh
```

## Monitoring

Le système de recommandation est équipé d'une infrastructure de monitoring complète pour surveiller ses performances et sa santé.

### Démarrage de l'infrastructure de monitoring

```bash
cd Backend-NestJS/monitoring
./start-monitoring.sh
```

### Accès aux tableaux de bord

- **Grafana** : http://localhost:3001 (admin/admin)
- **Prometheus** : http://localhost:9090
- **AlertManager** : http://localhost:9093

### Métriques surveillées

- Temps de réponse des API
- Taux d'erreur
- Taux de clic (CTR)
- Taux de feedback positif
- Utilisation des ressources (CPU, mémoire)
- Performances de la base de données

### Alertes

Des alertes sont configurées pour notifier l'équipe en cas de problèmes :

- Taux d'erreur élevé (> 5%)
- Temps de réponse lent (> 500ms)
- Taux de modération élevé (> 20%)
- Taux de clic bas (< 2%)
- Taux de feedback négatif élevé (> 50%)
- Service indisponible

Pour plus d'informations, consultez les [métriques de surveillance](Backend-NestJS/docs/monitoring-metrics.md).

## Déploiement

Pour préparer le déploiement :

```bash
cd Backend-NestJS
./scripts/prepare-deployment.sh
```

Pour plus d'informations, consultez le [guide de déploiement](Backend-NestJS/docs/deployment-guide.md).

## Documentation

### Documentation technique

- [Fonctionnalités du Sprint 4](Backend-NestJS/docs/sprint4-features.md)
- [Guide de déploiement](Backend-NestJS/docs/deployment-guide.md)
- [Guide de résolution des problèmes](Backend-NestJS/docs/troubleshooting-guide.md)
- [Métriques de surveillance](Backend-NestJS/docs/monitoring-metrics.md)
- [Bonnes pratiques](Backend-NestJS/docs/best-practices.md)
- [Guide d'intégration avec d'autres microservices](Backend-NestJS/docs/microservices-integration-guide.md)

### Documentation utilisateur

- [Guide utilisateur des nouvelles fonctionnalités](Front-Audrey-V1-Main-main/docs/user-guide-sprint4.md)

### Contribution

- [Guide de contribution](CONTRIBUTING.md)
- [Code de conduite](CODE_OF_CONDUCT.md)

### Monitoring

- [Tableau de bord Grafana](Backend-NestJS/monitoring/grafana-dashboard.json)
- [Configuration Prometheus](Backend-NestJS/monitoring/prometheus.yml)
- [Règles d'alerte](Backend-NestJS/monitoring/rules/recommendation_alerts.yml)
- [Configuration AlertManager](Backend-NestJS/monitoring/alertmanager.yml)

### Roadmap

- [Roadmap du système de recommandation](recommendation_roadmap.md)
- [Analyse des écarts](gap_analysis.md)

## Sprints

- [Sprint 4 - Transparence et Feedback](README-SPRINT4.md)

## Architecture

### Backend (NestJS)

Le backend du système de recommandation est organisé en modules :

- **Module de recommandation** : Génération et filtrage des recommandations
- **Module d'explication** : Génération d'explications améliorées
- **Module de feedback** : Gestion du feedback utilisateur
- **Module de modération** : Intégration avec le service de modération
- **Module d'analytics** : Suivi des interactions utilisateur

### Frontend (React)

Le frontend comprend les composants suivants :

- **RecommendationCard** : Affichage d'une recommandation avec explications et feedback
- **EnhancedExplanation** : Affichage des explications améliorées
- **FeedbackButtons** : Boutons pour donner un feedback
- **RecommendationsPage** : Page principale des recommandations

## API

### Recommandations

- `GET /api/recommendations` : Récupérer des recommandations
- `GET /api/recommendations/:id` : Récupérer une recommandation spécifique

### Explications

- `GET /api/recommendations/explanations/:type/:id` : Récupérer une explication améliorée

### Feedback

- `POST /api/recommendations/feedback` : Enregistrer un feedback
- `GET /api/recommendations/feedback/:type/:id` : Récupérer les feedbacks pour une recommandation
- `GET /api/recommendations/feedback/user` : Récupérer les feedbacks de l'utilisateur courant
- `DELETE /api/recommendations/feedback/:id` : Supprimer un feedback

### Modération

- `POST /api/recommendations/moderation/check/:type/:id` : Vérifier si une recommandation est autorisée
- `POST /api/recommendations/moderation/report/:type/:id` : Signaler une recommandation
- `GET /api/recommendations/moderation/status/:type/:id` : Récupérer le statut de modération d'une recommandation
- `POST /api/recommendations/moderation/filter` : Filtrer des recommandations

### Analytics

- `POST /api/recommendations/analytics-integration/view/:type/:id` : Enregistrer un événement de visualisation
- `POST /api/recommendations/analytics-integration/click/:type/:id` : Enregistrer un événement de clic
- `POST /api/recommendations/analytics-integration/explanation-view/:type/:id` : Enregistrer un événement de visualisation d'explication
- `POST /api/recommendations/analytics-integration/explanation-feedback/:type/:id` : Enregistrer un événement de feedback sur une explication

## Intégration avec d'autres services

### Service de modération

Le système de recommandation s'intègre avec le service de modération pour :
- Filtrer les recommandations inappropriées
- Vérifier si une recommandation est autorisée
- Signaler des recommandations inappropriées

### Service d'analytics

Le système de recommandation s'intègre avec le service d'analytics pour :
- Suivre les interactions utilisateur avec les recommandations
- Analyser l'efficacité des recommandations
- Améliorer la qualité des recommandations futures

## Prochaines étapes

Les prochaines étapes pour le système de recommandation sont définies dans le Sprint 5 :

- Mise en place d'un système d'apprentissage continu
- Personnalisation avancée des recommandations
- Recommandations contextuelles
- Optimisation multi-objectifs
- Intégration avec d'autres microservices

Pour plus de détails, consultez :
- [Roadmap du système de recommandation](recommendation_roadmap.md)
- [Plan de transition vers le Sprint 5](Backend-NestJS/docs/sprint4-to-sprint5-transition.md)
- [Spécifications du Sprint 5](Backend-NestJS/docs/sprint5-specifications.md)

### Préparation pour le Sprint 5

Pour préparer la transition vers le Sprint 5, les ressources suivantes sont disponibles :

- [Script de préparation des données](Backend-NestJS/scripts/prepare-data-for-sprint5.js) : Script pour préparer les données collectées pendant le Sprint 4 pour leur utilisation dans le Sprint 5
- [Plan de formation](Backend-NestJS/docs/training-plan.md) : Plan de formation pour les équipes qui utiliseront le système de recommandation
- [Guide de migration de la base de données](Backend-NestJS/docs/database-migration-guide.md) : Guide pour migrer la base de données du Sprint 4 au Sprint 5
- [Plan de déploiement progressif](Backend-NestJS/docs/progressive-deployment-plan.md) : Plan pour déployer progressivement les fonctionnalités du Sprint 5
- [Script de préparation de l'infrastructure ML](Backend-NestJS/scripts/setup-ml-infrastructure.sh) : Script pour préparer l'infrastructure d'apprentissage automatique
- [Guide d'intégration avec d'autres microservices](Backend-NestJS/docs/microservices-integration-guide.md) : Guide pour intégrer le système de recommandation avec d'autres microservices

## Comment contribuer

Nous accueillons les contributions de la communauté ! Si vous souhaitez contribuer au système de recommandation, veuillez consulter notre [guide de contribution](CONTRIBUTING.md).

### Processus de contribution

1. Forkez le dépôt
2. Créez une branche pour votre contribution (`git checkout -b feature/amazing-feature`)
3. Committez vos changements (`git commit -m 'Add some amazing feature'`)
4. Poussez vers la branche (`git push origin feature/amazing-feature`)
5. Ouvrez une Pull Request

### Code de conduite

Nous attendons de tous les contributeurs qu'ils respectent notre [code de conduite](CODE_OF_CONDUCT.md).
