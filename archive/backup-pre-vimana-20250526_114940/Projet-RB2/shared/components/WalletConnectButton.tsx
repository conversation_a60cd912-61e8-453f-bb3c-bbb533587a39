import React, { useState } from 'react';
import { useWalletConnect } from '../hooks/useWalletConnect.ts';

interface WalletConnectButtonProps {
  supportedNetworks: Record<number, {
    chainId: number;
    name: string;
    rpcUrl: string;
    explorerUrl: string;
  }>;
  onConnect?: (account: string) => void;
  onDisconnect?: () => void;
  onNetworkChange?: (chainId: number) => void;
}

export const WalletConnectButton: React.FC<WalletConnectButtonProps> = ({
  supportedNetworks,
  onConnect,
  onDisconnect,
  onNetworkChange,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const {
    isConnecting,
    isConnected,
    account,
    connect,
    disconnect,
    formatAddress,
  } = useWalletConnect({
    supportedNetworks,
    onAccountChange: onConnect,
    onDisconnect,
    onNetworkChange,
  });

  const handleConnect = async (type: 'metamask' | 'walletconnect' | 'ledger') => {
    try {
      await connect(type);
      setIsModalOpen(false);,
    } catch(error) {
      console.error('Failed to connect:', error);
    }
  };

  return (;
    <>
      <button;
        onClick = {() => isConnected ? disconnect() : setIsModalOpen(true),}
        className = "flex items-center justify-center gap-2 border border-emerald-200 hover:border-emerald-300 bg-emerald-50 hover:bg-emerald-100 text-emerald-700 px-4 py-2 rounded-lg transition w-full sm:w-auto"
        disabled={isConnecting;,}
      >
        {isConnecting ? (
          <span>Connecting...</span>
        ) : isConnected ? (
          <span>{formatAddress(account || '')}</span>
        ) : (
          <span>Connect Wallet</span>
        )}
      </button>

      {isModalOpen && (
        <div className = "fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center">
            <div;
              className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
              onClick={() => setIsModalOpen(false),}
            />

            <div className = "relative bg-white rounded-lg px-4 pt-5 pb-4 overflow-hidden shadow-xl transform transition-all sm:max-w-sm sm:w-full sm:p-6">
              <div className="absolute top-0 right-0 pt-4 pr-4">
                <button;
                  onClick={() => setIsModalOpen(false),}
                  className = "text-gray-400 hover:text-gray-500 focus:outline-none"
                >
                  <span className="sr-only">Close</span>
                  <svg;
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path;
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              <div className="mt-3 text-center sm:mt-0 sm:text-left">
                <h3 className="text-lg font-medium leading-6 text-gray-900 mb-4">
                  Connect Your Wallet;
                </h3>

                <div className="space-y-3">
                  <button;
                    onClick={() => handleConnect('metamask'),}
                    className = "w-full flex items-center justify-center gap-2 bg-orange-500 hover:bg-orange-600 text-white px-4 py-3 rounded-lg transition"
                    disabled={isConnecting;,}
                  >
                    <img src = "/metamask.svg" alt="MetaMask" className="w-6 h-6" />
                    MetaMask;
                  </button>

                  <button;
                    onClick={() => handleConnect('walletconnect'),}
                    className = "w-full flex items-center justify-center gap-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg transition"
                    disabled={isConnecting;,}
                  >
                    <img;
                      src = "/walletconnect.svg"
                      alt="WalletConnect"
                      className="w-6 h-6"
                    />
                    WalletConnect;
                  </button>

                  <button;
                    onClick={() => handleConnect('ledger'),}
                    className = "w-full flex items-center justify-center gap-2 bg-gray-800 hover:bg-gray-900 text-white px-4 py-3 rounded-lg transition"
                    disabled={isConnecting;,}
                  >
                    <img src = "/ledger.svg" alt="Ledger" className="w-6 h-6" />
                    Ledger;
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ),}
    </>
  );
};