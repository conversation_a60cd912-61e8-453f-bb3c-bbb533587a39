#!/bin/bash

# Script de déploiement en production pour Retreat And Be

# Couleurs pour les messages
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
function log() {
  echo -e "${GREEN}[INFO]${NC} $1"
}

function warn() {
  echo -e "${YELLOW}[WARN]${NC} $1"
}

function error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier si Docker est installé
if ! command -v docker &> /dev/null; then
  error "Docker n'est pas installé. Veuillez l'installer avant de continuer."
  exit 1
fi

# Vérifier si Docker Compose est installé
if ! command -v docker-compose &> /dev/null; then
  error "Docker Compose n'est pas installé. Veuillez l'installer avant de continuer."
  exit 1
fi

# Créer un répertoire pour les certificats SSL si nécessaire
if [ ! -d "ssl" ]; then
  log "Création du répertoire ssl..."
  mkdir -p ssl
fi

# Vérifier si les certificats SSL existent
if [ ! -f "ssl/cert.pem" ] || [ ! -f "ssl/key.pem" ]; then
  warn "Les certificats SSL n'existent pas. Génération de certificats auto-signés pour le développement..."
  openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout ssl/key.pem -out ssl/cert.pem -subj "/C=FR/ST=Paris/L=Paris/O=Retreat And Be/OU=IT/CN=retreat-and-be.com"
  warn "Pour la production, veuillez remplacer ces certificats par des certificats valides."
fi

# Créer une sauvegarde avant le déploiement
BACKUP_DIR="backups/$(date +%Y%m%d%H%M%S)"
log "Création d'une sauvegarde dans $BACKUP_DIR..."
mkdir -p "$BACKUP_DIR"

# Sauvegarder les fichiers importants
if [ -f "docker-compose.prod.yml" ]; then
  cp docker-compose.prod.yml "$BACKUP_DIR/"
fi

if [ -d "frontend/dist" ]; then
  mkdir -p "$BACKUP_DIR/frontend"
  cp -r frontend/dist "$BACKUP_DIR/frontend/"
fi

# Arrêter les conteneurs existants
log "Arrêt des conteneurs existants..."
docker-compose -f docker-compose.prod.yml down || warn "Aucun conteneur à arrêter ou erreur lors de l'arrêt."

# Construire les images Docker
log "Construction des images Docker..."
docker-compose -f docker-compose.prod.yml build || {
  error "Erreur lors de la construction des images Docker."
  exit 1
}

# Démarrer les conteneurs
log "Démarrage des conteneurs..."
docker-compose -f docker-compose.prod.yml up -d || {
  error "Erreur lors du démarrage des conteneurs."
  exit 1
}

# Vérifier que les conteneurs sont en cours d'exécution
log "Vérification des conteneurs..."
sleep 5
CONTAINERS=$(docker-compose -f docker-compose.prod.yml ps -q)
if [ -z "$CONTAINERS" ]; then
  error "Aucun conteneur n'est en cours d'exécution. Veuillez vérifier les logs."
  exit 1
fi

# Afficher les logs des conteneurs
log "Affichage des logs des conteneurs..."
docker-compose -f docker-compose.prod.yml logs --tail=20

# Vérifier que les services sont accessibles
log "Vérification de l'accessibilité des services..."
curl -s -o /dev/null -w "%{http_code}" http://localhost:80 | grep -q "200" && {
  log "Le frontend est accessible."
} || {
  warn "Le frontend n'est pas accessible. Veuillez vérifier les logs."
}

curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 | grep -q "200" && {
  log "Le backend est accessible."
} || {
  warn "Le backend n'est pas accessible. Veuillez vérifier les logs."
}

# Afficher un message de succès
log "Déploiement terminé avec succès !"
log "L'application est maintenant accessible à l'adresse http://localhost"
log "Pour accéder à l'application en production, veuillez configurer votre serveur DNS pour pointer vers cette machine."

# Afficher les informations de déploiement
log "Informations de déploiement :"
log "- Date : $(date)"
log "- Version : $(cat frontend/.env.production 2>/dev/null | grep VITE_APP_VERSION | cut -d '=' -f2)"
log "- Environnement : production"
log "- Sauvegarde : $BACKUP_DIR"

# Afficher les instructions pour la surveillance
log "Pour surveiller les conteneurs, exécutez la commande suivante :"
log "docker-compose -f docker-compose.prod.yml logs -f"

# Afficher les instructions pour l'arrêt
log "Pour arrêter les conteneurs, exécutez la commande suivante :"
log "docker-compose -f docker-compose.prod.yml down"

# Afficher les instructions pour le redémarrage
log "Pour redémarrer les conteneurs, exécutez la commande suivante :"
log "docker-compose -f docker-compose.prod.yml restart"

# Rendre le script exécutable
chmod +x deploy-production.sh
