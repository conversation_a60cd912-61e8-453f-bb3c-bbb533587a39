#!/usr/bin/env python3
import os
import re
import subprocess
import sys
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path

# Motifs à rechercher et à corriger
PATTERNS = [
    # Enlever les points-virgules supplémentaires au début des déclarations
    (r'export (class|interface|function|const|type|enum) ([^{;]+) {;+', r'export \1 \2 {'),
    # Enlever les points-virgules superflus entre accolades
    (r'{;+', r'{'),
    # Enlever les points-virgules superflus avant accolade fermante
    (r';+}', r'}'),
    # Corriger les retours multiples avec virgule
    (r'return ([^;,]+),;', r'return \1;'),
    # Corriger les points-virgules multiples
    (r';{2,}', r';'),
    # Enlever les virgules avant points-virgules
    (r',;', r';'),
]

# Fichiers à exclure (comme les fichiers .bak)
EXCLUDE_PATTERNS = [
    r'\.bak$',
    r'node_modules/',
    r'dist/',
    r'build/',
]

def should_exclude(file_path):
    """Détermine si un fichier doit être exclu du traitement."""
    return any(re.search(pattern, file_path) for pattern in EXCLUDE_PATTERNS)

def fix_file(file_path):
    """Corrige les erreurs de syntaxe dans un fichier."""
    if should_exclude(file_path):
        return False, file_path, "Exclu par motif"

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Appliquer toutes les corrections
        for pattern, replacement in PATTERNS:
            content = re.sub(pattern, replacement, content)
        
        # Si aucun changement n'a été effectué, retourner
        if content == original_content:
            return False, file_path, "Aucun changement nécessaire"
        
        # Écrire le contenu corrigé
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True, file_path, "Corriger"
    except Exception as e:
        return False, file_path, f"Erreur: {str(e)}"

def get_typescript_files():
    """Récupère tous les fichiers TypeScript modifiés."""
    result = subprocess.run(
        ['git', 'diff', '--name-only', '--diff-filter=M'], 
        capture_output=True, 
        text=True
    )
    
    files = result.stdout.strip().split('\n')
    return [f for f in files if f.endswith(('.ts', '.tsx')) and os.path.exists(f)]

def main():
    ts_files = get_typescript_files()
    print(f"Trouvé {len(ts_files)} fichiers TypeScript modifiés.")
    
    fixed_count = 0
    error_count = 0
    skipped_count = 0
    
    # Utiliser un pool de threads pour traiter les fichiers en parallèle
    with ThreadPoolExecutor(max_workers=os.cpu_count()) as executor:
        results = list(executor.map(fix_file, ts_files))
    
    for success, file_path, message in results:
        if success:
            fixed_count += 1
            print(f"✅ Corrigé: {file_path}")
        elif message == "Aucun changement nécessaire":
            skipped_count += 1
        elif message.startswith("Exclu"):
            skipped_count += 1
        else:
            error_count += 1
            print(f"❌ Erreur: {file_path} - {message}")
    
    print(f"\nRésumé:")
    print(f"  - {fixed_count} fichiers corrigés")
    print(f"  - {skipped_count} fichiers ignorés (pas de changement ou exclus)")
    print(f"  - {error_count} fichiers avec erreurs")
    
    if fixed_count > 0:
        print("\nPour ajouter tous les fichiers corrigés au commit:")
        print("  git add $(git diff --name-only --diff-filter=M)")
        print("Pour créer un commit avec ces corrections:")
        print('  git commit -m "Correction automatique des erreurs de syntaxe TypeScript"')

if __name__ == "__main__":
    main() 