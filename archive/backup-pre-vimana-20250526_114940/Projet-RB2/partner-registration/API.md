## Partner Registration API Documentation

### Authentication

All admin routes require JW<PERSON> authentication. Include the token in the Authorization header:
```
Authorization: Bearer <token>
```

### Endpoints

#### Partner Registration

**POST /api/partners/register**
```json
{
  "request": {
    "content-type": "multipart/form-data",
    "body": {
      "companyName": "string",
      "email": "string",
      "phone": "string",
      "address": "string",
      "identityDocument": "file",
      "businessCertificate": "file"
    }
  },
  "response": {
    "200": {
      "success": true,
      "partnerId": "string",
      "message": "Partner registration submitted successfully"
    },
    "400": {
      "success": false,
      "errors": ["array of error messages"]
    }
  }
}
```

#### Check Registration Status

**GET /api/partners/status/:partnerId**
```json
{
  "response": {
    "200": {
      "success": true,
      "data": {
        "status": "pending|approved|rejected",
        "created_at": "timestamp",
        "updated_at": "timestamp"
      }
    },
    "404": {
      "success": false,
      "message": "Partner not found"
    }
  }
}
```

#### Update Partner Information

**PUT /api/partners/:partnerId**
```json
{
  "request": {
    "body": {
      "companyName": "string",
      "email": "string",
      "phone": "string",
      "address": "string"
    }
  },
  "response": {
    "200": {
      "success": true,
      "message": "Partner information updated successfully"
    },
    "404": {
      "success": false,
      "message": "Partner not found"
    }
  }
}
```

#### Admin: Get Pending Partners

**GET /api/admin/pending-partners**
```json
{
  "response": {
    "200": {
      "success": true,
      "data": [{
        "id": "string",
        "companyName": "string",
        "email": "string",
        "phone": "string",
        "address": "string",
        "status": "pending",
        "created_at": "timestamp"
      }]
    }
  }
}
```

#### Admin: Verify Partner

**POST /api/admin/verify/:partnerId**
```json
{
  "request": {
    "body": {
      "status": "approved|rejected",
      "notes": "string"
    }
  },
  "response": {
    "200": {
      "success": true,
      "message": "Partner verification updated successfully"
    },
    "404": {
      "success": false,
      "message": "Partner not found"
    }
  }
}
```

#### Admin: Get Verification History

**GET /api/admin/history/:partnerId**
```json
{
  "response": {
    "200": {
      "success": true,
      "data": [{
        "id": "string",
        "status": "string",
        "notes": "string",
        "admin_id": "string",
        "created_at": "timestamp"
      }]
    }
  }
}
```

### Error Responses

All endpoints may return these error responses:
```json
{
  "401": {
    "success": false,
    "message": "Authentication required"
  },
  "403": {
    "success": false,
    "message": "Not authorized"
  },
  "429": {
    "success": false,
    "message": "Too many requests"
  },
  "500": {
    "success": false,
    "message": "Internal server error"
  }
}
```

### Rate Limiting

- Partner routes: 100 requests per hour
- Admin routes: 1000 requests per hour
- File upload: 50 requests per hour