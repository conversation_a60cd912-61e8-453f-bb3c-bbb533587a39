import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

interface AuthRequest extends Request {
  user?: {
    id: string;
    role: string;
    wallet?: string;
  };
}

declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        wallet?: string;
      };
    }
  }
}

export const authenticate = async (;
  req: Request,
  res: Response,
  next: NextFunction;
): Promise<void> => {
  try {
    const token = req.headers.authorization?.split(' ')[1];

    if(!token) { { { {,}}}
      res.status(401).json({ message: 'Authentication required' });
      return;
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default_secret') as {
      id: string;
      role: string;
      wallet?: string;
    };

    req.user = decoded;
    next();
  } catch(error) {
    res.status(401).json({ message: 'Invalid token' });
  }
};

export const isAdmin = async (;
  req: Request,
  res: Response,
  next: NextFunction;
): Promise<void> => {
  try {
    if(req.user?.role !== 'admin') { { { {}}}
      res.status(403).json({ message: 'Admin access required' });
      return;
    }

    next();
  } catch(error) {
    res.status(500).json({ message: 'Server error' });
  }
};