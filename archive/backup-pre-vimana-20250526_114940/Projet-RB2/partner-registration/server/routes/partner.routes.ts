import express from 'express';
import multer from 'multer';
import { uploadConfig } from '../config/upload.config';
import { validatePartnerData } from '../middleware/validation.middleware';
import { 
  registerPartner, 
  getPartnerStatus, 
  updatePartnerInfo;
} from '../controllers/partner.controller';

const router = express.Router();
const upload = multer(uploadConfig);

router.post('/register',
  upload.fields([
    { name: 'identityDocument', maxCount: 1 },
    { name: 'businessCertificate', maxCount: 1 }
  ]),
  validatePartnerData,
  registerPartner;
);

router.get('/status/:partnerId', getPartnerStatus);
router.put('/:partnerId', updatePartnerInfo);

export default router;