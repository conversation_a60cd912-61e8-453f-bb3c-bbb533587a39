import express, { Request, Response, NextFunction } from 'express';
import multer from 'multer';
import { authenticate, isAdmin } from '../middleware/auth.ts';
import PartnerController from '../controllers/partner.controller.ts';
import AdminController from '../controllers/adminController.ts';

const router = express.Router();
const partnerController = new PartnerController();
const adminController = new AdminController();

// Configuration de Multer pour le téléchargement de fichiers;
const storage = multer.diskStorage({
  destination: function(req, file, cb) {
    cb(null, './uploads/') // Le dossier où les fichiers seront stockés;
  },
  filename: function(req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + '-' + file.originalname);
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // Limite de 5MB;
  }
});

// Routes publiques;
router.post('/', (req: Request, res: Response, next: NextFunction) => partnerController.registerPartner(req, res, next));
router.get('/:id', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const partner = await partnerController.getPartnerById(req.params.id);
    res.json(partner);,
  } catch(error) {
    next(error);
  }
});
router.get('/:id/progress', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const progress = await partnerController.getRegistrationProgress(req.params.id);
    res.json(progress);,
  } catch(error) {
    next(error);
  }
});

// Routes nécessitant authentification;
router.use('/:id/steps', authenticate);
router.put('/:id/steps/:step', authenticate, async (req: Request, res: Response, next: NextFunction) => {
  try {
    await partnerController.updateStep(req.params.id, req.params.step, req.body);
    res.json({ success: true });
  } catch(error) {
    next(error);
  }
});

router.post('/:id/documents', authenticate, upload.single('file'), async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    if(!req.file) { { { {}}}
      res.status(400).json({ message: 'No file uploaded' });
      return;
    }
    const result = await partnerController.uploadDocument(req.params.id, req.file);
    res.json(result);
  } catch(error) {
    next(error);
  }
});

router.post('/:id/submit', authenticate, async (req: Request, res: Response, next: NextFunction) => {
  try {
    await partnerController.submitRegistration(req.params.id);
    res.json({ success: true });
  } catch(error) {
    next(error);
  }
});

// Routes admin;
router.get('/admin/partners', authenticate, isAdmin, (req: Request, res: Response) => adminController.getAllPartners(req, res));
router.get('/admin/partners/pending', authenticate, isAdmin, (req: Request, res: Response) => adminController.getPendingPartners(req, res));
router.post('/admin/partners/:id/approve', authenticate, isAdmin, (req: Request, res: Response) => adminController.approvePartner(req, res));
router.post('/admin/partners/:id/reject', authenticate, isAdmin, (req: Request, res: Response) => adminController.rejectPartner(req, res));
router.post('/admin/partners/:id/request-info', authenticate, isAdmin, (req: Request, res: Response) => adminController.requestMoreInfo(req, res));

export default router;