import express from 'express';
import { authenticateAdmin } from '../middleware/auth.middleware';
import {
  getPendingPartners,
  verifyPartner,
  getVerificationHistory;
} from '../controllers/admin.controller';

const router = express.Router();

router.use(authenticateAdmin);

router.get('/pending-partners', getPendingPartners);
router.post('/verify/:partnerId', verifyPartner);
router.get('/history/:partnerId', getVerificationHistory);

export default router;