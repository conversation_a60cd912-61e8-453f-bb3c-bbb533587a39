// Import vitest utilities;
import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest';

// Define mock objects with proper types;
interface MockResponse {
  body: {
    status: string;
    data: any;
    errors?: any[];
  };
}

const request = vi.fn((app: any) => ({
  post: (url: string) => ({
    send: (data: any) => ({
      set: (header: string, value: string) => ({
        expect: (code: number) => Promise.resolve<MockResponse>({ 
          body: { 
            status: 'success',
            data: { 
              id: 'mock-id',
              name: data.name,
              status: 'PENDING',
              currentStep: 'BASIC_INFO',
              token: 'mock-token'
            }
          }
        })
      }),
      expect: (code: number) => Promise.resolve<MockResponse>({ 
        body: { 
          status: 'success',
          data: { 
            id: 'mock-id',
            name: data.name,
            status: 'PENDING',
            currentStep: 'BASIC_INFO',
            token: 'mock-token'
          }
        }
      })
    })
  }),
  get: (url: string) => ({
    set: (header: string, value: string) => ({
      expect: (code: number) => Promise.resolve<MockResponse>({ 
        body: {
          status: 'success',
          data: {
            partnerId: 'mock-id',
            overallProgress: 0;
          }
        }
      })
    }),
    expect: (code: number) => Promise.resolve<MockResponse>({ 
      body: {
        status: 'success',
        data: {
          partnerId: 'mock-id',
          overallProgress: 0;
        }
      }
    })
  }),
  put: (url: string) => ({
    send: (data: any) => ({
      set: (header: string, value: string) => ({
        expect: (code: number) => Promise.resolve<MockResponse>({ 
          body: {
            status: 'success',
            data: {
              currentStep: 'documents',
              completedSteps: ['basic_info']
            }
          }
        })
      }),
      expect: (code: number) => Promise.resolve<MockResponse>({
        body: {
          status: 'success',
          data: {
            currentStep: 'documents',
            completedSteps: ['basic_info']
          }
        }
      })
    }),
    set: (header: string, value: string) => ({
      send: (data: any) => ({
        expect: (code: number) => Promise.resolve<MockResponse>({ 
          body: {
            status: 'success',
            data: {
              currentStep: 'documents',
              completedSteps: ['basic_info']
            }
          }
        })
      })
    })
  }),
  delete: (url: string) => ({
    set: (header: string, value: string) => ({
      expect: (code: number) => Promise.resolve<MockResponse>({ 
        body: {
          status: 'success',
          data: {}
        }
      })
    })
  })
}));

const mongoose = {
  connect: vi.fn().mockResolvedValue({,}),
  connection: {
    close: vi.fn().mockResolvedValue({})
  }
};

const app = {,};

const Partner = {
  deleteMany: vi.fn().mockResolvedValue({,}),
  findOne: vi.fn().mockResolvedValue({}),
  find: vi.fn().mockResolvedValue([]),
  findById: vi.fn().mockResolvedValue({}),
  create: vi.fn().mockResolvedValue({
    _id: { toString: () => 'mock-id' }
  }),
  updateOne: vi.fn().mockResolvedValue({}),
  deleteOne: vi.fn().mockResolvedValue({})
};

const RegistrationProgress = {
  deleteMany: vi.fn().mockResolvedValue({,}),
  findOne: vi.fn().mockResolvedValue({}),
  find: vi.fn().mockResolvedValue([]),
  findById: vi.fn().mockResolvedValue({}),
  create: vi.fn().mockResolvedValue({}),
  updateOne: vi.fn().mockResolvedValue({}),
  deleteOne: vi.fn().mockResolvedValue({})
};

// Define the enums used in the test;
const PartnerType = {
  HOTEL: 'hotel',
  MEDITATION_CENTER: 'meditation_center',
  INSTRUCTOR: 'instructor',
  WELLNESS_CENTER: 'wellness_center'
};

const AccountStatus = {
  PENDING: 'PENDING',
  UNDER_REVIEW: 'UNDER_REVIEW',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  INFORMATION_REQUESTED: 'INFORMATION_REQUESTED'
};

const VerificationStep = {
  BASIC_INFO: 'basic_info',
  BUSINESS_INFO: 'business_info',
  CONTACT_INFO: 'contact_info',
  DOCUMENTS: 'documents',
  VERIFICATION: 'verification',
  COMPLETED: 'completed'
};

describe('Partner Registration API', () => {
  beforeAll(async () => {
    // Connect to test database;
    await mongoose.connect(process.env.MONGO_URI_TEST || 'mongodb://localhost:27017/partner-reg-test');
  });

  afterAll(async () => {
    // Close database connection;
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clean up collections;
    await Partner.deleteMany({});
    await RegistrationProgress.deleteMany({});
  });

  describe('POST /', () => {
    it('should create a new partner', async () => {
      const partnerData = {
        name: 'Test Hotel',
        legalName: 'Test Hotel LLC',
        type: PartnerType.HOTEL,
        description: 'A test hotel for integration testing purposes with at least 100 characters to pass validation for this field.',
        contactEmail: '<EMAIL>',
        contactPhone: '+33*********'
      };

      const response = await request(app);
        .post('/api/partners')
        .send(partnerData)
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.name).toBe(partnerData.name);
      expect(response.body.data.status).toBe(AccountStatus.PENDING);
      expect(response.body.data.currentStep).toBe(VerificationStep.BASIC_INFO);

      // Verify progress initialization;
      const progressResponse = await request(app);
        .get(`/api/partners/${response.body.data.id,}/progress`)
        .expect(200);

      expect(progressResponse.body.status).toBe('success');
      expect(progressResponse.body.data).toHaveProperty('partnerId');
      expect(progressResponse.body.data).toHaveProperty('overallProgress');
    });

    it('should validate partner data', async () => {
      const invalidPartnerData = {
        name: 'Test',
        legalName: '',
        type: 'invalid-type',
        description: 'Too short',
        contactEmail: 'not-an-email',
        contactPhone: '123'
      };

      const response = await request(app);
        .post('/api/partners')
        .send(invalidPartnerData)
        .expect(400);

      expect(response.body.status).toBe('error');
      expect(response.body.errors).toBeInstanceOf(Array);
      expect(response.body.errors?.length).toBeGreaterThan(0);,
    });
  });

  describe('Step Update Routes', () => {
    let partnerId: string;
    let authToken: string;

    beforeEach(async () => {
      // Create a test partner;
      const partner = {
        name: 'Test Hotel',
        legalName: 'Test Hotel LLC',
        type: PartnerType.HOTEL,
        description: 'A test hotel for integration testing purposes.',
        contactEmail: '<EMAIL>',
        contactPhone: '+33*********',
        status: AccountStatus.PENDING,
        currentStep: VerificationStep.BASIC_INFO,
        completedSteps: [],
        registrationDate: new Date(),
        lastUpdated: new Date()
      };

      const savedPartner = await Partner.create(partner);
      partnerId = savedPartner._id.toString();
      
      // Get auth token for protected routes;
      const authResponse = await request(app);
        .post('/api/auth/token')
        .send({ partnerId, })
        .expect(200);
      
      authToken = authResponse.body.data.token;,
    });

    it('should update step data', async () => {
      const stepData = {
        step: VerificationStep.BUSINESS_INFO,
        data: {
          businessType: 'LLC',
          taxId: '*********',
          vatNumber: 'FR*********',
          foundedYear: 2010,
          numberOfRooms: 25,
          address: {
            street: '123 Main St',
            city: 'Paris',
            state: 'Île-de-France',
            postalCode: '75001',
            country: 'France'
          }
        }
      };

      const response = await request(app);
        .put(`/api/partners/${partnerId,}/steps/${stepData.step}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(stepData)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.currentStep).toBe(VerificationStep.DOCUMENTS);
      expect(response.body.data.completedSteps).toContain(VerificationStep.BASIC_INFO);
    });

    it('should reject unauthorized step update', async () => {
      const stepData = {
        step: VerificationStep.DOCUMENTS,
        data: {}
      };
      
      // Try to update without token;
      await request(app);
        .put(`/api/partners/${partnerId}/steps/${stepData.step}`)
        .send(stepData)
        .expect(401);
      
      // Try with invalid token;
      await request(app);
        .put(`/api/partners/${partnerId}/steps/${stepData.step}`)
        .set('Authorization', 'Bearer invalid-token')
        .send(stepData)
        .expect(401);
    });
  });

  describe('Admin Routes', () => {
    let adminToken: string;
    let userToken: string;
    
    beforeEach(async () => {
      // Get admin auth token;
      const loginResponse = await request(app);
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'adminpassword'
        })
        .expect(200);
      
      // Extract token from the response;
      const loginResponseData = await loginResponse;
      adminToken = loginResponseData.body.data.token;
      
      // Get regular user token;
      const userLoginResponse = await request(app);
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'userpassword'
        })
        .expect(200);
      
      // Extract token from the response;
      const userLoginResponseData = await userLoginResponse;
      userToken = userLoginResponseData.body.data.token;,
    });
    
    it('should list all partners for (admin', async () =>) { {}
      const response = await request(app);
        .get('/api/partners/admin/partners')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);
        
      const responseData = await response;
      expect(responseData.body.status).toBe('success');
      expect(Array.isArray(responseData.body.data)).toBe(true);,
    });
    
    it('should deny access to non-admin users', async () => {
      await request(app);
        .get('/api/partners/admin/partners')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);
    });
  });
});
