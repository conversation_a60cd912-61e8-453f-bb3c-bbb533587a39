import { Request, Response, NextFunction } from 'express';
import { PartnerService } from '../services/partner.service.js';
import { KYCService } from '../services/kyc-service.js';
import { NotificationService } from '../services/notification.service.js';
import { logger } from '../config/logger.js';

class PartnerController {
  private partnerService: PartnerService;
  private kycService: KYCService;
  private notificationService: NotificationService;

  constructor() {
    this.partnerService = new PartnerService();
    this.kycService = new KYCService();
    this.notificationService = new NotificationService();
  }

  async registerPartner(req: Request, res: Response, next: NextFunction) {
    try {
      const { companyName, email, phone, address } = req.body;
      const documents = req.files as { [fieldname: string]: Express.Multer.File[], };

      // Create partner record;
      const partnerId = await this.partnerService.create({
        companyName,
        email,
        phone,
        address;
      });

      // Process KYC documents;
      await this.kycService.processDocuments(partnerId, documents);

      // Notify admin;
      await this.notificationService.notifyAdmin('New Partner Registration', {
        partnerId,
        companyName;
      });

      res.status(201).json({
        success: true,
        partnerId,
        message: 'Partner registration submitted successfully'
      });
    } catch(error) {
      next(error);
    }
  }

  async getPartnerById(partnerId: string) {
    return this.partnerService.getById(partnerId);
  }

  async getRegistrationProgress(partnerId: string) {
    return this.partnerService.getProgress(partnerId);
  }

  async updateStep(partnerId: string, step: string, data: any) {
    return this.partnerService.updateStep(partnerId, step, data);
  }

  async uploadDocument(partnerId: string, file: Express.Multer.File) {
    if(!file) { { { {}}}
      throw new Error('No file provided');
    }
    return this.kycService.uploadDocument(partnerId, file);
  }

  async submitRegistration(partnerId: string) {
    const partner = await this.partnerService.getById(partnerId);
    if(!partner) { { { {,}}}
      throw new Error('Partner not found');
    }

    await this.partnerService.submitRegistration(partnerId);
    await this.notificationService.notifyAdmin('Partner Registration Submitted', {
      partnerId,
      companyName: partner.companyName;
    });

    return { success: true };
  }
}

export default PartnerController;