replicaCount: 2

image:
  repository: registry.example.com/partner-registration
  tag: 1.0.0
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 80
  targetPort: 3000

securityContext:
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  allowPrivilegeEscalation: false
  capabilities:
    drop: ["ALL"]

resources:
  limits:
    cpu: 300m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 256Mi

ingress:
  enabled: true
  hosts:
    - partners.example.com
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"

postgresql:
  enabled: true
  auth:
    username: partnerapp
    password: ""
    database: partnersdb
  primary:
    persistence:
      size: 8Gi

minio:
  enabled: true
  auth:
    rootUser: "minioadmin"
    rootPassword: ""
  defaultBuckets: "partner-uploads"
  persistence:
    size: 20Gi

env:
  - name: NODE_ENV
    value: production
  - name: BACKEND_API_URL
    value: http://backend-service:3000/api
  - name: POSTGRES_URI
    value: *********************************************************************************/partnersdb
  - name: JWT_SECRET
    valueFrom:
      secretKeyRef:
        name: partner-registration-secrets
        key: jwt-secret
  - name: VERIFICATION_EMAIL_TEMPLATE
    value: partner-verification-template
  - name: MINIO_ENDPOINT
    value: partner-registration-minio
  - name: MINIO_PORT
    value: "9000"
  - name: MINIO_BUCKET
    value: partner-uploads