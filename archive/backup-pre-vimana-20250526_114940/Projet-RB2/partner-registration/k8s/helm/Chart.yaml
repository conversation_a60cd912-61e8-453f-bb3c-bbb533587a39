apiVersion: v2
name: partner-registration
description: Service to manage partner onboarding and registration processes
type: application
version: 1.0.0
appVersion: "1.0.0"
dependencies:
- name: postgresql
  version: 12.1.5
  repository: https://charts.bitnami.com/bitnami
  condition: postgresql.enabled
- name: minio
  version: 12.1.1
  repository: https://charts.bitnami.com/bitnami
  condition: minio.enabled