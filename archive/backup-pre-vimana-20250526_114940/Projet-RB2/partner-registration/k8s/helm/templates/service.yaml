apiVersion: v1
kind: Service
metadata:
  name: {{ .Release.Name }}-partner-registration
  labels:
    app.kubernetes.io/name: {{ .Release.Name }}-partner-registration
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: {{ .Release.Name }}-partner-registration
    app.kubernetes.io/instance: {{ .Release.Name }}