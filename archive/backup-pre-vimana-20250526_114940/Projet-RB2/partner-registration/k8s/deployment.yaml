apiVersion: apps/v1
kind: Deployment
metadata:
  name: partner-registration
  labels:
    app: partner-registration
spec:
  replicas: 2
  selector:
    matchLabels:
      app: partner-registration
  template:
    metadata:
      labels:
        app: partner-registration
    spec:
      containers:
      - name: partner-registration
        image: partner-registration:latest
        ports:
        - containerPort: 8080
        resources:
          limits:
            cpu: "1"
            memory: 512Mi
          requests:
            cpu: "0.5"
            memory: 256Mi
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
