version: '3.8'
services:
  web:
    build: .
    ports:
      - "3000:80"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    networks:
      - retreat-network
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - web
    networks:
      - retreat-network

  partner-registration:
    build: ./partner-registration
    container_name: retreat-partner-registration
    ports:
      - "3001:3000"
    volumes:
      - ./partner-registration/uploads:/app/uploads
      - ./partner-registration/database:/app/database
    environment:
      - NODE_ENV=production
      - JWT_SECRET=${JWT_SECRET}
      - ADMIN_EMAIL=${ADMIN_EMAIL}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
    restart: unless-stopped
    depends_on:
      - web
    networks:
      - retreat-network

networks:
  retreat-network:
    driver: bridge