import sqlite3 from 'sqlite3';
import { dbConfig } from '../config/database.js';
import { logger } from '../utils/logger.js';
import path from 'path';
import fs from 'fs';

// Ensure database directory exists
const dbDir = path.dirname(dbConfig.path);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

const db = new sqlite3.Database(dbConfig.path);

export const setupDatabase = () => {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Partners table
      db.run(`CREATE TABLE IF NOT EXISTS partners (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        company_name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        phone TEXT NOT NULL,
        address TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`);

      // KYC documents table
      db.run(`CREATE TABLE IF NOT EXISTS kyc_documents (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        partner_id INTEGER,
        document_type TEXT NOT NULL,
        document_path TEXT NOT NULL,
        verification_status TEXT DEFAULT 'pending',
        ocr_text TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (partner_id) REFERENCES partners (id)
      )`);

      // Verification history table
      db.run(`CREATE TABLE IF NOT EXISTS verification_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        partner_id INTEGER,
        status TEXT NOT NULL,
        notes TEXT,
        admin_id INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (partner_id) REFERENCES partners (id)
      )`);
    });

    logger.info('Database setup completed');
    resolve();
  });
};

export const getDb = () => db;