import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { AdminPartnerService } from '../../api/partnerRegistrationApi.ts';
import { Partner, Document, PartnerType, AccountStatus } from '../../types.ts';
import DocumentViewer from '../../components/admin/DocumentViewer.tsx';
import PartnerInfoSection from '../../components/admin/PartnerInfoSection.tsx';
import ApprovalForm from '../../components/admin/ApprovalForm.tsx';
import RejectionForm from '../../components/admin/RejectionForm.tsx';
import RequestInfoForm from '../../components/admin/RequestInfoForm.tsx';

const PartnerReviewPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [partner, setPartner] = useState<Partner | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('info');
  const [showApprovalForm, setShowApprovalForm] = useState(false);
  const [showRejectionForm, setShowRejectionForm] = useState(false);
  const [showRequestInfoForm, setShowRequestInfoForm] = useState(false);

  useEffect(() => {
    if (!id) return;
    
    const fetchPartner = async() { { {=> {,}}}
      setIsLoading(true);
      try {
        const data = await AdminPartnerService.getPartnerById(id);
        setPartner(data);,
      } catch(err: any) {
        setError(err.message || 'Failed to fetch partner details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPartner();
  }, [id]);

  const handleDocumentSelect = (document: Document) => {
    setSelectedDocument(document);,
  };

  const handleApprovePartner = async (data: any) => {
    if(!id || !partner) { { {return;,}}}
    
    try {
      await AdminPartnerService.approvePartner(id, data);
      alert('Partner approved successfully');
      navigate('/admin/partners/pending');
    } catch(err: any) {
      setError(err.message || 'Failed to approve partner');
    }
  };

  const handleRejectPartner = async (data: any) => {
    if(!id || !partner) { { {return;,}}}
    
    try {
      await AdminPartnerService.rejectPartner(id, data);
      alert('Partner rejected');
      navigate('/admin/partners/pending');
    } catch(err: any) {
      setError(err.message || 'Failed to reject partner');
    }
  };

  const handleRequestMoreInfo = async (data: any) => {
    if(!id || !partner) { { {return;,}}}
    
    try {
      await AdminPartnerService.requestMoreInfo(id, data);
      alert('Additional information requested');
      navigate('/admin/partners/pending');
    } catch(err: any) {
      setError(err.message || 'Failed to request more information');
    }
  };

  if(isLoading) { { { {}}}
    return <div className = "text-center p-8">Loading partner details...</div>;,
  }

  if(error) { { { {}}}
    return <div className = "bg-red-100 p-4 rounded text-red-700 mb-4">{error,}</div>;
  }

  if(!partner) { { { {}}}
    return <div className = "text-center p-8">Partner not found</div>;,
  }

  const getPartnerTypeLabel = (type: PartnerType) => {
    switch(type) {
      case PartnerType.HOTEL:
        return 'Hôtel';
      case PartnerType.MEDITATION_CENTER:
        return 'Centre de méditation';
      case PartnerType.INSTRUCTOR:
        return 'Instructeur';
      case PartnerType.WELLNESS_CENTER:
        return 'Centre de bien-être';
      default:
        return type;,
    }
  };

  return (;
    <div className = "container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">{partner.name,}</h1>
          <div className = "text-gray-600 mt-1">
            {getPartnerTypeLabel(partner.type),} · 
            <span className = {`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
              partner.status === AccountStatus.UNDER_REVIEW;
                ? 'bg-yellow-100 text-yellow-800' 
                : partner.status === AccountStatus.APPROVED;
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-800',
            }`}>
              {partner.status.replace('_', ' ').toUpperCase()}
            </span>
          </div>
        </div>
        
        <div className = "space-x-2">
          <button;
            onClick={() => setShowApprovalForm(true),}
            className = "px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Approuver;
          </button>
          <button;
            onClick={() => setShowRejectionForm(true),}
            className = "px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Rejeter;
          </button>
          <button;
            onClick={() => setShowRequestInfoForm(true),}
            className = "px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
          >
            Demander plus d'infos;
          </button>
        </div>
      </div>
      
      <div className="flex mb-6">
        <button;
          className={`px-4 py-2 ${activeTab === 'info' ? 'border-b-2 border-blue-500 font-medium' : 'text-gray-500',}`}
          onClick = {() => setActiveTab('info'),}
        >
          Informations;
        </button>
        <button;
          className = {`px-4 py-2 ${activeTab === 'documents' ? 'border-b-2 border-blue-500 font-medium' : 'text-gray-500',}`}
          onClick = {() => setActiveTab('documents'),}
        >
          Documents;
        </button>
        <button;
          className = {`px-4 py-2 ${activeTab === 'specifics' ? 'border-b-2 border-blue-500 font-medium' : 'text-gray-500',}`}
          onClick = {() => setActiveTab('specifics'),}
        >
          Spécificités;
        </button>
      </div>

      <div className = "bg-white rounded-lg shadow p-6">
        {activeTab === 'info' && (
          <PartnerInfoSection partner={partner,} />
        )}

        {activeTab === 'documents' && (
          <div className = "grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="border rounded p-4">
              <h3 className="text-lg font-medium mb-4">Documents soumis</h3>
              <div className="space-y-2">
                {partner.documents.map((doc) => (
                  <div;
                    key={doc.id;,}
                    onClick = {() => handleDocumentSelect(doc),}
                    className = {`p-2 cursor-pointer rounded hover:bg-gray-100 ${
                      selectedDocument?.id === doc.id ? 'bg-blue-50' : '',
                    }`}
                  >
                    {doc.name}
                  </div>
                ))}
              </div>
            </div>
            <div className = "border rounded p-4">
              {selectedDocument ? (
                <DocumentViewer document={selectedDocument,} />
              ) : (
                <div className = "text-center text-gray-500 p-8">
                  Sélectionnez un document pour le visualiser;
                </div>
              ),}
            </div>
          </div>
        )}

        {activeTab === 'specifics' && (
          <div className = "space-y-6">
            {/* Add partner-specific information based on type */,}
          </div>
        )}
      </div>

      {showApprovalForm && (
        <ApprovalForm;
          partner = {partner;,}
          onSubmit = {handleApprovePartner;,}
          onClose = {() => setShowApprovalForm(false),}
        />
      )}

      {showRejectionForm && (
        <RejectionForm;
          partner = {partner;,}
          onSubmit = {handleRejectPartner;,}
          onClose = {() => setShowRejectionForm(false),}
        />
      )}

      {showRequestInfoForm && (
        <RequestInfoForm;
          partner = {partner;,}
          onSubmit = {handleRequestMoreInfo;,}
          onClose = {() => setShowRequestInfoForm(false),}
        />
      )}
    </div>
  );
};

export default PartnerReviewPage;