import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { 
  Building2, Hotel, UtensilsCrossed, Plane, Heart, 
  Users, Coins, ShieldCheck, Award, Crown, CheckCircle2,
  Sparkles, Bot, Zap, ArrowRight, Loader2
} from 'lucide-react';

export default function BecomePartnerPage() {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  const handleGetStarted = async (tierName) => {
    setIsLoading(true);
    try {
      // Store the selected tier in session storage for the registration flow
      sessionStorage.setItem('selectedPartnerTier', tierName);
      navigate('/partner/registration');
    } catch (error) {
      console.error('Error starting registration:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // ... (keep all the existing const definitions for partnerTiers, partnerTypes, benefits, aiFeatures)

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white pt-20">
      {/* Hero Section */}
      <section className="relative py-20 px-4 bg-emerald-50">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Partner With RetreatAndBe
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Join our ecosystem and help shape the future of wellness tourism through Web3 technology
          </p>
        </div>
      </section>

      {/* Partnership Tiers */}
      <section className="py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-6">
            Choose Your Partnership Level
          </h2>
          <p className="text-xl text-gray-600 text-center mb-16 max-w-2xl mx-auto">
            Select the tier that best fits your business needs and growth ambitions
          </p>

          <div className="grid md:grid-cols-3 gap-8">
            {partnerTiers.map((tier, index) => (
              <div 
                key={index}
                className={`bg-white rounded-xl shadow-sm hover:shadow-md transition p-8 relative ${
                  tier.popular ? 'border-2 border-emerald-500' : ''
                }`}
              >
                {tier.popular && (
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <span className="bg-emerald-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="text-center mb-8">
                  <tier.icon className={`w-12 h-12 mx-auto mb-4 ${
                    tier.popular ? 'text-emerald-600' : 'text-gray-600'
                  }`} />
                  <h3 className="text-xl font-bold mb-2">{tier.name}</h3>
                  <p className="text-gray-600 mb-4">{tier.description}</p>
                  <div className="text-3xl font-bold">{tier.price}</div>
                </div>

                <ul className="space-y-4 mb-8">
                  {tier.features.map((feature, i) => (
                    <li key={i} className="flex items-center gap-2">
                      <CheckCircle2 className="w-5 h-5 text-emerald-600 flex-shrink-0" />
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button
                  onClick={() => handleGetStarted(tier.name)}
                  disabled={isLoading}
                  className={`w-full py-3 px-6 rounded-lg font-semibold transition flex items-center justify-center ${
                    tier.popular
                      ? 'bg-emerald-600 hover:bg-emerald-700 text-white'
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-800'
                  }`}
                >
                  {isLoading ? (
                    <Loader2 className="w-5 h-5 animate-spin mr-2" />
                  ) : null}
                  Start Verification Process
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Keep all other sections unchanged */}
      {/* AI Features Section */}
      {/* Partner Types */}
      {/* Benefits Section */}
      {/* CTA Section */}
    </div>
  );
}