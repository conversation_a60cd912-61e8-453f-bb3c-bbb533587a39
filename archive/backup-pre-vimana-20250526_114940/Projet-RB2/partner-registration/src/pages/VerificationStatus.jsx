import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Loader2, CheckCircle2, XCircle } from 'lucide-react';

export default function VerificationStatus() {
  const navigate = useNavigate();
  const [status, setStatus] = useState(null);
  const [error, setError] = useState(null);
  const partnerId = sessionStorage.getItem('partnerId');

  useEffect(() => {
    if (!partnerId) {
      navigate('/partner');
      return;
    }

    const checkStatus = async () => {
      try {
        const response = await fetch(`/api/partners/status/${partnerId}`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.message || 'Failed to fetch status');
        }

        setStatus(data.data.status);
      } catch (err) {
        setError(err.message);
      }
    };

    checkStatus();
    const interval = setInterval(checkStatus, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [partnerId, navigate]);

  const getStatusDisplay = () => {
    switch (status) {
      case 'pending':
        return {
          icon: Loader2,
          text: 'Your application is being processed',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50'
        };
      case 'approved':
        return {
          icon: CheckCircle2,
          text: 'Your application has been approved!',
          color: 'text-green-600',
          bgColor: 'bg-green-50'
        };
      case 'rejected':
        return {
          icon: XCircle,
          text: 'Your application needs attention',
          color: 'text-red-600',
          bgColor: 'bg-red-50'
        };
      default:
        return {
          icon: Loader2,
          text: 'Checking status...',
          color: 'text-gray-600',
          bgColor: 'bg-gray-50'
        };
    }
  };

  const statusInfo = getStatusDisplay();

  return (
    <div className="min-h-screen bg-gray-50 py-20">
      <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-sm p-8">
        <h1 className="text-3xl font-bold mb-8">Verification Status</h1>

        {error ? (
          <div className="bg-red-50 text-red-600 p-4 rounded-lg">
            {error}
          </div>
        ) : (
          <div className={`${statusInfo.bgColor} p-6 rounded-lg`}>
            <div className="flex items-center gap-4">
              <statusInfo.icon className={`w-8 h-8 ${statusInfo.color} ${
                status === 'pending' ? 'animate-spin' : ''
              }`} />
              <div>
                <h2 className="text-xl font-semibold mb-2">
                  {statusInfo.text}
                </h2>
                <p className="text-gray-600">
                  {status === 'pending' 
                    ? 'We\'ll notify you once the verification is complete.'
                    : status === 'approved'
                    ? 'You can now access your partner dashboard.'
                    : 'Please check your email for more information.'}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}