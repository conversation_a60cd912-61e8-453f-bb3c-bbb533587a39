import { useState, useCallback } from 'react';

interface FileUploadState {
  isUploading: boolean;
  progress: number;
  error: string | null;
  uploadedFiles: File[];
}

interface UseFileUploadReturn {
  fileState: FileUploadState;
  handleFileSelect: (files: FileList | null) => void;
  uploadFiles: () => Promise<void>;
  resetUpload: () => void;
  uploadFile: (file: File) => Promise<void>;
  isUploading: boolean;
  uploadProgress: number;
  error: string | null;
}

export const useFileUpload = (partnerId?: string): UseFileUploadReturn => {
  const [fileState, setFileState] = useState<FileUploadState>({
    isUploading: false,
    progress: 0,
    error: null,
    uploadedFiles: []
  });

  const handleFileSelect = useCallback((files: FileList | null) => {
    if(!files) { { {return;,}}}

    setFileState(prev => ({
      ...prev,
      uploadedFiles: [...prev.uploadedFiles, ...Array.from(files)],
      error: null;
    }));
  }, []);

  const uploadFile = useCallback(async (file: File) => {
    setFileState(prev => ({ ...prev, isUploading: true, progress: 0 }));

    try {
      const formData = new FormData();
      formData.append('file', file);
      
      if(partnerId) { { { {}}}
        formData.append('partnerId', partnerId);
      }

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
        headers: {
          'Accept': 'application/json'
        }
      });

      if(!response.ok) { { { {}}}
        throw new Error('Upload failed');
      }

      setFileState(prev => ({
        ...prev,
        isUploading: false,
        progress: 100,
        error: null;
      }));
    } catch(error) {
      setFileState(prev => ({
        ...prev,
        isUploading: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      }));
    }
  }, [partnerId]);

  const uploadFiles = useCallback(async () => {
    if(fileState.uploadedFiles.length === 0) { { { {,}}}
      setFileState(prev => ({ ...prev, error: 'No files selected' }));
      return;
    }

    setFileState(prev => ({ ...prev, isUploading: true, progress: 0 }));

    try {
      const formData = new FormData();
      fileState.uploadedFiles.forEach((file, index) => {
        formData.append(`file${index}`, file);
      });
      
      if(partnerId) { { { {}}}
        formData.append('partnerId', partnerId);
      }

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
        headers: {
          'Accept': 'application/json'
        }
      });

      if(!response.ok) { { { {}}}
        throw new Error('Upload failed');
      }

      setFileState(prev => ({
        ...prev,
        isUploading: false,
        progress: 100,
        error: null;
      }));
    } catch(error) {
      setFileState(prev => ({
        ...prev,
        isUploading: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      }));
    }
  }, [fileState.uploadedFiles, partnerId]);

  const resetUpload = useCallback(() => {
    setFileState({
      isUploading: false,
      progress: 0,
      error: null,
      uploadedFiles: []
    });
  }, []);

  return {
    uploadFile,
    isUploading: fileState.isUploading,
    uploadProgress: fileState.progress,
    error: fileState.error,
    fileState,
    handleFileSelect,
    uploadFiles,
    resetUpload;
  };
};