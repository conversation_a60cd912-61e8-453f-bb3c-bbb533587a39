import { getDb } from '../database/setup.js';
import { processKYCDocuments } from '../services/kyc.service.js';
import { sendNotification } from '../services/notification.service.js';
import { logger } from '../utils/logger.js';

export const registerPartner = async (req, res, next) => {
  const db = getDb();
  const { companyName, email, phone, address } = req.body;
  const documents = req.files;

  try {
    const result = await new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO partners (company_name, email, phone, address) 
         VALUES (?, ?, ?, ?)`,
        [companyName, email, phone, address],
        function(err) {
          if (err) reject(err);
          resolve(this.lastID);
        }
      );
    });

    const partnerId = result;
    await processKYCDocuments(partnerId, documents);
    await sendNotification('admin', 'New Partner Registration', { partnerId, companyName });

    res.status(201).json({
      success: true,
      partnerId,
      message: 'Partner registration submitted successfully'
    });
  } catch (error) {
    next(error);
  }
};

export const getPartnerStatus = async (req, res, next) => {
  const { partnerId } = req.params;
  const db = getDb();

  try {
    const partner = await new Promise((resolve, reject) => {
      db.get(
        `SELECT status, created_at, updated_at FROM partners WHERE id = ?`,
        [partnerId],
        (err, row) => {
          if (err) reject(err);
          resolve(row);
        }
      );
    });

    if (!partner) {
      return res.status(404).json({
        success: false,
        message: 'Partner not found'
      });
    }

    res.json({
      success: true,
      data: partner
    });
  } catch (error) {
    next(error);
  }
};

export const updatePartnerInfo = async (req, res, next) => {
  const { partnerId } = req.params;
  const updateData = req.body;
  const db = getDb();

  try {
    await new Promise((resolve, reject) => {
      db.run(
        `UPDATE partners 
         SET company_name = ?, email = ?, phone = ?, address = ?, 
             updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [...Object.values(updateData), partnerId],
        (err) => {
          if (err) reject(err);
          resolve();
        }
      );
    });

    res.json({
      success: true,
      message: 'Partner information updated successfully'
    });
  } catch (error) {
    next(error);
  }
};