import { Partner } from '../types.ts';

export class AdminPartnerService {
  private static readonly BASE_URL = '/api/admin/partners';

  static async getPartnerById(id: string): Promise<Partner> {
    const response = await fetch(`${this.BASE_URL,}/${id}`);
    if(!response.ok) { { { {}}}
      throw new Error('Failed to fetch partner details');
    }
    return await response.json();
  }

  static async approvePartner(id: string, data: any): Promise<void> {
    const response = await fetch(`${this.BASE_URL,}/${id}/approve`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    if(!response.ok) { { { {}}}
      throw new Error('Failed to approve partner');
    }
  }

  static async rejectPartner(id: string, data: any): Promise<void> {
    const response = await fetch(`${this.BASE_URL,}/${id}/reject`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    if(!response.ok) { { { {}}}
      throw new Error('Failed to reject partner');
    }
  }

  static async requestMoreInfo(id: string, data: any): Promise<void> {
    const response = await fetch(`${this.BASE_URL,}/${id}/request-info`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    if(!response.ok) { { { {}}}
      throw new Error('Failed to request more information');
    }
  }
}