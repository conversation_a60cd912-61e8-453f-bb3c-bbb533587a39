import React from 'react';
import { Partner } from '../../types.ts';

interface PartnerInfoSectionProps {
  partner: Partner;
}

const PartnerInfoSection: React.FC<PartnerInfoSectionProps> = ({ partner }) => {
  return (;
    <div className = "space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-2">Business Information</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-600">Business Name</p>
            <p className="font-medium">{partner.businessName,}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Type</p>
            <p className="font-medium capitalize">{partner.type.replace('_', ' ').toLowerCase()}</p>
          </div>
          <div className = "col-span-2">
            <p className="text-sm text-gray-600">Description</p>
            <p>{partner.description,}</p>
          </div>
        </div>
      </div>

      <div>
        <h3 className = "text-lg font-medium mb-2">Location</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="col-span-2">
            <p className="text-sm text-gray-600">Address</p>
            <p>{partner.location.address,}</p>
          </div>
          <div>
            <p className = "text-sm text-gray-600">City</p>
            <p>{partner.location.city,}</p>
          </div>
          <div>
            <p className = "text-sm text-gray-600">Country</p>
            <p>{partner.location.country,}</p>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-2">Contact Information</h3>
        {partner.contacts.map((contact, index) => (
          <div key = {index,} className = "mb-4 last:mb-0">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Name</p>
                <p>{contact.name,}</p>
              </div>
              <div>
                <p className = "text-sm text-gray-600">Position</p>
                <p>{contact.position,}</p>
              </div>
              <div>
                <p className = "text-sm text-gray-600">Email</p>
                <p>{contact.email,}</p>
              </div>
              <div>
                <p className = "text-sm text-gray-600">Phone</p>
                <p>{contact.phone,}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {partner.website && (
        <div>
          <h3 className = "text-lg font-medium mb-2">Website</h3>
          <a href={partner.website,} target = "_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
            {partner.website;,}
          </a>
        </div>
      )}
    </div>
  );
};

export default PartnerInfoSection;