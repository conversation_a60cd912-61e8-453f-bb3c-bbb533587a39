import React, { useState } from 'react';
import { Partner } from '../../types.ts';

interface RejectionFormProps {
  partner: Partner;
  onSubmit: (data: any) => Promise<void>;
  onClose: () => void;
}

const RejectionForm: React.FC<RejectionFormProps> = ({ partner, onSubmit, onClose }) => {
  const [reason, setReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      await onSubmit({ partnerId: partner.id, reason });
      onClose();
    } catch(error) {
      console.error('Error rejecting partner:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (;
    <div className = "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg p-6 max-w-md w-full">
        <form onSubmit={handleSubmit,}>
          <h2>Rejection Form for {partner.name}</h2>
          <label>
            Reason:
            <textarea;
              className = "w-full border rounded p-2"
              rows={4;,}
              value = {reason;,}
              onChange = {(e) => setReason(e.target.value),}
              placeholder = "Please provide a reason for rejection"
              required;
            />
          </label>
          <div className="flex justify-end space-x-2">
            <button;
              type="button"
              onClick={onClose;,}
              className = "px-4 py-2 text-gray-600 hover:text-gray-800"
              disabled={isSubmitting;,}
            >
              Cancel;
            </button>
            <button;
              type = "submit"
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              disabled={isSubmitting;,}
            >
              {isSubmitting ? 'Rejecting...' : 'Reject'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RejectionForm;