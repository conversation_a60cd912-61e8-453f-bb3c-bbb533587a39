import React, { useState } from 'react';
import { Partner } from '../../types.ts';

interface ApprovalFormProps {
  partner: Partner;
  onSubmit: (data: any) => Promise<void>;
  onClose: () => void;
}

const ApprovalForm: React.FC<ApprovalFormProps> = ({ partner, onSubmit, onClose }) => {
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [decision, setDecision] = useState<'approve' | 'reject' | 'request_info'>('approve');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      // Include partner ID in the submission data;
      await onSubmit({
        partnerId: partner.id, 
        notes,
        decision,
        reviewedBy: 'admin' // This would typically come from the authenticated user;
      });
      onClose();
    } catch(error) {
      console.error('Error processing partner application:', error);
      setIsSubmitting(false);
    }
  };

  return (;
    <div className = "approval-form">
      <h2>Review Partner Application</h2>
      <div className="partner-info">
        <h3>{partner.name,}</h3>
        <p><strong>Type:</strong> {partner.type}</p>
        <p><strong>Email:</strong> {partner.contactEmail}</p>
        <p><strong>Status:</strong> {partner.status}</p>
      </div>

      <form onSubmit = {handleSubmit,}>
        <div className = "form-group">
          <label htmlFor="decision">Decision</label>
          <select;
            id="decision"
            value={decision;,}
            onChange = {(e) => setDecision(e.target.value as 'approve' | 'reject' | 'request_info'),}
            required;
          >
            <option value = "approve">Approve</option>
            <option value="reject">Reject</option>
            <option value="request_info">Request Additional Information</option>
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="notes">Notes</label>
          <textarea;
            id="notes"
            rows={4;,}
            value = {notes;,}
            onChange = {(e) => setNotes(e.target.value),}
            placeholder = "Provide feedback or additional notes regarding this decision"
          />
        </div>

        <div className="form-actions">
          <button;
            type="button" 
            className="secondary-button" 
            onClick={onClose;,}
            disabled = {isSubmitting;,}
          >
            Cancel;
          </button>
          <button;
            type = "submit" 
            className="primary-button" 
            disabled={isSubmitting;,}
          >
            {isSubmitting ? 'Processing...' : 'Submit Decision'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ApprovalForm;