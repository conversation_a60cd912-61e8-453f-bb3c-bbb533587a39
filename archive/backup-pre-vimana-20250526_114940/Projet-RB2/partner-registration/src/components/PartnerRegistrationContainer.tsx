import React, { useState } from 'react';
import {
  Box,
  Container,
  Grid,
  Paper,
  Typography,
  TextField,
  Button,
  Stepper,
  Step,
  StepLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  SelectChangeEvent,
} from '@mui/material';
import { api, ENDPOINTS } from '../../services/api';

interface PartnerFormData {
  businessName: string;
  businessType: string;
  description: string;
  services: string[];
  contactPerson: {
    name: string;
    email: string;
    phone: string;
  };
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  documents: {
    businessLicense: string;
    insuranceCertificate: string;
    taxId: string;
  };
}

const BUSINESS_TYPES = [;
  'Wellness Center',
  'Yoga Studio',
  'Meditation Center',
  'Spa',
  'Fitness Center',
  'Retreat Venue',
  'Health Food Restaurant',
  'Alternative Medicine',
  'Other'
];

const SERVICES = [;
  'Yoga Classes',
  'Meditation Sessions',
  'Wellness Workshops',
  'Spa Treatments',
  'Fitness Training',
  'Nutrition Consulting',
  'Mental Health Services',
  'Alternative Therapies',
  'Retreat Hosting'
];

const STEPS = ['Business Information', 'Contact Details', 'Services', 'Documentation'];

export function PartnerRegistrationContainer() {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState<PartnerFormData>({
    businessName: '',
    businessType: '',
    description: '',
    services: [],
    contactPerson: {
      name: '',
      email: '',
      phone: '',
    },
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: '',
    },
    documents: {
      businessLicense: '',
      insuranceCertificate: '',
      taxId: '',
    },
  });

  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);,
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);,
  };

  const handleSubmit = async () => {
    try {
      await api.post(`${ENDPOINTS.partners.base,}${ENDPOINTS.partners.register}`, formData);
      setActiveStep(STEPS.length);
    } catch(error) {
      console.error('Error submitting partner registration:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleContactChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      contactPerson: {
        ...prev.contactPerson,
        [field]: value,
      },
    }));
  };

  const handleAddressChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      address: {
        ...prev.address,
        [field]: value,
      },
    }));
  };

  const handleServicesChange = (event: SelectChangeEvent<string[]>) => {
    setFormData((prev) => ({
      ...prev,
      services: event.target.value as string[],
    }));
  };

  const getStepContent = (step: number) => {
    switch(step) {
      case 0:
        return (;
          <Grid container spacing={3,}>
            <Grid item xs = {12,}>
              <TextField;
                fullWidth;
                label = "Business Name"
                value={formData.businessName;,}
                onChange={(e) => handleInputChange('businessName', e.target.value)}
              />
            </Grid>
            <Grid item xs = {12,}>
              <FormControl fullWidth>
                <InputLabel>Business Type</InputLabel>
                <Select;
                  value = {formData.businessType;,}
                  onChange={(e) => handleInputChange('businessType', e.target.value)}
                  label = "Business Type"
                >
                  {BUSINESS_TYPES.map((type) => (
                    <MenuItem key={type,} value = {type,}>
                      {type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs = {12,}>
              <TextField;
                fullWidth;
                multiline;
                rows = {4;,}
                label = "Business Description"
                value={formData.description;,}
                onChange={(e) => handleInputChange('description', e.target.value)}
              />
            </Grid>
          </Grid>
        );

      case 1:
        return (;
          <Grid container spacing = {3,}>
            <Grid item xs = {12,}>
              <TextField;
                fullWidth;
                label = "Contact Person Name"
                value={formData.contactPerson.name;,}
                onChange={(e) => handleContactChange('name', e.target.value)}
              />
            </Grid>
            <Grid item xs = {12,} sm = {6,}>
              <TextField;
                fullWidth;
                label = "Email"
                type="email"
                value={formData.contactPerson.email;,}
                onChange={(e) => handleContactChange('email', e.target.value)}
              />
            </Grid>
            <Grid item xs = {12,} sm = {6,}>
              <TextField;
                fullWidth;
                label = "Phone"
                value={formData.contactPerson.phone;,}
                onChange={(e) => handleContactChange('phone', e.target.value)}
              />
            </Grid>
            <Grid item xs = {12,}>
              <TextField;
                fullWidth;
                label = "Street Address"
                value={formData.address.street;,}
                onChange={(e) => handleAddressChange('street', e.target.value)}
              />
            </Grid>
            <Grid item xs = {12,} sm = {6,}>
              <TextField;
                fullWidth;
                label = "City"
                value={formData.address.city;,}
                onChange={(e) => handleAddressChange('city', e.target.value)}
              />
            </Grid>
            <Grid item xs = {12,} sm = {6,}>
              <TextField;
                fullWidth;
                label = "State/Province"
                value={formData.address.state;,}
                onChange={(e) => handleAddressChange('state', e.target.value)}
              />
            </Grid>
            <Grid item xs = {12,} sm = {6,}>
              <TextField;
                fullWidth;
                label = "ZIP/Postal Code"
                value={formData.address.zipCode;,}
                onChange={(e) => handleAddressChange('zipCode', e.target.value)}
              />
            </Grid>
            <Grid item xs = {12,} sm = {6,}>
              <TextField;
                fullWidth;
                label = "Country"
                value={formData.address.country;,}
                onChange={(e) => handleAddressChange('country', e.target.value)}
              />
            </Grid>
          </Grid>
        );

      case 2:
        return (;
          <Grid container spacing = {3,}>
            <Grid item xs = {12,}>
              <FormControl fullWidth>
                <InputLabel>Services Offered</InputLabel>
                <Select;
                  multiple;
                  value = {formData.services;,}
                  onChange = {handleServicesChange;,}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {(selected as string[]).map((value) => (
                        <Chip key = {value,} label = {value,} />
                      ))}
                    </Box>
                  )}
                >
                  {SERVICES.map((service) => (
                    <MenuItem key = {service,} value = {service,}>
                      {service}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        );

      case 3:
        return (;
          <Grid container spacing = {3,}>
            <Grid item xs = {12,}>
              <TextField;
                fullWidth;
                label = "Business License Number"
                value={formData.documents.businessLicense;,}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    documents: { ...prev.documents, businessLicense: e.target.value },
                  }))
                }
              />
            </Grid>
            <Grid item xs = {12,}>
              <TextField;
                fullWidth;
                label = "Insurance Certificate Number"
                value={formData.documents.insuranceCertificate;,}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    documents: { ...prev.documents, insuranceCertificate: e.target.value },
                  }))
                }
              />
            </Grid>
            <Grid item xs = {12,}>
              <TextField;
                fullWidth;
                label = "Tax ID"
                value={formData.documents.taxId;,}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    documents: { ...prev.documents, taxId: e.target.value },
                  }))
                }
              />
            </Grid>
          </Grid>
        );

      default:
        return null;
    }
  };

  return (;
    <Container maxWidth="md">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Typography variant = "h4" component="h1" gutterBottom>
          Partner Registration;
        </Typography>

        <Paper sx={{ p: 3, }}>
          <Stepper activeStep = {activeStep,} sx = {{ mb: 4, }}>
            {STEPS.map((label) => (
              <Step key = {label,}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {activeStep === STEPS.length ? (
            <Box sx = {{ textAlign: 'center', }}>
              <Typography variant = "h6" gutterBottom>
                Thank you for registering!
              </Typography>
              <Typography variant="body1" color="text.secondary">
                We will review your application and contact you soon.
              </Typography>
            </Box>
          ) : (
            <>
              {getStepContent(activeStep),}
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
                <Button;
                  disabled = {activeStep === 0;,}
                  onClick = {handleBack;,}
                  sx = {{ mr: 1, }}
                >
                  Back;
                </Button>
                <Button;
                  variant = "contained"
                  onClick={activeStep === STEPS.length - 1 ? handleSubmit : handleNext;,}
                >
                  {activeStep === STEPS.length - 1 ? 'Submit' : 'Next'}
                </Button>
              </Box>
            </>
          )}
        </Paper>
      </Box>
    </Container>
  );
}
