import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { setupDatabase } from "./database/setup.js';
import { partnerRoutes } from "./routes/partner.routes.js';
import { adminRoutes } from "./routes/admin.routes.js';
import { errorHandler } from "./middleware/error.middleware.js';
import { logger } from "./utils/logger.js';
import path from 'path';
import { fileURLToPath } from 'url';

dotenv.config();

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Ensure uploads directory exists
import fs from 'fs';
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir);
}

// Routes
app.use('/api/partners', partnerRoutes);
app.use('/api/admin', adminRoutes);

// Error handling
app.use(errorHandler);

// Initialize database
setupDatabase()
  .then(() => {
    app.listen(PORT, () => {
      logger.info(`Server running on port ${PORT}`);
    });
  })
  .catch((error) => {
    logger.error('Failed to initialize database:', error);
    process.exit(1);
  });