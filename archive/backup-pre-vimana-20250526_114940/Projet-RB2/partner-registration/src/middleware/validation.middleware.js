import { logger } from '../utils/logger.js';

export const validatePartnerData = (req, res, next) => {
  const { companyName, email, phone, address } = req.body;
  const errors = [];

  if (!companyName?.trim()) {
    errors.push('Company name is required');
  }

  if (!email?.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
    errors.push('Valid email is required');
  }

  if (!phone?.match(/^\+?[\d\s-]{8,}$/)) {
    errors.push('Valid phone number is required');
  }

  if (!address?.trim()) {
    errors.push('Address is required');
  }

  if (!req.files?.identityDocument || !req.files?.businessCertificate) {
    errors.push('All required documents must be uploaded');
  }

  if (errors.length > 0) {
    logger.warn('Partner data validation failed:', { errors });
    return res.status(400).json({
      success: false,
      errors
    });
  }

  next();
};