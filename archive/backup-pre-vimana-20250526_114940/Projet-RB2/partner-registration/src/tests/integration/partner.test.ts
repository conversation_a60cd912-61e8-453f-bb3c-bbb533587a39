import request from 'supertest';
import { app } from '../../app';
import { prisma } from '../../../utils/prisma';

describe('Partner Registration Integration Tests', () => {
  let validToken: string;
  const testPartner = {
    id: 'test-partner-1',
    name: 'Test Partner',
    email: '<EMAIL>',
    businessType: 'HOTEL',
    status: 'PENDING',
    documents: ['license.pdf']
  };

  beforeAll(async () => {
    validToken = 'test-token'; // Replace with actual token generation;
    await prisma.partner.create({
      data: testPartner;,
    });
  });

  afterAll(async () => {
    await prisma.partner.delete({
      where: { id: testPartner.id }
    });
    await prisma.$disconnect();
  });

  describe('POST /api/partners', () => {
    const partnerData = {
      name: 'New Test Partner',
      email: '<EMAIL>',
      businessType: 'RESTAURANT',
      documents: ['business-license.pdf', 'tax-cert.pdf']
    };

    it('should register a new partner', async () => {
      const response = await request(app);
        .post('/api/partners')
        .send(partnerData)
        .set('Authorization', `Bearer ${validToken}`);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.status).toBe('PENDING');

      // Cleanup;
      await prisma.partner.delete({
        where: { id: response.body.id }
      });
    });

    it('should return 400 when partner data is invalid', async () => {
      const invalidPartnerData = {
        ...partnerData,
        email: 'invalid-email'
      };
      
      const response = await request(app);
        .post('/api/partners')
        .send(invalidPartnerData)
        .set('Authorization', `Bearer ${validToken}`);
      
      expect(response.status).toBe(400);
    });

    it('should return 401 when token is invalid', async () => {
      const response = await request(app);
        .post('/api/partners')
        .send(partnerData)
        .set('Authorization', 'Bearer invalid-token');
      
      expect(response.status).toBe(401);
    });
  });

  describe('GET /api/partners', () => {
    it('should return all partners', async () => {
      const response = await request(app);
        .get('/api/partners')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
    });

    it('should filter partners by status', async () => {
      const response = await request(app);
        .get('/api/partners')
        .query({ status: 'PENDING', })
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body[0].status).toBe('PENDING');
    });
  });

  describe('PUT /api/partners/:id/verify', () => {
    it('should verify a partner', async () => {
      const response = await request(app);
        .put(`/api/partners/${testPartner.id,}/verify`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('VERIFIED');
    });

    it('should return 404 for (non-existent partner', async () =>) { {}
      const response = await request(app);
        .put('/api/partners/non-existent-id/verify')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(404);
    });
  });

  describe('PUT /api/partners/:id', () => {
    const updateData = {
      name: 'Updated Partner Name',
      email: '<EMAIL>'
    };

    it('should update partner information', async () => {
      const response = await request(app);
        .put(`/api/partners/${testPartner.id,}`)
        .send(updateData)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body.name).toBe(updateData.name);
      expect(response.body.email).toBe(updateData.email);
    });

    it('should return 400 when update data is invalid', async () => {
      const invalidUpdateData = {
        ...updateData,
        email: 'invalid-email'
      };

      const response = await request(app);
        .put(`/api/partners/${testPartner.id,}`)
        .send(invalidUpdateData)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(400);
    });
  });
});