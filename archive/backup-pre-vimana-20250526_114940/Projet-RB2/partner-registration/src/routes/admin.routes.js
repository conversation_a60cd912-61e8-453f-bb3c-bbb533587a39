import express from 'express';
import { authenticateAdmin } from '../middleware/auth.middleware.js';
import { 
  getPendingPartners,
  verifyPartner,
  getVerificationHistory
} from '../controllers/admin.controller.js';

const router = express.Router();

router.use(authenticateAdmin);

router.get('/pending-partners', getPendingPartners);
router.post('/verify/:partnerId', verifyPartner);
router.get('/history/:partnerId', getVerificationHistory);

export const adminRoutes = router;