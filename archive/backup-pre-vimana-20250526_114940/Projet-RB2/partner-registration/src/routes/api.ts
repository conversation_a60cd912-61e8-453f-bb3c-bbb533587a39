import { Router } from 'express';
import authRoutes from './authRoutes';
import retreatRoutes from './retreatRoutes';
import bookingRoutes from './bookingRoutes';
import courseRoutes from './courseRoutes';
import productRoutes from './productRoutes';
import orderRoutes from './orderRoutes';
import nftRoutes from './nftRoutes';
import insuranceRoutes from './insuranceRoutes';
import partnerRoutes from './partnerRoutes';
import adminRoutes from './adminRoutes';

const router = Router();

// Authentication and user management;
router.use('/auth', authRoutes);

// Core business routes;
router.use('/retreats', retreatRoutes);
router.use('/bookings', bookingRoutes);
router.use('/courses', courseRoutes);
router.use('/products', productRoutes);
router.use('/orders', orderRoutes);

// Blockchain and NFT routes;
router.use('/nfts', nftRoutes);

// Additional services;
router.use('/insurance', insuranceRoutes);

// Partner registration and management;
router.use('/partners', partnerRoutes);
router.use('/admin', adminRoutes);

export default router;