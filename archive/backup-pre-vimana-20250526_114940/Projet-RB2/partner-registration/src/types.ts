// Partner Types and Interfaces;
export enum PartnerType {
  HOTEL = 'hotel',
  MEDITATION_CENTER = 'meditation_center',
  INSTRUCTOR = 'instructor',
  WELLNESS_CENTER = 'wellness_center',
}

export enum AccountStatus {
  PENDING = 'PENDING',
  UNDER_REVIEW = 'UNDER_REVIEW',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  INFORMATION_REQUESTED = 'INFORMATION_REQUESTED',
}

export enum RegistrationStatus {
  PENDING = 'pending',
  VERIFIED = 'verified',
  REJECTED = 'rejected',
  SUSPENDED = 'suspended',
}

export interface Location {
  address: string;
  city: string;
  state?: string;
  country: string;
  postalCode: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
}

export interface Contact {
  name: string;
  email: string;
  phone: string;
  position: string;
}

export interface Document {
  id: string;
  name: string;
  type: string;
  url: string;
  status: 'En attente' | 'Approuvé' | 'Rejeté';
  uploadedAt: Date;
  progress?: number;
}

export interface Partner {
  id: string;
  type: PartnerType;
  name: string; // Added name field;
  businessName: string;
  description: string;
  location: Location;
  contacts: Contact[];
  documents: Document[];
  status: AccountStatus; // Changed from RegistrationStatus to AccountStatus;
  createdAt: Date;
  updatedAt: Date;
  verificationDate?: Date;
  rating?: number;
  capacity?: number; // For hotels and meditation centers;
  specialties?: string[]; // For instructors;
  amenities?: string[]; // For hotels and meditation centers;
  website?: string;
  contactEmail: string; // Added contactEmail field;
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    linkedin?: string;
  };
}

export interface PartnerRegistrationRequest {
  type: PartnerType;
  businessName: string;
  description: string;
  location: Location;
  primaryContact: Contact;
  documents: {
    type: string;
    file: File;
  }[];
  additionalInfo?: {
    capacity?: number;
    specialties?: string[];
    amenities?: string[];
    website?: string;
    socialMedia?: {
      facebook?: string;
      instagram?: string;
      linkedin?: string;
    };
  };
}

export interface VerificationResult {
  success: boolean;
  message: string;
  verifiedFields: string[];
  issues?: {
    field: string;
    issue: string;
  }[];
}