## Partner Registration System

### Overview
The Partner Registration System is a microservice that handles the onboarding and verification of new partners for Retreat and Be. It provides automated KYC verification, document processing, and admin approval workflows.

### Features
- Automated partner registration
- Document verification with OCR
- Admin approval interface
- Real-time status updates
- Secure document storage
- Email notifications

### Tech Stack
- Node.js & Express
- TypeScript
- SQLite database
- TensorFlow.js for document verification
- Tesseract.js for OCR
- Docker containerization

### Installation

```bash
# Clone the repository into the partner-registration directory
git clone [repository-url] partner-registration

# Navigate to the directory
cd partner-registration

# Install dependencies
npm install

# Create .env file
cp .env.example .env

# Start the service
npm start
```

### API Endpoints

#### Partner Routes
- `POST /api/partners/register` - Register new partner
- `GET /api/partners/status/:partnerId` - Check registration status
- `PUT /api/partners/:partnerId` - Update partner information

#### Admin Routes
- `GET /api/admin/pending-partners` - List pending registrations
- `POST /api/admin/verify/:partnerId` - Verify partner
- `GET /api/admin/history/:partnerId` - View verification history

### Docker Integration

The service is containerized and can be integrated into the main project using Docker Compose. Add the following to your main docker-compose.yml:

```yaml
services:
  partner-registration:
    build: ./partner-registration
    container_name: retreat-partner-registration
    ports:
      - "3001:3000"
    volumes:
      - ./partner-registration/uploads:/app/uploads
      - ./partner-registration/database:/app/database
    environment:
      - NODE_ENV=production
      - JWT_SECRET=${JWT_SECRET}
      - ADMIN_EMAIL=${ADMIN_EMAIL}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
    restart: unless-stopped
    networks:
      - retreat-network
```

### Environment Variables

```env
NODE_ENV=production
JWT_SECRET=your-secret-key
ADMIN_EMAIL=<EMAIL>
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=user
SMTP_PASS=password
```

### Security Considerations
- All documents are encrypted at rest
- JWT authentication for admin routes
- Rate limiting on all endpoints
- Input validation and sanitization
- CORS protection
- Secure file upload handling

### Maintenance
- Regular database backups
- Log rotation
- Document cleanup for rejected applications
- System health monitoring