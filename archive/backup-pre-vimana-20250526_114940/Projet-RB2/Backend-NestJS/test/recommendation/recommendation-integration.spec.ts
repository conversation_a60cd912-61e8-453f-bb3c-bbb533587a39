import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { PrismaService } from '../../src/prisma/prisma.service';
import { RecommendationType } from '../../src/modules/recommendation/enums/recommendation-type.enum';
import { RecommendationStrategy } from '../../src/modules/recommendation/enums/recommendation-strategy.enum';
import { JwtAuthGuard } from '../../src/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../src/modules/auth/guards/roles.guard';

describe('Recommendation System Integration Tests', () => {
  let app: INestApplication;
  let prismaService: PrismaService;

  // Mock des données
  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    role: 'USER'
  };

  const mockUserProfile = {
    userId: mockUser.id,
    location: {
      country: 'France',
      city: 'Paris'
    },
    preferences: {
      categories: ['yoga', 'meditation']
    }
  };

  const mockCourses = [
    {
      id: 'course-1',
      title: 'Yoga for Beginners',
      description: 'A gentle introduction to yoga',
      category: 'yoga',
      level: 'BEGINNER',
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        tags: ['yoga', 'beginner', 'flexibility'],
        duration: 60
      }
    },
    {
      id: 'course-2',
      title: 'Advanced Meditation',
      description: 'Deep meditation techniques',
      category: 'meditation',
      level: 'ADVANCED',
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        tags: ['meditation', 'advanced', 'mindfulness'],
        duration: 45
      }
    }
  ];

  const mockInteractions = [
    {
      id: 'interaction-1',
      userId: mockUser.id,
      itemId: 'course-1',
      itemType: RecommendationType.COURSE,
      interactionType: 'VIEW',
      createdAt: new Date(),
      metadata: {}
    },
    {
      id: 'interaction-2',
      userId: mockUser.id,
      itemId: 'course-2',
      itemType: RecommendationType.COURSE,
      interactionType: 'LIKE',
      createdAt: new Date(),
      metadata: {}
    }
  ];

  // Mock du JWT Auth Guard pour contourner l'authentification
  const mockJwtAuthGuard = {
    canActivate: jest.fn().mockImplementation(() => true)
  };

  // Mock du Roles Guard pour contourner la vérification des rôles
  const mockRolesGuard = {
    canActivate: jest.fn().mockImplementation(() => true)
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .overrideGuard(RolesGuard)
      .useValue(mockRolesGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    prismaService = moduleFixture.get<PrismaService>(PrismaService);

    // Configurer les mocks pour Prisma
    jest.spyOn(prismaService.user, 'findUnique').mockResolvedValue(mockUser as any);
    jest.spyOn(prismaService.userProfile, 'findUnique').mockResolvedValue(mockUserProfile as any);
    jest.spyOn(prismaService.course, 'findMany').mockResolvedValue(mockCourses as any);
    jest.spyOn(prismaService.userInteraction, 'findMany').mockResolvedValue(mockInteractions as any);
    jest.spyOn(prismaService.userInteraction, 'count').mockResolvedValue(mockInteractions.length);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('GET /recommendations', () => {
    it('should return recommendations using the default strategy', async () => {
      const response = await request(app.getHttpServer())
        .get('/recommendations')
        .query({
          type: RecommendationType.COURSE,
          limit: 5
        })
        .set('Authorization', 'Bearer mock-token')
        .expect(200);

      expect(response.body).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeLessThanOrEqual(5);
      expect(response.body.meta.total).toBeGreaterThanOrEqual(0);
    });

    it('should return recommendations using the content-based strategy', async () => {
      const response = await request(app.getHttpServer())
        .get('/recommendations')
        .query({
          type: RecommendationType.COURSE,
          strategy: RecommendationStrategy.CONTENT_BASED,
          limit: 5
        })
        .set('Authorization', 'Bearer mock-token')
        .expect(200);

      expect(response.body).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeLessThanOrEqual(5);
      
      // Vérifier que les sources incluent 'content-based'
      if (response.body.data.length > 0) {
        expect(response.body.data[0].sources).toContain('content-based');
      }
    });

    it('should return recommendations using the matrix factorization strategy', async () => {
      const response = await request(app.getHttpServer())
        .get('/recommendations')
        .query({
          type: RecommendationType.COURSE,
          strategy: RecommendationStrategy.MATRIX_FACTORIZATION,
          limit: 5
        })
        .set('Authorization', 'Bearer mock-token')
        .expect(200);

      expect(response.body).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      
      // Vérifier que les sources incluent 'matrix-factorization'
      if (response.body.data.length > 0) {
        expect(response.body.data[0].sources).toContain('matrix-factorization');
      }
    });

    it('should return recommendations with diversity filter applied', async () => {
      const response = await request(app.getHttpServer())
        .get('/recommendations')
        .query({
          type: RecommendationType.COURSE,
          diversification: {
            enabled: true,
            weight: 0.5,
            method: 'MMR'
          },
          limit: 5
        })
        .set('Authorization', 'Bearer mock-token')
        .expect(200);

      expect(response.body).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      
      // Vérifier la diversité des catégories si possible
      if (response.body.data.length > 1) {
        const categories = new Set(response.body.data.map(r => r.metadata?.category));
        expect(categories.size).toBeGreaterThanOrEqual(1);
      }
    });
  });

  describe('GET /recommendations/similar/:itemId', () => {
    it('should return similar items', async () => {
      const response = await request(app.getHttpServer())
        .get(`/recommendations/similar/${mockCourses[0].id}`)
        .query({
          type: RecommendationType.COURSE,
          limit: 5
        })
        .set('Authorization', 'Bearer mock-token')
        .expect(200);

      expect(response.body).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeLessThanOrEqual(5);
      expect(response.body.meta.total).toBeGreaterThanOrEqual(0);
    });
  });
});
