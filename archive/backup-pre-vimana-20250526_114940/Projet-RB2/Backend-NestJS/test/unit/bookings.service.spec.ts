import { Test, TestingModule } from '@nestjs/testing';
import { BookingsService } from '../../src/modules/bookings/bookings.service';
import { PrismaService } from '../../src/prisma/prisma.service';
import { NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { UserRole } from '../../src/modules/auth/decorators/roles.decorator';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { mockDeep, DeepMockProxy } from 'jest-mock-extended';

describe('BookingsService', () => {
  let service: BookingsService;
  let prismaService: DeepMockProxy<PrismaService>;
  let eventEmitter: DeepMockProxy<EventEmitter2>;

  beforeEach(async () => {
    // Create mock instances
    prismaService = mockDeep<PrismaService>();
    eventEmitter = mockDeep<EventEmitter2>();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BookingsService,
        { provide: PrismaService, useValue: prismaService },
        { provide: EventEmitter2, useValue: eventEmitter },
      ],
    }).compile();

    service = module.get<BookingsService>(BookingsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a booking successfully', async () => {
      // Arrange
      const userId = 'user-id';
      const createBookingDto = {
        retreatId: 'retreat-id',
        startDate: '2023-12-01',
        endDate: '2023-12-10',
        numberOfParticipants: 2,
        specialRequests: 'Vegetarian meals',
      };

      const user = {
        id: userId,
        role: UserRole.USER,
      };

      const retreat = {
        id: createBookingDto.retreatId,
        title: 'Test Retreat',
        price: 1000,
        capacity: 20,
        startDate: new Date('2023-12-01'),
        endDate: new Date('2023-12-10'),
        hostId: 'host-id',
        bookings: [],
      };

      const createdBooking = {
        id: 'booking-id',
        retreatId: createBookingDto.retreatId,
        userId,
        startDate: new Date(createBookingDto.startDate),
        endDate: new Date(createBookingDto.endDate),
        numberOfParticipants: createBookingDto.numberOfParticipants,
        specialRequests: createBookingDto.specialRequests,
        status: 'PENDING',
        totalPrice: 2000, // 1000 * 2 participants
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock the prisma calls
      prismaService.user.findUnique.mockResolvedValue(user as any);
      prismaService.retreat.findUnique.mockResolvedValue(retreat as any);
      prismaService.booking.create.mockResolvedValue(createdBooking as any);

      // Act
      const result = await service.create(createBookingDto, userId);

      // Assert
      expect(result).toEqual(createdBooking);
      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
      });
      expect(prismaService.retreat.findUnique).toHaveBeenCalledWith({
        where: { id: createBookingDto.retreatId },
        include: { bookings: true },
      });
      expect(prismaService.booking.create).toHaveBeenCalledWith({
        data: {
          retreat: { connect: { id: createBookingDto.retreatId } },
          user: { connect: { id: userId } },
          startDate: new Date(createBookingDto.startDate),
          endDate: new Date(createBookingDto.endDate),
          numberOfParticipants: createBookingDto.numberOfParticipants,
          specialRequests: createBookingDto.specialRequests,
          status: 'PENDING',
          totalPrice: 2000, // 1000 * 2 participants
        },
      });
      expect(eventEmitter.emit).toHaveBeenCalledWith('booking.created', expect.any(Object));
    });

    it('should throw NotFoundException if user not found', async () => {
      // Arrange
      const userId = 'non-existent-user-id';
      const createBookingDto = {
        retreatId: 'retreat-id',
        startDate: '2023-12-01',
        endDate: '2023-12-10',
        numberOfParticipants: 2,
      };

      // Mock the prisma call to return null (user not found)
      prismaService.user.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(service.create(createBookingDto as any, userId)).rejects.toThrow(NotFoundException);
      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
      });
    });

    it('should throw NotFoundException if retreat not found', async () => {
      // Arrange
      const userId = 'user-id';
      const createBookingDto = {
        retreatId: 'non-existent-retreat-id',
        startDate: '2023-12-01',
        endDate: '2023-12-10',
        numberOfParticipants: 2,
      };

      const user = {
        id: userId,
        role: UserRole.USER,
      };

      // Mock the prisma calls
      prismaService.user.findUnique.mockResolvedValue(user as any);
      prismaService.retreat.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(service.create(createBookingDto as any, userId)).rejects.toThrow(NotFoundException);
      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
      });
      expect(prismaService.retreat.findUnique).toHaveBeenCalledWith({
        where: { id: createBookingDto.retreatId },
        include: { bookings: true },
      });
    });

    it('should throw BadRequestException if not enough capacity', async () => {
      // Arrange
      const userId = 'user-id';
      const createBookingDto = {
        retreatId: 'retreat-id',
        startDate: '2023-12-01',
        endDate: '2023-12-10',
        numberOfParticipants: 5,
      };

      const user = {
        id: userId,
        role: UserRole.USER,
      };

      const retreat = {
        id: createBookingDto.retreatId,
        title: 'Test Retreat',
        price: 1000,
        capacity: 10,
        startDate: new Date('2023-12-01'),
        endDate: new Date('2023-12-10'),
        hostId: 'host-id',
        bookings: [
          { numberOfParticipants: 6, status: 'CONFIRMED' }, // Already 6 participants booked
        ],
      };

      // Mock the prisma calls
      prismaService.user.findUnique.mockResolvedValue(user as any);
      prismaService.retreat.findUnique.mockResolvedValue(retreat as any);

      // Act & Assert
      await expect(service.create(createBookingDto as any, userId)).rejects.toThrow(BadRequestException);
      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
      });
      expect(prismaService.retreat.findUnique).toHaveBeenCalledWith({
        where: { id: createBookingDto.retreatId },
        include: { bookings: true },
      });
    });
  });

  describe('findAll', () => {
    it('should return paginated bookings', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        status: 'CONFIRMED',
      };

      const bookings = [
        {
          id: 'booking-1',
          retreatId: 'retreat-1',
          userId: 'user-1',
          status: 'CONFIRMED',
          retreat: { title: 'Yoga Retreat' },
          user: { firstName: 'John', lastName: 'Doe' },
        },
        {
          id: 'booking-2',
          retreatId: 'retreat-2',
          userId: 'user-2',
          status: 'CONFIRMED',
          retreat: { title: 'Meditation Retreat' },
          user: { firstName: 'Jane', lastName: 'Smith' },
        },
      ];

      const total = 2;

      // Mock the prisma calls
      prismaService.booking.findMany.mockResolvedValue(bookings as any);
      prismaService.booking.count.mockResolvedValue(total);

      // Act
      const result = await service.findAll(params);

      // Assert
      expect(result).toEqual({
        data: bookings,
        total,
        page: params.page,
        limit: params.limit,
      });
      expect(prismaService.booking.findMany).toHaveBeenCalled();
      expect(prismaService.booking.count).toHaveBeenCalled();
    });
  });

  // Add more test cases for other methods as needed
});
