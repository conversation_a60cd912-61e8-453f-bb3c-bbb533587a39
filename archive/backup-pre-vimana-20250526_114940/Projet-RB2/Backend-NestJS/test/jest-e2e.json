{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "moduleNameMapper": {"^src/(.*)$": "<rootDir>/../src/$1", "^@/(.*)$": "<rootDir>/../src/$1"}, "collectCoverageFrom": ["../src/**/*.(t|j)s", "!../src/**/*.spec.ts", "!../src/**/*.e2e-spec.ts", "!../src/**/*.d.ts", "!../src/main.ts", "!../src/main-mtls.ts"], "coverageDirectory": "./coverage-e2e", "setupFilesAfterEnv": ["<rootDir>/setup-e2e.ts"], "testTimeout": 60000, "maxWorkers": 1, "forceExit": true, "detectOpenHandles": true, "verbose": true}