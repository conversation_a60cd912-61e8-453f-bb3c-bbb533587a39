import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { JwtAuthGuard } from '../../src/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../src/modules/auth/guards/roles.guard';
import { RecommendationType } from '../../src/modules/recommendation/enums/recommendation-type.enum';
import { HttpService } from '@nestjs/axios';
import { of } from 'rxjs';

// Mock pour le JwtAuthGuard
const mockJwtAuthGuard = {
  canActivate: jest.fn().mockImplementation(() => true),
};

// Mock pour le RolesGuard
const mockRolesGuard = {
  canActivate: jest.fn().mockImplementation(() => true),
};

// Mock pour l'utilisateur authentifié
const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  role: 'USER',
};

// Mock pour le service HTTP
const mockHttpService = {
  post: jest.fn().mockImplementation((url, data, config) => {
    if (url.includes('/api/analytics/events')) {
      return of({
        data: {
          success: true,
          message: 'Event recorded',
          eventId: 'test-event-id',
        },
      });
    }
    return of({ data: {} });
  }),
};

describe('Analytics Integration Tests', () => {
  let app: INestApplication;
  let moduleFixture: TestingModule;

  beforeAll(async () => {
    moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .overrideGuard(RolesGuard)
      .useValue(mockRolesGuard)
      .overrideProvider(HttpService)
      .useValue(mockHttpService)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());

    // Mock pour la requête utilisateur
    app.use((req, res, next) => {
      req.user = mockUser;
      next();
    });

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /recommendations/analytics-integration/view/:type/:id', () => {
    it('should track a recommendation view event', async () => {
      const eventData = {
        metadata: {
          source: 'homepage',
          position: 2,
        },
      };

      const response = await request(app.getHttpServer())
        .post(`/recommendations/analytics-integration/view/${RecommendationType.RETREAT}/test-retreat-id`)
        .send(eventData)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Événement de visualisation enregistré avec succès');
      expect(response.body.data).toBeDefined();
    });
  });

  describe('POST /recommendations/analytics-integration/click/:type/:id', () => {
    it('should track a recommendation click event', async () => {
      const eventData = {
        metadata: {
          source: 'search-results',
          position: 1,
        },
      };

      const response = await request(app.getHttpServer())
        .post(`/recommendations/analytics-integration/click/${RecommendationType.COURSE}/test-course-id`)
        .send(eventData)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Événement de clic enregistré avec succès');
      expect(response.body.data).toBeDefined();
    });
  });

  describe('POST /recommendations/analytics-integration/explanation-view/:type/:id', () => {
    it('should track an explanation view event', async () => {
      const eventData = {
        explanationId: 'test-explanation-id',
        metadata: {
          detailLevel: 'DETAILED',
          language: 'fr',
        },
      };

      const response = await request(app.getHttpServer())
        .post(`/recommendations/analytics-integration/explanation-view/${RecommendationType.RETREAT}/test-retreat-id`)
        .send(eventData)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Événement de visualisation d\'explication enregistré avec succès');
      expect(response.body.data).toBeDefined();
    });

    it('should return 400 if explanationId is missing', async () => {
      const eventData = {
        metadata: {
          detailLevel: 'DETAILED',
          language: 'fr',
        },
      };

      await request(app.getHttpServer())
        .post(`/recommendations/analytics-integration/explanation-view/${RecommendationType.RETREAT}/test-retreat-id`)
        .send(eventData)
        .expect(400);
    });
  });

  describe('POST /recommendations/analytics-integration/explanation-feedback/:type/:id', () => {
    it('should track an explanation feedback event', async () => {
      const eventData = {
        explanationId: 'test-explanation-id',
        feedbackType: 'HELPFUL',
        metadata: {
          comment: 'This explanation was very helpful',
        },
      };

      const response = await request(app.getHttpServer())
        .post(`/recommendations/analytics-integration/explanation-feedback/${RecommendationType.RETREAT}/test-retreat-id`)
        .send(eventData)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Événement de feedback sur l\'explication enregistré avec succès');
      expect(response.body.data).toBeDefined();
    });

    it('should return 400 if explanationId or feedbackType is missing', async () => {
      // Missing explanationId
      const eventData1 = {
        feedbackType: 'HELPFUL',
        metadata: {
          comment: 'This explanation was very helpful',
        },
      };

      await request(app.getHttpServer())
        .post(`/recommendations/analytics-integration/explanation-feedback/${RecommendationType.RETREAT}/test-retreat-id`)
        .send(eventData1)
        .expect(400);

      // Missing feedbackType
      const eventData2 = {
        explanationId: 'test-explanation-id',
        metadata: {
          comment: 'This explanation was very helpful',
        },
      };

      await request(app.getHttpServer())
        .post(`/recommendations/analytics-integration/explanation-feedback/${RecommendationType.RETREAT}/test-retreat-id`)
        .send(eventData2)
        .expect(400);
    });
  });

  describe('GET /recommendations/analytics-integration/events/user/:userId', () => {
    it('should get events for a user (admin only)', async () => {
      // Override the user role to ADMIN for this test
      const originalUser = { ...mockUser };
      mockUser.role = 'ADMIN';

      const response = await request(app.getHttpServer())
        .get(`/recommendations/analytics-integration/events/user/test-user-id`)
        .query({
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          eventType: 'recommendation_viewed',
        })
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Cette fonctionnalité sera implémentée dans une version future');
      expect(response.body.data).toBeDefined();
      expect(response.body.data.userId).toBe('test-user-id');
      expect(response.body.data.startDate).toBe('2023-01-01');
      expect(response.body.data.endDate).toBe('2023-12-31');
      expect(response.body.data.eventType).toBe('recommendation_viewed');

      // Restore the original user role
      mockUser.role = originalUser.role;
    });

    it('should return 403 if user is not an admin', async () => {
      // Ensure the user role is not ADMIN
      mockUser.role = 'USER';

      await request(app.getHttpServer())
        .get(`/recommendations/analytics-integration/events/user/test-user-id`)
        .expect(403);
    });
  });
});
