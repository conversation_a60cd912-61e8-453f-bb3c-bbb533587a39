import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { JwtAuthGuard } from '../../src/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../src/modules/auth/guards/roles.guard';
import { RecommendationType } from '../../src/modules/recommendation/enums/recommendation-type.enum';
import { FeedbackType } from '../../src/modules/recommendation/services/feedback.service';

// Mock pour le JwtAuthGuard
const mockJwtAuthGuard = {
  canActivate: jest.fn().mockImplementation(() => true),
};

// Mock pour le RolesGuard
const mockRolesGuard = {
  canActivate: jest.fn().mockImplementation(() => true),
};

// Mock pour l'utilisateur authentifié
const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  role: 'USER',
};

describe('Feedback Integration Tests', () => {
  let app: INestApplication;
  let moduleFixture: TestingModule;

  beforeAll(async () => {
    moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .overrideGuard(RolesGuard)
      .useValue(mockRolesGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());

    // Mock pour la requête utilisateur
    app.use((req, res, next) => {
      req.user = mockUser;
      next();
    });

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /recommendations/feedback', () => {
    it('should record a feedback', async () => {
      const feedbackData = {
        recommendationId: 'test-recommendation-id',
        recommendationType: RecommendationType.RETREAT,
        feedbackType: FeedbackType.LIKE,
        comment: 'Great recommendation!',
        rating: 5,
      };

      const response = await request(app.getHttpServer())
        .post('/recommendations/feedback')
        .send(feedbackData)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Feedback enregistré avec succès');
      expect(response.body.data).toBeDefined();
      expect(response.body.data.userId).toBe(mockUser.id);
      expect(response.body.data.recommendationId).toBe(feedbackData.recommendationId);
      expect(response.body.data.recommendationType).toBe(feedbackData.recommendationType);
      expect(response.body.data.feedbackType).toBe(feedbackData.feedbackType);
      expect(response.body.data.comment).toBe(feedbackData.comment);
      expect(response.body.data.rating).toBe(feedbackData.rating);
    });

    it('should return 400 if required fields are missing', async () => {
      const invalidFeedbackData = {
        // Missing recommendationId and recommendationType
        feedbackType: FeedbackType.LIKE,
      };

      await request(app.getHttpServer())
        .post('/recommendations/feedback')
        .send(invalidFeedbackData)
        .expect(400);
    });
  });

  describe('GET /recommendations/feedback/:type/:id', () => {
    it('should get user feedback for a recommendation', async () => {
      // First, create a feedback
      const feedbackData = {
        recommendationId: 'test-recommendation-id-2',
        recommendationType: RecommendationType.COURSE,
        feedbackType: FeedbackType.SAVE,
      };

      await request(app.getHttpServer())
        .post('/recommendations/feedback')
        .send(feedbackData)
        .expect(201);

      // Then, get the feedback
      const response = await request(app.getHttpServer())
        .get(`/recommendations/feedback/${feedbackData.recommendationType}/${feedbackData.recommendationId}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.length).toBeGreaterThan(0);
      
      const feedback = response.body.data.find(
        (f) => f.recommendationId === feedbackData.recommendationId && 
               f.feedbackType === feedbackData.feedbackType
      );
      
      expect(feedback).toBeDefined();
      expect(feedback.userId).toBe(mockUser.id);
    });
  });

  describe('GET /recommendations/feedback/user', () => {
    it('should get all user feedbacks with pagination', async () => {
      // Create multiple feedbacks
      const feedbackData1 = {
        recommendationId: 'test-recommendation-id-3',
        recommendationType: RecommendationType.VIDEO,
        feedbackType: FeedbackType.LIKE,
      };

      const feedbackData2 = {
        recommendationId: 'test-recommendation-id-4',
        recommendationType: RecommendationType.ARTICLE,
        feedbackType: FeedbackType.DISLIKE,
      };

      await request(app.getHttpServer())
        .post('/recommendations/feedback')
        .send(feedbackData1)
        .expect(201);

      await request(app.getHttpServer())
        .post('/recommendations/feedback')
        .send(feedbackData2)
        .expect(201);

      // Get all feedbacks with pagination
      const response = await request(app.getHttpServer())
        .get('/recommendations/feedback/user')
        .query({ page: 1, limit: 10 })
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.data).toBeInstanceOf(Array);
      expect(response.body.data.pagination).toBeDefined();
      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(10);
      expect(response.body.data.pagination.total).toBeGreaterThanOrEqual(2);
    });

    it('should filter feedbacks by type', async () => {
      // Get feedbacks filtered by type
      const response = await request(app.getHttpServer())
        .get('/recommendations/feedback/user')
        .query({ feedbackType: FeedbackType.LIKE })
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.data).toBeInstanceOf(Array);
      
      // All feedbacks should be of type LIKE
      response.body.data.data.forEach((feedback) => {
        expect(feedback.feedbackType).toBe(FeedbackType.LIKE);
      });
    });
  });

  describe('DELETE /recommendations/feedback/:id', () => {
    it('should delete a feedback', async () => {
      // First, create a feedback
      const feedbackData = {
        recommendationId: 'test-recommendation-id-5',
        recommendationType: RecommendationType.EVENT,
        feedbackType: FeedbackType.HIDE,
      };

      const createResponse = await request(app.getHttpServer())
        .post('/recommendations/feedback')
        .send(feedbackData)
        .expect(201);

      const feedbackId = createResponse.body.data.id;

      // Then, delete the feedback
      const deleteResponse = await request(app.getHttpServer())
        .delete(`/recommendations/feedback/${feedbackId}`)
        .expect(200);

      expect(deleteResponse.body).toBeDefined();
      expect(deleteResponse.body.success).toBe(true);
      expect(deleteResponse.body.message).toBe('Feedback supprimé avec succès');

      // Verify that the feedback is deleted
      const getResponse = await request(app.getHttpServer())
        .get(`/recommendations/feedback/${feedbackData.recommendationType}/${feedbackData.recommendationId}`)
        .expect(200);

      const deletedFeedback = getResponse.body.data.find((f) => f.id === feedbackId);
      expect(deletedFeedback).toBeUndefined();
    });

    it('should return 404 if feedback does not exist', async () => {
      await request(app.getHttpServer())
        .delete('/recommendations/feedback/non-existent-id')
        .expect(404);
    });
  });
});
