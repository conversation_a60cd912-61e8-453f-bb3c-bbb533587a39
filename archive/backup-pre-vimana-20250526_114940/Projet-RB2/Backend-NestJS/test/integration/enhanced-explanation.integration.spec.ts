import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { JwtAuthGuard } from '../../src/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../src/modules/auth/guards/roles.guard';
import { RecommendationType } from '../../src/modules/recommendation/enums/recommendation-type.enum';
import { PrismaService } from '../../src/prisma/prisma.service';

// Mock pour le JwtAuthGuard
const mockJwtAuthGuard = {
  canActivate: jest.fn().mockImplementation(() => true),
};

// Mock pour le RolesGuard
const mockRolesGuard = {
  canActivate: jest.fn().mockImplementation(() => true),
};

// Mock pour l'utilisateur authentifié
const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  role: 'USER',
};

// Mock pour le service Prisma
const mockPrismaService = {
  retreat: {
    findUnique: jest.fn().mockImplementation((params) => {
      if (params.where.id === 'test-retreat-id') {
        return Promise.resolve({
          id: 'test-retreat-id',
          title: 'Test Retreat',
          description: 'A test retreat for integration testing',
          location: 'Test Location',
          startDate: new Date(),
          endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          price: 1000,
          capacity: 20,
          categories: ['yoga', 'meditation'],
          tags: ['beginner', 'relaxation'],
        });
      }
      return Promise.resolve(null);
    }),
  },
  course: {
    findUnique: jest.fn().mockImplementation((params) => {
      if (params.where.id === 'test-course-id') {
        return Promise.resolve({
          id: 'test-course-id',
          title: 'Test Course',
          description: 'A test course for integration testing',
          instructor: 'Test Instructor',
          duration: 60,
          level: 'BEGINNER',
          categories: ['yoga', 'fitness'],
          tags: ['online', 'beginner'],
        });
      }
      return Promise.resolve(null);
    }),
  },
  user: {
    findUnique: jest.fn().mockImplementation((params) => {
      if (params.where.id === 'test-user-id') {
        return Promise.resolve({
          id: 'test-user-id',
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
          preferences: {
            categories: ['yoga', 'meditation'],
            tags: ['beginner', 'relaxation'],
          },
        });
      }
      return Promise.resolve(null);
    }),
  },
};

describe('Enhanced Explanation Integration Tests', () => {
  let app: INestApplication;
  let moduleFixture: TestingModule;

  beforeAll(async () => {
    moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .overrideGuard(RolesGuard)
      .useValue(mockRolesGuard)
      .overrideProvider(PrismaService)
      .useValue(mockPrismaService)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());

    // Mock pour la requête utilisateur
    app.use((req, res, next) => {
      req.user = mockUser;
      next();
    });

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('GET /recommendations/explanations/:type/:id', () => {
    it('should get an enhanced explanation for a retreat', async () => {
      const response = await request(app.getHttpServer())
        .get(`/recommendations/explanations/${RecommendationType.RETREAT}/test-retreat-id`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.recommendationId).toBe('test-retreat-id');
      expect(response.body.data.recommendationType).toBe(RecommendationType.RETREAT);
      expect(response.body.data.generalExplanation).toBeDefined();
      expect(response.body.data.factors).toBeDefined();
      expect(response.body.data.factors).toBeInstanceOf(Array);
      expect(response.body.data.factors.length).toBeGreaterThan(0);
    });

    it('should get an enhanced explanation for a course', async () => {
      const response = await request(app.getHttpServer())
        .get(`/recommendations/explanations/${RecommendationType.COURSE}/test-course-id`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.recommendationId).toBe('test-course-id');
      expect(response.body.data.recommendationType).toBe(RecommendationType.COURSE);
      expect(response.body.data.generalExplanation).toBeDefined();
      expect(response.body.data.factors).toBeDefined();
      expect(response.body.data.factors).toBeInstanceOf(Array);
      expect(response.body.data.factors.length).toBeGreaterThan(0);
    });

    it('should include visualizations when requested', async () => {
      const response = await request(app.getHttpServer())
        .get(`/recommendations/explanations/${RecommendationType.RETREAT}/test-retreat-id`)
        .query({ includeVisualizations: 'true' })
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.visualizations).toBeDefined();
      expect(response.body.data.visualizations).toBeInstanceOf(Array);
      expect(response.body.data.visualizations.length).toBeGreaterThan(0);
    });

    it('should adjust detail level based on options', async () => {
      // Test BASIC level
      const basicResponse = await request(app.getHttpServer())
        .get(`/recommendations/explanations/${RecommendationType.RETREAT}/test-retreat-id`)
        .query({ detailLevel: 'BASIC' })
        .expect(200);

      expect(basicResponse.body.data.factors.length).toBeLessThanOrEqual(3);

      // Test DETAILED level
      const detailedResponse = await request(app.getHttpServer())
        .get(`/recommendations/explanations/${RecommendationType.RETREAT}/test-retreat-id`)
        .query({ detailLevel: 'DETAILED' })
        .expect(200);

      expect(detailedResponse.body.data.factors.length).toBeGreaterThanOrEqual(basicResponse.body.data.factors.length);
    });

    it('should return 404 if recommendation does not exist', async () => {
      await request(app.getHttpServer())
        .get(`/recommendations/explanations/${RecommendationType.RETREAT}/non-existent-id`)
        .expect(404);
    });

    it('should include personalized explanation if user profile is available', async () => {
      const response = await request(app.getHttpServer())
        .get(`/recommendations/explanations/${RecommendationType.RETREAT}/test-retreat-id`)
        .expect(200);

      expect(response.body.data.personalizedExplanation).toBeDefined();
      expect(response.body.data.isPersonalized).toBe(true);
    });

    it('should support different languages', async () => {
      // Test French
      const frResponse = await request(app.getHttpServer())
        .get(`/recommendations/explanations/${RecommendationType.RETREAT}/test-retreat-id`)
        .query({ language: 'fr' })
        .expect(200);

      expect(frResponse.body.data.metadata.language).toBe('fr');

      // Test English
      const enResponse = await request(app.getHttpServer())
        .get(`/recommendations/explanations/${RecommendationType.RETREAT}/test-retreat-id`)
        .query({ language: 'en' })
        .expect(200);

      expect(enResponse.body.data.metadata.language).toBe('en');
    });
  });
});
