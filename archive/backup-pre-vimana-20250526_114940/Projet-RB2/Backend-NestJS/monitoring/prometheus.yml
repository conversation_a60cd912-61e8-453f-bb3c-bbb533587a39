global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s

alerting:
  alertmanagers:
    - static_configs:
        - targets: ['alertmanager:9093']

rule_files:
  - "rules/recommendation_alerts.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'recommendation-service'
    metrics_path: '/metrics'
    scrape_interval: 5s
    static_configs:
      - targets: ['recommendation-service:3000']
        labels:
          service: 'recommendation-service'
          environment: 'production'

  - job_name: 'recommendation-service-dev'
    metrics_path: '/metrics'
    scrape_interval: 5s
    static_configs:
      - targets: ['recommendation-service-dev:3000']
        labels:
          service: 'recommendation-service'
          environment: 'development'

  - job_name: 'recommendation-service-staging'
    metrics_path: '/metrics'
    scrape_interval: 5s
    static_configs:
      - targets: ['recommendation-service-staging:3000']
        labels:
          service: 'recommendation-service'
          environment: 'staging'

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
        labels:
          database: 'recommendation-db'
