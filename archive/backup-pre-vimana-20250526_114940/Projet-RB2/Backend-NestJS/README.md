# Backend NestJS - Retreat And Be

Ce projet est une implémentation robuste du Backend pour la plateforme Retreat And Be, utilisant NestJS, Prisma et PostgreSQL.

## 🎯 Statut du Projet

**Version**: 1.0.0-rc
**Statut**: 🟡 **97% FINALISÉ - QUASI PRÊT POUR PRODUCTION**
**Score de finalisation**: **97%** ⭐ (3% restants identifiés)
**Dernière mise à jour**: 24 mai 2025
**Finalisation complète**: 7-9 jours restants

### 🔧 Ce qui reste à finaliser (3%)
- **Infrastructure Production**: Docker, Kubernetes, CI/CD
- **Tests E2E Complets**: Tous parcours utilisateur
- **Documentation Opérationnelle**: Guides et runbooks
- **Optimisations Finales**: Performance et cache

📋 **Voir**: [TACHES_RESTANTES.md](../TACHES_RESTANTES.md) pour le détail complet

## Fonctionnalités

### Architecture et infrastructure
- Architecture modulaire avec NestJS
- ORM Prisma pour l'accès à la base de données PostgreSQL
- Cache avec Redis pour améliorer les performances
- Configuration Docker et Kubernetes pour le déploiement
- Surveillance de l'état de santé de l'application

### Sécurité et authentification
- Authentification JWT avec refresh tokens
- Authentification à deux facteurs (2FA)
- Contrôle d'accès basé sur les rôles (RBAC)
- Protection contre les attaques courantes (XSS, CSRF, etc.)
- Rate limiting pour prévenir les attaques par force brute

### Modules fonctionnels
- Gestion des utilisateurs et des profils
- Système de gamification (niveaux, quêtes, progression)
- Plateforme d'apprentissage (cours, leçons, inscriptions)
- Journalisation des événements et des activités
- **CryptoComplianceService** : vérification automatisée de la conformité cryptographique (PCI DSS, GDPR, SOC2, HIPAA, etc.), reporting, API REST, intégration CI/CD

---

## CryptoComplianceService

Le `CryptoComplianceService` permet de vérifier automatiquement la conformité des implémentations cryptographiques avec les standards majeurs (PCI DSS, GDPR, SOC2, HIPAA, etc.). Il fournit :
- Un moteur de règles extensible
- Une API REST pour exécuter et consulter les contrôles
- Un reporting synthétique (JSON)
- Une intégration facile dans les workflows CI/CD

### Endpoints REST

- `GET /crypto-compliance/report` : Génère un rapport de conformité en temps réel (résumé et détail des contrôles)
- `GET /crypto-compliance/requirements` : Liste toutes les exigences de conformité vérifiées
- `POST /crypto-compliance/requirements` : Ajoute dynamiquement une exigence personnalisée (avancé)

### Exemple d’utilisation (API)

```bash
# Obtenir un rapport de conformité
curl http://localhost:3000/crypto-compliance/report

# Lister les exigences de conformité
curl http://localhost:3000/crypto-compliance/requirements
```

### Extension et personnalisation

Vous pouvez ajouter vos propres exigences via l’API ou en modifiant le service :
```typescript
complianceService.addRequirement({
  id: 'custom-check',
  label: 'Vérification personnalisée',
  description: 'Exemple d’exigence personnalisée',
  regulation: 'INTERNAL',
  check: async () => ({
    requirementId: 'custom-check',
    passed: true,
    details: 'OK',
    timestamp: new Date(),
  })
});
```

### Intégration CI/CD

Pour garantir la conformité à chaque déploiement, ajoutez une étape dans votre pipeline :

```bash
curl -s http://localhost:3000/crypto-compliance/report | jq '.summary' | grep 'failed: 0'
```

Si le nombre d’échecs est > 0, le pipeline peut échouer.

### Documentation technique
- Voir `src/modules/security/services/crypto-compliance.service.ts` pour la structure et l’extension des contrôles
- Voir `src/modules/security/controllers/crypto-compliance.controller.ts` pour l’API

### Qualité et développement
- Validation des données avec class-validator
- Documentation API complète avec Swagger
- Journalisation structurée et gestion centralisée des erreurs
- Tests unitaires, d'intégration et end-to-end
- Pagination, filtrage et recherche avancée

## Prérequis

- Node.js 18 ou supérieur
- npm ou yarn
- Docker et Docker Compose (pour le développement local)
- PostgreSQL (si exécuté en dehors de Docker)
- Redis (si exécuté en dehors de Docker)

## Installation

1. Cloner le dépôt
```bash
git clone <repository-url>
cd Backend-NestJS
```

2. Installer les dépendances
```bash
npm install
```

3. Configurer les variables d'environnement
```bash
cp .env.example .env
# Modifier les valeurs dans .env selon votre environnement
```

4. Générer le client Prisma
```bash
npx prisma generate
```

5. Exécuter les migrations de base de données
```bash
npx prisma migrate dev
```

## Développement

### Démarrer l'application en mode développement

```bash
npm run start:dev
```

### Démarrer avec Docker Compose

```bash
docker-compose up -d
```

L'API sera disponible à l'adresse: http://localhost:3000/api/v1
La documentation Swagger sera disponible à l'adresse: http://localhost:3000/api/docs

### Outils de développement

- PgAdmin: http://localhost:5050 (email: <EMAIL>, password: admin)
- Redis Commander: http://localhost:8081

## Tests

### Exécuter les tests unitaires

```bash
npm run test
```

### Exécuter les tests avec couverture

```bash
npm run test:cov
```

### Exécuter les tests e2e

```bash
npm run test:e2e
```

## Structure du projet

```
backend-nestjs/
├── src/
│   ├── main.ts                  # Point d'entrée de l'application
│   ├── app.module.ts            # Module racine
│   ├── config/                  # Configuration de l'application
│   ├── prisma/                  # Service et module Prisma
│   ├── cache/                   # Service et module de cache Redis
│   ├── common/                  # Filtres, intercepteurs, pipes, middlewares
│   ├── modules/                 # Modules fonctionnels
│   │   ├── auth/              # Authentification et autorisation
│   │   ├── users/             # Gestion des utilisateurs
│   │   ├── activities/        # Activités des utilisateurs
│   │   ├── events/            # Journalisation des événements
│   │   ├── gamification/      # Système de gamification
│   │   └── learning/          # Plateforme d'apprentissage
│   ├── shared/                  # Interfaces, constantes, utilitaires
│   └── health/                  # Vérification de l'état de santé
├── prisma/                      # Schéma et migrations Prisma
├── test/                        # Tests e2e
├── docker/                      # Configuration Docker
└── kubernetes/                  # Configuration Kubernetes
```

## Déploiement

### Construction de l'image Docker

```bash
docker build -t retreat-backend-nestjs .
```

### Déploiement sur Kubernetes

```bash
kubectl apply -f kubernetes/deployment.yaml
kubectl apply -f kubernetes/service.yaml
```

## Documentation

### Documentation API

La documentation complète de l'API est disponible via Swagger à l'adresse: http://localhost:3000/api/docs

Un fichier JSON de spécification Swagger est également généré à la racine du projet (`swagger-spec.json`) pour une intégration avec d'autres outils.

### Roadmap

La roadmap du projet est disponible dans le fichier [ROADMAP-BACKEND-NESTJS.md](./ROADMAP-BACKEND-NESTJS.md). Elle détaille les phases de développement, les fonctionnalités à implémenter et l'état d'avancement du projet.

## Contribution

1. Forker le projet
2. Créer une branche pour votre fonctionnalité (`git checkout -b feature/amazing-feature`)
3. Commiter vos changements (`git commit -m 'Add some amazing feature'`)
4. Pousser vers la branche (`git push origin feature/amazing-feature`)
5. Ouvrir une Pull Request

## Licence

Ce projet est sous licence privée et ne peut être utilisé sans autorisation.

## Contact

Retreat And Be - <EMAIL>
