// Social Platform Models

// Enums for Social Platform
enum LivestreamStatus {
  SCHEDULED
  LIVE
  ENDED
  CANCELLED
}

enum BlogPostStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum CommentType {
  TEXT
  QUESTION
  REACTION
}

enum SocialContentType {
  LIVESTREAM
  BLOG
  VIDEO
}

// Livestream Model
model Livestream {
  id                String           @id @default(uuid())
  title             String
  description       String           @db.Text
  hostId            String
  status            LivestreamStatus @default(SCHEDULED)
  startTime         DateTime
  endTime           DateTime?
  thumbnailUrl      String?
  streamUrl         String?
  recordingUrl      String?
  isPrivate         Boolean          @default(false)
  viewerCount       Int              @default(0)
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt

  // Relations
  host              User             @relation("UserHostedLivestreams", fields: [hostId], references: [id])
  messages          LivestreamMessage[] @relation("LivestreamMessages")
  participants      LivestreamParticipant[] @relation("LivestreamParticipants")
  analytics         SocialAnalytics? @relation("LivestreamAnalytics")

  // Metadata
  metadata          Json?            @default("{}")

  @@index([hostId])
  @@index([status])
  @@index([startTime])
  @@map("livestreams")
}

// Livestream Message Model
model LivestreamMessage {
  id                String      @id @default(uuid())
  content           String
  type              CommentType @default(TEXT)
  userId            String
  livestreamId      String
  createdAt         DateTime    @default(now())

  // Relations
  user              User        @relation("UserLivestreamMessages", fields: [userId], references: [id])
  livestream        Livestream  @relation("LivestreamMessages", fields: [livestreamId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([livestreamId])
  @@index([createdAt])
  @@map("livestream_messages")
}

// Livestream Participant Model
model LivestreamParticipant {
  id                String      @id @default(uuid())
  userId            String
  livestreamId      String
  joinedAt          DateTime    @default(now())
  leftAt            DateTime?
  isActive          Boolean     @default(true)

  // Relations
  user              User        @relation("UserLivestreamParticipations", fields: [userId], references: [id])
  livestream        Livestream  @relation("LivestreamParticipants", fields: [livestreamId], references: [id], onDelete: Cascade)

  @@unique([userId, livestreamId])
  @@index([userId])
  @@index([livestreamId])
  @@map("livestream_participants")
}

// Blog Post Model
model BlogPost {
  id                String          @id @default(uuid())
  title             String
  content           String          @db.Text
  authorId          String
  authorName        String
  status            BlogPostStatus  @default(DRAFT)
  publishDate       DateTime?
  imageUrl          String?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  // Relations
  author            User            @relation("UserBlogPosts", fields: [authorId], references: [id])
  comments          BlogComment[]   @relation("BlogPostComments")
  likes             BlogLike[]      @relation("BlogPostLikes")
  tags              BlogPostTag[]   @relation("BlogPostTags")
  analytics         SocialAnalytics? @relation("BlogPostAnalytics")

  // Metadata
  metadata          Json?           @default("{}")

  @@index([authorId])
  @@index([status])
  @@index([publishDate])
  @@map("blog_posts")
}

// Blog Comment Model
model BlogComment {
  id                String      @id @default(uuid())
  content           String
  userId            String
  blogPostId        String
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  // Relations
  user              User        @relation("UserBlogComments", fields: [userId], references: [id])
  blogPost          BlogPost    @relation("BlogPostComments", fields: [blogPostId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([blogPostId])
  @@index([createdAt])
  @@map("blog_comments")
}

// Blog Like Model
model BlogLike {
  id                String      @id @default(uuid())
  userId            String
  blogPostId        String
  createdAt         DateTime    @default(now())

  // Relations
  user              User        @relation("UserBlogLikes", fields: [userId], references: [id])
  blogPost          BlogPost    @relation("BlogPostLikes", fields: [blogPostId], references: [id], onDelete: Cascade)

  @@unique([userId, blogPostId])
  @@index([userId])
  @@index([blogPostId])
  @@map("blog_likes")
}

// Blog Post Tag Model
model BlogPostTag {
  id                String      @id @default(uuid())
  name              String
  blogPostId        String
  createdAt         DateTime    @default(now())

  // Relations
  blogPost          BlogPost    @relation("BlogPostTags", fields: [blogPostId], references: [id], onDelete: Cascade)

  @@index([blogPostId])
  @@index([name])
  @@map("blog_post_tags")
}

// Social Analytics Model
model SocialAnalytics {
  id                String            @id @default(uuid())
  entityId          String            @unique
  entityType        SocialContentType
  views             Int               @default(0)
  likes             Int               @default(0)
  comments          Int               @default(0)
  shares            Int               @default(0)
  engagementRate    Float             @default(0)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  livestream        Livestream?       @relation("LivestreamAnalytics", fields: [entityId], references: [id], onDelete: Cascade)
  blogPost          BlogPost?         @relation("BlogPostAnalytics", fields: [entityId], references: [id], onDelete: Cascade)

  // Metadata
  metadata          Json?             @default("{}")

  @@index([entityType])
  @@index([views])
  @@index([engagementRate])
  @@map("social_analytics")
}

// Social Event Model (for tracking analytics events)
model SocialEvent {
  id                String            @id @default(uuid())
  eventType         String
  entityId          String
  entityType        SocialContentType
  userId            String?
  timestamp         DateTime          @default(now())
  
  // Relations
  user              User?             @relation("UserSocialEvents", fields: [userId], references: [id])

  // Metadata
  metadata          Json?             @default("{}")

  @@index([eventType])
  @@index([entityId])
  @@index([entityType])
  @@index([userId])
  @@index([timestamp])
  @@map("social_events")
}
