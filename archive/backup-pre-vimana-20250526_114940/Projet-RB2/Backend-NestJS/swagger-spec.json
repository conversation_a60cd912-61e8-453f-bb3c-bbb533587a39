{"openapi": "3.0.0", "paths": {"/api/v1/auth/login": {"post": {"operationId": "AuthController_login", "summary": "Connexion utilisateur", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "Connexion réussie."}, "401": {"description": "Identifiants invalides."}}, "tags": ["auth"]}}, "/api/v1/auth/refresh": {"post": {"operationId": "AuthController_refreshToken", "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> le token d'accès", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}}}, "responses": {"200": {"description": "Token rafraîchi avec succès."}, "401": {"description": "Token de rafraîchissement invalide ou expiré."}}, "tags": ["auth"]}}, "/api/v1/auth/logout": {"post": {"operationId": "AuthController_logout", "summary": "Déconnexion utilisateur", "parameters": [], "responses": {"200": {"description": "Déconnexion réussie."}}, "tags": ["auth"], "security": [{"bearer": []}]}}, "/api/v1/auth/2fa/generate": {"post": {"operationId": "AuthController_generateTwoFactorSecret", "summary": "Générer un secret pour l'authentification à deux facteurs", "parameters": [], "responses": {"201": {"description": "Secret généré avec succès."}}, "tags": ["auth"], "security": [{"bearer": []}]}}, "/api/v1/auth/2fa/enable": {"post": {"operationId": "AuthController_enableTwoFactor", "summary": "Activer l'authentification à deux facteurs", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TwoFactorDto"}}}}, "responses": {"200": {"description": "Authentification à deux facteurs activée avec succès."}, "401": {"description": "Code d'authentification à deux facteurs invalide."}}, "tags": ["auth"], "security": [{"bearer": []}]}}, "/api/v1/auth/2fa/disable": {"post": {"operationId": "AuthController_disableTwoFactor", "summary": "Désactiver l'authentification à deux facteurs", "parameters": [], "responses": {"200": {"description": "Authentification à deux facteurs désactivée avec succès."}}, "tags": ["auth"], "security": [{"bearer": []}]}}, "/api/v1/users": {"post": {"operationId": "UsersController_create", "summary": "Créer un nouvel utilisateur", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"201": {"description": "L'utilisateur a été créé avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "409": {"description": "Un utilisateur avec cet email existe déjà."}}, "tags": ["users"], "security": [{"bearer": []}]}, "get": {"operationId": "UsersController_findAll", "summary": "Récupérer tous les utilisateurs", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des utilisateurs récupérée avec succès."}}, "tags": ["users"], "security": [{"bearer": []}]}}, "/api/v1/users/{id}": {"get": {"operationId": "UsersController_findOne", "summary": "Récupérer un utilisateur par son ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "L'utilisateur a été récupéré avec succès."}, "404": {"description": "Utilisateur non trouvé."}}, "tags": ["users"], "security": [{"bearer": []}]}, "patch": {"operationId": "UsersController_update", "summary": "Mettre à jour un utilisateur", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": "L'utilisateur a été mis à jour avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Utilisateur non trouvé."}}, "tags": ["users"], "security": [{"bearer": []}]}, "delete": {"operationId": "UsersController_remove", "summary": "Supprimer un utilisateur", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "L'utilisateur a été supprimé avec succès."}, "404": {"description": "Utilisateur non trouvé."}}, "tags": ["users"], "security": [{"bearer": []}]}}, "/api/v1/users/profile/me": {"get": {"operationId": "UsersController_getProfile", "summary": "Récupérer le profil de l'utilisateur connecté", "parameters": [], "responses": {"200": {"description": "Profil ré<PERSON> avec succès."}}, "tags": ["users"], "security": [{"bearer": []}]}}, "/api/v1/users/register": {"post": {"operationId": "UsersController_register", "summary": "Enregistrer un nouvel utilisateur", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"201": {"description": "L'utilisateur a été enregistré avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "409": {"description": "Un utilisateur avec cet email existe déjà."}}, "tags": ["users"], "security": [{"bearer": []}]}}, "/api/v1/retreats": {"post": {"operationId": "RetreatsController_create", "summary": "Create a new retreat", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRetreatDto"}}}}, "responses": {"201": {"description": "Retreat created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RetreatResponseDto"}}}}, "400": {"description": "Bad request"}}, "tags": ["retreats"]}, "get": {"operationId": "RetreatsController_findAll", "summary": "Get all retreats", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "category", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "startDate", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "endDate", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "minPrice", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "maxPrice", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "location", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "sortBy", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "sortOrder", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return all retreats", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RetreatResponseDto"}}}}}}, "tags": ["retreats"]}}, "/api/v1/retreats/featured": {"get": {"operationId": "RetreatsController_getFeatured", "summary": "Get featured retreats", "parameters": [], "responses": {"200": {"description": "Return featured retreats", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RetreatResponseDto"}}}}}}, "tags": ["retreats"]}}, "/api/v1/retreats/upcoming": {"get": {"operationId": "RetreatsController_getUpcoming", "summary": "Get upcoming retreats", "parameters": [], "responses": {"200": {"description": "Return upcoming retreats", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RetreatResponseDto"}}}}}}, "tags": ["retreats"]}}, "/api/v1/retreats/categories": {"get": {"operationId": "RetreatsController_getCategories", "summary": "Get retreat categories", "parameters": [], "responses": {"200": {"description": "Return retreat categories"}}, "tags": ["retreats"]}}, "/api/v1/retreats/{id}": {"get": {"operationId": "RetreatsController_findOne", "summary": "Get a retreat by id", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return a retreat", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RetreatResponseDto"}}}}, "404": {"description": "Retreat not found"}}, "tags": ["retreats"]}, "put": {"operationId": "RetreatsController_update", "summary": "Update a retreat", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRetreatDto"}}}}, "responses": {"200": {"description": "Retreat updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RetreatResponseDto"}}}}, "400": {"description": "Bad request"}, "404": {"description": "Retreat not found"}}, "tags": ["retreats"]}, "delete": {"operationId": "RetreatsController_remove", "summary": "Delete a retreat", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "Retreat deleted successfully"}, "404": {"description": "Retreat not found"}}, "tags": ["retreats"]}}, "/api/v1/retreats/{id}/images": {"post": {"operationId": "RetreatsController_uploadImages", "summary": "Upload retreat images", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}}}}, "responses": {"200": {"description": "Images uploaded successfully"}, "400": {"description": "Bad request"}, "404": {"description": "Retreat not found"}}, "tags": ["retreats"]}}, "/api/v1/retreats/{id}/reviews": {"get": {"operationId": "RetreatsController_getReviews", "summary": "Get retreat reviews", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return retreat reviews"}, "404": {"description": "Retreat not found"}}, "tags": ["retreats"]}}, "/api/v1/bookings": {"post": {"operationId": "BookingsController_create", "summary": "Create a new booking", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBookingDto"}}}}, "responses": {"201": {"description": "Booking created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingResponseDto"}}}}, "400": {"description": "Bad request"}}, "tags": ["bookings"], "security": [{"bearer": []}]}, "get": {"operationId": "BookingsController_findAll", "summary": "Get all bookings", "parameters": [{"name": "status", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "retreatId", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "userId", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "startDate", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "endDate", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return all bookings", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BookingResponseDto"}}}}}}, "tags": ["bookings"], "security": [{"bearer": []}]}}, "/api/v1/bookings/user": {"get": {"operationId": "BookingsController_findUserBookings", "summary": "Get bookings for the current user", "parameters": [{"name": "status", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return user bookings", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BookingResponseDto"}}}}}}, "tags": ["bookings"], "security": [{"bearer": []}]}}, "/api/v1/bookings/host": {"get": {"operationId": "BookingsController_findHostBookings", "summary": "Get bookings for retreats hosted by the current user", "parameters": [{"name": "status", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "retreatId", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return host bookings", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BookingResponseDto"}}}}}}, "tags": ["bookings"], "security": [{"bearer": []}]}}, "/api/v1/bookings/{id}": {"get": {"operationId": "BookingsController_findOne", "summary": "Get a booking by id", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return a booking", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingResponseDto"}}}}, "404": {"description": "Booking not found"}}, "tags": ["bookings"], "security": [{"bearer": []}]}, "put": {"operationId": "BookingsController_update", "summary": "Update a booking", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBookingDto"}}}}, "responses": {"200": {"description": "Booking updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingResponseDto"}}}}, "400": {"description": "Bad request"}, "404": {"description": "Booking not found"}}, "tags": ["bookings"], "security": [{"bearer": []}]}, "delete": {"operationId": "BookingsController_remove", "summary": "Delete a booking", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "Booking deleted successfully"}, "404": {"description": "Booking not found"}}, "tags": ["bookings"], "security": [{"bearer": []}]}}, "/api/v1/bookings/{id}/cancel": {"post": {"operationId": "BookingsController_cancelBooking", "summary": "Cancel a booking", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Booking cancelled successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingResponseDto"}}}}, "400": {"description": "Bad request"}, "404": {"description": "Booking not found"}}, "tags": ["bookings"], "security": [{"bearer": []}]}}, "/api/v1/bookings/{id}/confirm": {"post": {"operationId": "BookingsController_confirmBooking", "summary": "Confirm a booking", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Booking confirmed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingResponseDto"}}}}, "400": {"description": "Bad request"}, "404": {"description": "Booking not found"}}, "tags": ["bookings"], "security": [{"bearer": []}]}}, "/api/v1/payments/intent": {"post": {"operationId": "PaymentsController_createPaymentIntent", "summary": "Create a payment intent for a booking", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentIntentDto"}}}}, "responses": {"201": {"description": "Payment intent created successfully."}, "400": {"description": "Invalid request data."}, "404": {"description": "Booking not found."}}, "tags": ["payments"], "security": [{"bearer": []}]}}, "/api/v1/payments/confirm": {"post": {"operationId": "PaymentsController_confirmPayment", "summary": "Confirm a payment", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmPaymentDto"}}}}, "responses": {"200": {"description": "Payment confirmed successfully."}, "400": {"description": "Invalid request data."}}, "tags": ["payments"], "security": [{"bearer": []}]}}, "/api/v1/payments/methods": {"get": {"operationId": "PaymentsController_getPaymentMethods", "summary": "Get payment methods for the current user", "parameters": [], "responses": {"200": {"description": "Payment methods retrieved successfully."}}, "tags": ["payments"], "security": [{"bearer": []}]}, "post": {"operationId": "PaymentsController_addPaymentMethod", "summary": "Add a payment method for the current user", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddPaymentMethodDto"}}}}, "responses": {"201": {"description": "Payment method added successfully."}, "400": {"description": "Invalid request data."}}, "tags": ["payments"], "security": [{"bearer": []}]}}, "/api/v1/payments/methods/{id}": {"delete": {"operationId": "PaymentsController_deletePaymentMethod", "summary": "Delete a payment method", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Payment method ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Payment method deleted successfully."}, "404": {"description": "Payment method not found."}}, "tags": ["payments"], "security": [{"bearer": []}]}}, "/api/v1/payments/history": {"get": {"operationId": "PaymentsController_getPaymentHistory", "summary": "Get payment history for the current user", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Page number", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of items per page", "schema": {"type": "number"}}], "responses": {"200": {"description": "Payment history retrieved successfully."}}, "tags": ["payments"], "security": [{"bearer": []}]}}, "/api/v1/payments/refunds": {"post": {"operationId": "PaymentsController_requestRefund", "summary": "Request a refund for a payment", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequestRefundDto"}}}}, "responses": {"201": {"description": "Refund requested successfully."}, "400": {"description": "Invalid request data."}}, "tags": ["payments"], "security": [{"bearer": []}]}}, "/api/v1/payments/promo-codes/apply": {"post": {"operationId": "PaymentsController_applyPromoCode", "summary": "Apply a promo code to a booking", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplyPromoCodeDto"}}}}, "responses": {"200": {"description": "Promo code applied successfully."}, "400": {"description": "Invalid promo code or booking."}, "404": {"description": "Booking not found."}}, "tags": ["payments"], "security": [{"bearer": []}]}}, "/api/v1/payments/available-methods": {"get": {"operationId": "PaymentsController_getAvailablePaymentMethods", "summary": "Get available payment methods for a country", "parameters": [{"name": "country", "required": true, "in": "query", "description": "ISO country code", "schema": {"type": "string"}}], "responses": {"200": {"description": "Available payment methods retrieved successfully."}}, "tags": ["payments"], "security": [{"bearer": []}]}}, "/api/v1/game-systems": {"post": {"operationId": "GameSystemController_create", "summary": "<PERSON><PERSON>er un nouveau système de jeu", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGameSystemDto"}}}}, "responses": {"201": {"description": "Le système de jeu a été créé avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "409": {"description": "Un système de jeu avec ce nom existe déjà."}}, "tags": ["game-systems"], "security": [{"bearer": []}]}, "get": {"operationId": "GameSystemController_findAll", "summary": "Récupérer tous les systèmes de jeu", "parameters": [{"name": "sortOrder", "required": false, "in": "query", "description": "Ordre de tri", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Champ de tri", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des systèmes de jeu récupérée avec succès."}}, "tags": ["game-systems"], "security": [{"bearer": []}]}}, "/api/v1/game-systems/{id}": {"get": {"operationId": "GameSystemController_findOne", "summary": "Récupérer un système de jeu par son ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Le système de jeu a été récupéré avec succès."}, "404": {"description": "Système de jeu non trouvé."}}, "tags": ["game-systems"], "security": [{"bearer": []}]}, "patch": {"operationId": "GameSystemController_update", "summary": "Mettre à jour un système de jeu", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGameSystemDto"}}}}, "responses": {"200": {"description": "Le système de jeu a été mis à jour avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Système de jeu non trouvé."}, "409": {"description": "Un système de jeu avec ce nom existe déjà."}}, "tags": ["game-systems"], "security": [{"bearer": []}]}, "delete": {"operationId": "GameSystemController_remove", "summary": "Supprimer un système de jeu", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "Le système de jeu a été supprimé avec succès."}, "404": {"description": "Système de jeu non trouvé."}}, "tags": ["game-systems"], "security": [{"bearer": []}]}}, "/api/v1/game-systems/{systemId}/levels": {"post": {"operationId": "GameLevelController_create", "summary": "Créer un nouveau niveau de jeu", "parameters": [{"name": "systemId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGameLevelDto"}}}}, "responses": {"201": {"description": "Le niveau de jeu a été créé avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Système de jeu non trouvé."}, "409": {"description": "Un niveau avec ce numéro existe déjà pour ce système de jeu."}}, "tags": ["game-levels"], "security": [{"bearer": []}]}, "get": {"operationId": "GameLevelController_findAll", "summary": "<PERSON><PERSON><PERSON><PERSON>rer tous les niveaux d'un système de jeu", "parameters": [{"name": "systemId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Ordre de tri", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Champ de tri", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des niveaux de jeu récupérée avec succès."}, "404": {"description": "Système de jeu non trouvé."}}, "tags": ["game-levels"], "security": [{"bearer": []}]}}, "/api/v1/game-systems/{systemId}/levels/{id}": {"get": {"operationId": "GameLevelController_findOne", "summary": "Récupérer un niveau de jeu par son ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Le niveau de jeu a été récupéré avec succès."}, "404": {"description": "Niveau de jeu non trouvé."}}, "tags": ["game-levels"], "security": [{"bearer": []}]}, "patch": {"operationId": "GameLevelController_update", "summary": "Mettre à jour un niveau de jeu", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGameLevelDto"}}}}, "responses": {"200": {"description": "Le niveau de jeu a été mis à jour avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Niveau de jeu non trouvé."}, "409": {"description": "Un niveau avec ce numéro existe déjà pour ce système de jeu."}}, "tags": ["game-levels"], "security": [{"bearer": []}]}, "delete": {"operationId": "GameLevelController_remove", "summary": "Supprimer un niveau de jeu", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "Le niveau de jeu a été supprimé avec succès."}, "404": {"description": "Niveau de jeu non trouvé."}}, "tags": ["game-levels"], "security": [{"bearer": []}]}}, "/api/v1/game-systems/{systemId}/quests": {"post": {"operationId": "QuestController_create", "summary": "<PERSON><PERSON><PERSON> une nouvelle quête", "parameters": [{"name": "systemId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateQuestDto"}}}}, "responses": {"201": {"description": "La quête a été créée avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Système de jeu non trouvé."}, "409": {"description": "Une quête avec ce nom existe déjà pour ce système de jeu."}}, "tags": ["quests"], "security": [{"bearer": []}]}, "get": {"operationId": "QuestController_findAll", "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> toutes les quêtes d'un système de jeu", "parameters": [{"name": "systemId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Ordre de tri", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Champ de tri", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des quêtes récupérée avec succès."}, "404": {"description": "Système de jeu non trouvé."}}, "tags": ["quests"], "security": [{"bearer": []}]}}, "/api/v1/game-systems/{systemId}/quests/{id}": {"get": {"operationId": "QuestController_findOne", "summary": "Récupérer une quête par son ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "La quête a été récupérée avec succès."}, "404": {"description": "Quête non trouvée."}}, "tags": ["quests"], "security": [{"bearer": []}]}, "patch": {"operationId": "QuestController_update", "summary": "Mettre à jour une quête", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateQuestDto"}}}}, "responses": {"200": {"description": "La quête a été mise à jour avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Quête non trouvée."}, "409": {"description": "Une quête avec ce nom existe déjà pour ce système de jeu."}}, "tags": ["quests"], "security": [{"bearer": []}]}, "delete": {"operationId": "QuestController_remove", "summary": "Supprimer une quête", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "La quête a été supprimée avec succès."}, "404": {"description": "Quête non trouvée."}}, "tags": ["quests"], "security": [{"bearer": []}]}}, "/api/v1/game-systems/{systemId}/quests/{id}/status": {"patch": {"operationId": "QuestController_updateStatus", "summary": "Mettre à jour le statut d'une quête", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Le statut de la quête a été mis à jour avec succès."}, "400": {"description": "Statut invalide."}, "404": {"description": "Quête non trouvée."}}, "tags": ["quests"], "security": [{"bearer": []}]}}, "/api/v1/player-progress": {"post": {"operationId": "PlayerProgressController_create", "summary": "<PERSON><PERSON>er une nouvelle progression de joueur", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePlayerProgressDto"}}}}, "responses": {"201": {"description": "La progression du joueur a été créée avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Niveau de jeu non trouvé."}, "409": {"description": "Le joueur a déjà une progression pour ce niveau."}}, "tags": ["player-progress"], "security": [{"bearer": []}]}}, "/api/v1/player-progress/player/{playerId}": {"get": {"operationId": "PlayerProgressController_findAll", "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> toutes les progressions d'un joueur", "parameters": [{"name": "playerId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Ordre de tri", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Champ de tri", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des progressions du joueur récupérée avec succès."}}, "tags": ["player-progress"], "security": [{"bearer": []}]}}, "/api/v1/player-progress/my-progress": {"get": {"operationId": "PlayerProgressController_findMyProgress", "summary": "Récupérer les progressions de l'utilisateur connecté", "parameters": [{"name": "sortOrder", "required": false, "in": "query", "description": "Ordre de tri", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Champ de tri", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des progressions de l'utilisateur récupérée avec succès."}}, "tags": ["player-progress"], "security": [{"bearer": []}]}}, "/api/v1/player-progress/my-level": {"get": {"operationId": "PlayerProgressController_getMyLevel", "summary": "Récupérer le niveau actuel de l'utilisateur connecté", "parameters": [], "responses": {"200": {"description": "Niveau de l'utilisateur récupéré avec succès."}, "404": {"description": "Aucune progression trouvée pour l'utilisateur."}}, "tags": ["player-progress"], "security": [{"bearer": []}]}}, "/api/v1/player-progress/player/{playerId}/level": {"get": {"operationId": "PlayerProgressController_getPlayerLevel", "summary": "Récup<PERSON>rer le niveau d'un joueur", "parameters": [{"name": "playerId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Niveau du joueur récupéré avec succès."}, "404": {"description": "Aucune progression trouvée pour le joueur."}}, "tags": ["player-progress"], "security": [{"bearer": []}]}}, "/api/v1/player-progress/{id}": {"get": {"operationId": "PlayerProgressController_findOne", "summary": "Récupérer une progression de joueur par son ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "La progression du joueur a été récupérée avec succès."}, "404": {"description": "Progression du joueur non trouvée."}}, "tags": ["player-progress"], "security": [{"bearer": []}]}, "patch": {"operationId": "PlayerProgressController_update", "summary": "Mettre à jour une progression de joueur", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePlayerProgressDto"}}}}, "responses": {"200": {"description": "La progression du joueur a été mise à jour avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Progression du joueur non trouvée."}}, "tags": ["player-progress"], "security": [{"bearer": []}]}, "delete": {"operationId": "PlayerProgressController_remove", "summary": "Supprimer une progression de joueur", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "La progression du joueur a été supprimée avec succès."}, "404": {"description": "Progression du joueur non trouvée."}}, "tags": ["player-progress"], "security": [{"bearer": []}]}}, "/api/v1/player-progress/{id}/add-xp": {"patch": {"operationId": "PlayerProgressController_addXp", "summary": "Ajouter des points d'expérience à une progression de joueur", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddXpDto"}}}}, "responses": {"200": {"description": "Les points d'expérience ont été ajoutés avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Progression du joueur non trouvée."}, "409": {"description": "Le XP à ajouter doit être positif."}}, "tags": ["player-progress"], "security": [{"bearer": []}]}}, "/api/v1/courses": {"post": {"operationId": "CourseController_create", "summary": "<PERSON><PERSON>er un nouveau cours", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCourseDto"}}}}, "responses": {"201": {"description": "Le cours a été créé avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "409": {"description": "Un cours avec ce titre existe déjà."}}, "tags": ["courses"], "security": [{"bearer": []}]}, "get": {"operationId": "CourseController_findAll", "summary": "R<PERSON><PERSON><PERSON><PERSON> tous les cours", "parameters": [{"name": "sortOrder", "required": false, "in": "query", "description": "Ordre de tri", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Champ de tri", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des cours récupérée avec succès."}}, "tags": ["courses"], "security": [{"bearer": []}]}}, "/api/v1/courses/search": {"get": {"operationId": "CourseController_search", "summary": "Rechercher des cours", "parameters": [{"name": "query", "required": true, "in": "query", "description": "Terme de recherche", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Résultats de recherche récupérés avec succès."}}, "tags": ["courses"], "security": [{"bearer": []}]}}, "/api/v1/courses/category/{category}": {"get": {"operationId": "CourseController_findByCategory", "summary": "Récupérer les cours par catégorie", "parameters": [{"name": "category", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des cours récupérée avec succès."}}, "tags": ["courses"], "security": [{"bearer": []}]}}, "/api/v1/courses/level/{level}": {"get": {"operationId": "CourseController_findByLevel", "summary": "Récupérer les cours par niveau", "parameters": [{"name": "level", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des cours récupérée avec succès."}}, "tags": ["courses"], "security": [{"bearer": []}]}}, "/api/v1/courses/{id}": {"get": {"operationId": "CourseController_findOne", "summary": "Récupérer un cours par son ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Le cours a été récupéré avec succès."}, "404": {"description": "Cours non trouvé."}}, "tags": ["courses"], "security": [{"bearer": []}]}, "patch": {"operationId": "CourseController_update", "summary": "Mettre à jour un cours", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCourseDto"}}}}, "responses": {"200": {"description": "Le cours a été mis à jour avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Cours non trouvé."}, "409": {"description": "Un cours avec ce titre existe déjà."}}, "tags": ["courses"], "security": [{"bearer": []}]}, "delete": {"operationId": "CourseController_remove", "summary": "Supprimer un cours", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "Le cours a été supprimé avec succès."}, "404": {"description": "Cours non trouvé."}}, "tags": ["courses"], "security": [{"bearer": []}]}}, "/api/v1/courses/{courseId}/lessons": {"post": {"operationId": "LessonController_create", "summary": "<PERSON><PERSON>er une nouvelle leçon", "parameters": [{"name": "courseId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateLessonDto"}}}}, "responses": {"201": {"description": "La leçon a été créée avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Cours non trouvé."}, "409": {"description": "Une leçon avec cet ordre existe déjà pour ce cours."}}, "tags": ["lessons"], "security": [{"bearer": []}]}, "get": {"operationId": "LessonController_findAll", "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> toutes les leçons d'un cours", "parameters": [{"name": "courseId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Ordre de tri", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Champ de tri", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des leçons récupérée avec succès."}, "404": {"description": "Cours non trouvé."}}, "tags": ["lessons"], "security": [{"bearer": []}]}}, "/api/v1/courses/{courseId}/lessons/{id}": {"get": {"operationId": "LessonController_findOne", "summary": "Récupérer une leçon par son ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "La leçon a été récupérée avec succès."}, "404": {"description": "Leçon non trouvée."}}, "tags": ["lessons"], "security": [{"bearer": []}]}, "patch": {"operationId": "LessonController_update", "summary": "Mettre à jour une leçon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLessonDto"}}}}, "responses": {"200": {"description": "La leçon a été mise à jour avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Leçon non trouvée."}, "409": {"description": "Une leçon avec cet ordre existe déjà pour ce cours."}}, "tags": ["lessons"], "security": [{"bearer": []}]}, "delete": {"operationId": "LessonController_remove", "summary": "Supprimer une leçon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "La leçon a été supprimée avec succès."}, "404": {"description": "Leçon non trouvée."}}, "tags": ["lessons"], "security": [{"bearer": []}]}}, "/api/v1/courses/{courseId}/lessons/reorder": {"post": {"operationId": "LessonController_reorderLessons", "summary": "Réorganiser les leçons d'un cours", "parameters": [{"name": "courseId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReorderLessonsDto"}}}}, "responses": {"200": {"description": "Les leçons ont été réorganisées avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Cours non trouvé."}, "409": {"description": "Tous les IDs de leçon du cours doivent être présents pour la réorganisation."}}, "tags": ["lessons"], "security": [{"bearer": []}]}}, "/api/v1/enrollments": {"post": {"operationId": "EnrollmentController_create", "summary": "<PERSON><PERSON>er une nouvelle inscription", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEnrollmentDto"}}}}, "responses": {"201": {"description": "L'inscription a été créée avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Cours ou utilisateur non trouvé."}, "409": {"description": "L'utilisateur est déjà inscrit à ce cours."}}, "tags": ["enrollments"], "security": [{"bearer": []}]}, "get": {"operationId": "EnrollmentController_findAll", "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> toutes les inscriptions", "parameters": [{"name": "sortOrder", "required": false, "in": "query", "description": "Ordre de tri", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Champ de tri", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des inscriptions récupérée avec succès."}}, "tags": ["enrollments"], "security": [{"bearer": []}]}}, "/api/v1/enrollments/enroll-me/{courseId}": {"post": {"operationId": "EnrollmentController_enrollMe", "summary": "Inscrire l'utilisateur connecté à un cours", "parameters": [{"name": "courseId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": "L'inscription a été créée avec succès."}, "404": {"description": "Cours non trouvé."}, "409": {"description": "L'utilisateur est déjà inscrit à ce cours."}}, "tags": ["enrollments"], "security": [{"bearer": []}]}}, "/api/v1/enrollments/my-enrollments": {"get": {"operationId": "EnrollmentController_findMyEnrollments", "summary": "Récupérer les inscriptions de l'utilisateur connecté", "parameters": [{"name": "sortOrder", "required": false, "in": "query", "description": "Ordre de tri", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Champ de tri", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des inscriptions récupérée avec succès."}}, "tags": ["enrollments"], "security": [{"bearer": []}]}}, "/api/v1/enrollments/user/{userId}": {"get": {"operationId": "EnrollmentController_findByUser", "summary": "Récupérer les inscriptions d'un utilisateur", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Ordre de tri", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Champ de tri", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des inscriptions récupérée avec succès."}, "404": {"description": "Utilisateur non trouvé."}}, "tags": ["enrollments"], "security": [{"bearer": []}]}}, "/api/v1/enrollments/course/{courseId}": {"get": {"operationId": "EnrollmentController_findByCourse", "summary": "Récupérer les inscriptions à un cours", "parameters": [{"name": "courseId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Ordre de tri", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Champ de tri", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des inscriptions récupérée avec succès."}, "404": {"description": "Cours non trouvé."}}, "tags": ["enrollments"], "security": [{"bearer": []}]}}, "/api/v1/enrollments/course/{courseId}/stats": {"get": {"operationId": "EnrollmentController_getEnrollmentStats", "summary": "Récupérer les statistiques d'inscription à un cours", "parameters": [{"name": "courseId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Statistiques récupérées avec succès."}, "404": {"description": "Cours non trouvé."}}, "tags": ["enrollments"], "security": [{"bearer": []}]}}, "/api/v1/enrollments/{id}": {"get": {"operationId": "EnrollmentController_findOne", "summary": "Récupérer une inscription par son ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "L'inscription a été récupérée avec succès."}, "404": {"description": "Inscription non trouvée."}}, "tags": ["enrollments"], "security": [{"bearer": []}]}, "patch": {"operationId": "EnrollmentController_update", "summary": "Mettre à jour une inscription", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEnrollmentDto"}}}}, "responses": {"200": {"description": "L'inscription a été mise à jour avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Inscription non trouvée."}}, "tags": ["enrollments"], "security": [{"bearer": []}]}, "delete": {"operationId": "EnrollmentController_remove", "summary": "Supprimer une inscription", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "L'inscription a été supprimée avec succès."}, "404": {"description": "Inscription non trouvée."}}, "tags": ["enrollments"], "security": [{"bearer": []}]}}, "/api/v1/enrollments/{id}/progress": {"patch": {"operationId": "EnrollmentController_updateProgress", "summary": "Mettre à jour la progression d'une inscription", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProgressDto"}}}}, "responses": {"200": {"description": "La progression a été mise à jour avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Inscription non trouvée."}, "409": {"description": "La progression doit être comprise entre 0 et 100."}}, "tags": ["enrollments"], "security": [{"bearer": []}]}}, "/api/v1/events": {"post": {"operationId": "EventsController_create", "summary": "Créer un nouvel événement", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEventDto"}}}}, "responses": {"201": {"description": "L'événement a été créé avec succès."}, "400": {"description": "Donn<PERSON> invalides."}}, "tags": ["events"], "security": [{"bearer": []}]}, "get": {"operationId": "EventsController_findAll", "summary": "Récupérer tous les événements", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "eventType", "required": false, "in": "query", "description": "Type d'événement", "schema": {"type": "string"}}], "responses": {"200": {"description": "Liste des événements récupérée avec succès."}}, "tags": ["events"], "security": [{"bearer": []}]}}, "/api/v1/events/{id}": {"get": {"operationId": "EventsController_findOne", "summary": "Récupérer un événement par son ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "L'événement a été récupéré avec succès."}, "404": {"description": "Événement non trouvé."}}, "tags": ["events"], "security": [{"bearer": []}]}, "delete": {"operationId": "EventsController_remove", "summary": "Supprimer un événement", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "L'événement a été supprimé avec succès."}, "404": {"description": "Événement non trouvé."}}, "tags": ["events"], "security": [{"bearer": []}]}}, "/api/v1/events/user/{userId}": {"get": {"operationId": "EventsController_findByUserId", "summary": "Récupérer les événements d'un utilisateur", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des événements récupérée avec succès."}}, "tags": ["events"], "security": [{"bearer": []}]}}, "/api/v1/events/{id}/status": {"patch": {"operationId": "EventsController_updateStatus", "summary": "Mettre à jour le statut d'un événement", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Le statut de l'événement a été mis à jour avec succès."}, "404": {"description": "Événement non trouvé."}}, "tags": ["events"], "security": [{"bearer": []}]}}, "/api/v1/notifications": {"post": {"operationId": "NotificationsController_create", "summary": "<PERSON><PERSON>er une nouvelle notification", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNotificationDto"}}}}, "responses": {"201": {"description": "La notification a été créée avec succès."}, "400": {"description": "Donn<PERSON> invalides."}}, "tags": ["notifications"], "security": [{"bearer": []}]}, "get": {"operationId": "NotificationsController_findAll", "summary": "Récupérer les notifications de l'utilisateur connecté", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des notifications récupérée avec succès."}}, "tags": ["notifications"], "security": [{"bearer": []}]}}, "/api/v1/notifications/{id}": {"get": {"operationId": "NotificationsController_findOne", "summary": "Récupérer une notification par son ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "La notification a été récupérée avec succès."}, "404": {"description": "Notification non trouvée."}}, "tags": ["notifications"], "security": [{"bearer": []}]}, "delete": {"operationId": "NotificationsController_remove", "summary": "Supprimer une notification", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "La notification a été supprimée avec succès."}, "404": {"description": "Notification non trouvée."}}, "tags": ["notifications"], "security": [{"bearer": []}]}}, "/api/v1/notifications/{id}/read": {"patch": {"operationId": "NotificationsController_markAsRead", "summary": "Marquer une notification comme lue", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "La notification a été marquée comme lue avec succès."}, "404": {"description": "Notification non trouvée."}}, "tags": ["notifications"], "security": [{"bearer": []}]}}, "/api/v1/notifications/read-all": {"patch": {"operationId": "NotificationsController_markAllAsRead", "summary": "<PERSON><PERSON> toutes les notifications comme lues", "parameters": [], "responses": {"200": {"description": "Les notifications ont été marquées comme lues avec succès."}}, "tags": ["notifications"], "security": [{"bearer": []}]}}, "/api/v1/notifications/resend-failed": {"post": {"operationId": "NotificationsController_resendFailedNotifications", "summary": "Renvoyer les notifications échouées", "parameters": [], "responses": {"200": {"description": "Les notifications échouées ont été renvoyées avec succès."}}, "tags": ["notifications"], "security": [{"bearer": []}]}}, "/api/v1/security/health": {"get": {"operationId": "SecurityController_getHealthStatus", "summary": "Get security service health status", "parameters": [], "responses": {"200": {"description": "Returns the health status of the security service"}}, "tags": ["security"]}}, "/api/v1/security/dashboard": {"get": {"operationId": "SecurityController_getSecurityDashboard", "summary": "Get security dashboard data", "parameters": [{"name": "timeframe", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns security dashboard data"}}, "tags": ["security"], "security": [{"bearer": []}]}}, "/api/v1/security/events": {"get": {"operationId": "SecurityController_getSecurityEvents", "summary": "Get security events", "parameters": [{"name": "type", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "severity", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns security events"}}, "tags": ["security"], "security": [{"bearer": []}]}}, "/api/v1/security/alerts": {"get": {"operationId": "SecurityController_getSecurityAlerts", "summary": "Get security alerts", "parameters": [{"name": "status", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "priority", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns security alerts"}}, "tags": ["security"], "security": [{"bearer": []}]}}, "/api/v1/security/alerts/{id}/acknowledge": {"post": {"operationId": "SecurityController_acknowledgeAlert", "summary": "Acknowledge a security alert", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON> acknowledged successfully"}}, "tags": ["security"], "security": [{"bearer": []}]}}, "/api/v1/security/alerts/{id}/resolve": {"post": {"operationId": "SecurityController_resolve<PERSON>lert", "summary": "Resolve a security alert", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON> resolved successfully"}}, "tags": ["security"], "security": [{"bearer": []}]}}, "/api/v1/security/training/simulations": {"get": {"operationId": "SecurityController_getTrainingSimulations", "summary": "Get security training simulations", "parameters": [], "responses": {"200": {"description": "Returns security training simulations"}}, "tags": ["security"], "security": [{"bearer": []}]}}, "/api/v1/security/training/simulations/{id}/start": {"post": {"operationId": "SecurityController_startTrainingSimulation", "summary": "Start a security training simulation", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Simulation started successfully"}}, "tags": ["security"], "security": [{"bearer": []}]}}, "/api/v1/security/training/simulations/{id}/complete": {"post": {"operationId": "SecurityController_completeTrainingSimulation", "summary": "Complete a security training simulation", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Simulation completed successfully"}}, "tags": ["security"], "security": [{"bearer": []}]}}, "/api/v1/security/training/stats": {"get": {"operationId": "SecurityController_getUserTrainingStats", "summary": "Get user security training stats", "parameters": [], "responses": {"200": {"description": "Returns user security training stats"}}, "tags": ["security"], "security": [{"bearer": []}]}}, "/api/v1/encryption/keys": {"get": {"operationId": "EncryptionController_listActiveKeys", "summary": "Liste les clés actives", "parameters": [], "responses": {"200": {"description": "Liste des clés actives"}}, "tags": ["encryption"], "security": [{"bearer": []}]}}, "/api/v1/encryption/keys/rotate/{keyId}": {"post": {"operationId": "EncryptionController_<PERSON><PERSON>ey", "summary": "Effectue la rotation d'une clé", "parameters": [{"name": "keyId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Clé rotée avec succès"}}, "tags": ["encryption"], "security": [{"bearer": []}]}}, "/api/v1/encryption/vault/status": {"get": {"operationId": "EncryptionController_getVaultStatus", "summary": "V<PERSON>rifie le statut de l'intégration avec Vault", "parameters": [], "responses": {"200": {"description": "Statut de l'intégration avec Vault"}}, "tags": ["encryption"], "security": [{"bearer": []}]}}, "/api/v1/encryption/keys/create": {"post": {"operationId": "EncryptionController_create<PERSON>ey", "summary": "Crée une nouvelle clé de chiffrement", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "example": "financial"}, "algorithm": {"type": "string", "example": "aes-256-gcm"}, "purpose": {"type": "string", "enum": ["encryption", "signing", "hmac", "general"], "example": "encryption"}, "rotationInterval": {"type": "number", "example": 604800000}, "autoRotate": {"type": "boolean", "example": true}}, "required": ["name", "algorithm", "purpose"]}}}}, "responses": {"201": {"description": "Clé créée avec succès"}}, "tags": ["encryption"], "security": [{"bearer": []}]}}, "/api/v1/encryption/e2e/generate-keypair": {"post": {"operationId": "EncryptionController_generateE2EKeyPair", "summary": "Génère une paire de clés pour le chiffrement de bout en bout", "parameters": [], "responses": {"201": {"description": "Paire de clés générée avec succès"}}, "tags": ["encryption"], "security": [{"bearer": []}]}}, "/api/v1/encryption/e2e/encrypt": {"post": {"operationId": "EncryptionController_encryptMessage", "summary": "Chiffre un message avec une clé publique", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "publicKey": {"type": "string"}}}}}}, "responses": {"200": {"description": "Message chiffré avec succès"}}, "tags": ["encryption"], "security": [{"bearer": []}]}}, "/api/v1/encryption/e2e/decrypt": {"post": {"operationId": "EncryptionController_decryptMessage", "summary": "Déchiffre un message avec une clé privée", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"encryptedMessage": {"type": "string"}, "privateKey": {"type": "string"}}}}}}, "responses": {"200": {"description": "Message déchiffré avec succès"}}, "tags": ["encryption"], "security": [{"bearer": []}]}}, "/api/v1/encryption/certificates/generate": {"post": {"operationId": "EncryptionController_generateCertificates", "summary": "Génère des certificats pour un service", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"serviceName": {"type": "string"}}}}}}, "responses": {"201": {"description": "Certificats générés avec succès"}}, "tags": ["encryption"], "security": [{"bearer": []}]}}, "/api/v1/encryption/certificates/download/{serviceName}/{fileType}": {"get": {"operationId": "EncryptionController_downloadCertificate", "summary": "Télécharge un certificat ou une clé", "parameters": [{"name": "serviceName", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "fileType", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> téléchargé avec succès"}}, "tags": ["encryption"], "security": [{"bearer": []}]}}, "/api/v1/encryption/microservice/public-key": {"get": {"operationId": "EncryptionController_getPublic<PERSON>ey", "summary": "Récupère la clé publique de ce service", "parameters": [], "responses": {"200": {"description": "Clé publique récupérée avec succès"}}, "tags": ["encryption"], "security": [{"bearer": []}]}}, "/api/v1/encryption/microservice/register-key": {"post": {"operationId": "EncryptionController_registerServiceKey", "summary": "Enregistre la clé publique d'un service", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"serviceId": {"type": "string"}, "publicKey": {"type": "string"}}}}}}, "responses": {"201": {"description": "Clé publique enregistrée avec succès"}}, "tags": ["encryption"], "security": [{"bearer": []}]}}, "/api/v1/crypto-testing/run-tests": {"get": {"operationId": "CryptoTestingController_runTests", "summary": "Exécute tous les tests cryptographiques", "parameters": [], "responses": {"200": {"description": "Tests exécutés avec succès"}}, "tags": ["crypto-testing"], "security": [{"bearer": []}]}}, "/api/v1/crypto-testing/run-performance-tests": {"post": {"operationId": "CryptoTestingController_runPerformanceTests", "summary": "Exécute des tests de performance cryptographique", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"iterations": {"type": "number", "default": 100}, "dataSize": {"type": "number", "default": 1024}}}}}}, "responses": {"200": {"description": "Tests de performance exécutés avec succès"}}, "tags": ["crypto-testing"], "security": [{"bearer": []}]}}, "/api/v1/crypto-testing/performance-metrics": {"get": {"operationId": "CryptoTestingController_getPerformanceMetrics", "summary": "Récupère les métriques de performance cryptographique", "parameters": [], "responses": {"200": {"description": "Métriques de performance récupérées avec succès"}}, "tags": ["crypto-testing"], "security": [{"bearer": []}]}}, "/api/v1/crypto-testing/performance-report": {"get": {"operationId": "CryptoTestingController_generatePerformanceReport", "summary": "Génère un rapport de performance cryptographique", "parameters": [], "responses": {"200": {"description": "Rapport de performance généré avec succès"}}, "tags": ["crypto-testing"], "security": [{"bearer": []}]}}, "/api/v1/crypto-testing/verify-log-integrity": {"get": {"operationId": "CryptoTestingController_verifyLogIntegrity", "summary": "Vérifie l'intégrité des journaux cryptographiques", "parameters": [], "responses": {"200": {"description": "Intégrité des journaux vérifiée avec succès"}}, "tags": ["crypto-testing"], "security": [{"bearer": []}]}}, "/api/v1/crypto-testing/optimize": {"post": {"operationId": "CryptoTestingController_optimizeOperation", "summary": "Exécute une opération cryptographique avec optimisation", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"operation": {"type": "string", "enum": ["encrypt", "decrypt", "sign", "verify"]}, "algorithm": {"type": "string"}, "data": {"type": "string"}, "iterations": {"type": "number", "default": 1}}, "required": ["operation", "data"]}}}}, "responses": {"200": {"description": "Opération exécutée avec succès"}}, "tags": ["crypto-testing"], "security": [{"bearer": []}]}}, "/api/v1/security/certificates/generate/{serviceName}": {"post": {"operationId": "CertificateController_generateCertificate", "summary": "Génère un certificat pour un service", "parameters": [{"name": "serviceName", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": "Certificat généré avec succès"}}, "tags": ["security/certificates"], "security": [{"bearer": []}]}}, "/api/v1/security/certificates/health": {"get": {"operationId": "CertificateController_checkCertificateHealth", "summary": "Vérifie l'état de santé des certificats", "parameters": [], "responses": {"200": {"description": "État de santé des certificats"}}, "tags": ["security/certificates"], "security": [{"bearer": []}]}}, "/api/v1/security/certificates/rotate": {"post": {"operationId": "CertificateController_rotateCertificates", "summary": "Effectue la rotation des certificats", "parameters": [], "responses": {"200": {"description": "Rotation des certificats effectuée avec succès"}}, "tags": ["security/certificates"], "security": [{"bearer": []}]}}, "/api/v1/security/certificates/backups": {"get": {"operationId": "CertificateController_listCertificateBackups", "summary": "Liste les sauvegardes de certificats disponibles", "parameters": [], "responses": {"200": {"description": "Liste des sauvegardes de certificats"}}, "tags": ["security/certificates"], "security": [{"bearer": []}]}}, "/api/v1/security/certificates/restore/{backupId}": {"post": {"operationId": "CertificateController_restoreCertificatesFromBackup", "summary": "Restaure des certificats à partir d'une sauvegarde", "parameters": [{"name": "backupId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Certificats restaurés avec succès"}}, "tags": ["security/certificates"], "security": [{"bearer": []}]}}, "/api/v1/security/certificates/monitoring/metrics": {"get": {"operationId": "CertificateMonitoringController_getMetrics", "summary": "Obtient les métriques des certificats", "parameters": [], "responses": {"200": {"description": "Métriques des certificats"}}, "tags": ["security/certificates/monitoring"], "security": [{"bearer": []}]}}, "/api/v1/security/certificates/monitoring/certificates": {"get": {"operationId": "CertificateMonitoringController_getAllCertificatesInfo", "summary": "Obtient les informations détaillées de tous les certificats", "parameters": [], "responses": {"200": {"description": "Informations détaillées des certificats"}}, "tags": ["security/certificates/monitoring"], "security": [{"bearer": []}]}}, "/api/v1/security/certificates/monitoring/certificates/{service}": {"get": {"operationId": "CertificateMonitoringController_getCertificateInfo", "summary": "Obtient les informations détaillées d'un certificat", "parameters": [{"name": "service", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Informations détaillées du certificat"}, "404": {"description": "Certificat non trouvé"}}, "tags": ["security/certificates/monitoring"], "security": [{"bearer": []}]}}, "/api/v1/security/certificates/monitoring/expiring": {"get": {"operationId": "CertificateMonitoringController_getExpiringCertificates", "summary": "Obtient la liste des certificats expirant bientôt", "parameters": [{"name": "thresholdDays", "required": false, "in": "query", "description": "Nombre de jours avant expiration", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des certificats expirant bientôt"}}, "tags": ["security/certificates/monitoring"], "security": [{"bearer": []}]}}, "/api/v1/security/certificates/monitoring/expired": {"get": {"operationId": "CertificateMonitoringController_getExpiredCertificates", "summary": "Obtient la liste des certificats expirés", "parameters": [], "responses": {"200": {"description": "Liste des certificats expirés"}}, "tags": ["security/certificates/monitoring"], "security": [{"bearer": []}]}}, "/api/v1/security/certificates/monitoring/revoked": {"get": {"operationId": "CertificateMonitoringController_getRevokedCertificates", "summary": "Obtient la liste des certificats révoqués", "parameters": [], "responses": {"200": {"description": "Liste des certificats révoqués"}}, "tags": ["security/certificates/monitoring"], "security": [{"bearer": []}]}}, "/api/v1/security/certificates/monitoring/revoke/{service}": {"post": {"operationId": "CertificateMonitoringController_revokeCertificate", "summary": "Révoque un certificat", "parameters": [{"name": "service", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RevokeCertificateDto"}}}}, "responses": {"200": {"description": "Certificat révoqué avec succès"}, "404": {"description": "Certificat non trouvé"}}, "tags": ["security/certificates/monitoring"], "security": [{"bearer": []}]}}, "/api/v1/security/certificates/monitoring/crl": {"get": {"operationId": "CertificateMonitoringController_getCRL", "summary": "Obtient la liste de révocation de certificats (CRL)", "parameters": [], "responses": {"200": {"description": "Liste de révocation de certificats"}}, "tags": ["security/certificates/monitoring"], "security": [{"bearer": []}]}}, "/api/v1/security/certificates/monitoring/crl/generate": {"post": {"operationId": "CertificateMonitoringController_generateCRL", "summary": "Génère une nouvelle liste de révocation de certificats (CRL)", "parameters": [], "responses": {"200": {"description": "Liste de révocation de certificats générée avec succès"}}, "tags": ["security/certificates/monitoring"], "security": [{"bearer": []}]}}, "/api/v1/security/certificates/monitoring/scan": {"post": {"operationId": "CertificateMonitoringController_triggerCertificateScan", "summary": "Déclenche un scan des certificats", "parameters": [], "responses": {"200": {"description": "Scan des certificats déclenché avec succès"}}, "tags": ["security/certificates/monitoring"], "security": [{"bearer": []}]}}, "/api/v1/api/security/microservice/health": {"get": {"operationId": "MicroserviceSecurityController_checkHealth", "summary": "Vérifie l'état de santé du service de sécurité des microservices", "parameters": [], "responses": {"200": {"description": "État de santé du service"}}, "tags": ["microservice-security"]}}, "/api/v1/api/security/microservice/keys/rotate": {"post": {"operationId": "MicroserviceSecurityController_rotateKeys", "summary": "Force la rotation des clés de service", "parameters": [], "responses": {"200": {"description": "Clés rotées avec succès"}}, "tags": ["microservice-security"]}}, "/api/v1/api/security/microservice/keys/public": {"get": {"operationId": "MicroserviceSecurityController_getPublicKey", "summary": "Récupère la clé publique du service", "parameters": [], "responses": {"200": {"description": "Clé publique du service"}}, "tags": ["microservice-security"]}}, "/api/v1/api/security/microservice/certificates/validate": {"post": {"operationId": "MicroserviceSecurityController_validateCertificates", "summary": "Valide les certificats du service", "parameters": [], "responses": {"200": {"description": "Résultat de la validation des certificats"}}, "tags": ["microservice-security"]}}, "/api/v1/api/security/sensitive-data/encrypt": {"post": {"operationId": "SensitiveDataEncryptionController_encryptData", "summary": "<PERSON><PERSON>re des données sensibles", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "description": "<PERSON>n<PERSON> à chiffrer"}, "type": {"type": "string", "description": "Type de données sensibles"}}, "required": ["data"]}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chiff<PERSON>es avec succès"}}, "tags": ["sensitive-data-encryption"]}}, "/api/v1/api/security/sensitive-data/decrypt": {"post": {"operationId": "SensitiveDataEncryptionController_decryptData", "summary": "Déchiffre des données sensibles", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"encryptedData": {"type": "object", "properties": {"data": {"type": "string"}, "iv": {"type": "string"}, "authTag": {"type": "string"}, "keyId": {"type": "string"}, "algorithm": {"type": "string"}, "version": {"type": "number"}}, "required": ["data", "iv", "authTag", "keyId", "algorithm", "version"]}}, "required": ["encryptedData"]}}}}, "responses": {"200": {"description": "Don<PERSON><PERSON> avec succès"}}, "tags": ["sensitive-data-encryption"]}}, "/api/v1/api/security/sensitive-data/encrypt-fields": {"post": {"operationId": "SensitiveDataEncryptionController_encryptFields", "summary": "<PERSON><PERSON>re les champs sensibles d'un objet", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "description": "Objet contenant des champs sensibles"}}, "required": ["data"]}}}}, "responses": {"200": {"description": "Champs sensibles chiffrés avec succès"}}, "tags": ["sensitive-data-encryption"]}}, "/api/v1/api/security/sensitive-data/decrypt-fields": {"post": {"operationId": "SensitiveDataEncryptionController_decryptFields", "summary": "Déchiffre les champs sensibles d'un objet", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "description": "Objet contenant des champs chiffrés"}}, "required": ["data"]}}}}, "responses": {"200": {"description": "Champs sensibles déchiffrés avec succès"}}, "tags": ["sensitive-data-encryption"]}}, "/api/v1/api/security/sensitive-data/identify-fields": {"post": {"operationId": "SensitiveDataEncryptionController_identifySensitiveFields", "summary": "Identifie les champs sensibles dans un objet", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "description": "Objet à analyser"}}, "required": ["data"]}}}}, "responses": {"200": {"description": "Champs sensibles identifiés avec succès"}}, "tags": ["sensitive-data-encryption"]}}, "/api/v1/api/security/sensitive-data/sensitive-fields": {"get": {"operationId": "SensitiveDataEncryptionController_getSensitiveFields", "summary": "Obtient la liste des champs sensibles configurés", "parameters": [], "responses": {"200": {"description": "Liste des champs sensibles"}}, "tags": ["sensitive-data-encryption"]}}, "/api/v1/api/security/sensitive-data/sensitive-types": {"get": {"operationId": "SensitiveDataEncryptionController_getSensitiveTypes", "summary": "Obtient la liste des types de données sensibles configurés", "parameters": [], "responses": {"200": {"description": "Liste des types de données sensibles"}}, "tags": ["sensitive-data-encryption"]}}, "/api/v1/api/security/sensitive-data/add-sensitive-field": {"post": {"operationId": "SensitiveDataEncryptionController_addSensitiveField", "summary": "Ajoute un champ sensible à la configuration", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"fieldName": {"type": "string", "description": "Nom du champ sensible"}}, "required": ["fieldName"]}}}}, "responses": {"200": {"description": "<PERSON>mp <PERSON> ajouté avec succès"}}, "tags": ["sensitive-data-encryption"]}}, "/api/v1/crypto-compliance/report": {"get": {"operationId": "CryptoComplianceController_getComplianceReport", "parameters": [], "responses": {"200": {"description": ""}}}}, "/api/v1/crypto-compliance/report/markdown": {"get": {"operationId": "CryptoComplianceController_getMarkdownReport", "parameters": [], "responses": {"200": {"description": ""}}}}, "/api/v1/crypto-compliance/requirements": {"get": {"operationId": "CryptoComplianceController_getRequirements", "parameters": [], "responses": {"200": {"description": ""}}}, "post": {"operationId": "CryptoComplianceController_addRequirement", "parameters": [], "responses": {"201": {"description": ""}}}}, "/api/v1/security/files/upload": {"post": {"operationId": "FileSecurityController_uploadFile", "summary": "Upload a file securely", "parameters": [], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"201": {"description": "File uploaded successfully"}, "400": {"description": "Invalid file"}}, "tags": ["file-security"], "security": [{"bearer": []}]}}, "/api/v1/security/files/{filename}": {"get": {"operationId": "FileSecurityController_getFile", "summary": "Get a secure file", "parameters": [{"name": "filename", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the file"}, "404": {"description": "File not found"}}, "tags": ["file-security"], "security": [{"bearer": []}]}}, "/api/v1/security/files/validate": {"post": {"operationId": "FileSecurityController_validateFile", "summary": "Validate a file without uploading it", "parameters": [], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "File validation result"}}, "tags": ["file-security"], "security": [{"bearer": []}]}}, "/api/v1/security/files/scan/status": {"get": {"operationId": "FileSecurityController_getScanningStatus", "summary": "Get file scanning status", "parameters": [], "responses": {"200": {"description": "Returns file scanning status"}}, "tags": ["file-security"], "security": [{"bearer": []}]}}, "/api/v1/security/notifications": {"get": {"operationId": "SecurityNotificationController_getUserNotifications", "summary": "Get user security notifications", "parameters": [{"name": "severity", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns user security notifications"}}, "tags": ["security-notifications"], "security": [{"bearer": []}]}}, "/api/v1/security/notifications/unread-count": {"get": {"operationId": "SecurityNotificationController_getUnreadCount", "summary": "Get unread notifications count", "parameters": [], "responses": {"200": {"description": "Returns unread notifications count"}}, "tags": ["security-notifications"], "security": [{"bearer": []}]}}, "/api/v1/security/notifications/{id}/read": {"post": {"operationId": "SecurityNotificationController_markAsRead", "summary": "Mark notification as read", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Notification marked as read"}}, "tags": ["security-notifications"], "security": [{"bearer": []}]}}, "/api/v1/security/notifications/mark-all-read": {"post": {"operationId": "SecurityNotificationController_markAllAsRead", "summary": "Mark all notifications as read", "parameters": [], "responses": {"200": {"description": "All notifications marked as read"}}, "tags": ["security-notifications"], "security": [{"bearer": []}]}}, "/api/v1/encryption-audit/log-event": {"post": {"operationId": "EncryptionAuditController_logEvent", "parameters": [], "responses": {"201": {"description": ""}}}}, "/api/v1/encryption-audit/events": {"get": {"operationId": "EncryptionAuditController_getEvents", "parameters": [{"name": "userId", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "resourceId", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "eventType", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}}}, "/api/v1/encryption-audit/generate-report": {"post": {"operationId": "EncryptionAuditController_generateReport", "parameters": [], "responses": {"201": {"description": ""}}}}, "/api/v1/api/security/tokenization/tokenize": {"post": {"operationId": "TokenizationController_tokenize", "summary": "Tokenize sensitive data", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenizeRequestDto"}}}}, "responses": {"201": {"description": "Data tokenized successfully"}, "400": {"description": "Invalid request"}, "500": {"description": "Internal server error"}}, "tags": ["tokenization"]}}, "/api/v1/api/security/tokenization/detokenize": {"post": {"operationId": "TokenizationController_detokenize", "summary": "Detokenize data", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DetokenizeRequestDto"}}}}, "responses": {"200": {"description": "Data detokenized successfully"}, "400": {"description": "Invalid request"}, "404": {"description": "Token not found"}, "500": {"description": "Internal server error"}}, "tags": ["tokenization"]}}, "/api/v1/api/security/tokenization/token/{tokenValue}": {"delete": {"operationId": "TokenizationController_revokeToken", "summary": "Revoke a token", "parameters": [{"name": "tokenValue", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Token revoked successfully"}, "404": {"description": "Token not found"}, "500": {"description": "Internal server error"}}, "tags": ["tokenization"]}}, "/api/v1/api/security/tokenization/stats": {"get": {"operationId": "TokenizationController_getTokenizationStats", "summary": "Get tokenization statistics", "parameters": [], "responses": {"200": {"description": "Statistics retrieved successfully"}, "500": {"description": "Internal server error"}}, "tags": ["tokenization"]}}, "/api/v1/security/crypto-monitoring/metrics": {"get": {"operationId": "CryptoMonitoringController_getMetrics", "summary": "Get crypto metrics", "parameters": [], "responses": {"200": {"description": "Return crypto metrics"}}, "tags": ["crypto-monitoring"]}}, "/api/v1/security/crypto-monitoring/alerts": {"get": {"operationId": "CryptoMonitoringController_getAlerts", "summary": "Get crypto alerts", "parameters": [{"name": "includeAcknowledged", "required": false, "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Return crypto alerts"}}, "tags": ["crypto-monitoring"]}}, "/api/v1/security/crypto-monitoring/alerts/{id}/acknowledge": {"post": {"operationId": "CryptoMonitoringController_acknowledgeAlert", "summary": "Acknowledge a crypto alert", "parameters": [{"name": "id", "required": true, "in": "path", "description": "<PERSON><PERSON>", "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON> acknowledged"}, "404": {"description": "<PERSON><PERSON> not found"}}, "tags": ["crypto-monitoring"]}}, "/api/v1/security/crypto-monitoring/alerts/{id}/resolve": {"post": {"operationId": "CryptoMonitoringController_resolveAlert", "summary": "Resolve a crypto alert", "parameters": [{"name": "id", "required": true, "in": "path", "description": "<PERSON><PERSON>", "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON> resolved"}, "404": {"description": "<PERSON><PERSON> not found"}}, "tags": ["crypto-monitoring"]}}, "/api/v1/security/crypto-optimization/metrics": {"get": {"operationId": "CryptoOptimizationController_getMetrics", "summary": "Get performance metrics", "parameters": [{"name": "operationType", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return performance metrics"}}, "tags": ["crypto-optimization"], "security": [{"bearer": []}]}}, "/api/v1/security/crypto-optimization/metrics/export": {"get": {"operationId": "CryptoOptimizationController_exportMetrics", "summary": "Export performance metrics", "parameters": [{"name": "format", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return performance metrics in CSV format"}}, "tags": ["crypto-optimization"], "security": [{"bearer": []}]}}, "/api/v1/security/crypto-optimization/metrics/clear": {"post": {"operationId": "CryptoOptimizationController_clearMetrics", "summary": "Clear performance metrics", "parameters": [], "responses": {"200": {"description": "Performance metrics cleared"}}, "tags": ["crypto-optimization"], "security": [{"bearer": []}]}}, "/api/v1/security/crypto-optimization/cache/stats": {"get": {"operationId": "CryptoOptimizationController_getCacheStats", "summary": "Get cache statistics", "parameters": [], "responses": {"200": {"description": "Return cache statistics"}}, "tags": ["crypto-optimization"], "security": [{"bearer": []}]}}, "/api/v1/security/crypto-optimization/cache/entries": {"get": {"operationId": "CryptoOptimizationController_getCacheEntries", "summary": "Get cache entries", "parameters": [], "responses": {"200": {"description": "Return cache entries"}}, "tags": ["crypto-optimization"], "security": [{"bearer": []}]}}, "/api/v1/security/crypto-optimization/cache/clear": {"post": {"operationId": "CryptoOptimizationController_clearCache", "summary": "Clear cache", "parameters": [], "responses": {"200": {"description": "<PERSON><PERSON> cleared"}}, "tags": ["crypto-optimization"], "security": [{"bearer": []}]}}, "/api/v1/security/crypto-optimization/params": {"get": {"operationId": "CryptoOptimizationController_getParams", "summary": "Get optimization parameters", "parameters": [{"name": "operationType", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return optimization parameters"}}, "tags": ["crypto-optimization"], "security": [{"bearer": []}]}}, "/api/v1/security/crypto-optimization/params/{operationType}": {"post": {"operationId": "CryptoOptimizationController_setParams", "summary": "Set optimization parameters", "parameters": [{"name": "operationType", "required": true, "in": "path", "description": "Operation type", "schema": {"type": "string"}}], "responses": {"200": {"description": "Optimization parameters set"}}, "tags": ["crypto-optimization"], "security": [{"bearer": []}]}}, "/api/v1/security/crypto-optimization/params/{operationType}/reset": {"post": {"operationId": "CryptoOptimizationController_resetParams", "summary": "Reset optimization parameters", "parameters": [{"name": "operationType", "required": true, "in": "path", "description": "Operation type", "schema": {"type": "string"}}], "responses": {"200": {"description": "Optimization parameters reset"}}, "tags": ["crypto-optimization"], "security": [{"bearer": []}]}}, "/api/v1/security/crypto-optimization/optimize/{operationType}": {"post": {"operationId": "CryptoOptimizationController_optimize", "summary": "Optimize parameters for an operation type", "parameters": [{"name": "operationType", "required": true, "in": "path", "description": "Operation type", "schema": {"type": "string"}}], "responses": {"200": {"description": "Parameters optimized"}}, "tags": ["crypto-optimization"], "security": [{"bearer": []}]}}, "/api/v1/security/crypto-optimization/optimize/auto": {"post": {"operationId": "CryptoOptimizationController_autoOptimize", "summary": "Auto-optimize all parameters", "parameters": [], "responses": {"200": {"description": "All parameters auto-optimized"}}, "tags": ["crypto-optimization"], "security": [{"bearer": []}]}}, "/api/v1/security/crypto-optimization/report": {"get": {"operationId": "CryptoOptimizationController_generateReport", "summary": "Generate optimization report", "parameters": [], "responses": {"200": {"description": "Return optimization report"}}, "tags": ["crypto-optimization"], "security": [{"bearer": []}]}}, "/api/v1/security/crypto-optimization/profiling/report": {"get": {"operationId": "CryptoOptimizationController_generateProfilingReport", "summary": "Generate profiling report", "parameters": [], "responses": {"200": {"description": "Return profiling report"}}, "tags": ["crypto-optimization"], "security": [{"bearer": []}]}}, "/api/v1/security/crypto-optimization/profiling/sampling-rate": {"post": {"operationId": "CryptoOptimizationController_setSamplingRate", "summary": "Set profiling sampling rate", "parameters": [], "responses": {"200": {"description": "Profiling sampling rate set"}}, "tags": ["crypto-optimization"], "security": [{"bearer": []}]}}, "/api/v1/security/crypto-optimization/profiling/threshold": {"post": {"operationId": "CryptoOptimizationController_setThreshold", "summary": "Set profiling threshold", "parameters": [], "responses": {"200": {"description": "Profiling threshold set"}}, "tags": ["crypto-optimization"], "security": [{"bearer": []}]}}, "/api/v1/security/encryption-policy": {"get": {"operationId": "EncryptionPolicyController_getAllPolicies", "summary": "Get all encryption policies", "parameters": [], "responses": {"200": {"description": "Return all encryption policies"}}, "tags": ["encryption-policy"], "security": [{"bearer": []}]}, "post": {"operationId": "EncryptionPolicyController_setPolicy", "summary": "Create or update encryption policy", "parameters": [], "responses": {"201": {"description": "Policy created or updated successfully"}}, "tags": ["encryption-policy"], "security": [{"bearer": []}]}}, "/api/v1/security/encryption-policy/data-type/{type}": {"get": {"operationId": "EncryptionPolicyController_getPolicyForDataType", "summary": "Get encryption policy for a data type", "parameters": [{"name": "type", "required": true, "in": "path", "description": "Data type", "schema": {"enum": ["personal", "financial", "health", "authentication", "communication", "analytics", "general"], "type": "string"}}], "responses": {"200": {"description": "Return encryption policy for the specified data type"}}, "tags": ["encryption-policy"], "security": [{"bearer": []}]}}, "/api/v1/security/encryption-policy/sensitivity/{level}": {"get": {"operationId": "EncryptionPolicyController_getPolicyForSensitivityLevel", "summary": "Get encryption policy for a sensitivity level", "parameters": [{"name": "level", "required": true, "in": "path", "description": "Sensitivity level", "schema": {"enum": ["low", "medium", "high", "critical"], "type": "string"}}], "responses": {"200": {"description": "Return encryption policy for the specified sensitivity level"}}, "tags": ["encryption-policy"], "security": [{"bearer": []}]}}, "/api/v1/security/encryption-policy/{name}": {"get": {"operationId": "EncryptionPolicyController_getPolicy", "summary": "Get encryption policy by name", "parameters": [{"name": "name", "required": true, "in": "path", "description": "Policy name", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return encryption policy with the specified name"}}, "tags": ["encryption-policy"], "security": [{"bearer": []}]}}, "/api/v1/security/encryption-policy/map/data-type": {"post": {"operationId": "EncryptionPolicyController_mapDataTypeToPolicy", "summary": "Map data type to encryption policy", "parameters": [], "responses": {"201": {"description": "Data type mapped to policy successfully"}}, "tags": ["encryption-policy"], "security": [{"bearer": []}]}}, "/api/v1/security/encryption-policy/map/sensitivity": {"post": {"operationId": "EncryptionPolicyController_mapSensitivityLevelToPolicy", "summary": "Map sensitivity level to encryption policy", "parameters": [], "responses": {"201": {"description": "Sensitivity level mapped to policy successfully"}}, "tags": ["encryption-policy"], "security": [{"bearer": []}]}}, "/api/v1/security/encryption-policy/encryption-type/{type}/available": {"get": {"operationId": "EncryptionPolicyController_isEncryptionTypeAvailable", "summary": "Check if encryption type is available", "parameters": [{"name": "type", "required": true, "in": "path", "description": "Encryption type", "schema": {"enum": ["standard", "quantum-resistant", "homomorphic", "hybrid"], "type": "string"}}], "responses": {"200": {"description": "Return availability status of the encryption type"}}, "tags": ["encryption-policy"], "security": [{"bearer": []}]}}, "/api/v1/security/encryption-policy/homomorphic/allowed": {"get": {"operationId": "EncryptionPolicyController_isHomomorphicAllowed", "summary": "Check if homomorphic encryption is allowed for a context", "parameters": [], "responses": {"200": {"description": "Return whether homomorphic encryption is allowed"}}, "tags": ["encryption-policy"], "security": [{"bearer": []}]}}, "/api/v1/security/encryption-policy/quantum-resistant/required": {"get": {"operationId": "EncryptionPolicyController_isQuantumResistantRequired", "summary": "Check if quantum-resistant encryption is required for a context", "parameters": [], "responses": {"200": {"description": "Return whether quantum-resistant encryption is required"}}, "tags": ["encryption-policy"], "security": [{"bearer": []}]}}, "/api/v1/audit/entries": {"post": {"operationId": "AuditController_createAuditEntry", "summary": "C<PERSON>er une entrée d'audit", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAuditEntryDto"}}}}, "responses": {"201": {"description": "L'entrée d'audit a été créée avec succès."}, "400": {"description": "Donn<PERSON> invalides."}}, "tags": ["audit"], "security": [{"bearer": []}]}, "get": {"operationId": "AuditController_findAllEntries", "summary": "Ré<PERSON><PERSON>rer toutes les entrées d'audit", "parameters": [{"name": "endDate", "required": false, "in": "query", "description": "Date de fin (ISO)", "schema": {"type": "string"}}, {"name": "startDate", "required": false, "in": "query", "description": "Date de début (ISO)", "schema": {"type": "string"}}, {"name": "resource", "required": false, "in": "query", "description": "Ressource", "schema": {"type": "string"}}, {"name": "action", "required": false, "in": "query", "description": "Action", "schema": {"type": "string"}}, {"name": "userId", "required": false, "in": "query", "description": "ID de l'utilisateur", "schema": {"type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Ordre de tri", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Champ de tri", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des entrées d'audit récupérée avec succès."}}, "tags": ["audit"], "security": [{"bearer": []}]}}, "/api/v1/audit/entries/{id}": {"get": {"operationId": "AuditController_findOneEntry", "summary": "Récupérer une entrée d'audit par son ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "L'entrée d'audit a été récupérée avec succès."}, "404": {"description": "Entrée d'audit non trouvée."}}, "tags": ["audit"], "security": [{"bearer": []}]}}, "/api/v1/audit/entries/user/{userId}": {"get": {"operationId": "AuditController_findByUser", "summary": "Récupérer les entrées d'audit pour un utilisateur", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "Date de fin (ISO)", "schema": {"type": "string"}}, {"name": "startDate", "required": false, "in": "query", "description": "Date de début (ISO)", "schema": {"type": "string"}}, {"name": "resource", "required": false, "in": "query", "description": "Ressource", "schema": {"type": "string"}}, {"name": "action", "required": false, "in": "query", "description": "Action", "schema": {"type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Ordre de tri", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Champ de tri", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des entrées d'audit récupérée avec succès."}}, "tags": ["audit"], "security": [{"bearer": []}]}}, "/api/v1/audit/entries/action/{action}": {"get": {"operationId": "AuditController_findByAction", "summary": "Récupérer les entrées d'audit pour une action", "parameters": [{"name": "action", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "Date de fin (ISO)", "schema": {"type": "string"}}, {"name": "startDate", "required": false, "in": "query", "description": "Date de début (ISO)", "schema": {"type": "string"}}, {"name": "resource", "required": false, "in": "query", "description": "Ressource", "schema": {"type": "string"}}, {"name": "userId", "required": false, "in": "query", "description": "ID de l'utilisateur", "schema": {"type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Ordre de tri", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Champ de tri", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des entrées d'audit récupérée avec succès."}}, "tags": ["audit"], "security": [{"bearer": []}]}}, "/api/v1/audit/entries/resource/{resource}": {"get": {"operationId": "AuditController_findByResource", "summary": "Récupérer les entrées d'audit pour une ressource", "parameters": [{"name": "resource", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "Date de fin (ISO)", "schema": {"type": "string"}}, {"name": "startDate", "required": false, "in": "query", "description": "Date de début (ISO)", "schema": {"type": "string"}}, {"name": "action", "required": false, "in": "query", "description": "Action", "schema": {"type": "string"}}, {"name": "userId", "required": false, "in": "query", "description": "ID de l'utilisateur", "schema": {"type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Ordre de tri", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Champ de tri", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des entrées d'audit récupérée avec succès."}}, "tags": ["audit"], "security": [{"bearer": []}]}}, "/api/v1/audit/reports/generate": {"post": {"operationId": "AuditController_generateReport", "summary": "Générer un rapport d'audit", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuditQueryDto"}}}}, "responses": {"200": {"description": "Le rapport d'audit a été généré avec succès."}, "400": {"description": "Donn<PERSON> invalides."}}, "tags": ["audit"], "security": [{"bearer": []}]}}, "/api/v1/audit/compliance/gdpr": {"post": {"operationId": "AuditController_runGdprComplianceCheck", "summary": "Exécuter un contrôle de conformité GDPR", "parameters": [], "responses": {"200": {"description": "Le contrôle de conformité GDPR a été exécuté avec succès."}}, "tags": ["audit"], "security": [{"bearer": []}]}}, "/api/v1/recommendations": {"get": {"operationId": "RecommendationController_getRecommendations", "summary": "Récupérer des recommandations pour l'utilisateur connecté", "parameters": [{"name": "limit", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "strategy", "required": false, "in": "query", "schema": {"enum": ["CONTENT_BASED", "COLLABORATIVE", "HYBRID"], "type": "string"}}, {"name": "type", "required": false, "in": "query", "schema": {"enum": ["COURSE", "RETREAT", "PARTNER"], "type": "string"}}], "responses": {"200": {"description": "Liste des recommandations récupérée avec succès."}}, "tags": ["recommendations"], "security": [{"bearer": []}]}}, "/api/v1/recommendations/personalized": {"get": {"operationId": "RecommendationController_getPersonalizedRecommendations", "summary": "Récupérer des recommandations personnalisées pour l'utilisateur connecté", "parameters": [{"name": "limit", "required": false, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des recommandations personnalisées récupérée avec succès."}}, "tags": ["recommendations"], "security": [{"bearer": []}]}}, "/api/v1/recommendations/trending": {"get": {"operationId": "RecommendationController_getTrendingRecommendations", "summary": "Récupérer des recommandations tendance", "parameters": [{"name": "limit", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "type", "required": false, "in": "query", "schema": {"enum": ["COURSE", "RETREAT", "PARTNER"], "type": "string"}}], "responses": {"200": {"description": "Liste des recommandations tendance récupérée avec succès."}}, "tags": ["recommendations"], "security": [{"bearer": []}]}}, "/api/v1/recommendations/similar/{type}/{itemId}": {"get": {"operationId": "RecommendationController_getSimilarItems", "summary": "Récupérer des éléments similaires à un élément", "parameters": [{"name": "itemId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des éléments similaires récupérée avec succès."}}, "tags": ["recommendations"], "security": [{"bearer": []}]}}, "/api/v1/recommendations/interactions": {"post": {"operationId": "RecommendationController_recordInteraction", "summary": "Enregistrer une interaction utilisateur", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecordInteractionDto"}}}}, "responses": {"201": {"description": "L'interaction a été enregistrée avec succès."}}, "tags": ["recommendations"], "security": [{"bearer": []}]}}, "/api/v1/recommendations/preferences": {"get": {"operationId": "RecommendationController_getUserPreferences", "summary": "Récupérer les préférences de recommandation de l'utilisateur connecté", "parameters": [], "responses": {"200": {"description": "Préférences de recommandation récupérées avec succès."}}, "tags": ["recommendations"], "security": [{"bearer": []}]}, "post": {"operationId": "RecommendationController_updateUserPreferences", "summary": "Mettre à jour les préférences de recommandation de l'utilisateur connecté", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePreferencesDto"}}}}, "responses": {"200": {"description": "Préférences de recommandation mises à jour avec succès."}}, "tags": ["recommendations"], "security": [{"bearer": []}]}}, "/api/v1/recommendations/social-video/for-you": {"get": {"operationId": "SocialVideoRecommendationController_getPersonalizedRecommendations", "summary": "Obtenir des recommandations de vidéos personnalisées", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Nombre maximum de recommandations à retourner", "schema": {"type": "number"}}, {"name": "cursor", "required": false, "in": "query", "description": "Curseur pour la pagination", "schema": {"type": "string"}}], "responses": {"200": {"description": "Recommandations récupérées avec succès"}}, "tags": ["social-video-recommendations"], "security": [{"bearer": []}]}}, "/api/v1/recommendations/social-video/trending": {"get": {"operationId": "SocialVideoRecommendationController_getTrendingRecommendations", "summary": "Obtenir des recommandations de vidéos tendance", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Nombre maximum de recommandations à retourner", "schema": {"type": "number"}}, {"name": "cursor", "required": false, "in": "query", "description": "Curseur pour la pagination", "schema": {"type": "string"}}], "responses": {"200": {"description": "Recommandations récupérées avec succès"}}, "tags": ["social-video-recommendations"]}}, "/api/v1/recommendations/social-video/similar": {"get": {"operationId": "SocialVideoRecommendationController_getSimilar<PERSON><PERSON>nt", "summary": "Obtenir des recommandations de vidéos similaires", "parameters": [{"name": "contentId", "required": true, "in": "query", "description": "ID du contenu", "schema": {"type": "string"}}, {"name": "contentType", "required": true, "in": "query", "description": "Type de contenu", "schema": {"enum": ["video", "post", "livestream"], "type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre maximum de recommandations à retourner", "schema": {"type": "number"}}, {"name": "cursor", "required": false, "in": "query", "description": "Curseur pour la pagination", "schema": {"type": "string"}}], "responses": {"200": {"description": "Recommandations récupérées avec succès"}}, "tags": ["social-video-recommendations"]}}, "/api/v1/recommendations/social-video/feedback": {"post": {"operationId": "SocialVideoRecommendationController_provideRecommendationFeedback", "summary": "Fournir un feedback sur une recommandation", "parameters": [], "responses": {"200": {"description": "Feedback enregis<PERSON><PERSON> avec succès"}}, "tags": ["social-video-recommendations"], "security": [{"bearer": []}]}}, "/api/v1/recommendations/social-video/interactions": {"post": {"operationId": "SocialVideoRecommendationController_recordInteraction", "summary": "Enregistrer une interaction avec un contenu", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SocialVideoInteractionDto"}}}}, "responses": {"201": {"description": "Interaction enregistrée avec succès"}}, "tags": ["social-video-recommendations"], "security": [{"bearer": []}]}}, "/api/v1/recommendations/social-video/preferences": {"get": {"operationId": "SocialVideoRecommendationController_getPreferences", "summary": "Récupérer les préférences de recommandation de l'utilisateur", "parameters": [], "responses": {"200": {"description": "Préférences récupérées avec succès"}}, "tags": ["social-video-recommendations"], "security": [{"bearer": []}]}, "put": {"operationId": "SocialVideoRecommendationController_updatePreferences", "summary": "Mettre à jour les préférences de recommandation de l'utilisateur", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SocialVideoPreferencesDto"}}}}, "responses": {"200": {"description": "Préférences mises à jour avec succès"}}, "tags": ["social-video-recommendations"], "security": [{"bearer": []}]}}, "/api/v1/coupons": {"post": {"operationId": "CouponController_create", "summary": "<PERSON><PERSON>er un nouveau coupon", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCouponDto"}}}}, "responses": {"201": {"description": "Le coupon a été créé avec succès."}, "400": {"description": "Donn<PERSON> invalides."}}, "tags": ["coupons"], "security": [{"bearer": []}]}, "get": {"operationId": "CouponController_findAll", "summary": "Récupérer tous les coupons", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "includeInactive", "required": false, "in": "query", "description": "Inclure les coupons inactifs", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Liste des coupons récupérée avec succès."}}, "tags": ["coupons"], "security": [{"bearer": []}]}}, "/api/v1/coupons/stats": {"get": {"operationId": "CouponController_getCouponStats", "summary": "Récupérer les statistiques des coupons", "parameters": [], "responses": {"200": {"description": "Statistiques des coupons récupérées avec succès."}}, "tags": ["coupons"], "security": [{"bearer": []}]}}, "/api/v1/coupons/user": {"get": {"operationId": "CouponController_getUserCoupons", "summary": "Récupérer les coupons de l'utilisateur connecté", "parameters": [], "responses": {"200": {"description": "Liste des coupons de l'utilisateur récupérée avec succès."}}, "tags": ["coupons"], "security": [{"bearer": []}]}}, "/api/v1/coupons/{id}": {"get": {"operationId": "CouponController_findOne", "summary": "Récupérer un coupon par son ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Le coupon a été récupéré avec succès."}, "404": {"description": "Coupon non trouvé."}}, "tags": ["coupons"], "security": [{"bearer": []}]}, "patch": {"operationId": "CouponController_update", "summary": "Mettre à jour un coupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCouponDto"}}}}, "responses": {"200": {"description": "Le coupon a été mis à jour avec succès."}, "400": {"description": "Donn<PERSON> invalides."}, "404": {"description": "Coupon non trouvé."}}, "tags": ["coupons"], "security": [{"bearer": []}]}, "delete": {"operationId": "CouponController_remove", "summary": "Supprimer un coupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Le coupon a été supprimé avec succès."}, "404": {"description": "Coupon non trouvé."}}, "tags": ["coupons"], "security": [{"bearer": []}]}}, "/api/v1/coupons/code/{code}": {"get": {"operationId": "CouponController_findByCode", "summary": "Récupérer un coupon par son code", "parameters": [{"name": "code", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Le coupon a été récupéré avec succès."}, "404": {"description": "Coupon non trouvé."}}, "tags": ["coupons"], "security": [{"bearer": []}]}}, "/api/v1/coupons/{id}/activate": {"patch": {"operationId": "CouponController_activate", "summary": "Activer un coupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Le coupon a été activé avec succès."}, "404": {"description": "Coupon non trouvé."}}, "tags": ["coupons"], "security": [{"bearer": []}]}}, "/api/v1/coupons/{id}/deactivate": {"patch": {"operationId": "CouponController_deactivate", "summary": "Désactiver un coupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Le coupon a été désactivé avec succès."}, "404": {"description": "Coupon non trouvé."}}, "tags": ["coupons"], "security": [{"bearer": []}]}}, "/api/v1/coupons/apply": {"post": {"operationId": "CouponController_applyCoupon", "summary": "Appliquer un coupon à une commande", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplyCouponDto"}}}}, "responses": {"200": {"description": "Le coupon a été appliqué avec succès."}, "400": {"description": "Coupon invalide ou données invalides."}, "404": {"description": "Coupon non trouvé."}}, "tags": ["coupons"], "security": [{"bearer": []}]}}, "/api/v1/coupons/bulk": {"post": {"operationId": "CouponController_generateBulkCoupons", "summary": "Générer des coupons en masse", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateBulkCouponsDto"}}}}, "responses": {"201": {"description": "Les coupons ont été générés avec succès."}, "400": {"description": "Donn<PERSON> invalides."}}, "tags": ["coupons"], "security": [{"bearer": []}]}}, "/api/v1/coupons/welcome/{userId}": {"post": {"operationId": "CouponController_generateWelcomeCoupon", "summary": "Générer un coupon de bienvenue pour un utilisateur", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": "Le coupon de bienvenue a été généré avec succès."}, "400": {"description": "Donn<PERSON> invalides."}}, "tags": ["coupons"], "security": [{"bearer": []}]}}, "/api/v1/coupons/birthday/{userId}": {"post": {"operationId": "CouponController_generateBirthdayCoupon", "summary": "Générer un coupon d'anniversaire pour un utilisateur", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": "Le coupon d'anniversaire a été généré avec succès."}, "400": {"description": "Donn<PERSON> invalides."}}, "tags": ["coupons"], "security": [{"bearer": []}]}}, "/api/v1/coupons/loyalty/{userId}": {"post": {"operationId": "CouponController_generateLoyaltyCoupon", "summary": "Générer un coupon de fidélité pour un utilisateur", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": "Le coupon de fidélité a été généré avec succès."}, "400": {"description": "Donn<PERSON> invalides."}}, "tags": ["coupons"], "security": [{"bearer": []}]}}, "/api/v1/coupons/promotions": {"get": {"operationId": "CouponController_getActivePromotions", "summary": "Récupérer les promotions actives", "parameters": [], "responses": {"200": {"description": "Liste des promotions récupérée avec succès."}}, "tags": ["coupons"], "security": [{"bearer": []}]}}, "/api/v1/performance/report": {"get": {"operationId": "PerformanceController_generateReport", "summary": "Générer un rapport de performance", "parameters": [], "responses": {"200": {"description": "Rapport de performance généré avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/reports": {"get": {"operationId": "PerformanceController_getReports", "summary": "Récupérer les rapports de performance", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des rapports de performance récupérée avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/reports/{id}": {"get": {"operationId": "PerformanceController_getReportById", "summary": "Récupérer un rapport de performance par son ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Rapport de performance récupéré avec succès."}, "404": {"description": "Rapport de performance non trouvé."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/reports/latest": {"get": {"operationId": "PerformanceController_getLatestReport", "summary": "Récupérer le dernier rapport de performance", "parameters": [], "responses": {"200": {"description": "Dernier rapport de performance récupéré avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/metrics/system": {"get": {"operationId": "PerformanceController_getSystemMetrics", "summary": "Récupérer les métriques système", "parameters": [], "responses": {"200": {"description": "Métriques système récupérées avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/metrics/api": {"get": {"operationId": "PerformanceController_getApiMetrics", "summary": "Récupérer les métriques API", "parameters": [], "responses": {"200": {"description": "Métriques API récupérées avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/metrics/database": {"get": {"operationId": "PerformanceController_getDatabaseMetrics", "summary": "Récupérer les métriques de base de données", "parameters": [], "responses": {"200": {"description": "Métriques de base de données récupérées avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/metrics/cache": {"get": {"operationId": "PerformanceController_getCacheMetrics", "summary": "Récupérer les métriques de cache", "parameters": [], "responses": {"200": {"description": "Métriques de cache récupérées avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/metrics/custom": {"get": {"operationId": "PerformanceController_getCustomMetrics", "summary": "Récupérer les métriques personnalisées", "parameters": [], "responses": {"200": {"description": "Métriques personnalisées récupérées avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}, "post": {"operationId": "PerformanceController_recordCustomMetric", "summary": "Enregistrer une métrique personnalisée", "parameters": [], "responses": {"201": {"description": "Métrique personnalisée enregistrée avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/thresholds": {"get": {"operationId": "PerformanceController_getThresholds", "summary": "Récup<PERSON>rer les seuils de performance", "parameters": [], "responses": {"200": {"description": "Seuils de performance récupérés avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}, "post": {"operationId": "PerformanceController_setThresholds", "summary": "Définir des seuils de performance", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"201": {"description": "Seuils de performance définis avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/monitoring/status": {"get": {"operationId": "PerformanceController_getMonitoringStatus", "summary": "Récupérer le statut global du monitoring", "parameters": [], "responses": {"200": {"description": "Statut global du monitoring récupéré avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/monitoring/config": {"get": {"operationId": "PerformanceController_getMonitoringConfig", "summary": "Récupérer la configuration de monitoring", "parameters": [], "responses": {"200": {"description": "Configuration de monitoring récupérée avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}, "patch": {"operationId": "PerformanceController_updateMonitoringConfig", "summary": "Mettre à jour la configuration de monitoring", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMonitoringConfigDto"}}}}, "responses": {"200": {"description": "Configuration de monitoring mise à jour avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/monitoring/endpoints": {"get": {"operationId": "PerformanceController_getEndpoints", "summary": "Récupérer les endpoints surveillés", "parameters": [], "responses": {"200": {"description": "Liste des endpoints surveillés récupérée avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}, "post": {"operationId": "PerformanceController_addEndpoint", "summary": "Ajouter un endpoint à surveiller", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEndpointDto"}}}}, "responses": {"201": {"description": "Endpoint ajouté avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}, "delete": {"operationId": "PerformanceController_removeEndpoint", "summary": "Supprimer un endpoint à surveiller", "parameters": [], "responses": {"200": {"description": "Endpoint supprimé avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/monitoring/services": {"get": {"operationId": "PerformanceController_getServices", "summary": "Récupérer les services surveillés", "parameters": [], "responses": {"200": {"description": "Liste des services surveillés récupérée avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}, "post": {"operationId": "PerformanceController_addService", "summary": "Ajouter un service à surveiller", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateServiceDto"}}}}, "responses": {"201": {"description": "Service ajouté avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}, "delete": {"operationId": "PerformanceController_removeService", "summary": "Supprimer un service à surveiller", "parameters": [], "responses": {"200": {"description": "Service supprimé avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/alerts": {"get": {"operationId": "PerformanceController_get<PERSON>lerts", "summary": "Récupérer les alertes", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Numéro de <PERSON>", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre d'éléments par page", "schema": {"type": "number"}}, {"name": "severity", "required": false, "in": "query", "description": "Sévérité des alertes", "schema": {"enum": ["INFO", "WARNING", "ERROR", "CRITICAL"], "type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "Statut des alertes", "schema": {"enum": ["OPEN", "ACKNOWLEDGED", "RESOLVED"], "type": "string"}}], "responses": {"200": {"description": "Liste des alertes récupérée avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}, "post": {"operationId": "PerformanceController_createAlert", "summary": "<PERSON><PERSON><PERSON> une alerte", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAlertDto"}}}}, "responses": {"201": {"description": "Alerte créée avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/alerts/stats": {"get": {"operationId": "PerformanceController_getAlertStats", "summary": "Récupérer les statistiques des alertes", "parameters": [], "responses": {"200": {"description": "Statistiques des alertes récupérées avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/alerts/recent": {"get": {"operationId": "PerformanceController_getRecentAlerts", "summary": "Récupérer les alertes récentes", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Nombre d'alertes à récupérer", "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des alertes récentes récupérée avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/alerts/{id}": {"get": {"operationId": "PerformanceController_getAlertById", "summary": "Récupérer une alerte par son ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Alerte récupérée avec succès."}, "404": {"description": "Alerte non trouvée."}}, "tags": ["performance"], "security": [{"bearer": []}]}, "patch": {"operationId": "PerformanceController_updateAlert", "summary": "Mettre à jour une alerte", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAlertDto"}}}}, "responses": {"200": {"description": "Alerte mise à jour avec succès."}, "404": {"description": "Alerte non trouvée."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/alerts/{id}/acknowledge": {"post": {"operationId": "PerformanceController_acknowledgeAlert", "summary": "Accuser réception d'une alerte", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Accusé de réception de l'alerte effectué avec succès."}, "404": {"description": "Alerte non trouvée."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/alerts/{id}/resolve": {"post": {"operationId": "PerformanceController_resolveAlert", "summary": "Résoudre une alerte", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Alerte résolue avec succès."}, "404": {"description": "Alerte non trouvée."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/profiling/results": {"get": {"operationId": "PerformanceController_getProfilingResults", "summary": "Récupérer les résultats de profilage", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Nombre de résultats à récupérer", "schema": {"type": "number"}}], "responses": {"200": {"description": "Résultats de profilage récupérés avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/profiling/results/{name}": {"get": {"operationId": "PerformanceController_getProfilingResultsByName", "summary": "Récupérer les résultats de profilage par nom", "parameters": [{"name": "name", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre de résultats à récupérer", "schema": {"type": "number"}}], "responses": {"200": {"description": "Résultats de profilage récupérés avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/profiling/results/id/{id}": {"get": {"operationId": "PerformanceController_getProfilingResultById", "summary": "Récupérer un résultat de profilage par son ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Résultat de profilage récupéré avec succès."}, "404": {"description": "Résultat de profilage non trouvé."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/profiling/start": {"post": {"operationId": "PerformanceController_startProfiling", "summary": "<PERSON><PERSON><PERSON>rer une session de profilage", "parameters": [], "responses": {"201": {"description": "Session de profilage démar<PERSON>e avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/profiling/end": {"post": {"operationId": "PerformanceController_endProfiling", "summary": "Terminer une session de profilage", "parameters": [], "responses": {"200": {"description": "Session de profilage terminée avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/performance/profiling/measurement": {"post": {"operationId": "PerformanceController_addMeasurement", "summary": "Ajouter une mesure à une session de profilage", "parameters": [], "responses": {"201": {"description": "Mesure ajoutée avec succès."}}, "tags": ["performance"], "security": [{"bearer": []}]}}, "/api/v1/health": {"get": {"operationId": "HealthController_health", "parameters": [], "responses": {"200": {"description": ""}}}}, "/api/v1/partners": {"post": {"operationId": "PartnerController_register", "summary": "Register as a partner", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePartnerDto"}}}}, "responses": {"201": {"description": "Partner registration submitted successfully"}, "400": {"description": "Invalid data or user already registered as partner"}}, "tags": ["partners"], "security": [{"bearer": []}]}, "get": {"operationId": "PartnerController_findAll", "summary": "Get all partners", "parameters": [{"name": "status", "required": false, "in": "query", "schema": {"enum": ["PENDING", "ACTIVE", "SUSPENDED", "INACTIVE", "REJECTED"], "type": "string"}}], "responses": {"200": {"description": "Returns all partners"}}, "tags": ["partners"], "security": [{"bearer": []}]}}, "/api/v1/partners/{id}": {"get": {"operationId": "PartnerController_findOne", "summary": "Get partner by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the partner details"}, "404": {"description": "Partner not found"}}, "tags": ["partners"], "security": [{"bearer": []}]}}, "/api/v1/partners/{id}/status": {"patch": {"operationId": "PartnerController_updateStatus", "summary": "Update partner status", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Partner status updated successfully"}, "404": {"description": "Partner not found"}}, "tags": ["partners"], "security": [{"bearer": []}]}}, "/api/v1/partners/{id}/documents": {"post": {"operationId": "PartnerController_addDocument", "summary": "Add a document to a partner", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartnerDocumentDto"}}}}, "responses": {"201": {"description": "Document added successfully"}, "404": {"description": "Partner not found"}}, "tags": ["partners"], "security": [{"bearer": []}]}, "get": {"operationId": "PartnerController_getDocuments", "summary": "Get all documents for a partner", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns all documents for the partner"}, "404": {"description": "Partner not found"}}, "tags": ["partners"], "security": [{"bearer": []}]}}, "/api/v1/partners/documents/{documentId}": {"delete": {"operationId": "PartnerController_deleteDocument", "summary": "Delete a document", "parameters": [{"name": "documentId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Document deleted successfully"}, "404": {"description": "Document not found"}}, "tags": ["partners"], "security": [{"bearer": []}]}}, "/api/v1/files/upload": {"post": {"operationId": "FileUploadController_uploadFile", "summary": "Upload a single file securely", "parameters": [{"name": "category", "required": true, "in": "query", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "category": {"type": "string", "description": "File category", "example": "partner-documents"}}}}}}, "responses": {"201": {"description": "File uploaded successfully"}, "400": {"description": "Invalid file or security check failed"}, "401": {"description": "Unauthorized"}}, "tags": ["files"], "security": [{"bearer": []}]}}, "/api/v1/files/upload/multiple": {"post": {"operationId": "FileUploadController_uploadFiles", "summary": "Upload multiple files securely", "parameters": [{"name": "category", "required": true, "in": "query", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}, "category": {"type": "string", "description": "File category", "example": "partner-documents"}}}}}}, "responses": {"201": {"description": "Files uploaded successfully"}, "400": {"description": "Invalid files or security check failed"}, "401": {"description": "Unauthorized"}}, "tags": ["files"], "security": [{"bearer": []}]}}, "/api/v1/files": {"get": {"operationId": "FileUploadController_getFiles", "summary": "Get files by category", "parameters": [{"name": "category", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["files"], "security": [{"bearer": []}]}}, "/api/v1/files/{id}": {"get": {"operationId": "FileUploadController_getFile", "summary": "Get file by ID with integrity check", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the file information"}, "404": {"description": "File not found"}}, "tags": ["files"], "security": [{"bearer": []}]}, "delete": {"operationId": "FileUploadController_deleteFile", "summary": "Delete file by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "File deleted successfully"}, "401": {"description": "Unauthorized"}, "404": {"description": "File not found"}}, "tags": ["files"], "security": [{"bearer": []}]}}, "/api/v1/matching/partners": {"post": {"operationId": "MatchingController_findPartners", "summary": "Trouver des partenaires correspondant aux critères", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MatchingCriteriaDto"}}}}, "responses": {"200": {"description": "Retourne les partenaires correspondant aux critères", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MatchingResponseDto"}}}}, "400": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> invalide"}}, "tags": ["matching"], "security": [{"bearer": []}]}}, "/api/v1/matching/retreats": {"post": {"operationId": "MatchingController_findRetreats", "summary": "Trouver des retraites correspondant aux critères", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MatchingCriteriaDto"}}}}, "responses": {"200": {"description": "Retourne les retraites correspondant aux critères", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MatchingResponseDto"}}}}, "400": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> invalide"}}, "tags": ["matching"], "security": [{"bearer": []}]}}, "/api/v1/matching/partners/retreat/{retreatId}": {"get": {"operationId": "MatchingController_findPartnersForRetreat", "summary": "Trouver des partenaires pour une retraite spécifique", "parameters": [{"name": "retreatId", "required": true, "in": "path", "description": "ID de la retraite", "schema": {"type": "string"}}, {"name": "categories", "required": false, "in": "query", "description": "Catégories de partenaires", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "types", "required": false, "in": "query", "description": "Types de partenaires", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "specializations", "required": false, "in": "query", "description": "Spécialisations recherchées", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "languages", "required": false, "in": "query", "description": "Langues parlées requises", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "minExperience", "required": false, "in": "query", "description": "Expérience minimale requise", "schema": {"type": "number"}}, {"name": "minRating", "required": false, "in": "query", "description": "Note minimale requise", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre maximum de résultats", "schema": {"type": "number"}}], "responses": {"200": {"description": "Retourne les partenaires correspondant à la retraite", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MatchingResponseDto"}}}}, "404": {"description": "Retraite non trouvée"}}, "tags": ["matching"], "security": [{"bearer": []}]}}, "/api/v1/matching/retreats/partner/{partnerId}": {"get": {"operationId": "MatchingController_findRetreatsForPartner", "summary": "Trouver des retraites pour un partenaire spécifique", "parameters": [{"name": "partnerId", "required": true, "in": "path", "description": "ID du partenaire", "schema": {"type": "string"}}, {"name": "startDate", "required": false, "in": "query", "description": "Date de début (YYYY-MM-DD)", "schema": {"type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "Date de fin (YYYY-MM-DD)", "schema": {"type": "string"}}, {"name": "max<PERSON><PERSON><PERSON>", "required": false, "in": "query", "description": "Budget maximum", "schema": {"type": "number"}}, {"name": "country", "required": false, "in": "query", "description": "Pays", "schema": {"type": "string"}}, {"name": "minCapacity", "required": false, "in": "query", "description": "Capacité minimale", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Nombre maximum de résultats", "schema": {"type": "number"}}], "responses": {"200": {"description": "Re<PERSON>ne les retraites correspondant au partenaire", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MatchingResponseDto"}}}}, "404": {"description": "Partenaire non trouvé"}}, "tags": ["matching"], "security": [{"bearer": []}]}}, "/api/v1/matching/score/{partnerId}/{retreatId}": {"get": {"operationId": "MatchingController_getMatchingScore", "summary": "Obtenir le score de compatibilité entre un partenaire et une retraite", "parameters": [{"name": "partnerId", "required": true, "in": "path", "description": "ID du partenaire", "schema": {"type": "string"}}, {"name": "retreatId", "required": true, "in": "path", "description": "ID de la retraite", "schema": {"type": "string"}}], "responses": {"200": {"description": "Retourne le score de compatibilité", "content": {"application/json": {"schema": {"type": "object", "properties": {"score": {"type": "number", "example": 87}, "compatibilityFactors": {"type": "object", "properties": {"skillMatch": {"type": "number", "example": 85}, "availabilityMatch": {"type": "number", "example": 90}, "locationMatch": {"type": "number", "example": 75}, "ratingMatch": {"type": "number", "example": 95}, "budgetMatch": {"type": "number", "example": 80}}}}}}}}, "404": {"description": "Partenaire ou retraite non trouvé"}}, "tags": ["matching"], "security": [{"bearer": []}]}}, "/api/v1/matching/analytics/partner/{partnerId}": {"get": {"operationId": "MatchingAnalyticsController_getPartnerAnalytics", "summary": "Get matching analytics for a partner", "parameters": [{"name": "partnerId", "required": true, "in": "path", "description": "Partner ID", "schema": {"type": "string"}}, {"name": "period", "required": false, "in": "query", "description": "Period (day, week, month, year)", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns matching analytics for the partner"}}, "tags": ["matching-analytics"], "security": [{"bearer": []}]}}, "/api/v1/matching/analytics/view": {"post": {"operationId": "MatchingAnalyticsController_recordView", "summary": "Record a matching view event", "parameters": [], "responses": {"201": {"description": "Event recorded successfully"}}, "tags": ["matching-analytics"], "security": [{"bearer": []}]}}, "/api/v1/matching/analytics/contact": {"post": {"operationId": "MatchingAnalyticsController_recordContact", "summary": "Record a matching contact event", "parameters": [], "responses": {"201": {"description": "Event recorded successfully"}}, "tags": ["matching-analytics"], "security": [{"bearer": []}]}}, "/api/v1/matching/analytics/conversion": {"post": {"operationId": "MatchingAnalyticsController_recordConversion", "summary": "Record a matching conversion event", "parameters": [], "responses": {"201": {"description": "Event recorded successfully"}}, "tags": ["matching-analytics"], "security": [{"bearer": []}]}}, "/api/v1/matching/analytics/dashboard": {"get": {"operationId": "MatchingAnalyticsController_getDashboardData", "summary": "Get matching analytics dashboard data", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Period (day, week, month, year)", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns matching analytics dashboard data"}}, "tags": ["matching-analytics"], "security": [{"bearer": []}]}}, "/api/v1/matching/analytics/trends": {"get": {"operationId": "MatchingAnalyticsController_getTrendsData", "summary": "Get matching trends data", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Period (day, week, month, year)", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns matching trends data"}}, "tags": ["matching-analytics"], "security": [{"bearer": []}]}}, "/api/v1/matching/analytics/performance": {"get": {"operationId": "MatchingAnalyticsController_getPerformanceData", "summary": "Get matching performance data", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Period (day, week, month, year)", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns matching performance data"}}, "tags": ["matching-analytics"], "security": [{"bearer": []}]}}, "/api/v1/matching/recommendations/partner/{partnerId}": {"get": {"operationId": "MatchingRecommendationController_getPartnerRecommendations", "summary": "Get recommendations for a partner", "parameters": [{"name": "partnerId", "required": true, "in": "path", "description": "Partner ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns recommendations for the partner"}}, "tags": ["matching-recommendations"], "security": [{"bearer": []}]}}, "/api/v1/matching/recommendations/generate/partner/{partnerId}": {"post": {"operationId": "MatchingRecommendationController_generatePartnerRecommendations", "summary": "Generate recommendations for a partner", "parameters": [{"name": "partnerId", "required": true, "in": "path", "description": "Partner ID", "schema": {"type": "string"}}], "responses": {"201": {"description": "Recommendations generated successfully"}}, "tags": ["matching-recommendations"], "security": [{"bearer": []}]}}, "/api/v1/matching/recommendations/generate/retreat/{retreatId}": {"post": {"operationId": "MatchingRecommendationController_generateRetreatRecommendations", "summary": "Generate recommendations for a retreat", "parameters": [{"name": "retreatId", "required": true, "in": "path", "description": "Retreat ID", "schema": {"type": "string"}}], "responses": {"201": {"description": "Recommendations generated successfully"}}, "tags": ["matching-recommendations"], "security": [{"bearer": []}]}}, "/api/v1/matching/recommendations/generate/all-partners": {"post": {"operationId": "MatchingRecommendationController_generateAllPartnerRecommendations", "summary": "Generate recommendations for all partners", "parameters": [], "responses": {"201": {"description": "Recommendations generation started"}}, "tags": ["matching-recommendations"], "security": [{"bearer": []}]}}, "/api/v1/matching/recommendations/generate/all-retreats": {"post": {"operationId": "MatchingRecommendationController_generateAllRetreatRecommendations", "summary": "Generate recommendations for all retreats", "parameters": [], "responses": {"201": {"description": "Recommendations generation started"}}, "tags": ["matching-recommendations"], "security": [{"bearer": []}]}}, "/api/v1/matching/export/partner/{partnerId}": {"get": {"operationId": "MatchingExportController_exportPartnerMatchings", "summary": "Export matching results for a partner", "parameters": [{"name": "partnerId", "required": true, "in": "path", "description": "Partner ID", "schema": {"type": "string"}}, {"name": "format", "required": false, "in": "query", "description": "Export format (csv, excel, json)", "schema": {}}], "responses": {"200": {"description": "Returns the exported file"}}, "tags": ["matching-export"], "security": [{"bearer": []}]}, "post": {"operationId": "MatchingExportController_exportPartnerMatchingsWithCriteria", "summary": "Export matching results for a partner with custom criteria", "parameters": [{"name": "partnerId", "required": true, "in": "path", "description": "Partner ID", "schema": {"type": "string"}}, {"name": "format", "required": false, "in": "query", "description": "Export format (csv, excel, json)", "schema": {}}], "responses": {"200": {"description": "Returns the exported file"}}, "tags": ["matching-export"], "security": [{"bearer": []}]}}, "/api/v1/matching/export/retreat/{retreatId}": {"get": {"operationId": "MatchingExportController_exportRetreatMatchings", "summary": "Export matching results for a retreat", "parameters": [{"name": "retreatId", "required": true, "in": "path", "description": "Retreat ID", "schema": {"type": "string"}}, {"name": "format", "required": false, "in": "query", "description": "Export format (csv, excel, json)", "schema": {}}], "responses": {"200": {"description": "Returns the exported file"}}, "tags": ["matching-export"], "security": [{"bearer": []}]}, "post": {"operationId": "MatchingExportController_exportRetreatMatchingsWithCriteria", "summary": "Export matching results for a retreat with custom criteria", "parameters": [{"name": "retreatId", "required": true, "in": "path", "description": "Retreat ID", "schema": {"type": "string"}}, {"name": "format", "required": false, "in": "query", "description": "Export format (csv, excel, json)", "schema": {}}], "responses": {"200": {"description": "Returns the exported file"}}, "tags": ["matching-export"], "security": [{"bearer": []}]}}, "/api/v1/matching/messaging/partner-to-organizer": {"post": {"operationId": "MatchingMessagingController_sendPartnerToOrganizerMessage", "summary": "Send a message from partner to retreat organizer", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendMessageDto"}}}}, "responses": {"201": {"description": "Message sent successfully"}}, "tags": ["matching-messaging"], "security": [{"bearer": []}]}}, "/api/v1/matching/messaging/organizer-to-partner": {"post": {"operationId": "MatchingMessagingController_sendOrganizerToPartnerMessage", "summary": "Send a message from retreat organizer to partner", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendMessageDto"}}}}, "responses": {"201": {"description": "Message sent successfully"}}, "tags": ["matching-messaging"], "security": [{"bearer": []}]}}, "/api/v1/matching/messaging/follow-up/{conversationId}": {"post": {"operationId": "MatchingMessagingController_sendFollowUpMessage", "summary": "Send a follow-up message in an existing conversation", "parameters": [{"name": "conversationId", "required": true, "in": "path", "description": "Conversation ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendFollowUpDto"}}}}, "responses": {"201": {"description": "Message sent successfully"}}, "tags": ["matching-messaging"], "security": [{"bearer": []}]}}, "/api/v1/matching/messaging/contact-from-matching/{partnerId}/{retreatId}": {"post": {"operationId": "MatchingMessagingController_contactFromMatching", "summary": "Contact from matching details page", "parameters": [{"name": "partnerId", "required": true, "in": "path", "description": "Partner ID", "schema": {"type": "string"}}, {"name": "retreatId", "required": true, "in": "path", "description": "Retreat ID", "schema": {"type": "string"}}], "responses": {"201": {"description": "Contact initiated successfully"}}, "tags": ["matching-messaging"], "security": [{"bearer": []}]}}, "/api/v1/matching/booking/create": {"post": {"operationId": "MatchingBookingController_createBookingFromMatching", "summary": "Create a booking from a matching", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBookingDto"}}}}, "responses": {"201": {"description": "Booking created successfully"}}, "tags": ["matching-booking"], "security": [{"bearer": []}]}}, "/api/v1/matching/booking/check/{partnerId}/{retreatId}": {"get": {"operationId": "MatchingBookingController_checkExistingBooking", "summary": "Check if a booking already exists for a matching", "parameters": [{"name": "partnerId", "required": true, "in": "path", "description": "Partner ID", "schema": {"type": "string"}}, {"name": "retreatId", "required": true, "in": "path", "description": "Retreat ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns booking information if it exists"}}, "tags": ["matching-booking"], "security": [{"bearer": []}]}}, "/api/v1/matching/booking/conversion-stats": {"get": {"operationId": "MatchingBookingController_getConversionStats", "summary": "Get matching to booking conversion statistics", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Period (day, week, month, year)", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns conversion statistics"}}, "tags": ["matching-booking"], "security": [{"bearer": []}]}}, "/api/v1/matching/booking/conversion-stats/category": {"get": {"operationId": "MatchingBookingController_getConversionStatsByCategory", "summary": "Get matching to booking conversion statistics by partner category", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Period (day, week, month, year)", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns conversion statistics by category"}}, "tags": ["matching-booking"], "security": [{"bearer": []}]}}, "/api/v1/matching/booking/booking-from-matching/{bookingId}": {"get": {"operationId": "MatchingBookingController_getBookingFromMatching", "summary": "Get information about a booking created from a matching", "parameters": [{"name": "bookingId", "required": true, "in": "path", "description": "Booking ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns booking information with matching details"}}, "tags": ["matching-booking"], "security": [{"bearer": []}]}}, "/api/v1/matching/video/rooms": {"post": {"operationId": "MatchingVideoController_createVideoRoom", "summary": "Create a video conference room for a matching", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateVideoRoomDto"}}}}, "responses": {"201": {"description": "Video room created successfully"}}, "tags": ["matching-video"], "security": [{"bearer": []}]}}, "/api/v1/matching/video/rooms/{partnerId}/{retreatId}": {"get": {"operationId": "MatchingVideoController_getVideoRooms", "summary": "Get video rooms for a matching", "parameters": [{"name": "partnerId", "required": true, "in": "path", "description": "Partner ID", "schema": {"type": "string"}}, {"name": "retreatId", "required": true, "in": "path", "description": "Retreat ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns video rooms for the matching"}}, "tags": ["matching-video"], "security": [{"bearer": []}]}}, "/api/v1/matching/video/rooms/details/{roomId}": {"get": {"operationId": "MatchingVideoController_getVideoRoomDetails", "summary": "Get details of a video room", "parameters": [{"name": "roomId", "required": true, "in": "path", "description": "Room ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns details of the video room"}}, "tags": ["matching-video"], "security": [{"bearer": []}]}}, "/api/v1/matching/video/join/{roomId}": {"get": {"operationId": "MatchingVideoController_generateJoinLink", "summary": "Generate a join link for a video room", "parameters": [{"name": "roomId", "required": true, "in": "path", "description": "Room ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a join link for the video room"}}, "tags": ["matching-video"], "security": [{"bearer": []}]}}, "/api/v1/matching/video/rooms/{roomId}/end": {"post": {"operationId": "MatchingVideoController_endVideoRoom", "summary": "End a video room", "parameters": [{"name": "roomId", "required": true, "in": "path", "description": "Room ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Video room ended successfully"}}, "tags": ["matching-video"], "security": [{"bearer": []}]}}, "/api/v1/social/livestream": {"get": {"operationId": "LivestreamController_getLivestreams", "summary": "Get all livestreams", "parameters": [{"name": "offset", "required": false, "in": "query", "description": "Offset for pagination", "schema": {}}, {"name": "limit", "required": false, "in": "query", "description": "Limit the number of results", "schema": {}}, {"name": "hostId", "required": false, "in": "query", "description": "Filter by host ID", "schema": {}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status (scheduled, live, ended, cancelled)", "schema": {}}], "responses": {"200": {"description": "Returns all livestreams"}}, "tags": ["social-livestream"], "security": [{"bearer": []}]}, "post": {"operationId": "LivestreamController_createLivestream", "summary": "Create a new livestream", "parameters": [], "responses": {"201": {"description": "Livestream created successfully"}}, "tags": ["social-livestream"], "security": [{"bearer": []}]}}, "/api/v1/social/livestream/{id}": {"get": {"operationId": "LivestreamController_getLivestreamById", "summary": "Get a livestream by ID", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Livestream ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the livestream"}, "404": {"description": "Livestream not found"}}, "tags": ["social-livestream"], "security": [{"bearer": []}]}, "patch": {"operationId": "LivestreamController_updateLivestream", "summary": "Update a livestream", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Livestream ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Livestream updated successfully"}, "404": {"description": "Livestream not found"}}, "tags": ["social-livestream"], "security": [{"bearer": []}]}}, "/api/v1/social/livestream/{id}/start": {"post": {"operationId": "LivestreamController_startLivestream", "summary": "Start a livestream", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Livestream ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Livestream started successfully"}, "404": {"description": "Livestream not found"}}, "tags": ["social-livestream"], "security": [{"bearer": []}]}}, "/api/v1/social/livestream/{id}/end": {"post": {"operationId": "LivestreamController_endLivestream", "summary": "End a livestream", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Livestream ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Livestream ended successfully"}, "404": {"description": "Livestream not found"}}, "tags": ["social-livestream"], "security": [{"bearer": []}]}}, "/api/v1/social/livestream/{id}/messages": {"get": {"operationId": "LivestreamController_getLivestreamMessages", "summary": "Get messages for a livestream", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Livestream ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the livestream messages"}, "404": {"description": "Livestream not found"}}, "tags": ["social-livestream"], "security": [{"bearer": []}]}, "post": {"operationId": "LivestreamController_sendLivestreamMessage", "summary": "Send a message to a livestream", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Livestream ID", "schema": {"type": "string"}}], "responses": {"201": {"description": "Message sent successfully"}, "404": {"description": "Livestream not found"}}, "tags": ["social-livestream"], "security": [{"bearer": []}]}}, "/api/v1/social/blog": {"get": {"operationId": "BlogController_getBlogPosts", "summary": "Get all blog posts", "parameters": [{"name": "offset", "required": false, "in": "query", "description": "Offset for pagination", "schema": {}}, {"name": "limit", "required": false, "in": "query", "description": "Limit the number of results", "schema": {}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status (draft, published, archived)", "schema": {}}, {"name": "authorId", "required": false, "in": "query", "description": "Filter by author ID", "schema": {}}, {"name": "tags", "required": false, "in": "query", "description": "Filter by tags (comma-separated)", "schema": {}}], "responses": {"200": {"description": "Returns all blog posts"}}, "tags": ["social-blog"], "security": [{"bearer": []}]}, "post": {"operationId": "BlogController_createBlogPost", "summary": "Create a new blog post", "parameters": [], "responses": {"201": {"description": "Blog post created successfully"}}, "tags": ["social-blog"], "security": [{"bearer": []}]}}, "/api/v1/social/blog/{id}": {"get": {"operationId": "BlogController_getBlogPostById", "summary": "Get a blog post by ID", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Blog post ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the blog post"}, "404": {"description": "Blog post not found"}}, "tags": ["social-blog"], "security": [{"bearer": []}]}, "patch": {"operationId": "BlogController_updateBlogPost", "summary": "Update a blog post", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Blog post ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Blog post updated successfully"}, "404": {"description": "Blog post not found"}}, "tags": ["social-blog"], "security": [{"bearer": []}]}, "delete": {"operationId": "BlogController_deleteBlogPost", "summary": "Delete a blog post", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Blog post ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Blog post deleted successfully"}, "404": {"description": "Blog post not found"}}, "tags": ["social-blog"], "security": [{"bearer": []}]}}, "/api/v1/social/blog/{id}/comments": {"get": {"operationId": "BlogController_getBlogPostComments", "summary": "Get comments for a blog post", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Blog post ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the blog post comments"}, "404": {"description": "Blog post not found"}}, "tags": ["social-blog"], "security": [{"bearer": []}]}, "post": {"operationId": "BlogController_addBlogPostComment", "summary": "Add a comment to a blog post", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Blog post ID", "schema": {"type": "string"}}], "responses": {"201": {"description": "Comment added successfully"}, "404": {"description": "Blog post not found"}}, "tags": ["social-blog"], "security": [{"bearer": []}]}}, "/api/v1/social/blog/{id}/like": {"post": {"operationId": "BlogController_likeBlogPost", "summary": "Like a blog post", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Blog post ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Blog post liked successfully"}, "404": {"description": "Blog post not found"}}, "tags": ["social-blog"], "security": [{"bearer": []}]}}, "/api/v1/social/blog/{id}/unlike": {"post": {"operationId": "BlogController_unlikeBlogPost", "summary": "Unlike a blog post", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Blog post ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Blog post unliked successfully"}, "404": {"description": "Blog post not found"}}, "tags": ["social-blog"], "security": [{"bearer": []}]}}, "/api/v1/social/analytics": {"get": {"operationId": "SocialAnalyticsController_getSocialAnalytics", "summary": "Get social analytics", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Period (day, week, month, year)", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns social analytics"}}, "tags": ["social-analytics"], "security": [{"bearer": []}]}}, "/api/v1/social/analytics/livestreams/{id}": {"get": {"operationId": "SocialAnalyticsController_getLivestreamAnalytics", "summary": "Get analytics for a specific livestream", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Livestream ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns livestream analytics"}, "404": {"description": "Livestream not found"}}, "tags": ["social-analytics"], "security": [{"bearer": []}]}}, "/api/v1/social/analytics/blog/{id}": {"get": {"operationId": "SocialAnalyticsController_getBlogPostAnalytics", "summary": "Get analytics for a specific blog post", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Blog post ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns blog post analytics"}, "404": {"description": "Blog post not found"}}, "tags": ["social-analytics"], "security": [{"bearer": []}]}}, "/api/v1/social/analytics/users/{id}": {"get": {"operationId": "SocialAnalyticsController_getUserAnalytics", "summary": "Get analytics for a specific user", "parameters": [{"name": "id", "required": true, "in": "path", "description": "User ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns user analytics"}, "404": {"description": "User not found"}}, "tags": ["social-analytics"], "security": [{"bearer": []}]}}, "/api/v1/social/analytics/popular": {"get": {"operationId": "SocialAnalyticsController_getPopularContent", "summary": "Get popular content", "parameters": [{"name": "type", "required": false, "in": "query", "description": "Content type (livestream, blog, video)", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Limit the number of results", "schema": {"type": "number"}}, {"name": "period", "required": false, "in": "query", "description": "Period (day, week, month, year)", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns popular content"}}, "tags": ["social-analytics"], "security": [{"bearer": []}]}}, "/api/v1/social/analytics/events": {"post": {"operationId": "SocialAnalyticsController_trackEvent", "summary": "Track an analytics event", "parameters": [], "responses": {"201": {"description": "Event tracked successfully"}}, "tags": ["social-analytics"], "security": [{"bearer": []}]}}, "/api/v1/social/analytics/engagement": {"get": {"operationId": "SocialAnalyticsController_getEngagementTrends", "summary": "Get engagement trends", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Period (day, week, month, year)", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns engagement trends"}}, "tags": ["social-analytics"], "security": [{"bearer": []}]}}}, "info": {"title": "Retreat And Be API", "description": "The Retreat And Be API documentation", "version": "1.0", "contact": {}}, "tags": [{"name": "auth", "description": "Authentication endpoints"}, {"name": "users", "description": "User management endpoints"}, {"name": "courses", "description": "Course management endpoints"}, {"name": "lessons", "description": "Lesson management endpoints"}, {"name": "enrollments", "description": "Enrollment management endpoints"}, {"name": "game-systems", "description": "Game system management endpoints"}, {"name": "game-levels", "description": "Game level management endpoints"}, {"name": "quests", "description": "Quest management endpoints"}, {"name": "player-progress", "description": "Player progress management endpoints"}, {"name": "events", "description": "Event management endpoints"}, {"name": "health", "description": "Health check endpoints"}], "servers": [], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"LoginDto": {"type": "object", "properties": {"email": {"type": "string", "description": "Email de l'utilisateur", "example": "<EMAIL>"}, "password": {"type": "string", "description": "Mot de passe de l'utilisateur", "example": "Password123!"}, "twoFactorCode": {"type": "string", "description": "Code d'authentification à deux facteurs", "example": "123456"}}, "required": ["email", "password"]}, "RefreshTokenDto": {"type": "object", "properties": {"refreshToken": {"type": "string", "description": "Token de rafraîchissement", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}, "required": ["refreshToken"]}, "TwoFactorDto": {"type": "object", "properties": {"twoFactorCode": {"type": "string", "description": "Code d'authentification à deux facteurs", "example": "123456"}}, "required": ["twoFactorCode"]}, "CreateUserDto": {"type": "object", "properties": {"email": {"type": "string", "description": "Email de l'utilisateur", "example": "<EMAIL>"}, "password": {"type": "string", "description": "Mot de passe de l'utilisateur", "example": "Password123!", "minLength": 8}, "name": {"type": "string", "description": "Nom de l'utilisateur", "example": "<PERSON>"}, "role": {"type": "string", "description": "Rôle de l'utilisateur", "enum": ["USER", "ADMIN", "MODERATOR", "PARTNER", "HOST", "ORGANIZER", "PARTICIPANT"], "default": "USER"}}, "required": ["email", "password"]}, "UpdateUserDto": {"type": "object", "properties": {"email": {"type": "string", "description": "Email de l'utilisateur", "example": "<EMAIL>"}, "password": {"type": "string", "description": "Mot de passe de l'utilisateur", "example": "Password123!", "minLength": 8}, "name": {"type": "string", "description": "Nom de l'utilisateur", "example": "<PERSON>"}, "role": {"type": "string", "description": "Rôle de l'utilisateur", "enum": ["USER", "ADMIN", "MODERATOR", "PARTNER", "HOST", "ORGANIZER", "PARTICIPANT"], "default": "USER"}, "isActive": {"type": "boolean", "description": "Indique si l'utilisateur est actif", "example": true}, "isVerified": {"type": "boolean", "description": "Indique si l'utilisateur est vérifié", "example": true}}}, "CreateRetreatDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Title of the retreat", "example": "Yoga Retreat in Bali"}, "description": {"type": "string", "description": "Description of the retreat", "example": "A relaxing yoga retreat in the heart of Bali"}, "location": {"type": "string", "description": "Location of the retreat", "example": "Ubud, Bali, Indonesia"}, "startDate": {"type": "string", "description": "Start date of the retreat", "example": "2023-06-15T00:00:00.000Z"}, "endDate": {"type": "string", "description": "End date of the retreat", "example": "2023-06-22T00:00:00.000Z"}, "price": {"type": "number", "description": "Price of the retreat", "example": 1500}, "capacity": {"type": "number", "description": "Maximum number of participants", "example": 20}, "status": {"type": "string", "description": "Status of the retreat", "enum": ["DRAFT", "PUBLISHED", "ARCHIVED"], "default": "DRAFT", "example": "DRAFT"}, "categories": {"description": "Categories of the retreat", "example": ["yoga", "meditation", "wellness"], "type": "array", "items": {"type": "string"}}, "amenities": {"description": "Amenities offered during the retreat", "example": ["wifi", "pool", "spa", "organic meals"], "type": "array", "items": {"type": "string"}}, "images": {"description": "Images of the retreat", "example": ["/uploads/retreats/1/image1.jpg", "/uploads/retreats/1/image2.jpg"], "type": "array", "items": {"type": "string"}}}, "required": ["title", "description", "location", "startDate", "endDate", "price", "capacity"]}, "HostDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID of the host", "example": "123e4567-e89b-12d3-a456-************"}, "firstName": {"type": "string", "description": "First name of the host", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Last name of the host", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "description": "Email of the host", "example": "<EMAIL>"}, "image": {"type": "string", "description": "Profile picture of the host", "example": "/uploads/users/123/profile.jpg"}}, "required": ["id", "firstName", "lastName", "email"]}, "RetreatResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID of the retreat", "example": "123e4567-e89b-12d3-a456-************"}, "title": {"type": "string", "description": "Title of the retreat", "example": "Yoga Retreat in Bali"}, "description": {"type": "string", "description": "Description of the retreat", "example": "A relaxing yoga retreat in the heart of Bali"}, "location": {"type": "string", "description": "Location of the retreat", "example": "Ubud, Bali, Indonesia"}, "startDate": {"format": "date-time", "type": "string", "description": "Start date of the retreat", "example": "2023-06-15T00:00:00.000Z"}, "endDate": {"format": "date-time", "type": "string", "description": "End date of the retreat", "example": "2023-06-22T00:00:00.000Z"}, "price": {"type": "number", "description": "Price of the retreat", "example": 1500}, "capacity": {"type": "number", "description": "Maximum number of participants", "example": 20}, "status": {"type": "string", "description": "Status of the retreat", "enum": ["DRAFT", "PUBLISHED", "ARCHIVED"], "example": "PUBLISHED"}, "categories": {"description": "Categories of the retreat", "example": ["yoga", "meditation", "wellness"], "type": "array", "items": {"type": "string"}}, "amenities": {"description": "Amenities offered during the retreat", "example": ["wifi", "pool", "spa", "organic meals"], "type": "array", "items": {"type": "string"}}, "images": {"description": "Images of the retreat", "example": ["/uploads/retreats/1/image1.jpg", "/uploads/retreats/1/image2.jpg"], "type": "array", "items": {"type": "string"}}, "host": {"description": "Host of the retreat", "allOf": [{"$ref": "#/components/schemas/HostDto"}]}, "createdAt": {"format": "date-time", "type": "string", "description": "Creation date of the retreat", "example": "2023-01-01T00:00:00.000Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Last update date of the retreat", "example": "2023-01-02T00:00:00.000Z"}}, "required": ["id", "title", "description", "location", "startDate", "endDate", "price", "capacity", "status", "categories", "amenities", "images", "host", "createdAt", "updatedAt"]}, "UpdateRetreatDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Title of the retreat", "example": "Yoga Retreat in Bali"}, "description": {"type": "string", "description": "Description of the retreat", "example": "A relaxing yoga retreat in the heart of Bali"}, "location": {"type": "string", "description": "Location of the retreat", "example": "Ubud, Bali, Indonesia"}, "startDate": {"type": "string", "description": "Start date of the retreat", "example": "2023-06-15T00:00:00.000Z"}, "endDate": {"type": "string", "description": "End date of the retreat", "example": "2023-06-22T00:00:00.000Z"}, "price": {"type": "number", "description": "Price of the retreat", "example": 1500}, "capacity": {"type": "number", "description": "Maximum number of participants", "example": 20}, "status": {"type": "string", "description": "Status of the retreat", "enum": ["DRAFT", "PUBLISHED", "ARCHIVED"], "example": "PUBLISHED"}, "categories": {"description": "Categories of the retreat", "example": ["yoga", "meditation", "wellness"], "type": "array", "items": {"type": "string"}}, "amenities": {"description": "Amenities offered during the retreat", "example": ["wifi", "pool", "spa", "organic meals"], "type": "array", "items": {"type": "string"}}, "images": {"description": "Images of the retreat", "example": ["/uploads/retreats/1/image1.jpg", "/uploads/retreats/1/image2.jpg"], "type": "array", "items": {"type": "string"}}}}, "CreateBookingDto": {"type": "object", "properties": {}}, "UserDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID of the user", "example": "123e4567-e89b-12d3-a456-************"}, "firstName": {"type": "string", "description": "First name of the user", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Last name of the user", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "description": "Email of the user", "example": "<EMAIL>"}, "image": {"type": "string", "description": "Profile picture of the user", "example": "/uploads/users/123/profile.jpg"}}, "required": ["id", "firstName", "lastName", "email"]}, "RetreatDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID of the retreat", "example": "123e4567-e89b-12d3-a456-************"}, "title": {"type": "string", "description": "Title of the retreat", "example": "Yoga Retreat in Bali"}, "description": {"type": "string", "description": "Description of the retreat", "example": "A relaxing yoga retreat in the heart of Bali"}, "location": {"type": "string", "description": "Location of the retreat", "example": "Ubud, Bali, Indonesia"}, "startDate": {"format": "date-time", "type": "string", "description": "Start date of the retreat", "example": "2023-06-15T00:00:00.000Z"}, "endDate": {"format": "date-time", "type": "string", "description": "End date of the retreat", "example": "2023-06-22T00:00:00.000Z"}, "price": {"type": "number", "description": "Price of the retreat", "example": 1500}, "host": {"description": "Host of the retreat", "allOf": [{"$ref": "#/components/schemas/HostDto"}]}, "images": {"description": "Images of the retreat", "example": ["/uploads/retreats/1/image1.jpg", "/uploads/retreats/1/image2.jpg"], "type": "array", "items": {"type": "string"}}}, "required": ["id", "title", "description", "location", "startDate", "endDate", "price", "host", "images"]}, "BookingResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID of the booking", "example": "123e4567-e89b-12d3-a456-************"}, "retreatId": {"type": "string", "description": "ID of the retreat", "example": "123e4567-e89b-12d3-a456-************"}, "userId": {"type": "string", "description": "ID of the user", "example": "123e4567-e89b-12d3-a456-************"}, "participants": {"type": "number", "description": "Number of participants", "example": 2}, "totalPrice": {"type": "number", "description": "Total price of the booking", "example": 3000}, "status": {"type": "string", "description": "Status of the booking", "example": "PENDING", "enum": ["PENDING", "CONFIRMED", "CANCELLED", "COMPLETED", "REFUNDED"]}, "specialRequests": {"type": "string", "description": "Special requests or notes for the booking", "example": "I have dietary restrictions (vegan). Please also arrange airport pickup if possible."}, "createdAt": {"format": "date-time", "type": "string", "description": "Creation date of the booking", "example": "2023-01-01T00:00:00.000Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Last update date of the booking", "example": "2023-01-02T00:00:00.000Z"}, "confirmedAt": {"format": "date-time", "type": "string", "description": "Confirmation date of the booking", "example": "2023-01-02T00:00:00.000Z"}, "cancelledAt": {"format": "date-time", "type": "string", "description": "Cancellation date of the booking", "example": "2023-01-03T00:00:00.000Z"}, "completedAt": {"format": "date-time", "type": "string", "description": "Completion date of the booking", "example": "2023-06-22T00:00:00.000Z"}, "refundedAt": {"format": "date-time", "type": "string", "description": "Refund date of the booking", "example": "2023-01-04T00:00:00.000Z"}, "user": {"description": "User who made the booking", "allOf": [{"$ref": "#/components/schemas/UserDto"}]}, "retreat": {"description": "Retreat that was booked", "allOf": [{"$ref": "#/components/schemas/RetreatDto"}]}}, "required": ["id", "retreatId", "userId", "participants", "totalPrice", "status", "createdAt", "updatedAt", "user", "retreat"]}, "UpdateBookingDto": {"type": "object", "properties": {"participants": {"type": "number", "description": "Number of participants", "example": 2, "minimum": 1, "maximum": 20}, "specialRequests": {"type": "string", "description": "Special requests or notes for the booking", "example": "I have dietary restrictions (vegan). Please also arrange airport pickup if possible."}, "status": {"type": "string", "description": "Status of the booking", "enum": ["PENDING", "CONFIRMED", "CANCELLED", "COMPLETED", "REFUNDED"], "example": "CONFIRMED"}}}, "CreatePaymentIntentDto": {"type": "object", "properties": {"bookingId": {"type": "string", "description": "ID of the booking to pay for", "example": "123e4567-e89b-12d3-a456-************"}}, "required": ["bookingId"]}, "ConfirmPaymentDto": {"type": "object", "properties": {"paymentIntentId": {"type": "string", "description": "ID of the payment intent to confirm", "example": "pi_3NqLkSJHR94LkQpZ1gEDz7Xm"}, "paymentMethodId": {"type": "string", "description": "ID of the payment method to use", "example": "pm_1NqLkSJHR94LkQpZ1gEDz7Xm"}}, "required": ["paymentIntentId", "paymentMethodId"]}, "AddPaymentMethodDto": {"type": "object", "properties": {"paymentMethodId": {"type": "string", "description": "ID of the payment method to add", "example": "pm_1NqLkSJHR94LkQpZ1gEDz7Xm"}}, "required": ["paymentMethodId"]}, "RequestRefundDto": {"type": "object", "properties": {"paymentId": {"type": "string", "description": "ID of the payment to refund", "example": "py_3NqLkSJHR94LkQpZ1gEDz7Xm"}, "amount": {"type": "number", "description": "Amount to refund (in cents). If not provided, full amount will be refunded.", "example": 10000}, "reason": {"type": "string", "description": "Reason for the refund", "example": "Customer requested cancellation"}}, "required": ["paymentId"]}, "ApplyPromoCodeDto": {"type": "object", "properties": {"bookingId": {"type": "string", "description": "ID of the booking to apply the promo code to", "example": "123e4567-e89b-12d3-a456-************"}, "promoCode": {"type": "string", "description": "Promo code to apply", "example": "SUMMER2023"}}, "required": ["bookingId", "promoCode"]}, "CreateGameSystemDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Nom du système de jeu", "example": "Système de récompenses pour retraites", "minLength": 3, "maxLength": 100}, "description": {"type": "string", "description": "Description du système de jeu", "example": "Système de gamification pour encourager la participation aux retraites", "minLength": 10}, "rules": {"type": "object", "description": "Règles du système de jeu au format JSON", "example": {"pointsPerActivity": 10, "levelsRequired": 5, "badgesEnabled": true, "leaderboardEnabled": true}}}, "required": ["name", "description", "rules"]}, "UpdateGameSystemDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Nom du système de jeu", "example": "Système de récompenses pour retraites", "minLength": 3, "maxLength": 100}, "description": {"type": "string", "description": "Description du système de jeu", "example": "Système de gamification pour encourager la participation aux retraites", "minLength": 10}, "rules": {"type": "object", "description": "Règles du système de jeu au format JSON", "example": {"pointsPerActivity": 10, "levelsRequired": 5, "badgesEnabled": true, "leaderboardEnabled": true}}}}, "CreateGameLevelDto": {"type": "object", "properties": {"systemId": {"type": "string", "description": "ID du système de jeu", "example": "123e4567-e89b-12d3-a456-************"}, "level": {"type": "number", "description": "Numéro du niveau", "example": 1, "minimum": 1}, "name": {"type": "string", "description": "Nom du niveau", "example": "Débutant", "minLength": 2, "maxLength": 50}, "description": {"type": "string", "description": "Description du niveau", "example": "Niveau débutant pour les nouveaux participants", "minLength": 10}, "xpRequired": {"type": "number", "description": "Points d'expérience requis pour atteindre ce niveau", "example": 100, "minimum": 0}, "rewards": {"type": "object", "description": "Récompenses du niveau au format JSON", "example": {"badge": "debutant", "points": 50, "unlocks": ["feature1", "feature2"]}}}, "required": ["systemId", "level", "name", "description", "xpRequired", "rewards"]}, "UpdateGameLevelDto": {"type": "object", "properties": {"systemId": {"type": "string", "description": "ID du système de jeu", "example": "123e4567-e89b-12d3-a456-************"}, "level": {"type": "number", "description": "Numéro du niveau", "example": 1, "minimum": 1}, "name": {"type": "string", "description": "Nom du niveau", "example": "Débutant", "minLength": 2, "maxLength": 50}, "description": {"type": "string", "description": "Description du niveau", "example": "Niveau débutant pour les nouveaux participants", "minLength": 10}, "xpRequired": {"type": "number", "description": "Points d'expérience requis pour atteindre ce niveau", "example": 100, "minimum": 0}, "rewards": {"type": "object", "description": "Récompenses du niveau au format JSON", "example": {"badge": "debutant", "points": 50, "unlocks": ["feature1", "feature2"]}}}}, "CreateQuestDto": {"type": "object", "properties": {"systemId": {"type": "string", "description": "ID du système de jeu", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Nom de la quête", "example": "Première méditation", "minLength": 3, "maxLength": 100}, "description": {"type": "string", "description": "Description de la quête", "example": "Complétez votre première session de méditation guidée", "minLength": 10}, "objectives": {"type": "object", "description": "Objectifs de la quête au format JSON", "example": {"type": "meditation", "count": 1, "duration": 10, "requirements": ["audio", "quiet_space"]}}, "rewards": {"type": "object", "description": "Récompenses de la quête au format JSON", "example": {"xp": 50, "badge": "first_meditation", "unlocks": ["guided_meditation_level_2"]}}, "status": {"type": "string", "description": "Statut de la quête", "example": "DRAFT", "enum": ["DRAFT", "PUBLISHED", "ARCHIVED"], "default": "DRAFT"}}, "required": ["systemId", "name", "description", "objectives", "rewards"]}, "UpdateQuestDto": {"type": "object", "properties": {"systemId": {"type": "string", "description": "ID du système de jeu", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Nom de la quête", "example": "Première méditation", "minLength": 3, "maxLength": 100}, "description": {"type": "string", "description": "Description de la quête", "example": "Complétez votre première session de méditation guidée", "minLength": 10}, "objectives": {"type": "object", "description": "Objectifs de la quête au format JSON", "example": {"type": "meditation", "count": 1, "duration": 10, "requirements": ["audio", "quiet_space"]}}, "rewards": {"type": "object", "description": "Récompenses de la quête au format JSON", "example": {"xp": 50, "badge": "first_meditation", "unlocks": ["guided_meditation_level_2"]}}, "status": {"type": "string", "description": "Statut de la quête", "example": "DRAFT", "enum": ["DRAFT", "PUBLISHED", "ARCHIVED"], "default": "DRAFT"}}}, "CreatePlayerProgressDto": {"type": "object", "properties": {"playerId": {"type": "string", "description": "ID du joueur", "example": "123e4567-e89b-12d3-a456-************"}, "levelId": {"type": "string", "description": "ID du niveau", "example": "123e4567-e89b-12d3-a456-************"}, "xp": {"type": "number", "description": "Points d'expérience", "example": 50, "minimum": 0, "default": 0}, "status": {"type": "string", "description": "Statut de la progression", "example": "IN_PROGRESS", "enum": ["NOT_STARTED", "IN_PROGRESS", "COMPLETED", "FAILED"], "default": "IN_PROGRESS"}}, "required": ["playerId", "levelId"]}, "UpdatePlayerProgressDto": {"type": "object", "properties": {"playerId": {"type": "string", "description": "ID du joueur", "example": "123e4567-e89b-12d3-a456-************"}, "levelId": {"type": "string", "description": "ID du niveau", "example": "123e4567-e89b-12d3-a456-************"}, "xp": {"type": "number", "description": "Points d'expérience", "example": 50, "minimum": 0, "default": 0}, "status": {"type": "string", "description": "Statut de la progression", "example": "IN_PROGRESS", "enum": ["NOT_STARTED", "IN_PROGRESS", "COMPLETED", "FAILED"], "default": "IN_PROGRESS"}}}, "AddXpDto": {"type": "object", "properties": {"xp": {"type": "number", "description": "Points d'expérience à ajouter", "example": 50, "minimum": 1}}, "required": ["xp"]}, "CreateCourseDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Titre du cours", "example": "Introduction à la méditation", "minLength": 3, "maxLength": 100}, "description": {"type": "string", "description": "Description du cours", "example": "Un cours complet pour apprendre les bases de la méditation", "minLength": 10}, "level": {"type": "string", "description": "Niveau du cours", "example": "BEGINNER", "enum": ["BEGINNER", "INTERMEDIATE", "ADVANCED", "EXPERT"]}, "coverImage": {"type": "string", "description": "URL de l'image de couverture", "example": "https://example.com/images/meditation.jpg"}, "category": {"type": "string", "description": "Catégorie du cours", "example": "Méditation"}}, "required": ["title", "description"]}, "UpdateCourseDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Titre du cours", "example": "Introduction à la méditation", "minLength": 3, "maxLength": 100}, "description": {"type": "string", "description": "Description du cours", "example": "Un cours complet pour apprendre les bases de la méditation", "minLength": 10}, "level": {"type": "string", "description": "Niveau du cours", "example": "BEGINNER", "enum": ["BEGINNER", "INTERMEDIATE", "ADVANCED", "EXPERT"]}, "coverImage": {"type": "string", "description": "URL de l'image de couverture", "example": "https://example.com/images/meditation.jpg"}, "category": {"type": "string", "description": "Catégorie du cours", "example": "Méditation"}}}, "CreateLessonDto": {"type": "object", "properties": {"courseId": {"type": "string", "description": "ID du cours", "example": "123e4567-e89b-12d3-a456-************"}, "title": {"type": "string", "description": "Titre de la leçon", "example": "Introduction à la respiration consciente", "minLength": 3, "maxLength": 100}, "content": {"type": "string", "description": "Contenu de la leçon", "example": "<PERSON><PERSON> cette le<PERSON>, nous allons explorer les techniques de respiration consciente..."}, "videoUrl": {"type": "string", "description": "URL de la vidéo", "example": "https://example.com/videos/breathing-techniques.mp4"}, "order": {"type": "number", "description": "Ordre de la leçon dans le cours", "example": 1, "minimum": 1}}, "required": ["courseId", "title", "order"]}, "UpdateLessonDto": {"type": "object", "properties": {"courseId": {"type": "string", "description": "ID du cours", "example": "123e4567-e89b-12d3-a456-************"}, "title": {"type": "string", "description": "Titre de la leçon", "example": "Introduction à la respiration consciente", "minLength": 3, "maxLength": 100}, "content": {"type": "string", "description": "Contenu de la leçon", "example": "<PERSON><PERSON> cette le<PERSON>, nous allons explorer les techniques de respiration consciente..."}, "videoUrl": {"type": "string", "description": "URL de la vidéo", "example": "https://example.com/videos/breathing-techniques.mp4"}, "order": {"type": "number", "description": "Ordre de la leçon dans le cours", "example": 1, "minimum": 1}}}, "ReorderLessonsDto": {"type": "object", "properties": {"lessonIds": {"description": "Liste ordonnée des IDs de leçon", "example": ["123e4567-e89b-12d3-a456-************", "123e4567-e89b-12d3-a456-426614174001"], "type": "array", "items": {"type": "string"}}}, "required": ["lessonIds"]}, "CreateEnrollmentDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "ID de l'utilisateur", "example": "123e4567-e89b-12d3-a456-************"}, "courseId": {"type": "string", "description": "ID du cours", "example": "123e4567-e89b-12d3-a456-************"}, "progress": {"type": "number", "description": "Progression dans le cours (en pourcentage)", "example": 0, "minimum": 0, "maximum": 100, "default": 0}}, "required": ["userId", "courseId"]}, "UpdateEnrollmentDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "ID de l'utilisateur", "example": "123e4567-e89b-12d3-a456-************"}, "courseId": {"type": "string", "description": "ID du cours", "example": "123e4567-e89b-12d3-a456-************"}, "progress": {"type": "number", "description": "Progression dans le cours (en pourcentage)", "example": 0, "minimum": 0, "maximum": 100, "default": 0}}}, "UpdateProgressDto": {"type": "object", "properties": {"progress": {"type": "number", "description": "Progression en pourcentage", "example": 50, "minimum": 0, "maximum": 100}}, "required": ["progress"]}, "CreateEventDto": {"type": "object", "properties": {"eventType": {"type": "string", "description": "Type d'événement", "example": "USER_CREATED"}, "payload": {"type": "object", "description": "Données de l'événement", "example": {"userId": "123", "action": "create"}}, "status": {"type": "string", "description": "Statut de l'événement", "example": "RECEIVED", "enum": ["RECEIVED", "PROCESSING", "COMPLETED", "FAILED"], "default": "RECEIVED"}, "userId": {"type": "number", "description": "ID de l'utilisateur associé à l'événement", "example": 1}}, "required": ["eventType", "payload"]}, "CreateNotificationDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "ID de l'utilisateur destinataire", "example": "123e4567-e89b-12d3-a456-************"}, "type": {"type": "string", "description": "Type de notification", "enum": ["EMAIL", "PUSH", "IN_APP", "SMS"], "example": "EMAIL"}, "title": {"type": "string", "description": "Titre de la notification", "example": "Bienvenue sur Retreat And Be"}, "content": {"type": "string", "description": "Contenu de la notification", "example": "Nous sommes ravis de vous accueillir sur notre plateforme."}, "data": {"type": "object", "description": "Données supplémentaires pour la notification", "example": {"courseId": "123", "lessonId": "456"}}}, "required": ["userId", "type", "title"]}, "RevokeCertificateDto": {"type": "object", "properties": {}}, "TokenizeRequestDto": {"type": "object", "properties": {}}, "DetokenizeRequestDto": {"type": "object", "properties": {}}, "CreateAuditEntryDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "ID de l'utilisateur qui a effectué l'action", "example": "123e4567-e89b-12d3-a456-************"}, "action": {"type": "string", "description": "Action effectuée", "example": "CREATE_USER"}, "resource": {"type": "string", "description": "Ressource concernée", "example": "USER"}, "resourceId": {"type": "string", "description": "ID de la ressource concernée", "example": "123e4567-e89b-12d3-a456-************"}, "details": {"type": "object", "description": "Détails de l'action", "example": {"name": "<PERSON>", "email": "<EMAIL>"}}, "ip": {"type": "string", "description": "Adresse IP de l'utilisateur", "example": "***********"}, "userAgent": {"type": "string", "description": "User-Agent de l'utilisateur", "example": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"}}, "required": ["userId", "action", "resource"]}, "AuditQueryDto": {"type": "object", "properties": {}}, "RecordInteractionDto": {"type": "object", "properties": {"itemId": {"type": "string", "description": "ID de l'élément", "example": "123e4567-e89b-12d3-a456-************"}, "type": {"type": "string", "description": "Type d'élément", "enum": ["COURSE", "RETREAT", "PARTNER"], "example": "COURSE"}, "interactionType": {"type": "string", "description": "Type d'interaction", "example": "VIEW"}, "metadata": {"type": "object", "description": "Métadonnées de l'interaction", "example": {"duration": 300, "progress": 0.5}}}, "required": ["itemId", "type", "interactionType"]}, "UpdatePreferencesDto": {"type": "object", "properties": {"recommendationStrategy": {"type": "string", "description": "Stratégie de recommandation préférée", "enum": ["CONTENT_BASED", "COLLABORATIVE", "HYBRID"], "example": "HYBRID"}, "hybridMethod": {"type": "string", "description": "Méthode hybride préférée", "enum": ["WEIGHTED", "SWITCHING", "CASCADING", "MIXED"], "example": "WEIGHTED"}, "preferredTypes": {"type": "array", "description": "Types de recommandation préférés", "example": ["COURSE", "RETREAT"], "items": {"type": "string", "enum": ["COURSE", "RETREAT", "PARTNER"]}}, "preferredCategories": {"description": "Catégories préférées", "example": ["Yoga", "Meditation"], "type": "array", "items": {"type": "string"}}, "excludedCategories": {"description": "Catégories exclues", "example": ["Fitness"], "type": "array", "items": {"type": "string"}}, "preferredLevels": {"description": "Niveaux préférés", "example": ["BEGINNER", "INTERMEDIATE"], "type": "array", "items": {"type": "string"}}, "preferredLocations": {"description": "Localisations préférées", "example": ["Paris", "London"], "type": "array", "items": {"type": "string"}}, "preferredTags": {"description": "Tags préférés", "example": ["yoga", "meditation", "wellness"], "type": "array", "items": {"type": "string"}}, "maxRecommendations": {"type": "number", "description": "Nombre maximum de recommandations", "example": 10}, "includeMetadata": {"type": "boolean", "description": "Inclure les métadonnées dans les recommandations", "example": true}}}, "SocialVideoInteractionDto": {"type": "object", "properties": {"contentId": {"type": "string", "description": "ID du contenu", "example": "video-123"}, "contentType": {"type": "string", "description": "Type de contenu", "enum": ["VIDEO", "POST", "LIVESTREAM", "STORY"], "example": "VIDEO"}, "interactionType": {"type": "string", "description": "Type d'interaction", "enum": ["VIEW", "LIKE", "DISLIKE", "SHARE", "COMMENT", "SAVE", "WATCH_COMPLETE", "WATCH_PARTIAL", "FOLLOW_CREATOR", "UNFOLLOW_CREATOR"], "example": "VIEW"}, "metadata": {"type": "object", "description": "Métadonnées de l'interaction", "example": {"watchDuration": 120, "watchPercentage": 0.75}}}, "required": ["contentId", "contentType", "interactionType"]}, "SocialVideoPreferencesDto": {"type": "object", "properties": {"contentTypes": {"description": "Types de contenu préférés", "example": ["video", "post", "livestream"], "items": {"type": "array"}, "type": "array"}, "categories": {"description": "Catégories préférées", "example": ["yoga", "meditation", "fitness"], "items": {"type": "array"}, "type": "array"}, "excludedTags": {"description": "Tags exclus", "example": ["politics", "violence"], "items": {"type": "array"}, "type": "array"}, "excludedCreators": {"description": "Créateurs exclus", "example": ["creator-id-1", "creator-id-2"], "items": {"type": "array"}, "type": "array"}, "preferredLanguages": {"description": "<PERSON><PERSON> préféré<PERSON>", "example": ["fr", "en"], "items": {"type": "array"}, "type": "array"}, "preferredDuration": {"type": "string", "description": "<PERSON><PERSON><PERSON> p<PERSON>", "enum": ["short", "medium", "long"], "example": "medium"}, "preferNewContent": {"type": "boolean", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> le contenu récent", "example": true}, "preferFollowingContent": {"type": "boolean", "description": "Pré<PERSON><PERSON>rer le contenu des créateurs suivis", "example": true}}}, "CreateCouponDto": {"type": "object", "properties": {"code": {"type": "string", "description": "Code du coupon (généré automatiquement si non fourni)", "example": "SUMMER2023"}, "type": {"type": "string", "description": "Type de coupon", "enum": ["PERCENTAGE", "FIXED_AMOUNT", "FREE_SHIPPING", "BUY_X_GET_Y"], "example": "PERCENTAGE"}, "value": {"type": "number", "description": "Valeur du coupon (pourcentage ou montant fixe)", "example": 10}, "minPurchaseAmount": {"type": "number", "description": "Montant minimum d'achat pour appliquer le coupon", "example": 50}, "maxDiscountAmount": {"type": "number", "description": "Montant maximum de la remise", "example": 100}, "startDate": {"format": "date-time", "type": "string", "description": "Date de début de validité du coupon", "example": "2023-01-01T00:00:00.000Z"}, "endDate": {"format": "date-time", "type": "string", "description": "Date de fin de validité du coupon", "example": "2023-12-31T23:59:59.999Z"}, "usageLimit": {"type": "number", "description": "Limite d'utilisation du coupon", "example": 100}, "perUserLimit": {"type": "number", "description": "Limite d'utilisation du coupon par utilisateur", "example": 1}, "isActive": {"type": "boolean", "description": "Coupon actif", "example": true}, "description": {"type": "string", "description": "Description du coupon", "example": "Coupon de réduction pour l'été 2023"}, "applicableProducts": {"description": "Produits applicables", "example": ["product-1", "product-2"], "type": "array", "items": {"type": "string"}}, "excludedProducts": {"description": "Produits exclus", "example": ["product-3"], "type": "array", "items": {"type": "string"}}, "applicableCategories": {"description": "Catégories applicables", "example": ["category-1", "category-2"], "type": "array", "items": {"type": "string"}}, "excludedCategories": {"description": "Catégories exclues", "example": ["category-3"], "type": "array", "items": {"type": "string"}}, "metadata": {"type": "object", "description": "Métadonnées du coupon", "example": {"campaign": "summer-2023"}}}, "required": ["type", "value"]}, "UpdateCouponDto": {"type": "object", "properties": {"code": {"type": "string", "description": "Code du coupon (généré automatiquement si non fourni)", "example": "SUMMER2023"}, "type": {"type": "string", "description": "Type de coupon", "enum": ["PERCENTAGE", "FIXED_AMOUNT", "FREE_SHIPPING", "BUY_X_GET_Y"], "example": "PERCENTAGE"}, "value": {"type": "number", "description": "Valeur du coupon (pourcentage ou montant fixe)", "example": 10}, "minPurchaseAmount": {"type": "number", "description": "Montant minimum d'achat pour appliquer le coupon", "example": 50}, "maxDiscountAmount": {"type": "number", "description": "Montant maximum de la remise", "example": 100}, "startDate": {"format": "date-time", "type": "string", "description": "Date de début de validité du coupon", "example": "2023-01-01T00:00:00.000Z"}, "endDate": {"format": "date-time", "type": "string", "description": "Date de fin de validité du coupon", "example": "2023-12-31T23:59:59.999Z"}, "usageLimit": {"type": "number", "description": "Limite d'utilisation du coupon", "example": 100}, "perUserLimit": {"type": "number", "description": "Limite d'utilisation du coupon par utilisateur", "example": 1}, "isActive": {"type": "boolean", "description": "Coupon actif", "example": true}, "description": {"type": "string", "description": "Description du coupon", "example": "Coupon de réduction pour l'été 2023"}, "applicableProducts": {"description": "Produits applicables", "example": ["product-1", "product-2"], "type": "array", "items": {"type": "string"}}, "excludedProducts": {"description": "Produits exclus", "example": ["product-3"], "type": "array", "items": {"type": "string"}}, "applicableCategories": {"description": "Catégories applicables", "example": ["category-1", "category-2"], "type": "array", "items": {"type": "string"}}, "excludedCategories": {"description": "Catégories exclues", "example": ["category-3"], "type": "array", "items": {"type": "string"}}, "metadata": {"type": "object", "description": "Métadonnées du coupon", "example": {"campaign": "summer-2023"}}}}, "OrderItemDto": {"type": "object", "properties": {"productId": {"type": "string", "description": "ID du produit", "example": "product-1"}, "quantity": {"type": "number", "description": "Quantité", "example": 1}, "unitPrice": {"type": "number", "description": "Prix unitaire", "example": 49.99}, "categories": {"description": "Catégories du produit", "example": ["category-1", "category-2"], "type": "array", "items": {"type": "string"}}}, "required": ["productId", "quantity", "unitPrice"]}, "ApplyCouponDto": {"type": "object", "properties": {"code": {"type": "string", "description": "Code du coupon", "example": "SUMMER2023"}, "userId": {"type": "string", "description": "ID de l'utilisateur", "example": "123e4567-e89b-12d3-a456-************"}, "orderId": {"type": "string", "description": "ID de la commande", "example": "order-1"}, "orderAmount": {"type": "number", "description": "<PERSON><PERSON> <PERSON> commande", "example": 99.99}, "items": {"description": "Éléments de la commande", "type": "array", "items": {"$ref": "#/components/schemas/OrderItemDto"}}}, "required": ["code", "userId", "orderId", "orderAmount"]}, "GenerateBulkCouponsDto": {"type": "object", "properties": {"template": {"description": "Mod<PERSON>le de coupon", "allOf": [{"$ref": "#/components/schemas/CreateCouponDto"}]}, "count": {"type": "number", "description": "Nombre de coupons à générer", "example": 100}, "prefix": {"type": "string", "description": "Préfixe des codes de coupon", "example": "SUMMER-"}}, "required": ["template", "count"]}, "UpdateMonitoringConfigDto": {"type": "object", "properties": {"enabled": {"type": "boolean", "description": "Activer/désactiver le monitoring", "example": true}, "interval": {"type": "number", "description": "Intervalle de monitoring en ms", "example": 60000}, "alertThreshold": {"type": "number", "description": "<PERSON><PERSON> d'alerte", "example": 3}, "retentionDays": {"type": "number", "description": "Jours de rétention des données", "example": 30}}}, "CreateEndpointDto": {"type": "object", "properties": {"url": {"type": "string", "description": "URL de l'endpoint", "example": "https://api.example.com/health"}, "name": {"type": "string", "description": "Nom de l'endpoint", "example": "API Health Check"}, "description": {"type": "string", "description": "Description de l'endpoint", "example": "Vérification de l'état de santé de l'API"}, "enabled": {"type": "boolean", "description": "Activer/désactiver la surveillance de l'endpoint", "example": true}}, "required": ["url", "name"]}, "CreateServiceDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Nom du service", "example": "Database Service"}, "description": {"type": "string", "description": "Description du service", "example": "Service de base de données PostgreSQL"}, "healthCheckUrl": {"type": "string", "description": "URL de vérification de santé du service", "example": "http://localhost:5432/health"}, "enabled": {"type": "boolean", "description": "Activer/désactiver la surveillance du service", "example": true}}, "required": ["name"]}, "CreateAlertDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Type d'alerte", "example": "SYSTEM"}, "severity": {"type": "string", "description": "Sévérité de l'alerte", "enum": ["INFO", "WARNING", "ERROR", "CRITICAL"], "example": "WARNING"}, "message": {"type": "string", "description": "Message de l'alerte", "example": "Utilisation CPU élevée: 85%"}, "source": {"type": "string", "description": "Source de l'alerte", "example": "MONITORING"}, "metadata": {"type": "object", "description": "Métadonnées de l'alerte", "example": {"metricType": "CPU_USAGE", "value": 85, "threshold": 80}}}, "required": ["type", "severity", "message", "source"]}, "UpdateAlertDto": {"type": "object", "properties": {"status": {"type": "string", "description": "Statut de l'alerte", "enum": ["OPEN", "ACKNOWLEDGED", "RESOLVED"], "example": "ACKNOWLEDGED"}, "severity": {"type": "string", "description": "Sévérité de l'alerte", "enum": ["INFO", "WARNING", "ERROR", "CRITICAL"], "example": "WARNING"}, "message": {"type": "string", "description": "Message de l'alerte", "example": "Utilisation CPU élevée: 85%"}, "acknowledgedBy": {"type": "string", "description": "ID de l'utilisateur qui a accusé réception de l'alerte", "example": "123e4567-e89b-12d3-a456-************"}, "acknowledgedAt": {"format": "date-time", "type": "string", "description": "Date d'accusé de réception de l'alerte", "example": "2023-01-01T00:00:00.000Z"}, "resolvedBy": {"type": "string", "description": "ID de l'utilisateur qui a résolu l'alerte", "example": "123e4567-e89b-12d3-a456-************"}, "resolvedAt": {"format": "date-time", "type": "string", "description": "Date de résolution de l'alerte", "example": "2023-01-01T00:00:00.000Z"}, "comment": {"type": "string", "description": "Commentaire", "example": "Redémarrage du serveur effectué"}, "resolution": {"type": "string", "description": "Résolution", "example": "Augmentation des ressources CPU allouées"}, "metadata": {"type": "object", "description": "Métadonnées de l'alerte", "example": {"metricType": "CPU_USAGE", "value": 85, "threshold": 80}}}}, "ContactInfoDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Primary contact name", "example": "<PERSON>"}, "email": {"type": "string", "description": "Primary contact email", "example": "<EMAIL>"}, "phone": {"type": "string", "description": "Primary contact phone", "example": "+33612345678"}, "position": {"type": "string", "description": "Primary contact position", "example": "CEO"}}, "required": ["name", "email", "phone"]}, "AddressDto": {"type": "object", "properties": {"street": {"type": "string", "description": "Street address", "example": "123 Main St"}, "city": {"type": "string", "description": "City", "example": "Paris"}, "state": {"type": "string", "description": "State/Province", "example": "Île-de-France"}, "postalCode": {"type": "string", "description": "Postal code", "example": "75001"}, "country": {"type": "string", "description": "Country", "example": "France"}}, "required": ["street", "city", "state", "postalCode", "country"]}, "CreatePartnerDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "User ID of the partner", "example": "123e4567-e89b-12d3-a456-************"}, "companyName": {"type": "string", "description": "Company name", "example": "Wellness Retreat Center"}, "type": {"type": "string", "description": "Partner type", "enum": ["PREMIUM_CERTIFIED", "CERTIFIED", "STANDARD"], "example": "PREMIUM_CERTIFIED"}, "category": {"type": "string", "description": "Partner category", "enum": ["ORGANIZER", "TRAVEL_AGENCY", "CATERING", "GUIDE", "TRANSPORT", "WELLNESS", "INSURANCE", "ACCOMMODATION", "EQUIPMENT", "OTHER"], "example": "WELLNESS"}, "description": {"type": "string", "description": "Description of the partner services", "example": "We provide premium wellness retreats in the heart of nature."}, "logo": {"type": "string", "description": "Logo URL", "example": "https://example.com/logo.png"}, "website": {"type": "string", "description": "Website URL", "example": "https://example.com"}, "specializations": {"description": "Partner specializations", "example": ["Yoga", "Meditation", "Wellness"], "type": "array", "items": {"type": "string"}}, "languages": {"description": "Languages spoken", "example": ["French", "English", "Spanish"], "type": "array", "items": {"type": "string"}}, "contactInfo": {"description": "Contact information", "allOf": [{"$ref": "#/components/schemas/ContactInfoDto"}]}, "address": {"description": "Address information", "allOf": [{"$ref": "#/components/schemas/AddressDto"}]}, "coverageAreas": {"type": "object", "description": "Coverage areas (JSON)", "example": {"regions": ["Paris", "Lyon", "Marseille"]}}, "insurance": {"type": "object", "description": "Insurance information (JSON)", "example": {"provider": "Insurance Co", "policyNumber": "12345", "coverage": "1000000"}}}, "required": ["userId", "companyName", "type", "category", "description", "specializations", "languages", "contactInfo", "address"]}, "PartnerDocumentDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Document type", "enum": ["IDENTITY", "BUSINESS_REGISTRATION", "INSURANCE", "CERTIFICATION", "TAX_DOCUMENT", "BANK_DETAILS", "OTHER"], "example": "BUSINESS_REGISTRATION"}, "description": {"type": "string", "description": "Document description", "example": "Business registration certificate"}, "fileId": {"type": "string", "description": "File ID from the file upload service", "example": "123e4567-e89b-12d3-a456-************"}, "metadata": {"type": "object", "description": "Additional metadata (JSON)", "example": {"issueDate": "2023-01-01", "expiryDate": "2024-01-01"}}}, "required": ["type", "description", "fileId"]}, "DateRangeDto": {"type": "object", "properties": {"start": {"type": "string", "description": "Date de début de la période de disponibilité", "example": "2023-06-01"}, "end": {"type": "string", "description": "Date de fin de la période de disponibilité", "example": "2023-06-15"}}, "required": ["start", "end"]}, "LocationPreferenceDto": {"type": "object", "properties": {"country": {"type": "string", "description": "Pays", "example": "France"}, "region": {"type": "string", "description": "Région ou état", "example": "Île-de-France"}, "city": {"type": "string", "description": "Ville", "example": "Paris"}, "radius": {"type": "number", "description": "Rayon de recherche en kilomètres", "example": 50}}, "required": ["country"]}, "MatchingCriteriaDto": {"type": "object", "properties": {"retreatId": {"type": "string", "description": "ID de la retraite pour laquelle chercher des partenaires", "example": "123e4567-e89b-12d3-a456-************"}, "partnerId": {"type": "string", "description": "ID du partenaire pour lequel chercher des retraites", "example": "123e4567-e89b-12d3-a456-************"}, "categories": {"type": "array", "description": "Catégories de partenaires recherchées", "example": ["WELLNESS", "GUIDE"], "items": {"type": "string", "enum": ["ORGANIZER", "TRAVEL_AGENCY", "CATERING", "GUIDE", "TRANSPORT", "WELLNESS", "INSURANCE", "ACCOMMODATION", "EQUIPMENT", "OTHER"]}}, "types": {"type": "array", "description": "Type de partenaire recherché", "example": ["CERTIFIED", "PREMIUM_CERTIFIED"], "items": {"type": "string", "enum": ["PREMIUM_CERTIFIED", "CERTIFIED", "STANDARD"]}}, "specializations": {"description": "Spécialisations recherchées", "example": ["Yoga", "Méditation", "Nutrition"], "type": "array", "items": {"type": "string"}}, "languages": {"description": "Langues parlées requises", "example": ["Français", "<PERSON><PERSON><PERSON>"], "type": "array", "items": {"type": "string"}}, "maxBudget": {"type": "number", "description": "Budget maximum par jour en euros", "example": 200}, "dateRange": {"description": "Période de disponibilité", "allOf": [{"$ref": "#/components/schemas/DateRangeDto"}]}, "location": {"description": "Préférences de localisation", "allOf": [{"$ref": "#/components/schemas/LocationPreferenceDto"}]}, "minExperience": {"type": "number", "description": "Nombre minimum d'années d'expérience", "example": 3}, "minRating": {"type": "number", "description": "Note minimale (sur 5)", "example": 4.5}, "minCapacity": {"type": "number", "description": "Capacité minimale (nombre de personnes)", "example": 10}, "certifiedOnly": {"type": "boolean", "description": "Inclure uniquement les partenaires certifiés", "example": true}, "limit": {"type": "number", "description": "Nombre maximum de résultats à retourner", "example": 10}}}, "CompatibilityFactorsDto": {"type": "object", "properties": {"skillMatch": {"type": "number", "description": "Score de compatibilité des compétences (0-100)", "example": 85}, "availabilityMatch": {"type": "number", "description": "Score de compatibilité des disponibilités (0-100)", "example": 90}, "locationMatch": {"type": "number", "description": "Score de compatibilité de localisation (0-100)", "example": 75}, "ratingMatch": {"type": "number", "description": "Score de compatibilité basé sur les évaluations (0-100)", "example": 95}, "budgetMatch": {"type": "number", "description": "Score de compatibilité basé sur le budget (0-100)", "example": 80}}, "required": ["skillMatch", "availabilityMatch", "locationMatch", "ratingMatch", "budgetMatch"]}, "PartnerMatchDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID du partenaire", "example": "123e4567-e89b-12d3-a456-************"}, "companyName": {"type": "string", "description": "Nom de l'entreprise du partenaire", "example": "Wellness Retreat Center"}, "type": {"type": "string", "description": "Type de partenaire", "enum": ["PREMIUM_CERTIFIED", "CERTIFIED", "STANDARD"], "example": "PREMIUM_CERTIFIED"}, "category": {"type": "string", "description": "Catégorie du partenaire", "enum": ["ORGANIZER", "TRAVEL_AGENCY", "CATERING", "GUIDE", "TRANSPORT", "WELLNESS", "INSURANCE", "ACCOMMODATION", "EQUIPMENT", "OTHER"], "example": "WELLNESS"}, "description": {"type": "string", "description": "Description du partenaire", "example": "Centre de bien-être spécialisé dans les retraites de yoga et méditation."}, "logo": {"type": "string", "description": "URL du logo", "example": "https://example.com/logo.png"}, "website": {"type": "string", "description": "Site web", "example": "https://wellness-retreat.com"}, "specializations": {"description": "Spécialisations", "example": ["Yoga", "Méditation", "Nutrition"], "type": "array", "items": {"type": "string"}}, "languages": {"description": "<PERSON><PERSON> par<PERSON>", "example": ["Français", "<PERSON><PERSON><PERSON>"], "type": "array", "items": {"type": "string"}}, "averageRating": {"type": "number", "description": "Note moyenne (sur 5)", "example": 4.8}, "totalReviews": {"type": "number", "description": "Nombre total d'avis", "example": 42}, "completedServices": {"type": "number", "description": "Nombre de services complétés", "example": 120}}, "required": ["id", "companyName", "type", "category", "description", "logo", "website", "specializations", "languages", "averageRating", "totalReviews", "completedServices"]}, "RetreatMatchDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID de la retraite", "example": "123e4567-e89b-12d3-a456-************"}, "title": {"type": "string", "description": "Titre de la retraite", "example": "Retraite de yoga et méditation en Provence"}, "description": {"type": "string", "description": "Description de la retraite", "example": "Une semaine de détente et de reconnexion à soi dans un cadre idyllique."}, "startDate": {"format": "date-time", "type": "string", "description": "Date de début", "example": "2023-06-01T00:00:00.000Z"}, "endDate": {"format": "date-time", "type": "string", "description": "Date de fin", "example": "2023-06-08T00:00:00.000Z"}, "location": {"type": "string", "description": "Localisation", "example": "Aix-en-Provence, France"}, "capacity": {"type": "number", "description": "Capacité", "example": 20}, "price": {"type": "number", "description": "Prix", "example": 1200}, "images": {"description": "Images", "example": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"], "type": "array", "items": {"type": "string"}}}, "required": ["id", "title", "description", "startDate", "endDate", "location", "capacity", "price", "images"]}, "MatchingResultDto": {"type": "object", "properties": {"partnerId": {"type": "string", "description": "ID du partenaire", "example": "123e4567-e89b-12d3-a456-************"}, "retreatId": {"type": "string", "description": "ID de la retraite", "example": "123e4567-e89b-12d3-a456-************"}, "score": {"type": "number", "description": "Score global de compatibilité (0-100)", "example": 87}, "compatibilityFactors": {"description": "Facteurs de compatibilité détaillés", "allOf": [{"$ref": "#/components/schemas/CompatibilityFactorsDto"}]}, "partner": {"description": "Détails du partenaire", "allOf": [{"$ref": "#/components/schemas/PartnerMatchDto"}]}, "retreat": {"description": "Dé<PERSON> de la retraite", "allOf": [{"$ref": "#/components/schemas/RetreatMatchDto"}]}}, "required": ["partnerId", "retreatId", "score", "compatibilityFactors", "partner", "retreat"]}, "MatchingResponseDto": {"type": "object", "properties": {"results": {"description": "Résultats du matching", "type": "array", "items": {"$ref": "#/components/schemas/MatchingResultDto"}}, "total": {"type": "number", "description": "Nombre total de résultats", "example": 42}, "executionTimeMs": {"type": "number", "description": "Temps d'exécution de la recherche en millisecondes", "example": 120}}, "required": ["results", "total", "executionTimeMs"]}, "SendMessageDto": {"type": "object", "properties": {}}, "SendFollowUpDto": {"type": "object", "properties": {}}, "CreateVideoRoomDto": {"type": "object", "properties": {}}}}}