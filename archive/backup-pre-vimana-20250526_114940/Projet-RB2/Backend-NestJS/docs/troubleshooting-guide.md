# Guide de résolution des problèmes - Sprint 4

Ce guide présente les problèmes courants qui peuvent survenir lors du déploiement ou de l'utilisation du système de recommandation, ainsi que leurs solutions.

## Table des matières

1. [Problèmes de migration Prisma](#problèmes-de-migration-prisma)
2. [Problèmes d'intégration avec les services externes](#problèmes-dintégration-avec-les-services-externes)
3. [Problèmes de performance](#problèmes-de-performance)
4. [Problèmes d'authentification](#problèmes-dauthentification)
5. [Problèmes de frontend](#problèmes-de-frontend)
6. [Problèmes de déploiement](#problèmes-de-déploiement)
7. [Problèmes de base de données](#problèmes-de-base-de-données)

## Problèmes de migration Prisma

### Erreur : "Migration cannot be executed because the database schema is not up to date"

**Symptômes :**
- La migration Prisma échoue avec ce message d'erreur
- Les nouvelles tables ou relations ne sont pas créées

**Solutions :**
1. Exécutez le script de résolution des problèmes de migration Prisma :
   ```bash
   ./scripts/fix-prisma-migration.sh
   ```

2. Si le script ne résout pas le problème, essayez de réinitialiser complètement les migrations :
   ```bash
   # Sauvegardez d'abord vos données si nécessaire
   rm -rf prisma/migrations
   npx prisma migrate dev --name init
   ```

3. Si vous êtes en production et ne pouvez pas réinitialiser les migrations, utilisez la migration basée sur l'état :
   ```bash
   npx prisma migrate resolve --applied "migration_name"
   npx prisma db push
   ```

### Erreur : "The migration contains changes that cannot be executed safely"

**Symptômes :**
- La migration Prisma échoue avec ce message d'erreur
- Prisma détecte des changements qui pourraient entraîner une perte de données

**Solutions :**
1. Créez une migration avec l'option `--create-only` :
   ```bash
   npx prisma migrate dev --name safe_migration --create-only
   ```

2. Modifiez manuellement le fichier de migration pour rendre les changements sûrs
3. Appliquez la migration :
   ```bash
   npx prisma migrate dev
   ```

## Problèmes d'intégration avec les services externes

### Erreur : "Cannot connect to moderation service"

**Symptômes :**
- Les recommandations ne sont pas filtrées
- Les signalements ne sont pas traités
- Erreurs dans les logs concernant le service de modération

**Solutions :**
1. Vérifiez que l'URL du service de modération est correcte dans le fichier `.env` :
   ```
   MODERATION_SERVICE_URL="https://moderation.retreat-and-be.com"
   ```

2. Vérifiez que l'API key est valide :
   ```
   MODERATION_SERVICE_API_KEY="your-api-key"
   ```

3. Vérifiez que le service de modération est en cours d'exécution et accessible depuis le serveur backend

4. Si le service de modération est temporairement indisponible, le système utilisera le stockage local pour les signalements. Vérifiez les logs pour voir si c'est le cas.

### Erreur : "Cannot connect to analytics service"

**Symptômes :**
- Les événements d'analytics ne sont pas enregistrés
- Erreurs dans les logs concernant le service d'analytics

**Solutions :**
1. Vérifiez que l'URL du service d'analytics est correcte dans le fichier `.env` :
   ```
   ANALYTICS_SERVICE_URL="https://analytics.retreat-and-be.com"
   ```

2. Vérifiez que l'API key est valide :
   ```
   ANALYTICS_SERVICE_API_KEY="your-api-key"
   ```

3. Vérifiez que le service d'analytics est en cours d'exécution et accessible depuis le serveur backend

4. Si le service d'analytics est temporairement indisponible, le système utilisera le stockage local pour les événements. Vérifiez les logs pour voir si c'est le cas.

## Problèmes de performance

### Symptôme : Temps de réponse lent pour les recommandations

**Causes possibles :**
- Trop de recommandations à filtrer
- Intégration lente avec le service de modération
- Génération d'explications trop détaillées

**Solutions :**
1. Limitez le nombre de recommandations à filtrer :
   ```typescript
   // Avant
   const filteredRecommendations = await moderationService.filterRecommendations(allRecommendations, type);

   // Après
   const limitedRecommendations = allRecommendations.slice(0, 100);
   const filteredRecommendations = await moderationService.filterRecommendations(limitedRecommendations, type);
   ```

2. Mettez en cache les résultats de modération :
   ```typescript
   // Utilisez un cache pour stocker les résultats de modération
   const cacheKey = `moderation:${recommendationId}:${type}`;
   const cachedResult = await cacheService.get(cacheKey);
   
   if (cachedResult) {
     return cachedResult;
   }
   
   const result = await moderationService.isRecommendationAllowed(recommendationId, type);
   await cacheService.set(cacheKey, result, 3600); // Cache pour 1 heure
   
   return result;
   ```

3. Réduisez le niveau de détail des explications par défaut :
   ```typescript
   // Utilisez le niveau de détail BASIC par défaut
   const detailLevel = options?.detailLevel || 'BASIC';
   ```

### Symptôme : Utilisation élevée de la mémoire

**Causes possibles :**
- Fuites de mémoire dans le stockage local
- Trop de données en mémoire pour les feedbacks et les signalements

**Solutions :**
1. Limitez la taille des tableaux en mémoire :
   ```typescript
   // Limiter la taille du tableau localEvents
   if (this.localEvents.length > 1000) {
     this.localEvents = this.localEvents.slice(-1000);
   }
   ```

2. Nettoyez périodiquement les données en mémoire :
   ```typescript
   // Nettoyer les données plus anciennes que 24 heures
   const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
   this.localEvents = this.localEvents.filter(event => event.timestamp > oneDayAgo);
   ```

## Problèmes d'authentification

### Erreur : "Invalid token" ou "Unauthorized"

**Symptômes :**
- Les utilisateurs ne peuvent pas accéder aux API de recommandation
- Erreurs 401 ou 403 dans les logs

**Solutions :**
1. Vérifiez que le secret JWT est correctement configuré dans le fichier `.env` :
   ```
   JWT_SECRET="your-jwt-secret"
   ```

2. Vérifiez que les tokens JWT sont correctement générés et validés :
   ```typescript
   // Vérifiez la configuration du module JWT
   JwtModule.register({
     secret: configService.get<string>('JWT_SECRET'),
     signOptions: { expiresIn: configService.get<string>('JWT_EXPIRATION', '1d') },
   }),
   ```

3. Vérifiez que les gardes d'authentification sont correctement configurés :
   ```typescript
   // Assurez-vous que le JwtAuthGuard est appliqué aux contrôleurs
   @UseGuards(JwtAuthGuard)
   @Controller('recommendations')
   export class RecommendationController { ... }
   ```

## Problèmes de frontend

### Erreur : "Failed to fetch" lors des appels API

**Symptômes :**
- Les composants frontend ne peuvent pas récupérer les données du backend
- Erreurs dans la console du navigateur

**Solutions :**
1. Vérifiez que l'URL de l'API est correctement configurée :
   ```javascript
   // Vérifiez le fichier config.js
   export const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api';
   ```

2. Vérifiez que le proxy CORS est correctement configuré dans le backend :
   ```typescript
   // Vérifiez la configuration CORS dans main.ts
   app.enableCors({
     origin: configService.get<string>('CORS_ORIGIN', '*'),
     methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
     credentials: true,
   });
   ```

3. Vérifiez que les tokens d'authentification sont correctement envoyés :
   ```javascript
   // Vérifiez que le token est inclus dans les en-têtes
   const token = getAuthToken();
   const response = await axios.get(`${API_URL}/recommendations`, {
     headers: {
       Authorization: `Bearer ${token}`,
     },
   });
   ```

### Erreur : "ChartJS is not defined" ou erreurs similaires

**Symptômes :**
- Les visualisations ne s'affichent pas
- Erreurs dans la console du navigateur concernant des bibliothèques externes

**Solutions :**
1. Vérifiez que toutes les dépendances sont correctement installées :
   ```bash
   npm install chart.js react-chartjs-2
   ```

2. Vérifiez que les bibliothèques sont correctement importées :
   ```javascript
   import { Chart as ChartJS, CategoryScale, LinearScale, BarElement } from 'chart.js';
   ChartJS.register(CategoryScale, LinearScale, BarElement);
   ```

3. Vérifiez que les composants sont correctement utilisés :
   ```jsx
   <Bar 
     data={{
       labels: ['Label 1', 'Label 2'],
       datasets: [{
         label: 'Dataset',
         data: [1, 2],
       }]
     }}
   />
   ```

## Problèmes de déploiement

### Erreur : "Module not found" après le déploiement

**Symptômes :**
- Le backend ne démarre pas après le déploiement
- Erreurs concernant des modules manquants dans les logs

**Solutions :**
1. Vérifiez que toutes les dépendances sont installées :
   ```bash
   npm install --production
   ```

2. Vérifiez que le fichier `package.json` inclut toutes les dépendances nécessaires

3. Vérifiez que les chemins d'importation sont corrects :
   ```typescript
   // Utilisez des chemins d'importation relatifs ou absolus selon la configuration
   import { RecommendationType } from '../enums/recommendation-type.enum';
   // ou
   import { RecommendationType } from 'src/modules/recommendation/enums/recommendation-type.enum';
   ```

### Erreur : "Port already in use"

**Symptômes :**
- Le backend ne démarre pas après le déploiement
- Erreur "Port 3000 is already in use" dans les logs

**Solutions :**
1. Vérifiez qu'aucun autre processus n'utilise le port :
   ```bash
   lsof -i :3000
   ```

2. Arrêtez le processus existant :
   ```bash
   kill -9 <PID>
   ```

3. Configurez le backend pour utiliser un port différent :
   ```
   PORT=3001
   ```

## Problèmes de base de données

### Erreur : "Connection refused" ou "Cannot connect to database"

**Symptômes :**
- Le backend ne peut pas se connecter à la base de données
- Erreurs de connexion dans les logs

**Solutions :**
1. Vérifiez que la chaîne de connexion à la base de données est correcte :
   ```
   DATABASE_URL="postgresql://user:password@localhost:5432/retreat_and_be"
   ```

2. Vérifiez que la base de données est en cours d'exécution :
   ```bash
   pg_isready -h localhost -p 5432
   ```

3. Vérifiez que l'utilisateur a les permissions nécessaires :
   ```sql
   GRANT ALL PRIVILEGES ON DATABASE retreat_and_be TO user;
   ```

### Erreur : "Relation does not exist"

**Symptômes :**
- Erreurs concernant des tables ou des relations manquantes
- Les requêtes échouent avec des erreurs de schéma

**Solutions :**
1. Vérifiez que les migrations ont été appliquées :
   ```bash
   npx prisma migrate status
   ```

2. Appliquez les migrations manquantes :
   ```bash
   npx prisma migrate deploy
   ```

3. Si les migrations ne peuvent pas être appliquées, utilisez la synchronisation du schéma (à éviter en production) :
   ```bash
   npx prisma db push
   ```
