# Documentation Sprint 4 - Système de Recommandation

Cette documentation décrit les nouvelles fonctionnalités implémentées dans le Sprint 4 du système de recommandation de Retreat And Be.

## Table des matières

1. [Intégration avec le service de modération](#intégration-avec-le-service-de-modération)
2. [Explications améliorées des recommandations](#explications-améliorées-des-recommandations)
3. [Système de feedback utilisateur](#système-de-feedback-utilisateur)
4. [Intégration avec le service d'analytics](#intégration-avec-le-service-danalytics)
5. [Composants frontend](#composants-frontend)

## Intégration avec le service de modération

### Description

Le service de modération permet de filtrer les recommandations inappropriées et de gérer les signalements des utilisateurs. Il s'intègre avec un service externe de modération pour vérifier le contenu des recommandations.

### Services

#### ModerationIntegrationService

Ce service gère l'intégration avec le service externe de modération et fournit des méthodes pour filtrer les recommandations et gérer les signalements.

**Méthodes principales :**

- `filterRecommendations(recommendations, type)` : Filtre les recommandations selon les règles de modération
- `isRecommendationAllowed(recommendationId, type)` : Vérifie si une recommandation est autorisée
- `reportRecommendation(recommendationId, type, userId, reason)` : Signale une recommandation inappropriée

### API

#### RecommendationModerationController

Ce contrôleur expose les API de modération pour les recommandations.

**Endpoints :**

- `POST /recommendations/moderation/check/:type/:id` : Vérifie si une recommandation est autorisée
- `POST /recommendations/moderation/report/:type/:id` : Signale une recommandation inappropriée
- `GET /recommendations/moderation/status/:type/:id` : Récupère le statut de modération d'une recommandation

### Exemple d'utilisation

```typescript
// Filtrer les recommandations
const filteredRecommendations = await moderationService.filterRecommendations(
  recommendations,
  RecommendationType.RETREAT
);

// Signaler une recommandation
const result = await moderationService.reportRecommendation(
  'rec123',
  RecommendationType.RETREAT,
  'user456',
  'Contenu inapproprié'
);
```

## Explications améliorées des recommandations

### Description

Les explications améliorées permettent de fournir aux utilisateurs des explications détaillées et personnalisées sur les recommandations qui leur sont proposées. Elles incluent des facteurs d'explication et des visualisations pour une meilleure compréhension.

### Services

#### EnhancedExplanationService

Ce service génère des explications améliorées pour les recommandations.

**Méthodes principales :**

- `generateEnhancedExplanation(recommendationId, type, userId, options)` : Génère une explication améliorée pour une recommandation

### API

#### EnhancedExplanationController

Ce contrôleur expose les API d'explications améliorées.

**Endpoints :**

- `GET /recommendations/explanations/:type/:id` : Récupère une explication améliorée pour une recommandation

### Exemple d'utilisation

```typescript
// Générer une explication améliorée
const explanation = await explanationService.generateEnhancedExplanation(
  'rec123',
  RecommendationType.RETREAT,
  'user456',
  {
    detailLevel: 'DETAILED',
    includeVisualizations: true,
    language: 'fr'
  }
);
```

## Système de feedback utilisateur

### Description

Le système de feedback utilisateur permet aux utilisateurs de donner leur avis sur les recommandations qui leur sont proposées. Il inclut différents types de feedback (like, dislike, save, hide, report) et permet d'enregistrer des commentaires et des notes.

### Services

#### FeedbackService

Ce service gère le feedback utilisateur sur les recommandations.

**Méthodes principales :**

- `recordFeedback(feedbackData)` : Enregistre un feedback utilisateur
- `getUserFeedbackForRecommendation(userId, recommendationId, type)` : Récupère les feedbacks d'un utilisateur pour une recommandation
- `getUserFeedbacks(userId, options)` : Récupère tous les feedbacks d'un utilisateur
- `deleteFeedback(feedbackId, userId)` : Supprime un feedback

### API

#### FeedbackController

Ce contrôleur expose les API de feedback utilisateur.

**Endpoints :**

- `POST /recommendations/feedback` : Enregistre un feedback utilisateur
- `GET /recommendations/feedback/:type/:id` : Récupère les feedbacks pour une recommandation
- `GET /recommendations/feedback/user` : Récupère les feedbacks de l'utilisateur courant
- `DELETE /recommendations/feedback/:id` : Supprime un feedback

### Exemple d'utilisation

```typescript
// Enregistrer un feedback
const result = await feedbackService.recordFeedback({
  userId: 'user456',
  recommendationId: 'rec123',
  recommendationType: RecommendationType.RETREAT,
  feedbackType: FeedbackType.LIKE,
  comment: 'Super recommandation !',
  rating: 5
});
```

## Intégration avec le service d'analytics

### Description

L'intégration avec le service d'analytics permet de suivre les interactions des utilisateurs avec les recommandations et les explications. Elle s'intègre avec un service externe d'analytics pour enregistrer les événements.

### Services

#### AnalyticsIntegrationService

Ce service gère l'intégration avec le service externe d'analytics et fournit des méthodes pour suivre les interactions des utilisateurs.

**Méthodes principales :**

- `trackEvent(eventType, eventData)` : Enregistre un événement d'analytics
- `trackRecommendationViewed(userId, recommendationId, type, metadata)` : Enregistre un événement de visualisation de recommandation
- `trackRecommendationClicked(userId, recommendationId, type, metadata)` : Enregistre un événement de clic sur une recommandation
- `trackRecommendationFeedback(userId, recommendationId, type, feedbackType, metadata)` : Enregistre un événement de feedback sur une recommandation
- `trackExplanationViewed(userId, recommendationId, type, explanationId, metadata)` : Enregistre un événement de visualisation d'explication
- `trackExplanationFeedback(userId, recommendationId, type, explanationId, feedbackType, metadata)` : Enregistre un événement de feedback sur une explication

### API

#### AnalyticsIntegrationController

Ce contrôleur expose les API d'analytics pour les recommandations.

**Endpoints :**

- `POST /recommendations/analytics-integration/view/:type/:id` : Enregistre un événement de visualisation de recommandation
- `POST /recommendations/analytics-integration/click/:type/:id` : Enregistre un événement de clic sur une recommandation
- `POST /recommendations/analytics-integration/explanation-view/:type/:id` : Enregistre un événement de visualisation d'explication
- `POST /recommendations/analytics-integration/explanation-feedback/:type/:id` : Enregistre un événement de feedback sur une explication

### Exemple d'utilisation

```typescript
// Enregistrer un événement de visualisation de recommandation
const result = await analyticsService.trackRecommendationViewed(
  'user456',
  'rec123',
  RecommendationType.RETREAT,
  {
    source: 'homepage',
    position: 2
  }
);
```

## Composants frontend

### Description

Les composants frontend permettent d'afficher les recommandations, les explications et les boutons de feedback dans l'interface utilisateur.

### Composants

#### EnhancedExplanation

Ce composant affiche une explication améliorée pour une recommandation.

**Props :**

- `recommendationId` : ID de la recommandation
- `recommendationType` : Type de recommandation
- `language` : Langue de l'explication (par défaut : 'fr')
- `detailLevel` : Niveau de détail de l'explication (par défaut : 'STANDARD')

#### FeedbackButtons

Ce composant affiche les boutons de feedback pour une recommandation.

**Props :**

- `recommendationId` : ID de la recommandation
- `recommendationType` : Type de recommandation
- `onFeedbackSubmitted` : Fonction appelée lorsqu'un feedback est soumis

#### RecommendationCard

Ce composant affiche une carte de recommandation avec explication et feedback.

**Props :**

- `recommendation` : Objet de recommandation
- `type` : Type de recommandation
- `loading` : Indique si la recommandation est en cours de chargement
- `onFeedbackSubmitted` : Fonction appelée lorsqu'un feedback est soumis
- `language` : Langue de l'explication (par défaut : 'fr')
- `detailLevel` : Niveau de détail de l'explication (par défaut : 'STANDARD')

### Exemple d'utilisation

```jsx
// Afficher une carte de recommandation
<RecommendationCard
  recommendation={recommendation}
  type={RecommendationType.RETREAT}
  onFeedbackSubmitted={handleFeedbackSubmitted}
  language="fr"
  detailLevel="DETAILED"
/>
```
