# Guide d'intégration avec d'autres microservices

Ce document détail<PERSON> comment intégrer le système de recommandation avec d'autres microservices de la plateforme Retreat And Be pour le Sprint 5.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Architecture d'intégration](#architecture-dintégration)
3. [Intégration avec le service utilisateur](#intégration-avec-le-service-utilisateur)
4. [Intégration avec le service de contenu](#intégration-avec-le-service-de-contenu)
5. [Intégration avec le service de réservation](#intégration-avec-le-service-de-réservation)
6. [Intégration avec le service de notification](#intégration-avec-le-service-de-notification)
7. [Gestion des erreurs et résilience](#gestion-des-erreurs-et-résilience)
8. [Sécurité et authentification](#sécurité-et-authentification)
9. [Surveillance et observabilité](#surveillance-et-observabilité)
10. [Tests d'intégration](#tests-dintégration)

## Vue d'ensemble

L'intégration du système de recommandation avec d'autres microservices permettra d'enrichir les recommandations avec des données provenant de différentes parties de la plateforme, d'améliorer la personnalisation et de fournir une expérience utilisateur plus cohérente.

Les principaux microservices avec lesquels nous allons nous intégrer sont :

1. **Service utilisateur** : Pour accéder aux profils utilisateur complets et aux préférences
2. **Service de contenu** : Pour accéder aux métadonnées détaillées des retraites, cours, etc.
3. **Service de réservation** : Pour prendre en compte les disponibilités et les réservations passées
4. **Service de notification** : Pour envoyer des notifications personnalisées basées sur les recommandations

## Architecture d'intégration

L'architecture d'intégration suivra le modèle suivant :

```
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  Système de      |     |  API Gateway     |     |  Microservices   |
|  Recommandation  |<--->|                  |<--->|                  |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
        ^                        ^                        ^
        |                        |                        |
        v                        v                        v
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  Message Queue   |     |  Service Registry|     |  Config Server   |
|  (Kafka/RabbitMQ)|     |  (Eureka/Consul) |     |                  |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
```

### Patterns d'intégration

Nous utiliserons plusieurs patterns d'intégration selon les besoins :

1. **API REST** : Pour les communications synchrones directes
2. **Messaging** : Pour les communications asynchrones via Kafka ou RabbitMQ
3. **Event Sourcing** : Pour la propagation des événements importants
4. **CQRS** : Pour séparer les opérations de lecture et d'écriture

## Intégration avec le service utilisateur

### Objectifs

- Accéder aux profils utilisateur complets
- Synchroniser les préférences utilisateur
- Recevoir des notifications de mise à jour de profil

### API à implémenter

#### 1. API pour récupérer le profil utilisateur complet

```typescript
// Service côté système de recommandation
@Injectable()
export class UserProfileIntegrationService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async getUserProfile(userId: string): Promise<UserProfile> {
    try {
      const userServiceUrl = this.configService.get<string>('USER_SERVICE_URL');
      const { data } = await firstValueFrom(
        this.httpService.get(`${userServiceUrl}/api/users/${userId}/profile`)
      );
      return data;
    } catch (error) {
      // Gestion des erreurs et fallback
      this.logger.error(`Error fetching user profile: ${error.message}`);
      return this.getUserProfileFromCache(userId);
    }
  }
}
```

#### 2. Écouteur d'événements pour les mises à jour de profil

```typescript
// Service côté système de recommandation
@Injectable()
export class UserProfileEventListener {
  constructor(
    private readonly userProfileService: UserProfileService,
  ) {}

  @OnEvent('user.profile.updated')
  async handleUserProfileUpdated(payload: UserProfileUpdatedEvent) {
    await this.userProfileService.updateUserProfile(
      payload.userId,
      payload.profile
    );
  }
}
```

### Configuration Kafka pour les événements utilisateur

```typescript
// Configuration Kafka
const kafkaConfig = {
  clientId: 'recommendation-service',
  brokers: ['kafka:9092'],
  groupId: 'recommendation-service-group',
};

// Consumer pour les événements utilisateur
@Injectable()
export class UserEventsConsumer {
  constructor(
    private readonly userProfileService: UserProfileService,
  ) {}

  @Cron('*/5 * * * * *') // Toutes les 5 secondes
  async consumeUserEvents() {
    const consumer = kafka.consumer(kafkaConfig);
    await consumer.connect();
    await consumer.subscribe({ topic: 'user-events', fromBeginning: false });
    
    await consumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        const event = JSON.parse(message.value.toString());
        if (event.type === 'USER_PROFILE_UPDATED') {
          await this.userProfileService.updateUserProfile(
            event.payload.userId,
            event.payload.profile
          );
        }
      },
    });
  }
}
```

## Intégration avec le service de contenu

### Objectifs

- Accéder aux métadonnées détaillées des contenus
- Recevoir des notifications de nouveau contenu
- Synchroniser les données de contenu

### API à implémenter

#### 1. API pour récupérer les métadonnées de contenu

```typescript
// Service côté système de recommandation
@Injectable()
export class ContentIntegrationService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async getContentMetadata(contentId: string, type: string): Promise<ContentMetadata> {
    try {
      const contentServiceUrl = this.configService.get<string>('CONTENT_SERVICE_URL');
      const { data } = await firstValueFrom(
        this.httpService.get(`${contentServiceUrl}/api/content/${type}/${contentId}`)
      );
      return data;
    } catch (error) {
      // Gestion des erreurs et fallback
      this.logger.error(`Error fetching content metadata: ${error.message}`);
      return this.getContentMetadataFromCache(contentId, type);
    }
  }
}
```

#### 2. Écouteur d'événements pour les nouveaux contenus

```typescript
// Service côté système de recommandation
@Injectable()
export class ContentEventListener {
  constructor(
    private readonly contentService: ContentService,
  ) {}

  @OnEvent('content.created')
  async handleContentCreated(payload: ContentCreatedEvent) {
    await this.contentService.addNewContent(
      payload.contentId,
      payload.contentType,
      payload.metadata
    );
  }
}
```

## Intégration avec le service de réservation

### Objectifs

- Accéder aux disponibilités des retraites et cours
- Prendre en compte les réservations passées dans les recommandations
- Recevoir des notifications de nouvelles réservations

### API à implémenter

#### 1. API pour vérifier les disponibilités

```typescript
// Service côté système de recommandation
@Injectable()
export class ReservationIntegrationService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async checkAvailability(contentId: string, startDate: Date, endDate: Date): Promise<AvailabilityStatus> {
    try {
      const reservationServiceUrl = this.configService.get<string>('RESERVATION_SERVICE_URL');
      const { data } = await firstValueFrom(
        this.httpService.get(`${reservationServiceUrl}/api/availability/${contentId}`, {
          params: {
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
          },
        })
      );
      return data;
    } catch (error) {
      // Gestion des erreurs et fallback
      this.logger.error(`Error checking availability: ${error.message}`);
      return { available: false, reason: 'Error checking availability' };
    }
  }
}
```

#### 2. API pour récupérer l'historique des réservations

```typescript
// Service côté système de recommandation
@Injectable()
export class ReservationHistoryService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async getUserReservationHistory(userId: string): Promise<Reservation[]> {
    try {
      const reservationServiceUrl = this.configService.get<string>('RESERVATION_SERVICE_URL');
      const { data } = await firstValueFrom(
        this.httpService.get(`${reservationServiceUrl}/api/users/${userId}/reservations`)
      );
      return data;
    } catch (error) {
      // Gestion des erreurs et fallback
      this.logger.error(`Error fetching reservation history: ${error.message}`);
      return this.getUserReservationHistoryFromCache(userId);
    }
  }
}
```

## Intégration avec le service de notification

### Objectifs

- Envoyer des notifications de recommandations personnalisées
- Envoyer des notifications de nouveaux contenus recommandés
- Recevoir des retours sur les notifications

### API à implémenter

#### 1. API pour envoyer des notifications de recommandations

```typescript
// Service côté système de recommandation
@Injectable()
export class NotificationIntegrationService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async sendRecommendationNotification(
    userId: string,
    recommendationId: string,
    recommendationType: string,
    message: string,
  ): Promise<void> {
    try {
      const notificationServiceUrl = this.configService.get<string>('NOTIFICATION_SERVICE_URL');
      await firstValueFrom(
        this.httpService.post(`${notificationServiceUrl}/api/notifications`, {
          userId,
          type: 'RECOMMENDATION',
          data: {
            recommendationId,
            recommendationType,
          },
          message,
        })
      );
    } catch (error) {
      // Gestion des erreurs
      this.logger.error(`Error sending notification: ${error.message}`);
      // Enregistrer la notification pour une tentative ultérieure
      await this.queueNotificationForRetry(userId, recommendationId, recommendationType, message);
    }
  }
}
```

## Gestion des erreurs et résilience

Pour assurer la résilience du système, nous implémenterons les patterns suivants :

### 1. Circuit Breaker

```typescript
// Utilisation du pattern Circuit Breaker avec Hystrix
@Injectable()
export class ResilientHttpService {
  constructor(
    private readonly httpService: HttpService,
  ) {}

  @CircuitBreaker({
    fallback: (error, key, context) => this.handleFallback(error, key, context),
    options: {
      errorThresholdPercentage: 50,
      resetTimeout: 10000,
    },
  })
  async get(url: string, config?: AxiosRequestConfig): Promise<any> {
    return firstValueFrom(this.httpService.get(url, config));
  }

  private handleFallback(error: Error, key: string, context: any): any {
    // Logique de fallback
    this.logger.warn(`Circuit breaker triggered for ${key}: ${error.message}`);
    return { fallback: true, error: error.message };
  }
}
```

### 2. Retry Pattern

```typescript
// Utilisation du pattern Retry
@Injectable()
export class RetryService {
  constructor(
    private readonly httpService: HttpService,
  ) {}

  async executeWithRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000,
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        this.logger.warn(`Retry ${attempt}/${maxRetries} failed: ${error.message}`);
        
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delay * attempt));
        }
      }
    }
    
    throw lastError;
  }
}
```

### 3. Bulkhead Pattern

```typescript
// Utilisation du pattern Bulkhead
@Injectable()
export class BulkheadService {
  private semaphores: Map<string, Semaphore> = new Map();

  constructor() {
    // Initialiser les sémaphores pour chaque service
    this.semaphores.set('user-service', new Semaphore(10));
    this.semaphores.set('content-service', new Semaphore(10));
    this.semaphores.set('reservation-service', new Semaphore(5));
    this.semaphores.set('notification-service', new Semaphore(20));
  }

  async executeWithBulkhead<T>(
    serviceName: string,
    operation: () => Promise<T>,
  ): Promise<T> {
    const semaphore = this.semaphores.get(serviceName);
    
    if (!semaphore) {
      throw new Error(`No bulkhead configured for service: ${serviceName}`);
    }
    
    return semaphore.acquire(operation);
  }
}
```

## Sécurité et authentification

Pour sécuriser les communications entre les microservices, nous utiliserons :

### 1. Authentification par JWT

```typescript
// Middleware d'authentification pour les communications inter-services
@Injectable()
export class ServiceAuthMiddleware implements NestMiddleware {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  use(req: Request, res: Response, next: NextFunction) {
    try {
      // Générer un JWT pour l'authentification inter-services
      const serviceToken = this.jwtService.sign(
        {
          service: 'recommendation-service',
          timestamp: Date.now(),
        },
        {
          secret: this.configService.get<string>('SERVICE_JWT_SECRET'),
          expiresIn: '1m', // Courte durée de vie pour les tokens de service
        },
      );
      
      req.headers['service-authorization'] = `Bearer ${serviceToken}`;
      next();
    } catch (error) {
      throw new UnauthorizedException('Failed to authenticate service');
    }
  }
}
```

### 2. Communication sécurisée

```typescript
// Configuration pour les communications sécurisées
@Injectable()
export class SecureHttpService {
  constructor(
    private readonly httpService: HttpService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  async get(url: string, config?: AxiosRequestConfig): Promise<any> {
    const serviceToken = this.generateServiceToken();
    
    const secureConfig = {
      ...config,
      headers: {
        ...config?.headers,
        'Service-Authorization': `Bearer ${serviceToken}`,
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: true, // Vérifier les certificats SSL
      }),
    };
    
    return firstValueFrom(this.httpService.get(url, secureConfig));
  }

  private generateServiceToken(): string {
    return this.jwtService.sign(
      {
        service: 'recommendation-service',
        timestamp: Date.now(),
      },
      {
        secret: this.configService.get<string>('SERVICE_JWT_SECRET'),
        expiresIn: '1m',
      },
    );
  }
}
```

## Surveillance et observabilité

Pour surveiller les intégrations entre microservices, nous utiliserons :

### 1. Distributed Tracing

```typescript
// Configuration pour le tracing distribué avec OpenTelemetry
@Injectable()
export class TracingService {
  constructor(
    private readonly configService: ConfigService,
  ) {
    this.initTracing();
  }

  private initTracing() {
    const provider = new NodeTracerProvider();
    
    provider.addSpanProcessor(
      new BatchSpanProcessor(
        new JaegerExporter({
          serviceName: 'recommendation-service',
          endpoint: this.configService.get<string>('JAEGER_ENDPOINT'),
        }),
      ),
    );
    
    provider.register();
  }

  getTracer() {
    return trace.getTracer('recommendation-service');
  }
}
```

### 2. Métriques d'intégration

```typescript
// Service pour collecter des métriques d'intégration
@Injectable()
export class IntegrationMetricsService {
  private readonly requestCounter: Counter;
  private readonly requestLatency: Histogram;
  private readonly errorCounter: Counter;

  constructor() {
    // Initialiser les métriques Prometheus
    this.requestCounter = new Counter({
      name: 'integration_requests_total',
      help: 'Total number of integration requests',
      labelNames: ['service', 'endpoint', 'status'],
    });
    
    this.requestLatency = new Histogram({
      name: 'integration_request_duration_seconds',
      help: 'Integration request duration in seconds',
      labelNames: ['service', 'endpoint'],
      buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
    });
    
    this.errorCounter = new Counter({
      name: 'integration_errors_total',
      help: 'Total number of integration errors',
      labelNames: ['service', 'endpoint', 'error_type'],
    });
  }

  recordRequest(service: string, endpoint: string, status: string) {
    this.requestCounter.inc({ service, endpoint, status });
  }

  recordLatency(service: string, endpoint: string, durationMs: number) {
    this.requestLatency.observe({ service, endpoint }, durationMs / 1000);
  }

  recordError(service: string, endpoint: string, errorType: string) {
    this.errorCounter.inc({ service, endpoint, errorType });
  }
}
```

## Tests d'intégration

Pour tester les intégrations entre microservices, nous utiliserons :

### 1. Tests d'intégration avec des mocks

```typescript
// Test d'intégration avec le service utilisateur
describe('UserProfileIntegrationService', () => {
  let service: UserProfileIntegrationService;
  let httpMock: HttpServiceMock;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserProfileIntegrationService,
        {
          provide: HttpService,
          useClass: HttpServiceMock,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key) => {
              if (key === 'USER_SERVICE_URL') {
                return 'http://user-service';
              }
              return null;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<UserProfileIntegrationService>(UserProfileIntegrationService);
    httpMock = module.get(HttpService);
  });

  it('should fetch user profile successfully', async () => {
    const mockProfile = {
      id: 'user-1',
      name: 'Test User',
      preferences: {
        categories: ['yoga', 'meditation'],
      },
    };

    httpMock.get.mockReturnValue(of({ data: mockProfile }));

    const result = await service.getUserProfile('user-1');

    expect(httpMock.get).toHaveBeenCalledWith('http://user-service/api/users/user-1/profile');
    expect(result).toEqual(mockProfile);
  });

  it('should handle errors and use cache', async () => {
    httpMock.get.mockReturnValue(throwError(() => new Error('Service unavailable')));

    const mockCachedProfile = {
      id: 'user-1',
      name: 'Cached User',
      preferences: {
        categories: ['yoga'],
      },
    };

    jest.spyOn(service as any, 'getUserProfileFromCache').mockResolvedValue(mockCachedProfile);

    const result = await service.getUserProfile('user-1');

    expect(result).toEqual(mockCachedProfile);
  });
});
```

### 2. Tests d'intégration avec des conteneurs

```typescript
// Test d'intégration avec des conteneurs Docker
describe('Integration Tests with Containers', () => {
  let app: INestApplication;
  let userServiceContainer;
  let contentServiceContainer;

  beforeAll(async () => {
    // Démarrer les conteneurs Docker pour les services dépendants
    userServiceContainer = await new GenericContainer('user-service')
      .withExposedPorts(3001)
      .start();

    contentServiceContainer = await new GenericContainer('content-service')
      .withExposedPorts(3002)
      .start();

    // Configurer l'application avec les URLs des conteneurs
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(ConfigService)
      .useValue({
        get: jest.fn().mockImplementation((key) => {
          if (key === 'USER_SERVICE_URL') {
            return `http://${userServiceContainer.getHost()}:${userServiceContainer.getMappedPort(3001)}`;
          }
          if (key === 'CONTENT_SERVICE_URL') {
            return `http://${contentServiceContainer.getHost()}:${contentServiceContainer.getMappedPort(3002)}`;
          }
          return null;
        }),
      })
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
    await userServiceContainer.stop();
    await contentServiceContainer.stop();
  });

  it('should integrate with user service', async () => {
    const response = await request(app.getHttpServer())
      .get('/recommendations/personalized/user-1')
      .expect(200);

    expect(response.body).toBeDefined();
    expect(response.body.recommendations).toBeInstanceOf(Array);
  });
});
```
