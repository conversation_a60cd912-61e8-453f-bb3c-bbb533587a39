# Gestion des Certificats mTLS

Ce document décrit le système de gestion des certificats mTLS (mutual TLS) implémenté dans le projet Retreat And Be.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Services](#services)
3. [Configuration](#configuration)
4. [Utilisation](#utilisation)
5. [Rotation automatique](#rotation-automatique)
6. [API REST](#api-rest)
7. [Bonnes pratiques](#bonnes-pratiques)
8. [Dépannage](#dépannage)

## Vue d'ensemble

Le système de gestion des certificats mTLS permet de sécuriser les communications entre les microservices de la plateforme Retreat And Be. Il fournit des fonctionnalités pour :

- Générer des certificats pour les services
- Valider les certificats
- Révoquer les certificats
- Effectuer la rotation automatique des certificats
- Sauvegarder et restaurer les certificats

## Services

### CertificateManagementService

Ce service gère la création et la validation des certificats X.509 pour l'authentification mTLS. Il fournit des méthodes pour :

- Générer une autorité de certification (CA)
- Générer des certificats clients et serveurs
- Révoquer des certificats
- Vérifier la validité des certificats

### CertificateRotationService

Ce service gère la rotation automatique des certificats mTLS. Il fournit des fonctionnalités pour :

- Vérifier l'état de santé des certificats
- Effectuer la rotation des certificats avant leur expiration
- Sauvegarder les certificats avant rotation
- Restaurer des certificats à partir de sauvegardes
- Planifier la rotation automatique des certificats

## Configuration

La configuration des services de gestion des certificats se fait via les variables d'environnement suivantes :

```
# Zero Trust et mTLS
ZERO_TRUST_MTLS_ENABLED=true
MTLS_CERT_PATH=./certs/server.crt
MTLS_KEY_PATH=./certs/server.key
MTLS_CA_PATH=./certs/ca.crt
MTLS_REJECT_UNAUTHORIZED=true
MTLS_TRUSTED_CNS=client.retreatandbe.com
MTLS_TRUSTED_OUS=Clients
MTLS_CRL_PATH=./certs/ca.crl
MTLS_OCSP_ENABLED=false
CERTS_DIR=./certs

# Rotation des certificats
ENABLE_CERT_AUTO_ROTATION=true
CERT_ROTATION_THRESHOLD_DAYS=30
```

| Variable | Description | Valeur par défaut |
|----------|-------------|-------------------|
| `ZERO_TRUST_MTLS_ENABLED` | Active ou désactive l'authentification mTLS | `false` |
| `MTLS_CERT_PATH` | Chemin vers le certificat du service | `./certs/server.crt` |
| `MTLS_KEY_PATH` | Chemin vers la clé privée du service | `./certs/server.key` |
| `MTLS_CA_PATH` | Chemin vers le certificat de l'autorité de certification | `./certs/ca.crt` |
| `MTLS_REJECT_UNAUTHORIZED` | Rejette les connexions non autorisées | `true` |
| `MTLS_TRUSTED_CNS` | Liste des noms communs (CN) de confiance | `client.retreatandbe.com` |
| `MTLS_TRUSTED_OUS` | Liste des unités organisationnelles (OU) de confiance | `Clients` |
| `MTLS_CRL_PATH` | Chemin vers la liste de révocation de certificats (CRL) | `./certs/ca.crl` |
| `MTLS_OCSP_ENABLED` | Active ou désactive la vérification OCSP | `false` |
| `CERTS_DIR` | Répertoire de stockage des certificats | `./certs` |
| `ENABLE_CERT_AUTO_ROTATION` | Active ou désactive la rotation automatique des certificats | `true` |
| `CERT_ROTATION_THRESHOLD_DAYS` | Nombre de jours avant expiration pour déclencher la rotation | `30` |

## Utilisation

### Génération de certificats

Pour générer des certificats pour un nouveau service :

```typescript
import { CertificateManagementService } from './services/certificate-management.service';

@Injectable()
export class SomeService {
  constructor(
    private readonly certificateManagementService: CertificateManagementService
  ) {}

  async generateCertificates() {
    const { certPath, keyPath, caCertPath } = await this.certificateManagementService.generateServiceCertificates('my-service');
    console.log(`Certificates generated at: ${certPath}, ${keyPath}`);
    console.log(`CA certificate: ${caCertPath}`);
  }
}
```

### Vérification de certificats

Pour vérifier la validité d'un certificat :

```typescript
import { CertificateRotationService } from './services/certificate-rotation.service';

@Injectable()
export class SomeService {
  constructor(
    private readonly certificateRotationService: CertificateRotationService
  ) {}

  async verifyCertificate() {
    const result = await this.certificateRotationService.validateCertificate('./certs/my-service.crt');
    
    if (result.valid) {
      console.log(`Certificate is valid. Expires in ${result.details.expiresIn} days.`);
    } else {
      console.error(`Certificate is invalid: ${result.details.error}`);
    }
  }
}
```

### Rotation manuelle des certificats

Pour effectuer une rotation manuelle des certificats :

```typescript
import { CertificateRotationService } from './services/certificate-rotation.service';

@Injectable()
export class SomeService {
  constructor(
    private readonly certificateRotationService: CertificateRotationService
  ) {}

  async rotateCertificates() {
    const result = await this.certificateRotationService.rotateCertificates();
    
    if (result) {
      console.log('Certificate rotation completed successfully');
    } else {
      console.error('Certificate rotation failed');
    }
  }
}
```

## Rotation automatique

Le service `CertificateRotationService` effectue automatiquement la rotation des certificats avant leur expiration. Par défaut, la rotation est déclenchée 30 jours avant l'expiration du certificat.

La rotation automatique est planifiée pour s'exécuter tous les jours à minuit grâce à la tâche cron suivante :

```typescript
@Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
async scheduledCertificateRotation(): Promise<void> {
  // ...
}
```

Lors de la rotation, les certificats existants sont sauvegardés dans le répertoire `certs/backup/{timestamp}` avant d'être remplacés par de nouveaux certificats.

## API REST

Le contrôleur `CertificateController` expose les endpoints suivants pour gérer les certificats :

| Méthode | Endpoint | Description | Rôles |
|---------|----------|-------------|-------|
| `POST` | `/security/certificates/generate/:serviceName` | Génère un certificat pour un service | `admin` |
| `GET` | `/security/certificates/health` | Vérifie l'état de santé des certificats | `admin` |
| `POST` | `/security/certificates/rotate` | Effectue la rotation des certificats | `admin` |
| `GET` | `/security/certificates/backups` | Liste les sauvegardes de certificats disponibles | `admin` |
| `POST` | `/security/certificates/restore/:backupId` | Restaure des certificats à partir d'une sauvegarde | `admin` |

## Bonnes pratiques

1. **Sécurité des clés privées** : Assurez-vous que les clés privées sont stockées de manière sécurisée et ne sont accessibles qu'aux utilisateurs autorisés.

2. **Rotation régulière** : Effectuez la rotation des certificats régulièrement, même s'ils ne sont pas près d'expirer, pour limiter l'impact d'une compromission potentielle.

3. **Sauvegardes** : Sauvegardez régulièrement les certificats et les clés privées dans un emplacement sécurisé.

4. **Surveillance** : Surveillez l'état de santé des certificats et configurez des alertes pour être informé des problèmes potentiels.

5. **Validation** : Validez toujours les certificats clients lors des connexions mTLS pour éviter les attaques par usurpation d'identité.

## Dépannage

### Problèmes courants

1. **Certificat expiré** : Si un certificat a expiré, effectuez une rotation manuelle des certificats.

2. **Erreur de validation** : Vérifiez que le certificat a été signé par la bonne autorité de certification et que la chaîne de confiance est complète.

3. **Erreur de génération** : Assurez-vous que OpenSSL est installé et accessible dans le chemin système.

4. **Erreur de rotation** : Vérifiez les permissions des répertoires de certificats et assurez-vous que le service a les droits d'écriture nécessaires.

### Journalisation

Le service de gestion des certificats utilise le système de journalisation de NestJS pour enregistrer les événements importants. Consultez les journaux pour obtenir des informations détaillées sur les erreurs et les opérations effectuées.

```typescript
this.logger.log('Certificate rotation completed successfully');
this.logger.error('Failed to rotate certificates', error);
```

### Restauration

En cas de problème après une rotation de certificats, vous pouvez restaurer les certificats précédents à partir des sauvegardes :

```typescript
const backups = await this.certificateRotationService.listCertificateBackups();
const latestBackup = backups[0]; // Les sauvegardes sont triées par ordre chronologique inversé
await this.certificateRotationService.restoreCertificatesFromBackup(`certs/backup/${latestBackup}`);
```
