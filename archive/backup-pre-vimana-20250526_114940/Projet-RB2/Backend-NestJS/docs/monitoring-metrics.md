# Métriques de surveillance - Sprint 4

Ce document décrit les métriques clés à surveiller après le déploiement du Sprint 4 du système de recommandation de Retreat And Be.

## Table des matières

1. [Métriques de performance](#métriques-de-performance)
2. [Métriques d'utilisation](#métriques-dutilisation)
3. [Métriques de qualité](#métriques-de-qualité)
4. [Métriques d'infrastructure](#métriques-dinfrastructure)
5. [Alertes recommandées](#alertes-recommandées)
6. [Tableaux de bord](#tableaux-de-bord)

## Métriques de performance

### Temps de réponse

| Métrique | Description | Seuil d'alerte | Seuil critique |
|----------|-------------|----------------|----------------|
| `api.recommendations.response_time.avg` | Temps de réponse moyen pour l'API de recommandations | > 500 ms | > 1000 ms |
| `api.explanations.response_time.avg` | Temps de réponse moyen pour l'API d'explications | > 800 ms | > 1500 ms |
| `api.feedback.response_time.avg` | Temps de réponse moyen pour l'API de feedback | > 300 ms | > 600 ms |
| `api.moderation.response_time.avg` | Temps de réponse moyen pour l'API de modération | > 400 ms | > 800 ms |
| `api.analytics.response_time.avg` | Temps de réponse moyen pour l'API d'analytics | > 300 ms | > 600 ms |

### Débit

| Métrique | Description | Seuil d'alerte | Seuil critique |
|----------|-------------|----------------|----------------|
| `api.recommendations.requests_per_second` | Nombre de requêtes par seconde pour l'API de recommandations | > 100 | > 200 |
| `api.explanations.requests_per_second` | Nombre de requêtes par seconde pour l'API d'explications | > 50 | > 100 |
| `api.feedback.requests_per_second` | Nombre de requêtes par seconde pour l'API de feedback | > 30 | > 60 |
| `api.moderation.requests_per_second` | Nombre de requêtes par seconde pour l'API de modération | > 20 | > 40 |
| `api.analytics.requests_per_second` | Nombre de requêtes par seconde pour l'API d'analytics | > 50 | > 100 |

### Taux d'erreur

| Métrique | Description | Seuil d'alerte | Seuil critique |
|----------|-------------|----------------|----------------|
| `api.recommendations.error_rate` | Pourcentage de requêtes en erreur pour l'API de recommandations | > 1% | > 5% |
| `api.explanations.error_rate` | Pourcentage de requêtes en erreur pour l'API d'explications | > 1% | > 5% |
| `api.feedback.error_rate` | Pourcentage de requêtes en erreur pour l'API de feedback | > 1% | > 5% |
| `api.moderation.error_rate` | Pourcentage de requêtes en erreur pour l'API de modération | > 1% | > 5% |
| `api.analytics.error_rate` | Pourcentage de requêtes en erreur pour l'API d'analytics | > 1% | > 5% |

## Métriques d'utilisation

### Recommandations

| Métrique | Description | Seuil d'alerte | Seuil critique |
|----------|-------------|----------------|----------------|
| `recommendations.generated.count` | Nombre de recommandations générées | N/A | N/A |
| `recommendations.viewed.count` | Nombre de recommandations vues | N/A | N/A |
| `recommendations.clicked.count` | Nombre de recommandations cliquées | N/A | N/A |
| `recommendations.click_through_rate` | Taux de clic sur les recommandations | < 5% | < 2% |

### Explications

| Métrique | Description | Seuil d'alerte | Seuil critique |
|----------|-------------|----------------|----------------|
| `explanations.generated.count` | Nombre d'explications générées | N/A | N/A |
| `explanations.viewed.count` | Nombre d'explications vues | N/A | N/A |
| `explanations.view_rate` | Pourcentage de recommandations pour lesquelles l'explication a été vue | < 10% | < 5% |
| `explanations.detail_level.basic` | Nombre d'explications de niveau basique | N/A | N/A |
| `explanations.detail_level.standard` | Nombre d'explications de niveau standard | N/A | N/A |
| `explanations.detail_level.detailed` | Nombre d'explications de niveau détaillé | N/A | N/A |

### Feedback

| Métrique | Description | Seuil d'alerte | Seuil critique |
|----------|-------------|----------------|----------------|
| `feedback.count` | Nombre total de feedbacks | N/A | N/A |
| `feedback.like.count` | Nombre de likes | N/A | N/A |
| `feedback.dislike.count` | Nombre de dislikes | N/A | N/A |
| `feedback.save.count` | Nombre de sauvegardes | N/A | N/A |
| `feedback.hide.count` | Nombre de masquages | N/A | N/A |
| `feedback.report.count` | Nombre de signalements | N/A | N/A |
| `feedback.rate` | Pourcentage de recommandations ayant reçu un feedback | < 5% | < 2% |
| `feedback.positive_rate` | Pourcentage de feedbacks positifs | < 60% | < 40% |

### Modération

| Métrique | Description | Seuil d'alerte | Seuil critique |
|----------|-------------|----------------|----------------|
| `moderation.filtered.count` | Nombre de recommandations filtrées | > 10% | > 20% |
| `moderation.reported.count` | Nombre de recommandations signalées | > 5% | > 10% |
| `moderation.approved.count` | Nombre de recommandations approuvées | N/A | N/A |
| `moderation.rejected.count` | Nombre de recommandations rejetées | N/A | N/A |
| `moderation.pending.count` | Nombre de recommandations en attente de modération | > 100 | > 500 |

## Métriques de qualité

### Pertinence des recommandations

| Métrique | Description | Seuil d'alerte | Seuil critique |
|----------|-------------|----------------|----------------|
| `recommendations.relevance.score` | Score moyen de pertinence des recommandations (basé sur le feedback) | < 0.7 | < 0.5 |
| `recommendations.diversity.score` | Score de diversité des recommandations | < 0.6 | < 0.4 |
| `recommendations.serendipity.score` | Score de sérendipité des recommandations | < 0.5 | < 0.3 |

### Qualité des explications

| Métrique | Description | Seuil d'alerte | Seuil critique |
|----------|-------------|----------------|----------------|
| `explanations.helpfulness.score` | Score moyen d'utilité des explications (basé sur le feedback) | < 0.7 | < 0.5 |
| `explanations.clarity.score` | Score de clarté des explications | < 0.7 | < 0.5 |
| `explanations.personalization.rate` | Pourcentage d'explications personnalisées | < 70% | < 50% |

## Métriques d'infrastructure

### Utilisation des ressources

| Métrique | Description | Seuil d'alerte | Seuil critique |
|----------|-------------|----------------|----------------|
| `system.cpu.usage` | Utilisation CPU du serveur | > 70% | > 90% |
| `system.memory.usage` | Utilisation mémoire du serveur | > 70% | > 90% |
| `system.disk.usage` | Utilisation disque du serveur | > 70% | > 90% |
| `system.network.in` | Trafic réseau entrant | > 100 MB/s | > 200 MB/s |
| `system.network.out` | Trafic réseau sortant | > 100 MB/s | > 200 MB/s |

### Base de données

| Métrique | Description | Seuil d'alerte | Seuil critique |
|----------|-------------|----------------|----------------|
| `database.connections` | Nombre de connexions à la base de données | > 80% | > 95% |
| `database.query_time.avg` | Temps moyen d'exécution des requêtes | > 100 ms | > 500 ms |
| `database.size` | Taille de la base de données | > 70% | > 90% |
| `database.slow_queries` | Nombre de requêtes lentes | > 10 | > 50 |

### Services externes

| Métrique | Description | Seuil d'alerte | Seuil critique |
|----------|-------------|----------------|----------------|
| `external.moderation.availability` | Disponibilité du service de modération | < 99% | < 95% |
| `external.moderation.response_time` | Temps de réponse du service de modération | > 500 ms | > 1000 ms |
| `external.analytics.availability` | Disponibilité du service d'analytics | < 99% | < 95% |
| `external.analytics.response_time` | Temps de réponse du service d'analytics | > 500 ms | > 1000 ms |

## Alertes recommandées

### Alertes critiques (notification immédiate)

1. **Indisponibilité du service** : Le service est inaccessible pendant plus de 1 minute
2. **Taux d'erreur élevé** : Plus de 5% des requêtes sont en erreur pendant plus de 5 minutes
3. **Temps de réponse critique** : Temps de réponse moyen supérieur au seuil critique pendant plus de 5 minutes
4. **Utilisation CPU critique** : Utilisation CPU supérieure à 90% pendant plus de 5 minutes
5. **Utilisation mémoire critique** : Utilisation mémoire supérieure à 90% pendant plus de 5 minutes
6. **Indisponibilité de la base de données** : La base de données est inaccessible pendant plus de 1 minute

### Alertes importantes (notification dans l'heure)

1. **Taux d'erreur élevé pour un endpoint spécifique** : Plus de 5% des requêtes sont en erreur pour un endpoint spécifique
2. **Temps de réponse élevé pour un endpoint spécifique** : Temps de réponse moyen supérieur au seuil d'alerte pour un endpoint spécifique
3. **Indisponibilité d'un service externe** : Un service externe est inaccessible pendant plus de 5 minutes
4. **Taux de feedback négatif élevé** : Plus de 40% des feedbacks sont négatifs pendant plus de 1 heure
5. **Nombre élevé de signalements** : Plus de 10% des recommandations sont signalées pendant plus de 1 heure

### Alertes de surveillance (notification quotidienne)

1. **Baisse du taux de clic** : Taux de clic inférieur à 5% pendant plus de 24 heures
2. **Baisse du taux de feedback** : Taux de feedback inférieur à 5% pendant plus de 24 heures
3. **Baisse du score de pertinence** : Score de pertinence inférieur à 0.7 pendant plus de 24 heures
4. **Augmentation de l'utilisation disque** : Utilisation disque supérieure à 70% pendant plus de 24 heures
5. **Augmentation de la taille de la base de données** : Taille de la base de données supérieure à 70% pendant plus de 24 heures

## Tableaux de bord

### Tableau de bord opérationnel

Ce tableau de bord est destiné aux équipes d'exploitation et doit inclure :

1. **État du service** : Disponibilité, taux d'erreur, temps de réponse
2. **Utilisation des ressources** : CPU, mémoire, disque, réseau
3. **État de la base de données** : Connexions, temps de requête, taille
4. **État des services externes** : Disponibilité, temps de réponse
5. **Alertes actives** : Liste des alertes actives avec leur statut

### Tableau de bord produit

Ce tableau de bord est destiné aux équipes produit et doit inclure :

1. **Utilisation des recommandations** : Nombre de recommandations générées, vues, cliquées
2. **Utilisation des explications** : Nombre d'explications générées, vues, taux de visualisation
3. **Feedback utilisateur** : Nombre de feedbacks, répartition par type, taux de feedback positif
4. **Qualité des recommandations** : Score de pertinence, diversité, sérendipité
5. **Qualité des explications** : Score d'utilité, clarté, taux de personnalisation

### Tableau de bord exécutif

Ce tableau de bord est destiné aux dirigeants et doit inclure :

1. **KPIs principaux** : Taux de clic, taux de feedback positif, score de pertinence
2. **Tendances** : Évolution des KPIs sur les dernières semaines/mois
3. **Comparaison** : Comparaison avec les objectifs fixés
4. **Anomalies** : Mise en évidence des anomalies détectées
5. **Prochaines étapes** : Recommandations pour améliorer les KPIs
