# Bonnes pratiques - Système de recommandation

Ce document présente les bonnes pratiques pour l'utilisation et l'extension du système de recommandation de Retreat And Be.

## Table des matières

1. [Utilisation des API](#utilisation-des-api)
2. [Gestion des explications](#gestion-des-explications)
3. [Gestion du feedback](#gestion-du-feedback)
4. [Intégration avec la modération](#intégration-avec-la-modération)
5. [Intégration avec l'analytics](#intégration-avec-lanalytics)
6. [Extension du système](#extension-du-système)
7. [Optimisation des performances](#optimisation-des-performances)
8. [Sécurité](#sécurité)

## Utilisation des API

### Récupération des recommandations

✅ **À faire**
- Limiter le nombre de recommandations demandées (10-20 maximum)
- Utiliser les filtres disponibles pour affiner les résultats
- Mettre en cache les résultats côté client pour éviter des appels répétés
- Implémenter une stratégie de chargement progressif (lazy loading)

❌ **À éviter**
- Demander un grand nombre de recommandations en une seule requête
- Effectuer des appels répétés à l'API sans mise en cache
- Ignorer les paramètres de filtrage disponibles

**Exemple de bonne pratique :**
```typescript
// Récupérer un nombre limité de recommandations avec des filtres pertinents
const recommendations = await recommendationService.getRecommendations({
  type: RecommendationType.RETREAT,
  limit: 10,
  filters: {
    categories: userPreferences.categories,
    location: userPreferences.location,
    priceRange: userPreferences.priceRange,
  },
});

// Mettre en cache les résultats
cacheService.set('user_recommendations', recommendations, 3600); // Cache pour 1 heure
```

### Pagination

✅ **À faire**
- Utiliser la pagination pour toutes les requêtes qui peuvent retourner de nombreux résultats
- Respecter les paramètres de pagination (page, limit)
- Afficher les informations de pagination à l'utilisateur

❌ **À éviter**
- Ignorer la pagination et demander tous les résultats en une seule requête
- Utiliser des valeurs de limite trop élevées

**Exemple de bonne pratique :**
```typescript
// Récupérer les feedbacks avec pagination
const feedbacks = await feedbackService.getUserFeedbacks(userId, {
  page: currentPage,
  limit: 10,
  feedbackType: selectedFeedbackType,
});

// Afficher les informations de pagination
renderPagination(feedbacks.pagination.page, feedbacks.pagination.pages);
```

## Gestion des explications

### Niveau de détail

✅ **À faire**
- Adapter le niveau de détail au contexte d'utilisation
- Utiliser le niveau BASIC pour les aperçus rapides
- Utiliser le niveau STANDARD pour la plupart des cas
- Utiliser le niveau DETAILED uniquement lorsque l'utilisateur demande plus d'informations

❌ **À éviter**
- Utiliser systématiquement le niveau DETAILED, ce qui peut surcharger l'utilisateur
- Ignorer le paramètre de niveau de détail

**Exemple de bonne pratique :**
```typescript
// Utiliser le niveau de détail approprié selon le contexte
const explanation = await explanationService.generateEnhancedExplanation(
  recommendationId,
  recommendationType,
  userId,
  {
    detailLevel: isDetailView ? 'DETAILED' : 'STANDARD',
    includeVisualizations: isDetailView,
  }
);
```

### Visualisations

✅ **À faire**
- Inclure les visualisations uniquement lorsqu'elles apportent une valeur ajoutée
- Adapter les visualisations au type de données présentées
- Offrir des options pour masquer/afficher les visualisations

❌ **À éviter**
- Inclure systématiquement toutes les visualisations possibles
- Utiliser des visualisations complexes sans explication

**Exemple de bonne pratique :**
```jsx
// Afficher les visualisations de manière conditionnelle
<EnhancedExplanation
  recommendationId={recommendationId}
  recommendationType={recommendationType}
  detailLevel="STANDARD"
  includeVisualizations={userPreferences.showVisualizations}
/>
```

## Gestion du feedback

### Collecte de feedback

✅ **À faire**
- Proposer différents types de feedback (like, dislike, save, hide, report)
- Rendre la collecte de feedback simple et non intrusive
- Expliquer comment le feedback sera utilisé
- Offrir des options pour un feedback plus détaillé

❌ **À éviter**
- Demander trop de feedback à la fois
- Rendre le feedback obligatoire
- Ignorer le feedback collecté

**Exemple de bonne pratique :**
```jsx
// Proposer différents types de feedback de manière non intrusive
<FeedbackButtons
  recommendationId={recommendationId}
  recommendationType={recommendationType}
  onFeedbackSubmitted={handleFeedbackSubmitted}
  showDetailedOptions={false} // N'afficher les options détaillées que sur demande
/>
```

### Utilisation du feedback

✅ **À faire**
- Utiliser le feedback pour améliorer les recommandations futures
- Analyser les tendances de feedback pour identifier les problèmes
- Informer l'utilisateur de l'impact de son feedback

❌ **À éviter**
- Collecter du feedback sans l'utiliser
- Ignorer les tendances négatives dans le feedback

**Exemple de bonne pratique :**
```typescript
// Analyser les tendances de feedback
const feedbackAnalysis = await analyticsService.analyzeFeedbackTrends(
  recommendationType,
  timeRange
);

// Identifier les problèmes
const problematicRecommendations = feedbackAnalysis.recommendations
  .filter(rec => rec.negativeRatio > 0.5)
  .sort((a, b) => b.negativeRatio - a.negativeRatio);
```

## Intégration avec la modération

### Filtrage des recommandations

✅ **À faire**
- Filtrer les recommandations avant de les présenter à l'utilisateur
- Mettre en cache les résultats de modération pour éviter des appels répétés
- Gérer gracieusement les cas où le service de modération est indisponible

❌ **À éviter**
- Ignorer le filtrage de modération
- Effectuer des appels répétés au service de modération pour les mêmes recommandations

**Exemple de bonne pratique :**
```typescript
// Filtrer les recommandations avant de les présenter
const recommendations = await recommendationService.getRecommendations(options);
const filteredRecommendations = await moderationService.filterRecommendations(
  recommendations,
  options.type
);

// Mettre en cache les résultats
cacheService.set('filtered_recommendations', filteredRecommendations, 3600);
```

### Signalement de contenu

✅ **À faire**
- Rendre le signalement de contenu facilement accessible
- Demander une raison pour le signalement
- Informer l'utilisateur de la suite donnée à son signalement

❌ **À éviter**
- Cacher l'option de signalement
- Ignorer les signalements reçus
- Ne pas donner de feedback à l'utilisateur après un signalement

**Exemple de bonne pratique :**
```jsx
// Proposer une option de signalement claire
<ReportButton
  recommendationId={recommendationId}
  recommendationType={recommendationType}
  onReportSubmitted={(reason) => {
    notifyUser('Merci pour votre signalement. Notre équipe va l\'examiner.');
    trackReportSubmitted(recommendationId, reason);
  }}
/>
```

## Intégration avec l'analytics

### Suivi des événements

✅ **À faire**
- Suivre les événements clés (vue, clic, feedback, etc.)
- Inclure des métadonnées pertinentes avec chaque événement
- Respecter la vie privée des utilisateurs
- Gérer gracieusement les cas où le service d'analytics est indisponible

❌ **À éviter**
- Suivre trop d'événements non pertinents
- Inclure des données personnelles sensibles dans les événements
- Ignorer les erreurs du service d'analytics

**Exemple de bonne pratique :**
```typescript
// Suivre un événement de clic avec des métadonnées pertinentes
analyticsService.trackRecommendationClicked(
  userId,
  recommendationId,
  recommendationType,
  {
    position: index,
    source: 'homepage',
    timeSpentBeforeClick: timeSpent,
  }
);
```

### Analyse des données

✅ **À faire**
- Analyser régulièrement les données d'analytics
- Utiliser les insights pour améliorer le système
- Partager les insights pertinents avec les équipes concernées

❌ **À éviter**
- Collecter des données sans les analyser
- Ignorer les tendances négatives dans les données

**Exemple de bonne pratique :**
```typescript
// Analyser les données d'analytics régulièrement
const weeklyReport = await analyticsService.generateWeeklyReport();
const insights = weeklyReport.getInsights();

// Partager les insights avec les équipes concernées
notifyTeams(insights);
```

## Extension du système

### Ajout de nouveaux types de recommandation

✅ **À faire**
- Suivre le modèle existant pour les nouveaux types
- Mettre à jour l'énumération RecommendationType
- Implémenter les méthodes spécifiques au nouveau type
- Ajouter des tests pour le nouveau type

❌ **À éviter**
- Dupliquer le code existant
- Ignorer les fonctionnalités existantes (explications, feedback, etc.)

**Exemple de bonne pratique :**
```typescript
// Ajouter un nouveau type de recommandation
export enum RecommendationType {
  RETREAT = 'RETREAT',
  COURSE = 'COURSE',
  VIDEO = 'VIDEO',
  ARTICLE = 'ARTICLE',
  EVENT = 'EVENT',
  WORKSHOP = 'WORKSHOP', // Nouveau type
}

// Implémenter les méthodes spécifiques
class WorkshopRecommendationService extends BaseRecommendationService {
  // Implémentation spécifique aux ateliers
}
```

### Personnalisation des explications

✅ **À faire**
- Utiliser les données utilisateur disponibles pour personnaliser les explications
- Respecter la vie privée des utilisateurs
- Offrir des options pour désactiver la personnalisation

❌ **À éviter**
- Utiliser des données sensibles dans les explications
- Ignorer les préférences de l'utilisateur concernant la personnalisation

**Exemple de bonne pratique :**
```typescript
// Personnaliser les explications en fonction des préférences de l'utilisateur
const explanation = await explanationService.generateEnhancedExplanation(
  recommendationId,
  recommendationType,
  userId,
  {
    personalize: userPreferences.allowPersonalization,
    language: userPreferences.language,
  }
);
```

## Optimisation des performances

### Mise en cache

✅ **À faire**
- Mettre en cache les résultats fréquemment demandés
- Utiliser des stratégies de cache adaptées à chaque type de données
- Invalider le cache lorsque les données changent

❌ **À éviter**
- Mettre en cache des données personnalisées pour tous les utilisateurs
- Utiliser des durées de cache trop longues pour des données qui changent fréquemment

**Exemple de bonne pratique :**
```typescript
// Stratégie de cache adaptée à chaque type de données
const recommendations = await cacheService.getOrSet(
  `recommendations:${type}:${JSON.stringify(filters)}`,
  () => recommendationService.getRecommendations(type, filters),
  3600 // 1 heure pour les recommandations générales
);

const userFeedback = await cacheService.getOrSet(
  `feedback:user:${userId}`,
  () => feedbackService.getUserFeedbacks(userId),
  300 // 5 minutes pour les données utilisateur qui changent plus fréquemment
);
```

### Chargement progressif

✅ **À faire**
- Implémenter le chargement progressif pour les listes longues
- Charger les données détaillées uniquement lorsqu'elles sont nécessaires
- Utiliser des placeholders pendant le chargement

❌ **À éviter**
- Charger toutes les données en une seule requête
- Afficher des écrans vides pendant le chargement

**Exemple de bonne pratique :**
```jsx
// Chargement progressif des recommandations
<RecommendationList
  initialRecommendations={initialRecommendations}
  loadMore={async (page) => {
    const moreRecommendations = await recommendationService.getRecommendations({
      type: RecommendationType.RETREAT,
      page,
      limit: 10,
    });
    return moreRecommendations;
  }}
  renderLoading={() => <RecommendationSkeleton count={3} />}
/>
```

## Sécurité

### Authentification et autorisation

✅ **À faire**
- Vérifier l'authentification pour toutes les API
- Vérifier les autorisations pour les opérations sensibles
- Utiliser des tokens JWT avec une durée de validité limitée

❌ **À éviter**
- Exposer des API sensibles sans authentification
- Utiliser des tokens avec une durée de validité trop longue

**Exemple de bonne pratique :**
```typescript
// Vérifier l'authentification et les autorisations
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
@Get('analytics/events/user/:userId')
async getUserEvents(@Param('userId') userId: string) {
  // Seuls les administrateurs peuvent accéder à cette API
}
```

### Validation des données

✅ **À faire**
- Valider toutes les données d'entrée
- Utiliser des DTOs avec des validateurs
- Échapper les données sensibles avant de les afficher

❌ **À éviter**
- Faire confiance aux données d'entrée sans validation
- Afficher des données non échappées dans l'interface utilisateur

**Exemple de bonne pratique :**
```typescript
// Valider les données d'entrée avec des DTOs
export class RecordFeedbackDto {
  @IsUUID()
  recommendationId: string;

  @IsEnum(RecommendationType)
  recommendationType: RecommendationType;

  @IsEnum(FeedbackType)
  feedbackType: FeedbackType;

  @IsOptional()
  @IsString()
  @MaxLength(500)
  comment?: string;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(5)
  rating?: number;
}
```
