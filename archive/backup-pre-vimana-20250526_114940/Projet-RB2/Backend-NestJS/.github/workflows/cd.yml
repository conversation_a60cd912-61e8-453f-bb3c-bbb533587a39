name: CD

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'

      - name: Build Docker image
        run: |
          docker build -t backend-nestjs:${{ github.sha }} .

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Push Docker image
        run: |
          docker tag backend-nestjs:${{ github.sha }} ${{ secrets.DOCKERHUB_USERNAME }}/backend-nestjs:${{ github.sha }}
          docker push ${{ secrets.DOCKERHUB_USERNAME }}/backend-nestjs:${{ github.sha }}

      - name: Set up Kubeconfig
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.29.0'

      - name: Deploy to Kubernetes
        env:
          KUBE_CONFIG_DATA: ${{ secrets.KUBE_CONFIG_PRODUCTION }}
        run: |
          echo "$KUBE_CONFIG_DATA" | base64 --decode > kubeconfig
          export KUBECONFIG=$(pwd)/kubeconfig
          kubectl set image deployment/backend-nestjs backend-nestjs=${{ secrets.DOCKERHUB_USERNAME }}/backend-nestjs:${{ github.sha }} -n production
          kubectl rollout status deployment/backend-nestjs -n production
