name: CI Compliance & Security

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  compliance:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        ports: [5432:5432]
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install dependencies
        run: npm install
        working-directory: ./Backend-NestJS

      - name: Build backend
        run: npm run build
        working-directory: ./Backend-NestJS

      - name: Start backend (background)
        run: npm run start:prod &
        working-directory: ./Backend-NestJS

      - name: Wait for API to be ready
        run: |
          for i in {1..20}; do
            curl -s http://localhost:3000/health && exit 0 || sleep 5;
          done
          echo "API non disponible" && exit 1
        working-directory: ./Backend-NestJS

      - name: Run compliance check (fail on non-compliance)
        run: bash scripts/compliance-check.sh
        working-directory: ./Backend-NestJS

      - name: Generate PDF report
        run: node scripts/compliance-report-to-pdf.js
        working-directory: ./Backend-NestJS

      - name: Archive reports to S3
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION: ${{ secrets.AWS_DEFAULT_REGION }}
        run: bash scripts/compliance-archive-s3.sh
        working-directory: ./Backend-NestJS

      - name: Send compliance report by email
        env:
          EMAIL_PASSWORD: ${{ secrets.EMAIL_PASSWORD }}
        run: bash scripts/compliance-notify-email.sh
        working-directory: ./Backend-NestJS

      - name: Upload Markdown and PDF as workflow artifacts
        uses: actions/upload-artifact@v3
        with:
          name: compliance-reports
          path: |
            Backend-NestJS/compliance-report.md
            Backend-NestJS/compliance-report.pdf
