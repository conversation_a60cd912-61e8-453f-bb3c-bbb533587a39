{"name": "retreat-and-be-backend", "version": "0.1.0", "description": "NestJS Backend for Retreat And Be platform", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "start:mtls": "node dist/main-mtls", "start:mtls:dev": "nest start --watch --entryFile main-mtls", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:unit": "jest --testPathPattern=src/.*\\.spec\\.ts$", "test:integration": "jest --testPathPattern=test/integration/.*\\.spec\\.ts$", "test:performance": "jest --testPathPattern=test/performance/.*\\.spec\\.ts$", "test:security": "jest --testPathPattern=test/security/.*\\.spec\\.ts$", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "test:ci": "jest --coverage --watchAll=false --passWithNoTests", "report:performance": "ts-node scripts/performance-report.ts", "report:finalization": "ts-node scripts/finalization-check.ts", "report:finalization-simple": "ts-node scripts/simple-finalization-check.ts", "report:all": "npm run report:performance && npm run report:finalization", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "certs:generate": "bash ./scripts/generate-certificates.sh"}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^7.1.17", "@nestjs/terminus": "^11.0.0", "@prisma/client": "^5.7.1", "@types/otplib": "^7.0.0", "axios": "^1.6.7", "bcrypt": "^5.1.1", "cache-manager": "^6.4.2", "cache-manager-redis-store": "^3.0.1", "canvas": "^3.1.0", "chalk": "^5.4.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cli-table3": "^0.6.5", "commander": "^14.0.0", "compression": "^1.8.0", "cors": "^2.8.5", "exceljs": "^4.4.0", "express-rate-limit": "^7.5.0", "fast-csv": "^5.0.2", "handlebars": "^4.7.8", "helmet": "^7.1.0", "nestjs-i18n": "^10.5.1", "node-seal": "^5.1.5", "node-vault": "^0.10.2", "nodemailer": "^6.10.1", "ora": "^8.2.0", "otplib": "^12.0.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdfkit": "^0.17.1", "prom-client": "^15.1.3", "qrcode": "^1.5.4", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "speakeasy": "^2.0.0", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.4"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.4.17", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/supertest": "^2.0.12", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^5.7.1", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}}