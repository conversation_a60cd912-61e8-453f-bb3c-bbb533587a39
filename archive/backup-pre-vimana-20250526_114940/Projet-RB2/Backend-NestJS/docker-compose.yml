version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: retreat-backend-nestjs
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DATABASE_URL=****************************************/retreat_and_be?schema=public
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-jwt-secret-key
      - JWT_REFRESH_SECRET=your-refresh-secret-key
      - JWT_ACCESS_EXPIRES_IN=15m
      - JWT_REFRESH_EXPIRES_IN=7d
      - CORS_ORIGIN=http://localhost:3000
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - retreat-network
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    container_name: retreat-postgres
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=retreat_and_be
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - retreat-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d retreat_and_be"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: retreat-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - retreat-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Outil de gestion de base de données
  pgadmin:
    image: dpage/pgadmin4
    container_name: retreat-pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - retreat-network
    restart: unless-stopped

  # Outil de gestion Redis
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: retreat-redis-commander
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - retreat-network
    restart: unless-stopped

networks:
  retreat-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
