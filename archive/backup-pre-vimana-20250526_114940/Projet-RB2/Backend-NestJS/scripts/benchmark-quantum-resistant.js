/**
 * Script de benchmark pour le service QuantumResistantService
 * 
 * Ce script teste les performances des différentes méthodes de chiffrement
 * et déchiffrement du service QuantumResistantService.
 * 
 * Usage: node benchmark-quantum-resistant.js
 */

const crypto = require('crypto');
const { performance } = require('perf_hooks');

// Simuler l'environnement NestJS
const configService = {
  get: (key, defaultValue) => {
    const config = {
      'QUANTUM_RESISTANT_ENABLED': true,
      'QUANTUM_RESISTANT_ALGORITHM': 'hybrid',
      'QUANTUM_RESISTANT_KEY_SIZE': 3072,
      'QUANTUM_RESISTANT_HYBRID_CLASSICAL_ALGORITHM': 'rsa',
      'QUANTUM_RESISTANT_HYBRID_CLASSICAL_KEY_SIZE': 2048,
    };
    return config[key] !== undefined ? config[key] : defaultValue;
  }
};

// Importer dynamiquement le service
async function importService() {
  try {
    // Essayer d'importer liboqs-node
    let oqs = null;
    try {
      oqs = require('liboqs-node');
      console.log('liboqs-node détecté, utilisation du mode natif');
    } catch (e) {
      console.log('liboqs-node non détecté, utilisation du mode simulation');
    }

    // Importer le service
    const { QuantumResistantService } = require('../dist/modules/security/services/quantum-resistant.service');
    const service = new QuantumResistantService(configService);
    await service.generateKeys();
    return service;
  } catch (error) {
    console.error('Erreur lors de l\'importation du service:', error);
    throw error;
  }
}

// Fonction de benchmark
async function runBenchmark(service) {
  console.log('=== Benchmark du service QuantumResistantService ===');
  
  // Données de test de différentes tailles
  const testSizes = [
    { name: 'Petit (1 KB)', size: 1 * 1024 },
    { name: 'Moyen (10 KB)', size: 10 * 1024 },
    { name: 'Grand (100 KB)', size: 100 * 1024 },
  ];
  
  for (const { name, size } of testSizes) {
    console.log(`\n--- Test avec données ${name} ---`);
    const testData = crypto.randomBytes(size);
    
    // Mesurer le temps de chiffrement
    console.log(`Chiffrement de ${size} octets...`);
    const encryptStart = performance.now();
    const encryptedData = await service.encrypt(testData);
    const encryptEnd = performance.now();
    const encryptTime = encryptEnd - encryptStart;
    
    console.log(`Taille des données chiffrées: ${encryptedData.length} octets`);
    console.log(`Temps de chiffrement: ${encryptTime.toFixed(2)} ms`);
    console.log(`Débit de chiffrement: ${((size / 1024) / (encryptTime / 1000)).toFixed(2)} KB/s`);
    
    // Mesurer le temps de déchiffrement
    console.log(`\nDéchiffrement de ${encryptedData.length} octets...`);
    const decryptStart = performance.now();
    const decryptedData = await service.decrypt(encryptedData);
    const decryptEnd = performance.now();
    const decryptTime = decryptEnd - decryptStart;
    
    console.log(`Temps de déchiffrement: ${decryptTime.toFixed(2)} ms`);
    console.log(`Débit de déchiffrement: ${((size / 1024) / (decryptTime / 1000)).toFixed(2)} KB/s`);
    
    // Vérifier que les données déchiffrées correspondent aux données originales
    const isValid = Buffer.compare(testData, decryptedData) === 0;
    console.log(`Validation: ${isValid ? 'Réussi ✅' : 'Échec ❌'}`);
  }
}

// Exécuter le benchmark
async function main() {
  try {
    const service = await importService();
    await runBenchmark(service);
  } catch (error) {
    console.error('Erreur lors de l\'exécution du benchmark:', error);
  }
}

main();
