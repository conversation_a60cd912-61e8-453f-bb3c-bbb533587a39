#!/bin/bash

# Script pour préparer l'infrastructure d'apprentissage automatique pour le Sprint 5
# Ce script installe et configure les outils nécessaires pour l'apprentissage automatique,
# crée les dossiers requis et configure l'environnement.

# Couleurs pour les messages
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Préparation de l'infrastructure d'apprentissage automatique pour le Sprint 5 ===${NC}"

# Vérifier si Docker est installé
if ! [ -x "$(command -v docker)" ]; then
  echo -e "${RED}Erreur: Docker n'est pas installé.${NC}" >&2
  echo -e "${YELLOW}Installation de Docker...${NC}"
  
  # Installer Docker selon le système d'exploitation
  if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    sudo apt-get update
    sudo apt-get install -y apt-transport-https ca-certificates curl software-properties-common
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -
    sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"
    sudo apt-get update
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io
  elif [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    echo -e "${YELLOW}Veuillez installer Docker Desktop pour macOS depuis https://www.docker.com/products/docker-desktop${NC}"
    exit 1
  else
    echo -e "${RED}Système d'exploitation non pris en charge.${NC}"
    exit 1
  fi
fi

# Vérifier si Docker Compose est installé
if ! [ -x "$(command -v docker-compose)" ]; then
  echo -e "${RED}Erreur: Docker Compose n'est pas installé.${NC}" >&2
  echo -e "${YELLOW}Installation de Docker Compose...${NC}"
  
  # Installer Docker Compose
  sudo curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
  sudo chmod +x /usr/local/bin/docker-compose
fi

# Créer les dossiers nécessaires
echo -e "${YELLOW}Création des dossiers pour l'infrastructure d'apprentissage automatique...${NC}"

mkdir -p ml-infrastructure/data
mkdir -p ml-infrastructure/models
mkdir -p ml-infrastructure/notebooks
mkdir -p ml-infrastructure/scripts
mkdir -p ml-infrastructure/logs
mkdir -p ml-infrastructure/config

# Créer le fichier docker-compose.yml
echo -e "${YELLOW}Création du fichier docker-compose.yml...${NC}"

cat > ml-infrastructure/docker-compose.yml << EOF
version: '3'

services:
  # Service Jupyter pour le développement et l'expérimentation
  jupyter:
    image: jupyter/tensorflow-notebook:latest
    container_name: rb-jupyter
    ports:
      - "8888:8888"
    volumes:
      - ./notebooks:/home/<USER>/work
      - ./data:/home/<USER>/data
      - ./models:/home/<USER>/models
      - ./scripts:/home/<USER>/scripts
    environment:
      - JUPYTER_ENABLE_LAB=yes
    restart: unless-stopped

  # Service MLflow pour le suivi des expériences et la gestion des modèles
  mlflow:
    image: ghcr.io/mlflow/mlflow:latest
    container_name: rb-mlflow
    ports:
      - "5000:5000"
    volumes:
      - ./mlflow:/mlflow
    environment:
      - MLFLOW_TRACKING_URI=sqlite:///mlflow/mlflow.db
    command: mlflow server --backend-store-uri sqlite:///mlflow/mlflow.db --default-artifact-root /mlflow/artifacts --host 0.0.0.0
    restart: unless-stopped

  # Service PostgreSQL pour stocker les données d'apprentissage
  postgres-ml:
    image: postgres:13
    container_name: rb-postgres-ml
    ports:
      - "5433:5432"
    volumes:
      - ./postgres-data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=mluser
      - POSTGRES_PASSWORD=mlpassword
      - POSTGRES_DB=mldb
    restart: unless-stopped

  # Service Redis pour le cache et les files d'attente
  redis:
    image: redis:6
    container_name: rb-redis
    ports:
      - "6379:6379"
    volumes:
      - ./redis-data:/data
    restart: unless-stopped

  # Service Celery pour les tâches asynchrones
  celery:
    build:
      context: .
      dockerfile: Dockerfile.celery
    container_name: rb-celery
    volumes:
      - ./scripts:/app/scripts
      - ./models:/app/models
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - redis
    restart: unless-stopped
EOF

# Créer le Dockerfile pour Celery
echo -e "${YELLOW}Création du Dockerfile pour Celery...${NC}"

cat > ml-infrastructure/Dockerfile.celery << EOF
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

CMD ["celery", "-A", "scripts.tasks", "worker", "--loglevel=info"]
EOF

# Créer le fichier requirements.txt
echo -e "${YELLOW}Création du fichier requirements.txt...${NC}"

cat > ml-infrastructure/requirements.txt << EOF
celery==5.2.7
redis==4.3.4
scikit-learn==1.0.2
pandas==1.4.2
numpy==1.22.3
tensorflow==2.9.1
mlflow==1.26.1
psycopg2-binary==2.9.3
matplotlib==3.5.2
seaborn==0.11.2
jupyter==1.0.0
Flask==2.1.2
gunicorn==20.1.0
EOF

# Créer un script Python pour les tâches Celery
echo -e "${YELLOW}Création du script pour les tâches Celery...${NC}"

mkdir -p ml-infrastructure/scripts

cat > ml-infrastructure/scripts/tasks.py << EOF
from celery import Celery
import os
import json
import logging
from datetime import datetime

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/app/logs/celery.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration de Celery
app = Celery('tasks')
app.conf.broker_url = os.environ.get('CELERY_BROKER_URL', 'redis://localhost:6379/0')
app.conf.result_backend = os.environ.get('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')

@app.task
def train_model(model_type, parameters, dataset_path):
    """
    Tâche pour entraîner un modèle d'apprentissage automatique
    
    Args:
        model_type (str): Type de modèle à entraîner
        parameters (dict): Paramètres du modèle
        dataset_path (str): Chemin vers le jeu de données
        
    Returns:
        dict: Résultats de l'entraînement
    """
    logger.info(f"Début de l'entraînement du modèle {model_type}")
    
    try:
        # Ici, nous simulerons l'entraînement du modèle
        # Dans une implémentation réelle, nous utiliserions scikit-learn, TensorFlow, etc.
        
        # Simuler un temps d'entraînement
        import time
        import random
        time.sleep(10)
        
        # Simuler des métriques d'entraînement
        metrics = {
            'accuracy': random.uniform(0.7, 0.95),
            'precision': random.uniform(0.7, 0.95),
            'recall': random.uniform(0.7, 0.95),
            'f1_score': random.uniform(0.7, 0.95),
        }
        
        # Enregistrer le modèle et les métriques
        model_id = f"{model_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        model_path = f"/app/models/{model_id}.json"
        
        with open(model_path, 'w') as f:
            json.dump({
                'model_id': model_id,
                'model_type': model_type,
                'parameters': parameters,
                'metrics': metrics,
                'trained_at': datetime.now().isoformat(),
            }, f)
        
        logger.info(f"Modèle {model_id} entraîné avec succès")
        
        return {
            'success': True,
            'model_id': model_id,
            'model_path': model_path,
            'metrics': metrics,
        }
    
    except Exception as e:
        logger.error(f"Erreur lors de l'entraînement du modèle: {str(e)}")
        return {
            'success': False,
            'error': str(e),
        }

@app.task
def evaluate_model(model_id, test_dataset_path):
    """
    Tâche pour évaluer un modèle sur un jeu de données de test
    
    Args:
        model_id (str): ID du modèle à évaluer
        test_dataset_path (str): Chemin vers le jeu de données de test
        
    Returns:
        dict: Résultats de l'évaluation
    """
    logger.info(f"Début de l'évaluation du modèle {model_id}")
    
    try:
        # Ici, nous simulerons l'évaluation du modèle
        # Dans une implémentation réelle, nous chargerions le modèle et le jeu de données
        
        # Simuler un temps d'évaluation
        import time
        import random
        time.sleep(5)
        
        # Simuler des métriques d'évaluation
        metrics = {
            'accuracy': random.uniform(0.7, 0.95),
            'precision': random.uniform(0.7, 0.95),
            'recall': random.uniform(0.7, 0.95),
            'f1_score': random.uniform(0.7, 0.95),
        }
        
        # Enregistrer les résultats de l'évaluation
        eval_id = f"eval_{model_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        eval_path = f"/app/models/{eval_id}.json"
        
        with open(eval_path, 'w') as f:
            json.dump({
                'eval_id': eval_id,
                'model_id': model_id,
                'metrics': metrics,
                'evaluated_at': datetime.now().isoformat(),
            }, f)
        
        logger.info(f"Modèle {model_id} évalué avec succès")
        
        return {
            'success': True,
            'eval_id': eval_id,
            'eval_path': eval_path,
            'metrics': metrics,
        }
    
    except Exception as e:
        logger.error(f"Erreur lors de l'évaluation du modèle: {str(e)}")
        return {
            'success': False,
            'error': str(e),
        }
EOF

# Créer un script Python pour l'API de service des modèles
echo -e "${YELLOW}Création du script pour l'API de service des modèles...${NC}"

cat > ml-infrastructure/scripts/model_service.py << EOF
from flask import Flask, request, jsonify
import os
import json
import logging
from datetime import datetime

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/model_service.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

@app.route('/api/models', methods=['GET'])
def list_models():
    """Liste tous les modèles disponibles"""
    try:
        models = []
        for filename in os.listdir('models'):
            if filename.endswith('.json') and not filename.startswith('eval_'):
                with open(os.path.join('models', filename), 'r') as f:
                    model_data = json.load(f)
                    models.append(model_data)
        
        return jsonify({
            'success': True,
            'models': models,
        })
    
    except Exception as e:
        logger.error(f"Erreur lors de la liste des modèles: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
        }), 500

@app.route('/api/models/<model_id>', methods=['GET'])
def get_model(model_id):
    """Récupère les informations d'un modèle spécifique"""
    try:
        model_path = f"models/{model_id}.json"
        
        if not os.path.exists(model_path):
            return jsonify({
                'success': False,
                'error': f"Modèle {model_id} non trouvé",
            }), 404
        
        with open(model_path, 'r') as f:
            model_data = json.load(f)
        
        return jsonify({
            'success': True,
            'model': model_data,
        })
    
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du modèle {model_id}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
        }), 500

@app.route('/api/predict', methods=['POST'])
def predict():
    """Effectue une prédiction avec un modèle spécifique"""
    try:
        data = request.json
        
        if not data or 'model_id' not in data or 'inputs' not in data:
            return jsonify({
                'success': False,
                'error': "Les paramètres 'model_id' et 'inputs' sont requis",
            }), 400
        
        model_id = data['model_id']
        inputs = data['inputs']
        
        model_path = f"models/{model_id}.json"
        
        if not os.path.exists(model_path):
            return jsonify({
                'success': False,
                'error': f"Modèle {model_id} non trouvé",
            }), 404
        
        # Ici, nous simulerons une prédiction
        # Dans une implémentation réelle, nous chargerions le modèle et effectuerions une prédiction
        
        import random
        
        # Simuler une prédiction
        predictions = []
        for _ in range(len(inputs)):
            predictions.append({
                'score': random.uniform(0, 1),
                'confidence': random.uniform(0.5, 0.95),
            })
        
        return jsonify({
            'success': True,
            'predictions': predictions,
            'model_id': model_id,
            'timestamp': datetime.now().isoformat(),
        })
    
    except Exception as e:
        logger.error(f"Erreur lors de la prédiction: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
        }), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001)
EOF

# Créer un script pour démarrer l'infrastructure
echo -e "${YELLOW}Création du script de démarrage...${NC}"

cat > ml-infrastructure/start.sh << EOF
#!/bin/bash

# Démarrer l'infrastructure d'apprentissage automatique
docker-compose up -d

# Afficher les URLs des services
echo "Infrastructure d'apprentissage automatique démarrée !"
echo "Jupyter Lab: http://localhost:8888"
echo "MLflow: http://localhost:5000"
echo "PostgreSQL: localhost:5433"
echo "Redis: localhost:6379"

# Attendre que Jupyter soit prêt
echo "Récupération du token Jupyter..."
sleep 5
TOKEN=\$(docker logs rb-jupyter 2>&1 | grep -o "token=[a-zA-Z0-9]*" | head -n 1)
echo "URL Jupyter avec token: http://localhost:8888/?token=\${TOKEN#*=}"
EOF

chmod +x ml-infrastructure/start.sh

# Créer un script pour arrêter l'infrastructure
echo -e "${YELLOW}Création du script d'arrêt...${NC}"

cat > ml-infrastructure/stop.sh << EOF
#!/bin/bash

# Arrêter l'infrastructure d'apprentissage automatique
docker-compose down
EOF

chmod +x ml-infrastructure/stop.sh

# Créer un README
echo -e "${YELLOW}Création du README...${NC}"

cat > ml-infrastructure/README.md << EOF
# Infrastructure d'apprentissage automatique pour le Sprint 5

Cette infrastructure fournit les outils nécessaires pour le développement, l'entraînement et le déploiement des modèles d'apprentissage automatique pour le système de recommandation.

## Services inclus

- **Jupyter Lab** : Environnement de développement interactif pour l'expérimentation
- **MLflow** : Plateforme pour le suivi des expériences et la gestion des modèles
- **PostgreSQL** : Base de données pour stocker les données d'apprentissage
- **Redis** : Cache et file d'attente pour les tâches asynchrones
- **Celery** : Système de traitement asynchrone pour l'entraînement des modèles

## Démarrage

Pour démarrer l'infrastructure, exécutez :

\`\`\`bash
./start.sh
\`\`\`

## Arrêt

Pour arrêter l'infrastructure, exécutez :

\`\`\`bash
./stop.sh
\`\`\`

## Structure des dossiers

- **data/** : Dossier pour les données d'apprentissage
- **models/** : Dossier pour les modèles entraînés
- **notebooks/** : Dossier pour les notebooks Jupyter
- **scripts/** : Dossier pour les scripts Python
- **logs/** : Dossier pour les logs
- **config/** : Dossier pour les fichiers de configuration

## Utilisation

### Jupyter Lab

Accédez à Jupyter Lab à l'adresse http://localhost:8888 (un token sera affiché lors du démarrage).

### MLflow

Accédez à MLflow à l'adresse http://localhost:5000.

### API de service des modèles

L'API de service des modèles est disponible à l'adresse http://localhost:5001.

Endpoints :
- GET /api/models : Liste tous les modèles disponibles
- GET /api/models/<model_id> : Récupère les informations d'un modèle spécifique
- POST /api/predict : Effectue une prédiction avec un modèle spécifique

### Tâches Celery

Pour soumettre une tâche d'entraînement de modèle :

\`\`\`python
from scripts.tasks import train_model

result = train_model.delay(
    model_type='collaborative_filtering',
    parameters={'n_factors': 100, 'learning_rate': 0.01},
    dataset_path='/app/data/training_data.csv'
)

# Récupérer le résultat
result.get()
\`\`\`

Pour soumettre une tâche d'évaluation de modèle :

\`\`\`python
from scripts.tasks import evaluate_model

result = evaluate_model.delay(
    model_id='collaborative_filtering_20230101_120000',
    test_dataset_path='/app/data/test_data.csv'
)

# Récupérer le résultat
result.get()
\`\`\`
EOF

echo -e "${GREEN}=== Infrastructure d'apprentissage automatique préparée avec succès ===${NC}"
echo -e "${YELLOW}Pour démarrer l'infrastructure, exécutez :${NC}"
echo -e "${YELLOW}cd ml-infrastructure && ./start.sh${NC}"
exit 0
