import { createConnection, getRepository } from 'typeorm';
import { SensitiveEntity } from '../../src/entities/SensitiveEntity';
import { SensitiveDataEncryptionService } from '../../src/modules/security/services/sensitive-data-encryption.service';
import * as fs from 'fs';

async function backupData(entities: SensitiveEntity[], backupPath: string) {
  fs.writeFileSync(backupPath, JSON.stringify(entities, null, 2));
}

async function runMigration() {
  const connection = await createConnection();
  const repo = getRepository(SensitiveEntity);
  // Vous devez fournir une instance de ConfigService ici
// const configService = ... (récupérer depuis votre contexte d'application ou mocker pour les scripts)
// const encryptionService = new SensitiveDataEncryptionService(configService);

  // Backup data before migration
  const allData = await repo.find();
  await backupData(allData, './backup/sensitive-entities-backup.json');

  for (const entity of allData) {
    if (entity.encryptedValue) {
      // entity.encryptedValue = await encryptionService.encrypt(entity.encryptedValue);
      // Utilisez la méthode encrypt, et assurez-vous que l'instance encryptionService est bien initialisée
      // await repo.save(entity);
    }
  }

  await connection.close();
  console.log('Migration complete.');
}

runMigration().catch(e => {
  console.error('Migration failed:', e);
  process.exit(1);
});
