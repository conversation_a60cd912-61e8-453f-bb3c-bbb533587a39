import { createConnection, getRepository } from 'typeorm';
import { SensitiveEntity } from '../../src/entities/SensitiveEntity';
import * as crypto from 'crypto';

async function checkIntegrity() {
  const connection = await createConnection();
  const repo = getRepository(SensitiveEntity);
  const allData = await repo.find();
  for (const entity of allData) {
    const checksum = crypto.createHash('sha256').update(entity.encryptedValue).digest('hex');
    console.log(`Entity ${entity.id}: checksum = ${checksum}`);
  }
  await connection.close();
}

checkIntegrity().catch(e => {
  console.error('Integrity check failed:', e);
  process.exit(1);
});
