# Batch Data Re-Encryption Migration Script

This script migrates existing sensitive data to the new encryption scheme.

## Usage

```bash
npm run migration:re-encrypt-batch
```

## Steps
1. **Backup**: Backs up all sensitive entities to `./backup/sensitive-entities-backup.json` before migration.
2. **Re-encryption**: Iterates through all entities and re-encrypts sensitive fields using the latest encryption service.
3. **Rollback**: To rollback, restore the backed up file using a custom restore script.

## CI/CD Integration
Add the following job to your GitHub Actions workflow:

```yaml
- name: Run Data Migration
  run: npm run migration:re-encrypt-batch
```

## Rollback Plan
1. Stop all writes to the database.
2. Use the backup file to restore sensitive data.
3. Validate data integrity with checksums.

## Requirements
- Node.js LTS
- TypeORM configured
- SensitiveDataEncryptionService available

---
**Warning:** Always test on a staging environment before running in production.
