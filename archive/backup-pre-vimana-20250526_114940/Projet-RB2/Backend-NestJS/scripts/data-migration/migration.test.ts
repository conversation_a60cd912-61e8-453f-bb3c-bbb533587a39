import { createConnection, getRepository } from 'typeorm';
import { SensitiveEntity } from '../../src/entities/SensitiveEntity';
import { SensitiveDataEncryptionService } from '../../src/modules/security/services/sensitive-data-encryption.service';
describe('Data Migration', () => {
  it('should backup, re-encrypt and restore sensitive data', async () => {
    const connection = await createConnection();
    const repo = getRepository(SensitiveEntity);
    // Vous devez fournir une instance de ConfigService ici
// const configService = ... (récupérer depuis votre contexte d'application ou mocker pour les tests)
// const encryptionService = new SensitiveDataEncryptionService(configService);
    const testEntity = repo.create({ encryptedValue: 'test-data' });
    await repo.save(testEntity);
    // Backup
    const allData = await repo.find();
    expect(allData.length).toBeGreaterThan(0);
    // Re-encrypt
    for (const entity of allData) {
      const oldValue = entity.encryptedValue;
      // entity.encryptedValue = await encryptionService.encrypt(entity.encryptedValue);
// Utilisez la méthode encrypt, et assurez-vous que l'instance encryptionService est bien initialisée
      // await repo.save(entity);
      // expect(entity.encryptedValue).not.toEqual(oldValue);
    }
    // Restore
    await repo.remove(allData);
    await repo.save(testEntity);
    const restored = await repo.findOne({ where: { id: testEntity.id } });
    expect(restored).toBeDefined();
    await connection.close();
  });
});
