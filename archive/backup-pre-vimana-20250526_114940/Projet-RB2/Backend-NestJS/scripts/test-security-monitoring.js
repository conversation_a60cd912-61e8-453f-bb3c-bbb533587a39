#!/usr/bin/env node

/**
 * Script pour tester les fonctionnalités de surveillance et de réponse aux incidents
 * 
 * Usage:
 *   node test-security-monitoring.js [--mode <mode>]
 * 
 * Options:
 *   --mode   Mode de test (events, threats, incidents, dashboard, all) (défaut: all)
 */

const axios = require('axios');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  mode: 'all',
};

for (let i = 0; i < args.length; i++) {
  if (args[i] === '--mode' && i + 1 < args.length) {
    options.mode = args[i + 1];
    i++;
  }
}

// API base URL
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';

// Token d'authentification (simulé pour les tests)
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkFkbWluIFVzZXIiLCJyb2xlIjoiYWRtaW4iLCJpYXQiOjE1MTYyMzkwMjJ9.KjCZVrGRZ-SFKuAkro-X5mEQ0XBHcXyNh72K1JaK-Qw';

// Configuration Axios
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Authorization': `Bearer ${AUTH_TOKEN}`,
    'Content-Type': 'application/json',
  },
});

async function runTest() {
  console.log('Starting security monitoring and incident response test...');
  
  try {
    if (options.mode === 'all' || options.mode === 'events') {
      await testSecurityEvents();
    }
    
    if (options.mode === 'all' || options.mode === 'threats') {
      await testActiveThreats();
    }
    
    if (options.mode === 'all' || options.mode === 'incidents') {
      await testIncidentManagement();
    }
    
    if (options.mode === 'all' || options.mode === 'dashboard') {
      await testSecurityDashboard();
    }
    
    console.log('Security monitoring and incident response test completed successfully');
  } catch (error) {
    console.error('Error running security monitoring test:', error.message);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Data: ${JSON.stringify(error.response.data)}`);
    }
  } finally {
    await prisma.$disconnect();
  }
}

async function testSecurityEvents() {
  console.log('\n=== Testing Security Events ===');
  
  try {
    // Créer un événement de sécurité
    console.log('Creating security events...');
    
    // Événement de connexion échouée
    await api.post('/api/security/events', {
      type: 'FAILED_LOGIN',
      source: 'AUTH_SERVICE',
      details: {
        userId: '123456',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        reason: 'INVALID_PASSWORD',
      },
      severity: 'WARNING',
    });
    
    // Événement d'accès non autorisé
    await api.post('/api/security/events', {
      type: 'UNAUTHORIZED_ACCESS',
      source: 'API_GATEWAY',
      details: {
        endpoint: '/api/admin/users',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        userId: '789012',
      },
      severity: 'ERROR',
    });
    
    // Événement de détection de malware
    await api.post('/api/security/events', {
      type: 'MALWARE_DETECTED',
      source: 'FILE_UPLOAD',
      details: {
        fileName: 'document.pdf',
        fileHash: 'a1b2c3d4e5f6g7h8i9j0',
        malwareType: 'TROJAN',
        userId: '345678',
      },
      severity: 'CRITICAL',
    });
    
    console.log('Security events created successfully');
    
    // Récupérer les événements de sécurité
    console.log('Retrieving security events...');
    const eventsResponse = await api.get('/api/security/events');
    console.log(`Retrieved ${eventsResponse.data.data.length} security events`);
    
    console.log('Security Events test completed successfully');
  } catch (error) {
    console.error('Error testing Security Events:', error.message);
    throw error;
  }
}

async function testActiveThreats() {
  console.log('\n=== Testing Active Threats ===');
  
  try {
    // Récupérer les menaces actives
    console.log('Retrieving active threats...');
    const threatsResponse = await api.get('/api/security/dashboard/threats');
    console.log(`Retrieved ${threatsResponse.data.data.length} active threats`);
    
    if (threatsResponse.data.data.length > 0) {
      const threatId = threatsResponse.data.data[0].id;
      
      // Récupérer une menace active par son ID
      console.log(`Retrieving active threat with ID: ${threatId}`);
      const threatResponse = await api.get(`/api/security/dashboard/threats/${threatId}`);
      console.log(`Retrieved threat: ${threatResponse.data.data.type}`);
      
      // Mettre à jour le statut d'une menace active
      console.log('Updating active threat status...');
      const updateResponse = await api.put(`/api/security/dashboard/threats/${threatId}`, {
        status: 'INVESTIGATING',
        mitigationSteps: [
          'Isolate affected systems',
          'Analyze threat indicators',
          'Block suspicious IP addresses',
        ],
      });
      console.log(`Threat status updated to: ${updateResponse.data.data.status}`);
      
      // Créer un incident à partir d'une menace active
      console.log('Creating incident from active threat...');
      const incidentResponse = await api.post(`/api/security/dashboard/threats/${threatId}/create-incident`, {
        assignedToId: '123456',
      });
      console.log(`Incident created with ID: ${incidentResponse.data.data.incidentId}`);
    } else {
      console.log('No active threats found, skipping threat-specific tests');
    }
    
    console.log('Active Threats test completed successfully');
  } catch (error) {
    console.error('Error testing Active Threats:', error.message);
    throw error;
  }
}

async function testIncidentManagement() {
  console.log('\n=== Testing Incident Management ===');
  
  try {
    // Créer un incident de sécurité
    console.log('Creating a security incident...');
    const createResponse = await api.post('/api/security/incidents', {
      title: 'Suspicious Login Activity',
      description: 'Multiple failed login attempts detected from unusual location',
      severity: 'HIGH',
      type: 'UNAUTHORIZED_ACCESS',
      source: 'AUTH_SERVICE',
      affectedAssets: ['user-database', 'auth-service'],
      tags: ['login', 'brute-force', 'authentication'],
    });
    
    console.log(`Incident created with ID: ${createResponse.data.data.id}`);
    const incidentId = createResponse.data.data.id;
    
    // Récupérer l'incident
    console.log('Retrieving the incident...');
    const getResponse = await api.get(`/api/security/incidents/${incidentId}`);
    console.log(`Retrieved incident: ${getResponse.data.data.title}`);
    
    // Mettre à jour l'incident
    console.log('Updating the incident...');
    const updateResponse = await api.put(`/api/security/incidents/${incidentId}`, {
      status: 'INVESTIGATING',
      severity: 'CRITICAL',
      description: 'Multiple failed login attempts detected from unusual location. Investigation in progress.',
    });
    console.log(`Incident updated: ${updateResponse.data.data.status}`);
    
    // Ajouter une activité à l'incident
    console.log('Adding activity to the incident...');
    const activityResponse = await api.post(`/api/security/incidents/${incidentId}/activities`, {
      action: 'INVESTIGATION',
      details: 'Analyzing login logs and IP addresses',
    });
    console.log('Activity added to incident');
    
    // Récupérer tous les incidents
    console.log('Listing all incidents...');
    const listResponse = await api.get('/api/security/incidents');
    console.log(`Found ${listResponse.data.data.incidents.length} incidents`);
    
    // Résoudre l'incident
    console.log('Resolving the incident...');
    const resolveResponse = await api.put(`/api/security/incidents/${incidentId}`, {
      status: 'RESOLVED',
      description: 'Investigation complete. False positive confirmed. No actual breach occurred.',
    });
    console.log(`Incident resolved: ${resolveResponse.data.data.status}`);
    
    console.log('Incident Management test completed successfully');
  } catch (error) {
    console.error('Error testing Incident Management:', error.message);
    throw error;
  }
}

async function testSecurityDashboard() {
  console.log('\n=== Testing Security Dashboard ===');
  
  try {
    // Récupérer les métriques de sécurité
    console.log('Retrieving security metrics...');
    const metricsResponse = await api.get('/api/security/dashboard/metrics');
    console.log('Security metrics retrieved successfully');
    console.log(`Total events: ${metricsResponse.data.data.totalEvents}`);
    console.log(`Total alerts: ${metricsResponse.data.data.totalAlerts}`);
    console.log(`Events by severity: ${JSON.stringify(metricsResponse.data.data.eventsBySeverity)}`);
    
    // Récupérer les métriques pour différentes périodes
    console.log('Retrieving security metrics for different timeframes...');
    const weekMetricsResponse = await api.get('/api/security/dashboard/metrics?timeframe=week');
    console.log(`Weekly events: ${weekMetricsResponse.data.data.totalEvents}`);
    
    const monthMetricsResponse = await api.get('/api/security/dashboard/metrics?timeframe=month');
    console.log(`Monthly events: ${monthMetricsResponse.data.data.totalEvents}`);
    
    console.log('Security Dashboard test completed successfully');
  } catch (error) {
    console.error('Error testing Security Dashboard:', error.message);
    throw error;
  }
}

// Exécuter le test
runTest();
