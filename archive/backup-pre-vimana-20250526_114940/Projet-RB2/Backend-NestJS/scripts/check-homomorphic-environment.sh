#!/usr/bin/env bash
# Vérifie la présence de SEAL et des outils requis pour le chiffrement homomorphique
set -e

echo "=== Vérification de l'environnement pour le chiffrement homomorphique ==="

# Vérification de Node.js
if ! command -v node > /dev/null; then
  echo "[ERREUR] Node.js n'est pas installé." >&2
  exit 1
fi

# Vérification de la version Node.js (LTS recommandé)
NODE_VERSION=$(node -v)
echo "Node.js version: $NODE_VERSION"

# Vérification de CMake
if ! command -v cmake > /dev/null; then
  echo "[AVERTISSEMENT] CMake n'est pas installé. La compilation native de SEAL pourrait ne pas être possible." >&2
  HAS_CMAKE=0
else
  CMAKE_VERSION=$(cmake --version | head -n 1)
  echo "CMake version: $CMAKE_VERSION"
  HAS_CMAKE=1
fi

# Vérification du compilateur C/C++
if ! command -v g++ > /dev/null && ! command -v clang++ > /dev/null; then
  echo "[AVERTISSEMENT] Aucun compilateur C++ (g++/clang++) n'est installé. La compilation native de SEAL pourrait ne pas être possible." >&2
  HAS_COMPILER=0
else
  if command -v g++ > /dev/null; then
    GCC_VERSION=$(g++ --version | head -n 1)
    echo "G++ version: $GCC_VERSION"
    HAS_COMPILER=1
  elif command -v clang++ > /dev/null; then
    CLANG_VERSION=$(clang++ --version | head -n 1)
    echo "Clang++ version: $CLANG_VERSION"
    HAS_COMPILER=1
  fi
fi

# Vérification de la présence de node-seal dans node_modules
if [ ! -d "node_modules/node-seal" ]; then
  echo "[AVERTISSEMENT] node-seal n'est pas installé dans node_modules." >&2
  echo "Pour l'installer, exécutez: npm install node-seal" >&2
  HAS_SEAL=0
else
  echo "node-seal est installé dans node_modules."
  HAS_SEAL=1
  
  # Test rapide d'import JS
  if node -e "try { require('node-seal'); console.log('Import réussi!'); } catch(e) { console.error('Erreur:', e.message); process.exit(1); }"; then
    echo "Import de node-seal réussi."
    IMPORT_OK=1
  else
    echo "[ERREUR] Impossible de charger node-seal." >&2
    IMPORT_OK=0
  fi
fi

# Vérification de la mémoire disponible
MEM_TOTAL=$(free -m | awk '/^Mem:/{print $2}')
if [ "$MEM_TOTAL" -lt 4096 ]; then
  echo "[AVERTISSEMENT] Moins de 4 Go de RAM disponible ($MEM_TOTAL Mo). Le chiffrement homomorphique peut nécessiter beaucoup de mémoire." >&2
  HAS_MEMORY=0
else
  echo "Mémoire disponible: $MEM_TOTAL Mo"
  HAS_MEMORY=1
fi

# Vérification de l'environnement complet
if [ "$HAS_CMAKE" -eq 1 ] && [ "$HAS_COMPILER" -eq 1 ] && [ "$HAS_SEAL" -eq 1 ] && [ "$IMPORT_OK" -eq 1 ] && [ "$HAS_MEMORY" -eq 1 ]; then
  echo "[OK] Environnement prêt pour le chiffrement homomorphique."
  echo "Mode disponible: COMPLET (toutes les fonctionnalités sont disponibles)"
  exit 0
elif [ "$HAS_SEAL" -eq 1 ] && [ "$IMPORT_OK" -eq 1 ]; then
  echo "[OK] node-seal est installé mais l'environnement n'est pas optimal."
  if [ "$HAS_MEMORY" -eq 0 ]; then
    echo "[AVERTISSEMENT] Mémoire limitée, les performances peuvent être réduites."
  fi
  echo "Mode disponible: BASIQUE (fonctionnalités limitées ou performances réduites)"
  exit 0
else
  echo "[ERREUR] L'environnement n'est pas prêt pour le chiffrement homomorphique."
  echo "Mode disponible: AUCUN"
  exit 1
fi
