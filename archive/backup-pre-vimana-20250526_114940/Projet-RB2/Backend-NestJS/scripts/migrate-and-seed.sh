#!/bin/bash

# This script runs database migrations and seeds the database with initial data

# Set environment variables
export NODE_ENV=${NODE_ENV:-development}

echo "Running database migrations in $NODE_ENV environment..."

# Run Prisma migrations
npx prisma migrate deploy

# Generate Prisma client
npx prisma generate

# Check if seed flag is provided
if [ "$1" = "--seed" ]; then
  echo "Seeding the database..."
  npx prisma db seed
fi

echo "Database setup completed successfully!"
