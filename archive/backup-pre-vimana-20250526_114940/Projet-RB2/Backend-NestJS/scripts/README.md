# Scripts de conformité et d'automatisation

## 1. compliance-check.sh
Vérifie la conformité cryptographique via l'API REST et génère un rapport Markdown. À intégrer dans le pipeline CI/CD.

- **Succès** : continue le pipeline, exporte `compliance-report.md`
- **Échec** : arrête le pipeline, exporte `compliance-report.md` pour audit

```bash
bash scripts/compliance-check.sh
```

## 2. compliance-report-to-pdf.js
Convertit le rapport Markdown en PDF pour archivage ou audit externe.

- Nécessite Node.js et le package `markdown-pdf` (`npm install -g markdown-pdf`)
- Utilisation :

```bash
node scripts/compliance-report-to-pdf.js
```

- <PERSON><PERSON><PERSON> `compliance-report.pdf` à partir de `compliance-report.md`

---

**Astuce CI/CD** :
Ajoutez ces étapes à la fin de votre pipeline pour garantir la conformité, générer les rapports, et archiver automatiquement les preuves d’audit.
