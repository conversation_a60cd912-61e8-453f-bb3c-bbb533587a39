#!/bin/bash

# Script pour exécuter les tests d'intégration du système de recommandation

# Définir les couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Exécution des tests d'intégration du système de recommandation ===${NC}"

# Vérifier si le fichier .env.test existe
if [ ! -f ".env.test" ]; then
  echo -e "${YELLOW}Création du fichier .env.test...${NC}"
  cp .env.example .env.test
  echo "JWT_SECRET=test-secret" >> .env.test
  echo "AGENT_RB_SERVICE_URL=http://localhost:5000" >> .env.test
  echo "AGENT_RB_TIMEOUT_MS=5000" >> .env.test
  echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/retreatandbe_test" >> .env.test
  echo -e "${GREEN}Fichier .env.test créé avec succès.${NC}"
fi

# Vérifier si la base de données de test existe
echo -e "${YELLOW}Vérification de la base de données de test...${NC}"
if ! npx prisma db push --schema=./prisma/schema.prisma --preview-feature; then
  echo -e "${RED}Erreur lors de la vérification de la base de données de test.${NC}"
  echo -e "${YELLOW}Assurez-vous que la base de données est accessible et que les informations de connexion sont correctes dans .env.test.${NC}"
  exit 1
fi

# Créer le répertoire pour les résultats s'il n'existe pas
mkdir -p coverage/integration

# Exécuter les tests d'intégration
echo -e "\n${YELLOW}Exécution des tests d'intégration du contrôleur de recommandation...${NC}"
npx jest src/modules/recommendation/tests/integration/recommendation.controller.integration.spec.ts --config=src/modules/recommendation/tests/integration/jest.config.js --json --outputFile=coverage/integration/recommendation-controller.json || true

echo -e "\n${YELLOW}Exécution des tests d'intégration du service d'intégration Agent-RB...${NC}"
npx jest src/modules/recommendation/tests/integration/agent-rb-integration.service.integration.spec.ts --config=src/modules/recommendation/tests/integration/jest.config.js --json --outputFile=coverage/integration/agent-rb-integration.json || true

# Générer un rapport de couverture
echo -e "\n${YELLOW}Génération du rapport de couverture...${NC}"
npx jest src/modules/recommendation/tests/integration --config=src/modules/recommendation/tests/integration/jest.config.js --coverage || true

# Afficher un résumé
echo -e "\n${GREEN}=== Résumé des tests d'intégration ===${NC}"
echo -e "${YELLOW}Les résultats détaillés sont disponibles dans le dossier coverage/integration${NC}"
echo -e "${YELLOW}Le rapport de couverture est disponible dans le dossier coverage/lcov-report${NC}"

# Ouvrir le rapport de couverture dans le navigateur
if [ -f "coverage/lcov-report/index.html" ]; then
  echo -e "\n${YELLOW}Ouverture du rapport de couverture dans le navigateur...${NC}"
  if [[ "$OSTYPE" == "darwin"* ]]; then
    open coverage/lcov-report/index.html
  elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    xdg-open coverage/lcov-report/index.html
  elif [[ "$OSTYPE" == "cygwin" || "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    start coverage/lcov-report/index.html
  fi
fi
