#!/bin/bash

# Script pour appliquer les mises à jour du schéma Prisma

# Couleurs pour les messages
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Début de la mise à jour du schéma Prisma...${NC}"

# Vérifier si le fichier schema-update.prisma existe
if [ ! -f "./prisma/schema-update.prisma" ]; then
    echo -e "${RED}Erreur: Le fichier schema-update.prisma n'existe pas.${NC}"
    exit 1
fi

# Sauvegarder le schéma actuel
echo -e "${YELLOW}Sauvegarde du schéma actuel...${NC}"
cp ./prisma/schema.prisma ./prisma/schema.prisma.backup
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de la sauvegarde du schéma actuel.${NC}"
    exit 1
fi
echo -e "${GREEN}Sauvegarde effectuée avec succès.${NC}"

# Fusionner les schémas
echo -e "${YELLOW}Fusion des schémas...${NC}"
cat ./prisma/schema-update.prisma >> ./prisma/schema.prisma
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de la fusion des schémas.${NC}"
    # Restaurer le schéma original
    mv ./prisma/schema.prisma.backup ./prisma/schema.prisma
    exit 1
fi
echo -e "${GREEN}Fusion effectuée avec succès.${NC}"

# Générer la migration Prisma
echo -e "${YELLOW}Génération de la migration Prisma...${NC}"
npx prisma migrate dev --name add_moderation_and_analytics
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de la génération de la migration Prisma.${NC}"
    # Restaurer le schéma original
    mv ./prisma/schema.prisma.backup ./prisma/schema.prisma
    exit 1
fi
echo -e "${GREEN}Migration générée avec succès.${NC}"

# Générer le client Prisma
echo -e "${YELLOW}Génération du client Prisma...${NC}"
npx prisma generate
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de la génération du client Prisma.${NC}"
    exit 1
fi
echo -e "${GREEN}Client généré avec succès.${NC}"

# Supprimer la sauvegarde
echo -e "${YELLOW}Suppression de la sauvegarde...${NC}"
rm ./prisma/schema.prisma.backup
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de la suppression de la sauvegarde.${NC}"
    exit 1
fi
echo -e "${GREEN}Sauvegarde supprimée avec succès.${NC}"

echo -e "${GREEN}Mise à jour du schéma Prisma terminée avec succès.${NC}"
