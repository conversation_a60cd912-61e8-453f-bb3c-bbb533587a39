#!/usr/bin/env ts-node

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/prisma/prisma.service';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

interface FinalizationReport {
  timestamp: string;
  overall: {
    status: 'READY' | 'NEEDS_ATTENTION' | 'CRITICAL';
    completionPercentage: number;
    readyForProduction: boolean;
  };
  modules: {
    [key: string]: {
      status: 'COMPLETE' | 'PARTIAL' | 'MISSING';
      tests: boolean;
      documentation: boolean;
      functionality: boolean;
    };
  };
  infrastructure: {
    database: boolean;
    redis: boolean;
    docker: boolean;
    kubernetes: boolean;
  };
  security: {
    authentication: boolean;
    authorization: boolean;
    encryption: boolean;
    rateLimit: boolean;
  };
  performance: {
    tests: boolean;
    optimization: boolean;
    monitoring: boolean;
  };
  recommendations: string[];
}

class FinalizationChecker {
  private app: any;
  private prismaService: PrismaService;

  async initialize() {
    console.log('🔍 Initialisation de la vérification de finalisation...');
    
    try {
      this.app = await NestFactory.createApplicationContext(AppModule);
      this.prismaService = this.app.get(PrismaService);
    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation:', error.message);
      throw error;
    }
  }

  async checkFinalization(): Promise<FinalizationReport> {
    console.log('📋 Vérification de l\'état de finalisation...');

    const report: FinalizationReport = {
      timestamp: new Date().toISOString(),
      overall: {
        status: 'NEEDS_ATTENTION',
        completionPercentage: 0,
        readyForProduction: false,
      },
      modules: await this.checkModules(),
      infrastructure: await this.checkInfrastructure(),
      security: await this.checkSecurity(),
      performance: await this.checkPerformance(),
      recommendations: [],
    };

    // Calculer le pourcentage de completion
    report.overall.completionPercentage = this.calculateCompletionPercentage(report);
    
    // Déterminer le statut global
    if (report.overall.completionPercentage >= 95) {
      report.overall.status = 'READY';
      report.overall.readyForProduction = true;
    } else if (report.overall.completionPercentage >= 80) {
      report.overall.status = 'NEEDS_ATTENTION';
    } else {
      report.overall.status = 'CRITICAL';
    }

    // Générer les recommandations
    report.recommendations = this.generateRecommendations(report);

    return report;
  }

  private async checkModules() {
    console.log('🧩 Vérification des modules...');
    
    const modules = [
      'auth', 'users', 'analytics', 'moderation', 'recommendation',
      'learning', 'matching', 'security', 'notifications'
    ];

    const moduleStatus: any = {};

    for (const module of modules) {
      const modulePath = path.join(__dirname, `../src/modules/${module}`);
      const testsExist = fs.existsSync(path.join(modulePath, 'tests')) || 
                       fs.existsSync(path.join(modulePath, `${module}.service.spec.ts`));
      const docExists = fs.existsSync(path.join(modulePath, 'README.md'));
      const serviceExists = fs.existsSync(path.join(modulePath, `${module}.service.ts`));
      const controllerExists = fs.existsSync(path.join(modulePath, `${module}.controller.ts`));

      moduleStatus[module] = {
        status: (testsExist && serviceExists && controllerExists) ? 'COMPLETE' : 
                (serviceExists || controllerExists) ? 'PARTIAL' : 'MISSING',
        tests: testsExist,
        documentation: docExists,
        functionality: serviceExists && controllerExists,
      };
    }

    return moduleStatus;
  }

  private async checkInfrastructure() {
    console.log('🏗️  Vérification de l\'infrastructure...');
    
    // Vérifier la base de données
    let databaseOk = false;
    try {
      await this.prismaService.$connect();
      await this.prismaService.$queryRaw`SELECT 1`;
      databaseOk = true;
    } catch (error) {
      console.warn('⚠️  Base de données non accessible');
    }

    // Vérifier Redis (optionnel)
    const redisOk = process.env.REDIS_URL ? true : false;

    // Vérifier Docker
    const dockerOk = fs.existsSync(path.join(__dirname, '../Dockerfile'));

    // Vérifier Kubernetes
    const k8sOk = fs.existsSync(path.join(__dirname, '../k8s')) || 
                  fs.existsSync(path.join(__dirname, '../helm'));

    return {
      database: databaseOk,
      redis: redisOk,
      docker: dockerOk,
      kubernetes: k8sOk,
    };
  }

  private async checkSecurity() {
    console.log('🔒 Vérification de la sécurité...');
    
    // Vérifier l'authentification
    const authExists = fs.existsSync(path.join(__dirname, '../src/modules/auth'));
    
    // Vérifier l'autorisation (guards, roles)
    const guardsExist = fs.existsSync(path.join(__dirname, '../src/modules/auth/guards'));
    
    // Vérifier le chiffrement
    const encryptionExists = fs.existsSync(path.join(__dirname, '../src/modules/security'));
    
    // Vérifier le rate limiting
    const rateLimitExists = fs.existsSync(path.join(__dirname, '../src/common/guards')) ||
                           fs.readFileSync(path.join(__dirname, '../src/main.ts'), 'utf8')
                             .includes('rateLimit');

    return {
      authentication: authExists,
      authorization: guardsExist,
      encryption: encryptionExists,
      rateLimit: rateLimitExists,
    };
  }

  private async checkPerformance() {
    console.log('⚡ Vérification des performances...');
    
    // Vérifier les tests de performance
    const perfTestsExist = fs.existsSync(path.join(__dirname, '../test/performance')) ||
                          fs.existsSync(path.join(__dirname, './performance-report.ts'));
    
    // Vérifier l'optimisation (cache, indexation)
    const cacheExists = fs.readFileSync(path.join(__dirname, '../src/app.module.ts'), 'utf8')
                         .includes('CacheModule');
    
    // Vérifier le monitoring
    const monitoringExists = fs.existsSync(path.join(__dirname, '../src/modules/health')) ||
                            fs.readFileSync(path.join(__dirname, '../src/app.module.ts'), 'utf8')
                              .includes('TerminusModule');

    return {
      tests: perfTestsExist,
      optimization: cacheExists,
      monitoring: monitoringExists,
    };
  }

  private calculateCompletionPercentage(report: FinalizationReport): number {
    let totalPoints = 0;
    let earnedPoints = 0;

    // Modules (40% du score)
    const moduleCount = Object.keys(report.modules).length;
    totalPoints += moduleCount * 3; // 3 points par module (tests, doc, functionality)
    
    Object.values(report.modules).forEach(module => {
      if (module.tests) earnedPoints += 1;
      if (module.documentation) earnedPoints += 1;
      if (module.functionality) earnedPoints += 1;
    });

    // Infrastructure (25% du score)
    totalPoints += 4;
    if (report.infrastructure.database) earnedPoints += 1;
    if (report.infrastructure.redis) earnedPoints += 1;
    if (report.infrastructure.docker) earnedPoints += 1;
    if (report.infrastructure.kubernetes) earnedPoints += 1;

    // Sécurité (25% du score)
    totalPoints += 4;
    if (report.security.authentication) earnedPoints += 1;
    if (report.security.authorization) earnedPoints += 1;
    if (report.security.encryption) earnedPoints += 1;
    if (report.security.rateLimit) earnedPoints += 1;

    // Performance (10% du score)
    totalPoints += 3;
    if (report.performance.tests) earnedPoints += 1;
    if (report.performance.optimization) earnedPoints += 1;
    if (report.performance.monitoring) earnedPoints += 1;

    return Math.round((earnedPoints / totalPoints) * 100);
  }

  private generateRecommendations(report: FinalizationReport): string[] {
    const recommendations: string[] = [];

    // Recommandations pour les modules
    Object.entries(report.modules).forEach(([module, status]) => {
      if (!status.tests) {
        recommendations.push(`Ajouter des tests pour le module ${module}`);
      }
      if (!status.documentation) {
        recommendations.push(`Ajouter la documentation pour le module ${module}`);
      }
      if (!status.functionality) {
        recommendations.push(`Compléter les fonctionnalités du module ${module}`);
      }
    });

    // Recommandations pour l'infrastructure
    if (!report.infrastructure.database) {
      recommendations.push('Configurer et tester la connexion à la base de données');
    }
    if (!report.infrastructure.redis) {
      recommendations.push('Configurer Redis pour le cache et les sessions');
    }
    if (!report.infrastructure.docker) {
      recommendations.push('Créer un Dockerfile pour la containerisation');
    }
    if (!report.infrastructure.kubernetes) {
      recommendations.push('Préparer les manifests Kubernetes pour le déploiement');
    }

    // Recommandations pour la sécurité
    if (!report.security.authentication) {
      recommendations.push('Implémenter un système d\'authentification robuste');
    }
    if (!report.security.authorization) {
      recommendations.push('Ajouter des guards et un système de rôles');
    }
    if (!report.security.encryption) {
      recommendations.push('Implémenter le chiffrement des données sensibles');
    }
    if (!report.security.rateLimit) {
      recommendations.push('Configurer le rate limiting pour prévenir les abus');
    }

    // Recommandations pour les performances
    if (!report.performance.tests) {
      recommendations.push('Créer des tests de performance et de charge');
    }
    if (!report.performance.optimization) {
      recommendations.push('Optimiser les performances avec du cache et des index');
    }
    if (!report.performance.monitoring) {
      recommendations.push('Implémenter le monitoring et les health checks');
    }

    return recommendations;
  }

  async saveReport(report: FinalizationReport) {
    const reportDir = path.join(__dirname, '../reports');
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const filename = `finalization-report-${new Date().toISOString().split('T')[0]}.json`;
    const filepath = path.join(reportDir, filename);

    fs.writeFileSync(filepath, JSON.stringify(report, null, 2));
    console.log(`💾 Rapport de finalisation sauvegardé: ${filepath}`);

    // Générer un rapport HTML
    await this.generateHtmlReport(report, reportDir);
  }

  private async generateHtmlReport(report: FinalizationReport, reportDir: string) {
    const statusColor = {
      'READY': '#27ae60',
      'NEEDS_ATTENTION': '#f39c12',
      'CRITICAL': '#e74c3c'
    };

    const htmlContent = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport de Finalisation - Retreat And Be</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #2c3e50; margin-bottom: 30px; }
        .status-badge { display: inline-block; padding: 5px 15px; border-radius: 20px; color: white; font-weight: bold; }
        .section { margin: 20px 0; padding: 15px; background: #ecf0f1; border-radius: 5px; }
        .section-title { font-weight: bold; color: #2c3e50; margin-bottom: 15px; font-size: 1.2em; }
        .item { margin: 5px 0; padding: 8px; background: white; border-radius: 3px; }
        .complete { border-left: 4px solid #27ae60; }
        .partial { border-left: 4px solid #f39c12; }
        .missing { border-left: 4px solid #e74c3c; }
        .recommendations { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; }
        .progress-bar { width: 100%; height: 20px; background: #ecf0f1; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #27ae60, #2ecc71); transition: width 0.3s ease; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Rapport de Finalisation</h1>
            <h2>Retreat And Be - Backend NestJS</h2>
            <div class="status-badge" style="background-color: ${statusColor[report.overall.status]}">
                ${report.overall.status}
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: ${report.overall.completionPercentage}%"></div>
            </div>
            <p><strong>Completion: ${report.overall.completionPercentage}%</strong></p>
            <p><strong>Prêt pour la production: ${report.overall.readyForProduction ? '✅ OUI' : '❌ NON'}</strong></p>
        </div>

        <div class="section">
            <div class="section-title">🧩 Modules</div>
            ${Object.entries(report.modules).map(([name, status]) => `
                <div class="item ${status.status.toLowerCase()}">
                    <strong>${name.toUpperCase()}</strong> - ${status.status}
                    <br>Tests: ${status.tests ? '✅' : '❌'} | 
                    Documentation: ${status.documentation ? '✅' : '❌'} | 
                    Fonctionnalité: ${status.functionality ? '✅' : '❌'}
                </div>
            `).join('')}
        </div>

        <div class="section">
            <div class="section-title">🏗️ Infrastructure</div>
            <div class="item">Base de données: ${report.infrastructure.database ? '✅' : '❌'}</div>
            <div class="item">Redis: ${report.infrastructure.redis ? '✅' : '❌'}</div>
            <div class="item">Docker: ${report.infrastructure.docker ? '✅' : '❌'}</div>
            <div class="item">Kubernetes: ${report.infrastructure.kubernetes ? '✅' : '❌'}</div>
        </div>

        <div class="section">
            <div class="section-title">🔒 Sécurité</div>
            <div class="item">Authentification: ${report.security.authentication ? '✅' : '❌'}</div>
            <div class="item">Autorisation: ${report.security.authorization ? '✅' : '❌'}</div>
            <div class="item">Chiffrement: ${report.security.encryption ? '✅' : '❌'}</div>
            <div class="item">Rate Limiting: ${report.security.rateLimit ? '✅' : '❌'}</div>
        </div>

        <div class="section">
            <div class="section-title">⚡ Performance</div>
            <div class="item">Tests: ${report.performance.tests ? '✅' : '❌'}</div>
            <div class="item">Optimisation: ${report.performance.optimization ? '✅' : '❌'}</div>
            <div class="item">Monitoring: ${report.performance.monitoring ? '✅' : '❌'}</div>
        </div>

        ${report.recommendations.length > 0 ? `
        <div class="section recommendations">
            <div class="section-title">💡 Recommandations</div>
            <ul>
                ${report.recommendations.map(rec => `<li>${rec}</li>`).join('')}
            </ul>
        </div>
        ` : ''}

        <div style="text-align: center; color: #7f8c8d; margin-top: 20px;">
            Généré le: ${new Date(report.timestamp).toLocaleString('fr-FR')}
        </div>
    </div>
</body>
</html>`;

    const htmlFilename = `finalization-report-${new Date().toISOString().split('T')[0]}.html`;
    const htmlFilepath = path.join(reportDir, htmlFilename);
    
    fs.writeFileSync(htmlFilepath, htmlContent);
    console.log(`📄 Rapport HTML généré: ${htmlFilepath}`);
  }

  async cleanup() {
    if (this.prismaService) {
      await this.prismaService.$disconnect();
    }
    if (this.app) {
      await this.app.close();
    }
  }
}

// Exécution du script
async function main() {
  const checker = new FinalizationChecker();
  
  try {
    await checker.initialize();
    const report = await checker.checkFinalization();
    await checker.saveReport(report);
    
    console.log('\n🎯 RAPPORT DE FINALISATION');
    console.log('=' .repeat(50));
    console.log(`📊 Statut global: ${report.overall.status}`);
    console.log(`📈 Completion: ${report.overall.completionPercentage}%`);
    console.log(`🚀 Prêt pour production: ${report.overall.readyForProduction ? 'OUI' : 'NON'}`);
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 Recommandations principales:');
      report.recommendations.slice(0, 5).forEach((rec, i) => {
        console.log(`   ${i + 1}. ${rec}`);
      });
      if (report.recommendations.length > 5) {
        console.log(`   ... et ${report.recommendations.length - 5} autres`);
      }
    }
    
    console.log('\n✅ Rapport de finalisation généré avec succès!');
    
  } catch (error) {
    console.error('❌ Erreur lors de la vérification:', error);
    process.exit(1);
  } finally {
    await checker.cleanup();
  }
}

if (require.main === module) {
  main();
}

export { FinalizationChecker, FinalizationReport };
