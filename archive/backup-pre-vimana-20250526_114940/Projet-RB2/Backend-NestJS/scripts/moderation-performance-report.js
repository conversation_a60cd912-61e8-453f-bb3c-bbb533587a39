#!/usr/bin/env node

/**
 * Script to generate a performance report for the moderation system
 * 
 * Usage:
 *   node moderation-performance-report.js [--startDate YYYY-MM-DD] [--endDate YYYY-MM-DD] [--format json|csv|html] [--output filename]
 * 
 * Options:
 *   --startDate  Start date for the report (default: 30 days ago)
 *   --endDate    End date for the report (default: today)
 *   --format     Output format: json, csv, or html (default: json)
 *   --output     Output file (default: moderation-performance-report.{format})
 */

const fs = require('fs');
const path = require('path');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  startDate: new Date(new Date().setDate(new Date().getDate() - 30)),
  endDate: new Date(),
  format: 'json',
  output: null,
};

for (let i = 0; i < args.length; i++) {
  if (args[i] === '--startDate' && i + 1 < args.length) {
    options.startDate = new Date(args[i + 1]);
    i++;
  } else if (args[i] === '--endDate' && i + 1 < args.length) {
    options.endDate = new Date(args[i + 1]);
    i++;
  } else if (args[i] === '--format' && i + 1 < args.length) {
    options.format = args[i + 1];
    i++;
  } else if (args[i] === '--output' && i + 1 < args.length) {
    options.output = args[i + 1];
    i++;
  }
}

// Set default output filename if not provided
if (!options.output) {
  options.output = `moderation-performance-report.${options.format}`;
}

async function generateReport() {
  console.log('Generating moderation performance report...');
  console.log(`Start date: ${options.startDate.toISOString().split('T')[0]}`);
  console.log(`End date: ${options.endDate.toISOString().split('T')[0]}`);
  
  try {
    // Get all reports in the date range
    const reports = await prisma.report.findMany({
      where: {
        createdAt: {
          gte: options.startDate,
          lte: options.endDate,
        },
      },
      include: {
        moderationActions: true,
      },
    });
    
    console.log(`Found ${reports.length} reports in the date range`);
    
    // Calculate metrics
    const metrics = calculateMetrics(reports);
    
    // Generate report in the requested format
    let reportContent;
    switch (options.format) {
      case 'json':
        reportContent = JSON.stringify(metrics, null, 2);
        break;
      case 'csv':
        reportContent = generateCsvReport(metrics);
        break;
      case 'html':
        reportContent = generateHtmlReport(metrics);
        break;
      default:
        console.error(`Unsupported format: ${options.format}`);
        process.exit(1);
    }
    
    // Write report to file
    fs.writeFileSync(options.output, reportContent);
    console.log(`Report written to ${options.output}`);
    
  } catch (error) {
    console.error('Error generating report:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

function calculateMetrics(reports) {
  // Basic counts
  const totalReports = reports.length;
  const reportsByStatus = {};
  const reportsByContentType = {};
  
  // Moderation actions
  const totalActions = reports.reduce((sum, report) => sum + report.moderationActions.length, 0);
  const actionsByType = {};
  
  // Moderator performance
  const moderatorStats = {};
  
  // Time metrics
  let totalResolutionTime = 0;
  let resolvedReports = 0;
  
  // Process each report
  for (const report of reports) {
    // Count by status
    reportsByStatus[report.status] = (reportsByStatus[report.status] || 0) + 1;
    
    // Count by content type
    reportsByContentType[report.contentType] = (reportsByContentType[report.contentType] || 0) + 1;
    
    // Process moderation actions
    for (const action of report.moderationActions) {
      // Count by action type
      actionsByType[action.action] = (actionsByType[action.action] || 0) + 1;
      
      // Moderator stats
      if (!moderatorStats[action.moderatorId]) {
        moderatorStats[action.moderatorId] = {
          totalActions: 0,
          actionsByType: {},
        };
      }
      
      moderatorStats[action.moderatorId].totalActions++;
      moderatorStats[action.moderatorId].actionsByType[action.action] = 
        (moderatorStats[action.moderatorId].actionsByType[action.action] || 0) + 1;
    }
    
    // Calculate resolution time for resolved reports
    if (report.status === 'APPROVED' || report.status === 'REJECTED') {
      if (report.moderationActions.length > 0) {
        const lastAction = report.moderationActions.sort((a, b) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )[0];
        
        const resolutionTime = new Date(lastAction.createdAt).getTime() - new Date(report.createdAt).getTime();
        totalResolutionTime += resolutionTime;
        resolvedReports++;
      }
    }
  }
  
  // Calculate average resolution time (in hours)
  const avgResolutionTime = resolvedReports > 0 
    ? (totalResolutionTime / resolvedReports) / (1000 * 60 * 60) 
    : 0;
  
  // Calculate reports per day
  const reportsByDay = {};
  for (const report of reports) {
    const day = report.createdAt.toISOString().split('T')[0];
    reportsByDay[day] = (reportsByDay[day] || 0) + 1;
  }
  
  // Convert reportsByDay to array and sort
  const reportsByDayArray = Object.entries(reportsByDay).map(([date, count]) => ({ date, count }));
  reportsByDayArray.sort((a, b) => a.date.localeCompare(b.date));
  
  return {
    period: {
      startDate: options.startDate.toISOString().split('T')[0],
      endDate: options.endDate.toISOString().split('T')[0],
    },
    summary: {
      totalReports,
      totalActions,
      avgResolutionTime,
      resolvedReports,
    },
    reportsByStatus,
    reportsByContentType,
    actionsByType,
    moderatorStats,
    reportsByDay: reportsByDayArray,
  };
}

function generateCsvReport(metrics) {
  let csv = '';
  
  // Add header
  csv += 'Moderation Performance Report\n';
  csv += `Period: ${metrics.period.startDate} to ${metrics.period.endDate}\n\n`;
  
  // Summary
  csv += 'Summary\n';
  csv += 'Metric,Value\n';
  csv += `Total Reports,${metrics.summary.totalReports}\n`;
  csv += `Total Actions,${metrics.summary.totalActions}\n`;
  csv += `Average Resolution Time (hours),${metrics.summary.avgResolutionTime.toFixed(2)}\n`;
  csv += `Resolved Reports,${metrics.summary.resolvedReports}\n\n`;
  
  // Reports by Status
  csv += 'Reports by Status\n';
  csv += 'Status,Count\n';
  for (const [status, count] of Object.entries(metrics.reportsByStatus)) {
    csv += `${status},${count}\n`;
  }
  csv += '\n';
  
  // Reports by Content Type
  csv += 'Reports by Content Type\n';
  csv += 'Content Type,Count\n';
  for (const [contentType, count] of Object.entries(metrics.reportsByContentType)) {
    csv += `${contentType},${count}\n`;
  }
  csv += '\n';
  
  // Actions by Type
  csv += 'Actions by Type\n';
  csv += 'Action Type,Count\n';
  for (const [actionType, count] of Object.entries(metrics.actionsByType)) {
    csv += `${actionType},${count}\n`;
  }
  csv += '\n';
  
  // Reports by Day
  csv += 'Reports by Day\n';
  csv += 'Date,Count\n';
  for (const { date, count } of metrics.reportsByDay) {
    csv += `${date},${count}\n`;
  }
  
  return csv;
}

function generateHtmlReport(metrics) {
  let html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Moderation Performance Report</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h1, h2 { color: #333; }
    table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    tr:nth-child(even) { background-color: #f9f9f9; }
    .summary { display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 20px; }
    .summary-card { background-color: #f2f2f2; padding: 15px; border-radius: 5px; flex: 1; min-width: 200px; }
    .summary-card h3 { margin-top: 0; }
    .chart { height: 300px; margin-bottom: 20px; }
  </style>
</head>
<body>
  <h1>Moderation Performance Report</h1>
  <p>Period: ${metrics.period.startDate} to ${metrics.period.endDate}</p>
  
  <h2>Summary</h2>
  <div class="summary">
    <div class="summary-card">
      <h3>Total Reports</h3>
      <p>${metrics.summary.totalReports}</p>
    </div>
    <div class="summary-card">
      <h3>Total Actions</h3>
      <p>${metrics.summary.totalActions}</p>
    </div>
    <div class="summary-card">
      <h3>Average Resolution Time</h3>
      <p>${metrics.summary.avgResolutionTime.toFixed(2)} hours</p>
    </div>
    <div class="summary-card">
      <h3>Resolved Reports</h3>
      <p>${metrics.summary.resolvedReports}</p>
    </div>
  </div>
  
  <h2>Reports by Status</h2>
  <table>
    <tr>
      <th>Status</th>
      <th>Count</th>
    </tr>
    ${Object.entries(metrics.reportsByStatus).map(([status, count]) => `
    <tr>
      <td>${status}</td>
      <td>${count}</td>
    </tr>
    `).join('')}
  </table>
  
  <h2>Reports by Content Type</h2>
  <table>
    <tr>
      <th>Content Type</th>
      <th>Count</th>
    </tr>
    ${Object.entries(metrics.reportsByContentType).map(([contentType, count]) => `
    <tr>
      <td>${contentType}</td>
      <td>${count}</td>
    </tr>
    `).join('')}
  </table>
  
  <h2>Actions by Type</h2>
  <table>
    <tr>
      <th>Action Type</th>
      <th>Count</th>
    </tr>
    ${Object.entries(metrics.actionsByType).map(([actionType, count]) => `
    <tr>
      <td>${actionType}</td>
      <td>${count}</td>
    </tr>
    `).join('')}
  </table>
  
  <h2>Moderator Performance</h2>
  <table>
    <tr>
      <th>Moderator ID</th>
      <th>Total Actions</th>
    </tr>
    ${Object.entries(metrics.moderatorStats).map(([moderatorId, stats]) => `
    <tr>
      <td>${moderatorId}</td>
      <td>${stats.totalActions}</td>
    </tr>
    `).join('')}
  </table>
  
  <h2>Reports by Day</h2>
  <table>
    <tr>
      <th>Date</th>
      <th>Count</th>
    </tr>
    ${metrics.reportsByDay.map(({ date, count }) => `
    <tr>
      <td>${date}</td>
      <td>${count}</td>
    </tr>
    `).join('')}
  </table>
</body>
</html>
  `;
  
  return html;
}

// Run the report generation
generateReport();
