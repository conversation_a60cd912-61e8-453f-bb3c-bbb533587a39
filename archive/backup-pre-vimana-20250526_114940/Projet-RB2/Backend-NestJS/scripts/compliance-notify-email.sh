#!/bin/bash
# Envoie le rapport de conformité par e-mail (en pièce jointe)
# Prérequis : mailx installé et configuré (ou mutt, ou msmtp)

RECIPIENT="<EMAIL>"
SUBJECT="[CI/CD] Rapport de conformité cryptographique"
BODY="Bonjour,\n\nVeuillez trouver en pièce jointe le dernier rapport de conformité cryptographique généré automatiquement par le pipeline CI/CD.\n\nCordialement,\nL'équipe DevSecOps"

if [ ! -f compliance-report.pdf ]; then
  echo "Le rapport PDF n'existe pas. Générez-le d'abord."
  exit 1
fi

# Envoi du mail avec pièce jointe (mailx)
echo -e "$BODY" | mailx -s "$SUBJECT" -a compliance-report.pdf "$RECIPIENT"

if [ $? -eq 0 ]; then
  echo "✅ Rapport envoyé à $RECIPIENT."
else
  echo "❌ Erreur lors de l'envoi du mail."
  exit 1
fi
