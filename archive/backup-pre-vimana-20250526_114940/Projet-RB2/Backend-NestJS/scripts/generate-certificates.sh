#!/bin/bash

# Script pour générer les certificats nécessaires pour mTLS
# Ce script génère une autorité de certification (CA), un certificat serveur et un certificat client

# Définir les variables
CERTS_DIR="./certs"
CA_KEY="$CERTS_DIR/ca.key"
CA_CERT="$CERTS_DIR/ca.crt"
SERVER_KEY="$CERTS_DIR/server.key"
SERVER_CSR="$CERTS_DIR/server.csr"
SERVER_CERT="$CERTS_DIR/server.crt"
CLIENT_KEY="$CERTS_DIR/client.key"
CLIENT_CSR="$CERTS_DIR/client.csr"
CLIENT_CERT="$CERTS_DIR/client.crt"
OPENSSL_CONFIG="$CERTS_DIR/openssl.cnf"

# Créer le répertoire des certificats s'il n'existe pas
mkdir -p "$CERTS_DIR"

# C<PERSON><PERSON> le fichier de configuration OpenSSL
cat > "$OPENSSL_CONFIG" << EOF
[ req ]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = req_distinguished_name
req_extensions = v3_req

[ req_distinguished_name ]
C = FR
ST = Ile-de-France
L = Paris
O = Retreat And Be
OU = Security
CN = retreatandbe.com

[ v3_req ]
basicConstraints = CA:FALSE
keyUsage = digitalSignature, keyEncipherment
extendedKeyUsage = serverAuth, clientAuth
subjectAltName = @alt_names

[ v3_ca ]
basicConstraints = critical, CA:true
keyUsage = cRLSign, keyCertSign
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always,issuer:always

[ alt_names ]
DNS.1 = localhost
DNS.2 = retreatandbe.com
DNS.3 = *.retreatandbe.com
IP.1 = 127.0.0.1
EOF

echo "Génération des certificats pour mTLS..."

# Générer la clé privée et le certificat de l'autorité de certification (CA)
echo "Génération de l'autorité de certification (CA)..."
openssl genrsa -out "$CA_KEY" 4096
openssl req -x509 -new -nodes -key "$CA_KEY" -sha256 -days 3650 -out "$CA_CERT" -subj "/C=FR/ST=Ile-de-France/L=Paris/O=Retreat And Be/OU=Security/CN=Retreat And Be CA"

# Générer la clé privée et le certificat du serveur
echo "Génération du certificat serveur..."
openssl genrsa -out "$SERVER_KEY" 2048
openssl req -new -key "$SERVER_KEY" -out "$SERVER_CSR" -config "$OPENSSL_CONFIG"
openssl x509 -req -in "$SERVER_CSR" -CA "$CA_CERT" -CAkey "$CA_KEY" -CAcreateserial -out "$SERVER_CERT" -days 365 -sha256 -extensions v3_req -extfile "$OPENSSL_CONFIG"

# Générer la clé privée et le certificat du client
echo "Génération du certificat client..."
openssl genrsa -out "$CLIENT_KEY" 2048
openssl req -new -key "$CLIENT_KEY" -out "$CLIENT_CSR" -config "$OPENSSL_CONFIG" -subj "/C=FR/ST=Ile-de-France/L=Paris/O=Retreat And Be/OU=Clients/CN=client.retreatandbe.com"
openssl x509 -req -in "$CLIENT_CSR" -CA "$CA_CERT" -CAkey "$CA_KEY" -CAcreateserial -out "$CLIENT_CERT" -days 365 -sha256 -extensions v3_req -extfile "$OPENSSL_CONFIG"

# Vérifier les certificats
echo "Vérification des certificats..."
openssl verify -CAfile "$CA_CERT" "$SERVER_CERT"
openssl verify -CAfile "$CA_CERT" "$CLIENT_CERT"

# Afficher les informations des certificats
echo "Informations sur le certificat CA:"
openssl x509 -in "$CA_CERT" -text -noout | grep "Subject:" -A 1
echo "Informations sur le certificat serveur:"
openssl x509 -in "$SERVER_CERT" -text -noout | grep "Subject:" -A 1
echo "Informations sur le certificat client:"
openssl x509 -in "$CLIENT_CERT" -text -noout | grep "Subject:" -A 1

echo "Génération des certificats terminée avec succès!"
echo "Les certificats sont disponibles dans le répertoire $CERTS_DIR"
echo "Pour utiliser mTLS, démarrez l'application avec: npm run start:mtls"
