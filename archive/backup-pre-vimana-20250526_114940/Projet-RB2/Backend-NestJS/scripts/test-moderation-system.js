#!/usr/bin/env node

/**
 * <PERSON>ript to test the moderation system with reputation and learning features
 * 
 * Usage:
 *   node test-moderation-system.js [--users <number>] [--reports <number>] [--content <number>]
 * 
 * Options:
 *   --users    Number of test users to create (default: 10)
 *   --reports  Number of reports to generate (default: 20)
 *   --content  Number of content items to generate (default: 30)
 */

const axios = require('axios');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const faker = require('faker');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  users: 10,
  reports: 20,
  content: 30,
};

for (let i = 0; i < args.length; i++) {
  if (args[i] === '--users' && i + 1 < args.length) {
    options.users = parseInt(args[i + 1], 10);
    i++;
  } else if (args[i] === '--reports' && i + 1 < args.length) {
    options.reports = parseInt(args[i + 1], 10);
    i++;
  } else if (args[i] === '--content' && i + 1 < args.length) {
    options.content = parseInt(args[i + 1], 10);
    i++;
  }
}

// API base URL
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';

// Content types
const contentTypes = ['TEXT', 'IMAGE', 'VIDEO', 'COMMENT', 'ARTICLE'];

// Report reasons
const reportReasons = [
  'Contenu inapproprié',
  'Discours haineux',
  'Harcèlement',
  'Fausses informations',
  'Spam',
  'Contenu violent',
  'Contenu sexuellement explicite',
];

// Moderation actions
const moderationActions = ['APPROVE', 'REJECT', 'ESCALATE', 'ASSIGN'];

// Test users
const testUsers = [];

// Test content
const testContent = [];

// Test reports
const testReports = [];

// Test moderators
const testModerators = [];

async function runTest() {
  console.log('Starting moderation system test...');
  
  try {
    // Create test users
    await createTestUsers();
    
    // Create test content
    await createTestContent();
    
    // Create test reports
    await createTestReports();
    
    // Process reports
    await processReports();
    
    // Check user reputation
    await checkUserReputation();
    
    // Test trusted user moderation
    await testTrustedUserModeration();
    
    // Test moderation learning
    await testModerationLearning();
    
    console.log('Moderation system test completed successfully');
  } catch (error) {
    console.error('Error running moderation system test:', error.message);
  } finally {
    await cleanup();
    await prisma.$disconnect();
  }
}

async function createTestUsers() {
  console.log(`Creating ${options.users} test users...`);
  
  // Create regular users
  for (let i = 0; i < options.users; i++) {
    const user = await prisma.user.create({
      data: {
        email: faker.internet.email(),
        username: faker.internet.userName(),
        password: 'password123', // In a real app, this would be hashed
        role: 'USER',
      },
    });
    
    testUsers.push(user);
    console.log(`Created user: ${user.username} (${user.id})`);
  }
  
  // Create moderators
  for (let i = 0; i < 3; i++) {
    const moderator = await prisma.user.create({
      data: {
        email: faker.internet.email(),
        username: `moderator${i + 1}`,
        password: 'password123', // In a real app, this would be hashed
        role: 'MODERATOR',
      },
    });
    
    testModerators.push(moderator);
    console.log(`Created moderator: ${moderator.username} (${moderator.id})`);
  }
}

async function createTestContent() {
  console.log(`Creating ${options.content} test content items...`);
  
  for (let i = 0; i < options.content; i++) {
    const contentType = contentTypes[Math.floor(Math.random() * contentTypes.length)];
    const user = testUsers[Math.floor(Math.random() * testUsers.length)];
    
    let contentData = {
      userId: user.id,
      contentType,
      isPublished: true,
    };
    
    // Add content type specific data
    switch (contentType) {
      case 'TEXT':
      case 'COMMENT':
      case 'ARTICLE':
        contentData.text = faker.lorem.paragraphs(2);
        break;
      case 'IMAGE':
        contentData.imageUrl = faker.image.imageUrl();
        break;
      case 'VIDEO':
        contentData.videoUrl = `https://example.com/videos/${faker.random.uuid()}.mp4`;
        break;
    }
    
    // Add inappropriate content to some items
    const isInappropriate = Math.random() < 0.3; // 30% chance of inappropriate content
    
    if (isInappropriate) {
      if (contentType === 'TEXT' || contentType === 'COMMENT' || contentType === 'ARTICLE') {
        contentData.text += ' ' + ['hate speech', 'offensive content', 'explicit content'][Math.floor(Math.random() * 3)];
      }
      
      contentData.metadata = {
        isInappropriate: true,
        inappropriateReason: reportReasons[Math.floor(Math.random() * reportReasons.length)],
      };
    }
    
    const content = await prisma.content.create({
      data: contentData,
    });
    
    testContent.push(content);
    console.log(`Created ${contentType} content: ${content.id} (inappropriate: ${isInappropriate})`);
  }
}

async function createTestReports() {
  console.log(`Creating ${options.reports} test reports...`);
  
  for (let i = 0; i < options.reports; i++) {
    const content = testContent[Math.floor(Math.random() * testContent.length)];
    const reporter = testUsers[Math.floor(Math.random() * testUsers.length)];
    const reason = reportReasons[Math.floor(Math.random() * reportReasons.length)];
    
    const report = await prisma.report.create({
      data: {
        contentId: content.id,
        contentType: content.contentType,
        reporterId: reporter.id,
        reason,
        description: faker.lorem.sentence(),
        status: 'PENDING',
      },
    });
    
    testReports.push(report);
    console.log(`Created report: ${report.id} for content ${content.id} by user ${reporter.id}`);
  }
}

async function processReports() {
  console.log('Processing reports...');
  
  for (const report of testReports) {
    const moderator = testModerators[Math.floor(Math.random() * testModerators.length)];
    const action = moderationActions[Math.floor(Math.random() * moderationActions.length)];
    
    // Get content to check if it's actually inappropriate
    const content = testContent.find(c => c.id === report.contentId);
    const isActuallyInappropriate = content.metadata && content.metadata.isInappropriate;
    
    // Determine action based on content
    let finalAction = action;
    if (isActuallyInappropriate) {
      // If content is inappropriate, higher chance of approval
      finalAction = Math.random() < 0.8 ? 'APPROVE' : finalAction;
    } else {
      // If content is not inappropriate, higher chance of rejection
      finalAction = Math.random() < 0.8 ? 'REJECT' : finalAction;
    }
    
    try {
      // Add moderation action
      const moderationAction = await prisma.moderationAction.create({
        data: {
          reportId: report.id,
          moderatorId: moderator.id,
          action: finalAction,
          comment: faker.lorem.sentence(),
        },
      });
      
      // Update report status based on action
      let newStatus;
      switch (finalAction) {
        case 'APPROVE':
          newStatus = 'APPROVED';
          break;
        case 'REJECT':
          newStatus = 'REJECTED';
          break;
        case 'ESCALATE':
          newStatus = 'ESCALATED';
          break;
        case 'ASSIGN':
          newStatus = 'IN_REVIEW';
          break;
      }
      
      await prisma.report.update({
        where: { id: report.id },
        data: { status: newStatus },
      });
      
      console.log(`Processed report ${report.id} with action ${finalAction} by moderator ${moderator.username}`);
      
      // Simulate API call to trigger reputation updates
      try {
        await axios.post(`${API_BASE_URL}/moderation/reports/${report.id}/actions`, {
          action: finalAction,
          moderatorId: moderator.id,
          comment: moderationAction.comment,
        });
      } catch (error) {
        console.log(`API call failed, but continuing with test: ${error.message}`);
      }
    } catch (error) {
      console.error(`Error processing report ${report.id}:`, error.message);
    }
  }
}

async function checkUserReputation() {
  console.log('Checking user reputation...');
  
  for (const user of testUsers) {
    try {
      // Get user reputation from database
      const reputation = await prisma.userReputation.findUnique({
        where: { userId: user.id },
      });
      
      if (reputation) {
        console.log(`User ${user.username} reputation: ${reputation.reputationScore} (trusted: ${reputation.isTrusted})`);
      } else {
        console.log(`User ${user.username} has no reputation record yet`);
      }
      
      // Try API call to get reputation
      try {
        const response = await axios.get(`${API_BASE_URL}/moderation/reputation/user/${user.id}`);
        console.log(`API reputation for ${user.username}: ${JSON.stringify(response.data)}`);
      } catch (error) {
        console.log(`API call failed, but continuing with test: ${error.message}`);
      }
    } catch (error) {
      console.error(`Error checking reputation for user ${user.id}:`, error.message);
    }
  }
}

async function testTrustedUserModeration() {
  console.log('Testing trusted user moderation...');
  
  // Find or create a trusted user
  let trustedUser = testUsers.find(u => {
    const reputation = prisma.userReputation.findUnique({
      where: { userId: u.id },
    });
    return reputation && reputation.isTrusted;
  });
  
  if (!trustedUser) {
    // Create a trusted user
    trustedUser = testUsers[0];
    
    // Set reputation to trusted level
    await prisma.userReputation.upsert({
      where: { userId: trustedUser.id },
      update: {
        reputationScore: 200,
        isTrusted: true,
        privileges: ['report_content', 'priority_reports', 'review_content', 'auto_moderate'],
      },
      create: {
        userId: trustedUser.id,
        reputationScore: 200,
        isTrusted: true,
        privileges: ['report_content', 'priority_reports', 'review_content', 'auto_moderate'],
      },
    });
    
    console.log(`Set user ${trustedUser.username} as trusted user`);
  }
  
  // Create content with trusted user
  const contentType = 'TEXT';
  const contentData = {
    userId: trustedUser.id,
    contentType,
    text: faker.lorem.paragraphs(1) + ' potentially inappropriate content',
    isPublished: true,
  };
  
  const content = await prisma.content.create({
    data: contentData,
  });
  
  console.log(`Created content with trusted user: ${content.id}`);
  
  // Test moderation with trusted user
  try {
    const moderationResult = await axios.post(`${API_BASE_URL}/moderation/moderate`, {
      content: {
        id: content.id,
        text: content.text,
      },
      contentType,
      userId: trustedUser.id,
    });
    
    console.log(`Moderation result for trusted user content: ${JSON.stringify(moderationResult.data)}`);
  } catch (error) {
    console.log(`API call failed, but continuing with test: ${error.message}`);
  }
}

async function testModerationLearning() {
  console.log('Testing moderation learning...');
  
  // Create some feedback for learning
  for (let i = 0; i < 5; i++) {
    const content = testContent[Math.floor(Math.random() * testContent.length)];
    const moderator = testModerators[Math.floor(Math.random() * testModerators.length)];
    const isInappropriate = Math.random() < 0.5;
    
    try {
      await axios.post(`${API_BASE_URL}/moderation/learning/feedback`, {
        contentId: content.id,
        contentType: content.contentType,
        moderationResult: isInappropriate,
        moderatorId: moderator.id,
        confidence: 0.9,
      });
      
      console.log(`Added moderation feedback for content ${content.id} (inappropriate: ${isInappropriate})`);
    } catch (error) {
      console.log(`API call failed, but continuing with test: ${error.message}`);
    }
  }
  
  // Process feedback queue
  try {
    await axios.post(`${API_BASE_URL}/moderation/learning/process-queue`);
    console.log('Processed feedback queue');
  } catch (error) {
    console.log(`API call failed, but continuing with test: ${error.message}`);
  }
  
  // Get model metrics
  try {
    const response = await axios.get(`${API_BASE_URL}/moderation/learning/metrics`);
    console.log(`Model metrics: ${JSON.stringify(response.data)}`);
  } catch (error) {
    console.log(`API call failed, but continuing with test: ${error.message}`);
  }
}

async function cleanup() {
  console.log('Cleaning up test data...');
  
  // In a real test, you might want to clean up the test data
  // For this example, we'll leave the data for inspection
  console.log('Test data left in database for inspection');
}

// Run the test
runTest();
