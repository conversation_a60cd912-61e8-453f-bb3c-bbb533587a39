#!/bin/bash

# Script principal pour configurer les nouveaux modules

# Couleurs pour les messages
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Configuration des nouveaux modules...${NC}"

# Rendre les scripts exécutables
echo -e "${YELLOW}Rendre les scripts exécutables...${NC}"
chmod +x ./scripts/install-dependencies.sh
chmod +x ./scripts/apply-schema-updates.sh
chmod +x ./scripts/test-new-modules.sh
echo -e "${GREEN}Scripts rendus exécutables avec succès.${NC}"

# Installer les dépendances
echo -e "${YELLOW}Installation des dépendances...${NC}"
./scripts/install-dependencies.sh
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'installation des dépendances.${NC}"
    exit 1
fi
echo -e "${GREEN}Dépendances installées avec succès.${NC}"

# Appliquer les mises à jour du schéma Prisma
echo -e "${YELLOW}Application des mises à jour du schéma Prisma...${NC}"
./scripts/apply-schema-updates.sh
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'application des mises à jour du schéma Prisma.${NC}"
    exit 1
fi
echo -e "${GREEN}Mises à jour du schéma Prisma appliquées avec succès.${NC}"

# Exécuter les tests
echo -e "${YELLOW}Exécution des tests...${NC}"
./scripts/test-new-modules.sh
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'exécution des tests.${NC}"
    exit 1
fi
echo -e "${GREEN}Tests exécutés avec succès.${NC}"

echo -e "${GREEN}Configuration des nouveaux modules terminée avec succès.${NC}"
echo -e "${YELLOW}Pour démarrer l'application avec les nouveaux modules, exécutez :${NC}"
echo -e "${GREEN}npm run start:dev${NC}"
