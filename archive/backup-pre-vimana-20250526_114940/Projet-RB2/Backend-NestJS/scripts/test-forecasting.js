#!/usr/bin/env node

/**
 * Script pour tester les fonctionnalités de prévision et tendances
 * 
 * Usage:
 *   node test-forecasting.js [--creator <creatorId>] [--days <number>]
 * 
 * Options:
 *   --creator  ID du créateur à utiliser pour les tests (défaut: génère un nouvel ID)
 *   --days     Nombre de jours à prévoir (défaut: 30)
 */

const axios = require('axios');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const faker = require('faker');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  creatorId: null,
  days: 30,
};

for (let i = 0; i < args.length; i++) {
  if (args[i] === '--creator' && i + 1 < args.length) {
    options.creatorId = args[i + 1];
    i++;
  } else if (args[i] === '--days' && i + 1 < args.length) {
    options.days = parseInt(args[i + 1], 10);
    i++;
  }
}

// API base URL
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';

// Dates pour les données historiques
const today = new Date();
const oneYearAgo = new Date();
oneYearAgo.setFullYear(today.getFullYear() - 1);

async function runTest() {
  console.log('Starting forecasting test...');
  
  try {
    // Créer un créateur de test si nécessaire
    if (!options.creatorId) {
      const creator = await createTestCreator();
      options.creatorId = creator.id;
    }
    
    // Générer des données historiques
    await generateHistoricalData(options.creatorId);
    
    // Tester les prévisions d'engagement
    await testEngagementForecasts(options.creatorId, options.days);
    
    // Tester les tendances d'engagement
    await testEngagementTrends(options.creatorId);
    
    // Tester l'analyse de saisonnalité
    await testSeasonalityAnalysis(options.creatorId);
    
    console.log('Forecasting test completed successfully');
  } catch (error) {
    console.error('Error running forecasting test:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

async function createTestCreator() {
  console.log('Creating test creator...');
  
  const creator = await prisma.user.create({
    data: {
      email: faker.internet.email(),
      username: faker.internet.userName(),
      password: 'password123', // Dans une vraie application, ce serait hashé
      role: 'CREATOR',
    },
  });
  
  console.log(`Created test creator: ${creator.username} (${creator.id})`);
  return creator;
}

async function generateHistoricalData(creatorId) {
  console.log(`Generating historical data for creator ${creatorId}...`);
  
  // Générer des données d'engagement pour les 365 derniers jours
  const startDate = new Date(oneYearAgo);
  const endDate = new Date(today);
  
  // Tendance de base pour les vues (croissance progressive)
  let baseViews = 100;
  const viewsGrowthRate = 1.002; // Croissance quotidienne de 0.2%
  
  // Tendance de base pour les likes (croissance plus rapide)
  let baseLikes = 10;
  const likesGrowthRate = 1.003; // Croissance quotidienne de 0.3%
  
  // Tendance de base pour les commentaires (croissance lente)
  let baseComments = 5;
  const commentsGrowthRate = 1.001; // Croissance quotidienne de 0.1%
  
  // Tendance de base pour les partages (stable)
  let baseShares = 2;
  const sharesGrowthRate = 1.0005; // Croissance quotidienne de 0.05%
  
  // Générer des données pour chaque jour
  for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
    // Ajouter une variation aléatoire
    const dayOfWeek = date.getDay();
    const weekendMultiplier = (dayOfWeek === 0 || dayOfWeek === 6) ? 1.2 : 1.0;
    const randomFactor = 0.8 + Math.random() * 0.4; // Entre 0.8 et 1.2
    
    // Calculer les valeurs pour ce jour
    const views = Math.round(baseViews * weekendMultiplier * randomFactor);
    const likes = Math.round(baseLikes * weekendMultiplier * randomFactor);
    const comments = Math.round(baseComments * weekendMultiplier * randomFactor);
    const shares = Math.round(baseShares * weekendMultiplier * randomFactor);
    
    // Créer la métrique d'engagement
    await prisma.engagementMetric.create({
      data: {
        creatorId,
        date: new Date(date),
        views,
        likes,
        comments,
        shares,
        bookmarks: Math.round(likes * 0.3),
        engagementRate: (likes + comments + shares) / views,
      },
    });
    
    // Mettre à jour les valeurs de base pour le jour suivant
    baseViews *= viewsGrowthRate;
    baseLikes *= likesGrowthRate;
    baseComments *= commentsGrowthRate;
    baseShares *= sharesGrowthRate;
  }
  
  console.log(`Generated historical data for ${(endDate - startDate) / (1000 * 60 * 60 * 24)} days`);
}

async function testEngagementForecasts(creatorId, days) {
  console.log(`Testing engagement forecasts for creator ${creatorId}...`);
  
  try {
    // Appeler l'API de prévision
    const response = await axios.get(`${API_BASE_URL}/analytics/forecasting/${creatorId}/engagement`, {
      params: {
        days,
      },
    });
    
    console.log('Engagement forecasts:');
    console.log(JSON.stringify(response.data, null, 2));
    
    // Vérifier les résultats
    if (response.data.forecasts && response.data.forecasts.length > 0) {
      console.log('Forecast metrics:');
      response.data.forecasts.forEach(forecast => {
        console.log(`- ${forecast.metric}: ${forecast.predictions.length} predictions, confidence: ${forecast.confidence}`);
      });
    } else {
      console.log('No forecasts returned');
    }
  } catch (error) {
    console.error(`Error testing engagement forecasts: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Data: ${JSON.stringify(error.response.data)}`);
    }
  }
}

async function testEngagementTrends(creatorId) {
  console.log(`Testing engagement trends for creator ${creatorId}...`);
  
  try {
    // Appeler l'API de tendances
    const response = await axios.get(`${API_BASE_URL}/analytics/forecasting/${creatorId}/trends`);
    
    console.log('Engagement trends:');
    console.log(JSON.stringify(response.data, null, 2));
    
    // Vérifier les résultats
    if (response.data.trends && response.data.trends.length > 0) {
      console.log('Trend metrics:');
      response.data.trends.forEach(trend => {
        console.log(`- ${trend.metric}: ${trend.trend}, change rate: ${(trend.changeRate * 100).toFixed(2)}%, significance: ${(trend.significance * 100).toFixed(2)}%`);
      });
    } else {
      console.log('No trends returned');
    }
  } catch (error) {
    console.error(`Error testing engagement trends: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Data: ${JSON.stringify(error.response.data)}`);
    }
  }
}

async function testSeasonalityAnalysis(creatorId) {
  console.log(`Testing seasonality analysis for creator ${creatorId}...`);
  
  try {
    // Appeler l'API de saisonnalité
    const response = await axios.get(`${API_BASE_URL}/analytics/forecasting/${creatorId}/seasonality`);
    
    console.log('Seasonality analysis:');
    console.log(JSON.stringify(response.data, null, 2));
    
    // Vérifier les résultats
    if (response.data.seasonality && response.data.seasonality.length > 0) {
      console.log('Seasonality metrics:');
      response.data.seasonality.forEach(seasonality => {
        console.log(`- ${seasonality.metric}:`);
        
        if (seasonality.weekly) {
          console.log(`  Weekly: peak days: ${seasonality.weekly.peakDays.join(', ')}, low days: ${seasonality.weekly.lowDays.join(', ')}`);
        }
        
        if (seasonality.daily) {
          console.log(`  Daily: peak hours: ${seasonality.daily.peakHours.join(', ')}, low hours: ${seasonality.daily.lowHours.join(', ')}`);
        }
      });
    } else {
      console.log('No seasonality analysis returned');
    }
  } catch (error) {
    console.error(`Error testing seasonality analysis: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Data: ${JSON.stringify(error.response.data)}`);
    }
  }
}

// Exécuter le test
runTest();
