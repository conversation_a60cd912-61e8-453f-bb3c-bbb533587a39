#!/bin/bash
# Script CI/CD: vérification de conformité cryptographique et génération du rapport Markdown

API_URL="http://localhost:3000/crypto-compliance/report"
API_MD_URL="http://localhost:3000/crypto-compliance/report/markdown"

# Vérification du rapport JSON
REPORT_JSON=$(curl -s "$API_URL")
FAILED=$(echo "$REPORT_JSON" | jq '.summary.failed')

if [ "$FAILED" -gt 0 ]; then
  echo "❌ Non-conformité détectée ($FAILED contrôle(s) échoué(s))."
  # Générer le rapport Markdown pour archivage/audit
  curl -s "$API_MD_URL" > compliance-report.md
  echo "Rapport Markdown généré: compliance-report.md"
  exit 1
else
  echo "✅ Tous les contrôles de conformité sont passés."
  curl -s "$API_MD_URL" > compliance-report.md
  echo "Rapport Markdown généré: compliance-report.md"
  exit 0
fi
