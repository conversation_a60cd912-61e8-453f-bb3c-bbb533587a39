#!/usr/bin/env node

/**
 * Script de génération de rapports de performance pour le système de recommandation
 *
 * Ce script collecte des métriques de performance du système de recommandation,
 * les analyse et génère un rapport détaillé avec des visualisations et des recommandations.
 *
 * Usage:
 *   node recommendation-performance-report.js [options]
 *
 * Options:
 *   --format=json|pdf|html|interactive    Format de sortie (défaut: json)
 *   --output=<path>           Chemin du fichier de sortie (défaut: ./reports/recommendation-performance-report-<timestamp>.<format>)
 *   --days=<number>           Nombre de jours d'historique à analyser (défaut: 7)
 *   --detailed                Inclure des informations détaillées
 *   --api-url=<url>           URL de l'API (défaut: http://localhost:3000)
 *   --token=<jwt>             Token JWT pour l'authentification
 *   --endpoints=<list>        Liste d'endpoints à tester, séparés par des virgules
 *   --strategies=<list>       Liste de stratégies à tester, séparées par des virgules
 *   --compare                 Comparer avec la période précédente
 *   --theme=<theme>           Thème du rapport: light, dark, branded (défaut: light)
 *   --benchmark               Inclure des benchmarks de l'industrie
 *   --export-metrics          Exporter les métriques brutes dans un fichier séparé
 *   --help                    Afficher l'aide
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { program } = require('commander');
const chalk = require('chalk');
const ora = require('ora');
const Table = require('cli-table3');
const { createCanvas } = require('canvas');
const PDFDocument = require('pdfkit');
const os = require('os');
const { performance } = require('perf_hooks');
const { format, subDays, differenceInDays, parseISO } = require('date-fns');
const Chart = require('chart.js');
const { ChartJSNodeCanvas } = require('chartjs-node-canvas');
const open = require('open');
const { v4: uuidv4 } = require('uuid');

// Configuration par défaut
const DEFAULT_API_URL = 'http://localhost:3000';
const DEFAULT_FORMAT = 'json';
const DEFAULT_DAYS = 7;
const DEFAULT_THEME = 'light';
const REPORTS_DIR = path.join(process.cwd(), 'reports');
const TIMESTAMP = new Date().toISOString().replace(/:/g, '-').split('.')[0];
const SESSION_ID = uuidv4().substring(0, 8);

// Configuration pour la génération de graphiques
const chartJSNodeCanvas = new ChartJSNodeCanvas({
  width: 800,
  height: 400,
  plugins: {
    modern: true,
    requireLegacy: ['chartjs-plugin-datalabels']
  }
});

// Endpoints à tester
const DEFAULT_ENDPOINTS = [
  '/api/v1/recommendations',
  '/api/v1/recommendations/trending',
  '/api/v1/recommendations/similar',
  '/api/v1/recommendations/multi-criteria',
  '/api/v1/recommendations/explanation',
  '/api/v1/recommendations/context',
  '/api/v1/recommendations/social',
  '/api/v1/recommendations/group'
];

// Stratégies à tester
const DEFAULT_STRATEGIES = [
  'CONTENT_BASED',
  'COLLABORATIVE',
  'HYBRID',
  'CONTEXT_AWARE',
  'SOCIAL'
];

// Types d'éléments à tester
const ITEM_TYPES = ['RETREAT', 'PARTNER', 'COURSE'];

// Seuils de performance
const PERFORMANCE_THRESHOLDS = {
  responseTime: {
    excellent: 100,
    good: 300,
    average: 500,
    poor: 1000
  },
  errorRate: {
    excellent: 0.1,
    good: 0.5,
    average: 1,
    poor: 5
  },
  cacheHitRate: {
    excellent: 90,
    good: 80,
    average: 70,
    poor: 50
  },
  cpuUsage: {
    excellent: 30,
    good: 50,
    average: 70,
    poor: 85
  },
  memoryUsage: {
    excellent: 30,
    good: 50,
    average: 70,
    poor: 85
  },
  requestsPerSecond: {
    excellent: 100,
    good: 50,
    average: 20,
    poor: 10
  }
};

// Définir les options de ligne de commande
program
  .option('--format <format>', 'Format de sortie (json, pdf, html, interactive)', DEFAULT_FORMAT)
  .option('--output <path>', 'Chemin du fichier de sortie')
  .option('--days <number>', 'Nombre de jours d\'historique à analyser', DEFAULT_DAYS)
  .option('--detailed', 'Inclure des informations détaillées')
  .option('--api-url <url>', 'URL de l\'API', DEFAULT_API_URL)
  .option('--token <token>', 'Token JWT pour l\'authentification')
  .option('--endpoints <list>', 'Liste d\'endpoints à tester, séparés par des virgules')
  .option('--strategies <list>', 'Liste de stratégies à tester, séparées par des virgules')
  .option('--compare', 'Comparer avec la période précédente')
  .option('--theme <theme>', 'Thème du rapport: light, dark, branded', DEFAULT_THEME)
  .option('--benchmark', 'Inclure des benchmarks de l\'industrie')
  .option('--export-metrics', 'Exporter les métriques brutes dans un fichier séparé')
  .option('--help', 'Afficher l\'aide');

program.parse(process.argv);

const options = program.opts();

// Afficher l'aide si demandé
if (options.help) {
  console.log(`
Usage: node recommendation-performance-report.js [options]

Options:
  --format=json|pdf|html|interactive    Format de sortie (défaut: json)
  --output=<path>           Chemin du fichier de sortie (défaut: ./reports/recommendation-performance-report-<timestamp>.<format>)
  --days=<number>           Nombre de jours d'historique à analyser (défaut: 7)
  --detailed                Inclure des informations détaillées
  --api-url=<url>           URL de l'API (défaut: http://localhost:3000)
  --token=<jwt>             Token JWT pour l'authentification
  --endpoints=<list>        Liste d'endpoints à tester, séparés par des virgules
  --strategies=<list>       Liste de stratégies à tester, séparées par des virgules
  --compare                 Comparer avec la période précédente
  --theme=<theme>           Thème du rapport: light, dark, branded (défaut: light)
  --benchmark               Inclure des benchmarks de l'industrie
  --export-metrics          Exporter les métriques brutes dans un fichier séparé
  --help                    Afficher l'aide
  `);
  process.exit(0);
}

// Créer le répertoire de rapports s'il n'existe pas
if (!fs.existsSync(REPORTS_DIR)) {
  fs.mkdirSync(REPORTS_DIR, { recursive: true });
}

// Déterminer le chemin de sortie
const format = options.format.toLowerCase();
const theme = options.theme || DEFAULT_THEME;
const defaultFilename = `recommendation-performance-report-${TIMESTAMP}.${format}`;
const outputPath = options.output || path.join(REPORTS_DIR, defaultFilename);

// Chemin pour les métriques brutes si l'option est activée
const metricsOutputPath = options.exportMetrics
  ? path.join(REPORTS_DIR, `recommendation-metrics-${TIMESTAMP}.json`)
  : null;

// Chemin pour les graphiques générés
const chartsDir = path.join(REPORTS_DIR, 'charts');
if (!fs.existsSync(chartsDir)) {
  fs.mkdirSync(chartsDir, { recursive: true });
}

// Thèmes de couleurs pour les graphiques
const chartThemes = {
  light: {
    backgroundColor: 'white',
    textColor: '#333333',
    gridColor: '#dddddd',
    colors: ['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6', '#1abc9c', '#34495e', '#e67e22']
  },
  dark: {
    backgroundColor: '#2c3e50',
    textColor: '#ecf0f1',
    gridColor: '#34495e',
    colors: ['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6', '#1abc9c', '#95a5a6', '#e67e22']
  },
  branded: {
    backgroundColor: '#ecf0f1',
    textColor: '#2c3e50',
    gridColor: '#bdc3c7',
    colors: ['#8e44ad', '#27ae60', '#d35400', '#2c3e50', '#16a085', '#c0392b', '#7f8c8d', '#f1c40f']
  }
};

// Analyser les endpoints et stratégies
const endpoints = options.endpoints ? options.endpoints.split(',') : DEFAULT_ENDPOINTS;
const strategies = options.strategies ? options.strategies.split(',') : DEFAULT_STRATEGIES;

// Créer le client HTTP
const api = axios.create({
  baseURL: options.apiUrl,
  timeout: 30000,
  headers: options.token ? {
    Authorization: `Bearer ${options.token}`
  } : {}
});

/**
 * Fonction principale
 */
async function main() {
  try {
    console.log(chalk.blue.bold('=== Rapport de Performance du Système de Recommandation ==='));
    console.log(chalk.blue(`Date: ${new Date().toLocaleString()}`));
    console.log(chalk.blue(`Période d'analyse: ${options.days} jours`));
    console.log(chalk.blue(`Format de sortie: ${format}`));
    console.log(chalk.blue(`Thème: ${theme}`));
    console.log(chalk.blue(`Fichier de sortie: ${outputPath}`));
    if (options.compare) {
      console.log(chalk.blue('Comparaison avec la période précédente activée'));
    }
    if (options.benchmark) {
      console.log(chalk.blue('Benchmarks de l\'industrie inclus'));
    }
    if (options.exportMetrics) {
      console.log(chalk.blue(`Métriques brutes exportées vers: ${metricsOutputPath}`));
    }
    console.log('');

    // Collecter les métriques
    const spinner = ora('Collecte des métriques de performance...').start();
    const metrics = await collectPerformanceMetrics();
    spinner.succeed('Métriques collectées avec succès');

    // Collecter les données de comparaison si demandé
    if (options.compare) {
      spinner.text = 'Collecte des données de comparaison...';
      spinner.start();
      metrics.comparison = await collectComparisonData();
      spinner.succeed('Données de comparaison collectées avec succès');
    }

    // Collecter les benchmarks si demandé
    if (options.benchmark) {
      spinner.text = 'Collecte des benchmarks de l\'industrie...';
      spinner.start();
      metrics.benchmarks = await collectBenchmarkData();
      spinner.succeed('Benchmarks collectés avec succès');
    }

    // Analyser les métriques
    spinner.text = 'Analyse des métriques...';
    spinner.start();
    const analysis = await analyzePerformanceMetrics(metrics);
    spinner.succeed('Analyse terminée');

    // Générer les graphiques
    spinner.text = 'Génération des graphiques...';
    spinner.start();
    const charts = await generateCharts(metrics, analysis);
    spinner.succeed('Graphiques générés avec succès');

    // Générer le rapport
    spinner.text = 'Génération du rapport...';
    spinner.start();
    await generateReport(metrics, analysis, charts);
    spinner.succeed(`Rapport généré avec succès: ${outputPath}`);

    // Exporter les métriques brutes si demandé
    if (options.exportMetrics) {
      spinner.text = 'Exportation des métriques brutes...';
      spinner.start();
      await exportRawMetrics(metrics, metricsOutputPath);
      spinner.succeed(`Métriques exportées avec succès: ${metricsOutputPath}`);
    }

    // Afficher un résumé
    displaySummary(metrics, analysis);

    // Ouvrir le rapport dans le navigateur si format interactif
    if (format === 'interactive' || (format === 'html' && options.detailed)) {
      console.log(chalk.blue('Ouverture du rapport dans le navigateur...'));
      await open(outputPath);
    }

  } catch (error) {
    console.error(chalk.red(`Erreur: ${error.message}`));
    if (error.response) {
      console.error(chalk.red(`Statut: ${error.response.status}`));
      console.error(chalk.red(`Données: ${JSON.stringify(error.response.data)}`));
    }
    process.exit(1);
  }
}

/**
 * Collecte les métriques de performance
 */
async function collectPerformanceMetrics() {
  const startTime = performance.now();

  // Récupérer les métriques système
  const systemMetrics = await getSystemMetrics();

  // Récupérer les métriques de l'API
  const apiMetrics = await getApiMetrics();

  // Récupérer les métriques de la base de données
  const databaseMetrics = await getDatabaseMetrics();

  // Récupérer les métriques du cache
  const cacheMetrics = await getCacheMetrics();

  // Récupérer les métriques spécifiques au système de recommandation
  const recommendationMetrics = await getRecommendationMetrics();

  // Récupérer les alertes récentes
  const recentAlerts = await getRecentAlerts();

  // Effectuer des tests de performance
  const performanceTests = await runPerformanceTests();

  const endTime = performance.now();

  return {
    timestamp: new Date(),
    collectionDuration: endTime - startTime,
    system: systemMetrics,
    api: apiMetrics,
    database: databaseMetrics,
    cache: cacheMetrics,
    recommendation: recommendationMetrics,
    alerts: recentAlerts,
    performanceTests
  };
}

/**
 * Récupère les métriques système
 */
async function getSystemMetrics() {
  try {
    // Récupérer les métriques système via l'API
    const response = await api.get('/performance/metrics/system');

    // Si l'API n'est pas disponible, utiliser les métriques locales
    if (!response.data) {
      return getLocalSystemMetrics();
    }

    return response.data;
  } catch (error) {
    console.warn(chalk.yellow('Impossible de récupérer les métriques système via l\'API, utilisation des métriques locales'));
    return getLocalSystemMetrics();
  }
}

/**
 * Récupère les métriques système locales
 */
function getLocalSystemMetrics() {
  const cpus = os.cpus();
  const totalMem = os.totalmem();
  const freeMem = os.freemem();
  const usedMem = totalMem - freeMem;

  return {
    cpuUsage: {
      type: 'CPU_USAGE',
      value: getCpuUsagePercent(),
      unit: '%',
      timestamp: new Date(),
      status: getCpuStatus(getCpuUsagePercent())
    },
    memoryUsage: {
      type: 'MEMORY_USAGE',
      value: (usedMem / totalMem) * 100,
      unit: '%',
      timestamp: new Date(),
      status: getMemoryStatus((usedMem / totalMem) * 100)
    },
    uptime: os.uptime(),
    loadAverage: os.loadavg(),
    platform: os.platform(),
    arch: os.arch(),
    hostname: os.hostname(),
    cpuInfo: cpus[0].model,
    cpuCount: cpus.length,
    totalMemory: totalMem,
    freeMemory: freeMem
  };
}

/**
 * Calcule l'utilisation CPU en pourcentage
 */
function getCpuUsagePercent() {
  const cpus = os.cpus();
  let totalIdle = 0;
  let totalTick = 0;

  for (const cpu of cpus) {
    for (const type in cpu.times) {
      totalTick += cpu.times[type];
    }
    totalIdle += cpu.times.idle;
  }

  return 100 - (totalIdle / totalTick) * 100;
}

/**
 * Récupère les métriques de l'API
 */
async function getApiMetrics() {
  try {
    const response = await api.get('/performance/metrics/api');
    return response.data;
  } catch (error) {
    console.warn(chalk.yellow('Impossible de récupérer les métriques API, utilisation de valeurs par défaut'));
    return {
      requestsPerMinute: {
        type: 'API_REQUESTS_PER_MINUTE',
        value: 0,
        unit: 'req/min',
        timestamp: new Date(),
        status: 'UNKNOWN'
      },
      averageResponseTime: {
        type: 'API_AVERAGE_RESPONSE_TIME',
        value: 0,
        unit: 'ms',
        timestamp: new Date(),
        status: 'UNKNOWN'
      },
      errorRate: {
        type: 'API_ERROR_RATE',
        value: 0,
        unit: '%',
        timestamp: new Date(),
        status: 'UNKNOWN'
      }
    };
  }
}

/**
 * Récupère les métriques de la base de données
 */
async function getDatabaseMetrics() {
  try {
    const response = await api.get('/performance/metrics/database');
    return response.data;
  } catch (error) {
    console.warn(chalk.yellow('Impossible de récupérer les métriques de base de données, utilisation de valeurs par défaut'));
    return {
      averageQueryTime: {
        type: 'DB_AVERAGE_QUERY_TIME',
        value: 0,
        unit: 'ms',
        timestamp: new Date(),
        status: 'UNKNOWN'
      },
      queriesPerMinute: {
        type: 'DB_QUERIES_PER_MINUTE',
        value: 0,
        unit: 'queries/min',
        timestamp: new Date(),
        status: 'UNKNOWN'
      },
      connectionPoolUsage: {
        type: 'DB_CONNECTION_POOL_USAGE',
        value: 0,
        unit: '%',
        timestamp: new Date(),
        status: 'UNKNOWN'
      }
    };
  }
}

/**
 * Récupère les métriques du cache
 */
async function getCacheMetrics() {
  try {
    const response = await api.get('/performance/metrics/cache');
    return response.data;
  } catch (error) {
    console.warn(chalk.yellow('Impossible de récupérer les métriques de cache, utilisation de valeurs par défaut'));
    return {
      hitRate: {
        type: 'CACHE_HIT_RATE',
        value: 0,
        unit: '%',
        timestamp: new Date(),
        status: 'UNKNOWN'
      },
      size: {
        type: 'CACHE_SIZE',
        value: 0,
        unit: 'MB',
        timestamp: new Date(),
        status: 'UNKNOWN'
      },
      itemCount: {
        type: 'CACHE_ITEM_COUNT',
        value: 0,
        unit: 'items',
        timestamp: new Date(),
        status: 'UNKNOWN'
      }
    };
  }
}

/**
 * Récupère les métriques spécifiques au système de recommandation
 */
async function getRecommendationMetrics() {
  try {
    const response = await api.get('/recommendation-reports/metrics');
    return response.data;
  } catch (error) {
    console.warn(chalk.yellow('Impossible de récupérer les métriques de recommandation, utilisation de valeurs par défaut'));
    return {
      accuracy: {
        type: 'RECOMMENDATION_ACCURACY',
        value: 0,
        unit: '%',
        timestamp: new Date(),
        status: 'UNKNOWN'
      },
      diversity: {
        type: 'RECOMMENDATION_DIVERSITY',
        value: 0,
        unit: '%',
        timestamp: new Date(),
        status: 'UNKNOWN'
      },
      coverage: {
        type: 'RECOMMENDATION_COVERAGE',
        value: 0,
        unit: '%',
        timestamp: new Date(),
        status: 'UNKNOWN'
      },
      clickThroughRate: {
        type: 'RECOMMENDATION_CTR',
        value: 0,
        unit: '%',
        timestamp: new Date(),
        status: 'UNKNOWN'
      },
      conversionRate: {
        type: 'RECOMMENDATION_CONVERSION_RATE',
        value: 0,
        unit: '%',
        timestamp: new Date(),
        status: 'UNKNOWN'
      }
    };
  }
}

/**
 * Récupère les alertes récentes
 */
async function getRecentAlerts() {
  try {
    const response = await api.get('/performance/alerts/recent');
    return response.data;
  } catch (error) {
    console.warn(chalk.yellow('Impossible de récupérer les alertes récentes'));
    return [];
  }
}

/**
 * Exécute des tests de performance sur les endpoints de recommandation
 */
async function runPerformanceTests() {
  const results = [];

  // Tester chaque endpoint
  for (const endpoint of endpoints) {
    // Tester chaque stratégie pour cet endpoint
    for (const strategy of strategies) {
      // Tester chaque type d'élément
      for (const itemType of ITEM_TYPES) {
        try {
          const testResult = await testEndpoint(endpoint, strategy, itemType);
          results.push(testResult);
        } catch (error) {
          console.warn(chalk.yellow(`Erreur lors du test de l'endpoint ${endpoint} avec la stratégie ${strategy} pour le type ${itemType}: ${error.message}`));
          results.push({
            endpoint,
            strategy,
            itemType,
            success: false,
            error: error.message,
            timestamp: new Date()
          });
        }
      }
    }
  }

  return results;
}

/**
 * Teste un endpoint spécifique
 */
async function testEndpoint(endpoint, strategy, itemType) {
  const startTime = performance.now();

  // Construire les paramètres de requête
  const params = {
    strategy,
    type: itemType,
    limit: 10
  };

  // Effectuer la requête
  const response = await api.get(endpoint, { params });

  const endTime = performance.now();
  const duration = endTime - startTime;

  return {
    endpoint,
    strategy,
    itemType,
    success: true,
    statusCode: response.status,
    duration,
    resultCount: response.data?.length || 0,
    timestamp: new Date()
  };
}

/**
 * Analyse les métriques de performance
 */
async function analyzePerformanceMetrics(metrics) {
  // Évaluer le statut global de performance
  const performanceStatus = evaluatePerformanceStatus(metrics);

  // Générer des recommandations
  const recommendations = generateRecommendations(metrics, performanceStatus);

  // Identifier les tendances
  const trends = identifyTrends(metrics);

  // Calculer les statistiques
  const statistics = calculateStatistics(metrics);

  return {
    performanceStatus,
    recommendations,
    trends,
    statistics
  };
}

/**
 * Évalue le statut global de performance
 */
function evaluatePerformanceStatus(metrics) {
  // Calculer le score pour chaque catégorie
  const systemScore = evaluateSystemMetrics(metrics.system);
  const apiScore = evaluateApiMetrics(metrics.api);
  const databaseScore = evaluateDatabaseMetrics(metrics.database);
  const cacheScore = evaluateCacheMetrics(metrics.cache);
  const recommendationScore = evaluateRecommendationMetrics(metrics.recommendation);
  const testScore = evaluatePerformanceTests(metrics.performanceTests);

  // Calculer le score global
  const scores = [systemScore, apiScore, databaseScore, cacheScore, recommendationScore, testScore].filter(score => score !== null);
  const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;

  // Déterminer le statut
  if (averageScore >= 90) {
    return 'EXCELLENT';
  } else if (averageScore >= 75) {
    return 'GOOD';
  } else if (averageScore >= 60) {
    return 'AVERAGE';
  } else if (averageScore >= 40) {
    return 'POOR';
  } else {
    return 'CRITICAL';
  }
}

/**
 * Évalue les métriques système
 */
function evaluateSystemMetrics(systemMetrics) {
  if (!systemMetrics) return null;

  let score = 100;

  // Évaluer l'utilisation CPU
  if (systemMetrics.cpuUsage && typeof systemMetrics.cpuUsage.value === 'number') {
    const cpuUsage = systemMetrics.cpuUsage.value;
    if (cpuUsage > PERFORMANCE_THRESHOLDS.cpuUsage.poor) {
      score -= 30;
    } else if (cpuUsage > PERFORMANCE_THRESHOLDS.cpuUsage.average) {
      score -= 20;
    } else if (cpuUsage > PERFORMANCE_THRESHOLDS.cpuUsage.good) {
      score -= 10;
    }
  }

  // Évaluer l'utilisation mémoire
  if (systemMetrics.memoryUsage && typeof systemMetrics.memoryUsage.value === 'number') {
    const memoryUsage = systemMetrics.memoryUsage.value;
    if (memoryUsage > PERFORMANCE_THRESHOLDS.memoryUsage.poor) {
      score -= 30;
    } else if (memoryUsage > PERFORMANCE_THRESHOLDS.memoryUsage.average) {
      score -= 20;
    } else if (memoryUsage > PERFORMANCE_THRESHOLDS.memoryUsage.good) {
      score -= 10;
    }
  }

  return Math.max(0, score);
}

/**
 * Évalue les métriques API
 */
function evaluateApiMetrics(apiMetrics) {
  if (!apiMetrics) return null;

  let score = 100;

  // Évaluer le temps de réponse moyen
  if (apiMetrics.averageResponseTime && typeof apiMetrics.averageResponseTime.value === 'number') {
    const responseTime = apiMetrics.averageResponseTime.value;
    if (responseTime > PERFORMANCE_THRESHOLDS.responseTime.poor) {
      score -= 30;
    } else if (responseTime > PERFORMANCE_THRESHOLDS.responseTime.average) {
      score -= 20;
    } else if (responseTime > PERFORMANCE_THRESHOLDS.responseTime.good) {
      score -= 10;
    }
  }

  // Évaluer le taux d'erreur
  if (apiMetrics.errorRate && typeof apiMetrics.errorRate.value === 'number') {
    const errorRate = apiMetrics.errorRate.value;
    if (errorRate > PERFORMANCE_THRESHOLDS.errorRate.poor) {
      score -= 40;
    } else if (errorRate > PERFORMANCE_THRESHOLDS.errorRate.average) {
      score -= 25;
    } else if (errorRate > PERFORMANCE_THRESHOLDS.errorRate.good) {
      score -= 10;
    }
  }

  return Math.max(0, score);
}

/**
 * Évalue les métriques de base de données
 */
function evaluateDatabaseMetrics(databaseMetrics) {
  if (!databaseMetrics) return null;

  let score = 100;

  // Évaluer le temps de requête moyen
  if (databaseMetrics.averageQueryTime && typeof databaseMetrics.averageQueryTime.value === 'number') {
    const queryTime = databaseMetrics.averageQueryTime.value;
    if (queryTime > 100) {
      score -= 30;
    } else if (queryTime > 50) {
      score -= 20;
    } else if (queryTime > 20) {
      score -= 10;
    }
  }

  // Évaluer l'utilisation du pool de connexions
  if (databaseMetrics.connectionPoolUsage && typeof databaseMetrics.connectionPoolUsage.value === 'number') {
    const poolUsage = databaseMetrics.connectionPoolUsage.value;
    if (poolUsage > 90) {
      score -= 20;
    } else if (poolUsage > 80) {
      score -= 10;
    } else if (poolUsage > 70) {
      score -= 5;
    }
  }

  return Math.max(0, score);
}

/**
 * Évalue les métriques de cache
 */
function evaluateCacheMetrics(cacheMetrics) {
  if (!cacheMetrics) return null;

  let score = 100;

  // Évaluer le taux de succès du cache
  if (cacheMetrics.hitRate && typeof cacheMetrics.hitRate.value === 'number') {
    const hitRate = cacheMetrics.hitRate.value;
    if (hitRate < PERFORMANCE_THRESHOLDS.cacheHitRate.poor) {
      score -= 30;
    } else if (hitRate < PERFORMANCE_THRESHOLDS.cacheHitRate.average) {
      score -= 20;
    } else if (hitRate < PERFORMANCE_THRESHOLDS.cacheHitRate.good) {
      score -= 10;
    }
  }

  return Math.max(0, score);
}

/**
 * Évalue les métriques de recommandation
 */
function evaluateRecommendationMetrics(recommendationMetrics) {
  if (!recommendationMetrics) return null;

  let score = 100;

  // Évaluer la précision
  if (recommendationMetrics.accuracy && typeof recommendationMetrics.accuracy.value === 'number') {
    const accuracy = recommendationMetrics.accuracy.value;
    if (accuracy < 50) {
      score -= 30;
    } else if (accuracy < 70) {
      score -= 20;
    } else if (accuracy < 85) {
      score -= 10;
    }
  }

  // Évaluer la diversité
  if (recommendationMetrics.diversity && typeof recommendationMetrics.diversity.value === 'number') {
    const diversity = recommendationMetrics.diversity.value;
    if (diversity < 30) {
      score -= 20;
    } else if (diversity < 50) {
      score -= 10;
    } else if (diversity < 70) {
      score -= 5;
    }
  }

  // Évaluer le taux de clic
  if (recommendationMetrics.clickThroughRate && typeof recommendationMetrics.clickThroughRate.value === 'number') {
    const ctr = recommendationMetrics.clickThroughRate.value;
    if (ctr < 1) {
      score -= 30;
    } else if (ctr < 3) {
      score -= 20;
    } else if (ctr < 5) {
      score -= 10;
    }
  }

  return Math.max(0, score);
}

/**
 * Évalue les résultats des tests de performance
 */
function evaluatePerformanceTests(performanceTests) {
  if (!performanceTests || performanceTests.length === 0) return null;

  let score = 100;
  let totalTests = 0;
  let successfulTests = 0;
  let totalResponseTime = 0;

  // Analyser chaque test
  for (const test of performanceTests) {
    totalTests++;

    if (test.success) {
      successfulTests++;
      totalResponseTime += test.duration;

      // Pénaliser les temps de réponse lents
      if (test.duration > PERFORMANCE_THRESHOLDS.responseTime.poor) {
        score -= 2;
      } else if (test.duration > PERFORMANCE_THRESHOLDS.responseTime.average) {
        score -= 1;
      }
    } else {
      // Pénaliser les tests en échec
      score -= 5;
    }
  }

  // Pénaliser un taux de succès faible
  const successRate = (successfulTests / totalTests) * 100;
  if (successRate < 70) {
    score -= 30;
  } else if (successRate < 85) {
    score -= 20;
  } else if (successRate < 95) {
    score -= 10;
  }

  // Pénaliser un temps de réponse moyen élevé
  const averageResponseTime = totalResponseTime / successfulTests;
  if (averageResponseTime > PERFORMANCE_THRESHOLDS.responseTime.poor) {
    score -= 20;
  } else if (averageResponseTime > PERFORMANCE_THRESHOLDS.responseTime.average) {
    score -= 10;
  } else if (averageResponseTime > PERFORMANCE_THRESHOLDS.responseTime.good) {
    score -= 5;
  }

  return Math.max(0, score);
}

/**
 * Génère des recommandations basées sur les métriques
 */
function generateRecommendations(metrics, performanceStatus) {
  const recommendations = [];

  // Recommandations basées sur les métriques système
  if (metrics.system) {
    if (metrics.system.cpuUsage && metrics.system.cpuUsage.value > PERFORMANCE_THRESHOLDS.cpuUsage.average) {
      recommendations.push('Optimiser l\'utilisation du CPU ou augmenter les ressources CPU disponibles.');
    }

    if (metrics.system.memoryUsage && metrics.system.memoryUsage.value > PERFORMANCE_THRESHOLDS.memoryUsage.average) {
      recommendations.push('Optimiser l\'utilisation de la mémoire ou augmenter la mémoire disponible.');
    }
  }

  // Recommandations basées sur les métriques API
  if (metrics.api) {
    if (metrics.api.averageResponseTime && metrics.api.averageResponseTime.value > PERFORMANCE_THRESHOLDS.responseTime.average) {
      recommendations.push('Optimiser les temps de réponse de l\'API en améliorant les performances des endpoints les plus lents.');
    }

    if (metrics.api.errorRate && metrics.api.errorRate.value > PERFORMANCE_THRESHOLDS.errorRate.average) {
      recommendations.push('Réduire le taux d\'erreur de l\'API en corrigeant les endpoints problématiques.');
    }
  }

  // Recommandations basées sur les métriques de base de données
  if (metrics.database) {
    if (metrics.database.averageQueryTime && metrics.database.averageQueryTime.value > 50) {
      recommendations.push('Optimiser les requêtes de base de données en ajoutant des index ou en réécrivant les requêtes les plus lentes.');
    }

    if (metrics.database.connectionPoolUsage && metrics.database.connectionPoolUsage.value > 80) {
      recommendations.push('Augmenter la taille du pool de connexions à la base de données ou optimiser l\'utilisation des connexions.');
    }
  }

  // Recommandations basées sur les métriques de cache
  if (metrics.cache) {
    if (metrics.cache.hitRate && metrics.cache.hitRate.value < PERFORMANCE_THRESHOLDS.cacheHitRate.average) {
      recommendations.push('Améliorer l\'utilisation du cache en optimisant les stratégies de mise en cache.');
    }
  }

  // Recommandations basées sur les métriques de recommandation
  if (metrics.recommendation) {
    if (metrics.recommendation.accuracy && metrics.recommendation.accuracy.value < 70) {
      recommendations.push('Améliorer la précision des recommandations en ajustant les algorithmes ou en enrichissant les données d\'entrée.');
    }

    if (metrics.recommendation.diversity && metrics.recommendation.diversity.value < 50) {
      recommendations.push('Augmenter la diversité des recommandations en ajustant les algorithmes de diversification.');
    }

    if (metrics.recommendation.clickThroughRate && metrics.recommendation.clickThroughRate.value < 3) {
      recommendations.push('Améliorer le taux de clic des recommandations en optimisant leur pertinence et leur présentation.');
    }
  }

  // Recommandations basées sur les tests de performance
  if (metrics.performanceTests && metrics.performanceTests.length > 0) {
    const failedTests = metrics.performanceTests.filter(test => !test.success);
    if (failedTests.length > 0) {
      recommendations.push(`Corriger les ${failedTests.length} endpoints en échec pour améliorer la fiabilité du système.`);
    }

    const slowTests = metrics.performanceTests.filter(test => test.success && test.duration > PERFORMANCE_THRESHOLDS.responseTime.average);
    if (slowTests.length > 0) {
      recommendations.push(`Optimiser les ${slowTests.length} endpoints lents pour améliorer les temps de réponse.`);
    }
  }

  return recommendations;
}

/**
 * Identifie les tendances dans les métriques
 */
function identifyTrends(metrics) {
  // Cette fonction serait plus utile avec des données historiques
  // Pour l'instant, nous retournons un objet vide
  return {};
}

/**
 * Calcule des statistiques sur les métriques
 */
function calculateStatistics(metrics) {
  const statistics = {
    system: {},
    api: {},
    database: {},
    cache: {},
    recommendation: {},
    tests: {}
  };

  // Statistiques des tests de performance
  if (metrics.performanceTests && metrics.performanceTests.length > 0) {
    const successfulTests = metrics.performanceTests.filter(test => test.success);
    const durations = successfulTests.map(test => test.duration);

    statistics.tests = {
      totalTests: metrics.performanceTests.length,
      successfulTests: successfulTests.length,
      successRate: (successfulTests.length / metrics.performanceTests.length) * 100,
      averageResponseTime: durations.reduce((sum, duration) => sum + duration, 0) / durations.length || 0,
      minResponseTime: durations.length > 0 ? Math.min(...durations) : 0,
      maxResponseTime: durations.length > 0 ? Math.max(...durations) : 0,
      p95ResponseTime: calculatePercentile(durations, 95)
    };

    // Statistiques par stratégie
    const strategiesStats = {};
    for (const strategy of strategies) {
      const strategyTests = metrics.performanceTests.filter(test => test.strategy === strategy && test.success);
      if (strategyTests.length > 0) {
        const strategyDurations = strategyTests.map(test => test.duration);
        strategiesStats[strategy] = {
          count: strategyTests.length,
          averageResponseTime: strategyDurations.reduce((sum, duration) => sum + duration, 0) / strategyDurations.length,
          minResponseTime: Math.min(...strategyDurations),
          maxResponseTime: Math.max(...strategyDurations)
        };
      }
    }
    statistics.tests.strategies = strategiesStats;

    // Statistiques par type d'élément
    const itemTypesStats = {};
    for (const itemType of ITEM_TYPES) {
      const itemTypeTests = metrics.performanceTests.filter(test => test.itemType === itemType && test.success);
      if (itemTypeTests.length > 0) {
        const itemTypeDurations = itemTypeTests.map(test => test.duration);
        itemTypesStats[itemType] = {
          count: itemTypeTests.length,
          averageResponseTime: itemTypeDurations.reduce((sum, duration) => sum + duration, 0) / itemTypeDurations.length,
          minResponseTime: Math.min(...itemTypeDurations),
          maxResponseTime: Math.max(...itemTypeDurations)
        };
      }
    }
    statistics.tests.itemTypes = itemTypesStats;
  }

  return statistics;
}

/**
 * Calcule un percentile sur un tableau de valeurs
 */
function calculatePercentile(values, percentile) {
  if (!values || values.length === 0) return 0;

  // Trier les valeurs
  const sortedValues = [...values].sort((a, b) => a - b);

  // Calculer l'index
  const index = Math.ceil((percentile / 100) * sortedValues.length) - 1;

  // Retourner la valeur
  return sortedValues[index] || 0;
}

/**
 * Collecte les données de comparaison pour la période précédente
 */
async function collectComparisonData() {
  try {
    // Calculer la période précédente
    const currentStartDate = new Date();
    currentStartDate.setDate(currentStartDate.getDate() - parseInt(options.days));

    const previousEndDate = new Date(currentStartDate);
    previousEndDate.setDate(previousEndDate.getDate() - 1);

    const previousStartDate = new Date(previousEndDate);
    previousStartDate.setDate(previousStartDate.getDate() - parseInt(options.days));

    console.log(chalk.blue(`Période de comparaison: ${previousStartDate.toLocaleDateString()} - ${previousEndDate.toLocaleDateString()}`));

    // Préparer les paramètres
    const params = {
      startDate: previousStartDate.toISOString(),
      endDate: previousEndDate.toISOString(),
      detailed: 'true',
    };

    // Récupérer les données
    const responses = await Promise.all([
      api.get('/recommendation-reports/metrics', { params }),
      api.get('/performance/metrics/api', { params }),
      api.get('/performance/metrics/cache', { params }),
    ]);

    return {
      recommendation: responses[0].data,
      api: responses[1].data,
      cache: responses[2].data,
      period: {
        startDate: previousStartDate,
        endDate: previousEndDate,
      }
    };
  } catch (error) {
    console.warn(chalk.yellow(`Impossible de récupérer les données de comparaison: ${error.message}`));
    return {
      recommendation: {},
      api: {},
      cache: {},
      period: {
        startDate: new Date(Date.now() - (parseInt(options.days) * 2 * 24 * 60 * 60 * 1000)),
        endDate: new Date(Date.now() - (parseInt(options.days) * 24 * 60 * 60 * 1000)),
      }
    };
  }
}

/**
 * Collecte les benchmarks de l'industrie
 */
async function collectBenchmarkData() {
  try {
    const response = await api.get('/recommendation-reports/benchmarks');
    return response.data;
  } catch (error) {
    console.warn(chalk.yellow(`Impossible de récupérer les benchmarks: ${error.message}`));
    return {
      accuracy: {
        average: 75,
        top25Percent: 85,
        top10Percent: 92,
      },
      diversity: {
        average: 65,
        top25Percent: 75,
        top10Percent: 85,
      },
      responseTime: {
        average: 350,
        top25Percent: 200,
        top10Percent: 100,
      },
      ctr: {
        average: 2.5,
        top25Percent: 4.0,
        top10Percent: 6.0,
      },
      conversionRate: {
        average: 1.2,
        top25Percent: 2.5,
        top10Percent: 4.0,
      }
    };
  }
}

/**
 * Génère les graphiques pour le rapport
 */
async function generateCharts(metrics, analysis) {
  const charts = {};
  const currentTheme = chartThemes[theme] || chartThemes.light;

  // Configuration commune pour les graphiques
  const commonConfig = {
    plugins: {
      legend: {
        labels: {
          color: currentTheme.textColor,
          font: {
            size: 12
          }
        }
      },
      title: {
        display: true,
        color: currentTheme.textColor,
        font: {
          size: 16,
          weight: 'bold'
        }
      },
      datalabels: {
        color: currentTheme.textColor,
        font: {
          weight: 'bold'
        },
        formatter: (value) => {
          return value.toFixed(1);
        }
      }
    },
    scales: {
      x: {
        grid: {
          color: currentTheme.gridColor
        },
        ticks: {
          color: currentTheme.textColor
        }
      },
      y: {
        grid: {
          color: currentTheme.gridColor
        },
        ticks: {
          color: currentTheme.textColor
        }
      }
    }
  };

  // Graphique des métriques de recommandation
  if (metrics.recommendation) {
    const recommendationMetrics = [
      metrics.recommendation.accuracy?.value || 0,
      metrics.recommendation.diversity?.value || 0,
      metrics.recommendation.coverage?.value || 0,
      metrics.recommendation.clickThroughRate?.value || 0,
      metrics.recommendation.conversionRate?.value || 0
    ];

    const labels = [
      'Précision', 'Diversité', 'Couverture', 'Taux de clic', 'Taux de conversion'
    ];

    // Ajouter les données de comparaison si disponibles
    let comparisonData = null;
    if (metrics.comparison && metrics.comparison.recommendation) {
      comparisonData = [
        metrics.comparison.recommendation.accuracy?.value || 0,
        metrics.comparison.recommendation.diversity?.value || 0,
        metrics.comparison.recommendation.coverage?.value || 0,
        metrics.comparison.recommendation.clickThroughRate?.value || 0,
        metrics.comparison.recommendation.conversionRate?.value || 0
      ];
    }

    // Ajouter les benchmarks si disponibles
    let benchmarkData = null;
    if (metrics.benchmarks) {
      benchmarkData = [
        metrics.benchmarks.accuracy?.average || 0,
        metrics.benchmarks.diversity?.average || 0,
        metrics.benchmarks.coverage?.average || 0,
        metrics.benchmarks.ctr?.average || 0,
        metrics.benchmarks.conversionRate?.average || 0
      ];
    }

    const datasets = [{
      label: 'Période actuelle',
      data: recommendationMetrics,
      backgroundColor: currentTheme.colors[0] + '80', // Ajouter transparence
      borderColor: currentTheme.colors[0],
      borderWidth: 1
    }];

    if (comparisonData) {
      datasets.push({
        label: 'Période précédente',
        data: comparisonData,
        backgroundColor: currentTheme.colors[1] + '80',
        borderColor: currentTheme.colors[1],
        borderWidth: 1
      });
    }

    if (benchmarkData) {
      datasets.push({
        label: 'Moyenne de l\'industrie',
        data: benchmarkData,
        backgroundColor: currentTheme.colors[2] + '80',
        borderColor: currentTheme.colors[2],
        borderWidth: 1
      });
    }

    const config = {
      type: 'bar',
      data: {
        labels,
        datasets
      },
      options: {
        ...commonConfig,
        plugins: {
          ...commonConfig.plugins,
          title: {
            ...commonConfig.plugins.title,
            text: 'Métriques de Recommandation'
          }
        }
      }
    };

    const chartBuffer = await chartJSNodeCanvas.renderToBuffer(config);
    const chartPath = path.join(chartsDir, `recommendation-metrics-${SESSION_ID}.png`);
    fs.writeFileSync(chartPath, chartBuffer);
    charts.recommendationMetrics = chartPath;
  }

  // Graphique des temps de réponse par stratégie
  if (metrics.performanceTests && metrics.performanceTests.length > 0) {
    const strategiesData = {};

    // Regrouper les données par stratégie
    metrics.performanceTests.forEach(test => {
      if (test.success) {
        if (!strategiesData[test.strategy]) {
          strategiesData[test.strategy] = [];
        }
        strategiesData[test.strategy].push(test.duration);
      }
    });

    // Calculer les moyennes
    const labels = Object.keys(strategiesData);
    const data = labels.map(strategy => {
      const durations = strategiesData[strategy];
      return durations.reduce((sum, duration) => sum + duration, 0) / durations.length;
    });

    const config = {
      type: 'bar',
      data: {
        labels,
        datasets: [{
          label: 'Temps de réponse moyen (ms)',
          data,
          backgroundColor: labels.map((_, i) => currentTheme.colors[i % currentTheme.colors.length] + '80'),
          borderColor: labels.map((_, i) => currentTheme.colors[i % currentTheme.colors.length]),
          borderWidth: 1
        }]
      },
      options: {
        ...commonConfig,
        plugins: {
          ...commonConfig.plugins,
          title: {
            ...commonConfig.plugins.title,
            text: 'Temps de Réponse par Stratégie'
          }
        }
      }
    };

    const chartBuffer = await chartJSNodeCanvas.renderToBuffer(config);
    const chartPath = path.join(chartsDir, `response-time-by-strategy-${SESSION_ID}.png`);
    fs.writeFileSync(chartPath, chartBuffer);
    charts.responseTimeByStrategy = chartPath;
  }

  return charts;
}

/**
 * Exporte les métriques brutes dans un fichier JSON
 */
async function exportRawMetrics(metrics, outputPath) {
  const rawMetrics = {
    timestamp: new Date(),
    sessionId: SESSION_ID,
    metrics: {
      system: metrics.system,
      api: metrics.api,
      database: metrics.database,
      cache: metrics.cache,
      recommendation: metrics.recommendation,
      performanceTests: metrics.performanceTests
    }
  };

  if (metrics.comparison) {
    rawMetrics.comparison = metrics.comparison;
  }

  if (metrics.benchmarks) {
    rawMetrics.benchmarks = metrics.benchmarks;
  }

  fs.writeFileSync(outputPath, JSON.stringify(rawMetrics, null, 2));
  return outputPath;
}

/**
 * Génère le rapport dans le format demandé
 */
async function generateReport(metrics, analysis, charts) {
  switch (format) {
    case 'json':
      return generateJsonReport(metrics, analysis);
    case 'pdf':
      return generatePdfReport(metrics, analysis, charts);
    case 'html':
      return generateHtmlReport(metrics, analysis, charts);
    case 'interactive':
      return generateInteractiveReport(metrics, analysis);
    default:
      return generateJsonReport(metrics, analysis);
  }
}

/**
 * Génère un rapport au format JSON
 */
async function generateJsonReport(metrics, analysis) {
  const report = {
    timestamp: new Date(),
    metrics,
    analysis,
    options: {
      days: options.days,
      detailed: options.detailed,
      endpoints,
      strategies
    }
  };

  fs.writeFileSync(outputPath, JSON.stringify(report, null, 2));
  return outputPath;
}

/**
 * Génère un rapport au format PDF
 */
async function generatePdfReport(metrics, analysis) {
  return new Promise((resolve, reject) => {
    try {
      // Créer le document PDF
      const doc = new PDFDocument({ margin: 50 });

      // Créer le flux d'écriture
      const stream = fs.createWriteStream(outputPath);
      doc.pipe(stream);

      // Ajouter le titre
      doc.fontSize(25).text('Rapport de Performance du Système de Recommandation', { align: 'center' });
      doc.moveDown();

      // Ajouter la date
      doc.fontSize(14).text(`Date: ${new Date().toLocaleDateString()}`, { align: 'center' });
      doc.moveDown();

      // Ajouter le statut global
      doc.fontSize(16).text('Statut Global', { underline: true });
      doc.moveDown(0.5);
      doc.fontSize(14).text(`Statut: ${analysis.performanceStatus}`, { align: 'left' });
      doc.moveDown();

      // Ajouter les recommandations
      doc.fontSize(16).text('Recommandations', { underline: true });
      doc.moveDown(0.5);
      if (analysis.recommendations.length > 0) {
        analysis.recommendations.forEach((recommendation, index) => {
          doc.fontSize(12).text(`${index + 1}. ${recommendation}`);
          doc.moveDown(0.5);
        });
      } else {
        doc.fontSize(12).text('Aucune recommandation.');
      }
      doc.moveDown();

      // Ajouter les statistiques des tests
      if (analysis.statistics.tests) {
        doc.fontSize(16).text('Statistiques des Tests', { underline: true });
        doc.moveDown(0.5);
        doc.fontSize(12).text(`Nombre total de tests: ${analysis.statistics.tests.totalTests}`);
        doc.fontSize(12).text(`Taux de succès: ${analysis.statistics.tests.successRate.toFixed(2)}%`);
        doc.fontSize(12).text(`Temps de réponse moyen: ${analysis.statistics.tests.averageResponseTime.toFixed(2)} ms`);
        doc.fontSize(12).text(`Temps de réponse minimum: ${analysis.statistics.tests.minResponseTime.toFixed(2)} ms`);
        doc.fontSize(12).text(`Temps de réponse maximum: ${analysis.statistics.tests.maxResponseTime.toFixed(2)} ms`);
        doc.fontSize(12).text(`Temps de réponse P95: ${analysis.statistics.tests.p95ResponseTime.toFixed(2)} ms`);
        doc.moveDown();
      }

      // Finaliser le document
      doc.end();

      // Attendre la fin de l'écriture
      stream.on('finish', () => {
        resolve(outputPath);
      });

      stream.on('error', (error) => {
        reject(error);
      });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Génère un rapport au format HTML
 */
async function generateHtmlReport(metrics, analysis, charts) {
  // Déterminer le thème
  const currentTheme = theme || 'light';

  // Styles CSS basés sur le thème
  const themeStyles = {
    light: {
      backgroundColor: 'white',
      textColor: '#333333',
      headerColor: '#2c3e50',
      sectionBgColor: '#f8f9fa',
      borderColor: '#ddd',
      tableHeaderBg: '#f2f2f2',
      tableRowHoverBg: '#f5f5f5',
      linkColor: '#3498db',
      statusColors: {
        excellent: '#2ecc71',
        good: '#3498db',
        average: '#f39c12',
        poor: '#e74c3c',
        critical: '#c0392b'
      }
    },
    dark: {
      backgroundColor: '#1a1a1a',
      textColor: '#ecf0f1',
      headerColor: '#3498db',
      sectionBgColor: '#2c3e50',
      borderColor: '#34495e',
      tableHeaderBg: '#2c3e50',
      tableRowHoverBg: '#34495e',
      linkColor: '#3498db',
      statusColors: {
        excellent: '#2ecc71',
        good: '#3498db',
        average: '#f39c12',
        poor: '#e74c3c',
        critical: '#c0392b'
      }
    },
    branded: {
      backgroundColor: '#ecf0f1',
      textColor: '#2c3e50',
      headerColor: '#8e44ad',
      sectionBgColor: '#f5f5f5',
      borderColor: '#bdc3c7',
      tableHeaderBg: '#8e44ad',
      tableRowHoverBg: '#f5f5f5',
      linkColor: '#8e44ad',
      statusColors: {
        excellent: '#27ae60',
        good: '#2980b9',
        average: '#f39c12',
        poor: '#c0392b',
        critical: '#c0392b'
      }
    }
  };

  const style = themeStyles[currentTheme] || themeStyles.light;

  // Créer le contenu HTML
  let html = `
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Rapport de Performance du Système de Recommandation</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      color: ${style.textColor};
      background-color: ${style.backgroundColor};
    }
    h1, h2, h3 {
      color: ${style.headerColor};
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    .status {
      padding: 10px;
      border-radius: 5px;
      display: inline-block;
      margin-bottom: 20px;
      font-weight: bold;
    }
    .status.excellent {
      background-color: ${style.statusColors.excellent};
      color: white;
    }
    .status.good {
      background-color: ${style.statusColors.good};
      color: white;
    }
    .status.average {
      background-color: ${style.statusColors.average};
      color: white;
    }
    .status.poor {
      background-color: ${style.statusColors.poor};
      color: white;
    }
    .status.critical {
      background-color: ${style.statusColors.critical};
      color: white;
    }
    .recommendations {
      background-color: ${style.sectionBgColor};
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .recommendation {
      margin-bottom: 10px;
      padding-left: 20px;
      position: relative;
    }
    .recommendation:before {
      content: "•";
      position: absolute;
      left: 0;
      color: ${style.linkColor};
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid ${style.borderColor};
    }
    th {
      background-color: ${style.tableHeaderBg};
      color: ${currentTheme === 'dark' || currentTheme === 'branded' ? 'white' : style.textColor};
    }
    tr:hover {
      background-color: ${style.tableRowHoverBg};
    }
    .chart-container {
      margin: 20px 0;
      text-align: center;
    }
    .chart-container img {
      max-width: 100%;
      height: auto;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .comparison {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .comparison-column {
      flex: 1;
      padding: 15px;
      background-color: ${style.sectionBgColor};
      border-radius: 5px;
      margin: 0 10px;
    }
    .comparison-column:first-child {
      margin-left: 0;
    }
    .comparison-column:last-child {
      margin-right: 0;
    }
    .metric-change {
      font-weight: bold;
    }
    .metric-change.positive {
      color: ${style.statusColors.excellent};
    }
    .metric-change.negative {
      color: ${style.statusColors.poor};
    }
    .metric-change.neutral {
      color: ${style.statusColors.average};
    }
    footer {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid ${style.borderColor};
      text-align: center;
      font-size: 0.9em;
      color: ${style.textColor};
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Rapport de Performance du Système de Recommandation</h1>
    <p>Date: ${new Date().toLocaleDateString()}</p>
    <p>Période d'analyse: ${options.days} jours (${format(new Date(Date.now() - (options.days * 24 * 60 * 60 * 1000)), 'dd/MM/yyyy')} - ${format(new Date(), 'dd/MM/yyyy')})</p>

    <h2>Statut Global</h2>
    <div class="status ${analysis.performanceStatus.toLowerCase()}">${analysis.performanceStatus}</div>

    <h2>Recommandations</h2>
    <div class="recommendations">
      ${analysis.recommendations.length > 0
        ? analysis.recommendations.map(rec => `<div class="recommendation">${rec}</div>`).join('')
        : '<p>Aucune recommandation.</p>'}
    </div>

    ${charts && charts.recommendationMetrics ? `
    <h2>Métriques de Recommandation</h2>
    <div class="chart-container">
      <img src="${charts.recommendationMetrics}" alt="Graphique des métriques de recommandation">
    </div>
    ` : ''}

    ${charts && charts.responseTimeByStrategy ? `
    <h2>Temps de Réponse par Stratégie</h2>
    <div class="chart-container">
      <img src="${charts.responseTimeByStrategy}" alt="Graphique des temps de réponse par stratégie">
    </div>
    ` : ''}

    ${metrics.comparison ? `
    <h2>Comparaison avec la Période Précédente</h2>
    <div class="comparison">
      <div class="comparison-column">
        <h3>Période Actuelle</h3>
        <p>${format(new Date(Date.now() - (options.days * 24 * 60 * 60 * 1000)), 'dd/MM/yyyy')} - ${format(new Date(), 'dd/MM/yyyy')}</p>
        <table>
          <tr>
            <th>Métrique</th>
            <th>Valeur</th>
          </tr>
          <tr>
            <td>Précision</td>
            <td>${metrics.recommendation?.accuracy?.value?.toFixed(2) || 'N/A'}%</td>
          </tr>
          <tr>
            <td>Taux de clic</td>
            <td>${metrics.recommendation?.clickThroughRate?.value?.toFixed(2) || 'N/A'}%</td>
          </tr>
          <tr>
            <td>Temps de réponse moyen</td>
            <td>${analysis.statistics.tests?.averageResponseTime?.toFixed(2) || 'N/A'} ms</td>
          </tr>
        </table>
      </div>
      <div class="comparison-column">
        <h3>Période Précédente</h3>
        <p>${format(metrics.comparison.period.startDate, 'dd/MM/yyyy')} - ${format(metrics.comparison.period.endDate, 'dd/MM/yyyy')}</p>
        <table>
          <tr>
            <th>Métrique</th>
            <th>Valeur</th>
            <th>Évolution</th>
          </tr>
          <tr>
            <td>Précision</td>
            <td>${metrics.comparison.recommendation?.accuracy?.value?.toFixed(2) || 'N/A'}%</td>
            <td class="metric-change ${getChangeClass(metrics.recommendation?.accuracy?.value, metrics.comparison.recommendation?.accuracy?.value)}">
              ${getChangeText(metrics.recommendation?.accuracy?.value, metrics.comparison.recommendation?.accuracy?.value)}
            </td>
          </tr>
          <tr>
            <td>Taux de clic</td>
            <td>${metrics.comparison.recommendation?.clickThroughRate?.value?.toFixed(2) || 'N/A'}%</td>
            <td class="metric-change ${getChangeClass(metrics.recommendation?.clickThroughRate?.value, metrics.comparison.recommendation?.clickThroughRate?.value)}">
              ${getChangeText(metrics.recommendation?.clickThroughRate?.value, metrics.comparison.recommendation?.clickThroughRate?.value)}
            </td>
          </tr>
        </table>
      </div>
    </div>
    ` : ''}

    <h2>Statistiques des Tests</h2>
    ${analysis.statistics.tests ? `
    <table>
      <tr>
        <th>Métrique</th>
        <th>Valeur</th>
      </tr>
      <tr>
        <td>Nombre total de tests</td>
        <td>${analysis.statistics.tests.totalTests}</td>
      </tr>
      <tr>
        <td>Taux de succès</td>
        <td>${analysis.statistics.tests.successRate.toFixed(2)}%</td>
      </tr>
      <tr>
        <td>Temps de réponse moyen</td>
        <td>${analysis.statistics.tests.averageResponseTime.toFixed(2)} ms</td>
      </tr>
      <tr>
        <td>Temps de réponse minimum</td>
        <td>${analysis.statistics.tests.minResponseTime.toFixed(2)} ms</td>
      </tr>
      <tr>
        <td>Temps de réponse maximum</td>
        <td>${analysis.statistics.tests.maxResponseTime.toFixed(2)} ms</td>
      </tr>
      <tr>
        <td>Temps de réponse P95</td>
        <td>${analysis.statistics.tests.p95ResponseTime.toFixed(2)} ms</td>
      </tr>
    </table>
    ` : '<p>Aucune statistique disponible.</p>'}

    <footer>
      <p>Rapport généré le ${new Date().toLocaleString()} | ID de session: ${SESSION_ID}</p>
    </footer>
  </div>

  <script>
    function getChangeClass(current, previous) {
      if (!current || !previous) return 'neutral';
      const diff = current - previous;
      if (Math.abs(diff) < 0.1) return 'neutral';
      return diff > 0 ? 'positive' : 'negative';
    }

    function getChangeText(current, previous) {
      if (!current || !previous) return 'N/A';
      const diff = current - previous;
      if (Math.abs(diff) < 0.1) return '≈ 0%';
      const percent = ((diff / previous) * 100).toFixed(1);
      return diff > 0 ? `+${percent}%` : `${percent}%`;
    }
  </script>
</body>
</html>
  `;

  // Écrire le fichier HTML
  fs.writeFileSync(outputPath, html);
  return outputPath;
}

/**
 * Génère un rapport interactif au format HTML
 */
async function generateInteractiveReport(metrics, analysis) {
  // Créer le contenu HTML avec des graphiques interactifs
  let html = `
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Rapport Interactif - Système de Recommandation</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    :root {
      --primary-color: ${theme === 'branded' ? '#8e44ad' : theme === 'dark' ? '#3498db' : '#2c3e50'};
      --bg-color: ${theme === 'dark' ? '#1a1a1a' : '#ffffff'};
      --text-color: ${theme === 'dark' ? '#ecf0f1' : '#333333'};
      --section-bg: ${theme === 'dark' ? '#2c3e50' : theme === 'branded' ? '#f5f5f5' : '#f8f9fa'};
      --border-color: ${theme === 'dark' ? '#34495e' : theme === 'branded' ? '#bdc3c7' : '#dee2e6'};
    }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: var(--bg-color);
      color: var(--text-color);
      padding-top: 20px;
    }
    .navbar {
      background-color: var(--primary-color) !important;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .navbar-brand, .nav-link {
      color: white !important;
    }
    .card {
      background-color: var(--section-bg);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      margin-bottom: 20px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }
    .card-header {
      background-color: var(--primary-color);
      color: white;
      font-weight: bold;
      border-radius: 7px 7px 0 0 !important;
    }
    .status-badge {
      padding: 8px 15px;
      border-radius: 20px;
      font-weight: bold;
      display: inline-block;
      margin-bottom: 10px;
    }
    .chart-container {
      position: relative;
      height: 300px;
      margin-bottom: 20px;
    }
    .recommendation-item {
      padding: 10px 15px;
      border-left: 4px solid var(--primary-color);
      background-color: rgba(0,0,0,0.03);
      margin-bottom: 10px;
      border-radius: 0 4px 4px 0;
    }
    .metric-change.positive {
      color: #2ecc71;
    }
    .metric-change.negative {
      color: #e74c3c;
    }
    .table {
      color: var(--text-color);
    }
    .table thead th {
      background-color: var(--primary-color);
      color: white;
      border-color: var(--border-color);
    }
    .table-striped tbody tr:nth-of-type(odd) {
      background-color: rgba(0,0,0,0.02);
    }
    footer {
      margin-top: 40px;
      padding: 20px 0;
      text-align: center;
      border-top: 1px solid var(--border-color);
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
    <div class="container">
      <a class="navbar-brand" href="#">Système de Recommandation</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav ms-auto">
          <li class="nav-item">
            <a class="nav-link" href="#overview">Vue d'ensemble</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#metrics">Métriques</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#tests">Tests</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#recommendations">Recommandations</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <div class="container">
    <div class="row mb-4">
      <div class="col-md-12 text-center">
        <h1>Rapport de Performance du Système de Recommandation</h1>
        <p class="lead">Analyse détaillée des performances et recommandations d'optimisation</p>
        <p>Période: ${format(new Date(Date.now() - (options.days * 24 * 60 * 60 * 1000)), 'dd/MM/yyyy')} - ${format(new Date(), 'dd/MM/yyyy')}</p>
      </div>
    </div>

    <!-- Reste du contenu interactif -->
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Données pour les graphiques
    const metricsData = ${JSON.stringify({
      recommendation: metrics.recommendation,
      comparison: metrics.comparison?.recommendation,
      benchmarks: metrics.benchmarks,
      tests: analysis.statistics.tests
    })};

    // Code pour initialiser les graphiques interactifs
    document.addEventListener('DOMContentLoaded', function() {
      // Initialisation des graphiques Chart.js
      // ...
    });
  </script>
</body>
</html>
  `;

  // Écrire le fichier HTML
  fs.writeFileSync(outputPath, html);
  return outputPath;
}

/**
 * Détermine la classe CSS pour l'évolution d'une métrique
 */
function getChangeClass(current, previous) {
  if (!current || !previous) return 'neutral';
  const diff = current - previous;
  if (Math.abs(diff) < 0.1) return 'neutral';
  return diff > 0 ? 'positive' : 'negative';
}

/**
 * Génère le texte d'évolution d'une métrique
 */
function getChangeText(current, previous) {
  if (!current || !previous) return 'N/A';
  const diff = current - previous;
  if (Math.abs(diff) < 0.1) return '≈ 0%';
  const percent = ((diff / previous) * 100).toFixed(1);
  return diff > 0 ? `+${percent}%` : `${percent}%`;
}

/**
 * Affiche un résumé du rapport dans la console
 */
function displaySummary(metrics, analysis) {
  console.log('');
  console.log(chalk.blue.bold('=== Résumé du Rapport ==='));

  // Afficher le statut global
  let statusColor;
  switch (analysis.performanceStatus) {
    case 'EXCELLENT':
      statusColor = chalk.green;
      break;
    case 'GOOD':
      statusColor = chalk.cyan;
      break;
    case 'AVERAGE':
      statusColor = chalk.yellow;
      break;
    case 'POOR':
      statusColor = chalk.red;
      break;
    case 'CRITICAL':
      statusColor = chalk.bgRed.white;
      break;
    default:
      statusColor = chalk.white;
  }

  console.log(chalk.blue('Statut Global:'), statusColor(analysis.performanceStatus));
  console.log('');

  // Afficher les recommandations
  if (analysis.recommendations.length > 0) {
    console.log(chalk.blue('Recommandations:'));
    analysis.recommendations.forEach((recommendation, index) => {
      console.log(`  ${index + 1}. ${recommendation}`);
    });
  } else {
    console.log(chalk.blue('Recommandations:'), 'Aucune recommandation.');
  }
  console.log('');

  // Afficher les statistiques des tests
  if (analysis.statistics.tests) {
    console.log(chalk.blue('Statistiques des Tests:'));

    const table = new Table({
      head: ['Métrique', 'Valeur'],
      colWidths: [30, 20]
    });

    table.push(
      ['Nombre total de tests', analysis.statistics.tests.totalTests],
      ['Taux de succès', `${analysis.statistics.tests.successRate.toFixed(2)}%`],
      ['Temps de réponse moyen', `${analysis.statistics.tests.averageResponseTime.toFixed(2)} ms`],
      ['Temps de réponse minimum', `${analysis.statistics.tests.minResponseTime.toFixed(2)} ms`],
      ['Temps de réponse maximum', `${analysis.statistics.tests.maxResponseTime.toFixed(2)} ms`],
      ['Temps de réponse P95', `${analysis.statistics.tests.p95ResponseTime.toFixed(2)} ms`]
    );

    console.log(table.toString());
  }

  console.log('');
  console.log(chalk.blue(`Rapport complet disponible à: ${outputPath}`));
}

/**
 * Détermine le statut CPU en fonction de l'utilisation
 */
function getCpuStatus(cpuUsage) {
  if (cpuUsage > PERFORMANCE_THRESHOLDS.cpuUsage.poor) {
    return 'CRITICAL';
  } else if (cpuUsage > PERFORMANCE_THRESHOLDS.cpuUsage.average) {
    return 'WARNING';
  } else if (cpuUsage > PERFORMANCE_THRESHOLDS.cpuUsage.good) {
    return 'ATTENTION';
  } else {
    return 'HEALTHY';
  }
}

/**
 * Détermine le statut mémoire en fonction de l'utilisation
 */
function getMemoryStatus(memoryUsage) {
  if (memoryUsage > PERFORMANCE_THRESHOLDS.memoryUsage.poor) {
    return 'CRITICAL';
  } else if (memoryUsage > PERFORMANCE_THRESHOLDS.memoryUsage.average) {
    return 'WARNING';
  } else if (memoryUsage > PERFORMANCE_THRESHOLDS.memoryUsage.good) {
    return 'ATTENTION';
  } else {
    return 'HEALTHY';
  }
}

// Exécuter la fonction principale
main().catch(error => {
  console.error(chalk.red(`Erreur fatale: ${error.message}`));
  process.exit(1);
});