#!/bin/bash

# Exit on error
set -e

# Default values
NAMESPACE="retreat-and-be"
IMAGE_TAG="latest"
DOCKER_REGISTRY="docker.io"
ENVIRONMENT="dev"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --namespace)
      NAMESPACE="$2"
      shift 2
      ;;
    --tag)
      IMAGE_TAG="$2"
      shift 2
      ;;
    --registry)
      DOCKER_REGISTRY="$2"
      shift 2
      ;;
    --env)
      ENVIRONMENT="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
  echo "Invalid environment: $ENVIRONMENT. Must be one of: dev, staging, prod"
  exit 1
fi

# Set environment-specific variables
case $ENVIRONMENT in
  dev)
    REPLICAS=1
    ;;
  staging)
    REPLICAS=2
    ;;
  prod)
    REPLICAS=3
    ;;
esac

# Print deployment information
echo "Deploying Backend-NestJS to Kubernetes"
echo "Namespace: $NAMESPACE"
echo "Environment: $ENVIRONMENT"
echo "Image: $DOCKER_REGISTRY/retreat-and-be/backend:$IMAGE_TAG"
echo "Replicas: $REPLICAS"
echo

# Create namespace if it doesn't exist
kubectl get namespace $NAMESPACE > /dev/null 2>&1 || kubectl create namespace $NAMESPACE

# Apply ConfigMap
echo "Applying ConfigMap..."
envsubst < configmap.yaml | kubectl apply -n $NAMESPACE -f -

# Apply Secret
echo "Applying Secret..."
envsubst < secret.yaml | kubectl apply -n $NAMESPACE -f -

# Apply Deployment
echo "Applying Deployment..."
sed -e "s|\${DOCKER_REGISTRY}|$DOCKER_REGISTRY|g" \
    -e "s|\${IMAGE_TAG}|$IMAGE_TAG|g" \
    -e "s|\${REPLICAS}|$REPLICAS|g" \
    deployment.yaml | kubectl apply -n $NAMESPACE -f -

# Apply Service
echo "Applying Service..."
kubectl apply -n $NAMESPACE -f service.yaml

echo
echo "Deployment completed successfully!"
echo "To check the status of the deployment, run:"
echo "kubectl -n $NAMESPACE get pods"
echo
echo "To view the logs, run:"
echo "kubectl -n $NAMESPACE logs -l app=backend -f"
