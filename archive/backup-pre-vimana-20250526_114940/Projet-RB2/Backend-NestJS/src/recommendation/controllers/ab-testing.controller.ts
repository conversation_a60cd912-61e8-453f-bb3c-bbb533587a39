import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../user/enums/user-role.enum';
import { ABTestingService } from '../services/ab-testing.service';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';

class CreateABTestDto {
  name: string;
  description: string;
  strategies: RecommendationStrategy[];
  weights?: number[];
  startDate?: Date;
  endDate?: Date;
}

class UpdateABTestDto {
  name?: string;
  description?: string;
  strategies?: RecommendationStrategy[];
  weights?: number[];
  startDate?: Date;
  endDate?: Date;
  isActive?: boolean;
}

class TrackEventDto {
  userId: string;
  testId: string;
  strategy: RecommendationStrategy;
  recommendationId: string;
  amount?: number;
}

@ApiTags('ab-testing')
@Controller('ab-testing')
export class ABTestingController {
  private readonly logger = new Logger(ABTestingController.name);

  constructor(private readonly abTestingService: ABTestingService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Créer un nouveau test A/B' })
  @ApiResponse({ status: 201, description: 'Test A/B créé avec succès' })
  @ApiResponse({ status: 400, description: 'Données invalides' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async createTest(@Body() createABTestDto: CreateABTestDto) {
    try {
      const test = await this.abTestingService.createTest(
        createABTestDto.name,
        createABTestDto.description,
        createABTestDto.strategies,
        createABTestDto.weights,
        createABTestDto.startDate,
        createABTestDto.endDate,
      );
      return test;
    } catch (error) {
      this.logger.error(`Erreur lors de la création du test A/B: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de la création du test A/B',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer tous les tests A/B actifs' })
  @ApiResponse({ status: 200, description: 'Liste des tests A/B actifs' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async getActiveTests() {
    try {
      return await this.abTestingService.getActiveTests();
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des tests A/B actifs: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de la récupération des tests A/B actifs',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer un test A/B par son ID' })
  @ApiResponse({ status: 200, description: 'Test A/B trouvé' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  @ApiResponse({ status: 404, description: 'Test A/B non trouvé' })
  async getTestById(@Param('id') id: string) {
    try {
      const test = await this.abTestingService.getTestById(id);
      if (!test) {
        throw new HttpException('Test A/B non trouvé', HttpStatus.NOT_FOUND);
      }
      return test;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération du test A/B: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de la récupération du test A/B',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour un test A/B' })
  @ApiResponse({ status: 200, description: 'Test A/B mis à jour avec succès' })
  @ApiResponse({ status: 400, description: 'Données invalides' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  @ApiResponse({ status: 404, description: 'Test A/B non trouvé' })
  async updateTest(@Param('id') id: string, @Body() updateABTestDto: UpdateABTestDto) {
    try {
      const test = await this.abTestingService.updateTest(id, updateABTestDto);
      if (!test) {
        throw new HttpException('Test A/B non trouvé', HttpStatus.NOT_FOUND);
      }
      return test;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du test A/B: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de la mise à jour du test A/B',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Terminer un test A/B' })
  @ApiResponse({ status: 200, description: 'Test A/B terminé avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  @ApiResponse({ status: 404, description: 'Test A/B non trouvé' })
  async endTest(@Param('id') id: string) {
    try {
      const test = await this.abTestingService.endTest(id);
      if (!test) {
        throw new HttpException('Test A/B non trouvé', HttpStatus.NOT_FOUND);
      }
      return test;
    } catch (error) {
      this.logger.error(`Erreur lors de la fin du test A/B: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de la fin du test A/B',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id/metrics')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les métriques d\'un test A/B' })
  @ApiResponse({ status: 200, description: 'Métriques du test A/B' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  @ApiResponse({ status: 404, description: 'Test A/B non trouvé' })
  async getTestMetrics(@Param('id') id: string) {
    try {
      const test = await this.abTestingService.getTestById(id);
      if (!test) {
        throw new HttpException('Test A/B non trouvé', HttpStatus.NOT_FOUND);
      }
      
      const metrics = await this.abTestingService.getTestMetrics(id);
      const conversionRates = await this.abTestingService.getConversionRates(id);
      
      return {
        test,
        metrics,
        conversionRates,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des métriques: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de la récupération des métriques',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id/winner')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Déterminer la stratégie gagnante d\'un test A/B' })
  @ApiResponse({ status: 200, description: 'Stratégie gagnante' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  @ApiResponse({ status: 404, description: 'Test A/B non trouvé' })
  async determineWinner(@Param('id') id: string) {
    try {
      const test = await this.abTestingService.getTestById(id);
      if (!test) {
        throw new HttpException('Test A/B non trouvé', HttpStatus.NOT_FOUND);
      }
      
      const winner = await this.abTestingService.determineWinner(id);
      if (!winner) {
        return { message: 'Pas assez de données pour déterminer un gagnant' };
      }
      
      return { winner };
    } catch (error) {
      this.logger.error(`Erreur lors de la détermination du gagnant: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de la détermination du gagnant',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('assign')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Assigner une stratégie à un utilisateur pour un test A/B' })
  @ApiResponse({ status: 200, description: 'Stratégie assignée' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 404, description: 'Test A/B non trouvé' })
  async assignStrategy(@Body() { userId, testId }: { userId: string; testId: string }) {
    try {
      const test = await this.abTestingService.getTestById(testId);
      if (!test) {
        throw new HttpException('Test A/B non trouvé', HttpStatus.NOT_FOUND);
      }
      
      const strategy = await this.abTestingService.assignStrategy(userId, testId);
      return { strategy };
    } catch (error) {
      this.logger.error(`Erreur lors de l'assignation de stratégie: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de l\'assignation de stratégie',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('track/impression')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Enregistrer une impression pour une recommandation' })
  @ApiResponse({ status: 200, description: 'Impression enregistrée' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async trackImpression(@Body() trackEventDto: TrackEventDto) {
    try {
      await this.abTestingService.trackImpression(
        trackEventDto.userId,
        trackEventDto.testId,
        trackEventDto.strategy,
        trackEventDto.recommendationId,
      );
      return { success: true };
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de l'impression: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de l\'enregistrement de l\'impression',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('track/click')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Enregistrer un clic pour une recommandation' })
  @ApiResponse({ status: 200, description: 'Clic enregistré' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async trackClick(@Body() trackEventDto: TrackEventDto) {
    try {
      await this.abTestingService.trackClick(
        trackEventDto.userId,
        trackEventDto.testId,
        trackEventDto.strategy,
        trackEventDto.recommendationId,
      );
      return { success: true };
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement du clic: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de l\'enregistrement du clic',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('track/conversion')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Enregistrer une conversion pour une recommandation' })
  @ApiResponse({ status: 200, description: 'Conversion enregistrée' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async trackConversion(@Body() trackEventDto: TrackEventDto) {
    try {
      await this.abTestingService.trackConversion(
        trackEventDto.userId,
        trackEventDto.testId,
        trackEventDto.strategy,
        trackEventDto.recommendationId,
        trackEventDto.amount,
      );
      return { success: true };
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de la conversion: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de l\'enregistrement de la conversion',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
