import {
  <PERSON>,
  Get,
  Query,
  Param,
  UseGuards,
  HttpException,
  HttpStatus,
  Logger,
  Res,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../user/enums/user-role.enum';
import { RecommendationAnalyticsService } from '../services/recommendation-analytics.service';
import { Response } from 'express';

@ApiTags('recommendation-analytics')
@Controller('recommendation/analytics')
export class RecommendationAnalyticsController {
  private readonly logger = new Logger(RecommendationAnalyticsController.name);

  constructor(private readonly analyticsService: RecommendationAnalyticsService) {}

  @Get('metrics')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les métriques globales des recommandations' })
  @ApiResponse({ status: 200, description: 'Métriques récupérées avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiQuery({ name: 'userId', required: false, description: 'ID de l\'utilisateur (admin uniquement)' })
  @ApiQuery({ name: 'startDate', required: true, description: 'Date de début (ISO)' })
  @ApiQuery({ name: 'endDate', required: true, description: 'Date de fin (ISO)' })
  @ApiQuery({ name: 'strategy', required: false, description: 'Stratégie de recommandation' })
  @ApiQuery({ name: 'category', required: false, description: 'Catégorie' })
  @ApiQuery({ name: 'segment', required: false, description: 'Segment d\'utilisateurs (admin uniquement)' })
  async getRecommendationMetrics(
    @Request() req,
    @Query('userId') userId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('strategy') strategy?: string,
    @Query('category') category?: string,
    @Query('segment') segment?: string,
  ) {
    try {
      // Vérifier si l'utilisateur a le droit d'accéder aux données d'un autre utilisateur
      if (userId && userId !== req.user.id && !req.user.roles.includes(UserRole.ADMIN)) {
        throw new HttpException('Vous n\'êtes pas autorisé à accéder à ces données', HttpStatus.FORBIDDEN);
      }
      
      // Vérifier si l'utilisateur a le droit d'accéder aux données de segment
      if (segment && !req.user.roles.includes(UserRole.ADMIN)) {
        throw new HttpException('Vous n\'êtes pas autorisé à accéder aux données de segment', HttpStatus.FORBIDDEN);
      }
      
      // Si aucun userId n'est spécifié et que l'utilisateur n'est pas admin, utiliser l'ID de l'utilisateur actuel
      const targetUserId = userId || (!req.user.roles.includes(UserRole.ADMIN) ? req.user.id : undefined);
      
      return await this.analyticsService.getRecommendationMetrics({
        userId: targetUserId,
        startDate,
        endDate,
        strategy,
        category,
        segment,
      });
    } catch (error) {
      this.logger.error(`Error getting recommendation metrics: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting recommendation metrics',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('strategy-performance')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les performances par stratégie de recommandation' })
  @ApiResponse({ status: 200, description: 'Performances récupérées avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async getStrategyPerformance(
    @Request() req,
    @Query('userId') userId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('category') category?: string,
    @Query('segment') segment?: string,
  ) {
    try {
      // Vérifier si l'utilisateur a le droit d'accéder aux données d'un autre utilisateur
      if (userId && userId !== req.user.id && !req.user.roles.includes(UserRole.ADMIN)) {
        throw new HttpException('Vous n\'êtes pas autorisé à accéder à ces données', HttpStatus.FORBIDDEN);
      }
      
      // Vérifier si l'utilisateur a le droit d'accéder aux données de segment
      if (segment && !req.user.roles.includes(UserRole.ADMIN)) {
        throw new HttpException('Vous n\'êtes pas autorisé à accéder aux données de segment', HttpStatus.FORBIDDEN);
      }
      
      // Si aucun userId n'est spécifié et que l'utilisateur n'est pas admin, utiliser l'ID de l'utilisateur actuel
      const targetUserId = userId || (!req.user.roles.includes(UserRole.ADMIN) ? req.user.id : undefined);
      
      return await this.analyticsService.getStrategyPerformance({
        userId: targetUserId,
        startDate,
        endDate,
        category,
        segment,
      });
    } catch (error) {
      this.logger.error(`Error getting strategy performance: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting strategy performance',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('category-performance')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les performances par catégorie' })
  @ApiResponse({ status: 200, description: 'Performances récupérées avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async getCategoryPerformance(
    @Request() req,
    @Query('userId') userId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('strategy') strategy?: string,
    @Query('segment') segment?: string,
  ) {
    try {
      // Vérifier si l'utilisateur a le droit d'accéder aux données d'un autre utilisateur
      if (userId && userId !== req.user.id && !req.user.roles.includes(UserRole.ADMIN)) {
        throw new HttpException('Vous n\'êtes pas autorisé à accéder à ces données', HttpStatus.FORBIDDEN);
      }
      
      // Vérifier si l'utilisateur a le droit d'accéder aux données de segment
      if (segment && !req.user.roles.includes(UserRole.ADMIN)) {
        throw new HttpException('Vous n\'êtes pas autorisé à accéder aux données de segment', HttpStatus.FORBIDDEN);
      }
      
      // Si aucun userId n'est spécifié et que l'utilisateur n'est pas admin, utiliser l'ID de l'utilisateur actuel
      const targetUserId = userId || (!req.user.roles.includes(UserRole.ADMIN) ? req.user.id : undefined);
      
      return await this.analyticsService.getCategoryPerformance({
        userId: targetUserId,
        startDate,
        endDate,
        strategy,
        segment,
      });
    } catch (error) {
      this.logger.error(`Error getting category performance: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting category performance',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('segment-performance')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les performances par segment d\'utilisateurs' })
  @ApiResponse({ status: 200, description: 'Performances récupérées avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async getUserSegmentPerformance(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('strategy') strategy?: string,
    @Query('category') category?: string,
  ) {
    try {
      return await this.analyticsService.getUserSegmentPerformance({
        startDate,
        endDate,
        strategy,
        category,
      });
    } catch (error) {
      this.logger.error(`Error getting user segment performance: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting user segment performance',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('time-series')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les données de série temporelle' })
  @ApiResponse({ status: 200, description: 'Données récupérées avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async getTimeSeriesData(
    @Request() req,
    @Query('userId') userId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('granularity') granularity?: 'day' | 'week' | 'month',
    @Query('strategy') strategy?: string,
    @Query('category') category?: string,
    @Query('segment') segment?: string,
  ) {
    try {
      // Vérifier si l'utilisateur a le droit d'accéder aux données d'un autre utilisateur
      if (userId && userId !== req.user.id && !req.user.roles.includes(UserRole.ADMIN)) {
        throw new HttpException('Vous n\'êtes pas autorisé à accéder à ces données', HttpStatus.FORBIDDEN);
      }
      
      // Vérifier si l'utilisateur a le droit d'accéder aux données de segment
      if (segment && !req.user.roles.includes(UserRole.ADMIN)) {
        throw new HttpException('Vous n\'êtes pas autorisé à accéder aux données de segment', HttpStatus.FORBIDDEN);
      }
      
      // Si aucun userId n'est spécifié et que l'utilisateur n'est pas admin, utiliser l'ID de l'utilisateur actuel
      const targetUserId = userId || (!req.user.roles.includes(UserRole.ADMIN) ? req.user.id : undefined);
      
      return await this.analyticsService.getTimeSeriesData({
        userId: targetUserId,
        startDate,
        endDate,
        granularity: granularity || 'day',
        strategy,
        category,
        segment,
      });
    } catch (error) {
      this.logger.error(`Error getting time series data: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting time series data',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('flow')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les données de flux de recommandation' })
  @ApiResponse({ status: 200, description: 'Données récupérées avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async getRecommendationFlow(
    @Request() req,
    @Query('userId') userId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('strategy') strategy?: string,
    @Query('category') category?: string,
    @Query('segment') segment?: string,
  ) {
    try {
      // Vérifier si l'utilisateur a le droit d'accéder aux données d'un autre utilisateur
      if (userId && userId !== req.user.id && !req.user.roles.includes(UserRole.ADMIN)) {
        throw new HttpException('Vous n\'êtes pas autorisé à accéder à ces données', HttpStatus.FORBIDDEN);
      }
      
      // Vérifier si l'utilisateur a le droit d'accéder aux données de segment
      if (segment && !req.user.roles.includes(UserRole.ADMIN)) {
        throw new HttpException('Vous n\'êtes pas autorisé à accéder aux données de segment', HttpStatus.FORBIDDEN);
      }
      
      // Si aucun userId n'est spécifié et que l'utilisateur n'est pas admin, utiliser l'ID de l'utilisateur actuel
      const targetUserId = userId || (!req.user.roles.includes(UserRole.ADMIN) ? req.user.id : undefined);
      
      return await this.analyticsService.getRecommendationFlow({
        userId: targetUserId,
        startDate,
        endDate,
        strategy,
        category,
        segment,
      });
    } catch (error) {
      this.logger.error(`Error getting recommendation flow: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting recommendation flow',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('filters')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les filtres disponibles' })
  @ApiResponse({ status: 200, description: 'Filtres récupérés avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async getAvailableFilters(
    @Request() req,
    @Query('userId') userId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    try {
      // Vérifier si l'utilisateur a le droit d'accéder aux données d'un autre utilisateur
      if (userId && userId !== req.user.id && !req.user.roles.includes(UserRole.ADMIN)) {
        throw new HttpException('Vous n\'êtes pas autorisé à accéder à ces données', HttpStatus.FORBIDDEN);
      }
      
      // Si aucun userId n'est spécifié et que l'utilisateur n'est pas admin, utiliser l'ID de l'utilisateur actuel
      const targetUserId = userId || (!req.user.roles.includes(UserRole.ADMIN) ? req.user.id : undefined);
      
      return await this.analyticsService.getAvailableFilters({
        userId: targetUserId,
        startDate,
        endDate,
      });
    } catch (error) {
      this.logger.error(`Error getting available filters: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting available filters',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('export')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Exporter les données d\'analyse' })
  @ApiResponse({ status: 200, description: 'Données exportées avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async exportAnalyticsData(
    @Request() req,
    @Query('userId') userId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('strategy') strategy?: string,
    @Query('category') category?: string,
    @Query('segment') segment?: string,
    @Query('format') format: 'csv' | 'json' | 'excel' = 'csv',
    @Res() res: Response,
  ) {
    try {
      // Vérifier si l'utilisateur a le droit d'accéder aux données d'un autre utilisateur
      if (userId && userId !== req.user.id && !req.user.roles.includes(UserRole.ADMIN)) {
        throw new HttpException('Vous n\'êtes pas autorisé à accéder à ces données', HttpStatus.FORBIDDEN);
      }
      
      // Vérifier si l'utilisateur a le droit d'accéder aux données de segment
      if (segment && !req.user.roles.includes(UserRole.ADMIN)) {
        throw new HttpException('Vous n\'êtes pas autorisé à accéder aux données de segment', HttpStatus.FORBIDDEN);
      }
      
      // Si aucun userId n'est spécifié et que l'utilisateur n'est pas admin, utiliser l'ID de l'utilisateur actuel
      const targetUserId = userId || (!req.user.roles.includes(UserRole.ADMIN) ? req.user.id : undefined);
      
      const { data, filename, contentType } = await this.analyticsService.exportAnalyticsData({
        userId: targetUserId,
        startDate,
        endDate,
        strategy,
        category,
        segment,
        format,
      });
      
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
      return res.send(data);
    } catch (error) {
      this.logger.error(`Error exporting analytics data: ${error.message}`);
      throw new HttpException(
        error.message || 'Error exporting analytics data',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('top-recommendations')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les recommandations les plus populaires' })
  @ApiResponse({ status: 200, description: 'Recommandations récupérées avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async getTopRecommendations(
    @Request() req,
    @Query('userId') userId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('strategy') strategy?: string,
    @Query('category') category?: string,
    @Query('segment') segment?: string,
    @Query('limit') limit?: number,
  ) {
    try {
      // Vérifier si l'utilisateur a le droit d'accéder aux données d'un autre utilisateur
      if (userId && userId !== req.user.id && !req.user.roles.includes(UserRole.ADMIN)) {
        throw new HttpException('Vous n\'êtes pas autorisé à accéder à ces données', HttpStatus.FORBIDDEN);
      }
      
      // Vérifier si l'utilisateur a le droit d'accéder aux données de segment
      if (segment && !req.user.roles.includes(UserRole.ADMIN)) {
        throw new HttpException('Vous n\'êtes pas autorisé à accéder aux données de segment', HttpStatus.FORBIDDEN);
      }
      
      // Si aucun userId n'est spécifié et que l'utilisateur n'est pas admin, utiliser l'ID de l'utilisateur actuel
      const targetUserId = userId || (!req.user.roles.includes(UserRole.ADMIN) ? req.user.id : undefined);
      
      return await this.analyticsService.getTopRecommendations({
        userId: targetUserId,
        startDate,
        endDate,
        strategy,
        category,
        segment,
        limit: limit ? parseInt(limit.toString()) : 10,
      });
    } catch (error) {
      this.logger.error(`Error getting top recommendations: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting top recommendations',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
