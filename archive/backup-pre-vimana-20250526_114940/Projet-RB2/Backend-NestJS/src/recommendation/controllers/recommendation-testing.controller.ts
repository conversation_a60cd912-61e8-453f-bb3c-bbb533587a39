import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../user/enums/user-role.enum';
import { RecommendationTestingService } from '../services/recommendation-testing.service';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';

class CreateTestDto {
  name: string;
  description: string;
  strategies: RecommendationStrategy[];
  userSegments: string[];
  metrics: string[];
  sampleSize: number;
  testDuration: number;
}

class UpdateTestDto {
  name?: string;
  description?: string;
  strategies?: RecommendationStrategy[];
  userSegments?: string[];
  metrics?: string[];
  sampleSize?: number;
  testDuration?: number;
  isActive?: boolean;
}

@ApiTags('recommendation-testing')
@Controller('recommendation/testing')
export class RecommendationTestingController {
  private readonly logger = new Logger(RecommendationTestingController.name);

  constructor(private readonly testingService: RecommendationTestingService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Créer un nouveau test de recommandation' })
  @ApiResponse({ status: 201, description: 'Test créé avec succès' })
  @ApiResponse({ status: 400, description: 'Données invalides' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async createTest(@Body() createTestDto: CreateTestDto) {
    try {
      const test = await this.testingService.createTest(
        createTestDto.name,
        createTestDto.description,
        createTestDto.strategies,
        createTestDto.userSegments,
        createTestDto.metrics,
        createTestDto.sampleSize,
        createTestDto.testDuration,
      );
      return test;
    } catch (error) {
      this.logger.error(`Error creating test: ${error.message}`);
      throw new HttpException(
        error.message || 'Error creating test',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer tous les tests de recommandation' })
  @ApiResponse({ status: 200, description: 'Tests récupérés avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async getAllTests() {
    try {
      return await this.testingService.getAllTests();
    } catch (error) {
      this.logger.error(`Error getting all tests: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting all tests',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer un test de recommandation par son ID' })
  @ApiResponse({ status: 200, description: 'Test récupéré avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  @ApiResponse({ status: 404, description: 'Test non trouvé' })
  async getTestById(@Param('id') id: string) {
    try {
      const test = await this.testingService.getTestById(id);
      if (!test) {
        throw new HttpException('Test not found', HttpStatus.NOT_FOUND);
      }
      return test;
    } catch (error) {
      this.logger.error(`Error getting test by ID: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting test by ID',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour un test de recommandation' })
  @ApiResponse({ status: 200, description: 'Test mis à jour avec succès' })
  @ApiResponse({ status: 400, description: 'Données invalides' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  @ApiResponse({ status: 404, description: 'Test non trouvé' })
  async updateTest(@Param('id') id: string, @Body() updateTestDto: UpdateTestDto) {
    try {
      const test = await this.testingService.updateTest(id, updateTestDto);
      if (!test) {
        throw new HttpException('Test not found', HttpStatus.NOT_FOUND);
      }
      return test;
    } catch (error) {
      this.logger.error(`Error updating test: ${error.message}`);
      throw new HttpException(
        error.message || 'Error updating test',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Supprimer un test de recommandation' })
  @ApiResponse({ status: 200, description: 'Test supprimé avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  @ApiResponse({ status: 404, description: 'Test non trouvé' })
  async deleteTest(@Param('id') id: string) {
    try {
      const success = await this.testingService.deleteTest(id);
      if (!success) {
        throw new HttpException('Test not found', HttpStatus.NOT_FOUND);
      }
      return { success: true };
    } catch (error) {
      this.logger.error(`Error deleting test: ${error.message}`);
      throw new HttpException(
        error.message || 'Error deleting test',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':id/run')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Exécuter un test de recommandation' })
  @ApiResponse({ status: 200, description: 'Test exécuté avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  @ApiResponse({ status: 404, description: 'Test non trouvé' })
  async runTest(@Param('id') id: string) {
    try {
      const testRun = await this.testingService.runTest(id);
      return testRun;
    } catch (error) {
      this.logger.error(`Error running test: ${error.message}`);
      throw new HttpException(
        error.message || 'Error running test',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id/status')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer le statut d\'un test en cours d\'exécution' })
  @ApiResponse({ status: 200, description: 'Statut récupéré avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  @ApiResponse({ status: 404, description: 'Test non trouvé' })
  async getTestRunStatus(@Param('id') id: string) {
    try {
      const status = this.testingService.getTestRunStatus(id);
      if (!status) {
        // Vérifier si le test existe
        const test = await this.testingService.getTestById(id);
        if (!test) {
          throw new HttpException('Test not found', HttpStatus.NOT_FOUND);
        }
        return { status: 'NOT_RUNNING' };
      }
      return status;
    } catch (error) {
      this.logger.error(`Error getting test run status: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting test run status',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id/results')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les résultats d\'un test' })
  @ApiResponse({ status: 200, description: 'Résultats récupérés avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  @ApiResponse({ status: 404, description: 'Test non trouvé' })
  async getTestResults(@Param('id') id: string) {
    try {
      // Vérifier si le test existe
      const test = await this.testingService.getTestById(id);
      if (!test) {
        throw new HttpException('Test not found', HttpStatus.NOT_FOUND);
      }
      
      const results = await this.testingService.getTestResults(id);
      return results;
    } catch (error) {
      this.logger.error(`Error getting test results: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting test results',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id/runs')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les exécutions d\'un test' })
  @ApiResponse({ status: 200, description: 'Exécutions récupérées avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  @ApiResponse({ status: 404, description: 'Test non trouvé' })
  async getTestRuns(@Param('id') id: string) {
    try {
      // Vérifier si le test existe
      const test = await this.testingService.getTestById(id);
      if (!test) {
        throw new HttpException('Test not found', HttpStatus.NOT_FOUND);
      }
      
      const runs = await this.testingService.getTestRuns(id);
      return runs;
    } catch (error) {
      this.logger.error(`Error getting test runs: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting test runs',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('strategies')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les stratégies de recommandation disponibles' })
  @ApiResponse({ status: 200, description: 'Stratégies récupérées avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async getAvailableStrategies() {
    try {
      return Object.values(RecommendationStrategy);
    } catch (error) {
      this.logger.error(`Error getting available strategies: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting available strategies',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('segments')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les segments d\'utilisateurs disponibles' })
  @ApiResponse({ status: 200, description: 'Segments récupérés avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async getAvailableSegments() {
    try {
      return [
        'ALL_USERS',
        'NEW_USERS',
        'ACTIVE_USERS',
        'INACTIVE_USERS',
        'HIGH_ENGAGEMENT',
        'LOW_ENGAGEMENT',
      ];
    } catch (error) {
      this.logger.error(`Error getting available segments: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting available segments',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('metrics')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les métriques disponibles' })
  @ApiResponse({ status: 200, description: 'Métriques récupérées avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async getAvailableMetrics() {
    try {
      return [
        'CLICK_THROUGH_RATE',
        'CONVERSION_RATE',
        'RELEVANCE_SCORE',
        'SATISFACTION_SCORE',
        'DIVERSITY_SCORE',
        'NOVELTY_SCORE',
        'COVERAGE_SCORE',
      ];
    } catch (error) {
      this.logger.error(`Error getting available metrics: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting available metrics',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
