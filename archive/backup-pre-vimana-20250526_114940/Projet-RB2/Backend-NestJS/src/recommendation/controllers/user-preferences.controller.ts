import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  HttpException,
  HttpStatus,
  Logger,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { UserPreferencesService } from '../services/user-preferences.service';
import { ExternalDataService } from '../services/external-data.service';

class UpdatePreferencesDto {
  preferences: { category: string; weight: number }[];
}

class UpdateInterestsDto {
  interests: { interest: string; type: 'CATEGORY' | 'TAG' | 'KEYWORD'; weight: number }[];
}

class UpdatePersonalizationSettingsDto {
  enablePersonalization?: boolean;
  diversityLevel?: number;
  noveltyLevel?: number;
  contentFilters?: {
    excludedCategories?: string[];
    excludedTags?: string[];
    contentTypes?: string[];
    minContentRating?: number;
    languagePreferences?: string[];
  };
  privacySettings?: {
    allowInteractionTracking?: boolean;
    allowContentAnalysis?: boolean;
    allowThirdPartyData?: boolean;
    dataRetentionPeriod?: number;
  };
}

class ExternalContentQueryDto {
  limit?: number;
  categories?: string[];
  tags?: string[];
  sources?: string[];
  minDate?: string;
  maxDate?: string;
}

@ApiTags('user-preferences')
@Controller('user-preferences')
export class UserPreferencesController {
  private readonly logger = new Logger(UserPreferencesController.name);

  constructor(
    private readonly userPreferencesService: UserPreferencesService,
    private readonly externalDataService: ExternalDataService,
  ) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les préférences de l\'utilisateur actuel' })
  @ApiResponse({ status: 200, description: 'Préférences récupérées avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async getUserPreferences(@Request() req) {
    try {
      const userId = req.user.id;
      return await this.userPreferencesService.getUserPreferences(userId);
    } catch (error) {
      this.logger.error(`Error getting user preferences: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting user preferences',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour les préférences de l\'utilisateur actuel' })
  @ApiResponse({ status: 200, description: 'Préférences mises à jour avec succès' })
  @ApiResponse({ status: 400, description: 'Données invalides' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async updateUserPreferences(@Request() req, @Body() updatePreferencesDto: UpdatePreferencesDto) {
    try {
      const userId = req.user.id;
      return await this.userPreferencesService.updateUserPreferences(
        userId,
        updatePreferencesDto.preferences,
      );
    } catch (error) {
      this.logger.error(`Error updating user preferences: ${error.message}`);
      throw new HttpException(
        error.message || 'Error updating user preferences',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('interests')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les intérêts de l\'utilisateur actuel' })
  @ApiResponse({ status: 200, description: 'Intérêts récupérés avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async getUserInterests(@Request() req) {
    try {
      const userId = req.user.id;
      return await this.userPreferencesService.getUserInterests(userId);
    } catch (error) {
      this.logger.error(`Error getting user interests: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting user interests',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('interests')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour les intérêts de l\'utilisateur actuel' })
  @ApiResponse({ status: 200, description: 'Intérêts mis à jour avec succès' })
  @ApiResponse({ status: 400, description: 'Données invalides' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async updateUserInterests(@Request() req, @Body() updateInterestsDto: UpdateInterestsDto) {
    try {
      const userId = req.user.id;
      return await this.userPreferencesService.updateUserInterests(
        userId,
        updateInterestsDto.interests,
      );
    } catch (error) {
      this.logger.error(`Error updating user interests: ${error.message}`);
      throw new HttpException(
        error.message || 'Error updating user interests',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('settings')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les paramètres de personnalisation de l\'utilisateur actuel' })
  @ApiResponse({ status: 200, description: 'Paramètres récupérés avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async getPersonalizationSettings(@Request() req) {
    try {
      const userId = req.user.id;
      return await this.userPreferencesService.getPersonalizationSettings(userId);
    } catch (error) {
      this.logger.error(`Error getting personalization settings: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting personalization settings',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('settings')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour les paramètres de personnalisation de l\'utilisateur actuel' })
  @ApiResponse({ status: 200, description: 'Paramètres mis à jour avec succès' })
  @ApiResponse({ status: 400, description: 'Données invalides' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async updatePersonalizationSettings(
    @Request() req,
    @Body() updateSettingsDto: UpdatePersonalizationSettingsDto,
  ) {
    try {
      const userId = req.user.id;
      return await this.userPreferencesService.updatePersonalizationSettings(
        userId,
        updateSettingsDto,
      );
    } catch (error) {
      this.logger.error(`Error updating personalization settings: ${error.message}`);
      throw new HttpException(
        error.message || 'Error updating personalization settings',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('external-content')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer du contenu externe personnalisé' })
  @ApiResponse({ status: 200, description: 'Contenu récupéré avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async getExternalContent(
    @Request() req,
    @Query() query: ExternalContentQueryDto,
  ) {
    try {
      const userId = req.user.id;
      
      // Récupérer les paramètres de personnalisation
      const settings = await this.userPreferencesService.getPersonalizationSettings(userId);
      
      // Vérifier si l'utilisateur autorise les données tierces
      if (!settings.privacySettings.allowThirdPartyData) {
        throw new HttpException(
          'User has disabled third-party data',
          HttpStatus.FORBIDDEN,
        );
      }
      
      // Récupérer le contenu externe
      const content = await this.externalDataService.getExternalContentForRecommendations(
        userId,
        query.limit || 10,
        {
          categories: query.categories,
          tags: query.tags,
          sources: query.sources,
          minDate: query.minDate,
          maxDate: query.maxDate,
        },
      );
      
      // Appliquer la personnalisation
      return await this.userPreferencesService.applyPersonalization(
        userId,
        content,
        {
          limit: query.limit || 10,
        },
      );
    } catch (error) {
      this.logger.error(`Error getting external content: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting external content',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':userId/preferences')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les préférences d\'un utilisateur spécifique' })
  @ApiResponse({ status: 200, description: 'Préférences récupérées avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async getUserPreferencesById(@Request() req, @Param('userId') userId: string) {
    try {
      // Vérifier si l'utilisateur actuel a le droit d'accéder aux préférences d'un autre utilisateur
      if (req.user.id !== userId && !req.user.roles.includes('admin')) {
        throw new HttpException(
          'You are not authorized to access this resource',
          HttpStatus.FORBIDDEN,
        );
      }
      
      return await this.userPreferencesService.getUserPreferences(userId);
    } catch (error) {
      this.logger.error(`Error getting user preferences: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting user preferences',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':userId/interests')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les intérêts d\'un utilisateur spécifique' })
  @ApiResponse({ status: 200, description: 'Intérêts récupérés avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async getUserInterestsById(@Request() req, @Param('userId') userId: string) {
    try {
      // Vérifier si l'utilisateur actuel a le droit d'accéder aux intérêts d'un autre utilisateur
      if (req.user.id !== userId && !req.user.roles.includes('admin')) {
        throw new HttpException(
          'You are not authorized to access this resource',
          HttpStatus.FORBIDDEN,
        );
      }
      
      return await this.userPreferencesService.getUserInterests(userId);
    } catch (error) {
      this.logger.error(`Error getting user interests: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting user interests',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':userId/settings')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les paramètres de personnalisation d\'un utilisateur spécifique' })
  @ApiResponse({ status: 200, description: 'Paramètres récupérés avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async getPersonalizationSettingsById(@Request() req, @Param('userId') userId: string) {
    try {
      // Vérifier si l'utilisateur actuel a le droit d'accéder aux paramètres d'un autre utilisateur
      if (req.user.id !== userId && !req.user.roles.includes('admin')) {
        throw new HttpException(
          'You are not authorized to access this resource',
          HttpStatus.FORBIDDEN,
        );
      }
      
      return await this.userPreferencesService.getPersonalizationSettings(userId);
    } catch (error) {
      this.logger.error(`Error getting personalization settings: ${error.message}`);
      throw new HttpException(
        error.message || 'Error getting personalization settings',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
