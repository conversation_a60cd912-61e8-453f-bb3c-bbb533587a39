import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { PrismaService } from '../../prisma/prisma.service';
import { CacheService } from '../../shared/services/cache.service';
import { firstValueFrom } from 'rxjs';
import * as cheerio from 'cheerio';
import * as natural from 'natural';
import { SchedulerRegistry } from '@nestjs/schedule';
import { CronJob } from 'cron';

interface ExternalContent {
  id: string;
  source: string;
  title: string;
  description: string;
  url: string;
  imageUrl?: string;
  categories: string[];
  tags: string[];
  publishedAt: Date;
  popularity: number;
  metadata: Record<string, any>;
}

interface ExternalDataSource {
  id: string;
  name: string;
  type: 'RSS' | 'API' | 'SCRAPER';
  url: string;
  apiKey?: string;
  refreshInterval: number; // en minutes
  isActive: boolean;
  lastRefreshed?: Date;
  mapping: Record<string, string>;
  filters?: Record<string, any>;
}

@Injectable()
export class ExternalDataService {
  private readonly logger = new Logger(ExternalDataService.name);
  private readonly tokenizer = new natural.WordTokenizer();
  private readonly stemmer = natural.PorterStemmer;
  private readonly tfidf = new natural.TfIdf();
  private readonly dataSources: ExternalDataSource[] = [];
  private readonly cacheTTL: number;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly cacheService: CacheService,
    private readonly schedulerRegistry: SchedulerRegistry,
  ) {
    this.cacheTTL = this.configService.get<number>('EXTERNAL_DATA_CACHE_TTL', 3600); // 1 heure par défaut
    this.initialize();
  }

  /**
   * Initialise le service et charge les sources de données
   */
  private async initialize(): Promise<void> {
    try {
      // Charger les sources de données depuis la base de données
      const sources = await this.prisma.externalDataSource.findMany({
        where: { isActive: true },
      });

      // Convertir en format interne
      this.dataSources.push(
        ...sources.map(source => ({
          id: source.id,
          name: source.name,
          type: source.type as 'RSS' | 'API' | 'SCRAPER',
          url: source.url,
          apiKey: source.apiKey,
          refreshInterval: source.refreshInterval,
          isActive: source.isActive,
          lastRefreshed: source.lastRefreshed,
          mapping: source.mapping as Record<string, string>,
          filters: source.filters as Record<string, any>,
        })),
      );

      // Planifier les tâches de rafraîchissement
      this.scheduleRefreshTasks();

      this.logger.log(`Initialized with ${this.dataSources.length} external data sources`);
    } catch (error) {
      this.logger.error(`Error initializing external data service: ${error.message}`);
    }
  }

  /**
   * Planifie les tâches de rafraîchissement pour chaque source de données
   */
  private scheduleRefreshTasks(): void {
    this.dataSources.forEach(source => {
      if (!source.isActive) return;

      const jobName = `refresh-external-data-${source.id}`;
      const cronExpression = `*/${source.refreshInterval} * * * *`; // Toutes les X minutes

      const job = new CronJob(cronExpression, () => {
        this.refreshDataSource(source.id).catch(error => {
          this.logger.error(`Error refreshing data source ${source.name}: ${error.message}`);
        });
      });

      try {
        this.schedulerRegistry.addCronJob(jobName, job);
        job.start();
        this.logger.log(`Scheduled refresh task for ${source.name} (${cronExpression})`);
      } catch (error) {
        this.logger.error(`Error scheduling refresh task for ${source.name}: ${error.message}`);
      }
    });
  }

  /**
   * Rafraîchit les données d'une source spécifique
   */
  async refreshDataSource(sourceId: string): Promise<void> {
    const source = this.dataSources.find(s => s.id === sourceId);
    if (!source) {
      throw new Error(`Data source with ID ${sourceId} not found`);
    }

    this.logger.log(`Refreshing data from ${source.name}`);

    try {
      let content: ExternalContent[] = [];

      // Récupérer les données selon le type de source
      switch (source.type) {
        case 'RSS':
          content = await this.fetchRssContent(source);
          break;
        case 'API':
          content = await this.fetchApiContent(source);
          break;
        case 'SCRAPER':
          content = await this.scrapeContent(source);
          break;
      }

      // Filtrer le contenu selon les critères définis
      const filteredContent = this.filterContent(content, source.filters);

      // Stocker les données dans la base de données
      await this.storeExternalContent(filteredContent, source.id);

      // Mettre à jour la date de dernier rafraîchissement
      await this.prisma.externalDataSource.update({
        where: { id: source.id },
        data: { lastRefreshed: new Date() },
      });

      // Mettre à jour la source en mémoire
      const sourceIndex = this.dataSources.findIndex(s => s.id === sourceId);
      if (sourceIndex !== -1) {
        this.dataSources[sourceIndex].lastRefreshed = new Date();
      }

      this.logger.log(`Successfully refreshed ${filteredContent.length} items from ${source.name}`);
    } catch (error) {
      this.logger.error(`Error refreshing data from ${source.name}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère le contenu d'un flux RSS
   */
  private async fetchRssContent(source: ExternalDataSource): Promise<ExternalContent[]> {
    try {
      const response = await firstValueFrom(this.httpService.get(source.url));
      const xml = response.data;

      // Utiliser une bibliothèque de parsing XML ici
      // Pour simplifier, nous utilisons cheerio qui fonctionne bien pour le HTML/XML simple
      const $ = cheerio.load(xml, { xmlMode: true });
      const items = $('item');

      const content: ExternalContent[] = [];

      items.each((i, item) => {
        const $item = $(item);
        
        // Mapper les champs selon la configuration
        const title = $item.find(source.mapping.title || 'title').text();
        const description = $item.find(source.mapping.description || 'description').text();
        const link = $item.find(source.mapping.url || 'link').text();
        const pubDate = $item.find(source.mapping.publishedAt || 'pubDate').text();
        const categories = $item.find(source.mapping.categories || 'category')
          .map((i, el) => $(el).text())
          .get();

        // Générer un ID unique basé sur l'URL
        const id = `${source.id}-${Buffer.from(link).toString('base64').substring(0, 22)}`;

        content.push({
          id,
          source: source.id,
          title,
          description,
          url: link,
          imageUrl: $item.find(source.mapping.imageUrl || 'enclosure').attr('url'),
          categories,
          tags: categories, // Utiliser les catégories comme tags par défaut
          publishedAt: new Date(pubDate),
          popularity: 0, // À calculer ultérieurement
          metadata: {
            originalXml: $item.html(),
          },
        });
      });

      return content;
    } catch (error) {
      this.logger.error(`Error fetching RSS content from ${source.name}: ${error.message}`);
      return [];
    }
  }

  /**
   * Récupère le contenu via une API
   */
  private async fetchApiContent(source: ExternalDataSource): Promise<ExternalContent[]> {
    try {
      // Configurer les headers avec la clé API si nécessaire
      const headers: Record<string, string> = {};
      if (source.apiKey) {
        headers['Authorization'] = `Bearer ${source.apiKey}`;
      }

      const response = await firstValueFrom(
        this.httpService.get(source.url, { headers }),
      );
      
      const data = response.data;
      
      // Extraire les éléments selon le chemin spécifié dans la configuration
      const itemsPath = source.mapping.itemsPath || '';
      const items = itemsPath.split('.').reduce((obj, path) => obj?.[path], data) || [];
      
      if (!Array.isArray(items)) {
        throw new Error(`Items at path ${itemsPath} is not an array`);
      }
      
      const content: ExternalContent[] = [];
      
      items.forEach(item => {
        // Mapper les champs selon la configuration
        const title = this.getNestedProperty(item, source.mapping.title);
        const description = this.getNestedProperty(item, source.mapping.description);
        const url = this.getNestedProperty(item, source.mapping.url);
        const imageUrl = this.getNestedProperty(item, source.mapping.imageUrl);
        const publishedAt = this.getNestedProperty(item, source.mapping.publishedAt);
        const categoriesPath = source.mapping.categories;
        const categories = categoriesPath ? this.getNestedProperty(item, categoriesPath) || [] : [];
        const tagsPath = source.mapping.tags;
        const tags = tagsPath ? this.getNestedProperty(item, tagsPath) || [] : [];
        const popularityPath = source.mapping.popularity;
        const popularity = popularityPath ? this.getNestedProperty(item, popularityPath) || 0 : 0;
        
        // Générer un ID unique basé sur l'URL
        const id = `${source.id}-${Buffer.from(url).toString('base64').substring(0, 22)}`;
        
        content.push({
          id,
          source: source.id,
          title,
          description,
          url,
          imageUrl,
          categories: Array.isArray(categories) ? categories : [categories],
          tags: Array.isArray(tags) ? tags : [tags],
          publishedAt: new Date(publishedAt),
          popularity: Number(popularity),
          metadata: {
            originalData: item,
          },
        });
      });
      
      return content;
    } catch (error) {
      this.logger.error(`Error fetching API content from ${source.name}: ${error.message}`);
      return [];
    }
  }

  /**
   * Récupère le contenu en scrapant une page web
   */
  private async scrapeContent(source: ExternalDataSource): Promise<ExternalContent[]> {
    try {
      const response = await firstValueFrom(this.httpService.get(source.url));
      const html = response.data;
      
      const $ = cheerio.load(html);
      
      // Sélectionner les éléments selon le sélecteur CSS spécifié
      const itemSelector = source.mapping.itemSelector || '';
      const items = $(itemSelector);
      
      const content: ExternalContent[] = [];
      
      items.each((i, item) => {
        const $item = $(item);
        
        // Mapper les champs selon la configuration
        const titleSelector = source.mapping.title;
        const title = titleSelector ? $item.find(titleSelector).text().trim() : '';
        
        const descriptionSelector = source.mapping.description;
        const description = descriptionSelector ? $item.find(descriptionSelector).text().trim() : '';
        
        const urlSelector = source.mapping.url;
        let url = urlSelector ? $item.find(urlSelector).attr('href') : '';
        
        // Convertir en URL absolue si nécessaire
        if (url && !url.startsWith('http')) {
          const baseUrl = new URL(source.url);
          url = `${baseUrl.origin}${url.startsWith('/') ? '' : '/'}${url}`;
        }
        
        const imageUrlSelector = source.mapping.imageUrl;
        let imageUrl = imageUrlSelector ? $item.find(imageUrlSelector).attr('src') : '';
        
        // Convertir en URL absolue si nécessaire
        if (imageUrl && !imageUrl.startsWith('http')) {
          const baseUrl = new URL(source.url);
          imageUrl = `${baseUrl.origin}${imageUrl.startsWith('/') ? '' : '/'}${imageUrl}`;
        }
        
        const categoriesSelector = source.mapping.categories;
        const categories = categoriesSelector
          ? $item.find(categoriesSelector).map((i, el) => $(el).text().trim()).get()
          : [];
        
        const tagsSelector = source.mapping.tags;
        const tags = tagsSelector
          ? $item.find(tagsSelector).map((i, el) => $(el).text().trim()).get()
          : [];
        
        const dateSelector = source.mapping.publishedAt;
        const dateText = dateSelector ? $item.find(dateSelector).text().trim() : '';
        const publishedAt = dateText ? new Date(dateText) : new Date();
        
        // Générer un ID unique basé sur l'URL
        const id = `${source.id}-${Buffer.from(url).toString('base64').substring(0, 22)}`;
        
        content.push({
          id,
          source: source.id,
          title,
          description,
          url,
          imageUrl,
          categories,
          tags,
          publishedAt,
          popularity: 0, // À calculer ultérieurement
          metadata: {
            originalHtml: $item.html(),
          },
        });
      });
      
      return content;
    } catch (error) {
      this.logger.error(`Error scraping content from ${source.name}: ${error.message}`);
      return [];
    }
  }

  /**
   * Filtre le contenu selon les critères définis
   */
  private filterContent(
    content: ExternalContent[],
    filters?: Record<string, any>,
  ): ExternalContent[] {
    if (!filters) return content;
    
    return content.filter(item => {
      // Filtrer par date de publication
      if (filters.minDate && new Date(item.publishedAt) < new Date(filters.minDate)) {
        return false;
      }
      
      if (filters.maxDate && new Date(item.publishedAt) > new Date(filters.maxDate)) {
        return false;
      }
      
      // Filtrer par catégories
      if (filters.categories && filters.categories.length > 0) {
        if (!item.categories.some(category => 
          filters.categories.includes(category.toLowerCase())
        )) {
          return false;
        }
      }
      
      // Filtrer par tags
      if (filters.tags && filters.tags.length > 0) {
        if (!item.tags.some(tag => 
          filters.tags.includes(tag.toLowerCase())
        )) {
          return false;
        }
      }
      
      // Filtrer par mots-clés dans le titre ou la description
      if (filters.keywords && filters.keywords.length > 0) {
        const text = `${item.title} ${item.description}`.toLowerCase();
        if (!filters.keywords.some((keyword: string) => 
          text.includes(keyword.toLowerCase())
        )) {
          return false;
        }
      }
      
      return true;
    });
  }

  /**
   * Stocke le contenu externe dans la base de données
   */
  private async storeExternalContent(
    content: ExternalContent[],
    sourceId: string,
  ): Promise<void> {
    try {
      // Traiter par lots pour éviter les problèmes de mémoire
      const batchSize = 100;
      
      for (let i = 0; i < content.length; i += batchSize) {
        const batch = content.slice(i, i + batchSize);
        
        // Utiliser une transaction pour garantir l'atomicité
        await this.prisma.$transaction(async (prisma) => {
          for (const item of batch) {
            // Vérifier si l'élément existe déjà
            const existing = await prisma.externalContent.findUnique({
              where: { id: item.id },
            });
            
            if (existing) {
              // Mettre à jour l'élément existant
              await prisma.externalContent.update({
                where: { id: item.id },
                data: {
                  title: item.title,
                  description: item.description,
                  url: item.url,
                  imageUrl: item.imageUrl,
                  categories: item.categories,
                  tags: item.tags,
                  publishedAt: item.publishedAt,
                  popularity: item.popularity,
                  metadata: item.metadata,
                  updatedAt: new Date(),
                },
              });
            } else {
              // Créer un nouvel élément
              await prisma.externalContent.create({
                data: {
                  id: item.id,
                  sourceId,
                  title: item.title,
                  description: item.description,
                  url: item.url,
                  imageUrl: item.imageUrl,
                  categories: item.categories,
                  tags: item.tags,
                  publishedAt: item.publishedAt,
                  popularity: item.popularity,
                  metadata: item.metadata,
                },
              });
            }
          }
        });
      }
    } catch (error) {
      this.logger.error(`Error storing external content: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère une propriété imbriquée d'un objet
   */
  private getNestedProperty(obj: any, path: string): any {
    if (!path) return undefined;
    return path.split('.').reduce((o, p) => (o ? o[p] : undefined), obj);
  }

  /**
   * Récupère le contenu externe pour les recommandations
   */
  async getExternalContentForRecommendations(
    userId: string,
    limit: number = 10,
    filters?: Record<string, any>,
  ): Promise<ExternalContent[]> {
    try {
      // Vérifier le cache d'abord
      const cacheKey = `external_recommendations_${userId}_${JSON.stringify(filters)}_${limit}`;
      const cachedContent = await this.cacheService.get<ExternalContent[]>(cacheKey);
      
      if (cachedContent) {
        return cachedContent;
      }
      
      // Récupérer les préférences de l'utilisateur
      const userPreferences = await this.getUserPreferences(userId);
      
      // Récupérer le contenu externe
      const content = await this.prisma.externalContent.findMany({
        where: {
          ...(filters?.categories ? {
            categories: {
              hasSome: filters.categories,
            },
          } : {}),
          ...(filters?.tags ? {
            tags: {
              hasSome: filters.tags,
            },
          } : {}),
          ...(filters?.sources ? {
            sourceId: {
              in: filters.sources,
            },
          } : {}),
          ...(filters?.minDate ? {
            publishedAt: {
              gte: new Date(filters.minDate),
            },
          } : {}),
          ...(filters?.maxDate ? {
            publishedAt: {
              lte: new Date(filters.maxDate),
            },
          } : {}),
        },
        orderBy: {
          publishedAt: 'desc',
        },
        take: limit * 3, // Récupérer plus d'éléments pour le filtrage
      });
      
      // Calculer la pertinence pour chaque élément
      const scoredContent = content.map(item => {
        const relevanceScore = this.calculateRelevanceScore(item, userPreferences);
        return {
          ...item,
          relevanceScore,
        };
      });
      
      // Trier par pertinence et limiter le nombre d'éléments
      const recommendations = scoredContent
        .sort((a, b) => b.relevanceScore - a.relevanceScore)
        .slice(0, limit);
      
      // Mettre en cache les résultats
      await this.cacheService.set(cacheKey, recommendations, this.cacheTTL);
      
      return recommendations;
    } catch (error) {
      this.logger.error(`Error getting external content for recommendations: ${error.message}`);
      return [];
    }
  }

  /**
   * Récupère les préférences de l'utilisateur
   */
  private async getUserPreferences(userId: string): Promise<Record<string, number>> {
    try {
      // Récupérer les interactions de l'utilisateur
      const interactions = await this.prisma.userInteraction.findMany({
        where: {
          userId,
          value: {
            gt: 0, // Uniquement les interactions positives
          },
        },
        include: {
          retreat: true,
          course: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 100, // Limiter aux 100 dernières interactions
      });
      
      // Extraire les catégories et tags des interactions
      const categories: string[] = [];
      const tags: string[] = [];
      
      interactions.forEach(interaction => {
        if (interaction.retreat) {
          categories.push(...(interaction.retreat.categories || []));
          tags.push(...(interaction.retreat.tags || []));
        } else if (interaction.course) {
          categories.push(...(interaction.course.categories || []));
          tags.push(...(interaction.course.tags || []));
        }
      });
      
      // Calculer les poids pour chaque catégorie et tag
      const preferences: Record<string, number> = {};
      
      // Poids des catégories
      categories.forEach(category => {
        if (!preferences[`category:${category}`]) {
          preferences[`category:${category}`] = 0;
        }
        preferences[`category:${category}`] += 1;
      });
      
      // Poids des tags
      tags.forEach(tag => {
        if (!preferences[`tag:${tag}`]) {
          preferences[`tag:${tag}`] = 0;
        }
        preferences[`tag:${tag}`] += 1;
      });
      
      // Normaliser les poids
      const maxWeight = Math.max(...Object.values(preferences));
      if (maxWeight > 0) {
        Object.keys(preferences).forEach(key => {
          preferences[key] = preferences[key] / maxWeight;
        });
      }
      
      return preferences;
    } catch (error) {
      this.logger.error(`Error getting user preferences: ${error.message}`);
      return {};
    }
  }

  /**
   * Calcule le score de pertinence d'un élément pour un utilisateur
   */
  private calculateRelevanceScore(
    item: any,
    userPreferences: Record<string, number>,
  ): number {
    let score = 0;
    
    // Score basé sur les catégories
    (item.categories || []).forEach((category: string) => {
      const preferenceKey = `category:${category}`;
      if (userPreferences[preferenceKey]) {
        score += userPreferences[preferenceKey];
      }
    });
    
    // Score basé sur les tags
    (item.tags || []).forEach((tag: string) => {
      const preferenceKey = `tag:${tag}`;
      if (userPreferences[preferenceKey]) {
        score += userPreferences[preferenceKey];
      }
    });
    
    // Score basé sur la fraîcheur (plus récent = meilleur)
    const ageInDays = (Date.now() - new Date(item.publishedAt).getTime()) / (1000 * 60 * 60 * 24);
    const freshnessScore = Math.max(0, 1 - (ageInDays / 30)); // Décroît linéairement sur 30 jours
    
    // Score basé sur la popularité
    const popularityScore = Math.min(1, item.popularity / 100); // Plafonné à 1
    
    // Combiner les scores avec des poids
    const finalScore = (
      score * 0.5 + // Préférences utilisateur
      freshnessScore * 0.3 + // Fraîcheur
      popularityScore * 0.2 // Popularité
    );
    
    return finalScore;
  }

  /**
   * Analyse le contenu pour extraire des mots-clés
   */
  analyzeContent(text: string, maxKeywords: number = 10): string[] {
    // Tokeniser le texte
    const tokens = this.tokenizer.tokenize(text.toLowerCase());
    if (!tokens || tokens.length === 0) return [];
    
    // Filtrer les mots vides (stop words)
    const stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'against', 'between', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'from', 'up', 'down', 'of', 'off', 'over', 'under', 'again', 'further', 'then', 'once', 'here', 'there', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 's', 't', 'can', 'will', 'just', 'don', 'should', 'now'];
    const filteredTokens = tokens.filter(token => !stopWords.includes(token) && token.length > 2);
    
    // Stemming des tokens
    const stems = filteredTokens.map(token => this.stemmer.stem(token));
    
    // Calculer la fréquence des stems
    const stemFrequency: Record<string, number> = {};
    stems.forEach(stem => {
      if (!stemFrequency[stem]) {
        stemFrequency[stem] = 0;
      }
      stemFrequency[stem] += 1;
    });
    
    // Trier par fréquence et prendre les N premiers
    const topStems = Object.entries(stemFrequency)
      .sort((a, b) => b[1] - a[1])
      .slice(0, maxKeywords)
      .map(([stem]) => stem);
    
    // Mapper les stems aux tokens originaux (prendre le premier token correspondant)
    const keywords: string[] = [];
    topStems.forEach(stem => {
      const originalToken = filteredTokens.find(token => this.stemmer.stem(token) === stem);
      if (originalToken && !keywords.includes(originalToken)) {
        keywords.push(originalToken);
      }
    });
    
    return keywords;
  }
}
