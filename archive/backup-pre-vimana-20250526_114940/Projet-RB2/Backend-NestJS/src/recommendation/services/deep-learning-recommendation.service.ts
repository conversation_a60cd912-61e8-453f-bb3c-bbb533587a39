import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { RecommendationResult } from '../interfaces/recommendation-result.interface';
import { UserInteractionService } from './user-interaction.service';
import { ContentService } from '../../content/services/content.service';
import { UserService } from '../../user/services/user.service';
import { TensorflowService } from '../../shared/services/tensorflow.service';
import { CacheService } from '../../shared/services/cache.service';
import { RecommendationMetricsService } from './recommendation-metrics.service';

@Injectable()
export class DeepLearningRecommendationService {
  private readonly logger = new Logger(DeepLearningRecommendationService.name);
  private readonly modelPath: string;
  private readonly embeddingDimension: number;
  private readonly batchSize: number;
  private readonly learningRate: number;
  private readonly trainingInterval: number; // en millisecondes
  private readonly modelUpdateThreshold: number;
  private isTraining: boolean = false;
  private lastTrainingTime: Date = null;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly userInteractionService: UserInteractionService,
    private readonly contentService: ContentService,
    private readonly userService: UserService,
    private readonly tensorflowService: TensorflowService,
    private readonly cacheService: CacheService,
    private readonly metricsService: RecommendationMetricsService,
  ) {
    this.modelPath = this.configService.get<string>('RECOMMENDATION_MODEL_PATH', './models/recommendation');
    this.embeddingDimension = this.configService.get<number>('RECOMMENDATION_EMBEDDING_DIMENSION', 128);
    this.batchSize = this.configService.get<number>('RECOMMENDATION_BATCH_SIZE', 64);
    this.learningRate = this.configService.get<number>('RECOMMENDATION_LEARNING_RATE', 0.001);
    this.trainingInterval = this.configService.get<number>('RECOMMENDATION_TRAINING_INTERVAL', 86400000); // 24 heures par défaut
    this.modelUpdateThreshold = this.configService.get<number>('RECOMMENDATION_MODEL_UPDATE_THRESHOLD', 1000);

    // Initialiser le modèle
    this.initializeModel();

    // Planifier l'entraînement périodique
    setInterval(() => this.trainModelIfNeeded(), this.trainingInterval);
  }

  /**
   * Initialise le modèle de recommandation
   */
  private async initializeModel(): Promise<void> {
    try {
      // Vérifier si un modèle existe déjà
      const modelExists = await this.tensorflowService.modelExists(this.modelPath);

      if (modelExists) {
        this.logger.log('Chargement du modèle existant...');
        await this.tensorflowService.loadModel(this.modelPath);
        this.logger.log('Modèle chargé avec succès');
      } else {
        this.logger.log('Création d\'un nouveau modèle...');
        await this.createModel();
        this.logger.log('Nouveau modèle créé avec succès');
        
        // Entraîner le modèle initial
        await this.trainModel();
      }
    } catch (error) {
      this.logger.error(`Erreur lors de l'initialisation du modèle: ${error.message}`);
      throw error;
    }
  }

  /**
   * Crée un nouveau modèle de recommandation
   */
  private async createModel(): Promise<void> {
    try {
      // Créer un modèle de factorisation matricielle
      await this.tensorflowService.createMatrixFactorizationModel(
        this.embeddingDimension,
        await this.getUserCount(),
        await this.getItemCount(),
        this.learningRate,
      );

      // Sauvegarder le modèle
      await this.tensorflowService.saveModel(this.modelPath);
    } catch (error) {
      this.logger.error(`Erreur lors de la création du modèle: ${error.message}`);
      throw error;
    }
  }

  /**
   * Entraîne le modèle avec les données d'interaction récentes
   */
  private async trainModel(): Promise<void> {
    if (this.isTraining) {
      this.logger.log('Un entraînement est déjà en cours, ignoré');
      return;
    }

    this.isTraining = true;
    this.logger.log('Début de l\'entraînement du modèle...');

    try {
      // Récupérer les données d'entraînement
      const trainingData = await this.getTrainingData();
      
      if (trainingData.length === 0) {
        this.logger.log('Aucune donnée d\'entraînement disponible');
        this.isTraining = false;
        return;
      }

      this.logger.log(`Entraînement avec ${trainingData.length} interactions...`);

      // Préparer les données pour TensorFlow
      const { userIndices, itemIndices, ratings } = this.prepareTrainingData(trainingData);

      // Entraîner le modèle
      const trainingMetrics = await this.tensorflowService.trainModel(
        userIndices,
        itemIndices,
        ratings,
        this.batchSize,
        10, // epochs
      );

      // Sauvegarder le modèle
      await this.tensorflowService.saveModel(this.modelPath);

      // Mettre à jour les métriques
      await this.metricsService.updateModelTrainingMetrics({
        timestamp: new Date(),
        loss: trainingMetrics.finalLoss,
        accuracy: trainingMetrics.accuracy,
        samplesCount: trainingData.length,
        epochsCount: 10,
        duration: trainingMetrics.duration,
      });

      this.lastTrainingTime = new Date();
      this.logger.log(`Entraînement terminé en ${trainingMetrics.duration}ms avec une perte finale de ${trainingMetrics.finalLoss}`);
    } catch (error) {
      this.logger.error(`Erreur lors de l'entraînement du modèle: ${error.message}`);
    } finally {
      this.isTraining = false;
    }
  }

  /**
   * Vérifie si le modèle doit être entraîné et l'entraîne si nécessaire
   */
  private async trainModelIfNeeded(): Promise<void> {
    try {
      // Vérifier s'il y a suffisamment de nouvelles interactions depuis le dernier entraînement
      const lastTrainingTime = this.lastTrainingTime || new Date(0);
      const newInteractionsCount = await this.prisma.userInteraction.count({
        where: {
          createdAt: {
            gt: lastTrainingTime,
          },
        },
      });

      if (newInteractionsCount >= this.modelUpdateThreshold) {
        this.logger.log(`${newInteractionsCount} nouvelles interactions détectées, entraînement du modèle...`);
        await this.trainModel();
      } else {
        this.logger.log(`Seulement ${newInteractionsCount} nouvelles interactions, entraînement ignoré`);
      }
    } catch (error) {
      this.logger.error(`Erreur lors de la vérification des conditions d'entraînement: ${error.message}`);
    }
  }

  /**
   * Récupère les données d'entraînement depuis la base de données
   */
  private async getTrainingData(): Promise<any[]> {
    try {
      // Récupérer les interactions des utilisateurs
      return await this.prisma.userInteraction.findMany({
        select: {
          userId: true,
          itemId: true,
          itemType: true,
          interactionType: true,
          value: true,
        },
        where: {
          // Exclure les interactions négatives pour l'entraînement initial
          value: {
            gte: 0,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 100000, // Limiter le nombre d'interactions pour éviter les problèmes de mémoire
      });
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des données d'entraînement: ${error.message}`);
      return [];
    }
  }

  /**
   * Prépare les données d'entraînement pour TensorFlow
   */
  private prepareTrainingData(interactions: any[]): { userIndices: number[], itemIndices: number[], ratings: number[] } {
    const userIndices: number[] = [];
    const itemIndices: number[] = [];
    const ratings: number[] = [];

    // Créer des mappages d'ID à indices
    const userIdToIndex = new Map<string, number>();
    const itemIdToIndex = new Map<string, number>();
    let userIndexCounter = 0;
    let itemIndexCounter = 0;

    // Traiter chaque interaction
    interactions.forEach(interaction => {
      // Obtenir ou créer l'index de l'utilisateur
      if (!userIdToIndex.has(interaction.userId)) {
        userIdToIndex.set(interaction.userId, userIndexCounter++);
      }
      const userIndex = userIdToIndex.get(interaction.userId);

      // Obtenir ou créer l'index de l'élément
      const itemKey = `${interaction.itemType}:${interaction.itemId}`;
      if (!itemIdToIndex.has(itemKey)) {
        itemIdToIndex.set(itemKey, itemIndexCounter++);
      }
      const itemIndex = itemIdToIndex.get(itemKey);

      // Normaliser la valeur d'interaction entre 0 et 1
      let rating = interaction.value;
      if (interaction.interactionType === 'VIEW') rating = 0.3;
      if (interaction.interactionType === 'CLICK') rating = 0.5;
      if (interaction.interactionType === 'LIKE') rating = 0.7;
      if (interaction.interactionType === 'BOOKMARK') rating = 0.8;
      if (interaction.interactionType === 'PURCHASE') rating = 1.0;

      // Ajouter aux tableaux
      userIndices.push(userIndex);
      itemIndices.push(itemIndex);
      ratings.push(rating);
    });

    // Stocker les mappages pour une utilisation ultérieure
    this.cacheService.set('userIdToIndex', userIdToIndex, 3600); // 1 heure
    this.cacheService.set('itemIdToIndex', itemIdToIndex, 3600); // 1 heure
    this.cacheService.set('userIndexToId', this.invertMap(userIdToIndex), 3600); // 1 heure
    this.cacheService.set('itemIndexToId', this.invertMap(itemIdToIndex), 3600); // 1 heure

    return { userIndices, itemIndices, ratings };
  }

  /**
   * Inverse une Map (clé -> valeur devient valeur -> clé)
   */
  private invertMap<K, V>(map: Map<K, V>): Map<V, K> {
    const invertedMap = new Map<V, K>();
    for (const [key, value] of map.entries()) {
      invertedMap.set(value, key);
    }
    return invertedMap;
  }

  /**
   * Obtient le nombre total d'utilisateurs
   */
  private async getUserCount(): Promise<number> {
    return this.prisma.user.count();
  }

  /**
   * Obtient le nombre total d'éléments (retraites, cours, etc.)
   */
  private async getItemCount(): Promise<number> {
    const retreatCount = await this.prisma.retreat.count();
    const courseCount = await this.prisma.course.count();
    const partnerCount = await this.prisma.partner.count();
    return retreatCount + courseCount + partnerCount;
  }

  /**
   * Génère des recommandations pour un utilisateur en utilisant le modèle d'apprentissage profond
   */
  public async getRecommendations(
    userId: string,
    limit: number = 10,
    itemType?: string,
    context?: any,
  ): Promise<RecommendationResult[]> {
    try {
      // Vérifier si l'utilisateur existe
      const user = await this.userService.findById(userId);
      if (!user) {
        throw new Error(`Utilisateur avec l'ID ${userId} non trouvé`);
      }

      // Récupérer les mappages
      const userIdToIndex = await this.cacheService.get<Map<string, number>>('userIdToIndex');
      const itemIndexToId = await this.cacheService.get<Map<number, string>>('itemIndexToId');

      if (!userIdToIndex || !itemIndexToId) {
        this.logger.warn('Mappages non trouvés dans le cache, utilisation de recommandations de repli');
        return this.getFallbackRecommendations(userId, limit, itemType);
      }

      // Vérifier si l'utilisateur est dans le modèle
      if (!userIdToIndex.has(userId)) {
        this.logger.warn(`Utilisateur ${userId} non trouvé dans le modèle, utilisation de recommandations de repli`);
        return this.getFallbackRecommendations(userId, limit, itemType);
      }

      // Obtenir l'index de l'utilisateur
      const userIndex = userIdToIndex.get(userId);

      // Prédire les scores pour tous les éléments
      const predictions = await this.tensorflowService.predictForUser(userIndex, itemIndexToId.size);

      // Convertir les prédictions en résultats
      const results: RecommendationResult[] = [];
      for (let itemIndex = 0; itemIndex < predictions.length; itemIndex++) {
        const score = predictions[itemIndex];
        
        // Ignorer les scores faibles
        if (score < 0.1) continue;

        // Récupérer l'ID de l'élément
        const itemKey = itemIndexToId.get(itemIndex);
        if (!itemKey) continue;

        // Extraire le type et l'ID de l'élément
        const [type, id] = itemKey.split(':');
        
        // Filtrer par type si spécifié
        if (itemType && type !== itemType) continue;

        // Ajouter aux résultats
        results.push({
          id,
          type,
          score,
          strategy: RecommendationStrategy.DEEP_LEARNING,
          explanation: `Recommandé par notre système d'apprentissage profond basé sur vos préférences`,
        });
      }

      // Trier par score et limiter le nombre de résultats
      return results
        .sort((a, b) => b.score - a.score)
        .slice(0, limit);
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des recommandations: ${error.message}`);
      return this.getFallbackRecommendations(userId, limit, itemType);
    }
  }

  /**
   * Génère des recommandations de repli en cas d'erreur
   */
  private async getFallbackRecommendations(
    userId: string,
    limit: number = 10,
    itemType?: string,
  ): Promise<RecommendationResult[]> {
    try {
      // Récupérer les éléments les plus populaires
      const popularItems = await this.prisma.$queryRaw`
        SELECT 
          CASE 
            WHEN r.id IS NOT NULL THEN 'RETREAT'
            WHEN c.id IS NOT NULL THEN 'COURSE'
            WHEN p.id IS NOT NULL THEN 'PARTNER'
          END as type,
          COALESCE(r.id, c.id, p.id) as id,
          COUNT(ui.id) as interactionCount
        FROM "UserInteraction" ui
        LEFT JOIN "Retreat" r ON ui."itemId" = r.id AND ui."itemType" = 'RETREAT'
        LEFT JOIN "Course" c ON ui."itemId" = c.id AND ui."itemType" = 'COURSE'
        LEFT JOIN "Partner" p ON ui."itemId" = p.id AND ui."itemType" = 'PARTNER'
        WHERE ui."interactionType" IN ('VIEW', 'LIKE', 'BOOKMARK', 'PURCHASE')
        ${itemType ? `AND ui."itemType" = ${itemType}` : ''}
        GROUP BY type, id
        ORDER BY interactionCount DESC
        LIMIT ${limit}
      `;

      // Convertir en résultats
      return popularItems.map(item => ({
        id: item.id,
        type: item.type,
        score: 0.5, // Score moyen pour les recommandations de repli
        strategy: RecommendationStrategy.POPULARITY,
        explanation: `Élément populaire parmi nos utilisateurs`,
      }));
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des recommandations de repli: ${error.message}`);
      return [];
    }
  }
}
