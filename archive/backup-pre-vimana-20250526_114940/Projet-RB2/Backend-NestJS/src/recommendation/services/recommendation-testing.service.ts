import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { RecommendationService } from './recommendation.service';
import { ContentBasedRecommendationService } from './content-based-recommendation.service';
import { CollaborativeFilteringService } from './collaborative-filtering.service';
import { DeepLearningRecommendationService } from './deep-learning-recommendation.service';
import { UserPreferencesService } from './user-preferences.service';
import { RecommendationMetricsService } from './recommendation-metrics.service';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { RecommendationResult } from '../interfaces/recommendation-result.interface';
import { SchedulerRegistry } from '@nestjs/schedule';
import { CronJob } from 'cron';

interface TestConfig {
  id: string;
  name: string;
  description: string;
  strategies: RecommendationStrategy[];
  userSegments: string[];
  metrics: string[];
  sampleSize: number;
  testDuration: number; // en jours
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  startedAt?: Date;
  completedAt?: Date;
}

interface TestResult {
  testId: string;
  strategy: RecommendationStrategy;
  userSegment: string;
  impressions: number;
  clicks: number;
  conversions: number;
  clickThroughRate: number;
  conversionRate: number;
  averageRelevanceScore: number;
  averageSatisfactionScore: number;
  diversityScore: number;
  noveltyScore: number;
  coverageScore: number;
  createdAt: Date;
}

interface TestRun {
  id: string;
  testId: string;
  startedAt: Date;
  completedAt?: Date;
  status: 'RUNNING' | 'COMPLETED' | 'FAILED';
  results: TestResult[];
  error?: string;
}

@Injectable()
export class RecommendationTestingService {
  private readonly logger = new Logger(RecommendationTestingService.name);
  private readonly activeTests: Map<string, TestRun> = new Map();

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly recommendationService: RecommendationService,
    private readonly contentBasedService: ContentBasedRecommendationService,
    private readonly collaborativeFilteringService: CollaborativeFilteringService,
    private readonly deepLearningService: DeepLearningRecommendationService,
    private readonly userPreferencesService: UserPreferencesService,
    private readonly metricsService: RecommendationMetricsService,
    private readonly schedulerRegistry: SchedulerRegistry,
  ) {
    this.initializeScheduledTests();
  }

  /**
   * Initialise les tests programmés
   */
  private async initializeScheduledTests(): Promise<void> {
    try {
      // Récupérer les tests actifs
      const activeTests = await this.prisma.recommendationTest.findMany({
        where: { isActive: true },
      });

      // Planifier les tests
      activeTests.forEach(test => {
        this.scheduleTest(test.id, test.name);
      });

      this.logger.log(`Initialized ${activeTests.length} scheduled tests`);
    } catch (error) {
      this.logger.error(`Error initializing scheduled tests: ${error.message}`);
    }
  }

  /**
   * Planifie un test de recommandation
   */
  private scheduleTest(testId: string, testName: string): void {
    try {
      const jobName = `recommendation-test-${testId}`;

      // Vérifier si le job existe déjà
      try {
        this.schedulerRegistry.getCronJob(jobName);
        this.logger.log(`Test ${testName} (${testId}) already scheduled`);
        return;
      } catch (e) {
        // Le job n'existe pas, on peut le créer
      }

      // Créer un job qui s'exécute tous les jours à minuit
      const job = new CronJob('0 0 * * *', () => {
        this.runTest(testId).catch(error => {
          this.logger.error(`Error running test ${testName} (${testId}): ${error.message}`);
        });
      });

      // Enregistrer le job
      this.schedulerRegistry.addCronJob(jobName, job);
      job.start();

      this.logger.log(`Scheduled test ${testName} (${testId})`);
    } catch (error) {
      this.logger.error(`Error scheduling test ${testName} (${testId}): ${error.message}`);
    }
  }

  /**
   * Crée un nouveau test de recommandation
   */
  async createTest(
    name: string,
    description: string,
    strategies: RecommendationStrategy[],
    userSegments: string[],
    metrics: string[],
    sampleSize: number,
    testDuration: number,
  ): Promise<TestConfig> {
    try {
      // Valider les stratégies
      if (!strategies || strategies.length === 0) {
        throw new Error('At least one strategy must be specified');
      }

      // Valider les segments d'utilisateurs
      if (!userSegments || userSegments.length === 0) {
        throw new Error('At least one user segment must be specified');
      }

      // Valider les métriques
      if (!metrics || metrics.length === 0) {
        throw new Error('At least one metric must be specified');
      }

      // Créer le test dans la base de données
      const test = await this.prisma.recommendationTest.create({
        data: {
          name,
          description,
          strategies: strategies as string[],
          userSegments,
          metrics,
          sampleSize,
          testDuration,
          isActive: true,
        },
      });

      // Planifier le test
      this.scheduleTest(test.id, test.name);

      return test as TestConfig;
    } catch (error) {
      this.logger.error(`Error creating recommendation test: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère un test par son ID
   */
  async getTestById(testId: string): Promise<TestConfig | null> {
    try {
      const test = await this.prisma.recommendationTest.findUnique({
        where: { id: testId },
      });

      return test as TestConfig;
    } catch (error) {
      this.logger.error(`Error getting test by ID: ${error.message}`);
      return null;
    }
  }

  /**
   * Récupère tous les tests
   */
  async getAllTests(): Promise<TestConfig[]> {
    try {
      const tests = await this.prisma.recommendationTest.findMany({
        orderBy: { createdAt: 'desc' },
      });

      return tests as TestConfig[];
    } catch (error) {
      this.logger.error(`Error getting all tests: ${error.message}`);
      return [];
    }
  }

  /**
   * Met à jour un test
   */
  async updateTest(
    testId: string,
    updates: Partial<TestConfig>,
  ): Promise<TestConfig | null> {
    try {
      // Vérifier si le test existe
      const existingTest = await this.getTestById(testId);
      if (!existingTest) {
        return null;
      }

      // Mettre à jour le test
      const updatedTest = await this.prisma.recommendationTest.update({
        where: { id: testId },
        data: updates,
      });

      // Si le test est actif, planifier ou déplanifier le test
      if (updates.isActive !== undefined) {
        const jobName = `recommendation-test-${testId}`;

        if (updates.isActive) {
          // Planifier le test s'il est actif
          this.scheduleTest(testId, updatedTest.name);
        } else {
          // Déplanifier le test s'il est inactif
          try {
            this.schedulerRegistry.deleteCronJob(jobName);
            this.logger.log(`Unscheduled test ${updatedTest.name} (${testId})`);
          } catch (e) {
            // Le job n'existe pas, rien à faire
          }
        }
      }

      return updatedTest as TestConfig;
    } catch (error) {
      this.logger.error(`Error updating test: ${error.message}`);
      return null;
    }
  }

  /**
   * Supprime un test
   */
  async deleteTest(testId: string): Promise<boolean> {
    try {
      // Vérifier si le test existe
      const existingTest = await this.getTestById(testId);
      if (!existingTest) {
        return false;
      }

      // Supprimer le test
      await this.prisma.recommendationTest.delete({
        where: { id: testId },
      });

      // Déplanifier le test
      const jobName = `recommendation-test-${testId}`;
      try {
        this.schedulerRegistry.deleteCronJob(jobName);
        this.logger.log(`Unscheduled test ${existingTest.name} (${testId})`);
      } catch (e) {
        // Le job n'existe pas, rien à faire
      }

      return true;
    } catch (error) {
      this.logger.error(`Error deleting test: ${error.message}`);
      return false;
    }
  }

  /**
   * Exécute un test de recommandation
   */
  async runTest(testId: string): Promise<TestRun> {
    try {
      // Vérifier si le test existe
      const test = await this.getTestById(testId);
      if (!test) {
        throw new Error(`Test with ID ${testId} not found`);
      }

      // Vérifier si le test est déjà en cours d'exécution
      if (this.activeTests.has(testId)) {
        throw new Error(`Test ${test.name} (${testId}) is already running`);
      }

      // Créer une nouvelle exécution de test
      const testRun: TestRun = {
        id: `${testId}-${Date.now()}`,
        testId,
        startedAt: new Date(),
        status: 'RUNNING',
        results: [],
      };

      // Enregistrer l'exécution de test
      this.activeTests.set(testId, testRun);

      // Mettre à jour le test dans la base de données
      await this.prisma.recommendationTest.update({
        where: { id: testId },
        data: {
          startedAt: testRun.startedAt,
        },
      });

      this.logger.log(`Started test ${test.name} (${testId})`);

      // Exécuter le test en arrière-plan
      this.executeTest(test, testRun).catch(error => {
        this.logger.error(`Error executing test ${test.name} (${testId}): ${error.message}`);
        testRun.status = 'FAILED';
        testRun.error = error.message;
        this.activeTests.delete(testId);
      });

      return testRun;
    } catch (error) {
      this.logger.error(`Error running test: ${error.message}`);
      throw error;
    }
  }

  /**
   * Exécute un test de recommandation en arrière-plan
   */
  private async executeTest(test: TestConfig, testRun: TestRun): Promise<void> {
    try {
      // Récupérer les utilisateurs pour le test
      const users = await this.getUsersForTest(test.userSegments, test.sampleSize);

      if (users.length === 0) {
        throw new Error('No users found for the specified segments');
      }

      this.logger.log(`Testing with ${users.length} users`);

      // Exécuter le test pour chaque stratégie et segment
      for (const strategy of test.strategies) {
        for (const segment of test.userSegments) {
          // Récupérer les utilisateurs du segment
          const segmentUsers = users.filter(user => this.getUserSegment(user) === segment);

          if (segmentUsers.length === 0) {
            this.logger.warn(`No users found for segment ${segment}`);
            continue;
          }

          // Générer des recommandations pour chaque utilisateur
          const recommendations: RecommendationResult[] = [];
          for (const user of segmentUsers) {
            const userRecommendations = await this.getRecommendationsForStrategy(
              user.id,
              strategy,
              10,
            );
            recommendations.push(...userRecommendations);
          }

          // Simuler des interactions avec les recommandations
          const simulatedInteractions = this.simulateInteractions(recommendations, segmentUsers);

          // Calculer les métriques
          const result = this.calculateMetrics(strategy, segment, recommendations, simulatedInteractions);

          // Ajouter le résultat
          testRun.results.push(result);

          // Enregistrer le résultat dans la base de données
          await this.prisma.recommendationTestResult.create({
            data: {
              testId: test.id,
              testRunId: testRun.id,
              strategy,
              userSegment: segment,
              impressions: result.impressions,
              clicks: result.clicks,
              conversions: result.conversions,
              clickThroughRate: result.clickThroughRate,
              conversionRate: result.conversionRate,
              averageRelevanceScore: result.averageRelevanceScore,
              averageSatisfactionScore: result.averageSatisfactionScore,
              diversityScore: result.diversityScore,
              noveltyScore: result.noveltyScore,
              coverageScore: result.coverageScore,
            },
          });

          this.logger.log(`Completed test for strategy ${strategy} and segment ${segment}`);
        }
      }

      // Marquer le test comme terminé
      testRun.status = 'COMPLETED';
      testRun.completedAt = new Date();
      this.activeTests.delete(test.id);

      // Mettre à jour le test dans la base de données
      await this.prisma.recommendationTest.update({
        where: { id: test.id },
        data: {
          completedAt: testRun.completedAt,
        },
      });

      // Enregistrer l'exécution de test dans la base de données
      await this.prisma.recommendationTestRun.create({
        data: {
          id: testRun.id,
          testId: test.id,
          startedAt: testRun.startedAt,
          completedAt: testRun.completedAt,
          status: testRun.status,
          error: testRun.error,
        },
      });

      this.logger.log(`Completed test ${test.name} (${test.id})`);
    } catch (error) {
      this.logger.error(`Error executing test: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les utilisateurs pour le test
   */
  private async getUsersForTest(segments: string[], sampleSize: number): Promise<any[]> {
    try {
      // Récupérer les utilisateurs pour chaque segment
      const users: any[] = [];

      for (const segment of segments) {
        let segmentUsers: any[] = [];

        switch (segment) {
          case 'NEW_USERS':
            segmentUsers = await this.prisma.user.findMany({
              where: {
                createdAt: {
                  gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 jours
                },
              },
              take: Math.ceil(sampleSize / segments.length),
            });
            break;
          case 'ACTIVE_USERS':
            segmentUsers = await this.prisma.user.findMany({
              where: {
                lastLoginAt: {
                  gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 jours
                },
              },
              take: Math.ceil(sampleSize / segments.length),
            });
            break;
          case 'INACTIVE_USERS':
            segmentUsers = await this.prisma.user.findMany({
              where: {
                lastLoginAt: {
                  lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 jours
                },
              },
              take: Math.ceil(sampleSize / segments.length),
            });
            break;
          case 'HIGH_ENGAGEMENT':
            // Utilisateurs avec beaucoup d'interactions
            const highEngagementUserIds = await this.prisma.userInteraction.groupBy({
              by: ['userId'],
              _count: {
                id: true,
              },
              having: {
                id: {
                  _count: {
                    gt: 10,
                  },
                },
              },
              take: Math.ceil(sampleSize / segments.length),
            });
            
            segmentUsers = await this.prisma.user.findMany({
              where: {
                id: {
                  in: highEngagementUserIds.map(u => u.userId),
                },
              },
            });
            break;
          case 'LOW_ENGAGEMENT':
            // Utilisateurs avec peu d'interactions
            const lowEngagementUserIds = await this.prisma.userInteraction.groupBy({
              by: ['userId'],
              _count: {
                id: true,
              },
              having: {
                id: {
                  _count: {
                    lte: 5,
                    gt: 0,
                  },
                },
              },
              take: Math.ceil(sampleSize / segments.length),
            });
            
            segmentUsers = await this.prisma.user.findMany({
              where: {
                id: {
                  in: lowEngagementUserIds.map(u => u.userId),
                },
              },
            });
            break;
          case 'ALL_USERS':
          default:
            segmentUsers = await this.prisma.user.findMany({
              take: Math.ceil(sampleSize / segments.length),
            });
            break;
        }

        users.push(...segmentUsers);
      }

      // Dédupliquer les utilisateurs
      const uniqueUsers = users.filter((user, index, self) =>
        index === self.findIndex(u => u.id === user.id)
      );

      return uniqueUsers;
    } catch (error) {
      this.logger.error(`Error getting users for test: ${error.message}`);
      return [];
    }
  }

  /**
   * Détermine le segment d'un utilisateur
   */
  private getUserSegment(user: any): string {
    // Vérifier si l'utilisateur est nouveau
    if (new Date(user.createdAt).getTime() > Date.now() - 30 * 24 * 60 * 60 * 1000) {
      return 'NEW_USERS';
    }

    // Vérifier si l'utilisateur est actif
    if (user.lastLoginAt && new Date(user.lastLoginAt).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000) {
      return 'ACTIVE_USERS';
    }

    // Vérifier si l'utilisateur est inactif
    if (!user.lastLoginAt || new Date(user.lastLoginAt).getTime() < Date.now() - 30 * 24 * 60 * 60 * 1000) {
      return 'INACTIVE_USERS';
    }

    // Par défaut, tous les utilisateurs
    return 'ALL_USERS';
  }

  /**
   * Récupère les recommandations pour une stratégie donnée
   */
  private async getRecommendationsForStrategy(
    userId: string,
    strategy: RecommendationStrategy,
    limit: number,
  ): Promise<RecommendationResult[]> {
    try {
      switch (strategy) {
        case RecommendationStrategy.CONTENT_BASED:
          return await this.contentBasedService.getRecommendations(userId, limit);
        case RecommendationStrategy.COLLABORATIVE_FILTERING:
          return await this.collaborativeFilteringService.getRecommendations(userId, limit);
        case RecommendationStrategy.DEEP_LEARNING:
          return await this.deepLearningService.getRecommendations(userId, limit);
        case RecommendationStrategy.HYBRID:
          return await this.recommendationService.getRecommendations(userId, limit);
        default:
          return await this.recommendationService.getRecommendations(userId, limit);
      }
    } catch (error) {
      this.logger.error(`Error getting recommendations for strategy ${strategy}: ${error.message}`);
      return [];
    }
  }

  /**
   * Simule des interactions avec les recommandations
   */
  private simulateInteractions(recommendations: RecommendationResult[], users: any[]): any[] {
    const interactions: any[] = [];

    // Pour chaque recommandation
    recommendations.forEach(recommendation => {
      // Simuler une impression (100% des recommandations sont vues)
      interactions.push({
        type: 'IMPRESSION',
        recommendation,
        userId: this.getRandomUser(users).id,
      });

      // Simuler un clic (20-40% des recommandations sont cliquées)
      if (Math.random() < 0.2 + (recommendation.score * 0.2)) {
        interactions.push({
          type: 'CLICK',
          recommendation,
          userId: this.getRandomUser(users).id,
        });

        // Simuler une conversion (5-15% des clics aboutissent à une conversion)
        if (Math.random() < 0.05 + (recommendation.score * 0.1)) {
          interactions.push({
            type: 'CONVERSION',
            recommendation,
            userId: this.getRandomUser(users).id,
          });
        }
      }
    });

    return interactions;
  }

  /**
   * Récupère un utilisateur aléatoire
   */
  private getRandomUser(users: any[]): any {
    return users[Math.floor(Math.random() * users.length)];
  }

  /**
   * Calcule les métriques pour un test
   */
  private calculateMetrics(
    strategy: RecommendationStrategy,
    segment: string,
    recommendations: RecommendationResult[],
    interactions: any[],
  ): TestResult {
    // Compter les impressions, clics et conversions
    const impressions = interactions.filter(i => i.type === 'IMPRESSION').length;
    const clicks = interactions.filter(i => i.type === 'CLICK').length;
    const conversions = interactions.filter(i => i.type === 'CONVERSION').length;

    // Calculer les taux
    const clickThroughRate = impressions > 0 ? clicks / impressions : 0;
    const conversionRate = clicks > 0 ? conversions / clicks : 0;

    // Calculer les scores moyens
    const averageRelevanceScore = recommendations.length > 0
      ? recommendations.reduce((sum, rec) => sum + rec.score, 0) / recommendations.length
      : 0;

    // Simuler un score de satisfaction (basé sur le score de pertinence)
    const averageSatisfactionScore = averageRelevanceScore * (0.8 + Math.random() * 0.4);

    // Calculer le score de diversité (basé sur la variété des catégories)
    const categories = new Set(recommendations.map(rec => rec.category).filter(Boolean));
    const diversityScore = recommendations.length > 0
      ? categories.size / Math.min(10, recommendations.length)
      : 0;

    // Calculer le score de nouveauté (simulé)
    const noveltyScore = 0.5 + Math.random() * 0.5;

    // Calculer le score de couverture (simulé)
    const coverageScore = 0.4 + Math.random() * 0.6;

    return {
      testId: '',
      strategy,
      userSegment: segment,
      impressions,
      clicks,
      conversions,
      clickThroughRate,
      conversionRate,
      averageRelevanceScore,
      averageSatisfactionScore,
      diversityScore,
      noveltyScore,
      coverageScore,
      createdAt: new Date(),
    };
  }

  /**
   * Récupère les résultats d'un test
   */
  async getTestResults(testId: string): Promise<TestResult[]> {
    try {
      const results = await this.prisma.recommendationTestResult.findMany({
        where: { testId },
        orderBy: { createdAt: 'desc' },
      });

      return results as TestResult[];
    } catch (error) {
      this.logger.error(`Error getting test results: ${error.message}`);
      return [];
    }
  }

  /**
   * Récupère les exécutions d'un test
   */
  async getTestRuns(testId: string): Promise<TestRun[]> {
    try {
      const runs = await this.prisma.recommendationTestRun.findMany({
        where: { testId },
        orderBy: { startedAt: 'desc' },
      });

      return runs as TestRun[];
    } catch (error) {
      this.logger.error(`Error getting test runs: ${error.message}`);
      return [];
    }
  }

  /**
   * Récupère le statut d'un test en cours d'exécution
   */
  getTestRunStatus(testId: string): TestRun | null {
    return this.activeTests.get(testId) || null;
  }
}
