import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { RecommendationResult } from '../interfaces/recommendation-result.interface';
import { CacheService } from '../../shared/services/cache.service';
import { RecommendationMetricsService } from './recommendation-metrics.service';

interface ABTest {
  id: string;
  name: string;
  description: string;
  strategies: RecommendationStrategy[];
  weights: number[];
  startDate: Date;
  endDate: Date | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface ABTestAssignment {
  userId: string;
  testId: string;
  strategy: RecommendationStrategy;
  assignedAt: Date;
}

interface ABTestMetrics {
  testId: string;
  strategy: RecommendationStrategy;
  impressions: number;
  clicks: number;
  conversions: number;
  revenue: number;
  updatedAt: Date;
}

@Injectable()
export class ABTestingService {
  private readonly logger = new Logger(ABTestingService.name);
  private readonly cacheKeyPrefix: string = 'ab_test_';
  private readonly cacheAssignmentPrefix: string = 'ab_test_assignment_';
  private readonly cacheTTL: number;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly cacheService: CacheService,
    private readonly metricsService: RecommendationMetricsService,
  ) {
    this.cacheTTL = this.configService.get<number>('AB_TEST_CACHE_TTL', 3600); // 1 heure par défaut
  }

  /**
   * Crée un nouveau test A/B
   */
  async createTest(
    name: string,
    description: string,
    strategies: RecommendationStrategy[],
    weights?: number[],
    startDate?: Date,
    endDate?: Date,
  ): Promise<ABTest> {
    try {
      // Valider les stratégies
      if (!strategies || strategies.length < 2) {
        throw new Error('Un test A/B doit avoir au moins deux stratégies');
      }

      // Normaliser les poids si fournis, sinon utiliser des poids égaux
      let normalizedWeights: number[];
      if (weights && weights.length === strategies.length) {
        const sum = weights.reduce((a, b) => a + b, 0);
        normalizedWeights = weights.map(w => w / sum);
      } else {
        // Poids égaux pour toutes les stratégies
        normalizedWeights = Array(strategies.length).fill(1 / strategies.length);
      }

      // Créer le test dans la base de données
      const test = await this.prisma.aBTest.create({
        data: {
          name,
          description,
          strategies: strategies as string[],
          weights: normalizedWeights,
          startDate: startDate || new Date(),
          endDate,
          isActive: true,
        },
      });

      // Mettre en cache le test
      await this.cacheService.set(`${this.cacheKeyPrefix}${test.id}`, test, this.cacheTTL);

      return test as ABTest;
    } catch (error) {
      this.logger.error(`Erreur lors de la création du test A/B: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère un test A/B par son ID
   */
  async getTestById(testId: string): Promise<ABTest | null> {
    try {
      // Vérifier le cache d'abord
      const cachedTest = await this.cacheService.get<ABTest>(`${this.cacheKeyPrefix}${testId}`);
      if (cachedTest) {
        return cachedTest;
      }

      // Récupérer depuis la base de données
      const test = await this.prisma.aBTest.findUnique({
        where: { id: testId },
      });

      if (!test) {
        return null;
      }

      // Mettre en cache
      await this.cacheService.set(`${this.cacheKeyPrefix}${test.id}`, test as ABTest, this.cacheTTL);

      return test as ABTest;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération du test A/B: ${error.message}`);
      return null;
    }
  }

  /**
   * Récupère tous les tests A/B actifs
   */
  async getActiveTests(): Promise<ABTest[]> {
    try {
      const tests = await this.prisma.aBTest.findMany({
        where: {
          isActive: true,
          startDate: { lte: new Date() },
          OR: [
            { endDate: null },
            { endDate: { gte: new Date() } },
          ],
        },
      });

      return tests as ABTest[];
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des tests A/B actifs: ${error.message}`);
      return [];
    }
  }

  /**
   * Met à jour un test A/B
   */
  async updateTest(
    testId: string,
    updates: Partial<ABTest>,
  ): Promise<ABTest | null> {
    try {
      // Vérifier si le test existe
      const existingTest = await this.getTestById(testId);
      if (!existingTest) {
        return null;
      }

      // Mettre à jour le test
      const updatedTest = await this.prisma.aBTest.update({
        where: { id: testId },
        data: updates,
      });

      // Mettre à jour le cache
      await this.cacheService.set(`${this.cacheKeyPrefix}${testId}`, updatedTest as ABTest, this.cacheTTL);

      return updatedTest as ABTest;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du test A/B: ${error.message}`);
      return null;
    }
  }

  /**
   * Termine un test A/B
   */
  async endTest(testId: string): Promise<ABTest | null> {
    try {
      return await this.updateTest(testId, {
        isActive: false,
        endDate: new Date(),
      });
    } catch (error) {
      this.logger.error(`Erreur lors de la fin du test A/B: ${error.message}`);
      return null;
    }
  }

  /**
   * Assigne une stratégie à un utilisateur pour un test A/B
   */
  async assignStrategy(userId: string, testId: string): Promise<RecommendationStrategy> {
    try {
      // Vérifier si l'utilisateur a déjà une assignation pour ce test
      const cacheKey = `${this.cacheAssignmentPrefix}${userId}_${testId}`;
      const cachedAssignment = await this.cacheService.get<ABTestAssignment>(cacheKey);
      
      if (cachedAssignment) {
        return cachedAssignment.strategy;
      }

      // Récupérer l'assignation depuis la base de données
      const existingAssignment = await this.prisma.aBTestAssignment.findUnique({
        where: {
          userId_testId: {
            userId,
            testId,
          },
        },
      });

      if (existingAssignment) {
        // Mettre en cache
        await this.cacheService.set(cacheKey, existingAssignment as ABTestAssignment, this.cacheTTL);
        return existingAssignment.strategy as RecommendationStrategy;
      }

      // Récupérer le test
      const test = await this.getTestById(testId);
      if (!test || !test.isActive) {
        throw new Error(`Test A/B ${testId} non trouvé ou inactif`);
      }

      // Sélectionner une stratégie en fonction des poids
      const strategy = this.selectStrategyByWeight(test.strategies as RecommendationStrategy[], test.weights);

      // Créer l'assignation
      const assignment = await this.prisma.aBTestAssignment.create({
        data: {
          userId,
          testId,
          strategy,
        },
      });

      // Mettre en cache
      await this.cacheService.set(cacheKey, assignment as ABTestAssignment, this.cacheTTL);

      return strategy;
    } catch (error) {
      this.logger.error(`Erreur lors de l'assignation de stratégie: ${error.message}`);
      // En cas d'erreur, retourner une stratégie par défaut
      return RecommendationStrategy.COLLABORATIVE_FILTERING;
    }
  }

  /**
   * Sélectionne une stratégie en fonction des poids
   */
  private selectStrategyByWeight(
    strategies: RecommendationStrategy[],
    weights: number[],
  ): RecommendationStrategy {
    // Générer un nombre aléatoire entre 0 et 1
    const random = Math.random();
    
    // Calculer les poids cumulatifs
    let cumulativeWeight = 0;
    
    for (let i = 0; i < strategies.length; i++) {
      cumulativeWeight += weights[i];
      if (random <= cumulativeWeight) {
        return strategies[i];
      }
    }
    
    // Fallback au cas où (ne devrait jamais arriver si les poids sont normalisés)
    return strategies[0];
  }

  /**
   * Enregistre une impression pour une recommandation
   */
  async trackImpression(
    userId: string,
    testId: string,
    strategy: RecommendationStrategy,
    recommendationId: string,
  ): Promise<void> {
    try {
      // Enregistrer l'impression
      await this.prisma.aBTestImpression.create({
        data: {
          userId,
          testId,
          strategy,
          recommendationId,
        },
      });

      // Mettre à jour les métriques
      await this.updateMetrics(testId, strategy, 'impressions');
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de l'impression: ${error.message}`);
    }
  }

  /**
   * Enregistre un clic pour une recommandation
   */
  async trackClick(
    userId: string,
    testId: string,
    strategy: RecommendationStrategy,
    recommendationId: string,
  ): Promise<void> {
    try {
      // Enregistrer le clic
      await this.prisma.aBTestClick.create({
        data: {
          userId,
          testId,
          strategy,
          recommendationId,
        },
      });

      // Mettre à jour les métriques
      await this.updateMetrics(testId, strategy, 'clicks');
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement du clic: ${error.message}`);
    }
  }

  /**
   * Enregistre une conversion pour une recommandation
   */
  async trackConversion(
    userId: string,
    testId: string,
    strategy: RecommendationStrategy,
    recommendationId: string,
    amount?: number,
  ): Promise<void> {
    try {
      // Enregistrer la conversion
      await this.prisma.aBTestConversion.create({
        data: {
          userId,
          testId,
          strategy,
          recommendationId,
          amount: amount || 0,
        },
      });

      // Mettre à jour les métriques
      await this.updateMetrics(testId, strategy, 'conversions');
      if (amount) {
        await this.updateMetrics(testId, strategy, 'revenue', amount);
      }
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de la conversion: ${error.message}`);
    }
  }

  /**
   * Met à jour les métriques d'un test A/B
   */
  private async updateMetrics(
    testId: string,
    strategy: RecommendationStrategy,
    metricType: 'impressions' | 'clicks' | 'conversions' | 'revenue',
    value: number = 1,
  ): Promise<void> {
    try {
      // Récupérer les métriques existantes
      const existingMetrics = await this.prisma.aBTestMetrics.findUnique({
        where: {
          testId_strategy: {
            testId,
            strategy,
          },
        },
      });

      if (existingMetrics) {
        // Mettre à jour les métriques existantes
        await this.prisma.aBTestMetrics.update({
          where: {
            testId_strategy: {
              testId,
              strategy,
            },
          },
          data: {
            [metricType]: {
              increment: value,
            },
            updatedAt: new Date(),
          },
        });
      } else {
        // Créer de nouvelles métriques
        await this.prisma.aBTestMetrics.create({
          data: {
            testId,
            strategy,
            impressions: metricType === 'impressions' ? value : 0,
            clicks: metricType === 'clicks' ? value : 0,
            conversions: metricType === 'conversions' ? value : 0,
            revenue: metricType === 'revenue' ? value : 0,
          },
        });
      }
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour des métriques: ${error.message}`);
    }
  }

  /**
   * Récupère les métriques d'un test A/B
   */
  async getTestMetrics(testId: string): Promise<ABTestMetrics[]> {
    try {
      const metrics = await this.prisma.aBTestMetrics.findMany({
        where: { testId },
      });

      return metrics as ABTestMetrics[];
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des métriques: ${error.message}`);
      return [];
    }
  }

  /**
   * Calcule les taux de conversion pour un test A/B
   */
  async getConversionRates(testId: string): Promise<Record<string, { ctr: number; cvr: number; revenue: number }>> {
    try {
      const metrics = await this.getTestMetrics(testId);
      
      const result: Record<string, { ctr: number; cvr: number; revenue: number }> = {};
      
      metrics.forEach(metric => {
        const ctr = metric.impressions > 0 ? (metric.clicks / metric.impressions) * 100 : 0;
        const cvr = metric.clicks > 0 ? (metric.conversions / metric.clicks) * 100 : 0;
        
        result[metric.strategy] = {
          ctr,
          cvr,
          revenue: metric.revenue,
        };
      });
      
      return result;
    } catch (error) {
      this.logger.error(`Erreur lors du calcul des taux de conversion: ${error.message}`);
      return {};
    }
  }

  /**
   * Détermine la stratégie gagnante d'un test A/B
   */
  async determineWinner(testId: string): Promise<RecommendationStrategy | null> {
    try {
      const conversionRates = await this.getConversionRates(testId);
      
      if (Object.keys(conversionRates).length === 0) {
        return null;
      }
      
      // Trouver la stratégie avec le meilleur taux de conversion
      let bestStrategy: RecommendationStrategy | null = null;
      let bestCVR = -1;
      
      Object.entries(conversionRates).forEach(([strategy, rates]) => {
        if (rates.cvr > bestCVR) {
          bestCVR = rates.cvr;
          bestStrategy = strategy as RecommendationStrategy;
        }
      });
      
      return bestStrategy;
    } catch (error) {
      this.logger.error(`Erreur lors de la détermination du gagnant: ${error.message}`);
      return null;
    }
  }

  /**
   * Applique la stratégie de recommandation en fonction des tests A/B
   */
  async applyTestStrategy(
    userId: string,
    recommendations: RecommendationResult[],
    testId?: string,
  ): Promise<RecommendationResult[]> {
    try {
      // Si aucun test n'est spécifié, récupérer le premier test actif
      if (!testId) {
        const activeTests = await this.getActiveTests();
        if (activeTests.length === 0) {
          return recommendations;
        }
        testId = activeTests[0].id;
      }

      // Assigner une stratégie à l'utilisateur
      const strategy = await this.assignStrategy(userId, testId);

      // Filtrer les recommandations pour ne garder que celles de la stratégie assignée
      const filteredRecommendations = recommendations.filter(rec => rec.strategy === strategy);

      // Si aucune recommandation ne correspond à la stratégie, retourner toutes les recommandations
      if (filteredRecommendations.length === 0) {
        return recommendations;
      }

      // Enregistrer les impressions
      filteredRecommendations.forEach(rec => {
        this.trackImpression(userId, testId, strategy, rec.id);
      });

      return filteredRecommendations;
    } catch (error) {
      this.logger.error(`Erreur lors de l'application de la stratégie de test: ${error.message}`);
      return recommendations;
    }
  }
}
