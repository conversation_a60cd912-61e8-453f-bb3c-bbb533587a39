import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { CacheService } from '../../shared/services/cache.service';
import { UserInteractionService } from './user-interaction.service';
import { ExternalDataService } from './external-data.service';

interface UserPreference {
  userId: string;
  category: string;
  weight: number;
}

interface UserInterest {
  userId: string;
  interest: string;
  type: 'CATEGORY' | 'TAG' | 'KEYWORD';
  source: 'EXPLICIT' | 'IMPLICIT' | 'INFERRED';
  weight: number;
}

interface PersonalizationSettings {
  userId: string;
  enablePersonalization: boolean;
  diversityLevel: number; // 0-100, où 100 est le plus diversifié
  noveltyLevel: number; // 0-100, où 100 favorise le plus les nouveaux contenus
  contentFilters: {
    excludedCategories: string[];
    excludedTags: string[];
    contentTypes: string[];
    minContentRating: number; // 0-5
    languagePreferences: string[];
  };
  privacySettings: {
    allowInteractionTracking: boolean;
    allowContentAnalysis: boolean;
    allowThirdPartyData: boolean;
    dataRetentionPeriod: number; // en jours
  };
}

@Injectable()
export class UserPreferencesService {
  private readonly logger = new Logger(UserPreferencesService.name);
  private readonly cacheTTL: number;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly cacheService: CacheService,
    private readonly userInteractionService: UserInteractionService,
    private readonly externalDataService: ExternalDataService,
  ) {
    this.cacheTTL = this.configService.get<number>('USER_PREFERENCES_CACHE_TTL', 3600); // 1 heure par défaut
  }

  /**
   * Récupère les préférences d'un utilisateur
   */
  async getUserPreferences(userId: string): Promise<UserPreference[]> {
    try {
      // Vérifier le cache d'abord
      const cacheKey = `user_preferences_${userId}`;
      const cachedPreferences = await this.cacheService.get<UserPreference[]>(cacheKey);
      
      if (cachedPreferences) {
        return cachedPreferences;
      }
      
      // Récupérer les préférences explicites
      const explicitPreferences = await this.prisma.userPreference.findMany({
        where: { userId },
      });
      
      // Récupérer les préférences implicites basées sur les interactions
      const implicitPreferences = await this.getImplicitPreferences(userId);
      
      // Fusionner les préférences
      const mergedPreferences = this.mergePreferences(explicitPreferences, implicitPreferences);
      
      // Mettre en cache les résultats
      await this.cacheService.set(cacheKey, mergedPreferences, this.cacheTTL);
      
      return mergedPreferences;
    } catch (error) {
      this.logger.error(`Error getting user preferences: ${error.message}`);
      return [];
    }
  }

  /**
   * Récupère les préférences implicites basées sur les interactions
   */
  private async getImplicitPreferences(userId: string): Promise<UserPreference[]> {
    try {
      // Récupérer les interactions de l'utilisateur
      const interactions = await this.userInteractionService.getUserInteractions(userId, 100);
      
      // Extraire les catégories des interactions
      const categoryWeights: Record<string, number> = {};
      
      interactions.forEach(interaction => {
        let categories: string[] = [];
        
        // Extraire les catégories selon le type d'élément
        if (interaction.itemType === 'RETREAT' && interaction.retreat) {
          categories = interaction.retreat.categories || [];
        } else if (interaction.itemType === 'COURSE' && interaction.course) {
          categories = interaction.course.categories || [];
        } else if (interaction.itemType === 'PARTNER' && interaction.partner) {
          categories = interaction.partner.categories || [];
        }
        
        // Calculer le poids de l'interaction
        let weight = 0;
        switch (interaction.interactionType) {
          case 'VIEW':
            weight = 0.2;
            break;
          case 'CLICK':
            weight = 0.5;
            break;
          case 'LIKE':
            weight = 1.0;
            break;
          case 'BOOKMARK':
            weight = 1.5;
            break;
          case 'PURCHASE':
            weight = 2.0;
            break;
          default:
            weight = 0.1;
        }
        
        // Appliquer un facteur de décroissance temporelle
        const ageInDays = (Date.now() - new Date(interaction.createdAt).getTime()) / (1000 * 60 * 60 * 24);
        const timeFactor = Math.exp(-ageInDays / 30); // Décroissance exponentielle sur 30 jours
        weight *= timeFactor;
        
        // Mettre à jour les poids des catégories
        categories.forEach(category => {
          if (!categoryWeights[category]) {
            categoryWeights[category] = 0;
          }
          categoryWeights[category] += weight;
        });
      });
      
      // Convertir en tableau de préférences
      const preferences: UserPreference[] = Object.entries(categoryWeights).map(([category, weight]) => ({
        userId,
        category,
        weight,
      }));
      
      // Normaliser les poids
      const maxWeight = Math.max(...preferences.map(p => p.weight), 1);
      return preferences.map(p => ({
        ...p,
        weight: p.weight / maxWeight,
      }));
    } catch (error) {
      this.logger.error(`Error getting implicit preferences: ${error.message}`);
      return [];
    }
  }

  /**
   * Fusionne les préférences explicites et implicites
   */
  private mergePreferences(
    explicitPreferences: any[],
    implicitPreferences: UserPreference[],
  ): UserPreference[] {
    const mergedPreferences: Record<string, UserPreference> = {};
    
    // Ajouter les préférences implicites
    implicitPreferences.forEach(pref => {
      mergedPreferences[pref.category] = {
        ...pref,
        weight: pref.weight * 0.7, // Les préférences implicites ont un poids de 70%
      };
    });
    
    // Ajouter ou mettre à jour avec les préférences explicites
    explicitPreferences.forEach(pref => {
      const category = pref.category;
      if (mergedPreferences[category]) {
        // Combiner avec la préférence implicite existante
        mergedPreferences[category].weight = (
          mergedPreferences[category].weight * 0.3 + // 30% du poids implicite
          pref.weight * 0.7 // 70% du poids explicite
        );
      } else {
        // Ajouter la préférence explicite
        mergedPreferences[category] = {
          userId: pref.userId,
          category,
          weight: pref.weight,
        };
      }
    });
    
    // Convertir en tableau et trier par poids décroissant
    return Object.values(mergedPreferences).sort((a, b) => b.weight - a.weight);
  }

  /**
   * Met à jour les préférences explicites d'un utilisateur
   */
  async updateUserPreferences(
    userId: string,
    preferences: { category: string; weight: number }[],
  ): Promise<UserPreference[]> {
    try {
      // Utiliser une transaction pour garantir l'atomicité
      await this.prisma.$transaction(async (prisma) => {
        // Supprimer les préférences existantes
        await prisma.userPreference.deleteMany({
          where: { userId },
        });
        
        // Ajouter les nouvelles préférences
        for (const pref of preferences) {
          await prisma.userPreference.create({
            data: {
              userId,
              category: pref.category,
              weight: pref.weight,
            },
          });
        }
      });
      
      // Invalider le cache
      const cacheKey = `user_preferences_${userId}`;
      await this.cacheService.delete(cacheKey);
      
      // Récupérer les préférences mises à jour
      return this.getUserPreferences(userId);
    } catch (error) {
      this.logger.error(`Error updating user preferences: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les intérêts d'un utilisateur
   */
  async getUserInterests(userId: string): Promise<UserInterest[]> {
    try {
      // Vérifier le cache d'abord
      const cacheKey = `user_interests_${userId}`;
      const cachedInterests = await this.cacheService.get<UserInterest[]>(cacheKey);
      
      if (cachedInterests) {
        return cachedInterests;
      }
      
      // Récupérer les intérêts explicites
      const explicitInterests = await this.prisma.userInterest.findMany({
        where: {
          userId,
          source: 'EXPLICIT',
        },
      });
      
      // Récupérer les intérêts implicites
      const implicitInterests = await this.prisma.userInterest.findMany({
        where: {
          userId,
          source: 'IMPLICIT',
        },
      });
      
      // Inférer des intérêts supplémentaires
      const inferredInterests = await this.inferUserInterests(userId);
      
      // Fusionner tous les intérêts
      const allInterests = [
        ...explicitInterests,
        ...implicitInterests,
        ...inferredInterests,
      ];
      
      // Dédupliquer les intérêts
      const interestMap: Record<string, UserInterest> = {};
      allInterests.forEach(interest => {
        const key = `${interest.type}:${interest.interest}`;
        if (!interestMap[key] || interestMap[key].weight < interest.weight) {
          interestMap[key] = interest;
        }
      });
      
      const interests = Object.values(interestMap);
      
      // Mettre en cache les résultats
      await this.cacheService.set(cacheKey, interests, this.cacheTTL);
      
      return interests;
    } catch (error) {
      this.logger.error(`Error getting user interests: ${error.message}`);
      return [];
    }
  }

  /**
   * Infère des intérêts supplémentaires pour l'utilisateur
   */
  private async inferUserInterests(userId: string): Promise<UserInterest[]> {
    try {
      // Récupérer les interactions récentes
      const interactions = await this.userInteractionService.getUserInteractions(userId, 50);
      
      // Extraire le texte des éléments avec lesquels l'utilisateur a interagi
      let combinedText = '';
      
      interactions.forEach(interaction => {
        if (interaction.itemType === 'RETREAT' && interaction.retreat) {
          combinedText += ` ${interaction.retreat.title || ''} ${interaction.retreat.description || ''}`;
        } else if (interaction.itemType === 'COURSE' && interaction.course) {
          combinedText += ` ${interaction.course.title || ''} ${interaction.course.description || ''}`;
        } else if (interaction.itemType === 'PARTNER' && interaction.partner) {
          combinedText += ` ${interaction.partner.name || ''} ${interaction.partner.description || ''}`;
        }
      });
      
      // Analyser le texte pour extraire des mots-clés
      const keywords = this.externalDataService.analyzeContent(combinedText, 20);
      
      // Convertir en intérêts
      return keywords.map((keyword, index) => ({
        userId,
        interest: keyword,
        type: 'KEYWORD',
        source: 'INFERRED',
        weight: 1 - (index * 0.05), // Poids décroissant selon l'ordre
      }));
    } catch (error) {
      this.logger.error(`Error inferring user interests: ${error.message}`);
      return [];
    }
  }

  /**
   * Met à jour les intérêts explicites d'un utilisateur
   */
  async updateUserInterests(
    userId: string,
    interests: { interest: string; type: 'CATEGORY' | 'TAG' | 'KEYWORD'; weight: number }[],
  ): Promise<UserInterest[]> {
    try {
      // Utiliser une transaction pour garantir l'atomicité
      await this.prisma.$transaction(async (prisma) => {
        // Supprimer les intérêts explicites existants
        await prisma.userInterest.deleteMany({
          where: {
            userId,
            source: 'EXPLICIT',
          },
        });
        
        // Ajouter les nouveaux intérêts
        for (const interest of interests) {
          await prisma.userInterest.create({
            data: {
              userId,
              interest: interest.interest,
              type: interest.type,
              source: 'EXPLICIT',
              weight: interest.weight,
            },
          });
        }
      });
      
      // Invalider le cache
      const cacheKey = `user_interests_${userId}`;
      await this.cacheService.delete(cacheKey);
      
      // Récupérer les intérêts mis à jour
      return this.getUserInterests(userId);
    } catch (error) {
      this.logger.error(`Error updating user interests: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les paramètres de personnalisation d'un utilisateur
   */
  async getPersonalizationSettings(userId: string): Promise<PersonalizationSettings> {
    try {
      // Vérifier le cache d'abord
      const cacheKey = `personalization_settings_${userId}`;
      const cachedSettings = await this.cacheService.get<PersonalizationSettings>(cacheKey);
      
      if (cachedSettings) {
        return cachedSettings;
      }
      
      // Récupérer les paramètres depuis la base de données
      const settings = await this.prisma.personalizationSettings.findUnique({
        where: { userId },
      });
      
      // Si aucun paramètre n'existe, créer des paramètres par défaut
      if (!settings) {
        return this.createDefaultPersonalizationSettings(userId);
      }
      
      // Convertir en format interne
      const personalizationSettings: PersonalizationSettings = {
        userId,
        enablePersonalization: settings.enablePersonalization,
        diversityLevel: settings.diversityLevel,
        noveltyLevel: settings.noveltyLevel,
        contentFilters: settings.contentFilters as any,
        privacySettings: settings.privacySettings as any,
      };
      
      // Mettre en cache les résultats
      await this.cacheService.set(cacheKey, personalizationSettings, this.cacheTTL);
      
      return personalizationSettings;
    } catch (error) {
      this.logger.error(`Error getting personalization settings: ${error.message}`);
      return this.createDefaultPersonalizationSettings(userId);
    }
  }

  /**
   * Crée des paramètres de personnalisation par défaut
   */
  private async createDefaultPersonalizationSettings(userId: string): Promise<PersonalizationSettings> {
    try {
      const defaultSettings: PersonalizationSettings = {
        userId,
        enablePersonalization: true,
        diversityLevel: 50,
        noveltyLevel: 50,
        contentFilters: {
          excludedCategories: [],
          excludedTags: [],
          contentTypes: ['RETREAT', 'COURSE', 'PARTNER'],
          minContentRating: 0,
          languagePreferences: ['en'],
        },
        privacySettings: {
          allowInteractionTracking: true,
          allowContentAnalysis: true,
          allowThirdPartyData: false,
          dataRetentionPeriod: 365,
        },
      };
      
      // Enregistrer les paramètres par défaut dans la base de données
      await this.prisma.personalizationSettings.create({
        data: {
          userId,
          enablePersonalization: defaultSettings.enablePersonalization,
          diversityLevel: defaultSettings.diversityLevel,
          noveltyLevel: defaultSettings.noveltyLevel,
          contentFilters: defaultSettings.contentFilters as any,
          privacySettings: defaultSettings.privacySettings as any,
        },
      });
      
      return defaultSettings;
    } catch (error) {
      this.logger.error(`Error creating default personalization settings: ${error.message}`);
      
      // Retourner les paramètres par défaut même en cas d'erreur
      return {
        userId,
        enablePersonalization: true,
        diversityLevel: 50,
        noveltyLevel: 50,
        contentFilters: {
          excludedCategories: [],
          excludedTags: [],
          contentTypes: ['RETREAT', 'COURSE', 'PARTNER'],
          minContentRating: 0,
          languagePreferences: ['en'],
        },
        privacySettings: {
          allowInteractionTracking: true,
          allowContentAnalysis: true,
          allowThirdPartyData: false,
          dataRetentionPeriod: 365,
        },
      };
    }
  }

  /**
   * Met à jour les paramètres de personnalisation d'un utilisateur
   */
  async updatePersonalizationSettings(
    userId: string,
    settings: Partial<PersonalizationSettings>,
  ): Promise<PersonalizationSettings> {
    try {
      // Récupérer les paramètres actuels
      const currentSettings = await this.getPersonalizationSettings(userId);
      
      // Fusionner avec les nouveaux paramètres
      const updatedSettings = {
        ...currentSettings,
        ...settings,
        contentFilters: {
          ...currentSettings.contentFilters,
          ...(settings.contentFilters || {}),
        },
        privacySettings: {
          ...currentSettings.privacySettings,
          ...(settings.privacySettings || {}),
        },
      };
      
      // Mettre à jour dans la base de données
      await this.prisma.personalizationSettings.upsert({
        where: { userId },
        update: {
          enablePersonalization: updatedSettings.enablePersonalization,
          diversityLevel: updatedSettings.diversityLevel,
          noveltyLevel: updatedSettings.noveltyLevel,
          contentFilters: updatedSettings.contentFilters as any,
          privacySettings: updatedSettings.privacySettings as any,
        },
        create: {
          userId,
          enablePersonalization: updatedSettings.enablePersonalization,
          diversityLevel: updatedSettings.diversityLevel,
          noveltyLevel: updatedSettings.noveltyLevel,
          contentFilters: updatedSettings.contentFilters as any,
          privacySettings: updatedSettings.privacySettings as any,
        },
      });
      
      // Invalider le cache
      const cacheKey = `personalization_settings_${userId}`;
      await this.cacheService.delete(cacheKey);
      
      return updatedSettings;
    } catch (error) {
      this.logger.error(`Error updating personalization settings: ${error.message}`);
      throw error;
    }
  }

  /**
   * Applique les paramètres de personnalisation aux recommandations
   */
  async applyPersonalization(
    userId: string,
    recommendations: any[],
    options?: {
      diversityBoost?: number; // 0-1, augmente la diversité
      noveltyBoost?: number; // 0-1, augmente la nouveauté
      limit?: number; // Nombre maximum de recommandations à retourner
    },
  ): Promise<any[]> {
    try {
      // Récupérer les paramètres de personnalisation
      const settings = await this.getPersonalizationSettings(userId);
      
      // Si la personnalisation est désactivée, retourner les recommandations telles quelles
      if (!settings.enablePersonalization) {
        return recommendations;
      }
      
      // Récupérer les préférences de l'utilisateur
      const preferences = await this.getUserPreferences(userId);
      
      // Récupérer les intérêts de l'utilisateur
      const interests = await this.getUserInterests(userId);
      
      // Appliquer les filtres de contenu
      let filteredRecommendations = recommendations.filter(item => {
        // Filtrer par catégories exclues
        if (settings.contentFilters.excludedCategories.length > 0) {
          const itemCategories = item.categories || [];
          if (itemCategories.some(category => 
            settings.contentFilters.excludedCategories.includes(category)
          )) {
            return false;
          }
        }
        
        // Filtrer par tags exclus
        if (settings.contentFilters.excludedTags.length > 0) {
          const itemTags = item.tags || [];
          if (itemTags.some(tag => 
            settings.contentFilters.excludedTags.includes(tag)
          )) {
            return false;
          }
        }
        
        // Filtrer par type de contenu
        if (settings.contentFilters.contentTypes.length > 0) {
          if (!settings.contentFilters.contentTypes.includes(item.type)) {
            return false;
          }
        }
        
        // Filtrer par note minimale
        if (settings.contentFilters.minContentRating > 0) {
          if ((item.rating || 0) < settings.contentFilters.minContentRating) {
            return false;
          }
        }
        
        // Filtrer par préférence de langue
        if (settings.contentFilters.languagePreferences.length > 0) {
          if (item.language && !settings.contentFilters.languagePreferences.includes(item.language)) {
            return false;
          }
        }
        
        return true;
      });
      
      // Calculer les scores personnalisés
      const scoredRecommendations = filteredRecommendations.map(item => {
        // Score de base (déjà calculé par le système de recommandation)
        let score = item.score || 0.5;
        
        // Score basé sur les préférences
        const preferenceScore = this.calculatePreferenceScore(item, preferences);
        
        // Score basé sur les intérêts
        const interestScore = this.calculateInterestScore(item, interests);
        
        // Score de diversité
        const diversityScore = this.calculateDiversityScore(item, filteredRecommendations);
        
        // Score de nouveauté
        const noveltyScore = this.calculateNoveltyScore(item);
        
        // Calculer le score final en tenant compte des paramètres de personnalisation
        const diversityWeight = settings.diversityLevel / 100 * (options?.diversityBoost || 1);
        const noveltyWeight = settings.noveltyLevel / 100 * (options?.noveltyBoost || 1);
        
        const finalScore = (
          score * 0.4 + // Score de base
          preferenceScore * 0.3 + // Préférences
          interestScore * 0.1 + // Intérêts
          diversityScore * diversityWeight * 0.1 + // Diversité
          noveltyScore * noveltyWeight * 0.1 // Nouveauté
        );
        
        return {
          ...item,
          score: finalScore,
        };
      });
      
      // Trier par score décroissant
      const sortedRecommendations = scoredRecommendations.sort((a, b) => b.score - a.score);
      
      // Limiter le nombre de recommandations si nécessaire
      const limit = options?.limit || sortedRecommendations.length;
      return sortedRecommendations.slice(0, limit);
    } catch (error) {
      this.logger.error(`Error applying personalization: ${error.message}`);
      return recommendations;
    }
  }

  /**
   * Calcule le score basé sur les préférences de l'utilisateur
   */
  private calculatePreferenceScore(item: any, preferences: UserPreference[]): number {
    const itemCategories = item.categories || [];
    
    if (itemCategories.length === 0 || preferences.length === 0) {
      return 0.5; // Score neutre
    }
    
    // Calculer le score moyen des catégories de l'élément
    let totalWeight = 0;
    let matchCount = 0;
    
    itemCategories.forEach(category => {
      const preference = preferences.find(p => p.category === category);
      if (preference) {
        totalWeight += preference.weight;
        matchCount++;
      }
    });
    
    // Si aucune correspondance, retourner un score neutre
    if (matchCount === 0) {
      return 0.5;
    }
    
    return totalWeight / matchCount;
  }

  /**
   * Calcule le score basé sur les intérêts de l'utilisateur
   */
  private calculateInterestScore(item: any, interests: UserInterest[]): number {
    if (interests.length === 0) {
      return 0.5; // Score neutre
    }
    
    // Extraire le texte de l'élément
    const itemText = `${item.title || ''} ${item.description || ''}`.toLowerCase();
    
    // Calculer le score basé sur la présence des intérêts dans le texte
    let totalWeight = 0;
    let matchCount = 0;
    
    interests.forEach(interest => {
      if (itemText.includes(interest.interest.toLowerCase())) {
        totalWeight += interest.weight;
        matchCount++;
      }
    });
    
    // Si aucune correspondance, retourner un score neutre
    if (matchCount === 0) {
      return 0.5;
    }
    
    return totalWeight / matchCount;
  }

  /**
   * Calcule le score de diversité
   */
  private calculateDiversityScore(item: any, recommendations: any[]): number {
    // Calculer la similarité moyenne avec les autres recommandations
    const similarities: number[] = [];
    
    recommendations.forEach(other => {
      if (other.id === item.id) return;
      
      // Calculer la similarité basée sur les catégories
      const itemCategories = new Set(item.categories || []);
      const otherCategories = new Set(other.categories || []);
      
      // Intersection des catégories
      const intersection = new Set(
        [...itemCategories].filter(category => otherCategories.has(category))
      );
      
      // Union des catégories
      const union = new Set([...itemCategories, ...otherCategories]);
      
      // Coefficient de Jaccard
      const similarity = union.size > 0 ? intersection.size / union.size : 0;
      
      similarities.push(similarity);
    });
    
    // Calculer la similarité moyenne
    const avgSimilarity = similarities.length > 0
      ? similarities.reduce((sum, sim) => sum + sim, 0) / similarities.length
      : 0;
    
    // Le score de diversité est l'inverse de la similarité moyenne
    return 1 - avgSimilarity;
  }

  /**
   * Calcule le score de nouveauté
   */
  private calculateNoveltyScore(item: any): number {
    // Calculer le score basé sur la date de publication
    if (!item.publishedAt) {
      return 0.5; // Score neutre
    }
    
    const ageInDays = (Date.now() - new Date(item.publishedAt).getTime()) / (1000 * 60 * 60 * 24);
    
    // Décroissance exponentielle sur 90 jours
    return Math.exp(-ageInDays / 90);
  }
}
