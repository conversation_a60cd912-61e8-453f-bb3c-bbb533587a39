import { Test } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';

// Configuration globale pour les tests
beforeAll(async () => {
  // Configuration des variables d'environnement pour les tests
  process.env.NODE_ENV = 'test';
  process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/retreat_and_be_test';
  process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-purposes';
  process.env.REDIS_URL = 'redis://localhost:6379/1';
  process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters';
});

// Augmenter le timeout pour les tests
jest.setTimeout(30000);

// Supprimer les logs pendant les tests (sauf erreurs critiques)
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: console.error, // Garder les erreurs pour le debugging
};

// Mock pour les modules externes
jest.mock('cache-manager-redis-store', () => {
  return jest.fn();
}, { virtual: true });

jest.mock('cache-manager', () => {
  return {
    caching: jest.fn().mockReturnValue({
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      reset: jest.fn(),
    }),
  };
}, { virtual: true });

// Mock pour les services externes (virtuel)
jest.mock('../common/services/external-api.service', () => ({
  ExternalApiService: jest.fn().mockImplementation(() => ({
    call: jest.fn().mockResolvedValue({ success: true }),
  })),
}), { virtual: true });

// Nettoyer les mocks après chaque test
afterEach(() => {
  jest.clearAllMocks();
});

// Helper pour créer un module de test
export const createTestingModule = async (providers: any[] = []) => {
  const moduleRef = await Test.createTestingModule({
    imports: [
      ConfigModule.forRoot({
        isGlobal: true,
        envFilePath: '.env.test',
      }),
    ],
    providers: [...providers],
  }).compile();

  return moduleRef;
};

// Données de test standardisées
export const testData = {
  user: {
    id: 'test-user-id-123',
    email: '<EMAIL>',
    name: 'Test User',
    password: '$2b$10$hashedPasswordForTesting',
    role: 'USER',
  },
  retreat: {
    id: 'test-retreat-id-123',
    title: 'Test Retreat',
    description: 'Test Description for retreat',
    price: 100,
    duration: 7,
    capacity: 20,
  },
  booking: {
    id: 'test-booking-id-123',
    userId: 'test-user-id-123',
    retreatId: 'test-retreat-id-123',
    status: 'CONFIRMED',
    totalPrice: 100,
  },
};
