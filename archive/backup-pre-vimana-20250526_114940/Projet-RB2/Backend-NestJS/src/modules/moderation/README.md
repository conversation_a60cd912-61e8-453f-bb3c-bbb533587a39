# Moderation Module

This module provides content moderation capabilities for the Retreat And Be platform.

## Features

### Content Moderation

- **Text Moderation**: Analyzes text content for inappropriate language, hate speech, etc.
- **Image Moderation**: Analyzes images for inappropriate content.
- **Report Management**: Handles user reports of inappropriate content.

### Moderation Dashboard

The moderation dashboard provides tools for moderators to manage reported content efficiently.

#### Dashboard Features

- **Report Filtering**: Filter reports by status, content type, date range, etc.
- **Moderation Actions**: Take actions on reports (approve, reject, escalate).
- **Performance Metrics**: View moderation performance metrics.
- **Notification System**: Receive notifications about new reports and updates.

## API Endpoints

### Moderation Dashboard API

#### Get Dashboard Statistics

```
GET /moderation/dashboard/stats
```

Returns statistics about the moderation system, including:
- Report counts by status
- Content type distribution
- Reports over time
- Average resolution time

#### Get Filtered Reports

```
GET /moderation/dashboard/reports
```

Query Parameters:
- `status`: Filter by report status (PENDING, IN_REVIEW, APPROVED, REJECTED, ESCALATED)
- `contentType`: Filter by content type (TEXT, IMAGE, VIDEO, COMMENT, POST)
- `startDate`: Filter by start date (ISO format)
- `endDate`: Filter by end date (ISO format)
- `skip`: Number of items to skip for pagination
- `take`: Number of items to take for pagination
- `sortBy`: Field to sort by (createdAt, updatedAt, status)
- `sortOrder`: Sort order (asc, desc)

#### Get Pending Reports

```
GET /moderation/dashboard/reports/pending
```

Returns a list of pending reports that need moderation.

#### Get Reports by Moderator

```
GET /moderation/dashboard/reports/by-moderator/:moderatorId
```

Returns reports handled by a specific moderator.

#### Get Performance Metrics

```
GET /moderation/dashboard/performance
```

Query Parameters:
- `startDate`: Start date for metrics (ISO format)
- `endDate`: End date for metrics (ISO format)

Returns performance metrics for the moderation system.

#### Get Moderators Performance

```
GET /moderation/dashboard/moderators/performance
```

Query Parameters:
- `startDate`: Start date for metrics (ISO format)
- `endDate`: End date for metrics (ISO format)

Returns performance metrics for all moderators.

#### Update Notification Settings

```
POST /moderation/dashboard/notifications/settings
```

Request Body:
```json
{
  "moderatorId": "string",
  "emailNotifications": true,
  "pushNotifications": true,
  "reportTypes": ["TEXT", "IMAGE", "VIDEO"]
}
```

Updates notification settings for a moderator.

## Performance Reporting

The module includes a script for generating performance reports:

```
node scripts/moderation-performance-report.js [--startDate YYYY-MM-DD] [--endDate YYYY-MM-DD] [--format json|csv|html] [--output filename]
```

Options:
- `--startDate`: Start date for the report (default: 30 days ago)
- `--endDate`: End date for the report (default: today)
- `--format`: Output format: json, csv, or html (default: json)
- `--output`: Output file (default: moderation-performance-report.{format})

## Integration with Other Services

The moderation module integrates with the following services:

- **Agent IA**: For AI-powered content moderation
- **Notification Service**: For sending notifications to moderators
- **User Service**: For accessing user information
- **Content Service**: For accessing and updating content

## Future Enhancements

- **Real-time Moderation**: Implement real-time moderation for live content
- **Moderation Queues**: Implement priority queues for different content types
- **Machine Learning Models**: Improve moderation accuracy with custom ML models
- **Moderator Training**: Implement a training system for moderators
