import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ModerationService } from './moderation.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { ContentType } from '@prisma/client';

@ApiTags('moderation')
@Controller('moderation')
export class ModerationController {
  private readonly logger = new Logger(ModerationController.name);

  constructor(private readonly moderationService: ModerationService) {}

  @Post('content')
  @ApiOperation({ summary: 'Modérer un contenu' })
  @ApiResponse({
    status: 200,
    description: 'Résultat de la modération du contenu',
  })
  async moderateContent(
    @Body() content: any,
    @Query('contentType') contentType: ContentType,
  ) {
    this.logger.debug(`Moderating content of type: ${contentType}`);
    return this.moderationService.moderateContent(content, contentType);
  }

  @Get('reports')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir la liste des signalements' })
  @ApiResponse({
    status: 200,
    description: 'Liste des signalements',
  })
  async getReports(@Query() filters: any) {
    return this.moderationService.getReports(filters);
  }

  @Get('reports/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir un signalement par son ID' })
  @ApiResponse({
    status: 200,
    description: 'Le signalement',
  })
  async getReport(@Param('id') id: string) {
    return this.moderationService.getReport(id);
  }

  @Post('reports')
  @ApiOperation({ summary: 'Créer un nouveau signalement' })
  @ApiResponse({
    status: 201,
    description: 'Le signalement a été créé avec succès',
  })
  async createReport(@Body() reportData: any) {
    return this.moderationService.createReport(reportData);
  }

  @Put('reports/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour un signalement' })
  @ApiResponse({
    status: 200,
    description: 'Le signalement a été mis à jour avec succès',
  })
  async updateReport(
    @Param('id') id: string,
    @Body() updateData: any,
  ) {
    return this.moderationService.updateReport(id, updateData);
  }

  @Post('reports/:id/actions')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Ajouter une action de modération à un signalement' })
  @ApiResponse({
    status: 201,
    description: 'L\'action de modération a été ajoutée avec succès',
  })
  async addModerationAction(
    @Param('id') id: string,
    @Body() actionData: any,
  ) {
    return this.moderationService.addModerationAction(id, actionData);
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les statistiques des signalements' })
  @ApiResponse({
    status: 200,
    description: 'Statistiques des signalements',
  })
  async getReportStats() {
    return this.moderationService.getReportStats();
  }
}
