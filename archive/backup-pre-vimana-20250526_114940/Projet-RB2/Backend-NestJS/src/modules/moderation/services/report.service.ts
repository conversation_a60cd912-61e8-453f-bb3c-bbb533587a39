import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ReportStatus, ContentType, ActionType } from '@prisma/client';
import { TextModerationService } from './text-moderation.service';
import { ImageModerationService } from './image-moderation.service';

@Injectable()
export class ReportService {
  private readonly logger = new Logger(ReportService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly textModerationService: TextModerationService,
    private readonly imageModerationService: ImageModerationService,
  ) {}

  async findAll(
    filters?: {
      status?: ReportStatus;
      contentType?: ContentType;
      reporterId?: string;
      skip?: number;
      take?: number;
    },
  ) {
    const where: any = {};
    
    if (filters?.status) {
      where.status = filters.status;
    }
    
    if (filters?.contentType) {
      where.contentType = filters.contentType;
    }
    
    if (filters?.reporterId) {
      where.reporterId = filters.reporterId;
    }
    
    const [reports, total] = await Promise.all([
      this.prisma.report.findMany({
        where,
        skip: filters?.skip || 0,
        take: filters?.take || 10,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          moderationActions: true,
        },
      }),
      this.prisma.report.count({ where }),
    ]);
    
    return { reports, total };
  }

  async findById(id: string) {
    const report = await this.prisma.report.findUnique({
      where: { id },
      include: {
        moderationActions: true,
      },
    });
    
    if (!report) {
      throw new NotFoundException(`Report with ID ${id} not found`);
    }
    
    return report;
  }

  async create(data: {
    contentType: ContentType;
    contentId: string;
    reporterId: string;
    reason: string;
    description?: string;
  }) {
    this.logger.debug(`Creating report: ${JSON.stringify(data)}`);
    
    // Créer le signalement
    const report = await this.prisma.report.create({
      data: {
        contentType: data.contentType,
        contentId: data.contentId,
        reporterId: data.reporterId,
        reason: data.reason,
        description: data.description,
      },
    });
    
    // Vérifier automatiquement le contenu signalé si c'est du texte ou une image
    if (data.contentType === ContentType.TEXT || data.contentType === ContentType.COMMENT) {
      // Simuler la récupération du texte depuis un service externe
      const text = `Ceci est un exemple de texte pour le contenu ${data.contentId}`;
      
      // Modérer le texte
      const moderationResult = await this.textModerationService.moderateText(text);
      
      // Si le texte est inapproprié, mettre à jour le statut du signalement
      if (moderationResult.isInappropriate) {
        await this.updateStatus(report.id, ReportStatus.APPROVED);
        
        // Ajouter une action de modération automatique
        await this.addModerationAction(
          report.id,
          'system',
          ActionType.APPROVE,
          `Contenu automatiquement détecté comme inapproprié avec une confiance de ${moderationResult.confidence}`,
        );
      }
    } else if (data.contentType === ContentType.IMAGE) {
      // Simuler la récupération de l'URL de l'image depuis un service externe
      const imageUrl = `https://example.com/images/${data.contentId}.jpg`;
      
      // Modérer l'image
      const moderationResult = await this.imageModerationService.moderateImage({ imageUrl });
      
      // Si l'image est inappropriée, mettre à jour le statut du signalement
      if (moderationResult.isInappropriate) {
        await this.updateStatus(report.id, ReportStatus.APPROVED);
        
        // Ajouter une action de modération automatique
        await this.addModerationAction(
          report.id,
          'system',
          ActionType.APPROVE,
          `Contenu automatiquement détecté comme inapproprié avec une confiance de ${moderationResult.confidence}`,
        );
      }
    }
    
    return this.findById(report.id);
  }

  async update(id: string, data: {
    status?: ReportStatus;
    description?: string;
  }) {
    await this.findById(id); // Vérifier que le signalement existe
    return this.prisma.report.update({
      where: { id },
      data,
    });
  }

  async updateStatus(id: string, status: ReportStatus) {
    await this.findById(id); // Vérifier que le signalement existe
    return this.prisma.report.update({
      where: { id },
      data: { status },
    });
  }

  async addModerationAction(
    reportId: string,
    moderatorId: string,
    action: ActionType,
    comment?: string,
  ) {
    await this.findById(reportId); // Vérifier que le signalement existe
    
    // Ajouter l'action de modération
    const moderationAction = await this.prisma.moderationAction.create({
      data: {
        reportId,
        moderatorId,
        action,
        comment,
      },
    });
    
    // Mettre à jour le statut du signalement en fonction de l'action
    let newStatus: ReportStatus;
    
    switch (action) {
      case ActionType.APPROVE:
        newStatus = ReportStatus.APPROVED;
        break;
      case ActionType.REJECT:
        newStatus = ReportStatus.REJECTED;
        break;
      case ActionType.ESCALATE:
        newStatus = ReportStatus.ESCALATED;
        break;
      default:
        newStatus = ReportStatus.IN_REVIEW;
    }
    
    await this.updateStatus(reportId, newStatus);
    
    return moderationAction;
  }

  async getModerationActions(reportId: string) {
    await this.findById(reportId); // Vérifier que le signalement existe
    return this.prisma.moderationAction.findMany({
      where: { reportId },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getReportsByContentId(contentType: ContentType, contentId: string) {
    return this.prisma.report.findMany({
      where: { contentType, contentId },
      include: {
        moderationActions: true,
      },
    });
  }

  async getReportStats() {
    const total = await this.prisma.report.count();
    const pending = await this.prisma.report.count({ where: { status: ReportStatus.PENDING } });
    const inReview = await this.prisma.report.count({ where: { status: ReportStatus.IN_REVIEW } });
    const approved = await this.prisma.report.count({ where: { status: ReportStatus.APPROVED } });
    const rejected = await this.prisma.report.count({ where: { status: ReportStatus.REJECTED } });
    const escalated = await this.prisma.report.count({ where: { status: ReportStatus.ESCALATED } });
    
    return {
      total,
      pending,
      inReview,
      approved,
      rejected,
      escalated,
    };
  }
}
