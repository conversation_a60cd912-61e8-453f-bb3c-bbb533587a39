import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { ReportStatus, ActionType } from '@prisma/client';

export enum ReputationAction {
  REPORT_CREATED = 'REPORT_CREATED',
  REPORT_APPROVED = 'REPORT_APPROVED',
  REPORT_REJECTED = 'REPORT_REJECTED',
  CONTENT_FLAGGED = 'CONTENT_FLAGGED',
  CONTENT_APPROVED = 'CONTENT_APPROVED',
  TRUSTED_MODERATION = 'TRUSTED_MODERATION',
  ACCURATE_REPORT = 'ACCURATE_REPORT',
  FALSE_REPORT = 'FALSE_REPORT',
}

export interface ReputationEvent {
  userId: string;
  action: ReputationAction;
  contentId?: string;
  reportId?: string;
  moderatorId?: string;
}

export interface ReputationPrivilege {
  id: string;
  name: string;
  description: string;
  requiredScore: number;
  actions: string[];
}

@Injectable()
export class ReputationService {
  private readonly logger = new Logger(ReputationService.name);
  private readonly reputationScores: Map<ReputationAction, number> = new Map();
  private readonly privileges: ReputationPrivilege[] = [];
  private readonly trustThreshold: number;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initializeReputationScores();
    this.initializePrivileges();
    this.trustThreshold = this.configService.get<number>('MODERATION_TRUST_THRESHOLD', 100);
  }

  private initializeReputationScores() {
    // Points gagnés ou perdus pour chaque action
    this.reputationScores.set(ReputationAction.REPORT_CREATED, 1);
    this.reputationScores.set(ReputationAction.REPORT_APPROVED, 5);
    this.reputationScores.set(ReputationAction.REPORT_REJECTED, -2);
    this.reputationScores.set(ReputationAction.CONTENT_FLAGGED, -10);
    this.reputationScores.set(ReputationAction.CONTENT_APPROVED, 2);
    this.reputationScores.set(ReputationAction.TRUSTED_MODERATION, 10);
    this.reputationScores.set(ReputationAction.ACCURATE_REPORT, 15);
    this.reputationScores.set(ReputationAction.FALSE_REPORT, -5);
  }

  private initializePrivileges() {
    this.privileges = [
      {
        id: 'basic_reporting',
        name: 'Signalement de base',
        description: 'Permet de signaler du contenu inapproprié',
        requiredScore: 0,
        actions: ['report_content'],
      },
      {
        id: 'trusted_reporter',
        name: 'Signaleur de confiance',
        description: 'Les signalements sont traités en priorité',
        requiredScore: 50,
        actions: ['report_content', 'priority_reports'],
      },
      {
        id: 'content_reviewer',
        name: 'Réviseur de contenu',
        description: 'Peut aider à réviser le contenu signalé',
        requiredScore: 100,
        actions: ['report_content', 'priority_reports', 'review_content'],
      },
      {
        id: 'trusted_moderator',
        name: 'Modérateur de confiance',
        description: 'Peut modérer certains types de contenu automatiquement',
        requiredScore: 200,
        actions: ['report_content', 'priority_reports', 'review_content', 'auto_moderate'],
      },
      {
        id: 'super_moderator',
        name: 'Super modérateur',
        description: 'Peut modérer tout type de contenu et former les modérateurs',
        requiredScore: 500,
        actions: [
          'report_content',
          'priority_reports',
          'review_content',
          'auto_moderate',
          'train_moderators',
        ],
      },
    ];
  }

  /**
   * Récupère la réputation d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Informations sur la réputation de l'utilisateur
   */
  async getUserReputation(userId: string) {
    const userReputation = await this.prisma.userReputation.findUnique({
      where: { userId },
    });

    if (!userReputation) {
      // Créer une réputation par défaut si elle n'existe pas
      return this.createUserReputation(userId);
    }

    // Récupérer les privilèges basés sur le score de réputation
    const userPrivileges = this.getUserPrivileges(userReputation.reputationScore);

    return {
      ...userReputation,
      privileges: userPrivileges,
    };
  }

  /**
   * Crée une réputation par défaut pour un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Réputation créée
   */
  async createUserReputation(userId: string) {
    // Vérifier que l'utilisateur existe
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { id: true },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Créer une réputation par défaut
    const defaultReputation = await this.prisma.userReputation.create({
      data: {
        userId,
        reputationScore: 0,
        isTrusted: false,
        privileges: ['report_content'],
      },
    });

    return {
      ...defaultReputation,
      privileges: this.getUserPrivileges(defaultReputation.reputationScore),
    };
  }

  /**
   * Met à jour la réputation d'un utilisateur
   * @param event Événement de réputation
   * @returns Réputation mise à jour
   */
  async updateReputation(event: ReputationEvent) {
    this.logger.debug(`Updating reputation for user ${event.userId} with action ${event.action}`);

    // Récupérer la réputation actuelle
    let userReputation = await this.prisma.userReputation.findUnique({
      where: { userId: event.userId },
    });

    // Créer une réputation par défaut si elle n'existe pas
    if (!userReputation) {
      userReputation = await this.prisma.userReputation.create({
        data: {
          userId: event.userId,
          reputationScore: 0,
          isTrusted: false,
          privileges: ['report_content'],
        },
      });
    }

    // Calculer le nouveau score
    const scoreChange = this.reputationScores.get(event.action) || 0;
    const newScore = Math.max(0, userReputation.reputationScore + scoreChange);

    // Déterminer si l'utilisateur est de confiance
    const isTrusted = newScore >= this.trustThreshold;

    // Déterminer les privilèges basés sur le nouveau score
    const privileges = this.getUserPrivileges(newScore).map(p => p.id);

    // Mettre à jour la réputation
    const updatedReputation = await this.prisma.userReputation.update({
      where: { userId: event.userId },
      data: {
        reputationScore: newScore,
        isTrusted,
        privileges,
        updatedAt: new Date(),
      },
    });

    // Émettre un événement si le statut de confiance a changé
    if (userReputation.isTrusted !== isTrusted) {
      this.eventEmitter.emit('user.trust_status_changed', {
        userId: event.userId,
        isTrusted,
        previousStatus: userReputation.isTrusted,
      });
    }

    return {
      ...updatedReputation,
      privileges: this.getUserPrivileges(updatedReputation.reputationScore),
    };
  }

  /**
   * Récupère les privilèges disponibles pour un score de réputation donné
   * @param score Score de réputation
   * @returns Liste des privilèges disponibles
   */
  getUserPrivileges(score: number): ReputationPrivilege[] {
    return this.privileges.filter(privilege => privilege.requiredScore <= score);
  }

  /**
   * Récupère tous les privilèges disponibles
   * @returns Liste de tous les privilèges
   */
  getAllPrivileges(): ReputationPrivilege[] {
    return this.privileges;
  }

  /**
   * Vérifie si un utilisateur a un privilège spécifique
   * @param userId ID de l'utilisateur
   * @param privilegeId ID du privilège
   * @returns Booléen indiquant si l'utilisateur a le privilège
   */
  async hasPrivilege(userId: string, privilegeId: string): Promise<boolean> {
    const userReputation = await this.getUserReputation(userId);
    return userReputation.privileges.some(p => p.id === privilegeId);
  }

  /**
   * Vérifie si un utilisateur est de confiance
   * @param userId ID de l'utilisateur
   * @returns Booléen indiquant si l'utilisateur est de confiance
   */
  async isTrustedUser(userId: string): Promise<boolean> {
    const userReputation = await this.getUserReputation(userId);
    return userReputation.isTrusted;
  }

  /**
   * Gestionnaire d'événement pour les rapports approuvés
   */
  @OnEvent('report.approved')
  async handleReportApproved(payload: { reportId: string; reporterId: string; moderatorId: string }) {
    this.logger.debug(`Report ${payload.reportId} approved, updating reputation for reporter ${payload.reporterId}`);
    
    await this.updateReputation({
      userId: payload.reporterId,
      action: ReputationAction.REPORT_APPROVED,
      reportId: payload.reportId,
      moderatorId: payload.moderatorId,
    });
  }

  /**
   * Gestionnaire d'événement pour les rapports rejetés
   */
  @OnEvent('report.rejected')
  async handleReportRejected(payload: { reportId: string; reporterId: string; moderatorId: string }) {
    this.logger.debug(`Report ${payload.reportId} rejected, updating reputation for reporter ${payload.reporterId}`);
    
    await this.updateReputation({
      userId: payload.reporterId,
      action: ReputationAction.REPORT_REJECTED,
      reportId: payload.reportId,
      moderatorId: payload.moderatorId,
    });
  }

  /**
   * Gestionnaire d'événement pour le contenu signalé
   */
  @OnEvent('content.flagged')
  async handleContentFlagged(payload: { contentId: string; userId: string }) {
    this.logger.debug(`Content ${payload.contentId} flagged, updating reputation for user ${payload.userId}`);
    
    await this.updateReputation({
      userId: payload.userId,
      action: ReputationAction.CONTENT_FLAGGED,
      contentId: payload.contentId,
    });
  }
}
