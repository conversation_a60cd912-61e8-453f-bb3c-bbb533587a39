import { Injectable, Logger, BadRequestException, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { Severity } from '@prisma/client';
import * as tf from '@tensorflow/tfjs-node';
import * as https from 'https';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { AgentIAService } from './agent-ia.service';

// Promisify fs functions
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);
const exists = promisify(fs.exists);

interface ModerationResult {
  isInappropriate: boolean;
  severity: Severity | null;
  matchedRules: {
    id: string;
    name: string;
    category: string;
    threshold: number;
    severity: Severity;
  }[];
  categories: {
    [key: string]: number;
  };
  confidence: number;
  moderationDetails?: {
    explicitContent?: boolean;
    violence?: boolean;
    harassment?: boolean;
    selfHarm?: boolean;
    sexualContent?: boolean;
    hateSpeech?: boolean;
    recommendation?: string;
  };
}

@Injectable()
export class ImageModerationService implements OnModuleInit {
  private readonly logger = new Logger(ImageModerationService.name);
  private model: tf.GraphModel | null = null;
  private readonly modelPath: string;
  private readonly modelDownloadUrl: string;
  private readonly categories: string[] = [
    'drawings', 'hentai', 'neutral', 'porn', 'sexy'
  ];
  private readonly extendedCategories: string[] = [
    'neutral', 'explicit', 'suggestive', 'violence', 'hate', 'harassment', 'self-harm'
  ];
  private modelLoaded = false;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly agentIAService: AgentIAService,
  ) {
    this.modelPath = this.configService.get<string>('MODERATION.IMAGE_MODEL_PATH', './models/image-moderation');
    this.modelDownloadUrl = this.configService.get<string>(
      'MODERATION.IMAGE_MODEL_DOWNLOAD_URL',
      'https://storage.googleapis.com/tfjs-models/nsfw/model.json'
    );
  }

  async onModuleInit() {
    await this.loadModel();
  }

  private async loadModel(): Promise<void> {
    try {
      this.logger.log(`Loading image moderation model from ${this.modelPath}...`);

      // Vérifier si le répertoire du modèle existe
      const modelDir = path.dirname(this.modelPath);
      if (!await exists(modelDir)) {
        this.logger.log(`Creating model directory: ${modelDir}`);
        await mkdir(modelDir, { recursive: true });
      }

      // Vérifier si le modèle existe localement
      const modelJsonPath = path.join(this.modelPath, 'model.json');
      if (await exists(modelJsonPath)) {
        this.logger.log('Loading model from local path');
        this.model = await tf.loadGraphModel(`file://${modelJsonPath}`);
        this.modelLoaded = true;
      } else {
        // Télécharger le modèle depuis l'URL
        this.logger.log(`Downloading model from ${this.modelDownloadUrl}`);
        try {
          this.model = await tf.loadGraphModel(this.modelDownloadUrl);
          this.modelLoaded = true;

          // Sauvegarder le modèle localement pour une utilisation future
          await this.model.save(`file://${this.modelPath}`);
          this.logger.log(`Model saved to ${this.modelPath}`);
        } catch (downloadError) {
          this.logger.error(`Failed to download model: ${downloadError.message}`);
          this.logger.log('Using Agent IA service as fallback');
        }
      }

      if (this.modelLoaded) {
        this.logger.log('Image moderation model loaded successfully');
      }
    } catch (error) {
      this.logger.error(`Failed to load image moderation model: ${error.message}`);
      this.logger.log('Will use Agent IA service for image moderation');
    }
  }

  async moderateImage(imageData: { imageUrl?: string; base64Image?: string }): Promise<ModerationResult> {
    if (!imageData.imageUrl && !imageData.base64Image) {
      throw new BadRequestException('Either imageUrl or base64Image must be provided');
    }

    this.logger.debug(`Moderating image: ${imageData.imageUrl || 'base64 image'}`);

    // 1. Obtenir l'image (télécharger depuis URL ou décoder base64)
    let imageBuffer: Buffer | null = null;

    if (imageData.imageUrl) {
      try {
        imageBuffer = await this.downloadImage(imageData.imageUrl);
      } catch (error) {
        this.logger.error(`Failed to download image: ${error.message}`);
        throw new BadRequestException(`Failed to download image: ${error.message}`);
      }
    } else if (imageData.base64Image) {
      try {
        // Supprimer le préfixe data:image/...;base64, s'il existe
        const base64Data = imageData.base64Image.replace(/^data:image\/\w+;base64,/, '');
        imageBuffer = Buffer.from(base64Data, 'base64');
      } catch (error) {
        this.logger.error(`Failed to decode base64 image: ${error.message}`);
        throw new BadRequestException(`Failed to decode base64 image: ${error.message}`);
      }
    }

    // 2. Analyser l'image avec le modèle d'IA ou le service Agent IA
    let predictions: number[];
    let moderationDetails = null;

    if (this.modelLoaded && this.model) {
      // Utiliser le modèle TensorFlow.js local
      predictions = await this.analyzeImageWithModel(imageBuffer);
    } else {
      // Utiliser le service Agent IA comme fallback
      const agentIAResult = await this.agentIAService.analyzeImage(imageBuffer);
      predictions = this.mapAgentIAResultToCategories(agentIAResult.categories);
      moderationDetails = agentIAResult.details;
    }

    // 3. Obtenir les règles de modération
    const rules = await this.findAllRules();

    // 4. Vérifier quelles règles sont déclenchées
    const matchedRules = rules.filter(rule => {
      const categoryIndex = this.categories.indexOf(rule.category.toLowerCase());
      if (categoryIndex === -1) return false;

      const prediction = predictions[categoryIndex];
      return prediction >= rule.threshold;
    });

    // 5. Déterminer si l'image est inappropriée et la sévérité
    let isInappropriate = matchedRules.length > 0;

    // Si aucune règle n'est déclenchée mais que l'Agent IA a détecté du contenu inapproprié
    if (!isInappropriate && moderationDetails) {
      isInappropriate = moderationDetails.explicitContent ||
                        moderationDetails.violence ||
                        moderationDetails.harassment ||
                        moderationDetails.selfHarm ||
                        moderationDetails.sexualContent ||
                        moderationDetails.hateSpeech;
    }

    let highestSeverity: Severity | null = null;
    if (matchedRules.length > 0) {
      const severityOrder = {
        LOW: 1,
        MEDIUM: 2,
        HIGH: 3,
        CRITICAL: 4,
      };

      highestSeverity = matchedRules.reduce((highest, rule) => {
        return severityOrder[rule.severity] > severityOrder[highest] ? rule.severity : highest;
      }, matchedRules[0].severity);
    } else if (isInappropriate) {
      // Attribuer une sévérité par défaut si l'Agent IA a détecté du contenu inapproprié
      highestSeverity = 'MEDIUM';
    }

    // 6. Calculer la confiance globale
    const maxPrediction = Math.max(...predictions);

    // 7. Créer un objet avec les catégories et leurs scores
    const categoriesResult = {};
    this.categories.forEach((category, index) => {
      categoriesResult[category] = predictions[index];
    });

    return {
      isInappropriate,
      severity: highestSeverity,
      matchedRules: matchedRules.map(rule => ({
        id: rule.id,
        name: rule.name,
        category: rule.category,
        threshold: rule.threshold,
        severity: rule.severity,
      })),
      categories: categoriesResult,
      confidence: maxPrediction,
      moderationDetails,
    };
  }

  private async downloadImage(url: string): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      https.get(url, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`Failed to download image: HTTP status ${response.statusCode}`));
          return;
        }

        const chunks: Buffer[] = [];
        response.on('data', (chunk) => chunks.push(chunk));
        response.on('end', () => resolve(Buffer.concat(chunks)));
        response.on('error', (error) => reject(error));
      }).on('error', (error) => reject(error));
    });
  }

  /**
   * Analyse une image avec le modèle TensorFlow.js
   * @param imageBuffer Buffer contenant l'image à analyser
   * @returns Tableau de prédictions pour chaque catégorie
   */
  private async analyzeImageWithModel(imageBuffer: Buffer): Promise<number[]> {
    try {
      if (!this.model) {
        throw new Error('Model not loaded');
      }

      // Convertir le buffer en tensor
      const image = tf.node.decodeImage(imageBuffer, 3);

      // Redimensionner l'image à la taille attendue par le modèle (généralement 224x224)
      const resized = tf.image.resizeBilinear(image as tf.Tensor3D, [224, 224]);

      // Normaliser les valeurs des pixels entre 0 et 1
      const normalized = resized.div(tf.scalar(255));

      // Ajouter une dimension pour le batch (le modèle attend [batch, height, width, channels])
      const batched = normalized.expandDims(0);

      // Faire la prédiction
      const predictions = await this.model.predict(batched) as tf.Tensor;

      // Convertir le tensor en tableau JavaScript
      const predictionData = await predictions.data();

      // Nettoyer les tensors pour éviter les fuites de mémoire
      tf.dispose([image, resized, normalized, batched, predictions]);

      // Convertir les données en tableau de nombres
      const result = Array.from(predictionData);

      // Normaliser pour que la somme soit 1
      const sum = result.reduce((a, b) => a + b, 0);
      const normalized_result = result.map(p => p / sum);

      return normalized_result;
    } catch (error) {
      this.logger.error(`Error analyzing image with model: ${error.message}`);
      // En cas d'erreur, retourner une prédiction par défaut
      return this.simulateImagePrediction();
    }
  }

  /**
   * Mappe les résultats de l'Agent IA aux catégories du service de modération
   * @param agentIACategories Catégories retournées par l'Agent IA
   * @returns Tableau de prédictions pour chaque catégorie
   */
  private mapAgentIAResultToCategories(agentIACategories: Record<string, number>): number[] {
    // Initialiser les prédictions avec des valeurs faibles
    const predictions = this.categories.map(() => 0.01);

    // Mapper les catégories de l'Agent IA aux catégories du service
    if (agentIACategories.explicit > 0.5) {
      const pornIndex = this.categories.indexOf('porn');
      if (pornIndex !== -1) {
        predictions[pornIndex] = agentIACategories.explicit;
      }
    }

    if (agentIACategories.suggestive > 0.5) {
      const sexyIndex = this.categories.indexOf('sexy');
      if (sexyIndex !== -1) {
        predictions[sexyIndex] = agentIACategories.suggestive;
      }
    }

    if (agentIACategories.neutral > 0.5) {
      const neutralIndex = this.categories.indexOf('neutral');
      if (neutralIndex !== -1) {
        predictions[neutralIndex] = agentIACategories.neutral;
      }
    }

    // Normaliser pour que la somme soit 1
    const sum = predictions.reduce((a, b) => a + b, 0);
    const normalized = predictions.map(p => p / sum);

    return normalized;
  }

  /**
   * Génère des prédictions simulées pour les tests
   * @returns Tableau de prédictions pour chaque catégorie
   */
  private simulateImagePrediction(): number[] {
    // Simulation d'une prédiction d'IA pour les différentes catégories
    // Utilisé comme fallback en cas d'erreur

    // Générer des valeurs aléatoires pour chaque catégorie
    const predictions = this.categories.map(() => Math.random());

    // Normaliser pour que la somme soit 1
    const sum = predictions.reduce((a, b) => a + b, 0);
    const normalized = predictions.map(p => p / sum);

    return normalized;
  }

  async findAllRules() {
    return this.prisma.imageModerationRule.findMany({
      where: { isActive: true },
    });
  }

  async findRuleById(id: string) {
    return this.prisma.imageModerationRule.findUnique({
      where: { id },
    });
  }

  async createRule(data: {
    name: string;
    description?: string;
    category: string;
    threshold: number;
    severity: Severity;
    isActive?: boolean;
  }) {
    return this.prisma.imageModerationRule.create({
      data,
    });
  }

  async updateRule(
    id: string,
    data: {
      name?: string;
      description?: string;
      category?: string;
      threshold?: number;
      severity?: Severity;
      isActive?: boolean;
    },
  ) {
    return this.prisma.imageModerationRule.update({
      where: { id },
      data,
    });
  }

  async deleteRule(id: string) {
    return this.prisma.imageModerationRule.delete({
      where: { id },
    });
  }
}
