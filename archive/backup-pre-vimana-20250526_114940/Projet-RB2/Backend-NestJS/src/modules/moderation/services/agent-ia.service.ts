import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

@Injectable()
export class AgentIAService {
  private readonly logger = new Logger(AgentIAService.name);
  private readonly apiUrl: string;

  constructor(private readonly configService: ConfigService) {
    this.apiUrl = this.configService.get<string>('AGENT_IA_API_URL', 'http://agent-ia:3000/api');
  }

  /**
   * Analyse une image pour détecter du contenu inapproprié
   * @param imageBuffer Buffer contenant l'image à analyser
   * @returns Résultat de l'analyse
   */
  async analyzeImage(imageBuffer: Buffer): Promise<{
    isInappropriate: boolean;
    categories: Record<string, number>;
    details: {
      explicitContent: boolean;
      violence: boolean;
      harassment: boolean;
      selfHarm: boolean;
      sexualContent: boolean;
      hateSpeech: boolean;
      recommendation?: string;
    };
  }> {
    try {
      const base64Image = imageBuffer.toString('base64');
      
      const response = await axios.post(`${this.apiUrl}/moderation/image`, {
        image: base64Image,
        analysisType: 'comprehensive',
      }, {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 30000, // 30 secondes
      });

      if (response.status !== 200) {
        throw new Error(`Agent IA API returned status ${response.status}`);
      }

      return response.data;
    } catch (error) {
      this.logger.error(`Failed to analyze image with Agent IA: ${error.message}`);
      
      // En cas d'erreur, retourner un résultat par défaut
      return {
        isInappropriate: false,
        categories: {
          'neutral': 1.0,
          'explicit': 0.0,
          'suggestive': 0.0,
          'violence': 0.0,
          'hate': 0.0,
        },
        details: {
          explicitContent: false,
          violence: false,
          harassment: false,
          selfHarm: false,
          sexualContent: false,
          hateSpeech: false,
          recommendation: 'Error analyzing image. Manual review recommended.',
        }
      };
    }
  }

  /**
   * Analyse du texte pour détecter du contenu inapproprié
   * @param text Texte à analyser
   * @returns Résultat de l'analyse
   */
  async analyzeText(text: string): Promise<{
    isInappropriate: boolean;
    categories: Record<string, number>;
    details: {
      hateSpeech: boolean;
      harassment: boolean;
      selfHarm: boolean;
      sexualContent: boolean;
      violence: boolean;
      recommendation?: string;
    };
  }> {
    try {
      const response = await axios.post(`${this.apiUrl}/moderation/text`, {
        text,
        analysisType: 'comprehensive',
      }, {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000, // 10 secondes
      });

      if (response.status !== 200) {
        throw new Error(`Agent IA API returned status ${response.status}`);
      }

      return response.data;
    } catch (error) {
      this.logger.error(`Failed to analyze text with Agent IA: ${error.message}`);
      
      // En cas d'erreur, retourner un résultat par défaut
      return {
        isInappropriate: false,
        categories: {
          'neutral': 1.0,
          'hate': 0.0,
          'harassment': 0.0,
          'self-harm': 0.0,
          'sexual': 0.0,
          'violence': 0.0,
        },
        details: {
          hateSpeech: false,
          harassment: false,
          selfHarm: false,
          sexualContent: false,
          violence: false,
          recommendation: 'Error analyzing text. Manual review recommended.',
        }
      };
    }
  }
}
