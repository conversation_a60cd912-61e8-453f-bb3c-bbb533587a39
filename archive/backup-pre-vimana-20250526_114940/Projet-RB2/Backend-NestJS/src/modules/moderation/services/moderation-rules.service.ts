import { Injectable, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ContentType } from '@prisma/client';

export enum RuleSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export interface ModerationRule {
  id: string;
  name: string;
  description: string;
  contentType: ContentType;
  pattern: string;
  severity: RuleSeverity;
  isActive: boolean;
  isRegex: boolean;
  threshold: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface CreateRuleDto {
  name: string;
  description: string;
  contentType: ContentType;
  pattern: string;
  severity: RuleSeverity;
  isActive?: boolean;
  isRegex?: boolean;
  threshold?: number;
}

export interface UpdateRuleDto {
  name?: string;
  description?: string;
  pattern?: string;
  severity?: RuleSeverity;
  isActive?: boolean;
  isRegex?: boolean;
  threshold?: number;
}

@Injectable()
export class ModerationRulesService {
  private readonly logger = new Logger(ModerationRulesService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Récupère toutes les règles de modération
   * @param contentType Type de contenu (optionnel)
   * @param activeOnly Ne récupérer que les règles actives
   * @returns Liste des règles de modération
   */
  async getAllRules(contentType?: ContentType, activeOnly: boolean = false) {
    this.logger.debug(`Getting all moderation rules for content type: ${contentType || 'all'}`);
    
    const where: any = {};
    
    if (contentType) {
      where.contentType = contentType;
    }
    
    if (activeOnly) {
      where.isActive = true;
    }
    
    return this.prisma.moderationRule.findMany({
      where,
      orderBy: {
        severity: 'desc',
      },
    });
  }

  /**
   * Récupère une règle de modération par son ID
   * @param id ID de la règle
   * @returns Règle de modération
   */
  async getRuleById(id: string) {
    this.logger.debug(`Getting moderation rule with ID: ${id}`);
    
    const rule = await this.prisma.moderationRule.findUnique({
      where: { id },
    });
    
    if (!rule) {
      throw new NotFoundException(`Moderation rule with ID ${id} not found`);
    }
    
    return rule;
  }

  /**
   * Crée une nouvelle règle de modération
   * @param createRuleDto Données de la règle
   * @param userId ID de l'utilisateur créateur
   * @returns Règle créée
   */
  async createRule(createRuleDto: CreateRuleDto, userId: string) {
    this.logger.debug(`Creating new moderation rule: ${createRuleDto.name}`);
    
    // Vérifier si une règle avec le même nom existe déjà
    const existingRule = await this.prisma.moderationRule.findFirst({
      where: {
        name: createRuleDto.name,
        contentType: createRuleDto.contentType,
      },
    });
    
    if (existingRule) {
      throw new ConflictException(`A moderation rule with name ${createRuleDto.name} already exists for content type ${createRuleDto.contentType}`);
    }
    
    // Créer la règle
    const rule = await this.prisma.moderationRule.create({
      data: {
        name: createRuleDto.name,
        description: createRuleDto.description,
        contentType: createRuleDto.contentType,
        pattern: createRuleDto.pattern,
        severity: createRuleDto.severity,
        isActive: createRuleDto.isActive !== undefined ? createRuleDto.isActive : true,
        isRegex: createRuleDto.isRegex !== undefined ? createRuleDto.isRegex : false,
        threshold: createRuleDto.threshold !== undefined ? createRuleDto.threshold : 0.7,
        createdBy: userId,
      },
    });
    
    // Émettre un événement pour notifier de la création de la règle
    this.eventEmitter.emit('moderation.rule_created', {
      ruleId: rule.id,
      contentType: rule.contentType,
      userId,
    });
    
    return rule;
  }

  /**
   * Met à jour une règle de modération
   * @param id ID de la règle
   * @param updateRuleDto Données de mise à jour
   * @param userId ID de l'utilisateur qui fait la mise à jour
   * @returns Règle mise à jour
   */
  async updateRule(id: string, updateRuleDto: UpdateRuleDto, userId: string) {
    this.logger.debug(`Updating moderation rule with ID: ${id}`);
    
    // Vérifier si la règle existe
    const existingRule = await this.getRuleById(id);
    
    // Mettre à jour la règle
    const updatedRule = await this.prisma.moderationRule.update({
      where: { id },
      data: {
        ...(updateRuleDto.name && { name: updateRuleDto.name }),
        ...(updateRuleDto.description && { description: updateRuleDto.description }),
        ...(updateRuleDto.pattern && { pattern: updateRuleDto.pattern }),
        ...(updateRuleDto.severity && { severity: updateRuleDto.severity }),
        ...(updateRuleDto.isActive !== undefined && { isActive: updateRuleDto.isActive }),
        ...(updateRuleDto.isRegex !== undefined && { isRegex: updateRuleDto.isRegex }),
        ...(updateRuleDto.threshold !== undefined && { threshold: updateRuleDto.threshold }),
        updatedAt: new Date(),
      },
    });
    
    // Émettre un événement pour notifier de la mise à jour de la règle
    this.eventEmitter.emit('moderation.rule_updated', {
      ruleId: updatedRule.id,
      contentType: updatedRule.contentType,
      userId,
      changes: updateRuleDto,
    });
    
    return updatedRule;
  }

  /**
   * Active ou désactive une règle de modération
   * @param id ID de la règle
   * @param isActive État d'activation
   * @param userId ID de l'utilisateur qui fait la mise à jour
   * @returns Règle mise à jour
   */
  async toggleRuleStatus(id: string, isActive: boolean, userId: string) {
    this.logger.debug(`${isActive ? 'Activating' : 'Deactivating'} moderation rule with ID: ${id}`);
    
    // Vérifier si la règle existe
    const existingRule = await this.getRuleById(id);
    
    // Mettre à jour l'état d'activation
    const updatedRule = await this.prisma.moderationRule.update({
      where: { id },
      data: {
        isActive,
        updatedAt: new Date(),
      },
    });
    
    // Émettre un événement pour notifier du changement d'état
    this.eventEmitter.emit('moderation.rule_status_changed', {
      ruleId: updatedRule.id,
      contentType: updatedRule.contentType,
      userId,
      isActive,
    });
    
    return updatedRule;
  }

  /**
   * Supprime une règle de modération
   * @param id ID de la règle
   * @param userId ID de l'utilisateur qui fait la suppression
   * @returns Résultat de la suppression
   */
  async deleteRule(id: string, userId: string) {
    this.logger.debug(`Deleting moderation rule with ID: ${id}`);
    
    // Vérifier si la règle existe
    const existingRule = await this.getRuleById(id);
    
    // Supprimer la règle
    await this.prisma.moderationRule.delete({
      where: { id },
    });
    
    // Émettre un événement pour notifier de la suppression de la règle
    this.eventEmitter.emit('moderation.rule_deleted', {
      ruleId: id,
      contentType: existingRule.contentType,
      userId,
    });
    
    return { success: true, message: `Moderation rule with ID ${id} deleted successfully` };
  }

  /**
   * Teste une règle de modération sur un contenu
   * @param ruleId ID de la règle
   * @param content Contenu à tester
   * @returns Résultat du test
   */
  async testRule(ruleId: string, content: string) {
    this.logger.debug(`Testing moderation rule with ID: ${ruleId}`);
    
    // Récupérer la règle
    const rule = await this.getRuleById(ruleId);
    
    // Tester la règle
    let isMatch = false;
    
    if (rule.isRegex) {
      try {
        const regex = new RegExp(rule.pattern, 'i');
        isMatch = regex.test(content);
      } catch (error) {
        this.logger.error(`Invalid regex pattern in rule ${ruleId}: ${error.message}`);
        return {
          isMatch: false,
          error: `Invalid regex pattern: ${error.message}`,
        };
      }
    } else {
      // Recherche simple de chaîne de caractères
      isMatch = content.toLowerCase().includes(rule.pattern.toLowerCase());
    }
    
    return {
      rule,
      isMatch,
      content: content.substring(0, 100) + (content.length > 100 ? '...' : ''),
    };
  }
}
