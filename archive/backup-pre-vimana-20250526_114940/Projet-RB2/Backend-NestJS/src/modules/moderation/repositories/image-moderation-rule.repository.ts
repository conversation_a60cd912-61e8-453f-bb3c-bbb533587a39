import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { Severity } from '@prisma/client';

@Injectable()
export class ImageModerationRuleRepository {
  constructor(private prisma: PrismaService) {}

  /**
   * <PERSON><PERSON><PERSON><PERSON><PERSON> toutes les règles de modération d'images
   */
  async findAll() {
    return this.prisma.imageModerationRule.findMany({
      where: {
        isActive: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  /**
   * Récupérer une règle de modération d'image par son ID
   */
  async findById(id: string) {
    return this.prisma.imageModerationRule.findUnique({
      where: { id },
    });
  }

  /**
   * Créer une nouvelle règle de modération d'image
   */
  async create(data: {
    name: string;
    description?: string;
    category: string;
    threshold: number;
    severity: Severity;
    isActive?: boolean;
  }) {
    return this.prisma.imageModerationRule.create({
      data: {
        name: data.name,
        description: data.description || '',
        category: data.category,
        threshold: data.threshold,
        severity: data.severity,
        isActive: data.isActive !== undefined ? data.isActive : true,
      },
    });
  }

  /**
   * Mettre à jour une règle de modération d'image
   */
  async update(
    id: string,
    data: {
      name?: string;
      description?: string;
      category?: string;
      threshold?: number;
      severity?: Severity;
      isActive?: boolean;
    },
  ) {
    return this.prisma.imageModerationRule.update({
      where: { id },
      data,
    });
  }

  /**
   * Supprimer une règle de modération d'image
   */
  async delete(id: string) {
    return this.prisma.imageModerationRule.delete({
      where: { id },
    });
  }

  /**
   * Récupérer les règles de modération par catégorie
   */
  async findByCategory(category: string) {
    return this.prisma.imageModerationRule.findMany({
      where: {
        category,
        isActive: true,
      },
    });
  }

  /**
   * Récupérer les règles de modération par sévérité
   */
  async findBySeverity(severity: Severity) {
    return this.prisma.imageModerationRule.findMany({
      where: {
        severity,
        isActive: true,
      },
    });
  }
}
