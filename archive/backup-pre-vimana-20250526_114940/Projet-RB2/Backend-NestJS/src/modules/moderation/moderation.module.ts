import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { ModerationService } from './moderation.service';
import { ModerationController } from './moderation.controller';
import { TextModerationService } from './services/text-moderation.service';
import { ImageModerationService } from './services/image-moderation.service';
import { AgentIAService } from './services/agent-ia.service';
import { ReportService } from './services/report.service';
import { DashboardService } from './services/dashboard.service';
import { NotificationService } from './services/notification.service';
import { ReputationService } from './services/reputation.service';
import { ModerationLearningService } from './services/moderation-learning.service';
import { ModerationRulesService } from './services/moderation-rules.service';
import { DashboardController } from './controllers/dashboard.controller';
import { ReputationController } from './controllers/reputation.controller';
import { ModerationLearningController } from './controllers/moderation-learning.controller';
import { ModerationRulesController } from './controllers/moderation-rules.controller';
import { PrismaModule } from '../../prisma/prisma.module';

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
    EventEmitterModule.forRoot(),
    ScheduleModule.forRoot(),
  ],
  controllers: [
    ModerationController,
    DashboardController,
    ReputationController,
    ModerationLearningController,
    ModerationRulesController,
  ],
  providers: [
    ModerationService,
    TextModerationService,
    ImageModerationService,
    AgentIAService,
    ReportService,
    DashboardService,
    NotificationService,
    ReputationService,
    ModerationLearningService,
    ModerationRulesService,
  ],
  exports: [
    ModerationService,
    TextModerationService,
    ImageModerationService,
    AgentIAService,
    ReportService,
    DashboardService,
    NotificationService,
    ReputationService,
    ModerationLearningService,
    ModerationRulesService,
  ],
}
export class ModerationModule {}
