import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../../common/decorators/roles.decorator';
import { DashboardService } from '../services/dashboard.service';
import { FilterReportsDto } from '../dto/filter-reports.dto';
import { ReportStatus, ContentType } from '@prisma/client';

@ApiTags('moderation-dashboard')
@Controller('moderation/dashboard')
export class DashboardController {
  private readonly logger = new Logger(DashboardController.name);

  constructor(private readonly dashboardService: DashboardService) {}

  @Get('stats')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get moderation dashboard statistics' })
  @ApiResponse({
    status: 200,
    description: 'Returns moderation statistics',
  })
  async getStats() {
    this.logger.debug('Getting moderation dashboard statistics');
    return this.dashboardService.getStats();
  }

  @Get('reports')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get filtered reports for dashboard' })
  @ApiQuery({ name: 'status', enum: ReportStatus, required: false })
  @ApiQuery({ name: 'contentType', enum: ContentType, required: false })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  @ApiQuery({ name: 'skip', required: false })
  @ApiQuery({ name: 'take', required: false })
  @ApiQuery({ name: 'sortBy', required: false })
  @ApiQuery({ name: 'sortOrder', enum: ['asc', 'desc'], required: false })
  @ApiResponse({
    status: 200,
    description: 'Returns filtered reports',
  })
  async getReports(@Query() filterDto: FilterReportsDto) {
    this.logger.debug(`Getting filtered reports: ${JSON.stringify(filterDto)}`);
    return this.dashboardService.getFilteredReports(filterDto);
  }

  @Get('reports/pending')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get pending reports for dashboard' })
  @ApiResponse({
    status: 200,
    description: 'Returns pending reports',
  })
  async getPendingReports() {
    this.logger.debug('Getting pending reports');
    return this.dashboardService.getPendingReports();
  }

  @Get('reports/by-moderator/:moderatorId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get reports handled by a specific moderator' })
  @ApiResponse({
    status: 200,
    description: 'Returns reports handled by the specified moderator',
  })
  async getReportsByModerator(@Param('moderatorId') moderatorId: string) {
    this.logger.debug(`Getting reports handled by moderator: ${moderatorId}`);
    return this.dashboardService.getReportsByModerator(moderatorId);
  }

  @Get('performance')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get moderation performance metrics' })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  @ApiResponse({
    status: 200,
    description: 'Returns moderation performance metrics',
  })
  async getPerformanceMetrics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.debug(`Getting performance metrics from ${startDate} to ${endDate}`);
    return this.dashboardService.getPerformanceMetrics(
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Get('moderators/performance')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get performance metrics for all moderators' })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  @ApiResponse({
    status: 200,
    description: 'Returns performance metrics for all moderators',
  })
  async getModeratorsPerformance(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.debug(`Getting moderators performance from ${startDate} to ${endDate}`);
    return this.dashboardService.getModeratorsPerformance(
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Post('notifications/settings')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update notification settings for a moderator' })
  @ApiResponse({
    status: 200,
    description: 'Notification settings updated successfully',
  })
  async updateNotificationSettings(
    @Body() settings: {
      moderatorId: string;
      emailNotifications: boolean;
      pushNotifications: boolean;
      reportTypes: string[];
    },
  ) {
    this.logger.debug(`Updating notification settings for moderator: ${settings.moderatorId}`);
    return this.dashboardService.updateNotificationSettings(settings);
  }
}
