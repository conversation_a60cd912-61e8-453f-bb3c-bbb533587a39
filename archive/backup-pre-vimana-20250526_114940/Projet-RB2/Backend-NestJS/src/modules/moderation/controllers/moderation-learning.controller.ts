import {
  <PERSON>,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Logger,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../../common/decorators/roles.decorator';
import { ModerationLearningService } from '../services/moderation-learning.service';
import { ContentType } from '@prisma/client';

@ApiTags('moderation-learning')
@Controller('moderation/learning')
export class ModerationLearningController {
  private readonly logger = new Logger(ModerationLearningController.name);

  constructor(private readonly moderationLearningService: ModerationLearningService) {}

  @Get('metrics')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get moderation model metrics' })
  @ApiQuery({ name: 'contentType', enum: ContentType, required: false })
  @ApiResponse({
    status: 200,
    description: 'Returns moderation model metrics',
  })
  async getModelMetrics(@Query('contentType') contentType?: ContentType) {
    this.logger.debug(`Getting model metrics for content type: ${contentType || 'all'}`);
    return this.moderationLearningService.getModelMetrics(contentType);
  }

  @Post('feedback')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Add moderation feedback for learning' })
  @ApiResponse({
    status: 200,
    description: 'Feedback added successfully',
  })
  async addModerationFeedback(
    @Body() body: {
      contentId: string;
      contentType: ContentType;
      moderationResult: boolean;
      moderatorId: string;
      confidence: number;
      reportId?: string;
    },
  ) {
    this.logger.debug(`Adding moderation feedback for content ${body.contentId}`);
    await this.moderationLearningService.addModerationFeedback(body);
    return { success: true, message: 'Feedback added successfully' };
  }

  @Post('process-queue')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Manually process the feedback queue' })
  @ApiResponse({
    status: 200,
    description: 'Feedback queue processed successfully',
  })
  async processFeedbackQueue() {
    this.logger.debug('Manually processing feedback queue');
    await this.moderationLearningService.processFeedbackQueue();
    return { success: true, message: 'Feedback queue processed successfully' };
  }
}
