import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsString, IsOptional, IsUUID, IsNotEmpty, IsBoolean, IsN<PERSON>ber, Min, Max } from 'class-validator';
import { ContentType, Severity, ReportStatus, ActionType } from '@prisma/client';

export class ModerateContentDto {
  @ApiPropertyOptional({ description: 'Texte à modérer' })
  @IsOptional()
  @IsString()
  text?: string;

  @ApiPropertyOptional({ description: 'URL de l\'image à modérer' })
  @IsOptional()
  @IsString()
  imageUrl?: string;

  @ApiPropertyOptional({ description: 'Image en base64 à modérer' })
  @IsOptional()
  @IsString()
  base64Image?: string;
}

export class CreateTextModerationRuleDto {
  @ApiProperty({ description: 'Nom de la règle' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Description de la règle' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Motif (regex ou mot-clé) à détecter' })
  @IsNotEmpty()
  @IsString()
  pattern: string;

  @ApiProperty({ 
    description: 'Sévérité de la règle',
    enum: Severity,
    example: Severity.MEDIUM
  })
  @IsEnum(Severity)
  severity: Severity;

  @ApiPropertyOptional({ 
    description: 'Indique si la règle est active',
    default: true
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateTextModerationRuleDto {
  @ApiPropertyOptional({ description: 'Nom de la règle' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Description de la règle' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Motif (regex ou mot-clé) à détecter' })
  @IsOptional()
  @IsString()
  pattern?: string;

  @ApiPropertyOptional({ 
    description: 'Sévérité de la règle',
    enum: Severity
  })
  @IsOptional()
  @IsEnum(Severity)
  severity?: Severity;

  @ApiPropertyOptional({ 
    description: 'Indique si la règle est active'
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class CreateImageModerationRuleDto {
  @ApiProperty({ description: 'Nom de la règle' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Description de la règle' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Catégorie de contenu à détecter (ex: nudité, violence)' })
  @IsNotEmpty()
  @IsString()
  category: string;

  @ApiProperty({ 
    description: 'Seuil de confiance (entre 0 et 1)',
    minimum: 0,
    maximum: 1,
    example: 0.7
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  threshold: number;

  @ApiProperty({ 
    description: 'Sévérité de la règle',
    enum: Severity,
    example: Severity.HIGH
  })
  @IsEnum(Severity)
  severity: Severity;

  @ApiPropertyOptional({ 
    description: 'Indique si la règle est active',
    default: true
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateImageModerationRuleDto {
  @ApiPropertyOptional({ description: 'Nom de la règle' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Description de la règle' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Catégorie de contenu à détecter (ex: nudité, violence)' })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({ 
    description: 'Seuil de confiance (entre 0 et 1)',
    minimum: 0,
    maximum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  threshold?: number;

  @ApiPropertyOptional({ 
    description: 'Sévérité de la règle',
    enum: Severity
  })
  @IsOptional()
  @IsEnum(Severity)
  severity?: Severity;

  @ApiPropertyOptional({ 
    description: 'Indique si la règle est active'
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class CreateReportDto {
  @ApiProperty({ 
    description: 'Type de contenu signalé',
    enum: ContentType,
    example: ContentType.TEXT
  })
  @IsEnum(ContentType)
  contentType: ContentType;

  @ApiProperty({ description: 'ID du contenu signalé' })
  @IsNotEmpty()
  @IsString()
  contentId: string;

  @ApiProperty({ description: 'ID de l\'utilisateur qui signale' })
  @IsNotEmpty()
  @IsString()
  reporterId: string;

  @ApiProperty({ description: 'Raison du signalement' })
  @IsNotEmpty()
  @IsString()
  reason: string;

  @ApiPropertyOptional({ description: 'Description détaillée du signalement' })
  @IsOptional()
  @IsString()
  description?: string;
}

export class UpdateReportDto {
  @ApiPropertyOptional({ 
    description: 'Statut du signalement',
    enum: ReportStatus
  })
  @IsOptional()
  @IsEnum(ReportStatus)
  status?: ReportStatus;

  @ApiPropertyOptional({ description: 'Description détaillée du signalement' })
  @IsOptional()
  @IsString()
  description?: string;
}

export class AddModerationActionDto {
  @ApiProperty({ description: 'ID du modérateur' })
  @IsNotEmpty()
  @IsString()
  moderatorId: string;

  @ApiProperty({ 
    description: 'Action de modération',
    enum: ActionType,
    example: ActionType.APPROVE
  })
  @IsEnum(ActionType)
  action: ActionType;

  @ApiPropertyOptional({ description: 'Commentaire sur l\'action de modération' })
  @IsOptional()
  @IsString()
  comment?: string;
}

export class ReportFiltersDto {
  @ApiPropertyOptional({ 
    description: 'Statut du signalement',
    enum: ReportStatus
  })
  @IsOptional()
  @IsEnum(ReportStatus)
  status?: ReportStatus;

  @ApiPropertyOptional({ 
    description: 'Type de contenu signalé',
    enum: ContentType
  })
  @IsOptional()
  @IsEnum(ContentType)
  contentType?: ContentType;

  @ApiPropertyOptional({ description: 'ID de l\'utilisateur qui signale' })
  @IsOptional()
  @IsString()
  reporterId?: string;

  @ApiPropertyOptional({ 
    description: 'Nombre d\'éléments à ignorer (pour la pagination)',
    minimum: 0
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  skip?: number;

  @ApiPropertyOptional({ 
    description: 'Nombre d\'éléments à récupérer (pour la pagination)',
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  take?: number;
}
