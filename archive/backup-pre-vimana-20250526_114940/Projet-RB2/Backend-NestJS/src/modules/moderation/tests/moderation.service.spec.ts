import { Test, TestingModule } from '@nestjs/testing';
import { ModerationService } from '../moderation.service';
import { TextModerationService } from '../services/text-moderation.service';
import { ImageModerationService } from '../services/image-moderation.service';
import { ReportService } from '../services/report.service';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { ContentType } from '@prisma/client';

// Mock des services
const mockTextModerationService = {
  moderateText: jest.fn(),
};

const mockImageModerationService = {
  moderateImage: jest.fn(),
};

const mockReportService = {
  findAll: jest.fn(),
  findById: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  addModerationAction: jest.fn(),
  getReportStats: jest.fn(),
};

const mockConfigService = {
  get: jest.fn(),
};

const mockPrismaService = {
  textModerationRule: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  imageModerationRule: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
};

describe('ModerationService', () => {
  let service: ModerationService;
  let textModerationService: TextModerationService;
  let imageModerationService: ImageModerationService;
  let reportService: ReportService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ModerationService,
        {
          provide: TextModerationService,
          useValue: mockTextModerationService,
        },
        {
          provide: ImageModerationService,
          useValue: mockImageModerationService,
        },
        {
          provide: ReportService,
          useValue: mockReportService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<ModerationService>(ModerationService);
    textModerationService = module.get<TextModerationService>(TextModerationService);
    imageModerationService = module.get<ImageModerationService>(ImageModerationService);
    reportService = module.get<ReportService>(ReportService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('moderateContent', () => {
    it('should call textModerationService.moderateText for text content', async () => {
      const content = { text: 'Test content' };
      const contentType = ContentType.TEXT;
      const expectedResult = {
        isInappropriate: false,
        severity: null,
        matchedRules: [],
        confidence: 0,
      };

      mockTextModerationService.moderateText.mockResolvedValue(expectedResult);

      const result = await service.moderateContent(content, contentType);

      expect(textModerationService.moderateText).toHaveBeenCalledWith(content.text);
      expect(result).toEqual(expectedResult);
    });

    it('should call imageModerationService.moderateImage for image content', async () => {
      const content = { imageUrl: 'https://example.com/image.jpg' };
      const contentType = ContentType.IMAGE;
      const expectedResult = {
        isInappropriate: false,
        severity: null,
        matchedRules: [],
        categories: {},
        confidence: 0,
      };

      mockImageModerationService.moderateImage.mockResolvedValue(expectedResult);

      const result = await service.moderateContent(content, contentType);

      expect(imageModerationService.moderateImage).toHaveBeenCalledWith({
        imageUrl: content.imageUrl,
        base64Image: content.base64Image,
      });
      expect(result).toEqual(expectedResult);
    });

    it('should return default result for unsupported content type', async () => {
      const content = { data: 'Test content' };
      const contentType = 'UNSUPPORTED' as ContentType;

      const result = await service.moderateContent(content, contentType);

      expect(result).toEqual({
        isInappropriate: false,
        severity: null,
        matchedRules: [],
        confidence: 0,
      });
    });
  });

  describe('getReports', () => {
    it('should call reportService.findAll', async () => {
      const filters = { status: 'PENDING' };
      const expectedResult = { reports: [], total: 0 };

      mockReportService.findAll.mockResolvedValue(expectedResult);

      const result = await service.getReports(filters);

      expect(reportService.findAll).toHaveBeenCalledWith(filters);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('getReport', () => {
    it('should call reportService.findById', async () => {
      const id = '123';
      const expectedResult = { id, status: 'PENDING' };

      mockReportService.findById.mockResolvedValue(expectedResult);

      const result = await service.getReport(id);

      expect(reportService.findById).toHaveBeenCalledWith(id);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('createReport', () => {
    it('should call reportService.create', async () => {
      const reportData = { contentId: '123', contentType: ContentType.TEXT };
      const expectedResult = { id: '456', ...reportData };

      mockReportService.create.mockResolvedValue(expectedResult);

      const result = await service.createReport(reportData);

      expect(reportService.create).toHaveBeenCalledWith(reportData);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('updateReport', () => {
    it('should call reportService.update', async () => {
      const id = '123';
      const updateData = { status: 'APPROVED' };
      const expectedResult = { id, ...updateData };

      mockReportService.update.mockResolvedValue(expectedResult);

      const result = await service.updateReport(id, updateData);

      expect(reportService.update).toHaveBeenCalledWith(id, updateData);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('addModerationAction', () => {
    it('should call reportService.addModerationAction', async () => {
      const reportId = '123';
      const actionData = { action: 'APPROVE', comment: 'Test comment' };
      const expectedResult = { id: '456', reportId, ...actionData };

      mockReportService.addModerationAction.mockResolvedValue(expectedResult);

      const result = await service.addModerationAction(reportId, actionData);

      expect(reportService.addModerationAction).toHaveBeenCalledWith(reportId, actionData);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('getReportStats', () => {
    it('should call reportService.getReportStats', async () => {
      const expectedResult = { total: 10, pending: 5 };

      mockReportService.getReportStats.mockResolvedValue(expectedResult);

      const result = await service.getReportStats();

      expect(reportService.getReportStats).toHaveBeenCalled();
      expect(result).toEqual(expectedResult);
    });
  });
});
