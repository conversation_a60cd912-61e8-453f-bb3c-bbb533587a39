import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { catchError, firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';

@Injectable()
export class SocialAnalyticsService {
  private readonly logger = new Logger(SocialAnalyticsService.name);
  private readonly socialPlatformVideoUrl: string;
  private readonly apiKey: string;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.socialPlatformVideoUrl = this.configService.get<string>('microservices.socialPlatformVideo.url', 'http://social-platform-video:3002');
    this.apiKey = this.configService.get<string>('microservices.socialPlatformVideo.apiKey', 'default-api-key');
  }

  /**
   * Récupère les statistiques sociales globales
   * @param period Période (day, week, month, year)
   * @returns Statistiques sociales
   */
  async getSocialAnalytics(period: string = 'month'): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(`${this.socialPlatformVideoUrl}/api/analytics`, {
          params: { period },
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error fetching social analytics: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error fetching social analytics',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      this.logger.error(`Error in getSocialAnalytics: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error fetching social analytics', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Récupère les statistiques d'un livestream spécifique
   * @param livestreamId ID du livestream
   * @returns Statistiques du livestream
   */
  async getLivestreamAnalytics(livestreamId: string): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(`${this.socialPlatformVideoUrl}/api/analytics/livestreams/${livestreamId}`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error fetching livestream analytics: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error fetching livestream analytics',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      this.logger.error(`Error in getLivestreamAnalytics: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error fetching livestream analytics', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Récupère les statistiques d'un article de blog spécifique
   * @param blogPostId ID de l'article de blog
   * @returns Statistiques de l'article de blog
   */
  async getBlogPostAnalytics(blogPostId: string): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(`${this.socialPlatformVideoUrl}/api/analytics/blog/${blogPostId}`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error fetching blog post analytics: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error fetching blog post analytics',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      this.logger.error(`Error in getBlogPostAnalytics: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error fetching blog post analytics', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Récupère les statistiques d'un utilisateur spécifique
   * @param userId ID de l'utilisateur
   * @returns Statistiques de l'utilisateur
   */
  async getUserAnalytics(userId: string): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(`${this.socialPlatformVideoUrl}/api/analytics/users/${userId}`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error fetching user analytics: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error fetching user analytics',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      this.logger.error(`Error in getUserAnalytics: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error fetching user analytics', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Récupère les statistiques de contenu populaire
   * @param type Type de contenu (livestream, blog, video)
   * @param limit Nombre de résultats à retourner
   * @param period Période (day, week, month, year)
   * @returns Statistiques de contenu populaire
   */
  async getPopularContent(type?: string, limit: number = 10, period: string = 'month'): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(`${this.socialPlatformVideoUrl}/api/analytics/popular`, {
          params: { type, limit, period },
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error fetching popular content: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error fetching popular content',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      this.logger.error(`Error in getPopularContent: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error fetching popular content', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Enregistre un événement d'analyse
   * @param eventData Données de l'événement
   * @returns Résultat de l'opération
   */
  async trackEvent(eventData: {
    eventType: string;
    entityId: string;
    entityType: 'livestream' | 'blog' | 'video' | 'user';
    userId?: string;
    metadata?: Record<string, any>;
  }): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.post(`${this.socialPlatformVideoUrl}/api/analytics/events`, eventData, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error tracking event: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error tracking event',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      this.logger.error(`Error in trackEvent: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error tracking event', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Récupère les tendances d'engagement
   * @param period Période (day, week, month, year)
   * @returns Tendances d'engagement
   */
  async getEngagementTrends(period: string = 'month'): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(`${this.socialPlatformVideoUrl}/api/analytics/engagement`, {
          params: { period },
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error fetching engagement trends: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error fetching engagement trends',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      this.logger.error(`Error in getEngagementTrends: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error fetching engagement trends', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
