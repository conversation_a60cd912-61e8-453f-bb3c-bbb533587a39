import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { catchError, firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class BlogService {
  private readonly logger = new Logger(BlogService.name);
  private readonly socialPlatformVideoUrl: string;
  private readonly apiKey: string;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.socialPlatformVideoUrl = this.configService.get<string>('microservices.socialPlatformVideo.url', 'http://social-platform-video:3002');
    this.apiKey = this.configService.get<string>('microservices.socialPlatformVideo.apiKey', 'default-api-key');
  }

  /**
   * Récupère tous les articles de blog avec filtres optionnels
   * @param filters Filtres optionnels (tags, authorId, etc.)
   * @returns Liste des articles de blog
   */
  async getBlogPosts(filters?: Record<string, any>): Promise<any[]> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(`${this.socialPlatformVideoUrl}/api/blog`, {
          params: filters,
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error fetching blog posts: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error fetching blog posts',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      this.logger.error(`Error in getBlogPosts: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error fetching blog posts', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Récupère un article de blog par son ID
   * @param id ID de l'article de blog
   * @returns Détails de l'article de blog
   */
  async getBlogPostById(id: string): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(`${this.socialPlatformVideoUrl}/api/blog/${id}`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error fetching blog post: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error fetching blog post',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      this.logger.error(`Error in getBlogPostById: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error fetching blog post', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Crée un nouvel article de blog
   * @param blogPostData Données de l'article de blog
   * @returns Article de blog créé
   */
  async createBlogPost(blogPostData: {
    title: string;
    content: string;
    authorId: string;
    authorName: string;
    tags?: string[];
    imageUrl?: string;
    publishDate?: string;
    status?: 'draft' | 'published' | 'archived';
    metadata?: Record<string, any>;
  }): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.post(`${this.socialPlatformVideoUrl}/api/blog`, blogPostData, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error creating blog post: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error creating blog post',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      // Émettre un événement pour la création de l'article de blog
      this.eventEmitter.emit('blog.created', {
        blogPostId: data.id,
        authorId: blogPostData.authorId,
        authorName: blogPostData.authorName,
        title: blogPostData.title,
        content: blogPostData.content.substring(0, 200) + (blogPostData.content.length > 200 ? '...' : ''),
        tags: blogPostData.tags || [],
        imageUrl: blogPostData.imageUrl,
        status: blogPostData.status || 'draft',
        timestamp: new Date(),
      });

      return data;
    } catch (error) {
      this.logger.error(`Error in createBlogPost: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error creating blog post', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Met à jour un article de blog existant
   * @param id ID de l'article de blog
   * @param updateData Données à mettre à jour
   * @returns Article de blog mis à jour
   */
  async updateBlogPost(id: string, updateData: Record<string, any>): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.patch(`${this.socialPlatformVideoUrl}/api/blog/${id}`, updateData, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error updating blog post: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error updating blog post',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      this.logger.error(`Error in updateBlogPost: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error updating blog post', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Supprime un article de blog
   * @param id ID de l'article de blog
   * @returns Résultat de l'opération
   */
  async deleteBlogPost(id: string): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.delete(`${this.socialPlatformVideoUrl}/api/blog/${id}`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error deleting blog post: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error deleting blog post',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      // Émettre un événement pour la suppression de l'article de blog
      this.eventEmitter.emit('blog.deleted', {
        blogPostId: id,
        timestamp: new Date(),
      });

      return data;
    } catch (error) {
      this.logger.error(`Error in deleteBlogPost: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error deleting blog post', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Récupère les commentaires d'un article de blog
   * @param id ID de l'article de blog
   * @returns Liste des commentaires
   */
  async getBlogPostComments(id: string): Promise<any[]> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(`${this.socialPlatformVideoUrl}/api/blog/${id}/comments`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error fetching blog post comments: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error fetching blog post comments',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      this.logger.error(`Error in getBlogPostComments: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error fetching blog post comments', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Ajoute un commentaire à un article de blog
   * @param id ID de l'article de blog
   * @param commentData Données du commentaire
   * @returns Commentaire ajouté
   */
  async addBlogPostComment(id: string, commentData: {
    userId: string;
    userName: string;
    content: string;
  }): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.post(`${this.socialPlatformVideoUrl}/api/blog/${id}/comments`, commentData, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error adding blog post comment: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error adding blog post comment',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      // Émettre un événement pour le nouveau commentaire
      this.eventEmitter.emit('blog.comment', {
        blogPostId: id,
        commentId: data.id,
        userId: commentData.userId,
        userName: commentData.userName,
        content: commentData.content,
        timestamp: new Date(),
      });

      return data;
    } catch (error) {
      this.logger.error(`Error in addBlogPostComment: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error adding blog post comment', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Ajoute ou retire un like à un article de blog
   * @param id ID de l'article de blog
   * @param userId ID de l'utilisateur
   * @param action 'like' ou 'unlike'
   * @returns Résultat de l'opération
   */
  async toggleBlogPostLike(id: string, userId: string, action: 'like' | 'unlike'): Promise<any> {
    try {
      const endpoint = action === 'like' ? 'like' : 'unlike';
      const { data } = await firstValueFrom(
        this.httpService.post(`${this.socialPlatformVideoUrl}/api/blog/${id}/${endpoint}`, { userId }, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error toggling blog post like: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error toggling blog post like',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      // Émettre un événement pour le like/unlike
      this.eventEmitter.emit('blog.like', {
        blogPostId: id,
        userId: userId,
        action: action,
        timestamp: new Date(),
      });

      return data;
    } catch (error) {
      this.logger.error(`Error in toggleBlogPostLike: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error toggling blog post like', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
