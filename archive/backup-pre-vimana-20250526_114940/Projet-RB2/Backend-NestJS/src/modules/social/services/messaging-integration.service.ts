import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { catchError, firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { EventEmitter2 } from '@nestjs/event-emitter';

/**
 * Service d'intégration avec le microservice de messagerie
 * Permet d'envoyer des messages et des notifications depuis le microservice Social
 */
@Injectable()
export class MessagingIntegrationService {
  private readonly logger = new Logger(MessagingIntegrationService.name);
  private readonly messagingServiceUrl: string;
  private readonly apiKey: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.messagingServiceUrl = this.configService.get<string>('microservices.messaging.url', 'http://messaging-service:5178');
    this.apiKey = this.configService.get<string>('microservices.messaging.apiKey', 'default-api-key');
    
    // Écouter les événements du microservice Social pour les transmettre au microservice de messagerie
    this.setupEventListeners();
  }

  /**
   * Configure les écouteurs d'événements pour l'intégration avec le microservice de messagerie
   */
  private setupEventListeners() {
    // Événement de création de livestream
    this.eventEmitter.on('livestream.created', (data) => {
      this.sendLivestreamNotification(
        data.livestreamId,
        data.hostId,
        'created',
        `Nouveau livestream: ${data.title}`,
      );
    });

    // Événement de démarrage de livestream
    this.eventEmitter.on('livestream.started', (data) => {
      this.sendLivestreamNotification(
        data.livestreamId,
        null, // Envoyer à tous les abonnés
        'started',
        `Le livestream vient de commencer!`,
      );
    });

    // Événement de fin de livestream
    this.eventEmitter.on('livestream.ended', (data) => {
      this.sendLivestreamNotification(
        data.livestreamId,
        null, // Envoyer à tous les abonnés
        'ended',
        `Le livestream est terminé.`,
      );
    });

    // Événement de nouveau message dans un livestream
    this.eventEmitter.on('livestream.message', (data) => {
      this.relayLivestreamMessage(
        data.livestreamId,
        data.userId,
        data.userName,
        data.content,
      );
    });

    // Événement de nouveau commentaire sur un article de blog
    this.eventEmitter.on('blog.comment', (data) => {
      this.sendBlogCommentNotification(
        data.blogPostId,
        data.userId,
        data.userName,
        data.content,
      );
    });
  }

  /**
   * Envoie une notification concernant un livestream via le microservice de messagerie
   * @param livestreamId ID du livestream
   * @param recipientId ID du destinataire (null pour tous les abonnés)
   * @param eventType Type d'événement (created, started, ended)
   * @param message Message de notification
   */
  async sendLivestreamNotification(
    livestreamId: string,
    recipientId: string | null,
    eventType: 'created' | 'started' | 'ended',
    message: string,
  ): Promise<void> {
    try {
      const payload = {
        type: 'livestream_notification',
        livestreamId,
        recipientId,
        eventType,
        message,
        timestamp: new Date().toISOString(),
      };

      await firstValueFrom(
        this.httpService.post(`${this.messagingServiceUrl}/api/notifications`, payload, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error sending livestream notification: ${error.message}`, error.stack);
            throw error;
          }),
        ),
      );

      this.logger.log(`Livestream notification sent: ${eventType} - ${livestreamId}`);
    } catch (error) {
      this.logger.error(`Failed to send livestream notification: ${error.message}`, error.stack);
    }
  }

  /**
   * Relaie un message de livestream au microservice de messagerie
   * @param livestreamId ID du livestream
   * @param userId ID de l'utilisateur qui a envoyé le message
   * @param userName Nom de l'utilisateur qui a envoyé le message
   * @param content Contenu du message
   */
  async relayLivestreamMessage(
    livestreamId: string,
    userId: string,
    userName: string,
    content: string,
  ): Promise<void> {
    try {
      const payload = {
        type: 'livestream_message',
        livestreamId,
        userId,
        userName,
        content,
        timestamp: new Date().toISOString(),
      };

      await firstValueFrom(
        this.httpService.post(`${this.messagingServiceUrl}/api/messages/relay`, payload, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error relaying livestream message: ${error.message}`, error.stack);
            throw error;
          }),
        ),
      );

      this.logger.log(`Livestream message relayed: ${livestreamId} - ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to relay livestream message: ${error.message}`, error.stack);
    }
  }

  /**
   * Envoie une notification concernant un commentaire sur un article de blog
   * @param blogPostId ID de l'article de blog
   * @param userId ID de l'utilisateur qui a commenté
   * @param userName Nom de l'utilisateur qui a commenté
   * @param content Contenu du commentaire
   */
  async sendBlogCommentNotification(
    blogPostId: string,
    userId: string,
    userName: string,
    content: string,
  ): Promise<void> {
    try {
      const payload = {
        type: 'blog_comment',
        blogPostId,
        userId,
        userName,
        content,
        timestamp: new Date().toISOString(),
      };

      await firstValueFrom(
        this.httpService.post(`${this.messagingServiceUrl}/api/notifications`, payload, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error sending blog comment notification: ${error.message}`, error.stack);
            throw error;
          }),
        ),
      );

      this.logger.log(`Blog comment notification sent: ${blogPostId} - ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to send blog comment notification: ${error.message}`, error.stack);
    }
  }
}
