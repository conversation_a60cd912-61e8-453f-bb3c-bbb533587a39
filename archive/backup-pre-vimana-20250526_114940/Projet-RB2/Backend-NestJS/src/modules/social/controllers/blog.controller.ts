import { Controller, Get, Post, Patch, Delete, Body, Param, Query, UseGuards, HttpException, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { User } from '../../users/decorators/user.decorator';
import { BlogService } from '../services/blog.service';

@ApiTags('social-blog')
@Controller('social/blog')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class BlogController {
  constructor(
    private readonly blogService: BlogService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all blog posts' })
  @ApiQuery({ name: 'tags', required: false, description: 'Filter by tags (comma-separated)' })
  @ApiQuery({ name: 'authorId', required: false, description: 'Filter by author ID' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status (draft, published, archived)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limit the number of results' })
  @ApiQuery({ name: 'offset', required: false, description: 'Offset for pagination' })
  @ApiResponse({ status: 200, description: 'Returns all blog posts' })
  async getBlogPosts(
    @Query() filters: Record<string, any>,
  ) {
    // Convertir les tags en tableau s'ils sont fournis sous forme de chaîne
    if (filters.tags && typeof filters.tags === 'string') {
      filters.tags = filters.tags.split(',').map(tag => tag.trim());
    }

    const blogPosts = await this.blogService.getBlogPosts(filters);
    return {
      success: true,
      data: blogPosts,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a blog post by ID' })
  @ApiParam({ name: 'id', description: 'Blog post ID' })
  @ApiResponse({ status: 200, description: 'Returns the blog post' })
  @ApiResponse({ status: 404, description: 'Blog post not found' })
  async getBlogPostById(
    @Param('id') id: string,
  ) {
    const blogPost = await this.blogService.getBlogPostById(id);
    return {
      success: true,
      data: blogPost,
    };
  }

  @Post()
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Create a new blog post' })
  @ApiResponse({ status: 201, description: 'Blog post created successfully' })
  async createBlogPost(
    @Body() blogPostData: {
      title: string;
      content: string;
      tags?: string[];
      imageUrl?: string;
      publishDate?: string;
      status?: 'draft' | 'published' | 'archived';
      metadata?: Record<string, any>;
    },
    @User() user: any,
  ) {
    const createdBlogPost = await this.blogService.createBlogPost({
      ...blogPostData,
      authorId: user.id,
      authorName: `${user.firstName} ${user.lastName}`,
    });
    
    return {
      success: true,
      data: createdBlogPost,
      message: 'Blog post created successfully',
    };
  }

  @Patch(':id')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Update a blog post' })
  @ApiParam({ name: 'id', description: 'Blog post ID' })
  @ApiResponse({ status: 200, description: 'Blog post updated successfully' })
  @ApiResponse({ status: 404, description: 'Blog post not found' })
  async updateBlogPost(
    @Param('id') id: string,
    @Body() updateData: Record<string, any>,
    @User() user: any,
  ) {
    // Vérifier que l'utilisateur est l'auteur du blog post
    const blogPost = await this.blogService.getBlogPostById(id);
    if (blogPost.authorId !== user.id && user.role !== 'ADMIN') {
      throw new HttpException('You are not authorized to update this blog post', HttpStatus.FORBIDDEN);
    }

    const updatedBlogPost = await this.blogService.updateBlogPost(id, updateData);
    
    return {
      success: true,
      data: updatedBlogPost,
      message: 'Blog post updated successfully',
    };
  }

  @Delete(':id')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Delete a blog post' })
  @ApiParam({ name: 'id', description: 'Blog post ID' })
  @ApiResponse({ status: 200, description: 'Blog post deleted successfully' })
  @ApiResponse({ status: 404, description: 'Blog post not found' })
  async deleteBlogPost(
    @Param('id') id: string,
    @User() user: any,
  ) {
    // Vérifier que l'utilisateur est l'auteur du blog post
    const blogPost = await this.blogService.getBlogPostById(id);
    if (blogPost.authorId !== user.id && user.role !== 'ADMIN') {
      throw new HttpException('You are not authorized to delete this blog post', HttpStatus.FORBIDDEN);
    }

    const result = await this.blogService.deleteBlogPost(id);
    
    return {
      success: true,
      data: result,
      message: 'Blog post deleted successfully',
    };
  }

  @Get(':id/comments')
  @ApiOperation({ summary: 'Get comments for a blog post' })
  @ApiParam({ name: 'id', description: 'Blog post ID' })
  @ApiResponse({ status: 200, description: 'Returns the blog post comments' })
  @ApiResponse({ status: 404, description: 'Blog post not found' })
  async getBlogPostComments(
    @Param('id') id: string,
  ) {
    const comments = await this.blogService.getBlogPostComments(id);
    
    return {
      success: true,
      data: comments,
    };
  }

  @Post(':id/comments')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Add a comment to a blog post' })
  @ApiParam({ name: 'id', description: 'Blog post ID' })
  @ApiResponse({ status: 201, description: 'Comment added successfully' })
  @ApiResponse({ status: 404, description: 'Blog post not found' })
  async addBlogPostComment(
    @Param('id') id: string,
    @Body() commentData: {
      content: string;
    },
    @User() user: any,
  ) {
    const comment = await this.blogService.addBlogPostComment(id, {
      userId: user.id,
      userName: `${user.firstName} ${user.lastName}`,
      content: commentData.content,
    });
    
    return {
      success: true,
      data: comment,
      message: 'Comment added successfully',
    };
  }

  @Post(':id/like')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Like a blog post' })
  @ApiParam({ name: 'id', description: 'Blog post ID' })
  @ApiResponse({ status: 200, description: 'Blog post liked successfully' })
  @ApiResponse({ status: 404, description: 'Blog post not found' })
  async likeBlogPost(
    @Param('id') id: string,
    @User() user: any,
  ) {
    const result = await this.blogService.toggleBlogPostLike(id, user.id, 'like');
    
    return {
      success: true,
      data: result,
      message: 'Blog post liked successfully',
    };
  }

  @Post(':id/unlike')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Unlike a blog post' })
  @ApiParam({ name: 'id', description: 'Blog post ID' })
  @ApiResponse({ status: 200, description: 'Blog post unliked successfully' })
  @ApiResponse({ status: 404, description: 'Blog post not found' })
  async unlikeBlogPost(
    @Param('id') id: string,
    @User() user: any,
  ) {
    const result = await this.blogService.toggleBlogPostLike(id, user.id, 'unlike');
    
    return {
      success: true,
      data: result,
      message: 'Blog post unliked successfully',
    };
  }
}
