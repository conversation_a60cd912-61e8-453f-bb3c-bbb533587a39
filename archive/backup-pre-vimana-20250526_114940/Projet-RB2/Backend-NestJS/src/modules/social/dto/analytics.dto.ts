import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsEnum, IsUUID } from 'class-validator';
import { SocialContentType } from '../../../prisma/prisma-types';

export class TrackEventDto {
  @ApiProperty({ description: 'Type of the event (view, like, comment, share, etc.)' })
  @IsString()
  @IsNotEmpty()
  eventType: string;

  @ApiProperty({ description: 'ID of the entity (livestream, blog post, etc.)' })
  @IsUUID()
  @IsNotEmpty()
  entityId: string;

  @ApiProperty({ description: 'Type of the entity', enum: SocialContentType })
  @IsEnum(SocialContentType)
  @IsNotEmpty()
  entityType: SocialContentType;

  @ApiPropertyOptional({ description: 'Additional metadata for the event', type: 'object' })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class AnalyticsFilterDto {
  @ApiPropertyOptional({ description: 'Period for analytics (day, week, month, year)', default: 'month' })
  @IsString()
  @IsOptional()
  period?: string;

  @ApiPropertyOptional({ description: 'Type of content to filter by', enum: SocialContentType })
  @IsEnum(SocialContentType)
  @IsOptional()
  contentType?: SocialContentType;

  @ApiPropertyOptional({ description: 'User ID to filter by' })
  @IsUUID()
  @IsOptional()
  userId?: string;

  @ApiPropertyOptional({ description: 'Start date for the analytics period' })
  @IsString()
  @IsOptional()
  startDate?: string;

  @ApiPropertyOptional({ description: 'End date for the analytics period' })
  @IsString()
  @IsOptional()
  endDate?: string;

  @ApiPropertyOptional({ description: 'Limit the number of results' })
  @IsOptional()
  limit?: number;

  @ApiPropertyOptional({ description: 'Skip a number of results (for pagination)' })
  @IsOptional()
  skip?: number;
}
