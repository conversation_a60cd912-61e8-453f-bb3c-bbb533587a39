import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsBoolean, IsDateString, IsEnum, IsArray, IsUUID } from 'class-validator';
import { LivestreamStatus, CommentType } from '../../../prisma/prisma-types';

export class CreateLivestreamDto {
  @ApiProperty({ description: 'Title of the livestream' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'Description of the livestream' })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiPropertyOptional({ description: 'Scheduled start time of the livestream' })
  @IsDateString()
  @IsOptional()
  startTime?: string;

  @ApiPropertyOptional({ description: 'Thumbnail URL for the livestream' })
  @IsString()
  @IsOptional()
  thumbnailUrl?: string;

  @ApiPropertyOptional({ description: 'Whether the livestream is private' })
  @IsBoolean()
  @IsOptional()
  isPrivate?: boolean;

  @ApiPropertyOptional({ description: 'Additional metadata for the livestream', type: 'object' })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class UpdateLivestreamDto {
  @ApiPropertyOptional({ description: 'Title of the livestream' })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiPropertyOptional({ description: 'Description of the livestream' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ description: 'Status of the livestream', enum: LivestreamStatus })
  @IsEnum(LivestreamStatus)
  @IsOptional()
  status?: LivestreamStatus;

  @ApiPropertyOptional({ description: 'Scheduled start time of the livestream' })
  @IsDateString()
  @IsOptional()
  startTime?: string;

  @ApiPropertyOptional({ description: 'End time of the livestream' })
  @IsDateString()
  @IsOptional()
  endTime?: string;

  @ApiPropertyOptional({ description: 'Thumbnail URL for the livestream' })
  @IsString()
  @IsOptional()
  thumbnailUrl?: string;

  @ApiPropertyOptional({ description: 'Stream URL for the livestream' })
  @IsString()
  @IsOptional()
  streamUrl?: string;

  @ApiPropertyOptional({ description: 'Recording URL for the livestream' })
  @IsString()
  @IsOptional()
  recordingUrl?: string;

  @ApiPropertyOptional({ description: 'Whether the livestream is private' })
  @IsBoolean()
  @IsOptional()
  isPrivate?: boolean;

  @ApiPropertyOptional({ description: 'Additional metadata for the livestream', type: 'object' })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class LivestreamMessageDto {
  @ApiProperty({ description: 'Content of the message' })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiPropertyOptional({ description: 'Type of the message', enum: CommentType, default: CommentType.TEXT })
  @IsEnum(CommentType)
  @IsOptional()
  type?: CommentType;
}

export class LivestreamParticipantDto {
  @ApiProperty({ description: 'User ID of the participant' })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({ description: 'Livestream ID' })
  @IsUUID()
  @IsNotEmpty()
  livestreamId: string;
}

export class LivestreamFilterDto {
  @ApiPropertyOptional({ description: 'Status of the livestream', enum: LivestreamStatus })
  @IsEnum(LivestreamStatus)
  @IsOptional()
  status?: LivestreamStatus;

  @ApiPropertyOptional({ description: 'Host ID of the livestream' })
  @IsUUID()
  @IsOptional()
  hostId?: string;

  @ApiPropertyOptional({ description: 'Whether to include private livestreams' })
  @IsBoolean()
  @IsOptional()
  includePrivate?: boolean;

  @ApiPropertyOptional({ description: 'Limit the number of results' })
  @IsOptional()
  limit?: number;

  @ApiPropertyOptional({ description: 'Skip a number of results (for pagination)' })
  @IsOptional()
  skip?: number;
}
