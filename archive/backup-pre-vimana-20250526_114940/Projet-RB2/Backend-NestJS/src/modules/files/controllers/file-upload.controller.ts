import {
  Controller,
  Post,
  Get,
  Delete,
  Param,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  Query,
  BadRequestException,
  Req,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiConsumes, ApiBody, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { FileUploadService } from '../services/file-upload.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CurrentUser } from '../../../common/decorators';
import { Request } from 'express';

@ApiTags('files')
@Controller('files')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class FileUploadController {
  constructor(private readonly fileUploadService: FileUploadService) {}

  @Post('upload')
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload a single file securely' })
  @ApiResponse({ status: 201, description: 'File uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Invalid file or security check failed' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        category: {
          type: 'string',
          description: 'File category',
          example: 'partner-documents',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Query('category') category: string = 'general',
    @CurrentUser() user: any,
    @Req() req: Request,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    try {
      const ipAddress = req.ip || req.connection.remoteAddress;
      return await this.fileUploadService.uploadFile(file, user.id, category, ipAddress);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `File upload failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('upload/multiple')
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload multiple files securely' })
  @ApiResponse({ status: 201, description: 'Files uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Invalid files or security check failed' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
        category: {
          type: 'string',
          description: 'File category',
          example: 'partner-documents',
        },
      },
    },
  })
  @UseInterceptors(FilesInterceptor('files', 10)) // Max 10 files
  async uploadFiles(
    @UploadedFiles() files: Express.Multer.File[],
    @Query('category') category: string = 'general',
    @CurrentUser() user: any,
    @Req() req: Request,
  ) {
    if (!files || files.length === 0) {
      throw new BadRequestException('No files uploaded');
    }

    try {
      const ipAddress = req.ip || req.connection.remoteAddress;
      const uploadPromises = files.map(file =>
        this.fileUploadService.uploadFile(file, user.id, category, ipAddress)
      );
      return await Promise.all(uploadPromises);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Files upload failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get files by category' })
  async getFiles(
    @Query('category') category: string = 'general',
    @CurrentUser() user: any,
  ) {
    return this.fileUploadService.getFilesByUserAndCategory(user.id, category);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get file by ID with integrity check' })
  @ApiResponse({ status: 200, description: 'Returns the file information' })
  @ApiResponse({ status: 404, description: 'File not found' })
  async getFile(@Param('id') id: string, @CurrentUser() user: any) {
    const file = await this.fileUploadService.getFileById(id, user?.id);

    if (!file) {
      throw new HttpException('File not found', HttpStatus.NOT_FOUND);
    }

    // If integrity is compromised, add a warning header
    if (file.integrityCompromised) {
      throw new HttpException(
        {
          message: 'File integrity check failed. The file may have been tampered with.',
          file: {
            id: file.id,
            name: file.name,
            mimeType: file.mimeType,
            size: file.size,
            category: file.category,
            createdAt: file.createdAt,
            integrityCompromised: true
          }
        },
        HttpStatus.OK
      );
    }

    return file;
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete file by ID' })
  @ApiResponse({ status: 200, description: 'File deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'File not found' })
  async deleteFile(@Param('id') id: string, @CurrentUser() user: any) {
    try {
      // The security check is now handled in the service
      return await this.fileUploadService.deleteFile(id, user.id);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to delete file: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
