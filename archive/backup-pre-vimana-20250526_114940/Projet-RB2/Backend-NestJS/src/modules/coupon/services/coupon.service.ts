import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { CouponValidationService } from './coupon-validation.service';
import { CouponGenerationService } from './coupon-generation.service';
import { EventsService } from '../../events/events.service';
import { CreateCouponDto } from '../dto/create-coupon.dto';
import { UpdateCouponDto } from '../dto/update-coupon.dto';
import { ApplyCouponDto } from '../dto/apply-coupon.dto';
import { CouponType } from '../enums/coupon-type.enum';
import { CouponStatus } from '../enums/coupon-status.enum';

@Injectable()
export class CouponService {
  private readonly logger = new Logger(CouponService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly couponValidationService: CouponValidationService,
    private readonly couponGenerationService: CouponGenerationService,
    private readonly eventsService: EventsService,
  ) {}

  /**
   * Crée un nouveau coupon
   * @param createCouponDto Données du coupon
   * @returns Le coupon créé
   */
  async create(createCouponDto: CreateCouponDto) {
    try {
      this.logger.log(`Création d'un nouveau coupon: ${JSON.stringify(createCouponDto)}`);
      
      // Générer un code de coupon si non fourni
      const code = createCouponDto.code || await this.couponGenerationService.generateCouponCode();
      
      // Vérifier si le code existe déjà
      const existingCoupon = await this.prisma.coupon.findUnique({
        where: { code },
      });
      
      if (existingCoupon) {
        throw new BadRequestException(`Le code de coupon ${code} existe déjà`);
      }
      
      // Créer le coupon
      const coupon = await this.prisma.coupon.create({
        data: {
          code,
          type: createCouponDto.type,
          value: createCouponDto.value,
          minPurchaseAmount: createCouponDto.minPurchaseAmount,
          maxDiscountAmount: createCouponDto.maxDiscountAmount,
          startDate: createCouponDto.startDate,
          endDate: createCouponDto.endDate,
          usageLimit: createCouponDto.usageLimit,
          perUserLimit: createCouponDto.perUserLimit,
          isActive: createCouponDto.isActive ?? true,
          description: createCouponDto.description,
          applicableProducts: createCouponDto.applicableProducts,
          excludedProducts: createCouponDto.excludedProducts,
          applicableCategories: createCouponDto.applicableCategories,
          excludedCategories: createCouponDto.excludedCategories,
          metadata: createCouponDto.metadata || {},
        },
      });
      
      // Enregistrer un événement
      await this.eventsService.create({
        eventType: 'COUPON_CREATED',
        payload: {
          couponId: coupon.id,
          code: coupon.code,
          type: coupon.type,
          value: coupon.value,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Coupon créé avec succès: ${coupon.id}`);
      return coupon;
    } catch (error) {
      this.logger.error(`Erreur lors de la création du coupon: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère tous les coupons avec pagination
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @param includeInactive Inclure les coupons inactifs
   * @returns Liste paginée des coupons
   */
  async findAll(page = 1, limit = 10, includeInactive = false) {
    const skip = (page - 1) * limit;
    
    const where = includeInactive ? {} : { isActive: true };
    
    const [coupons, total] = await Promise.all([
      this.prisma.coupon.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.coupon.count({ where }),
    ]);
    
    return {
      coupons,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère un coupon par son ID
   * @param id ID du coupon
   * @returns Le coupon
   */
  async findOne(id: string) {
    const coupon = await this.prisma.coupon.findUnique({
      where: { id },
      include: {
        usages: {
          select: {
            id: true,
            userId: true,
            orderId: true,
            discountAmount: true,
            createdAt: true,
          },
        },
      },
    });
    
    if (!coupon) {
      throw new NotFoundException(`Coupon avec l'ID ${id} non trouvé`);
    }
    
    return coupon;
  }

  /**
   * Récupère un coupon par son code
   * @param code Code du coupon
   * @returns Le coupon
   */
  async findByCode(code: string) {
    const coupon = await this.prisma.coupon.findUnique({
      where: { code },
      include: {
        usages: {
          select: {
            id: true,
            userId: true,
            orderId: true,
            discountAmount: true,
            createdAt: true,
          },
        },
      },
    });
    
    if (!coupon) {
      throw new NotFoundException(`Coupon avec le code ${code} non trouvé`);
    }
    
    return coupon;
  }

  /**
   * Met à jour un coupon
   * @param id ID du coupon
   * @param updateCouponDto Données de mise à jour
   * @returns Le coupon mis à jour
   */
  async update(id: string, updateCouponDto: UpdateCouponDto) {
    try {
      // Vérifier si le coupon existe
      const existingCoupon = await this.findOne(id);
      
      // Vérifier si le code est modifié et s'il existe déjà
      if (updateCouponDto.code && updateCouponDto.code !== existingCoupon.code) {
        const couponWithCode = await this.prisma.coupon.findUnique({
          where: { code: updateCouponDto.code },
        });
        
        if (couponWithCode) {
          throw new BadRequestException(`Le code de coupon ${updateCouponDto.code} existe déjà`);
        }
      }
      
      // Mettre à jour le coupon
      const coupon = await this.prisma.coupon.update({
        where: { id },
        data: updateCouponDto,
      });
      
      // Enregistrer un événement
      await this.eventsService.create({
        eventType: 'COUPON_UPDATED',
        payload: {
          couponId: coupon.id,
          code: coupon.code,
          updates: updateCouponDto,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Coupon mis à jour avec succès: ${coupon.id}`);
      return coupon;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du coupon: ${error.message}`);
      throw error;
    }
  }

  /**
   * Supprime un coupon
   * @param id ID du coupon
   * @returns Le coupon supprimé
   */
  async remove(id: string) {
    try {
      // Vérifier si le coupon existe
      await this.findOne(id);
      
      // Supprimer le coupon
      const coupon = await this.prisma.coupon.delete({
        where: { id },
      });
      
      // Enregistrer un événement
      await this.eventsService.create({
        eventType: 'COUPON_DELETED',
        payload: {
          couponId: coupon.id,
          code: coupon.code,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Coupon supprimé avec succès: ${coupon.id}`);
      return coupon;
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression du coupon: ${error.message}`);
      throw error;
    }
  }

  /**
   * Active un coupon
   * @param id ID du coupon
   * @returns Le coupon activé
   */
  async activate(id: string) {
    return this.update(id, { isActive: true });
  }

  /**
   * Désactive un coupon
   * @param id ID du coupon
   * @returns Le coupon désactivé
   */
  async deactivate(id: string) {
    return this.update(id, { isActive: false });
  }

  /**
   * Applique un coupon à une commande
   * @param applyCouponDto Données d'application du coupon
   * @returns Résultat de l'application du coupon
   */
  async applyCoupon(applyCouponDto: ApplyCouponDto) {
    try {
      this.logger.log(`Application du coupon ${applyCouponDto.code} à la commande ${applyCouponDto.orderId}`);
      
      // Récupérer le coupon
      const coupon = await this.findByCode(applyCouponDto.code);
      
      // Valider le coupon
      const validationResult = await this.couponValidationService.validateCoupon(
        coupon,
        applyCouponDto.userId,
        applyCouponDto.orderId,
        applyCouponDto.orderAmount,
        applyCouponDto.items,
      );
      
      if (!validationResult.isValid) {
        throw new BadRequestException(validationResult.message);
      }
      
      // Calculer le montant de la remise
      const discountAmount = this.calculateDiscountAmount(
        coupon,
        applyCouponDto.orderAmount,
        applyCouponDto.items,
      );
      
      // Enregistrer l'utilisation du coupon
      const couponUsage = await this.prisma.couponUsage.create({
        data: {
          couponId: coupon.id,
          userId: applyCouponDto.userId,
          orderId: applyCouponDto.orderId,
          discountAmount,
        },
      });
      
      // Enregistrer un événement
      await this.eventsService.create({
        eventType: 'COUPON_APPLIED',
        payload: {
          couponId: coupon.id,
          code: coupon.code,
          userId: applyCouponDto.userId,
          orderId: applyCouponDto.orderId,
          discountAmount,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Coupon appliqué avec succès: ${coupon.code}, remise: ${discountAmount}`);
      
      return {
        couponId: coupon.id,
        code: coupon.code,
        type: coupon.type,
        discountAmount,
        orderAmountAfterDiscount: applyCouponDto.orderAmount - discountAmount,
        usageId: couponUsage.id,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de l'application du coupon: ${error.message}`);
      throw error;
    }
  }

  /**
   * Calcule le montant de la remise
   * @param coupon Coupon
   * @param orderAmount Montant de la commande
   * @param items Éléments de la commande
   * @returns Montant de la remise
   */
  private calculateDiscountAmount(coupon: any, orderAmount: number, items: any[] = []) {
    let discountAmount = 0;
    
    switch (coupon.type) {
      case CouponType.PERCENTAGE:
        // Calculer la remise en pourcentage
        discountAmount = (orderAmount * coupon.value) / 100;
        
        // Limiter la remise au montant maximum si défini
        if (coupon.maxDiscountAmount && discountAmount > coupon.maxDiscountAmount) {
          discountAmount = coupon.maxDiscountAmount;
        }
        break;
        
      case CouponType.FIXED_AMOUNT:
        // Appliquer la remise fixe
        discountAmount = coupon.value;
        
        // La remise ne peut pas dépasser le montant de la commande
        if (discountAmount > orderAmount) {
          discountAmount = orderAmount;
        }
        break;
        
      case CouponType.FREE_SHIPPING:
        // Dans une implémentation réelle, on récupérerait les frais de livraison
        discountAmount = 0; // Simuler les frais de livraison
        break;
        
      case CouponType.BUY_X_GET_Y:
        // Dans une implémentation réelle, on appliquerait la logique "acheter X, obtenir Y"
        discountAmount = 0; // Simuler la remise
        break;
        
      default:
        discountAmount = 0;
    }
    
    return discountAmount;
  }

  /**
   * Récupère les statistiques des coupons
   * @returns Statistiques des coupons
   */
  async getCouponStats() {
    const [
      totalCoupons,
      activeCoupons,
      expiredCoupons,
      totalUsages,
      totalDiscountAmount,
    ] = await Promise.all([
      this.prisma.coupon.count(),
      this.prisma.coupon.count({
        where: {
          isActive: true,
          endDate: {
            gte: new Date(),
          },
        },
      }),
      this.prisma.coupon.count({
        where: {
          endDate: {
            lt: new Date(),
          },
        },
      }),
      this.prisma.couponUsage.count(),
      this.prisma.couponUsage.aggregate({
        _sum: {
          discountAmount: true,
        },
      }),
    ]);
    
    return {
      totalCoupons,
      activeCoupons,
      expiredCoupons,
      totalUsages,
      totalDiscountAmount: totalDiscountAmount._sum.discountAmount || 0,
    };
  }

  /**
   * Récupère les coupons d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Liste des coupons de l'utilisateur
   */
  async getUserCoupons(userId: string) {
    // Récupérer tous les coupons actifs
    const activeCoupons = await this.prisma.coupon.findMany({
      where: {
        isActive: true,
        endDate: {
          gte: new Date(),
        },
      },
    });
    
    // Récupérer les utilisations de coupons de l'utilisateur
    const userCouponUsages = await this.prisma.couponUsage.findMany({
      where: {
        userId,
      },
      select: {
        couponId: true,
        createdAt: true,
      },
    });
    
    // Créer une map des utilisations par coupon
    const couponUsagesMap = new Map();
    userCouponUsages.forEach((usage: any) => {
      if (!couponUsagesMap.has(usage.couponId)) {
        couponUsagesMap.set(usage.couponId, []);
      }
      couponUsagesMap.get(usage.couponId).push(usage);
    });
    
    // Filtrer les coupons disponibles pour l'utilisateur
    const availableCoupons = activeCoupons.filter((coupon: any) => {
      const usages = couponUsagesMap.get(coupon.id) || [];
      
      // Vérifier si l'utilisateur a atteint sa limite d'utilisation
      if (coupon.perUserLimit && usages.length >= coupon.perUserLimit) {
        return false;
      }
      
      return true;
    });
    
    return availableCoupons;
  }
}
