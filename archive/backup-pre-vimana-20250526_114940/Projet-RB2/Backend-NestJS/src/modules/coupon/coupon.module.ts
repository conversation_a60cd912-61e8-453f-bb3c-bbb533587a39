import { Module } from '@nestjs/common';
import { CouponService } from './services/coupon.service';
import { CouponController } from './controllers/coupon.controller';
import { PrismaModule } from '../../prisma/prisma.module';
// import { PromotionService } from './services/promotion.service';
// import { DiscountService } from './services/discount.service';
import { CouponValidationService } from './services/coupon-validation.service';
import { CouponGenerationService } from './services/coupon-generation.service';
import { EventsModule } from '../events/events.module';

@Module({
  imports: [PrismaModule, EventsModule],
  controllers: [CouponController],
  providers: [
    CouponService,
    CouponValidationService,
    CouponGenerationService,
    // PromotionService,
    // DiscountService,
  ],
  exports: [
    CouponService,
    // PromotionService,
    // DiscountService,
  ],
})
export class CouponModule {}
