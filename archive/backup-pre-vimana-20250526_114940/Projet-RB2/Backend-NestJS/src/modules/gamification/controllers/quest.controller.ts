import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { QuestService } from '../services/quest.service';
import { CreateQuestDto } from '../dto/create-quest.dto';
import { UpdateQuestDto } from '../dto/update-quest.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { PaginationPipe } from '../../../common/pipes/pagination.pipe';
import { PaginationOptions } from '../../../shared/interfaces';
import { ParseObjectIdPipe } from '../../../common/pipes/parse-object-id.pipe';
import { Cacheable } from '../../../common/decorators';

@ApiTags('quests')
@Controller('game-systems/:systemId/quests')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class QuestController {
  constructor(private readonly questService: QuestService) {}

  @Post()
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Créer une nouvelle quête' })
  @ApiResponse({ status: 201, description: 'La quête a été créée avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 404, description: 'Système de jeu non trouvé.' })
  @ApiResponse({ status: 409, description: 'Une quête avec ce nom existe déjà pour ce système de jeu.' })
  create(
    @Param('systemId', ParseObjectIdPipe) systemId: string,
    @Body() createQuestDto: CreateQuestDto,
  ) {
    // Assurer que le systemId du DTO correspond au systemId de l'URL
    createQuestDto.systemId = systemId;
    return this.questService.create(createQuestDto);
  }

  @Get()
  @Cacheable(60) // Cache pour 60 secondes
  @ApiOperation({ summary: 'Récupérer toutes les quêtes d\'un système de jeu' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Champ de tri' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordre de tri' })
  @ApiResponse({ status: 200, description: 'Liste des quêtes récupérée avec succès.' })
  @ApiResponse({ status: 404, description: 'Système de jeu non trouvé.' })
  findAll(
    @Param('systemId', ParseObjectIdPipe) systemId: string,
    @Query(new PaginationPipe()) paginationOptions: PaginationOptions,
  ) {
    return this.questService.findAll(systemId, paginationOptions);
  }

  @Get(':id')
  @Cacheable(30) // Cache pour 30 secondes
  @ApiOperation({ summary: 'Récupérer une quête par son ID' })
  @ApiResponse({ status: 200, description: 'La quête a été récupérée avec succès.' })
  @ApiResponse({ status: 404, description: 'Quête non trouvée.' })
  findOne(@Param('id', ParseObjectIdPipe) id: string) {
    return this.questService.findOne(id);
  }

  @Patch(':id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Mettre à jour une quête' })
  @ApiResponse({ status: 200, description: 'La quête a été mise à jour avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 404, description: 'Quête non trouvée.' })
  @ApiResponse({ status: 409, description: 'Une quête avec ce nom existe déjà pour ce système de jeu.' })
  update(
    @Param('id', ParseObjectIdPipe) id: string,
    @Body() updateQuestDto: UpdateQuestDto,
  ) {
    return this.questService.update(id, updateQuestDto);
  }

  @Delete(':id')
  @Roles('ADMIN')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Supprimer une quête' })
  @ApiResponse({ status: 204, description: 'La quête a été supprimée avec succès.' })
  @ApiResponse({ status: 404, description: 'Quête non trouvée.' })
  remove(@Param('id', ParseObjectIdPipe) id: string) {
    return this.questService.remove(id);
  }

  @Patch(':id/status')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Mettre à jour le statut d\'une quête' })
  @ApiResponse({ status: 200, description: 'Le statut de la quête a été mis à jour avec succès.' })
  @ApiResponse({ status: 400, description: 'Statut invalide.' })
  @ApiResponse({ status: 404, description: 'Quête non trouvée.' })
  updateStatus(
    @Param('id', ParseObjectIdPipe) id: string,
    @Body('status') status: string,
  ) {
    return this.questService.updateStatus(id, status);
  }
}
