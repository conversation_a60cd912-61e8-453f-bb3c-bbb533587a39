import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { GameSystemService } from '../services/game-system.service';
import { CreateGameSystemDto } from '../dto/create-game-system.dto';
import { UpdateGameSystemDto } from '../dto/update-game-system.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { PaginationPipe } from '../../../common/pipes/pagination.pipe';
import { PaginationOptions } from '../../../shared/interfaces';
import { ParseObjectIdPipe } from '../../../common/pipes/parse-object-id.pipe';
import { Cacheable } from '../../../common/decorators';

@ApiTags('game-systems')
@Controller('game-systems')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class GameSystemController {
  constructor(private readonly gameSystemService: GameSystemService) {}

  @Post()
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Créer un nouveau système de jeu' })
  @ApiResponse({ status: 201, description: 'Le système de jeu a été créé avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 409, description: 'Un système de jeu avec ce nom existe déjà.' })
  create(@Body() createGameSystemDto: CreateGameSystemDto) {
    return this.gameSystemService.create(createGameSystemDto);
  }

  @Get()
  @Cacheable(60) // Cache pour 60 secondes
  @ApiOperation({ summary: 'Récupérer tous les systèmes de jeu' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Champ de tri' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordre de tri' })
  @ApiResponse({ status: 200, description: 'Liste des systèmes de jeu récupérée avec succès.' })
  findAll(@Query(new PaginationPipe()) paginationOptions: PaginationOptions) {
    return this.gameSystemService.findAll(paginationOptions);
  }

  @Get(':id')
  @Cacheable(30) // Cache pour 30 secondes
  @ApiOperation({ summary: 'Récupérer un système de jeu par son ID' })
  @ApiResponse({ status: 200, description: 'Le système de jeu a été récupéré avec succès.' })
  @ApiResponse({ status: 404, description: 'Système de jeu non trouvé.' })
  findOne(@Param('id', ParseObjectIdPipe) id: string) {
    return this.gameSystemService.findOne(id);
  }

  @Patch(':id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Mettre à jour un système de jeu' })
  @ApiResponse({ status: 200, description: 'Le système de jeu a été mis à jour avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 404, description: 'Système de jeu non trouvé.' })
  @ApiResponse({ status: 409, description: 'Un système de jeu avec ce nom existe déjà.' })
  update(
    @Param('id', ParseObjectIdPipe) id: string,
    @Body() updateGameSystemDto: UpdateGameSystemDto,
  ) {
    return this.gameSystemService.update(id, updateGameSystemDto);
  }

  @Delete(':id')
  @Roles('ADMIN')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Supprimer un système de jeu' })
  @ApiResponse({ status: 204, description: 'Le système de jeu a été supprimé avec succès.' })
  @ApiResponse({ status: 404, description: 'Système de jeu non trouvé.' })
  remove(@Param('id', ParseObjectIdPipe) id: string) {
    return this.gameSystemService.remove(id);
  }
}
