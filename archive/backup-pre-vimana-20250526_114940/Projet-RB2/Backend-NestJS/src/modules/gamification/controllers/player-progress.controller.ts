import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { PlayerProgressService } from '../services/player-progress.service';
import { CreatePlayerProgressDto } from '../dto/create-player-progress.dto';
import { UpdatePlayerProgressDto } from '../dto/update-player-progress.dto';
import { AddXpDto } from '../dto/add-xp.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { PaginationPipe } from '../../../common/pipes/pagination.pipe';
import { PaginationOptions } from '../../../shared/interfaces';
import { ParseObjectIdPipe } from '../../../common/pipes/parse-object-id.pipe';
import { Cacheable } from '../../../common/decorators';
import { CurrentUser } from '../../../common/decorators';

@ApiTags('player-progress')
@Controller('player-progress')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class PlayerProgressController {
  constructor(private readonly playerProgressService: PlayerProgressService) {}

  @Post()
  @Roles('ADMIN', 'MODERATOR')
  @ApiOperation({ summary: 'Créer une nouvelle progression de joueur' })
  @ApiResponse({ status: 201, description: 'La progression du joueur a été créée avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 404, description: 'Niveau de jeu non trouvé.' })
  @ApiResponse({ status: 409, description: 'Le joueur a déjà une progression pour ce niveau.' })
  create(@Body() createPlayerProgressDto: CreatePlayerProgressDto) {
    return this.playerProgressService.create(createPlayerProgressDto);
  }

  @Get('player/:playerId')
  @Cacheable(30) // Cache pour 30 secondes
  @ApiOperation({ summary: 'Récupérer toutes les progressions d\'un joueur' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Champ de tri' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordre de tri' })
  @ApiResponse({ status: 200, description: 'Liste des progressions du joueur récupérée avec succès.' })
  findAll(
    @Param('playerId', ParseObjectIdPipe) playerId: string,
    @Query(new PaginationPipe()) paginationOptions: PaginationOptions,
  ) {
    return this.playerProgressService.findAll(playerId, paginationOptions);
  }

  @Get('my-progress')
  @Cacheable(30) // Cache pour 30 secondes
  @ApiOperation({ summary: 'Récupérer les progressions de l\'utilisateur connecté' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Champ de tri' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordre de tri' })
  @ApiResponse({ status: 200, description: 'Liste des progressions de l\'utilisateur récupérée avec succès.' })
  findMyProgress(
    @CurrentUser('id') userId: string,
    @Query(new PaginationPipe()) paginationOptions: PaginationOptions,
  ) {
    return this.playerProgressService.findAll(userId, paginationOptions);
  }

  @Get('my-level')
  @Cacheable(30) // Cache pour 30 secondes
  @ApiOperation({ summary: 'Récupérer le niveau actuel de l\'utilisateur connecté' })
  @ApiResponse({ status: 200, description: 'Niveau de l\'utilisateur récupéré avec succès.' })
  @ApiResponse({ status: 404, description: 'Aucune progression trouvée pour l\'utilisateur.' })
  getMyLevel(@CurrentUser('id') userId: string) {
    return this.playerProgressService.getPlayerLevel(userId);
  }

  @Get('player/:playerId/level')
  @Roles('ADMIN', 'MODERATOR')
  @Cacheable(30) // Cache pour 30 secondes
  @ApiOperation({ summary: 'Récupérer le niveau d\'un joueur' })
  @ApiResponse({ status: 200, description: 'Niveau du joueur récupéré avec succès.' })
  @ApiResponse({ status: 404, description: 'Aucune progression trouvée pour le joueur.' })
  getPlayerLevel(@Param('playerId', ParseObjectIdPipe) playerId: string) {
    return this.playerProgressService.getPlayerLevel(playerId);
  }

  @Get(':id')
  @Cacheable(30) // Cache pour 30 secondes
  @ApiOperation({ summary: 'Récupérer une progression de joueur par son ID' })
  @ApiResponse({ status: 200, description: 'La progression du joueur a été récupérée avec succès.' })
  @ApiResponse({ status: 404, description: 'Progression du joueur non trouvée.' })
  findOne(@Param('id', ParseObjectIdPipe) id: string) {
    return this.playerProgressService.findOne(id);
  }

  @Patch(':id')
  @Roles('ADMIN', 'MODERATOR')
  @ApiOperation({ summary: 'Mettre à jour une progression de joueur' })
  @ApiResponse({ status: 200, description: 'La progression du joueur a été mise à jour avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 404, description: 'Progression du joueur non trouvée.' })
  update(
    @Param('id', ParseObjectIdPipe) id: string,
    @Body() updatePlayerProgressDto: UpdatePlayerProgressDto,
  ) {
    return this.playerProgressService.update(id, updatePlayerProgressDto);
  }

  @Delete(':id')
  @Roles('ADMIN')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Supprimer une progression de joueur' })
  @ApiResponse({ status: 204, description: 'La progression du joueur a été supprimée avec succès.' })
  @ApiResponse({ status: 404, description: 'Progression du joueur non trouvée.' })
  remove(@Param('id', ParseObjectIdPipe) id: string) {
    return this.playerProgressService.remove(id);
  }

  @Patch(':id/add-xp')
  @Roles('ADMIN', 'MODERATOR')
  @ApiOperation({ summary: 'Ajouter des points d\'expérience à une progression de joueur' })
  @ApiResponse({ status: 200, description: 'Les points d\'expérience ont été ajoutés avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 404, description: 'Progression du joueur non trouvée.' })
  @ApiResponse({ status: 409, description: 'Le XP à ajouter doit être positif.' })
  addXp(
    @Param('id', ParseObjectIdPipe) id: string,
    @Body() addXpDto: AddXpDto,
  ) {
    return this.playerProgressService.addXp(id, addXpDto.xp);
  }
}
