import { IsString, IsNotEmpty, <PERSON>Optional, IsInt, Min, IsIn, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreatePlayerProgressDto {
  @ApiProperty({
    description: 'ID du joueur',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  playerId: string;

  @ApiProperty({
    description: 'ID du niveau',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  levelId: string;

  @ApiPropertyOptional({
    description: 'Points d\'expérience',
    example: 50,
    minimum: 0,
    default: 0,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  xp?: number;

  @ApiPropertyOptional({
    description: 'Statut de la progression',
    example: 'IN_PROGRESS',
    enum: ['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'FAILED'],
    default: 'IN_PROGRESS',
  })
  @IsOptional()
  @IsString()
  @IsIn(['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'FAILED'])
  status?: string;
}
