import { Injectable, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { CreatePlayerProgressDto } from '../dto/create-player-progress.dto';
import { UpdatePlayerProgressDto } from '../dto/update-player-progress.dto';
// import { any } from '@prisma/client';
import { PaginationOptions } from '../../../shared/interfaces';

@Injectable()
export class PlayerProgressService {
  private readonly logger = new Logger(PlayerProgressService.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(createanyDto: CreatePlayerProgressDto): Promise<any> {
    try {
      // Vérifier si le niveau existe
      const gameLevel = await this.prisma.gameLevel.findUnique({
        where: { id: createanyDto.levelId },
      });

      if (!gameLevel) {
        throw new NotFoundException(`Niveau de jeu avec l'ID ${createanyDto.levelId} non trouvé`);
      }

      // Vérifier si le joueur a déjà une progression pour ce niveau
      const existingProgress = await this.prisma.playerProgress.findFirst({
        where: {
          playerId: createanyDto.playerId,
          levelId: createanyDto.levelId,
        },
      });

      if (existingProgress) {
        throw new ConflictException(`Le joueur a déjà une progression pour ce niveau`);
      }

      // Créer la progression
      const playerProgress = await this.prisma.playerProgress.create({
        data: {
          playerId: createanyDto.playerId,
          levelId: createanyDto.levelId,
          xp: createanyDto.xp || 0,
          status: createanyDto.status || 'IN_PROGRESS',
        },
      });

      this.logger.log(`Progression du joueur créée avec succès: ${playerProgress.id}`);
      return playerProgress;
    } catch (error) {
      this.logger.error(`Erreur lors de la création de la progression du joueur: ${error.message}`);
      throw error;
    }
  }

  async findAll(playerId: string, options?: PaginationOptions): Promise<{ 
    playerProgress: any[]; 
    total: number; 
    page: number; 
    limit: number;
  }> {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;
    const sortBy = options?.sortBy || 'createdAt';
    const sortOrder = options?.sortOrder || 'desc';

    const [playerProgress, total] = await Promise.all([
      this.prisma.playerProgress.findMany({
        where: { playerId },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        include: {
          level: true,
        },
      }),
      this.prisma.playerProgress.count({
        where: { playerId },
      }),
    ]);

    return {
      playerProgress,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string): Promise<any> {
    const playerProgress = await this.prisma.playerProgress.findUnique({
      where: { id },
      include: {
        level: {
          include: {
            system: true,
          },
        },
      },
    });

    if (!playerProgress) {
      throw new NotFoundException(`Progression du joueur avec l'ID ${id} non trouvée`);
    }

    return playerProgress;
  }

  async update(id: string, updateanyDto: UpdatePlayerProgressDto): Promise<any> {
    // Vérifier si la progression existe
    await this.findOne(id);

    try {
      const updatedany = await this.prisma.playerProgress.update({
        where: { id },
        data: updateanyDto,
      });

      this.logger.log(`Progression du joueur mise à jour avec succès: ${id}`);
      return updatedany;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour de la progression du joueur: ${error.message}`);
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    // Vérifier si la progression existe
    await this.findOne(id);

    try {
      await this.prisma.playerProgress.delete({
        where: { id },
      });

      this.logger.log(`Progression du joueur supprimée avec succès: ${id}`);
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression de la progression du joueur: ${error.message}`);
      throw error;
    }
  }

  async addXp(id: string, xp: number): Promise<any> {
    // Vérifier si la progression existe
    const playerProgress = await this.findOne(id);

    // Vérifier si le XP est positif
    if (xp <= 0) {
      throw new ConflictException('Le XP à ajouter doit être positif');
    }

    try {
      // Mettre à jour le XP
      const newXp = playerProgress.xp + xp;
      
      // Vérifier si le joueur a atteint le niveau suivant
      const level = await this.prisma.gameLevel.findUnique({
        where: { id: playerProgress.levelId },
      });
      
      // Mettre à jour le statut si le joueur a atteint le XP requis
      let status = playerProgress.status;
      if (newXp >= level.xpRequired && status !== 'COMPLETED') {
        status = 'COMPLETED';
      }
      
      const updatedany = await this.prisma.playerProgress.update({
        where: { id },
        data: {
          xp: newXp,
          status,
        },
      });

      this.logger.log(`XP ajouté à la progression du joueur avec succès: ${id} (+${xp} XP)`);
      return updatedany;
    } catch (error) {
      this.logger.error(`Erreur lors de l'ajout de XP à la progression du joueur: ${error.message}`);
      throw error;
    }
  }

  async getPlayerLevel(playerId: string): Promise<{ 
    currentLevel: any; 
    nextLevel: any | null; 
    progress: number;
    totalXp: number;
  }> {
    // Récupérer toutes les progressions du joueur
    const playerProgressList = await this.prisma.playerProgress.findMany({
      where: { playerId },
      include: {
        level: {
          include: {
            system: true,
          },
        },
      },
    });

    if (playerProgressList.length === 0) {
      throw new NotFoundException(`Aucune progression trouvée pour le joueur ${playerId}`);
    }

    // Calculer le XP total du joueur
    const totalXp = playerProgressList.reduce((sum: number, progress: any) => sum + progress.xp, 0);

    // Trouver le niveau actuel du joueur (le plus élevé complété)
    const completedLevels = playerProgressList
      .filter((progress: any) => progress.status === 'COMPLETED')
      .sort((a: any, b: any) => b.level.level - a.level.level);

    // Si aucun niveau n'est complété, prendre le niveau en cours avec le plus de XP
    const currentLevel = completedLevels.length > 0
      ? completedLevels[0].level
      : playerProgressList.sort((a: any, b: any) => b.xp - a.xp)[0].level;

    // Trouver le prochain niveau
    const nextLevel = await this.prisma.gameLevel.findFirst({
      where: {
        systemId: currentLevel.systemId,
        level: currentLevel.level + 1,
      },
      include: {
        system: true,
      },
    });

    // Calcul de la progression vers le niveau suivant
    let progress = 100;
    if (nextLevel) {
      const currentXp = playerProgressList.find((progress: any) => progress.levelId === currentLevel.id)?.xp || 0;
      const xpForCurrentLevel = currentLevel.xpRequired;
      const xpForNextLevel = nextLevel.xpRequired;
      progress = Math.min(
        100,
        Math.round(((currentXp - xpForCurrentLevel) / (xpForNextLevel - xpForCurrentLevel)) * 100)
      );
      // S'assurer que la progression est au moins 0
      progress = Math.max(0, progress);
    }

    return {
      currentLevel,
      nextLevel,
      progress,
      totalXp,
    };
  }
}
