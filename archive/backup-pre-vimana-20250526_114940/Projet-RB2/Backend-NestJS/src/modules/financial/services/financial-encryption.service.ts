import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { KeyManagementService } from '../../security/services/key-management.service';

/**
 * Interface pour les données financières chiffrées
 */
export interface EncryptedFinancialData {
  encryptedData: string;
  iv: string;
  authTag: string;
  keyId: string;
}

/**
 * Liste des champs financiers sensibles à chiffrer automatiquement
 */
export const SENSITIVE_FINANCIAL_FIELDS = [
  'cardNumber',
  'cvv',
  'cvc',
  'securityCode',
  'expirationDate',
  'accountNumber',
  'routingNumber',
  'iban',
  'bic',
  'swift',
  'taxId',
  'ssn',
  'paymentDetails',
  'transactionDetails',
];

/**
 * Service de chiffrement pour les données financières
 * Ce service utilise AES-256-GCM pour chiffrer les données financières sensibles
 */
@Injectable()
export class FinancialEncryptionService implements OnModuleInit {
  private readonly logger = new Logger(FinancialEncryptionService.name);
  private readonly enabled: boolean;
  private readonly algorithm: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly keyManagementService: KeyManagementService,
  ) {
    this.enabled = this.configService.get<boolean>('FINANCIAL_ENCRYPTION_ENABLED', true);
    this.algorithm = this.configService.get<string>('FINANCIAL_ENCRYPTION_ALGORITHM', 'aes-256-gcm');
  }

  /**
   * Initialisation du service
   */
  async onModuleInit() {
    if (this.enabled) {
      this.logger.log(`Financial encryption service initialized with algorithm: ${this.algorithm}`);

      // Vérifier si une clé financière existe déjà, sinon en créer une
      try {
        await this.keyManagementService.getActiveKey('encryption', 'financial');
        this.logger.debug('Financial encryption key found');
      } catch (error) {
        this.logger.warn('No financial encryption key found, creating a new one');
        await this.keyManagementService.createKey('financial', this.algorithm, 'encryption');
      }
    } else {
      this.logger.warn('Financial encryption service is disabled');
    }
  }

  /**
   * Chiffre des données financières
   * @param data Données à chiffrer
   * @returns Données chiffrées
   */
  async encrypt(data: string | object): Promise<EncryptedFinancialData> {
    if (!this.enabled) {
      throw new Error('Financial encryption is disabled');
    }

    try {
      // Convertir les objets en JSON
      const dataString = typeof data === 'string' ? data : JSON.stringify(data);

      // Récupérer une clé active pour le chiffrement financier
      const { key, metadata } = await this.keyManagementService.getActiveKey('encryption', 'financial');

      // Générer un vecteur d'initialisation aléatoire
      const iv = crypto.randomBytes(12);

      // Créer le chiffreur
      const cipher = crypto.createCipheriv(this.algorithm, key, iv) as crypto.CipherGCM;

      // Chiffrer les données
      let encryptedData = cipher.update(dataString, 'utf8', 'base64');
      encryptedData += cipher.final('base64');

      // Récupérer le tag d'authentification
      const authTag = cipher.getAuthTag();

      return {
        encryptedData,
        iv: iv.toString('base64'),
        authTag: authTag.toString('base64'),
        keyId: metadata.id
      };
    } catch (error) {
      this.logger.error('Failed to encrypt financial data:', error);
      throw new Error(`Encryption failed: ${error.message}`);
    }
  }

  /**
   * Déchiffre des données financières
   * @param encryptedData Données chiffrées
   * @returns Données déchiffrées
   */
  async decrypt(encryptedData: EncryptedFinancialData): Promise<string> {
    if (!this.enabled) {
      throw new Error('Financial encryption is disabled');
    }

    try {
      // Récupérer la clé utilisée pour le chiffrement
      const { key } = await this.keyManagementService.getKey(encryptedData.keyId);

      // Convertir IV et authTag en Buffer
      const iv = Buffer.from(encryptedData.iv, 'base64');
      const authTag = Buffer.from(encryptedData.authTag, 'base64');

      // Créer le déchiffreur
      const decipher = crypto.createDecipheriv(this.algorithm, key, iv) as crypto.DecipherGCM;
      decipher.setAuthTag(authTag);

      // Déchiffrer les données
      let decrypted = decipher.update(encryptedData.encryptedData, 'base64', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      this.logger.error('Failed to decrypt financial data:', error);
      throw new Error(`Decryption failed: ${error.message}`);
    }
  }

  /**
   * Chiffre un numéro de carte de crédit
   * @param cardNumber Numéro de carte de crédit
   * @returns Numéro de carte chiffré
   */
  async encryptCreditCard(cardNumber: string): Promise<EncryptedFinancialData> {
    // Valider le format du numéro de carte
    if (!this.isValidCreditCard(cardNumber)) {
      throw new Error('Invalid credit card number format');
    }

    return this.encrypt(cardNumber);
  }

  /**
   * Déchiffre un numéro de carte de crédit
   * @param encryptedCard Numéro de carte chiffré
   * @returns Numéro de carte déchiffré
   */
  async decryptCreditCard(encryptedCard: EncryptedFinancialData): Promise<string> {
    const decrypted = await this.decrypt(encryptedCard);

    // Valider le format du numéro de carte déchiffré
    if (!this.isValidCreditCard(decrypted)) {
      throw new Error('Decrypted value is not a valid credit card number');
    }

    return decrypted;
  }

  /**
   * Chiffre des informations bancaires
   * @param bankInfo Informations bancaires
   * @returns Informations bancaires chiffrées
   */
  async encryptBankInfo(bankInfo: {
    accountNumber: string;
    routingNumber: string;
    bankName: string;
  }): Promise<EncryptedFinancialData> {
    return this.encrypt(bankInfo);
  }

  /**
   * Déchiffre des informations bancaires
   * @param encryptedBankInfo Informations bancaires chiffrées
   * @returns Informations bancaires déchiffrées
   */
  async decryptBankInfo(encryptedBankInfo: EncryptedFinancialData): Promise<{
    accountNumber: string;
    routingNumber: string;
    bankName: string;
  }> {
    const decrypted = await this.decrypt(encryptedBankInfo);
    return JSON.parse(decrypted);
  }

  /**
   * Vérifie si un numéro de carte de crédit est valide (format)
   * @param cardNumber Numéro de carte de crédit
   * @returns true si le format est valide
   */
  /**
   * Chiffre un objet financier en chiffrant uniquement les champs sensibles
   * @param data Objet à chiffrer
   * @param sensitiveFields Liste des champs sensibles à chiffrer (optionnel)
   * @returns Objet avec les champs sensibles chiffrés
   */
  async encryptObject<T extends Record<string, any>>(
    data: T,
    sensitiveFields?: string[],
  ): Promise<T & { _encrypted: string[] }> {
    if (!this.enabled) {
      return { ...data, _encrypted: [] };
    }

    try {
      const result = { ...data } as any;
      const fieldsToEncrypt = sensitiveFields || this.identifySensitiveFields(data);
      const encryptedFields: string[] = [];

      for (const field of fieldsToEncrypt) {
        if (data[field] !== undefined && data[field] !== null) {
          result[field] = await this.encrypt(data[field]);
          encryptedFields.push(field);
        }
      }

      result._encrypted = encryptedFields;
      return result;
    } catch (error) {
      this.logger.error('Failed to encrypt financial object', error);
      throw new Error(`Object encryption failed: ${error.message}`);
    }
  }

  /**
   * Déchiffre un objet financier en déchiffrant uniquement les champs chiffrés
   * @param data Objet avec des champs chiffrés
   * @returns Objet avec les champs déchiffrés
   */
  async decryptObject<T extends Record<string, any> & { _encrypted?: string[] }>(
    data: T,
  ): Promise<Omit<T, '_encrypted'>> {
    if (!this.enabled || !data._encrypted || data._encrypted.length === 0) {
      const { _encrypted, ...rest } = data;
      return rest as Omit<T, '_encrypted'>;
    }

    try {
      const result = { ...data } as any;
      delete result._encrypted;

      for (const field of data._encrypted) {
        if (data[field] !== undefined && data[field] !== null) {
          result[field] = await this.decrypt(data[field] as unknown as EncryptedFinancialData);

          // Essayer de parser le résultat comme JSON si possible
          try {
            result[field] = JSON.parse(result[field]);
          } catch {
            // Si ce n'est pas du JSON valide, garder la chaîne telle quelle
          }
        }
      }

      return result as Omit<T, '_encrypted'>;
    } catch (error) {
      this.logger.error('Failed to decrypt financial object', error);
      throw new Error(`Object decryption failed: ${error.message}`);
    }
  }

  /**
   * Identifie les champs sensibles dans un objet financier
   * @param data Objet financier
   * @returns Liste des champs sensibles
   */
  identifySensitiveFields(data: Record<string, any>): string[] {
    return Object.keys(data).filter(key =>
      SENSITIVE_FINANCIAL_FIELDS.includes(key) ||
      SENSITIVE_FINANCIAL_FIELDS.some(pattern =>
        key.toLowerCase().includes(pattern.toLowerCase())
      )
    );
  }

  /**
   * Vérifie si un numéro de carte de crédit est valide (format)
   * @param cardNumber Numéro de carte de crédit
   * @returns true si le format est valide
   */
  private isValidCreditCard(cardNumber: string): boolean {
    // Supprimer les espaces et tirets
    const sanitized = cardNumber.replace(/[\s-]/g, '');

    // Vérifier que la carte contient uniquement des chiffres
    if (!/^\d+$/.test(sanitized)) {
      return false;
    }

    // Vérifier la longueur (la plupart des cartes ont entre 13 et 19 chiffres)
    if (sanitized.length < 13 || sanitized.length > 19) {
      return false;
    }

    // Algorithme de Luhn (vérification de la somme de contrôle)
    let sum = 0;
    let double = false;

    // Parcourir les chiffres de droite à gauche
    for (let i = sanitized.length - 1; i >= 0; i--) {
      let digit = parseInt(sanitized.charAt(i), 10);

      // Doubler un chiffre sur deux
      if (double) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      double = !double;
    }

    // La somme doit être divisible par 10
    return sum % 10 === 0;
  }
}
