import { Controller, Post, Body, Get, Query, UseGuards, Req, HttpException, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles, UserRole } from '../../auth/decorators/roles.decorator';
import { MonetizationService } from '../services/monetization.service';
import { Request } from 'express';

// Define a custom request interface with user property
interface RequestWithUser extends Request {
  user: {
    id: string;
    [key: string]: any;
  };
}

// DTO pour la monétisation de contenu
class MonetizeContentDto {
  contentIds: string[];
  contentType: 'video' | 'post' | 'livestream';
  monetizationType: 'premium' | 'subscription' | 'oneTime' | 'donation';
  price?: number;
  currency?: string;
  description?: string;
}

// DTO pour la configuration des dons
class DonationSettingsDto {
  contentIds: string[];
  contentType: 'video' | 'post' | 'livestream';
  settings: {
    minAmount?: number;
    suggestedAmounts?: number[];
    thanksMessage?: string;
    goals?: { amount: number; description: string }[];
  };
}

// DTO pour la conversion en contenu premium
class PremiumContentDto {
  contentIds: string[];
  contentType: 'video' | 'post' | 'livestream';
  price: number;
}

// DTO pour la promotion de contenu
class PromoteContentDto {
  contentIds: string[];
  contentType: 'video' | 'post' | 'livestream';
  budget: number;
  duration: number; // en jours
}

@ApiTags('financial')
@Controller('api/financial')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class FinancialMonetizationController {
  constructor(private readonly monetizationService: MonetizationService) {}

  @Post('monetize')
  @Roles(UserRole.CREATOR)
  @ApiOperation({ summary: 'Monétiser du contenu' })
  @ApiResponse({ status: 201, description: 'Contenu monétisé avec succès' })
  @ApiResponse({ status: 400, description: 'Données de requête invalides' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async monetizeContent(@Body() monetizeContentDto: MonetizeContentDto, @Req() req: RequestWithUser) {
    const userId = req.user.id;

    // Vérifier que l'utilisateur est le propriétaire du contenu
    const isOwner = await this.monetizationService.verifyContentOwnership(
      userId,
      monetizeContentDto.contentIds,
      monetizeContentDto.contentType
    );

    if (!isOwner) {
      throw new HttpException('Vous n\'êtes pas autorisé à monétiser ce contenu', HttpStatus.FORBIDDEN);
    }

    // Monétiser le contenu
    return this.monetizationService.monetizeContent(
      userId,
      monetizeContentDto.contentIds,
      monetizeContentDto.contentType,
      monetizeContentDto.monetizationType,
      monetizeContentDto.price,
      monetizeContentDto.currency,
      monetizeContentDto.description
    );
  }

  @Post('convert-to-premium')
  @Roles(UserRole.CREATOR)
  @ApiOperation({ summary: 'Convertir du contenu en contenu premium' })
  @ApiResponse({ status: 201, description: 'Contenu converti avec succès' })
  @ApiResponse({ status: 400, description: 'Données de requête invalides' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async convertToPremium(@Body() premiumContentDto: PremiumContentDto, @Req() req: RequestWithUser) {
    const userId = req.user.id;

    // Vérifier que l'utilisateur est le propriétaire du contenu
    const isOwner = await this.monetizationService.verifyContentOwnership(
      userId,
      premiumContentDto.contentIds,
      premiumContentDto.contentType
    );

    if (!isOwner) {
      throw new HttpException('Vous n\'êtes pas autorisé à convertir ce contenu', HttpStatus.FORBIDDEN);
    }

    // Convertir en contenu premium
    return this.monetizationService.convertToPremiumContent(
      userId,
      premiumContentDto.contentIds,
      premiumContentDto.contentType,
      premiumContentDto.price
    );
  }

  @Post('setup-donations')
  @Roles(UserRole.CREATOR)
  @ApiOperation({ summary: 'Configurer les dons pour du contenu' })
  @ApiResponse({ status: 201, description: 'Dons configurés avec succès' })
  @ApiResponse({ status: 400, description: 'Données de requête invalides' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async setupDonations(@Body() donationSettingsDto: DonationSettingsDto, @Req() req: RequestWithUser) {
    const userId = req.user.id;

    // Vérifier que l'utilisateur est le propriétaire du contenu
    const isOwner = await this.monetizationService.verifyContentOwnership(
      userId,
      donationSettingsDto.contentIds,
      donationSettingsDto.contentType
    );

    if (!isOwner) {
      throw new HttpException('Vous n\'êtes pas autorisé à configurer les dons pour ce contenu', HttpStatus.FORBIDDEN);
    }

    // Configurer les dons
    return this.monetizationService.setupDonations(
      userId,
      donationSettingsDto.contentIds,
      donationSettingsDto.contentType,
      donationSettingsDto.settings
    );
  }

  @Post('promote')
  @Roles(UserRole.CREATOR)
  @ApiOperation({ summary: 'Promouvoir du contenu' })
  @ApiResponse({ status: 201, description: 'Promotion configurée avec succès' })
  @ApiResponse({ status: 400, description: 'Données de requête invalides' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async promoteContent(@Body() promoteContentDto: PromoteContentDto, @Req() req: RequestWithUser) {
    const userId = req.user.id;

    // Vérifier que l'utilisateur est le propriétaire du contenu
    const isOwner = await this.monetizationService.verifyContentOwnership(
      userId,
      promoteContentDto.contentIds,
      promoteContentDto.contentType
    );

    if (!isOwner) {
      throw new HttpException('Vous n\'êtes pas autorisé à promouvoir ce contenu', HttpStatus.FORBIDDEN);
    }

    // Vérifier le solde du compte de l'utilisateur
    const hasEnoughBalance = await this.monetizationService.verifyUserBalance(
      userId,
      promoteContentDto.budget
    );

    if (!hasEnoughBalance) {
      throw new HttpException('Solde insuffisant pour cette promotion', HttpStatus.BAD_REQUEST);
    }

    // Promouvoir le contenu
    return this.monetizationService.promoteContent(
      userId,
      promoteContentDto.contentIds,
      promoteContentDto.contentType,
      promoteContentDto.budget,
      promoteContentDto.duration
    );
  }

  @Get('revenue-stats')
  @Roles(UserRole.CREATOR)
  @ApiOperation({ summary: 'Obtenir les statistiques de revenus pour du contenu' })
  @ApiResponse({ status: 200, description: 'Statistiques récupérées avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async getRevenueStats(
    @Query('contentIds') contentIds: string,
    @Query('contentType') contentType: 'video' | 'post' | 'livestream',
    @Req() req: RequestWithUser
  ) {
    const userId = req.user.id;
    const contentIdArray = contentIds.split(',');

    // Vérifier que l'utilisateur est le propriétaire du contenu
    const isOwner = await this.monetizationService.verifyContentOwnership(
      userId,
      contentIdArray,
      contentType
    );

    if (!isOwner) {
      throw new HttpException('Vous n\'êtes pas autorisé à accéder aux statistiques de ce contenu', HttpStatus.FORBIDDEN);
    }

    // Obtenir les statistiques de revenus
    return this.monetizationService.getContentRevenueStats(
      userId,
      contentIdArray,
      contentType
    );
  }

  @Get('monetization-status')
  @ApiOperation({ summary: 'Obtenir le statut de monétisation pour du contenu' })
  @ApiResponse({ status: 200, description: 'Statut récupéré avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async getMonetizationStatus(
    @Query('contentIds') contentIds: string,
    @Query('contentType') contentType: 'video' | 'post' | 'livestream'
  ) {
    const contentIdArray = contentIds.split(',');

    // Obtenir le statut de monétisation
    return this.monetizationService.getMonetizationStatus(
      contentIdArray,
      contentType
    );
  }
}