import { Modu<PERSON> } from '@nestjs/common';
import { FinancialMonetizationController } from './controllers/financial-monetization.controller';
import { MonetizationService } from './services/monetization.service';
import { PrismaModule } from '../../prisma/prisma.module';
import { FinancialEncryptionService } from './services/financial-encryption.service';

@Module({
  imports: [PrismaModule],
  controllers: [FinancialMonetizationController],
  providers: [MonetizationService, FinancialEncryptionService],
  exports: [MonetizationService, FinancialEncryptionService],
})
export class FinancialModule {} 