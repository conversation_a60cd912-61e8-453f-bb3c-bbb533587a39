import { Test, TestingModule } from '@nestjs/testing';
import { NotificationsService } from './notifications.service';
import { PrismaService } from '../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';

describe('NotificationsService', () => {
  let service: NotificationsService;
  let prismaService: jest.Mocked<PrismaService>;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    const mockPrismaService = {
      notification: {
        create: jest.fn(),
        findMany: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      },
      userNotificationPreference: {
        findUnique: jest.fn(),
        upsert: jest.fn(),
      },
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<NotificationsService>(NotificationsService);
    prismaService = module.get(PrismaService);
    configService = module.get(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendNotification', () => {
    it('should create and send a notification', async () => {
      const mockNotification = {
        id: 'notification-1',
        userId: 'user-1',
        title: 'Test Notification',
        message: 'This is a test notification',
        type: 'INFO',
        channel: 'EMAIL',
        status: 'SENT',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaService.notification.create.mockResolvedValue(mockNotification);

      const result = await service.sendNotification('user-1', {
        title: 'Test Notification',
        message: 'This is a test notification',
        type: 'INFO',
        channel: 'EMAIL',
      });

      expect(result).toEqual(mockNotification);
      expect(prismaService.notification.create).toHaveBeenCalledWith({
        data: {
          userId: 'user-1',
          title: 'Test Notification',
          message: 'This is a test notification',
          type: 'INFO',
          channel: 'EMAIL',
          status: 'PENDING',
        },
      });
    });
  });

  describe('getUserNotifications', () => {
    it('should return user notifications', async () => {
      const mockNotifications = [
        {
          id: 'notification-1',
          userId: 'user-1',
          title: 'Test Notification 1',
          message: 'Message 1',
          type: 'INFO',
          channel: 'EMAIL',
          status: 'SENT',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'notification-2',
          userId: 'user-1',
          title: 'Test Notification 2',
          message: 'Message 2',
          type: 'WARNING',
          channel: 'PUSH',
          status: 'SENT',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      prismaService.notification.findMany.mockResolvedValue(mockNotifications);

      const result = await service.getUserNotifications('user-1');

      expect(result).toEqual(mockNotifications);
      expect(prismaService.notification.findMany).toHaveBeenCalledWith({
        where: { userId: 'user-1' },
        orderBy: { createdAt: 'desc' },
      });
    });
  });

  describe('markAsRead', () => {
    it('should mark notification as read', async () => {
      const mockNotification = {
        id: 'notification-1',
        userId: 'user-1',
        title: 'Test Notification',
        message: 'Message',
        type: 'INFO',
        channel: 'EMAIL',
        status: 'READ',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaService.notification.update.mockResolvedValue(mockNotification);

      const result = await service.markAsRead('notification-1');

      expect(result).toEqual(mockNotification);
      expect(prismaService.notification.update).toHaveBeenCalledWith({
        where: { id: 'notification-1' },
        data: { status: 'READ', readAt: expect.any(Date) },
      });
    });
  });

  describe('updatePreferences', () => {
    it('should update user notification preferences', async () => {
      const mockPreferences = {
        userId: 'user-1',
        emailEnabled: true,
        pushEnabled: false,
        smsEnabled: false,
        preferences: {
          marketing: false,
          updates: true,
          security: true,
        },
      };

      prismaService.userNotificationPreference.upsert.mockResolvedValue(mockPreferences);

      const result = await service.updatePreferences('user-1', {
        emailEnabled: true,
        pushEnabled: false,
        smsEnabled: false,
        preferences: {
          marketing: false,
          updates: true,
          security: true,
        },
      });

      expect(result).toEqual(mockPreferences);
      expect(prismaService.userNotificationPreference.upsert).toHaveBeenCalled();
    });
  });
});
