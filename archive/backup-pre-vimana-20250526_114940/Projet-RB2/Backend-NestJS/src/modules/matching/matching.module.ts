import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MatchingController } from './controllers/matching.controller';
import { MatchingAnalyticsController } from './controllers/matching-analytics.controller';
import { MatchingRecommendationController } from './controllers/matching-recommendation.controller';
import { MatchingExportController } from './controllers/matching-export.controller';
import { MatchingMessagingController } from './controllers/matching-messaging.controller';
import { MatchingBookingController } from './controllers/matching-booking.controller';
import { MatchingVideoController } from './controllers/matching-video.controller';
import { MatchingService } from './services/matching.service';
import { MatchingNotificationService } from './services/matching-notification.service';
import { MatchingAnalyticsService } from './services/matching-analytics.service';
import { MatchingRecommendationService } from './services/matching-recommendation.service';
import { MatchingExportService } from './services/matching-export.service';
import { MatchingMessagingService } from './services/matching-messaging.service';
import { MatchingBookingService } from './services/matching-booking.service';
import { MatchingVideoService } from './services/matching-video.service';
import { PrismaModule } from '../../prisma/prisma.module';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { NotificationsModule } from '../notifications/notifications.module';
import { MessagingModule } from '../messaging/messaging.module';
import { BookingModule } from '../booking/booking.module';
import { HttpModule } from '@nestjs/axios';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
  imports: [
    PrismaModule,
    ConfigModule,
    EventEmitterModule.forRoot(),
    NotificationsModule,
    MessagingModule,
    BookingModule,
    HttpModule,
    ScheduleModule.forRoot(),
  ],
  controllers: [
    MatchingController,
    MatchingAnalyticsController,
    MatchingRecommendationController,
    MatchingExportController,
    MatchingMessagingController,
    MatchingBookingController,
    MatchingVideoController
  ],
  providers: [
    MatchingService,
    MatchingNotificationService,
    MatchingAnalyticsService,
    MatchingRecommendationService,
    MatchingExportService,
    MatchingMessagingService,
    MatchingBookingService,
    MatchingVideoService
  ],
  exports: [
    MatchingService,
    MatchingNotificationService,
    MatchingAnalyticsService,
    MatchingRecommendationService,
    MatchingExportService,
    MatchingMessagingService,
    MatchingBookingService,
    MatchingVideoService
  ],
})
export class MatchingModule {}
