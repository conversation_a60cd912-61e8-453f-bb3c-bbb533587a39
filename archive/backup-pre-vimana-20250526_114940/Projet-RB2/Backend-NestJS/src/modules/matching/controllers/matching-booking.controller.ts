import { <PERSON>, Post, Get, Body, Param, Query, UseGuards, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { MatchingBookingService } from '../services/matching-booking.service';
import { MatchingService } from '../services/matching.service';
import { User } from '../../users/decorators/user.decorator';
import { PrismaService } from '../../../prisma/prisma.service';

class CreateBookingDto {
  partnerId: string;
  retreatId: string;
  startDate?: Date;
  endDate?: Date;
  notes?: string;
  services?: string[];
  participants?: number;
  specialRequirements?: string;
}

@ApiTags('matching-booking')
@Controller('matching/booking')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class MatchingBookingController {
  constructor(
    private readonly bookingService: MatchingBookingService,
    private readonly matchingService: MatchingService,
    private readonly prisma: PrismaService,
  ) {}

  @Post('create')
  @Roles('USER')
  @ApiOperation({ summary: 'Create a booking from a matching' })
  @ApiResponse({ status: 201, description: 'Booking created successfully' })
  async createBookingFromMatching(
    @Body() dto: CreateBookingDto,
    @User() user: any,
  ) {
    // Vérifier que l'utilisateur est bien l'organisateur de la retraite
    const retreat = await this.prisma.retreat.findUnique({
      where: { id: dto.retreatId },
      select: { organizerId: true },
    });

    if (!retreat || retreat.organizerId !== user.id) {
      throw new BadRequestException('You are not authorized to create a booking for this retreat');
    }

    // Vérifier si une réservation existe déjà
    const existingBooking = await this.bookingService.checkExistingBooking(dto.partnerId, dto.retreatId);
    if (existingBooking) {
      throw new BadRequestException('A booking already exists for this partner and retreat');
    }

    // Récupérer les informations de matching
    const matchingResult = await this.matchingService.getMatchingScore(dto.partnerId, dto.retreatId);
    
    if (!matchingResult || matchingResult.score === 0) {
      throw new BadRequestException('No matching found between this partner and retreat');
    }

    // Créer un objet de résultat de matching complet
    const fullMatchingResult = {
      partnerId: dto.partnerId,
      retreatId: dto.retreatId,
      score: matchingResult.score,
      compatibilityFactors: matchingResult.compatibilityFactors,
      partner: await this.prisma.partner.findUnique({
        where: { id: dto.partnerId },
        select: {
          id: true,
          userId: true,
          companyName: true,
          description: true,
          category: true,
          type: true,
        },
      }),
      retreat: await this.prisma.retreat.findUnique({
        where: { id: dto.retreatId },
        select: {
          id: true,
          organizerId: true,
          title: true,
          description: true,
          startDate: true,
          endDate: true,
          location: true,
        },
      }),
    };

    // Créer la réservation
    const booking = await this.bookingService.createBookingFromMatching(
      fullMatchingResult,
      user.id,
      {
        startDate: dto.startDate,
        endDate: dto.endDate,
        notes: dto.notes,
        services: dto.services,
        participants: dto.participants,
        specialRequirements: dto.specialRequirements,
      },
    );

    return {
      success: true,
      booking,
      message: 'Booking created successfully',
    };
  }

  @Get('check/:partnerId/:retreatId')
  @Roles('USER', 'PARTNER')
  @ApiOperation({ summary: 'Check if a booking already exists for a matching' })
  @ApiParam({ name: 'partnerId', description: 'Partner ID' })
  @ApiParam({ name: 'retreatId', description: 'Retreat ID' })
  @ApiResponse({ status: 200, description: 'Returns booking information if it exists' })
  async checkExistingBooking(
    @Param('partnerId') partnerId: string,
    @Param('retreatId') retreatId: string,
    @User() user: any,
  ) {
    // Vérifier que l'utilisateur est autorisé à consulter cette information
    const retreat = await this.prisma.retreat.findUnique({
      where: { id: retreatId },
      select: { organizerId: true },
    });

    const partner = await this.prisma.partner.findUnique({
      where: { id: partnerId },
      select: { userId: true },
    });

    if (
      !retreat || 
      !partner || 
      (retreat.organizerId !== user.id && partner.userId !== user.id && user.role !== 'ADMIN')
    ) {
      throw new BadRequestException('You are not authorized to access this information');
    }

    const booking = await this.bookingService.checkExistingBooking(partnerId, retreatId);

    return {
      exists: !!booking,
      booking,
    };
  }

  @Get('conversion-stats')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Get matching to booking conversion statistics' })
  @ApiQuery({ name: 'period', description: 'Period (day, week, month, year)', required: false })
  @ApiResponse({ status: 200, description: 'Returns conversion statistics' })
  async getConversionStats(
    @Query('period') period: string = 'month',
  ) {
    return this.bookingService.getConversionStats(period);
  }

  @Get('conversion-stats/category')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Get matching to booking conversion statistics by partner category' })
  @ApiQuery({ name: 'period', description: 'Period (day, week, month, year)', required: false })
  @ApiResponse({ status: 200, description: 'Returns conversion statistics by category' })
  async getConversionStatsByCategory(
    @Query('period') period: string = 'month',
  ) {
    return this.bookingService.getConversionStatsByCategory(period);
  }

  @Get('booking-from-matching/:bookingId')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Get information about a booking created from a matching' })
  @ApiParam({ name: 'bookingId', description: 'Booking ID' })
  @ApiResponse({ status: 200, description: 'Returns booking information with matching details' })
  async getBookingFromMatching(
    @Param('bookingId') bookingId: string,
    @User() user: any,
  ) {
    // Récupérer la réservation avec les informations de matching
    const booking = await this.prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        retreat: {
          select: {
            id: true,
            title: true,
            organizerId: true,
          },
        },
        partner: {
          select: {
            id: true,
            companyName: true,
            userId: true,
          },
        },
      },
    });

    if (!booking) {
      throw new BadRequestException('Booking not found');
    }

    // Vérifier que l'utilisateur est autorisé à consulter cette information
    if (
      booking.retreat.organizerId !== user.id && 
      booking.partner.userId !== user.id && 
      user.role !== 'ADMIN'
    ) {
      throw new BadRequestException('You are not authorized to access this information');
    }

    // Récupérer les informations de matching
    const matchingResult = await this.matchingService.getMatchingScore(booking.partnerId, booking.retreatId);

    return {
      booking,
      matching: matchingResult,
      createdFromMatching: booking.metadata?.createdFromMatching || false,
    };
  }
}
