import { <PERSON>, Get, Post, Param, Query, UseGuards, Res, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { MatchingExportService, ExportFormat } from '../services/matching-export.service';
import { MatchingCriteriaDto } from '../dto';
import { User } from '../../users/decorators/user.decorator';
import { Response } from 'express';
import * as fs from 'fs';
import * as path from 'path';

@ApiTags('matching-export')
@Controller('matching/export')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class MatchingExportController {
  constructor(private readonly exportService: MatchingExportService) {}

  @Get('partner/:partnerId')
  @Roles('ADMIN', 'PARTNER')
  @ApiOperation({ summary: 'Export matching results for a partner' })
  @ApiParam({ name: 'partnerId', description: 'Partner ID' })
  @ApiQuery({ name: 'format', description: 'Export format (csv, excel, json)', required: false })
  @ApiResponse({ status: 200, description: 'Returns the exported file' })
  async exportPartnerMatchings(
    @Param('partnerId') partnerId: string,
    @Query('format') format: ExportFormat = 'csv',
    @User() user: any,
    @Res() res: Response,
  ) {
    // Vérifier que l'utilisateur est le propriétaire du partenaire ou un administrateur
    if (user.role !== 'ADMIN') {
      const partner = await this.validatePartnerOwnership(partnerId, user.id);
      if (!partner) {
        return res.status(403).json({ error: 'You are not authorized to access this resource' });
      }
    }

    try {
      const filePath = await this.exportService.exportPartnerMatchings(partnerId, format);
      return this.sendFile(res, filePath, format);
    } catch (error) {
      return res.status(500).json({ error: error.message });
    }
  }

  @Post('partner/:partnerId')
  @Roles('ADMIN', 'PARTNER')
  @ApiOperation({ summary: 'Export matching results for a partner with custom criteria' })
  @ApiParam({ name: 'partnerId', description: 'Partner ID' })
  @ApiQuery({ name: 'format', description: 'Export format (csv, excel, json)', required: false })
  @ApiResponse({ status: 200, description: 'Returns the exported file' })
  async exportPartnerMatchingsWithCriteria(
    @Param('partnerId') partnerId: string,
    @Query('format') format: ExportFormat = 'csv',
    @Body() criteria: Partial<MatchingCriteriaDto>,
    @User() user: any,
    @Res() res: Response,
  ) {
    // Vérifier que l'utilisateur est le propriétaire du partenaire ou un administrateur
    if (user.role !== 'ADMIN') {
      const partner = await this.validatePartnerOwnership(partnerId, user.id);
      if (!partner) {
        return res.status(403).json({ error: 'You are not authorized to access this resource' });
      }
    }

    try {
      const filePath = await this.exportService.exportPartnerMatchings(partnerId, format, criteria);
      return this.sendFile(res, filePath, format);
    } catch (error) {
      return res.status(500).json({ error: error.message });
    }
  }

  @Get('retreat/:retreatId')
  @Roles('ADMIN', 'USER')
  @ApiOperation({ summary: 'Export matching results for a retreat' })
  @ApiParam({ name: 'retreatId', description: 'Retreat ID' })
  @ApiQuery({ name: 'format', description: 'Export format (csv, excel, json)', required: false })
  @ApiResponse({ status: 200, description: 'Returns the exported file' })
  async exportRetreatMatchings(
    @Param('retreatId') retreatId: string,
    @Query('format') format: ExportFormat = 'csv',
    @User() user: any,
    @Res() res: Response,
  ) {
    // Vérifier que l'utilisateur est l'organisateur de la retraite ou un administrateur
    if (user.role !== 'ADMIN') {
      const retreat = await this.validateRetreatOwnership(retreatId, user.id);
      if (!retreat) {
        return res.status(403).json({ error: 'You are not authorized to access this resource' });
      }
    }

    try {
      const filePath = await this.exportService.exportRetreatMatchings(retreatId, format);
      return this.sendFile(res, filePath, format);
    } catch (error) {
      return res.status(500).json({ error: error.message });
    }
  }

  @Post('retreat/:retreatId')
  @Roles('ADMIN', 'USER')
  @ApiOperation({ summary: 'Export matching results for a retreat with custom criteria' })
  @ApiParam({ name: 'retreatId', description: 'Retreat ID' })
  @ApiQuery({ name: 'format', description: 'Export format (csv, excel, json)', required: false })
  @ApiResponse({ status: 200, description: 'Returns the exported file' })
  async exportRetreatMatchingsWithCriteria(
    @Param('retreatId') retreatId: string,
    @Query('format') format: ExportFormat = 'csv',
    @Body() criteria: Partial<MatchingCriteriaDto>,
    @User() user: any,
    @Res() res: Response,
  ) {
    // Vérifier que l'utilisateur est l'organisateur de la retraite ou un administrateur
    if (user.role !== 'ADMIN') {
      const retreat = await this.validateRetreatOwnership(retreatId, user.id);
      if (!retreat) {
        return res.status(403).json({ error: 'You are not authorized to access this resource' });
      }
    }

    try {
      const filePath = await this.exportService.exportRetreatMatchings(retreatId, format, criteria);
      return this.sendFile(res, filePath, format);
    } catch (error) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Envoie le fichier exporté au client
   * @param res Réponse Express
   * @param filePath Chemin du fichier
   * @param format Format d'exportation
   */
  private sendFile(res: Response, filePath: string, format: ExportFormat): void {
    const filename = path.basename(filePath);
    
    let contentType: string;
    switch (format) {
      case 'csv':
        contentType = 'text/csv';
        break;
      case 'excel':
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        break;
      case 'json':
        contentType = 'application/json';
        break;
      default:
        contentType = 'application/octet-stream';
    }
    
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  }

  /**
   * Valide que l'utilisateur est le propriétaire du partenaire
   * @param partnerId ID du partenaire
   * @param userId ID de l'utilisateur
   * @returns Partenaire si l'utilisateur est le propriétaire, null sinon
   */
  private async validatePartnerOwnership(partnerId: string, userId: string) {
    const partner = await this.exportService['prisma'].partner.findUnique({
      where: { id: partnerId },
      select: {
        id: true,
        userId: true,
      },
    });
    
    if (!partner || partner.userId !== userId) {
      return null;
    }
    
    return partner;
  }

  /**
   * Valide que l'utilisateur est l'organisateur de la retraite
   * @param retreatId ID de la retraite
   * @param userId ID de l'utilisateur
   * @returns Retraite si l'utilisateur est l'organisateur, null sinon
   */
  private async validateRetreatOwnership(retreatId: string, userId: string) {
    const retreat = await this.exportService['prisma'].retreat.findUnique({
      where: { id: retreatId },
      select: {
        id: true,
        organizerId: true,
      },
    });
    
    if (!retreat || retreat.organizerId !== userId) {
      return null;
    }
    
    return retreat;
  }
}
