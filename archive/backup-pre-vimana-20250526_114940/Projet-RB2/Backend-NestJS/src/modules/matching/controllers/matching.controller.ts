import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { MatchingService } from '../services/matching.service';
import { MatchingCriteriaDto, MatchingResponseDto } from '../dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { PartnerCategory, PartnerType } from '../../../prisma/prisma-types';

@ApiTags('matching')
@Controller('matching')
export class MatchingController {
  constructor(private readonly matchingService: MatchingService) {}

  @Post('partners')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Trouver des partenaires correspondant aux critères' })
  @ApiResponse({
    status: 200,
    description: 'Retourne les partenaires correspondant aux critères',
    type: MatchingResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Requête invalide' })
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  async findPartners(@Body() criteria: MatchingCriteriaDto): Promise<MatchingResponseDto> {
    return this.matchingService.findPartnersForRetreat(criteria);
  }

  @Post('retreats')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Trouver des retraites correspondant aux critères' })
  @ApiResponse({
    status: 200,
    description: 'Retourne les retraites correspondant aux critères',
    type: MatchingResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Requête invalide' })
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  async findRetreats(@Body() criteria: MatchingCriteriaDto): Promise<MatchingResponseDto> {
    return this.matchingService.findRetreatsForPartner(criteria);
  }

  @Get('partners/retreat/:retreatId')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Trouver des partenaires pour une retraite spécifique' })
  @ApiParam({ name: 'retreatId', description: 'ID de la retraite' })
  @ApiQuery({
    name: 'categories',
    required: false,
    isArray: true,
    enum: PartnerCategory,
    description: 'Catégories de partenaires',
  })
  @ApiQuery({
    name: 'types',
    required: false,
    isArray: true,
    enum: PartnerType,
    description: 'Types de partenaires',
  })
  @ApiQuery({
    name: 'specializations',
    required: false,
    isArray: true,
    description: 'Spécialisations recherchées',
  })
  @ApiQuery({
    name: 'languages',
    required: false,
    isArray: true,
    description: 'Langues parlées requises',
  })
  @ApiQuery({
    name: 'minExperience',
    required: false,
    type: Number,
    description: 'Expérience minimale requise',
  })
  @ApiQuery({
    name: 'minRating',
    required: false,
    type: Number,
    description: 'Note minimale requise',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Nombre maximum de résultats',
  })
  @ApiResponse({
    status: 200,
    description: 'Retourne les partenaires correspondant à la retraite',
    type: MatchingResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Retraite non trouvée' })
  @ApiBearerAuth()
  async findPartnersForRetreat(
    @Param('retreatId') retreatId: string,
    @Query('categories') categories?: PartnerCategory[],
    @Query('types') types?: PartnerType[],
    @Query('specializations') specializations?: string[],
    @Query('languages') languages?: string[],
    @Query('minExperience') minExperience?: number,
    @Query('minRating') minRating?: number,
    @Query('limit') limit?: number,
  ): Promise<MatchingResponseDto> {
    const criteria: MatchingCriteriaDto = {
      retreatId,
      categories,
      types,
      specializations,
      languages,
      minExperience,
      minRating,
      limit,
    };
    return this.matchingService.findPartnersForRetreat(criteria);
  }

  @Get('retreats/partner/:partnerId')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Trouver des retraites pour un partenaire spécifique' })
  @ApiParam({ name: 'partnerId', description: 'ID du partenaire' })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: 'Date de début (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    type: String,
    description: 'Date de fin (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'maxBudget',
    required: false,
    type: Number,
    description: 'Budget maximum',
  })
  @ApiQuery({
    name: 'country',
    required: false,
    type: String,
    description: 'Pays',
  })
  @ApiQuery({
    name: 'minCapacity',
    required: false,
    type: Number,
    description: 'Capacité minimale',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Nombre maximum de résultats',
  })
  @ApiResponse({
    status: 200,
    description: 'Retourne les retraites correspondant au partenaire',
    type: MatchingResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Partenaire non trouvé' })
  @ApiBearerAuth()
  async findRetreatsForPartner(
    @Param('partnerId') partnerId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('maxBudget') maxBudget?: number,
    @Query('country') country?: string,
    @Query('minCapacity') minCapacity?: number,
    @Query('limit') limit?: number,
  ): Promise<MatchingResponseDto> {
    const criteria: MatchingCriteriaDto = {
      partnerId,
      dateRange: startDate && endDate ? { start: startDate, end: endDate } : undefined,
      maxBudget,
      location: country ? { country } : undefined,
      minCapacity,
      limit,
    };
    return this.matchingService.findRetreatsForPartner(criteria);
  }

  @Get('score/:partnerId/:retreatId')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Obtenir le score de compatibilité entre un partenaire et une retraite' })
  @ApiParam({ name: 'partnerId', description: 'ID du partenaire' })
  @ApiParam({ name: 'retreatId', description: 'ID de la retraite' })
  @ApiResponse({
    status: 200,
    description: 'Retourne le score de compatibilité',
    schema: {
      type: 'object',
      properties: {
        score: {
          type: 'number',
          example: 87,
        },
        compatibilityFactors: {
          type: 'object',
          properties: {
            skillMatch: { type: 'number', example: 85 },
            availabilityMatch: { type: 'number', example: 90 },
            locationMatch: { type: 'number', example: 75 },
            ratingMatch: { type: 'number', example: 95 },
            budgetMatch: { type: 'number', example: 80 },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Partenaire ou retraite non trouvé' })
  @ApiBearerAuth()
  async getMatchingScore(
    @Param('partnerId') partnerId: string,
    @Param('retreatId') retreatId: string,
  ): Promise<{ score: number; compatibilityFactors: any }> {
    const criteria: MatchingCriteriaDto = { partnerId, retreatId };
    const result = await this.matchingService.findPartnersForRetreat(criteria);

    if (result.results.length === 0) {
      return {
        score: 0,
        compatibilityFactors: {
          skillMatch: 0,
          availabilityMatch: 0,
          locationMatch: 0,
          ratingMatch: 0,
          budgetMatch: 0,
        },
      };
    }

    return {
      score: result.results[0].score,
      compatibilityFactors: result.results[0].compatibilityFactors,
    };
  }
}
