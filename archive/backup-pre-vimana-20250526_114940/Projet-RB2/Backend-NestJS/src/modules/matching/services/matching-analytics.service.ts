import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { MatchingResult } from '../dto';

interface MatchingAnalyticsData {
  id?: string;
  eventType: string;
  partnerId?: string;
  retreatId?: string;
  userId?: string;
  score?: number;
  criteriaUsed?: any;
  executionTimeMs?: number;
  resultCount?: number;
  timestamp: Date;
  metadata?: any;
}

@Injectable()
export class MatchingAnalyticsService {
  private readonly logger = new Logger(MatchingAnalyticsService.name);
  private readonly enableAnalytics: boolean;
  private readonly enableDetailedLogging: boolean;
  private readonly enablePerformanceTracking: boolean;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {
    this.enableAnalytics = this.configService.get<boolean>('matching.enableAnalytics', true);
    this.enableDetailedLogging = this.configService.get<boolean>('matching.enableDetailedLogging', false);
    this.enablePerformanceTracking = this.configService.get<boolean>('matching.enablePerformanceTracking', true);
  }

  /**
   * Enregistre un événement d'analyse
   * @param data Données d'analyse
   */
  async recordAnalyticsEvent(data: MatchingAnalyticsData): Promise<void> {
    if (!this.enableAnalytics) {
      return;
    }

    try {
      // Enregistrer l'événement dans la base de données
      await this.prisma.matchingAnalytics.create({
        data: {
          eventType: data.eventType,
          partnerId: data.partnerId,
          retreatId: data.retreatId,
          userId: data.userId,
          score: data.score,
          criteriaUsed: data.criteriaUsed ? JSON.stringify(data.criteriaUsed) : null,
          executionTimeMs: data.executionTimeMs,
          resultCount: data.resultCount,
          timestamp: data.timestamp,
          metadata: data.metadata ? JSON.stringify(data.metadata) : null,
        },
      });

      if (this.enableDetailedLogging) {
        this.logger.log(`Analytics event recorded: ${data.eventType}`);
      }
    } catch (error) {
      this.logger.error(`Error recording analytics event: ${error.message}`, error.stack);
    }
  }

  /**
   * Gère l'événement de matching effectué
   * @param payload Données de l'événement
   */
  @OnEvent('matching.performed')
  async handleMatchingPerformed(payload: any): Promise<void> {
    if (!this.enableAnalytics) {
      return;
    }

    try {
      await this.recordAnalyticsEvent({
        eventType: 'MATCHING_PERFORMED',
        criteriaUsed: payload.criteria,
        resultCount: payload.resultsCount,
        score: payload.topScore,
        executionTimeMs: payload.executionTimeMs,
        timestamp: payload.timestamp || new Date(),
        metadata: {
          source: payload.source || 'api',
          userAgent: payload.userAgent,
          ip: payload.ip,
        },
      });
    } catch (error) {
      this.logger.error(`Error handling matching.performed event: ${error.message}`, error.stack);
    }
  }

  /**
   * Gère l'événement de matching consulté
   * @param payload Données de l'événement
   */
  @OnEvent('matching.viewed')
  async handleMatchingViewed(payload: any): Promise<void> {
    if (!this.enableAnalytics) {
      return;
    }

    try {
      await this.recordAnalyticsEvent({
        eventType: 'MATCHING_VIEWED',
        partnerId: payload.partnerId,
        retreatId: payload.retreatId,
        userId: payload.userId,
        score: payload.score,
        timestamp: payload.timestamp || new Date(),
        metadata: {
          source: payload.source || 'web',
          userAgent: payload.userAgent,
          ip: payload.ip,
          viewDurationMs: payload.viewDurationMs,
        },
      });
    } catch (error) {
      this.logger.error(`Error handling matching.viewed event: ${error.message}`, error.stack);
    }
  }

  /**
   * Gère l'événement de contact après matching
   * @param payload Données de l'événement
   */
  @OnEvent('matching.contacted')
  async handleMatchingContacted(payload: any): Promise<void> {
    if (!this.enableAnalytics) {
      return;
    }

    try {
      await this.recordAnalyticsEvent({
        eventType: 'MATCHING_CONTACTED',
        partnerId: payload.partnerId,
        retreatId: payload.retreatId,
        userId: payload.userId,
        score: payload.score,
        timestamp: payload.timestamp || new Date(),
        metadata: {
          source: payload.source || 'web',
          contactMethod: payload.contactMethod || 'message',
          messageLength: payload.messageLength,
        },
      });
    } catch (error) {
      this.logger.error(`Error handling matching.contacted event: ${error.message}`, error.stack);
    }
  }

  /**
   * Gère l'événement de conversion après matching
   * @param payload Données de l'événement
   */
  @OnEvent('matching.converted')
  async handleMatchingConverted(payload: any): Promise<void> {
    if (!this.enableAnalytics) {
      return;
    }

    try {
      await this.recordAnalyticsEvent({
        eventType: 'MATCHING_CONVERTED',
        partnerId: payload.partnerId,
        retreatId: payload.retreatId,
        userId: payload.userId,
        score: payload.score,
        timestamp: payload.timestamp || new Date(),
        metadata: {
          source: payload.source || 'web',
          conversionType: payload.conversionType || 'booking',
          bookingId: payload.bookingId,
          amount: payload.amount,
          currency: payload.currency || 'EUR',
        },
      });
    } catch (error) {
      this.logger.error(`Error handling matching.converted event: ${error.message}`, error.stack);
    }
  }

  /**
   * Enregistre une vue de matching
   * @param result Résultat du matching
   * @param userId ID de l'utilisateur
   * @param metadata Métadonnées supplémentaires
   */
  async recordMatchingView(
    result: MatchingResult,
    userId: string,
    metadata?: any,
  ): Promise<void> {
    if (!this.enableAnalytics) {
      return;
    }

    try {
      const eventData = {
        eventType: 'MATCHING_VIEWED',
        partnerId: result.partnerId,
        retreatId: result.retreatId,
        userId,
        score: result.score,
        timestamp: new Date(),
        metadata: {
          ...metadata,
          source: metadata?.source || 'web',
        },
      };

      // Émettre un événement pour le suivi des vues
      this.handleMatchingViewed(eventData);
    } catch (error) {
      this.logger.error(`Error recording matching view: ${error.message}`, error.stack);
    }
  }

  /**
   * Enregistre un contact après matching
   * @param result Résultat du matching
   * @param userId ID de l'utilisateur
   * @param contactMethod Méthode de contact
   * @param metadata Métadonnées supplémentaires
   */
  async recordMatchingContact(
    result: MatchingResult,
    userId: string,
    contactMethod: string,
    metadata?: any,
  ): Promise<void> {
    if (!this.enableAnalytics) {
      return;
    }

    try {
      const eventData = {
        eventType: 'MATCHING_CONTACTED',
        partnerId: result.partnerId,
        retreatId: result.retreatId,
        userId,
        score: result.score,
        timestamp: new Date(),
        contactMethod,
        metadata: {
          ...metadata,
          source: metadata?.source || 'web',
        },
      };

      // Émettre un événement pour le suivi des contacts
      this.handleMatchingContacted(eventData);
    } catch (error) {
      this.logger.error(`Error recording matching contact: ${error.message}`, error.stack);
    }
  }

  /**
   * Enregistre une conversion après matching
   * @param result Résultat du matching
   * @param userId ID de l'utilisateur
   * @param conversionType Type de conversion
   * @param metadata Métadonnées supplémentaires
   */
  /**
   * Enregistre une interaction liée au matching
   * @param data Données de l'interaction
   */
  async recordMatchingInteraction(data: {
    partnerId: string;
    retreatId: string;
    eventType: string;
    userId?: string;
    metadata?: any;
  }): Promise<void> {
    if (!this.enableAnalytics) {
      return;
    }

    try {
      await this.recordAnalyticsEvent({
        eventType: data.eventType,
        partnerId: data.partnerId,
        retreatId: data.retreatId,
        userId: data.userId,
        timestamp: new Date(),
        metadata: data.metadata || {},
      });

      this.logger.log(`Matching interaction recorded: ${data.eventType}`);
    } catch (error) {
      this.logger.error(`Error recording matching interaction: ${error.message}`, error.stack);
    }
  }

  async recordMatchingConversion(
    result: MatchingResult,
    userId: string,
    conversionType: string,
    metadata?: any,
  ): Promise<void> {
    if (!this.enableAnalytics) {
      return;
    }

    try {
      const eventData = {
        eventType: 'MATCHING_CONVERTED',
        partnerId: result.partnerId,
        retreatId: result.retreatId,
        userId,
        score: result.score,
        timestamp: new Date(),
        conversionType,
        metadata: {
          ...metadata,
          source: metadata?.source || 'web',
        },
      };

      // Émettre un événement pour le suivi des conversions
      this.handleMatchingConverted(eventData);
    } catch (error) {
      this.logger.error(`Error recording matching conversion: ${error.message}`, error.stack);
    }
  }

  /**
   * Obtient les statistiques de matching pour un partenaire
   * @param partnerId ID du partenaire
   * @param period Période (day, week, month, year)
   */
  async getPartnerMatchingStats(partnerId: string, period: string): Promise<any> {
    if (!this.enableAnalytics) {
      return {
        views: 0,
        contacts: 0,
        conversions: 0,
        conversionRate: 0,
        averageScore: 0,
      };
    }

    try {
      // Calculer la date de début en fonction de la période
      const startDate = new Date();
      switch (period) {
        case 'day':
          startDate.setDate(startDate.getDate() - 1);
          break;
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case 'year':
          startDate.setFullYear(startDate.getFullYear() - 1);
          break;
        default:
          startDate.setMonth(startDate.getMonth() - 1); // Par défaut: 1 mois
      }

      // Requête pour obtenir les statistiques
      const views = await this.prisma.matchingAnalytics.count({
        where: {
          partnerId,
          eventType: 'MATCHING_VIEWED',
          timestamp: { gte: startDate },
        },
      });

      const contacts = await this.prisma.matchingAnalytics.count({
        where: {
          partnerId,
          eventType: 'MATCHING_CONTACTED',
          timestamp: { gte: startDate },
        },
      });

      const conversions = await this.prisma.matchingAnalytics.count({
        where: {
          partnerId,
          eventType: 'MATCHING_CONVERTED',
          timestamp: { gte: startDate },
        },
      });

      const scoreData = await this.prisma.matchingAnalytics.findMany({
        where: {
          partnerId,
          eventType: 'MATCHING_VIEWED',
          timestamp: { gte: startDate },
          score: { not: null },
        },
        select: {
          score: true,
        },
      });

      const totalScore = scoreData.reduce((sum, item) => sum + (item.score || 0), 0);
      const averageScore = scoreData.length > 0 ? totalScore / scoreData.length : 0;
      const conversionRate = views > 0 ? (conversions / views) * 100 : 0;

      return {
        views,
        contacts,
        conversions,
        conversionRate,
        averageScore,
      };
    } catch (error) {
      this.logger.error(`Error getting partner matching stats: ${error.message}`, error.stack);
      throw error;
    }
  }
}
