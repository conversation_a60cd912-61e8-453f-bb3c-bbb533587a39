import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { MatchingService } from './matching.service';
import { MatchingCriteriaDto, MatchingResult } from '../dto';
import * as fs from 'fs';
import * as path from 'path';
import * as csv from 'fast-csv';
import * as ExcelJS from 'exceljs';
import { v4 as uuidv4 } from 'uuid';

export type ExportFormat = 'csv' | 'excel' | 'json';

@Injectable()
export class MatchingExportService {
  private readonly logger = new Logger(MatchingExportService.name);
  private readonly exportDir: string;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly matchingService: MatchingService,
  ) {
    // Créer le répertoire d'exportation s'il n'existe pas
    this.exportDir = this.configService.get<string>('matching.exportDir', 'exports/matching');
    if (!fs.existsSync(this.exportDir)) {
      fs.mkdirSync(this.exportDir, { recursive: true });
    }
  }

  /**
   * Exporte les résultats de matching pour un partenaire
   * @param partnerId ID du partenaire
   * @param format Format d'exportation (csv, excel, json)
   * @param criteria Critères de matching supplémentaires
   * @returns Chemin du fichier exporté
   */
  async exportPartnerMatchings(
    partnerId: string,
    format: ExportFormat = 'csv',
    criteria?: Partial<MatchingCriteriaDto>,
  ): Promise<string> {
    try {
      // Récupérer le partenaire
      const partner = await this.prisma.partner.findUnique({
        where: { id: partnerId },
        select: {
          id: true,
          companyName: true,
          userId: true,
        },
      });

      if (!partner) {
        throw new Error(`Partner with ID ${partnerId} not found`);
      }

      // Construire les critères de matching
      const matchingCriteria: MatchingCriteriaDto = {
        partnerId,
        ...criteria,
        limit: criteria?.limit || 100, // Limiter à 100 résultats par défaut
      };

      // Rechercher des retraites correspondantes
      const matchingResponse = await this.matchingService.findRetreatsForPartner(matchingCriteria);

      // Générer le nom du fichier
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `partner-matchings-${partner.companyName.replace(/[^a-z0-9]/gi, '-').toLowerCase()}-${timestamp}`;

      // Exporter les données dans le format demandé
      const filePath = await this.exportData(
        matchingResponse.results,
        format,
        filename,
        `Matchings for partner: ${partner.companyName}`,
      );

      // Enregistrer l'exportation dans la base de données
      await this.prisma.exportHistory.create({
        data: {
          userId: partner.userId,
          entityType: 'PARTNER',
          entityId: partnerId,
          format,
          filePath,
          recordCount: matchingResponse.results.length,
          createdAt: new Date(),
        },
      });

      return filePath;
    } catch (error) {
      this.logger.error(`Error exporting partner matchings: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Exporte les résultats de matching pour une retraite
   * @param retreatId ID de la retraite
   * @param format Format d'exportation (csv, excel, json)
   * @param criteria Critères de matching supplémentaires
   * @returns Chemin du fichier exporté
   */
  async exportRetreatMatchings(
    retreatId: string,
    format: ExportFormat = 'csv',
    criteria?: Partial<MatchingCriteriaDto>,
  ): Promise<string> {
    try {
      // Récupérer la retraite
      const retreat = await this.prisma.retreat.findUnique({
        where: { id: retreatId },
        select: {
          id: true,
          title: true,
          organizerId: true,
        },
      });

      if (!retreat) {
        throw new Error(`Retreat with ID ${retreatId} not found`);
      }

      // Construire les critères de matching
      const matchingCriteria: MatchingCriteriaDto = {
        retreatId,
        ...criteria,
        limit: criteria?.limit || 100, // Limiter à 100 résultats par défaut
      };

      // Rechercher des partenaires correspondants
      const matchingResponse = await this.matchingService.findPartnersForRetreat(matchingCriteria);

      // Générer le nom du fichier
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `retreat-matchings-${retreat.title.replace(/[^a-z0-9]/gi, '-').toLowerCase()}-${timestamp}`;

      // Exporter les données dans le format demandé
      const filePath = await this.exportData(
        matchingResponse.results,
        format,
        filename,
        `Matchings for retreat: ${retreat.title}`,
      );

      // Enregistrer l'exportation dans la base de données
      await this.prisma.exportHistory.create({
        data: {
          userId: retreat.organizerId,
          entityType: 'RETREAT',
          entityId: retreatId,
          format,
          filePath,
          recordCount: matchingResponse.results.length,
          createdAt: new Date(),
        },
      });

      return filePath;
    } catch (error) {
      this.logger.error(`Error exporting retreat matchings: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Exporte les données dans le format spécifié
   * @param data Données à exporter
   * @param format Format d'exportation
   * @param filename Nom du fichier (sans extension)
   * @param title Titre du document
   * @returns Chemin du fichier exporté
   */
  private async exportData(
    data: MatchingResult[],
    format: ExportFormat,
    filename: string,
    title: string,
  ): Promise<string> {
    // Préparer les données pour l'exportation
    const exportData = data.map(result => {
      // Utiliser des assertions de type pour éviter les erreurs TypeScript
      const retreat = result.retreat as any || {};
      const partner = result.partner as any || {};

      return {
        score: result.score,
        partnerId: result.partnerId,
        retreatId: result.retreatId,
        partnerName: partner.companyName || '',
        partnerCategory: partner.category || '',
        partnerType: partner.type || '',
        retreatTitle: retreat.title || '',
        retreatLocation: retreat.location || '',
        retreatStartDate: retreat.startDate ? new Date(retreat.startDate).toISOString().split('T')[0] : '',
        retreatEndDate: retreat.endDate ? new Date(retreat.endDate).toISOString().split('T')[0] : '',
        retreatPrice: retreat.price || 0,
        skillMatch: result.compatibilityFactors?.skillMatch || 0,
        locationMatch: result.compatibilityFactors?.locationMatch || 0,
        ratingMatch: result.compatibilityFactors?.ratingMatch || 0,
        availabilityMatch: result.compatibilityFactors?.availabilityMatch || 0,
        budgetMatch: result.compatibilityFactors?.budgetMatch || 0,
      };
    });

    // Exporter dans le format demandé
    switch (format) {
      case 'csv':
        return this.exportToCsv(exportData, filename);
      case 'excel':
        return this.exportToExcel(exportData, filename, title);
      case 'json':
        return this.exportToJson(exportData, filename);
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * Exporte les données au format CSV
   * @param data Données à exporter
   * @param filename Nom du fichier (sans extension)
   * @returns Chemin du fichier exporté
   */
  private async exportToCsv(data: any[], filename: string): Promise<string> {
    const filePath = path.join(this.exportDir, `${filename}.csv`);

    return new Promise((resolve, reject) => {
      const csvStream = csv.format({ headers: true });
      const writeStream = fs.createWriteStream(filePath);

      csvStream.pipe(writeStream);

      data.forEach(row => {
        csvStream.write(row);
      });

      csvStream.end();

      writeStream.on('finish', () => {
        resolve(filePath);
      });

      writeStream.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * Exporte les données au format Excel
   * @param data Données à exporter
   * @param filename Nom du fichier (sans extension)
   * @param title Titre du document
   * @returns Chemin du fichier exporté
   */
  private async exportToExcel(data: any[], filename: string, title: string): Promise<string> {
    const filePath = path.join(this.exportDir, `${filename}.xlsx`);

    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'Retreat And Be';
    workbook.lastModifiedBy = 'Retreat And Be';
    workbook.created = new Date();
    workbook.modified = new Date();

    const worksheet = workbook.addWorksheet('Matchings');

    // Ajouter un titre
    worksheet.mergeCells('A1:P1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = title;
    titleCell.font = { size: 14, bold: true };
    titleCell.alignment = { horizontal: 'center' };

    // Ajouter les en-têtes
    if (data.length > 0) {
      const headers = Object.keys(data[0]);
      worksheet.columns = headers.map(header => ({
        header,
        key: header,
        width: 15,
      }));
    }

    // Ajouter les données
    worksheet.addRows(data);

    // Formater les cellules
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) { // Ignorer la ligne d'en-tête
        row.eachCell((cell, colNumber) => {
          if (typeof cell.value === 'number') {
            if (cell.value >= 0 && cell.value <= 100) {
              // Formater les scores en pourcentage
              cell.numFmt = '0.00%';
              cell.value = cell.value / 100;
            }
          }
        });
      }
    });

    // Enregistrer le fichier
    await workbook.xlsx.writeFile(filePath);

    return filePath;
  }

  /**
   * Exporte les données au format JSON
   * @param data Données à exporter
   * @param filename Nom du fichier (sans extension)
   * @returns Chemin du fichier exporté
   */
  private async exportToJson(data: any[], filename: string): Promise<string> {
    const filePath = path.join(this.exportDir, `${filename}.json`);

    return new Promise((resolve, reject) => {
      fs.writeFile(filePath, JSON.stringify(data, null, 2), (err) => {
        if (err) {
          reject(err);
        } else {
          resolve(filePath);
        }
      });
    });
  }
}
