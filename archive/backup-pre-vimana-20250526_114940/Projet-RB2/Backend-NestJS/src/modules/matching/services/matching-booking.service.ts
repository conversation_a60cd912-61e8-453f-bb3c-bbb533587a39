import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { BookingService } from '../../booking/services/booking.service';
import { MatchingAnalyticsService } from './matching-analytics.service';
import { MatchingResult } from '../dto';

@Injectable()
export class MatchingBookingService {
  private readonly logger = new Logger(MatchingBookingService.name);
  private readonly enableDirectBooking: boolean;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly bookingService: BookingService,
    private readonly analyticsService: MatchingAnalyticsService,
  ) {
    this.enableDirectBooking = this.configService.get<boolean>('matching.enableDirectBooking', true);
  }

  /**
   * Crée une réservation à partir d'un matching
   * @param matchingResult Résultat du matching
   * @param userId ID de l'utilisateur qui effectue la réservation
   * @param bookingDetails Détails supplémentaires de la réservation
   * @returns Réservation créée
   */
  async createBookingFromMatching(
    matchingResult: MatchingResult,
    userId: string,
    bookingDetails: {
      startDate?: Date;
      endDate?: Date;
      notes?: string;
      services?: string[];
      participants?: number;
      specialRequirements?: string;
    },
  ): Promise<any> {
    if (!this.enableDirectBooking) {
      this.logger.warn('Direct booking from matching is disabled');
      throw new Error('Direct booking from matching is disabled');
    }

    try {
      // Vérifier que les données nécessaires sont présentes
      if (!matchingResult.partnerId || !matchingResult.retreatId) {
        throw new Error('Missing required data for booking creation');
      }

      // Récupérer les informations du partenaire
      const partner = await this.prisma.partner.findUnique({
        where: { id: matchingResult.partnerId },
        select: {
          id: true,
          userId: true,
          companyName: true,
          category: true,
        },
      });

      if (!partner) {
        throw new Error(`Partner with ID ${matchingResult.partnerId} not found`);
      }

      // Récupérer les informations de la retraite
      const retreat = await this.prisma.retreat.findUnique({
        where: { id: matchingResult.retreatId },
        select: {
          id: true,
          organizerId: true,
          title: true,
          startDate: true,
          endDate: true,
          location: true,
        },
      });

      if (!retreat) {
        throw new Error(`Retreat with ID ${matchingResult.retreatId} not found`);
      }

      // Vérifier que l'utilisateur est l'organisateur de la retraite
      if (retreat.organizerId !== userId) {
        throw new Error('Only the retreat organizer can create a booking');
      }

      // Créer la réservation
      const booking = await this.bookingService.createBooking({
        retreatId: matchingResult.retreatId,
        partnerId: matchingResult.partnerId,
        userId: userId,
        startDate: bookingDetails.startDate || retreat.startDate,
        endDate: bookingDetails.endDate || retreat.endDate,
        status: 'PENDING',
        notes: bookingDetails.notes || `Réservation créée à partir d'un matching avec un score de ${matchingResult.score}%`,
        services: bookingDetails.services || [],
        participants: bookingDetails.participants || 1,
        specialRequirements: bookingDetails.specialRequirements || '',
        metadata: {
          matchingScore: matchingResult.score,
          matchingFactors: matchingResult.compatibilityFactors,
          createdFromMatching: true,
        },
      });

      // Enregistrer l'événement d'analyse
      await this.analyticsService.recordMatchingConversion(
        matchingResult,
        booking.userId,
        'booking',
        {
          bookingId: booking.id,
          amount: booking.totalAmount,
          currency: booking.currency || 'EUR',
        },
      );

      this.logger.log(`Booking created from matching between partner ${matchingResult.partnerId} and retreat ${matchingResult.retreatId}`);

      return booking;
    } catch (error) {
      this.logger.error(`Error creating booking from matching: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Vérifie si un matching a déjà été converti en réservation
   * @param partnerId ID du partenaire
   * @param retreatId ID de la retraite
   * @returns Informations sur la réservation si elle existe
   */
  async checkExistingBooking(partnerId: string, retreatId: string): Promise<any> {
    try {
      const booking = await this.prisma.booking.findFirst({
        where: {
          partnerId,
          retreatId,
        },
        select: {
          id: true,
          status: true,
          createdAt: true,
          startDate: true,
          endDate: true,
          totalAmount: true,
          currency: true,
        },
      });

      return booking;
    } catch (error) {
      this.logger.error(`Error checking existing booking: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Obtient les statistiques de conversion des matchings en réservations
   * @param period Période (day, week, month, year)
   * @returns Statistiques de conversion
   */
  async getConversionStats(period: string = 'month'): Promise<any> {
    try {
      // Calculer la date de début en fonction de la période
      const startDate = new Date();
      switch (period) {
        case 'day':
          startDate.setDate(startDate.getDate() - 1);
          break;
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case 'year':
          startDate.setFullYear(startDate.getFullYear() - 1);
          break;
        default:
          startDate.setMonth(startDate.getMonth() - 1); // Par défaut: 1 mois
      }

      // Récupérer les événements de conversion
      const conversions = await this.prisma.matchingAnalytics.count({
        where: {
          eventType: 'MATCHING_CONVERTED',
          timestamp: { gte: startDate },
        },
      });

      // Récupérer les événements de vue
      const views = await this.prisma.matchingAnalytics.count({
        where: {
          eventType: 'MATCHING_VIEWED',
          timestamp: { gte: startDate },
        },
      });

      // Récupérer les événements de contact
      const contacts = await this.prisma.matchingAnalytics.count({
        where: {
          eventType: 'MATCHING_CONTACTED',
          timestamp: { gte: startDate },
        },
      });

      // Calculer les taux de conversion
      const viewToContactRate = views > 0 ? (contacts / views) * 100 : 0;
      const contactToBookingRate = contacts > 0 ? (conversions / contacts) * 100 : 0;
      const viewToBookingRate = views > 0 ? (conversions / views) * 100 : 0;

      return {
        period,
        views,
        contacts,
        conversions,
        viewToContactRate,
        contactToBookingRate,
        viewToBookingRate,
      };
    } catch (error) {
      this.logger.error(`Error getting conversion stats: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Obtient les statistiques de conversion par catégorie de partenaire
   * @param period Période (day, week, month, year)
   * @returns Statistiques de conversion par catégorie
   */
  async getConversionStatsByCategory(period: string = 'month'): Promise<any> {
    try {
      // Calculer la date de début en fonction de la période
      const startDate = new Date();
      switch (period) {
        case 'day':
          startDate.setDate(startDate.getDate() - 1);
          break;
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case 'year':
          startDate.setFullYear(startDate.getFullYear() - 1);
          break;
        default:
          startDate.setMonth(startDate.getMonth() - 1); // Par défaut: 1 mois
      }

      // Récupérer les statistiques par catégorie
      const stats = await this.prisma.$queryRaw`
        SELECT
          p.category,
          COUNT(DISTINCT ma.id) FILTER (WHERE ma.eventType = 'MATCHING_VIEWED') as views,
          COUNT(DISTINCT ma.id) FILTER (WHERE ma.eventType = 'MATCHING_CONTACTED') as contacts,
          COUNT(DISTINCT ma.id) FILTER (WHERE ma.eventType = 'MATCHING_CONVERTED') as conversions
        FROM
          matching_analytics ma
        JOIN
          partner p ON ma.partnerId = p.id
        WHERE
          ma.timestamp >= ${startDate}
        GROUP BY
          p.category
      `;

      // Calculer les taux de conversion pour chaque catégorie
      return stats.map((stat: any) => ({
        category: stat.category,
        views: parseInt(stat.views) || 0,
        contacts: parseInt(stat.contacts) || 0,
        conversions: parseInt(stat.conversions) || 0,
        viewToContactRate: stat.views > 0 ? (stat.contacts / stat.views) * 100 : 0,
        contactToBookingRate: stat.contacts > 0 ? (stat.conversions / stat.contacts) * 100 : 0,
        viewToBookingRate: stat.views > 0 ? (stat.conversions / stat.views) * 100 : 0,
      }));
    } catch (error) {
      this.logger.error(`Error getting conversion stats by category: ${error.message}`, error.stack);
      throw error;
    }
  }
}
