import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { PrismaService } from '../../../prisma/prisma.service';
import { NotificationsService } from '../../notifications/notifications.service';
import { MatchingResult } from '../dto';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class MatchingNotificationService {
  private readonly logger = new Logger(MatchingNotificationService.name);
  private readonly notificationThreshold: number;

  constructor(
    private readonly prisma: PrismaService,
    private readonly notificationsService: NotificationsService,
    private readonly configService: ConfigService,
  ) {
    // Seuil de score pour envoyer une notification (configurable)
    this.notificationThreshold = this.configService.get<number>('matching.notificationThreshold', 80);
  }

  /**
   * Gère l'événement de matching effectué
   * @param payload Données de l'événement
   */
  @OnEvent('matching.performed')
  async handleMatchingPerformed(payload: any) {
    this.logger.log(`Matching performed: ${JSON.stringify(payload)}`);

    // Si le score est suffisamment élevé, on peut enregistrer les résultats pour référence future
    if (payload.topScore >= this.notificationThreshold) {
      this.logger.log(`High score matching detected: ${payload.topScore}`);

      // On pourrait enregistrer ce matching dans une table dédiée pour référence future
      // ou pour des analyses de tendances
    }
  }

  /**
   * Gère l'événement de nouveau matching de haute qualité
   * @param payload Données du matching
   */
  @OnEvent('matching.highQualityMatch')
  async handleHighQualityMatch(payload: {
    result: MatchingResult;
    userId: string;
    title?: string;
    message?: string;
    data?: any;
  }) {
    try {
      const { result, userId, title, message, data } = payload;

      // Créer une notification pour l'utilisateur
      await this.notificationsService.createNotification({
        userId,
        type: 'MATCHING',
        title: title || 'Nouveau matching de haute compatibilité',
        message: message || `Un nouveau matching avec un score de ${result.score}% a été trouvé entre ${result.partner?.companyName || 'un partenaire'} et ${result.retreat?.title || 'une retraite'}.`,
        data: data || {
          matchingResult: result,
          score: result.score,
          partnerId: result.partnerId,
          retreatId: result.retreatId,
        },
        severity: 'INFO',
        actionRequired: false
      });

      this.logger.log(`High quality match notification sent to user ${userId}`);
    } catch (error) {
      this.logger.error(`Error sending high quality match notification: ${error.message}`, error.stack);
    }
  }

  /**
   * Envoie une notification pour un nouveau matching
   * @param result Résultat du matching
   * @param userId ID de l'utilisateur à notifier
   */
  async sendSimpleMatchingNotification(result: MatchingResult, userId: string) {
    try {
      // Vérifier si le score est suffisamment élevé pour envoyer une notification
      if (result.score >= this.notificationThreshold) {
        // Émettre un événement pour un matching de haute qualité
        this.handleHighQualityMatch({ result, userId });
      }
    } catch (error) {
      this.logger.error(`Error in sendSimpleMatchingNotification: ${error.message}`, error.stack);
    }
  }

  /**
   * Envoie des notifications pour plusieurs matchings
   * @param results Résultats des matchings
   * @param userId ID de l'utilisateur à notifier
   */
  async sendBatchMatchingNotifications(results: MatchingResult[], userId: string) {
    try {
      // Filtrer les résultats avec un score suffisamment élevé
      const highQualityMatches = results.filter(result => result.score >= this.notificationThreshold);

      if (highQualityMatches.length === 0) {
        return;
      }

      // Si un seul matching de haute qualité, envoyer une notification spécifique
      if (highQualityMatches.length === 1) {
        await this.sendSimpleMatchingNotification(highQualityMatches[0], userId);
        return;
      }

      // Si plusieurs matchings de haute qualité, envoyer une notification groupée
      await this.notificationsService.createNotification({
        userId,
        type: 'MATCHING',
        title: `${highQualityMatches.length} nouveaux matchings de haute compatibilité`,
        message: `Nous avons trouvé ${highQualityMatches.length} nouveaux matchings avec un score de compatibilité élevé.`,
        data: {
          matchCount: highQualityMatches.length,
          topScore: Math.max(...highQualityMatches.map(match => match.score)),
          matchingResults: highQualityMatches.map(match => ({
            score: match.score,
            partnerId: match.partnerId,
            retreatId: match.retreatId,
          })),
        },
        severity: 'INFO',
        actionRequired: false
      });

      this.logger.log(`Batch match notification sent to user ${userId} for ${highQualityMatches.length} matches`);
    } catch (error) {
      this.logger.error(`Error in sendBatchMatchingNotifications: ${error.message}`, error.stack);
    }
  }

  /**
   * Envoie une notification personnalisée pour un matching
   * @param data Données de la notification
   */
  async sendMatchingNotification(data: {
    result: MatchingResult;
    userId: string;
    title: string;
    message: string;
    data?: any;
  }): Promise<void> {
    try {
      // Émettre un événement pour un matching de haute qualité
      await this.handleHighQualityMatch({
        result: data.result,
        userId: data.userId,
        title: data.title,
        message: data.message,
        data: data.data
      });
    } catch (error) {
      this.logger.error(`Error in sendMatchingNotification: ${error.message}`, error.stack);
    }
  }

  /**
   * Envoie une notification de rappel pour les matchings non traités
   * @param userId ID de l'utilisateur à notifier
   * @param matchCount Nombre de matchings non traités
   */
  async sendReminderNotification(userId: string, matchCount: number) {
    try {
      await this.notificationsService.createNotification({
        userId,
        type: 'REMINDER',
        title: 'Matchings en attente',
        message: `Vous avez ${matchCount} matchings de haute compatibilité en attente de traitement.`,
        data: {
          matchCount,
        },
        severity: 'INFO',
        actionRequired: true
      });

      this.logger.log(`Reminder notification sent to user ${userId} for ${matchCount} pending matches`);
    } catch (error) {
      this.logger.error(`Error in sendReminderNotification: ${error.message}`, error.stack);
    }
  }
}
