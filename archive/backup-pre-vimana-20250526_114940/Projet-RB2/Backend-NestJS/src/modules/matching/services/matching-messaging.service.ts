import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { MessagingService } from '../../messaging/services/messaging.service';
import { MatchingResult } from '../dto';

@Injectable()
export class MatchingMessagingService {
  private readonly logger = new Logger(MatchingMessagingService.name);
  private readonly enableMessaging: boolean;
  private readonly messageTemplates: Record<string, string>;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly messagingService: MessagingService,
  ) {
    this.enableMessaging = this.configService.get<boolean>('matching.enableMessaging', true);
    
    // Templates de messages pour différentes situations
    this.messageTemplates = {
      partnerToOrganizer: `
Bonjour,

J'ai remarqué que mon profil correspond parfaitement à votre retraite "{{retreatTitle}}". Notre score de compatibilité est de {{score}}%.

Je serais ravi(e) de discuter de la possibilité de collaborer sur cet événement. Voici quelques informations sur mes services :

{{partnerDescription}}

N'hésitez pas à me contacter pour plus d'informations ou pour discuter des détails.

Cordialement,
{{partnerName}}
      `,
      
      organizerToPartner: `
Bonjour,

Je suis l'organisateur de la retraite "{{retreatTitle}}" et j'ai remarqué que votre profil correspond parfaitement à nos besoins. Notre score de compatibilité est de {{score}}%.

Nous recherchons actuellement des partenaires pour cet événement qui se déroulera du {{startDate}} au {{endDate}} à {{location}}.

Seriez-vous intéressé(e) par une collaboration ? Je serais ravi(e) d'en discuter plus en détail.

Cordialement,
{{organizerName}}
      `,
      
      followUp: `
Bonjour,

Suite à notre échange concernant la retraite "{{retreatTitle}}", je souhaitais savoir si vous aviez des questions supplémentaires ou si vous souhaitiez avancer dans notre collaboration.

Je reste à votre disposition pour en discuter.

Cordialement,
{{senderName}}
      `,
    };
  }

  /**
   * Envoie un message initial du partenaire à l'organisateur de la retraite
   * @param result Résultat du matching
   * @param customMessage Message personnalisé (optionnel)
   * @returns ID de la conversation créée
   */
  async sendPartnerToOrganizerMessage(
    result: MatchingResult,
    customMessage?: string,
  ): Promise<string> {
    if (!this.enableMessaging) {
      this.logger.warn('Messaging is disabled');
      return null;
    }

    try {
      // Vérifier que les données nécessaires sont présentes
      if (!result.partnerId || !result.retreatId || !result.partner || !result.retreat) {
        throw new Error('Missing required data for sending message');
      }

      // Récupérer les informations du partenaire
      const partner = await this.prisma.partner.findUnique({
        where: { id: result.partnerId },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      if (!partner) {
        throw new Error(`Partner with ID ${result.partnerId} not found`);
      }

      // Récupérer les informations de la retraite
      const retreat = await this.prisma.retreat.findUnique({
        where: { id: result.retreatId },
        include: {
          organizer: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      if (!retreat) {
        throw new Error(`Retreat with ID ${result.retreatId} not found`);
      }

      // Préparer le message
      let messageContent = customMessage;
      if (!messageContent) {
        messageContent = this.messageTemplates.partnerToOrganizer
          .replace('{{retreatTitle}}', retreat.title)
          .replace('{{score}}', result.score.toString())
          .replace('{{partnerDescription}}', partner.description || 'Spécialiste dans mon domaine avec une expérience significative.')
          .replace('{{partnerName}}', `${partner.user.firstName} ${partner.user.lastName}`);
      }

      // Créer une nouvelle conversation
      const conversation = await this.messagingService.createConversation({
        participants: [partner.userId, retreat.organizerId],
        title: `Discussion concernant la retraite: ${retreat.title}`,
        metadata: {
          matchingId: `${result.partnerId}-${result.retreatId}`,
          matchingScore: result.score,
          retreatId: result.retreatId,
          partnerId: result.partnerId,
        },
      });

      // Envoyer le message initial
      await this.messagingService.sendMessage({
        conversationId: conversation.id,
        senderId: partner.userId,
        content: messageContent,
        metadata: {
          matchingId: `${result.partnerId}-${result.retreatId}`,
          matchingScore: result.score,
          messageType: 'initial_contact',
        },
      });

      this.logger.log(`Message sent from partner ${result.partnerId} to organizer of retreat ${result.retreatId}`);
      
      return conversation.id;
    } catch (error) {
      this.logger.error(`Error sending message from partner to organizer: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Envoie un message initial de l'organisateur au partenaire
   * @param result Résultat du matching
   * @param customMessage Message personnalisé (optionnel)
   * @returns ID de la conversation créée
   */
  async sendOrganizerToPartnerMessage(
    result: MatchingResult,
    customMessage?: string,
  ): Promise<string> {
    if (!this.enableMessaging) {
      this.logger.warn('Messaging is disabled');
      return null;
    }

    try {
      // Vérifier que les données nécessaires sont présentes
      if (!result.partnerId || !result.retreatId || !result.partner || !result.retreat) {
        throw new Error('Missing required data for sending message');
      }

      // Récupérer les informations du partenaire
      const partner = await this.prisma.partner.findUnique({
        where: { id: result.partnerId },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      if (!partner) {
        throw new Error(`Partner with ID ${result.partnerId} not found`);
      }

      // Récupérer les informations de la retraite
      const retreat = await this.prisma.retreat.findUnique({
        where: { id: result.retreatId },
        include: {
          organizer: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      if (!retreat) {
        throw new Error(`Retreat with ID ${result.retreatId} not found`);
      }

      // Formater les dates
      const startDate = new Date(retreat.startDate).toLocaleDateString('fr-FR');
      const endDate = new Date(retreat.endDate).toLocaleDateString('fr-FR');

      // Préparer le message
      let messageContent = customMessage;
      if (!messageContent) {
        messageContent = this.messageTemplates.organizerToPartner
          .replace('{{retreatTitle}}', retreat.title)
          .replace('{{score}}', result.score.toString())
          .replace('{{startDate}}', startDate)
          .replace('{{endDate}}', endDate)
          .replace('{{location}}', retreat.location || 'lieu à confirmer')
          .replace('{{organizerName}}', `${retreat.organizer.firstName} ${retreat.organizer.lastName}`);
      }

      // Créer une nouvelle conversation
      const conversation = await this.messagingService.createConversation({
        participants: [retreat.organizerId, partner.userId],
        title: `Discussion concernant la retraite: ${retreat.title}`,
        metadata: {
          matchingId: `${result.partnerId}-${result.retreatId}`,
          matchingScore: result.score,
          retreatId: result.retreatId,
          partnerId: result.partnerId,
        },
      });

      // Envoyer le message initial
      await this.messagingService.sendMessage({
        conversationId: conversation.id,
        senderId: retreat.organizerId,
        content: messageContent,
        metadata: {
          matchingId: `${result.partnerId}-${result.retreatId}`,
          matchingScore: result.score,
          messageType: 'initial_contact',
        },
      });

      this.logger.log(`Message sent from organizer of retreat ${result.retreatId} to partner ${result.partnerId}`);
      
      return conversation.id;
    } catch (error) {
      this.logger.error(`Error sending message from organizer to partner: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Envoie un message de suivi dans une conversation existante
   * @param conversationId ID de la conversation
   * @param senderId ID de l'expéditeur
   * @param customMessage Message personnalisé (optionnel)
   * @returns ID du message envoyé
   */
  async sendFollowUpMessage(
    conversationId: string,
    senderId: string,
    customMessage?: string,
  ): Promise<string> {
    if (!this.enableMessaging) {
      this.logger.warn('Messaging is disabled');
      return null;
    }

    try {
      // Récupérer les informations de la conversation
      const conversation = await this.prisma.conversation.findUnique({
        where: { id: conversationId },
        include: {
          participants: {
            select: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
        },
      });

      if (!conversation) {
        throw new Error(`Conversation with ID ${conversationId} not found`);
      }

      // Vérifier que l'expéditeur est un participant de la conversation
      const isParticipant = conversation.participants.some(p => p.user.id === senderId);
      if (!isParticipant) {
        throw new Error(`User ${senderId} is not a participant of conversation ${conversationId}`);
      }

      // Récupérer les informations de l'expéditeur
      const sender = conversation.participants.find(p => p.user.id === senderId).user;

      // Récupérer les métadonnées de la conversation
      const metadata = conversation.metadata as any;
      const retreatId = metadata?.retreatId;

      // Récupérer les informations de la retraite si disponible
      let retreatTitle = 'cette retraite';
      if (retreatId) {
        const retreat = await this.prisma.retreat.findUnique({
          where: { id: retreatId },
          select: { title: true },
        });
        if (retreat) {
          retreatTitle = retreat.title;
        }
      }

      // Préparer le message
      let messageContent = customMessage;
      if (!messageContent) {
        messageContent = this.messageTemplates.followUp
          .replace('{{retreatTitle}}', retreatTitle)
          .replace('{{senderName}}', `${sender.firstName} ${sender.lastName}`);
      }

      // Envoyer le message
      const message = await this.messagingService.sendMessage({
        conversationId,
        senderId,
        content: messageContent,
        metadata: {
          messageType: 'follow_up',
        },
      });

      this.logger.log(`Follow-up message sent in conversation ${conversationId} by user ${senderId}`);
      
      return message.id;
    } catch (error) {
      this.logger.error(`Error sending follow-up message: ${error.message}`, error.stack);
      throw error;
    }
  }
}
