import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsArray,
  IsOptional,
  IsNumber,
  IsDateString,
  IsEnum,
  Min,
  Max,
  ValidateNested,
  ArrayMinSize,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PartnerCategory, PartnerType } from '../../../prisma/prisma-types';

export class DateRangeDto {
  @ApiProperty({
    description: 'Date de début de la période de disponibilité',
    example: '2023-06-01',
  })
  @IsDateString()
  start: string;

  @ApiProperty({
    description: 'Date de fin de la période de disponibilité',
    example: '2023-06-15',
  })
  @IsDateString()
  end: string;
}

export class LocationPreferenceDto {
  @ApiProperty({
    description: 'Pays',
    example: 'France',
  })
  @IsString()
  country: string;

  @ApiPropertyOptional({
    description: 'Région ou état',
    example: 'Île-de-France',
  })
  @IsString()
  @IsOptional()
  region?: string;

  @ApiPropertyOptional({
    description: 'Ville',
    example: 'Paris',
  })
  @IsString()
  @IsOptional()
  city?: string;

  @ApiPropertyOptional({
    description: 'Rayon de recherche en kilomètres',
    example: 50,
  })
  @IsNumber()
  @Min(1)
  @Max(500)
  @IsOptional()
  radius?: number;
}

export class MatchingCriteriaDto {
  @ApiPropertyOptional({
    description: 'ID de la retraite pour laquelle chercher des partenaires',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsOptional()
  retreatId?: string;

  @ApiPropertyOptional({
    description: 'ID du partenaire pour lequel chercher des retraites',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsOptional()
  partnerId?: string;

  @ApiPropertyOptional({
    description: 'Catégories de partenaires recherchées',
    enum: PartnerCategory,
    isArray: true,
    example: [PartnerCategory.WELLNESS, PartnerCategory.GUIDE],
  })
  @IsEnum(PartnerCategory, { each: true })
  @IsArray()
  @IsOptional()
  categories?: PartnerCategory[];

  @ApiPropertyOptional({
    description: 'Type de partenaire recherché',
    enum: PartnerType,
    isArray: true,
    example: [PartnerType.CERTIFIED, PartnerType.PREMIUM_CERTIFIED],
  })
  @IsEnum(PartnerType, { each: true })
  @IsArray()
  @IsOptional()
  types?: PartnerType[];

  @ApiPropertyOptional({
    description: 'Spécialisations recherchées',
    example: ['Yoga', 'Méditation', 'Nutrition'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  specializations?: string[];

  @ApiPropertyOptional({
    description: 'Langues parlées requises',
    example: ['Français', 'Anglais'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  languages?: string[];

  @ApiPropertyOptional({
    description: 'Budget maximum par jour en euros',
    example: 200,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  maxBudget?: number;

  @ApiPropertyOptional({
    description: 'Période de disponibilité',
    type: DateRangeDto,
  })
  @ValidateNested()
  @Type(() => DateRangeDto)
  @IsOptional()
  dateRange?: DateRangeDto;

  @ApiPropertyOptional({
    description: 'Préférences de localisation',
    type: LocationPreferenceDto,
  })
  @ValidateNested()
  @Type(() => LocationPreferenceDto)
  @IsOptional()
  location?: LocationPreferenceDto;

  @ApiPropertyOptional({
    description: 'Nombre minimum d\'années d\'expérience',
    example: 3,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  minExperience?: number;

  @ApiPropertyOptional({
    description: 'Note minimale (sur 5)',
    example: 4.5,
  })
  @IsNumber()
  @Min(0)
  @Max(5)
  @IsOptional()
  minRating?: number;

  @ApiPropertyOptional({
    description: 'Capacité minimale (nombre de personnes)',
    example: 10,
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  minCapacity?: number;

  @ApiPropertyOptional({
    description: 'Inclure uniquement les partenaires certifiés',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  certifiedOnly?: boolean;

  @ApiPropertyOptional({
    description: 'Nombre maximum de résultats à retourner',
    example: 10,
  })
  @IsNumber()
  @Min(1)
  @Max(100)
  @IsOptional()
  limit?: number;
}
