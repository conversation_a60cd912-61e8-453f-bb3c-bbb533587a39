import { ApiProperty } from '@nestjs/swagger';
import { PartnerCategory, PartnerType } from '../../../prisma/prisma-types';

export class CompatibilityFactorsDto {
  @ApiProperty({
    description: 'Score de compatibilité des compétences (0-100)',
    example: 85,
  })
  skillMatch: number;

  @ApiProperty({
    description: 'Score de compatibilité des disponibilités (0-100)',
    example: 90,
  })
  availabilityMatch: number;

  @ApiProperty({
    description: 'Score de compatibilité de localisation (0-100)',
    example: 75,
  })
  locationMatch: number;

  @ApiProperty({
    description: 'Score de compatibilité basé sur les évaluations (0-100)',
    example: 95,
  })
  ratingMatch: number;

  @ApiProperty({
    description: 'Score de compatibilité basé sur le budget (0-100)',
    example: 80,
  })
  budgetMatch: number;
}

export class PartnerMatchDto {
  @ApiProperty({
    description: 'ID du partenaire',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Nom de l\'entreprise du partenaire',
    example: 'Wellness Retreat Center',
  })
  companyName: string;

  @ApiProperty({
    description: 'Type de partenaire',
    enum: PartnerType,
    example: PartnerType.PREMIUM_CERTIFIED,
  })
  type: PartnerType;

  @ApiProperty({
    description: 'Catégorie du partenaire',
    enum: PartnerCategory,
    example: PartnerCategory.WELLNESS,
  })
  category: PartnerCategory;

  @ApiProperty({
    description: 'Description du partenaire',
    example: 'Centre de bien-être spécialisé dans les retraites de yoga et méditation.',
  })
  description: string;

  @ApiProperty({
    description: 'URL du logo',
    example: 'https://example.com/logo.png',
  })
  logo?: string;

  @ApiProperty({
    description: 'Site web',
    example: 'https://wellness-retreat.com',
  })
  website?: string;

  @ApiProperty({
    description: 'Spécialisations',
    example: ['Yoga', 'Méditation', 'Nutrition'],
  })
  specializations: string[];

  @ApiProperty({
    description: 'Langues parlées',
    example: ['Français', 'Anglais'],
  })
  languages: string[];

  @ApiProperty({
    description: 'Note moyenne (sur 5)',
    example: 4.8,
  })
  averageRating?: number;

  @ApiProperty({
    description: 'Nombre total d\'avis',
    example: 42,
  })
  totalReviews?: number;

  @ApiProperty({
    description: 'Nombre de services complétés',
    example: 120,
  })
  completedServices: number;
}

export class RetreatMatchDto {
  @ApiProperty({
    description: 'ID de la retraite',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Titre de la retraite',
    example: 'Retraite de yoga et méditation en Provence',
  })
  title: string;

  @ApiProperty({
    description: 'Description de la retraite',
    example: 'Une semaine de détente et de reconnexion à soi dans un cadre idyllique.',
  })
  description: string;

  @ApiProperty({
    description: 'Date de début',
    example: '2023-06-01T00:00:00.000Z',
  })
  startDate: Date;

  @ApiProperty({
    description: 'Date de fin',
    example: '2023-06-08T00:00:00.000Z',
  })
  endDate: Date;

  @ApiProperty({
    description: 'Localisation',
    example: 'Aix-en-Provence, France',
  })
  location: string;

  @ApiProperty({
    description: 'Capacité',
    example: 20,
  })
  capacity: number;

  @ApiProperty({
    description: 'Prix',
    example: 1200,
  })
  price: number;

  @ApiProperty({
    description: 'Images',
    example: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
  })
  images: string[];
}

export class MatchingResultDto {
  @ApiProperty({
    description: 'ID du partenaire',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  partnerId: string;

  @ApiProperty({
    description: 'ID de la retraite',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  retreatId?: string;

  @ApiProperty({
    description: 'Score global de compatibilité (0-100)',
    example: 87,
  })
  score: number;

  @ApiProperty({
    description: 'Facteurs de compatibilité détaillés',
    type: CompatibilityFactorsDto,
  })
  compatibilityFactors: CompatibilityFactorsDto;

  @ApiProperty({
    description: 'Détails du partenaire',
    type: PartnerMatchDto,
  })
  partner?: PartnerMatchDto;

  @ApiProperty({
    description: 'Détails de la retraite',
    type: RetreatMatchDto,
  })
  retreat?: RetreatMatchDto;
}

export class MatchingResponseDto {
  @ApiProperty({
    description: 'Résultats du matching',
    type: [MatchingResultDto],
  })
  results: MatchingResultDto[];

  @ApiProperty({
    description: 'Nombre total de résultats',
    example: 42,
  })
  total: number;

  @ApiProperty({
    description: 'Temps d\'exécution de la recherche en millisecondes',
    example: 120,
  })
  executionTimeMs: number;
}
