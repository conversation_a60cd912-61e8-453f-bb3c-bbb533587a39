import { Test, TestingModule } from '@nestjs/testing';
import { MatchingService } from '../services/matching.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { MatchingCriteriaDto } from '../dto';
import { PartnerCategory, PartnerType } from '../../../prisma/prisma-types';

describe('MatchingService', () => {
  let service: MatchingService;
  let prismaService: PrismaService;
  let eventEmitter: EventEmitter2;

  const mockPrismaService = {
    partner: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
    },
    retreat: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
    },
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockEventEmitter = {
    emit: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MatchingService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
      ],
    }).compile();

    service = module.get<MatchingService>(MatchingService);
    prismaService = module.get<PrismaService>(PrismaService);
    eventEmitter = module.get<EventEmitter2>(EventEmitter2);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findPartnersForRetreat', () => {
    it('should return matching partners for a retreat', async () => {
      // Mock data
      const mockRetreat = {
        id: 'retreat-1',
        title: 'Yoga Retreat',
        description: 'A yoga retreat',
        startDate: new Date('2023-06-01'),
        endDate: new Date('2023-06-08'),
        location: 'Paris, France',
        capacity: 20,
        price: 1000,
        images: ['image1.jpg'],
        categories: [{ name: 'Yoga' }, { name: 'Meditation' }],
      };

      const mockPartners = [
        {
          id: 'partner-1',
          companyName: 'Yoga Studio',
          type: PartnerType.CERTIFIED,
          category: PartnerCategory.WELLNESS,
          description: 'A yoga studio',
          specializations: ['Yoga', 'Meditation'],
          languages: ['French', 'English'],
          coverageAreas: {
            countries: ['France'],
            regions: ['Île-de-France'],
          },
          completedServices: 10,
          reviews: [{ rating: 4 }, { rating: 5 }],
          user: {
            id: 'user-1',
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
          },
        },
      ];

      // Mock the prisma service methods
      mockPrismaService.retreat.findUnique.mockResolvedValue(mockRetreat);
      mockPrismaService.partner.findMany.mockResolvedValue(mockPartners);

      // Create test criteria
      const criteria: MatchingCriteriaDto = {
        retreatId: 'retreat-1',
        categories: [PartnerCategory.WELLNESS],
        specializations: ['Yoga'],
      };

      // Call the service method
      const result = await service.findPartnersForRetreat(criteria);

      // Assertions
      expect(mockPrismaService.retreat.findUnique).toHaveBeenCalledWith({
        where: { id: 'retreat-1' },
        include: {
          categories: true,
        },
      });

      expect(mockPrismaService.partner.findMany).toHaveBeenCalled();
      expect(mockEventEmitter.emit).toHaveBeenCalled();

      expect(result.results.length).toBe(1);
      expect(result.total).toBe(1);
      expect(result.results[0].partnerId).toBe('partner-1');
      expect(result.results[0].retreatId).toBe('retreat-1');
      expect(result.results[0].score).toBeGreaterThan(0);
      expect(result.results[0].compatibilityFactors).toBeDefined();
    });

    it('should throw NotFoundException when retreat is not found', async () => {
      // Mock the prisma service to return null for the retreat
      mockPrismaService.retreat.findUnique.mockResolvedValue(null);

      // Create test criteria
      const criteria: MatchingCriteriaDto = {
        retreatId: 'non-existent-retreat',
      };

      // Expect the service to throw NotFoundException
      await expect(service.findPartnersForRetreat(criteria)).rejects.toThrow();
    });
  });

  describe('findRetreatsForPartner', () => {
    it('should return matching retreats for a partner', async () => {
      // Mock data
      const mockPartner = {
        id: 'partner-1',
        companyName: 'Yoga Studio',
        type: PartnerType.CERTIFIED,
        category: PartnerCategory.WELLNESS,
        description: 'A yoga studio',
        specializations: ['Yoga', 'Meditation'],
        languages: ['French', 'English'],
        coverageAreas: {
          countries: ['France'],
          regions: ['Île-de-France'],
        },
        completedServices: 10,
        reviews: [{ rating: 4 }, { rating: 5 }],
      };

      const mockRetreats = [
        {
          id: 'retreat-1',
          title: 'Yoga Retreat',
          description: 'A yoga retreat',
          startDate: new Date('2023-06-01'),
          endDate: new Date('2023-06-08'),
          location: 'Paris, France',
          capacity: 20,
          price: 1000,
          images: ['image1.jpg'],
          categories: [{ name: 'Yoga' }, { name: 'Meditation' }],
          organizer: {
            id: 'organizer-1',
            name: 'Retreat Organizer',
          },
          reviews: [{ rating: 4 }, { rating: 5 }],
        },
      ];

      // Mock the prisma service methods
      mockPrismaService.partner.findUnique.mockResolvedValue(mockPartner);
      mockPrismaService.retreat.findMany.mockResolvedValue(mockRetreats);

      // Create test criteria
      const criteria: MatchingCriteriaDto = {
        partnerId: 'partner-1',
        dateRange: {
          start: '2023-06-01',
          end: '2023-06-30',
        },
      };

      // Call the service method
      const result = await service.findRetreatsForPartner(criteria);

      // Assertions
      expect(mockPrismaService.partner.findUnique).toHaveBeenCalledWith({
        where: { id: 'partner-1' },
        include: {
          reviews: {
            select: {
              rating: true,
            },
          },
        },
      });

      expect(mockPrismaService.retreat.findMany).toHaveBeenCalled();
      expect(mockEventEmitter.emit).toHaveBeenCalled();

      expect(result.results.length).toBe(1);
      expect(result.total).toBe(1);
      expect(result.results[0].partnerId).toBe('partner-1');
      expect(result.results[0].retreatId).toBe('retreat-1');
      expect(result.results[0].score).toBeGreaterThan(0);
      expect(result.results[0].compatibilityFactors).toBeDefined();
    });

    it('should throw NotFoundException when partner is not found', async () => {
      // Mock the prisma service to return null for the partner
      mockPrismaService.partner.findUnique.mockResolvedValue(null);

      // Create test criteria
      const criteria: MatchingCriteriaDto = {
        partnerId: 'non-existent-partner',
      };

      // Expect the service to throw NotFoundException
      await expect(service.findRetreatsForPartner(criteria)).rejects.toThrow();
    });
  });
});
