import { Test, TestingModule } from '@nestjs/testing';
import { MatchingController } from '../controllers/matching.controller';
import { MatchingService } from '../services/matching.service';
import { MatchingCriteriaDto, MatchingResponseDto } from '../dto';
import { PartnerCategory, PartnerType } from '../../../prisma/prisma-types';

describe('MatchingController', () => {
  let controller: MatchingController;
  let service: MatchingService;

  const mockMatchingService = {
    findPartnersForRetreat: jest.fn(),
    findRetreatsForPartner: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MatchingController],
      providers: [
        {
          provide: MatchingService,
          useValue: mockMatchingService,
        },
      ],
    }).compile();

    controller = module.get<MatchingController>(MatchingController);
    service = module.get<MatchingService>(MatchingService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findPartners', () => {
    it('should call service.findPartnersForRetreat with the provided criteria', async () => {
      // Mock data
      const criteria: MatchingCriteriaDto = {
        retreatId: 'retreat-1',
        categories: [PartnerCategory.WELLNESS],
        specializations: ['Yoga'],
      };

      const mockResponse: MatchingResponseDto = {
        results: [
          {
            partnerId: 'partner-1',
            retreatId: 'retreat-1',
            score: 85,
            compatibilityFactors: {
              skillMatch: 90,
              availabilityMatch: 80,
              locationMatch: 100,
              ratingMatch: 85,
              budgetMatch: 70,
            },
            partner: {
              id: 'partner-1',
              companyName: 'Yoga Studio',
              type: PartnerType.CERTIFIED,
              category: PartnerCategory.WELLNESS,
              description: 'A yoga studio',
              specializations: ['Yoga', 'Meditation'],
              languages: ['French', 'English'],
              averageRating: 4.5,
              totalReviews: 10,
              completedServices: 20,
            },
          },
        ],
        total: 1,
        executionTimeMs: 100,
      };

      // Mock the service method
      mockMatchingService.findPartnersForRetreat.mockResolvedValue(mockResponse);

      // Call the controller method
      const result = await controller.findPartners(criteria);

      // Assertions
      expect(mockMatchingService.findPartnersForRetreat).toHaveBeenCalledWith(criteria);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('findRetreats', () => {
    it('should call service.findRetreatsForPartner with the provided criteria', async () => {
      // Mock data
      const criteria: MatchingCriteriaDto = {
        partnerId: 'partner-1',
        dateRange: {
          start: '2023-06-01',
          end: '2023-06-30',
        },
      };

      const mockResponse: MatchingResponseDto = {
        results: [
          {
            partnerId: 'partner-1',
            retreatId: 'retreat-1',
            score: 85,
            compatibilityFactors: {
              skillMatch: 90,
              availabilityMatch: 80,
              locationMatch: 100,
              ratingMatch: 85,
              budgetMatch: 70,
            },
            retreat: {
              id: 'retreat-1',
              title: 'Yoga Retreat',
              description: 'A yoga retreat',
              startDate: new Date('2023-06-01'),
              endDate: new Date('2023-06-08'),
              location: 'Paris, France',
              capacity: 20,
              price: 1000,
              images: ['image1.jpg'],
            },
          },
        ],
        total: 1,
        executionTimeMs: 100,
      };

      // Mock the service method
      mockMatchingService.findRetreatsForPartner.mockResolvedValue(mockResponse);

      // Call the controller method
      const result = await controller.findRetreats(criteria);

      // Assertions
      expect(mockMatchingService.findRetreatsForPartner).toHaveBeenCalledWith(criteria);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('findPartnersForRetreat', () => {
    it('should call service.findPartnersForRetreat with the correct parameters', async () => {
      // Mock data
      const retreatId = 'retreat-1';
      const categories = [PartnerCategory.WELLNESS];
      const types = [PartnerType.CERTIFIED];
      const specializations = ['Yoga'];
      const languages = ['French'];
      const minExperience = 5;
      const minRating = 4;
      const limit = 10;

      const mockResponse: MatchingResponseDto = {
        results: [],
        total: 0,
        executionTimeMs: 100,
      };

      // Mock the service method
      mockMatchingService.findPartnersForRetreat.mockResolvedValue(mockResponse);

      // Call the controller method
      const result = await controller.findPartnersForRetreat(
        retreatId,
        categories,
        types,
        specializations,
        languages,
        minExperience,
        minRating,
        limit,
      );

      // Assertions
      expect(mockMatchingService.findPartnersForRetreat).toHaveBeenCalledWith({
        retreatId,
        categories,
        types,
        specializations,
        languages,
        minExperience,
        minRating,
        limit,
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('findRetreatsForPartner', () => {
    it('should call service.findRetreatsForPartner with the correct parameters', async () => {
      // Mock data
      const partnerId = 'partner-1';
      const startDate = '2023-06-01';
      const endDate = '2023-06-30';
      const maxBudget = 1000;
      const country = 'France';
      const minCapacity = 10;
      const limit = 10;

      const mockResponse: MatchingResponseDto = {
        results: [],
        total: 0,
        executionTimeMs: 100,
      };

      // Mock the service method
      mockMatchingService.findRetreatsForPartner.mockResolvedValue(mockResponse);

      // Call the controller method
      const result = await controller.findRetreatsForPartner(
        partnerId,
        startDate,
        endDate,
        maxBudget,
        country,
        minCapacity,
        limit,
      );

      // Assertions
      expect(mockMatchingService.findRetreatsForPartner).toHaveBeenCalledWith({
        partnerId,
        dateRange: { start: startDate, end: endDate },
        maxBudget,
        location: { country },
        minCapacity,
        limit,
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getMatchingScore', () => {
    it('should return the matching score between a partner and a retreat', async () => {
      // Mock data
      const partnerId = 'partner-1';
      const retreatId = 'retreat-1';

      const mockResponse: MatchingResponseDto = {
        results: [
          {
            partnerId,
            retreatId,
            score: 85,
            compatibilityFactors: {
              skillMatch: 90,
              availabilityMatch: 80,
              locationMatch: 100,
              ratingMatch: 85,
              budgetMatch: 70,
            },
          },
        ],
        total: 1,
        executionTimeMs: 100,
      };

      // Mock the service method
      mockMatchingService.findPartnersForRetreat.mockResolvedValue(mockResponse);

      // Call the controller method
      const result = await controller.getMatchingScore(partnerId, retreatId);

      // Assertions
      expect(mockMatchingService.findPartnersForRetreat).toHaveBeenCalledWith({
        partnerId,
        retreatId,
      });
      expect(result).toEqual({
        score: 85,
        compatibilityFactors: {
          skillMatch: 90,
          availabilityMatch: 80,
          locationMatch: 100,
          ratingMatch: 85,
          budgetMatch: 70,
        },
      });
    });

    it('should return zero scores when no matching result is found', async () => {
      // Mock data
      const partnerId = 'partner-1';
      const retreatId = 'retreat-1';

      const mockResponse: MatchingResponseDto = {
        results: [],
        total: 0,
        executionTimeMs: 100,
      };

      // Mock the service method
      mockMatchingService.findPartnersForRetreat.mockResolvedValue(mockResponse);

      // Call the controller method
      const result = await controller.getMatchingScore(partnerId, retreatId);

      // Assertions
      expect(mockMatchingService.findPartnersForRetreat).toHaveBeenCalledWith({
        partnerId,
        retreatId,
      });
      expect(result).toEqual({
        score: 0,
        compatibilityFactors: {
          skillMatch: 0,
          availabilityMatch: 0,
          locationMatch: 0,
          ratingMatch: 0,
          budgetMatch: 0,
        },
      });
    });
  });
});
