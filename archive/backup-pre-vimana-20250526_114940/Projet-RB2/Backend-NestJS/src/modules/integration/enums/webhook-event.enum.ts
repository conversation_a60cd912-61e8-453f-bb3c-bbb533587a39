export enum WebhookEvent {
  USER_CREATED = 'user.created',
  USER_UPDATED = 'user.updated',
  USER_DELETED = 'user.deleted',
  COURSE_CREATED = 'course.created',
  COURSE_UPDATED = 'course.updated',
  COURSE_DELETED = 'course.deleted',
  ENROLLMENT_CREATED = 'enrollment.created',
  ENROLLMENT_UPDATED = 'enrollment.updated',
  ENROLLMENT_DELETED = 'enrollment.deleted',
  LESSON_COMPLETED = 'lesson.completed',
  COURSE_COMPLETED = 'course.completed',
  PAYMENT_SUCCEEDED = 'payment.succeeded',
  PAYMENT_FAILED = 'payment.failed',
  SUBSCRIPTION_CREATED = 'subscription.created',
  SUBSCRIPTION_UPDATED = 'subscription.updated',
  SUBSCRIPTION_CANCELLED = 'subscription.cancelled',
}
