import { Module } from '@nestjs/common';
import { IntegrationService } from './services/integration.service';
// TODO: Fichier manquant: './controllers/integration.controller'
// import { IntegrationController } from './controllers/integration.controller';
import { PrismaModule } from '../../prisma/prisma.module';
import { WebhookService } from './services/webhook.service';
// TODO: Fichier manquant: './services/api-key.service'
// import { ApiKeyService } from './services/api-key.service';
// TODO: Fichier manquant: './services/oauth.service'
// import { OAuthService } from './services/oauth.service';
// TODO: Fichier manquant: './services/external-api.service'
// import { ExternalApiService } from './services/external-api.service';
import { EventsModule } from '../events/events.module';

@Module({
  imports: [PrismaModule, EventsModule],
  // TODO: controllers: [IntegrationController], // Fichier manquant
  providers: [
    IntegrationService,
    WebhookService,
  ],
  exports: [
    IntegrationService,
    WebhookService
  ],
})
export class IntegrationModule {}
