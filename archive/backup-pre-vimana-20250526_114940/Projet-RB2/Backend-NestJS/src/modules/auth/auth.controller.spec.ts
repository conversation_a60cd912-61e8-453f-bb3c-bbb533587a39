import { Test, TestingModule } from '@nestjs/testing';
import { UnauthorizedException } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { testData } from '../../test/setup';

describe('AuthController', () => {
  let controller: AuthController;
  let authService: jest.Mocked<AuthService>;

  const mockAuthService = {
    login: jest.fn(),
    refreshToken: jest.fn(),
    logout: jest.fn(),
    validateUser: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get(AuthService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('login', () => {
    const loginDto: LoginDto = {
      email: testData.user.email,
      password: 'password123',
    };

    const expectedResponse = {
      access_token: 'access-token',
      refresh_token: 'refresh-token',
      user: {
        id: testData.user.id,
        email: testData.user.email,
        name: testData.user.name,
        role: testData.user.role,
      },
    };

    it('should return tokens and user data on successful login', async () => {
      // Arrange
      authService.login.mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.login(loginDto);

      // Assert
      expect(result).toEqual(expectedResponse);
      expect(authService.login).toHaveBeenCalledWith(loginDto);
      expect(authService.login).toHaveBeenCalledTimes(1);
    });

    it('should throw UnauthorizedException when login fails', async () => {
      // Arrange
      authService.login.mockRejectedValue(new UnauthorizedException('Invalid credentials'));

      // Act & Assert
      await expect(controller.login(loginDto)).rejects.toThrow(UnauthorizedException);
      expect(authService.login).toHaveBeenCalledWith(loginDto);
    });

    it('should handle login with two-factor authentication', async () => {
      // Arrange
      const loginDtoWith2FA: LoginDto = {
        ...loginDto,
        twoFactorToken: '123456',
      };

      authService.login.mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.login(loginDtoWith2FA);

      // Assert
      expect(result).toEqual(expectedResponse);
      expect(authService.login).toHaveBeenCalledWith(loginDtoWith2FA);
    });

    it('should validate input data', async () => {
      // Arrange
      const invalidLoginDto = {
        email: 'invalid-email',
        password: '',
      } as LoginDto;

      // Act & Assert
      // Note: La validation sera gérée par les ValidationPipes de NestJS
      // Ce test vérifie que le contrôleur appelle le service avec les bonnes données
      authService.login.mockResolvedValue(expectedResponse);
      
      await controller.login(invalidLoginDto);
      expect(authService.login).toHaveBeenCalledWith(invalidLoginDto);
    });
  });

  describe('refreshToken', () => {
    const refreshTokenDto: RefreshTokenDto = {
      refresh_token: 'valid-refresh-token',
    };

    const expectedResponse = {
      access_token: 'new-access-token',
    };

    it('should return new access token when refresh token is valid', async () => {
      // Arrange
      authService.refreshToken.mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.refreshToken(refreshTokenDto);

      // Assert
      expect(result).toEqual(expectedResponse);
      expect(authService.refreshToken).toHaveBeenCalledWith(refreshTokenDto);
      expect(authService.refreshToken).toHaveBeenCalledTimes(1);
    });

    it('should throw UnauthorizedException when refresh token is invalid', async () => {
      // Arrange
      const invalidRefreshTokenDto: RefreshTokenDto = {
        refresh_token: 'invalid-refresh-token',
      };

      authService.refreshToken.mockRejectedValue(
        new UnauthorizedException('Invalid refresh token'),
      );

      // Act & Assert
      await expect(controller.refreshToken(invalidRefreshTokenDto)).rejects.toThrow(
        UnauthorizedException,
      );
      expect(authService.refreshToken).toHaveBeenCalledWith(invalidRefreshTokenDto);
    });

    it('should handle empty refresh token', async () => {
      // Arrange
      const emptyRefreshTokenDto: RefreshTokenDto = {
        refresh_token: '',
      };

      authService.refreshToken.mockRejectedValue(
        new UnauthorizedException('Refresh token is required'),
      );

      // Act & Assert
      await expect(controller.refreshToken(emptyRefreshTokenDto)).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });

  describe('logout', () => {
    it('should successfully logout user', async () => {
      // Arrange
      const mockRequest = {
        user: {
          id: testData.user.id,
          email: testData.user.email,
        },
      };

      authService.logout.mockResolvedValue({ message: 'Logout successful' });

      // Act
      const result = await controller.logout(mockRequest);

      // Assert
      expect(result).toEqual({ message: 'Logout successful' });
      expect(authService.logout).toHaveBeenCalledWith(mockRequest.user.id);
    });

    it('should handle logout when user is not authenticated', async () => {
      // Arrange
      const mockRequest = {
        user: null,
      };

      // Act & Assert
      // Le guard JWT devrait empêcher cela, mais testons la robustesse
      await expect(controller.logout(mockRequest)).rejects.toThrow();
    });
  });

  describe('getProfile', () => {
    it('should return user profile when authenticated', async () => {
      // Arrange
      const mockRequest = {
        user: {
          id: testData.user.id,
          email: testData.user.email,
          name: testData.user.name,
          role: testData.user.role,
        },
      };

      // Act
      const result = await controller.getProfile(mockRequest);

      // Assert
      expect(result).toEqual(mockRequest.user);
    });

    it('should handle missing user in request', async () => {
      // Arrange
      const mockRequest = {
        user: null,
      };

      // Act & Assert
      await expect(controller.getProfile(mockRequest)).rejects.toThrow();
    });
  });

  describe('error handling', () => {
    it('should handle service errors gracefully', async () => {
      // Arrange
      const loginDto: LoginDto = {
        email: testData.user.email,
        password: 'password123',
      };

      authService.login.mockRejectedValue(new Error('Database connection failed'));

      // Act & Assert
      await expect(controller.login(loginDto)).rejects.toThrow('Database connection failed');
    });

    it('should handle network timeouts', async () => {
      // Arrange
      const loginDto: LoginDto = {
        email: testData.user.email,
        password: 'password123',
      };

      authService.login.mockRejectedValue(new Error('Request timeout'));

      // Act & Assert
      await expect(controller.login(loginDto)).rejects.toThrow('Request timeout');
    });
  });
});
