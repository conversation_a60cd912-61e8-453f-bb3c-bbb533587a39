import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import axios from 'axios';

/**
 * Interface pour les résultats de validation de mot de passe
 */
export interface PasswordValidationResult {
  isValid: boolean;
  score: number; // 0-4, 0 étant très faible, 4 étant très fort
  feedback: {
    warning?: string;
    suggestions: string[];
  };
  breached?: boolean; // Si le mot de passe a été compromis dans une fuite de données
}

/**
 * Service pour gérer la politique de mots de passe
 * Vérifie la force des mots de passe et s'ils ont été compromis
 */
@Injectable()
export class PasswordPolicyService {
  private readonly logger = new Logger(PasswordPolicyService.name);
  private readonly minLength: number;
  private readonly requireUppercase: boolean;
  private readonly requireLowercase: boolean;
  private readonly requireNumbers: boolean;
  private readonly requireSpecialChars: boolean;
  private readonly checkBreachedPasswords: boolean;
  private readonly minScore: number;

  constructor(private readonly configService: ConfigService) {
    this.minLength = this.configService.get<number>('PASSWORD_MIN_LENGTH', 8);
    this.requireUppercase = this.configService.get<boolean>('PASSWORD_REQUIRE_UPPERCASE', true);
    this.requireLowercase = this.configService.get<boolean>('PASSWORD_REQUIRE_LOWERCASE', true);
    this.requireNumbers = this.configService.get<boolean>('PASSWORD_REQUIRE_NUMBERS', true);
    this.requireSpecialChars = this.configService.get<boolean>('PASSWORD_REQUIRE_SPECIAL_CHARS', true);
    this.checkBreachedPasswords = this.configService.get<boolean>('PASSWORD_CHECK_BREACHED', true);
    this.minScore = this.configService.get<number>('PASSWORD_MIN_SCORE', 3);
  }

  /**
   * Valider un mot de passe selon la politique configurée
   * @param password Mot de passe à valider
   */
  async validatePassword(password: string): Promise<PasswordValidationResult> {
    try {
      const result: PasswordValidationResult = {
        isValid: true,
        score: 0,
        feedback: {
          suggestions: [],
        },
        breached: false,
      };

      // Vérifier la longueur minimale
      if (password.length < this.minLength) {
        result.isValid = false;
        result.feedback.warning = `Password must be at least ${this.minLength} characters long`;
        result.feedback.suggestions.push(`Add at least ${this.minLength - password.length} more characters`);
      }

      // Vérifier les exigences de caractères
      if (this.requireUppercase && !/[A-Z]/.test(password)) {
        result.isValid = false;
        result.feedback.suggestions.push('Add uppercase letters');
      }

      if (this.requireLowercase && !/[a-z]/.test(password)) {
        result.isValid = false;
        result.feedback.suggestions.push('Add lowercase letters');
      }

      if (this.requireNumbers && !/[0-9]/.test(password)) {
        result.isValid = false;
        result.feedback.suggestions.push('Add numbers');
      }

      if (this.requireSpecialChars && !/[^A-Za-z0-9]/.test(password)) {
        result.isValid = false;
        result.feedback.suggestions.push('Add special characters');
      }

      // Calculer le score de force du mot de passe
      result.score = this.calculatePasswordStrength(password);

      if (result.score < this.minScore) {
        result.isValid = false;
        result.feedback.warning = result.feedback.warning || 'Password is too weak';
        result.feedback.suggestions.push('Use a stronger password');
      }

      // Vérifier si le mot de passe a été compromis
      if (this.checkBreachedPasswords) {
        result.breached = await this.isPasswordBreached(password);
        
        if (result.breached) {
          result.isValid = false;
          result.feedback.warning = 'This password has been found in a data breach';
          result.feedback.suggestions.push('Choose a password that hasn\'t been compromised');
        }
      }

      return result;
    } catch (error) {
      this.logger.error(`Error validating password: ${error.message}`, error.stack);
      // En cas d'erreur, retourner un résultat par défaut
      return {
        isValid: false,
        score: 0,
        feedback: {
          warning: 'Error validating password',
          suggestions: ['Please try again'],
        },
      };
    }
  }

  /**
   * Calculer la force d'un mot de passe (score de 0 à 4)
   * @param password Mot de passe à évaluer
   */
  private calculatePasswordStrength(password: string): number {
    let score = 0;

    // Longueur
    if (password.length >= this.minLength) {
      score += 1;
    }
    if (password.length >= 12) {
      score += 1;
    }

    // Complexité
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumbers = /[0-9]/.test(password);
    const hasSpecialChars = /[^A-Za-z0-9]/.test(password);

    // Ajouter des points pour chaque type de caractère
    let typesCount = 0;
    if (hasUppercase) typesCount++;
    if (hasLowercase) typesCount++;
    if (hasNumbers) typesCount++;
    if (hasSpecialChars) typesCount++;

    score += Math.min(2, typesCount - 1); // Max 2 points pour la diversité des caractères

    // Vérifier les motifs courants
    const hasRepeatedChars = /(.)\1{2,}/.test(password); // 3+ caractères répétés
    const hasSequentialChars = this.hasSequentialChars(password);

    if (hasRepeatedChars || hasSequentialChars) {
      score = Math.max(0, score - 1);
    }

    return score;
  }

  /**
   * Vérifier si le mot de passe contient des séquences de caractères
   * @param password Mot de passe à vérifier
   */
  private hasSequentialChars(password: string): boolean {
    const sequences = [
      'abcdefghijklmnopqrstuvwxyz',
      'qwertyuiop',
      'asdfghjkl',
      'zxcvbnm',
      '1234567890',
    ];

    const lowercasePassword = password.toLowerCase();

    for (const sequence of sequences) {
      for (let i = 0; i < sequence.length - 2; i++) {
        const seq = sequence.substring(i, i + 3);
        if (lowercasePassword.includes(seq)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Vérifier si un mot de passe a été compromis dans une fuite de données
   * Utilise l'API "Have I Been Pwned" avec k-anonymity
   * @param password Mot de passe à vérifier
   */
  private async isPasswordBreached(password: string): Promise<boolean> {
    try {
      // Calculer le hash SHA-1 du mot de passe
      const sha1Hash = crypto.createHash('sha1').update(password).digest('hex').toUpperCase();
      
      // Utiliser les 5 premiers caractères du hash pour l'API (k-anonymity)
      const prefix = sha1Hash.substring(0, 5);
      const suffix = sha1Hash.substring(5);
      
      // Appeler l'API Have I Been Pwned
      const response = await axios.get(`https://api.pwnedpasswords.com/range/${prefix}`, {
        headers: {
          'User-Agent': 'Retreat-And-Be-API',
        },
      });
      
      // Vérifier si le suffixe du hash est présent dans la réponse
      const hashes = response.data.split('\r\n');
      
      for (const hash of hashes) {
        const [hashSuffix, count] = hash.split(':');
        
        if (hashSuffix === suffix) {
          this.logger.warn(`Password has been found in ${count} data breaches`);
          return true;
        }
      }
      
      return false;
    } catch (error) {
      this.logger.error(`Error checking breached password: ${error.message}`, error.stack);
      // En cas d'erreur, supposer que le mot de passe n'est pas compromis
      return false;
    }
  }
}
