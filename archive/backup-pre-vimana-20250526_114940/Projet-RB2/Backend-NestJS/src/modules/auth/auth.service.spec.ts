import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UnauthorizedException, BadRequestException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import { TwoFactorAuthService } from './services/two-factor-auth.service';
import { PasswordPolicyService } from './services/password-policy.service';
import { LoginDto } from './dto/login.dto';
import { testData } from '../../test/setup';
import * as bcrypt from 'bcrypt';

// Mock bcrypt
jest.mock('bcrypt');
const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('AuthService', () => {
  let service: AuthService;
  let usersService: jest.Mocked<UsersService>;
  let jwtService: jest.Mocked<JwtService>;
  let twoFactorAuthService: jest.Mocked<TwoFactorAuthService>;
  let passwordPolicyService: jest.Mocked<PasswordPolicyService>;
  let configService: jest.Mocked<ConfigService>;

  const mockUser = {
    id: testData.user.id,
    email: testData.user.email,
    name: testData.user.name,
    password: testData.user.password,
    role: testData.user.role,
    isActive: true,
    isVerified: true,
    twoFactorEnabled: false,
    isTwoFactorEnabled: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockUsersService = {
      findByEmail: jest.fn(),
      findById: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      create: jest.fn(),
    };

    const mockJwtService = {
      sign: jest.fn(),
      verify: jest.fn(),
    };

    const mockTwoFactorAuthService = {
      verifyToken: jest.fn(),
      generateSecret: jest.fn(),
      verify: jest.fn(),
    };

    const mockPasswordPolicyService = {
      validatePassword: jest.fn(),
      hashPassword: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: UsersService, useValue: mockUsersService },
        { provide: JwtService, useValue: mockJwtService },
        { provide: TwoFactorAuthService, useValue: mockTwoFactorAuthService },
        { provide: PasswordPolicyService, useValue: mockPasswordPolicyService },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    usersService = module.get(UsersService);
    jwtService = module.get(JwtService);
    twoFactorAuthService = module.get(TwoFactorAuthService);
    passwordPolicyService = module.get(PasswordPolicyService);
    configService = module.get(ConfigService);

    // Configuration par défaut
    configService.get.mockImplementation((key: string) => {
      const config = {
        JWT_ACCESS_EXPIRES_IN: '15m',
        JWT_REFRESH_EXPIRES_IN: '7d',
        JWT_SECRET: 'test-secret',
      };
      return config[key];
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validateUser', () => {
    it('should return user data when credentials are valid', async () => {
      // Arrange
      const email = testData.user.email;
      const password = 'password123';

      usersService.findByEmail.mockResolvedValue(mockUser);
      mockedBcrypt.compare.mockResolvedValue(true as never);

      // Act
      const result = await service.validateUser(email, password);

      // Assert
      expect(result).toEqual(mockUser);
      expect(usersService.findByEmail).toHaveBeenCalledWith(email);
      expect(bcrypt.compare).toHaveBeenCalledWith(password, mockUser.password);
    });

    it('should return null when user is not found', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';

      usersService.findByEmail.mockResolvedValue(null);

      // Act
      const result = await service.validateUser(email, password);

      // Assert
      expect(result).toBeNull();
      expect(usersService.findByEmail).toHaveBeenCalledWith(email);
      expect(bcrypt.compare).not.toHaveBeenCalled();
    });

    it('should return null when password is invalid', async () => {
      // Arrange
      const email = testData.user.email;
      const password = 'wrongpassword';

      usersService.findByEmail.mockResolvedValue(mockUser);
      mockedBcrypt.compare.mockResolvedValue(false as never);

      // Act
      const result = await service.validateUser(email, password);

      // Assert
      expect(result).toBeNull();
      expect(bcrypt.compare).toHaveBeenCalledWith(password, mockUser.password);
    });

    it('should return null when user is inactive', async () => {
      // Arrange
      const email = testData.user.email;
      const password = 'password123';
      const inactiveUser = { ...mockUser, isActive: false };

      usersService.findByEmail.mockResolvedValue(inactiveUser);

      // Act
      const result = await service.validateUser(email, password);

      // Assert
      expect(result).toBeNull();
      expect(usersService.findByEmail).toHaveBeenCalledWith(email);
    });
  });

  describe('login', () => {
    const loginDto: LoginDto = {
      email: testData.user.email,
      password: 'password123',
    };

    it('should return tokens when login is successful', async () => {
      // Arrange
      const accessToken = 'access-token';
      const refreshToken = 'refresh-token';

      jest.spyOn(service, 'validateUser').mockResolvedValue(mockUser);
      usersService.findByEmail.mockResolvedValue(mockUser);

      jwtService.sign
        .mockReturnValueOnce(accessToken)
        .mockReturnValueOnce(refreshToken);

      // Act
      const result = await service.login(loginDto);

      // Assert
      expect(result).toEqual({
        access_token: accessToken,
        refresh_token: refreshToken,
        user: {
          id: mockUser.id,
          email: mockUser.email,
          name: mockUser.name,
          role: mockUser.role,
        },
      });
    });

    it('should throw UnauthorizedException when credentials are invalid', async () => {
      // Arrange
      jest.spyOn(service, 'validateUser').mockResolvedValue(null);

      // Act & Assert
      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });

    // Note: Test 2FA temporairement désactivé en attendant la finalisation de l'interface
    it.skip('should handle two-factor authentication when enabled', async () => {
      // Test à implémenter une fois l'interface TwoFactorAuthService finalisée
    });
  });

  describe('refreshToken', () => {
    it('should return new access token when refresh token is valid', async () => {
      // Arrange
      const refreshToken = 'valid-refresh-token';
      const newAccessToken = 'new-access-token';
      const payload = { sub: mockUser.id, email: mockUser.email };

      jwtService.verify.mockReturnValue(payload);
      usersService.findOne.mockResolvedValue(mockUser);
      jwtService.sign.mockReturnValue(newAccessToken);

      // Act
      const result = await service.refreshToken({ refreshToken: refreshToken });

      // Assert
      expect(result).toEqual({
        access_token: newAccessToken,
      });
      expect(jwtService.verify).toHaveBeenCalledWith(refreshToken);
      expect(usersService.findOne).toHaveBeenCalledWith(mockUser.id);
    });

    it('should throw UnauthorizedException when refresh token is invalid', async () => {
      // Arrange
      const invalidRefreshToken = 'invalid-refresh-token';

      jwtService.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Act & Assert
      await expect(
        service.refreshToken({ refreshToken: invalidRefreshToken }),
      ).rejects.toThrow(UnauthorizedException);
    });
  });
});
