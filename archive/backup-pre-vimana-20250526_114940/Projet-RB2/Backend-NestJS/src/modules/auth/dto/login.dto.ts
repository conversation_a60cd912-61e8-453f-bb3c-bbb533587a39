import { IsEmail, IsString, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({
    description: 'Email de l\'utilisateur',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Veuillez fournir un email valide' })
  email: string;

  @ApiProperty({
    description: 'Mot de passe de l\'utilisateur',
    example: 'Password123!',
  })
  @IsString()
  password: string;

  @ApiPropertyOptional({
    description: 'Code d\'authentification à deux facteurs',
    example: '123456',
  })
  @IsOptional()
  @IsString()
  twoFactorCode?: string;
}
