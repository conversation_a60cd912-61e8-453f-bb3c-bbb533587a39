import { IsNotEmpty, IsString, Length } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO pour la vérification d'un token 2FA
 */
export class VerifyTwoFactorDto {
  @ApiProperty({
    description: 'Token 2FA à 6 chiffres ou code de récupération',
    example: '123456',
  })
  @IsNotEmpty({ message: 'Token is required' })
  @IsString({ message: 'Token must be a string' })
  @Length(6, 25, { message: 'Token must be between 6 and 25 characters' })
  token: string;
}
