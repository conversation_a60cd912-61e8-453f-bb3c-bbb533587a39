import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../users/users.service';
import { LoginDto } from './dto/login.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import * as bcrypt from 'bcrypt';
import * as speakeasy from 'speakeasy';
import * as QRCode from 'qrcode';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    try {
      const user = await this.usersService.findByEmail(email);
      
      if (!user) {
        return null;
      }
      
      const isPasswordValid = await bcrypt.compare(password, user.password);
      
      if (!isPasswordValid) {
        return null;
      }
      
      // Ne pas renvoyer le mot de passe
      const { password: _, ...result } = user;
      return result;
    } catch (error) {
      this.logger.error(`Erreur lors de la validation de l'utilisateur: ${error.message}`);
      return null;
    }
  }

  async login(loginDto: LoginDto) {
    const user = await this.validateUser(loginDto.email, loginDto.password);
    
    if (!user) {
      throw new UnauthorizedException('Identifiants invalides');
    }
    
    // Vérifier si l'utilisateur est actif
    if (!user.isActive) {
      throw new UnauthorizedException('Compte désactivé');
    }
    
    // Vérifier si l'authentification à deux facteurs est activée
    if (user.twoFactorEnabled && !loginDto.twoFactorCode) {
      return {
        requiresTwoFactor: true,
        message: 'Code d\'authentification à deux facteurs requis',
      };
    }
    
    // Vérifier le code d'authentification à deux facteurs si nécessaire
    if (user.twoFactorEnabled && loginDto.twoFactorCode) {
      const isCodeValid = this.verifyTwoFactorCode(
        user.twoFactorSecret,
        loginDto.twoFactorCode,
      );
      
      if (!isCodeValid) {
        throw new UnauthorizedException('Code d\'authentification à deux facteurs invalide');
      }
    }
    
    // Générer les tokens
    const tokens = await this.generateTokens(user);
    
    return {
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
      },
      ...tokens,
    };
  }

  async refreshToken(refreshTokenDto: RefreshTokenDto) {
    try {
      // Vérifier le refresh token
      const decoded = this.jwtService.verify(refreshTokenDto.refreshToken, {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
      });
      
      // Récupérer l'utilisateur
      const user = await this.usersService.findOne(decoded.sub);
      
      // Vérifier si l'utilisateur est actif
      if (!user.isActive) {
        throw new UnauthorizedException('Compte désactivé');
      }
      
      // Générer de nouveaux tokens
      const tokens = await this.generateTokens(user);
      
      return tokens;
    } catch (error) {
      this.logger.error(`Erreur lors du rafraîchissement du token: ${error.message}`);
      throw new UnauthorizedException('Token de rafraîchissement invalide ou expiré');
    }
  }

  async logout(userId: string) {
    // Dans une implémentation plus complète, on pourrait ajouter le token à une liste noire
    // ou invalider les sessions utilisateur
    return { message: 'Déconnexion réussie' };
  }

  async generateTwoFactorSecret(userId: string) {
    // Générer un secret pour l'authentification à deux facteurs
    const secret = speakeasy.generateSecret({
      name: `RetreatAndBe:${userId}`,
    });
    
    // Enregistrer le secret dans la base de données
    await this.usersService.setTwoFactorSecret(userId, secret.base32);
    
    // Générer un QR code
    const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);
    
    return {
      secret: secret.base32,
      qrCodeUrl,
    };
  }

  async enableTwoFactor(userId: string, twoFactorCode: string) {
    // Récupérer l'utilisateur
    const user = await this.usersService.findOne(userId);
    
    // Vérifier le code
    const isCodeValid = this.verifyTwoFactorCode(
      user.twoFactorSecret,
      twoFactorCode,
    );
    
    if (!isCodeValid) {
      throw new UnauthorizedException('Code d\'authentification à deux facteurs invalide');
    }
    
    // Activer l'authentification à deux facteurs
    await this.usersService.enableTwoFactor(userId);
    
    return { message: 'Authentification à deux facteurs activée avec succès' };
  }

  async disableTwoFactor(userId: string) {
    // Désactiver l'authentification à deux facteurs
    await this.usersService.disableTwoFactor(userId);
    
    return { message: 'Authentification à deux facteurs désactivée avec succès' };
  }

  private verifyTwoFactorCode(secret: string, token: string): boolean {
    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token,
      window: 2, // Permet une tolérance de 2 périodes (30 secondes chacune)
    });
  }

  private async generateTokens(user: any) {
    const payload = { sub: user.id, email: user.email, role: user.role };
    
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('JWT_SECRET'),
        expiresIn: this.configService.get<string>('JWT_ACCESS_EXPIRES_IN'),
      }),
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
        expiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRES_IN'),
      }),
    ]);
    
    return {
      accessToken,
      refreshToken,
    };
  }
}
