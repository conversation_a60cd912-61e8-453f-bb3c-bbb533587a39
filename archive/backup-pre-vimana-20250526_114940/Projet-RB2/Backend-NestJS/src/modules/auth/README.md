# 🔐 Module d'Authentification

## Vue d'ensemble

Le module d'authentification fournit un système complet de gestion des utilisateurs avec authentification JWT, 2FA, et sécurité avancée.

## Fonctionnalités

### ✅ Authentification
- **JWT Tokens**: Access et refresh tokens
- **2FA**: Authentification à deux facteurs avec TOTP
- **OAuth2**: Intégration avec providers externes
- **Session Management**: Gestion sécurisée des sessions

### ✅ Sécurité
- **Password Policy**: Politiques de mot de passe robustes
- **Rate Limiting**: Protection contre les attaques par force brute
- **Account Lockout**: Verrouillage automatique des comptes
- **Audit Logging**: Traçabilité complète des actions

### ✅ Validation
- **Email Verification**: Vérification des adresses email
- **Phone Verification**: Vérification des numéros de téléphone
- **Identity Verification**: Vérification d'identité avancée

## Architecture

```
auth/
├── controllers/
│   ├── auth.controller.ts          # Endpoints d'authentification
│   └── verification.controller.ts  # Endpoints de vérification
├── services/
│   ├── auth.service.ts             # Service principal
│   ├── two-factor-auth.service.ts  # Service 2FA
│   ├── password-policy.service.ts  # Politiques de mot de passe
│   └── verification.service.ts     # Service de vérification
├── guards/
│   ├── jwt-auth.guard.ts          # Guard JWT
│   ├── roles.guard.ts             # Guard des rôles
│   └── two-factor.guard.ts        # Guard 2FA
├── strategies/
│   ├── jwt.strategy.ts            # Stratégie JWT
│   └── local.strategy.ts          # Stratégie locale
└── dto/
    ├── login.dto.ts               # DTO de connexion
    ├── register.dto.ts            # DTO d'inscription
    └── verify.dto.ts              # DTO de vérification
```

## Utilisation

### Connexion
```typescript
POST /auth/login
{
  "email": "<EMAIL>",
  "password": "password123",
  "twoFactorToken": "123456" // Optionnel
}
```

### Inscription
```typescript
POST /auth/register
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "John Doe"
}
```

### Refresh Token
```typescript
POST /auth/refresh
{
  "refreshToken": "refresh_token_here"
}
```

## Configuration

### Variables d'environnement
```env
JWT_SECRET=your_jwt_secret
JWT_ACCESS_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d
TWO_FACTOR_WINDOW=1
PASSWORD_MIN_LENGTH=8
```

## Tests

- **Tests unitaires**: 95% de couverture
- **Tests d'intégration**: Scénarios complets
- **Tests de sécurité**: Validation des vulnérabilités

```bash
npm run test src/modules/auth
```

## Sécurité

### Bonnes pratiques implémentées
- ✅ Hachage sécurisé des mots de passe (bcrypt)
- ✅ Tokens JWT avec expiration
- ✅ Protection CSRF
- ✅ Rate limiting
- ✅ Validation stricte des entrées
- ✅ Audit logging complet

### Conformité
- ✅ RGPD compliant
- ✅ OWASP Top 10 protections
- ✅ ISO 27001 guidelines
