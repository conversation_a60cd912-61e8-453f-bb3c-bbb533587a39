import { Controller, Get, Query, Param, UseGuards, Logger, Req, Res, StreamableFile } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { ReportGeneratorService } from '../services/report-generator.service';
import { Response } from 'express';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Contrôleur pour la génération de rapports PDF
 */
@ApiTags('recommendation-reports')
@Controller('recommendation-reports')
export class ReportGeneratorController {
  private readonly logger = new Logger(ReportGeneratorController.name);

  constructor(private readonly reportGeneratorService: ReportGeneratorService) {}

  /**
   * Génère un rapport PDF global sur les recommandations
   * @param res Réponse HTTP
   * @param startDate Date de début de la période d'analyse
   * @param endDate Date de fin de la période d'analyse
   * @returns Fichier PDF
   */
  @Get('global')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Générer un rapport PDF global sur les recommandations' })
  @ApiResponse({ status: 200, description: 'Fichier PDF' })
  async generateGlobalReport(
    @Res({ passthrough: true }) res: Response,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.log(`Génération d'un rapport global du ${startDate || 'début'} au ${endDate || 'aujourd\'hui'}`);
    
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    
    const filePath = await this.reportGeneratorService.generateGlobalReport(start, end);
    
    const fileName = path.basename(filePath);
    const file = fs.createReadStream(filePath);
    
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${fileName}"`,
    });
    
    return new StreamableFile(file);
  }

  /**
   * Génère un rapport PDF pour l'utilisateur courant
   * @param req Requête avec les informations de l'utilisateur
   * @param res Réponse HTTP
   * @param startDate Date de début de la période d'analyse
   * @param endDate Date de fin de la période d'analyse
   * @returns Fichier PDF
   */
  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Générer un rapport PDF pour l\'utilisateur courant' })
  @ApiResponse({ status: 200, description: 'Fichier PDF' })
  async generateCurrentUserReport(
    @Req() req,
    @Res({ passthrough: true }) res: Response,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const userId = req.user.id;
    
    this.logger.log(`Génération d'un rapport pour l'utilisateur ${userId} du ${startDate || 'début'} au ${endDate || 'aujourd\'hui'}`);
    
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    
    const filePath = await this.reportGeneratorService.generateUserReport(userId, start, end);
    
    const fileName = path.basename(filePath);
    const file = fs.createReadStream(filePath);
    
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${fileName}"`,
    });
    
    return new StreamableFile(file);
  }

  /**
   * Génère un rapport PDF pour un utilisateur spécifique
   * @param userId ID de l'utilisateur
   * @param res Réponse HTTP
   * @param startDate Date de début de la période d'analyse
   * @param endDate Date de fin de la période d'analyse
   * @returns Fichier PDF
   */
  @Get('users/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Générer un rapport PDF pour un utilisateur spécifique' })
  @ApiResponse({ status: 200, description: 'Fichier PDF' })
  async generateUserReport(
    @Param('userId') userId: string,
    @Res({ passthrough: true }) res: Response,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.log(`Génération d'un rapport pour l'utilisateur ${userId} du ${startDate || 'début'} au ${endDate || 'aujourd\'hui'}`);
    
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    
    const filePath = await this.reportGeneratorService.generateUserReport(userId, start, end);
    
    const fileName = path.basename(filePath);
    const file = fs.createReadStream(filePath);
    
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${fileName}"`,
    });
    
    return new StreamableFile(file);
  }
}
