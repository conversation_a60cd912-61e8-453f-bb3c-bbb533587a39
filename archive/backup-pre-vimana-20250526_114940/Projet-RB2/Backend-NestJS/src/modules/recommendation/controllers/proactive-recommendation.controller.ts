import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { ProactiveRecommendationService } from '../services/proactive-recommendation.service';
import {
  ProactiveRecommendationConfig,
  ProactiveRecommendationTrigger,
  ProactiveRecommendationPriority,
  ProactiveRecommendationRequest,
  NotificationRequest,
} from '../interfaces/proactive-recommendation.interface';

/**
 * Controller for proactive recommendations
 */
@ApiTags('Proactive Recommendations')
@Controller('recommendation/proactive')
export class ProactiveRecommendationController {
  private readonly logger = new Logger(ProactiveRecommendationController.name);

  constructor(private readonly proactiveService: ProactiveRecommendationService) {}

  /**
   * Get proactive recommendation configuration
   */
  @ApiOperation({ summary: 'Get proactive recommendation configuration' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Proactive recommendation configuration' })
  @Get('config')
  getProactiveConfig() {
    try {
      return this.proactiveService.getProactiveConfig();
    } catch (error) {
      this.logger.error(`Error getting proactive configuration: ${error.message}`);
      throw new HttpException('Failed to get proactive configuration', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update proactive recommendation configuration
   */
  @ApiOperation({ summary: 'Update proactive recommendation configuration' })
  @ApiBody({ description: 'Proactive recommendation configuration' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Updated proactive recommendation configuration' })
  @Put('config')
  updateProactiveConfig(@Body() config: Partial<ProactiveRecommendationConfig>) {
    try {
      return this.proactiveService.updateProactiveConfig(config);
    } catch (error) {
      this.logger.error(`Error updating proactive configuration: ${error.message}`);
      throw new HttpException('Failed to update proactive configuration', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get pending proactive recommendations for a user
   */
  @ApiOperation({ summary: 'Get pending proactive recommendations for a user' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiQuery({ name: 'limit', description: 'Maximum number of recommendations to return', required: false })
  @ApiQuery({ name: 'minPriority', description: 'Minimum priority level', required: false })
  @ApiResponse({ status: HttpStatus.OK, description: 'Pending proactive recommendations' })
  @Get('recommendations/:userId')
  async getPendingRecommendations(
    @Param('userId') userId: string,
    @Query('limit') limit?: number,
    @Query('minPriority') minPriority?: ProactiveRecommendationPriority,
  ) {
    try {
      const recommendations = await this.proactiveService.getPendingRecommendations(
        userId,
        limit ? parseInt(limit.toString()) : 10,
        minPriority ? parseInt(minPriority.toString()) as ProactiveRecommendationPriority : ProactiveRecommendationPriority.LOW,
      );
      
      return {
        recommendations,
        count: recommendations.length,
        userId,
      };
    } catch (error) {
      this.logger.error(`Error getting pending recommendations for user ${userId}: ${error.message}`);
      throw new HttpException('Failed to get pending recommendations', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Generate proactive recommendations for a user
   */
  @ApiOperation({ summary: 'Generate proactive recommendations for a user' })
  @ApiBody({ description: 'Proactive recommendation request' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Generated proactive recommendations' })
  @Post('recommendations')
  async generateProactiveRecommendations(@Body() request: ProactiveRecommendationRequest) {
    try {
      const recommendations = await this.proactiveService.generateProactiveRecommendations(
        request.userId,
        request.trigger || ProactiveRecommendationTrigger.MANUAL,
      );
      
      return {
        recommendations,
        count: recommendations.length,
        userId: request.userId,
      };
    } catch (error) {
      this.logger.error(`Error generating proactive recommendations for user ${request.userId}: ${error.message}`);
      throw new HttpException('Failed to generate proactive recommendations', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Mark recommendation as viewed
   */
  @ApiOperation({ summary: 'Mark recommendation as viewed' })
  @ApiParam({ name: 'recommendationId', description: 'Recommendation ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Recommendation marked as viewed' })
  @Put('recommendations/:recommendationId/viewed')
  async markRecommendationAsViewed(@Param('recommendationId') recommendationId: string) {
    try {
      // In a real implementation, this would update the recommendation in the database
      // For now, we'll just return a success message
      
      return {
        success: true,
        recommendationId,
        message: 'Recommendation marked as viewed',
      };
    } catch (error) {
      this.logger.error(`Error marking recommendation ${recommendationId} as viewed: ${error.message}`);
      throw new HttpException('Failed to mark recommendation as viewed', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Dismiss recommendation
   */
  @ApiOperation({ summary: 'Dismiss recommendation' })
  @ApiParam({ name: 'recommendationId', description: 'Recommendation ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Recommendation dismissed' })
  @Delete('recommendations/:recommendationId')
  async dismissRecommendation(@Param('recommendationId') recommendationId: string) {
    try {
      // In a real implementation, this would update the recommendation in the database
      // For now, we'll just return a success message
      
      return {
        success: true,
        recommendationId,
        message: 'Recommendation dismissed',
      };
    } catch (error) {
      this.logger.error(`Error dismissing recommendation ${recommendationId}: ${error.message}`);
      throw new HttpException('Failed to dismiss recommendation', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Send notification for a recommendation
   */
  @ApiOperation({ summary: 'Send notification for a recommendation' })
  @ApiBody({ description: 'Notification request' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Notification sent' })
  @Post('notifications')
  async sendNotification(@Body() request: NotificationRequest) {
    try {
      // In a real implementation, this would send a notification
      // For now, we'll just return a success message
      
      return {
        success: true,
        userId: request.userId,
        recommendationId: request.recommendationId,
        strategy: request.strategy || 'push',
        message: 'Notification sent',
      };
    } catch (error) {
      this.logger.error(`Error sending notification for recommendation ${request.recommendationId}: ${error.message}`);
      throw new HttpException('Failed to send notification', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get notification preferences for a user
   */
  @ApiOperation({ summary: 'Get notification preferences for a user' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Notification preferences' })
  @Get('notifications/preferences/:userId')
  async getNotificationPreferences(@Param('userId') userId: string) {
    try {
      // In a real implementation, this would fetch preferences from the database
      // For now, we'll return mock data
      
      return {
        userId,
        enabled: true,
        strategies: {
          push: true,
          email: true,
          in_app: true,
          sms: false,
        },
        quietHours: {
          enabled: true,
          start: 22, // 10 PM
          end: 8, // 8 AM
        },
        frequency: 'medium',
      };
    } catch (error) {
      this.logger.error(`Error getting notification preferences for user ${userId}: ${error.message}`);
      throw new HttpException('Failed to get notification preferences', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update notification preferences for a user
   */
  @ApiOperation({ summary: 'Update notification preferences for a user' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiBody({ description: 'Notification preferences' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Updated notification preferences' })
  @Put('notifications/preferences/:userId')
  async updateNotificationPreferences(
    @Param('userId') userId: string,
    @Body() preferences: any,
  ) {
    try {
      // In a real implementation, this would update preferences in the database
      // For now, we'll just return the updated preferences
      
      return {
        ...preferences,
        userId,
        message: 'Notification preferences updated',
      };
    } catch (error) {
      this.logger.error(`Error updating notification preferences for user ${userId}: ${error.message}`);
      throw new HttpException('Failed to update notification preferences', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
