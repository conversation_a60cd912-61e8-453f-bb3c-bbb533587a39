import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { GenerativeAIService } from '../services/generative-ai.service';
import {
  GenerativeAIConfig,
  ExplanationGenerationType,
  ContentGenerationType,
  ExplanationRequest,
  ContentGenerationRequest,
  NLPRecommendationRequest,
} from '../interfaces/generative-ai.interface';

/**
 * Controller for generative AI features
 */
@ApiTags('Generative AI')
@Controller('recommendation/ai')
export class GenerativeAIController {
  private readonly logger = new Logger(GenerativeAIController.name);

  constructor(private readonly generativeAIService: GenerativeAIService) {}

  /**
   * Get AI configuration
   */
  @ApiOperation({ summary: 'Get AI configuration' })
  @ApiResponse({ status: HttpStatus.OK, description: 'AI configuration' })
  @Get('config')
  getAIConfig() {
    try {
      return this.generativeAIService.getAIConfig();
    } catch (error) {
      this.logger.error(`Error getting AI configuration: ${error.message}`);
      throw new HttpException('Failed to get AI configuration', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update AI configuration
   */
  @ApiOperation({ summary: 'Update AI configuration' })
  @ApiBody({ description: 'AI configuration' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Updated AI configuration' })
  @Put('config')
  updateAIConfig(@Body() config: Partial<GenerativeAIConfig>) {
    try {
      return this.generativeAIService.updateAIConfig(config);
    } catch (error) {
      this.logger.error(`Error updating AI configuration: ${error.message}`);
      throw new HttpException('Failed to update AI configuration', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Generate explanation
   */
  @ApiOperation({ summary: 'Generate explanation for a recommendation' })
  @ApiBody({ description: 'Explanation request' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Generated explanation' })
  @Post('explanation')
  async generateExplanation(@Body() request: ExplanationRequest) {
    try {
      // Get item from database
      const item = await this.getItem(request.itemId);
      
      if (!item) {
        throw new HttpException('Item not found', HttpStatus.NOT_FOUND);
      }
      
      // Generate explanation
      const explanation = await this.generativeAIService.generateExplanation(
        item,
        request.userId,
        request.type || ExplanationGenerationType.FEATURE_BASED,
        request.context,
      );
      
      return { explanation, item: { id: item.id, title: item.title } };
    } catch (error) {
      this.logger.error(`Error generating explanation: ${error.message}`);
      throw new HttpException('Failed to generate explanation', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Generate content
   */
  @ApiOperation({ summary: 'Generate content for an item' })
  @ApiBody({ description: 'Content generation request' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Generated content' })
  @Post('content')
  async generateContent(@Body() request: ContentGenerationRequest) {
    try {
      // Get item from database
      const item = await this.getItem(request.itemId);
      
      if (!item) {
        throw new HttpException('Item not found', HttpStatus.NOT_FOUND);
      }
      
      // Generate content
      const content = await this.generativeAIService.generateContent(
        item,
        request.userId,
        request.type || ContentGenerationType.DESCRIPTION,
        request.context,
      );
      
      return { content, item: { id: item.id, title: item.title } };
    } catch (error) {
      this.logger.error(`Error generating content: ${error.message}`);
      throw new HttpException('Failed to generate content', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get NLP recommendations
   */
  @ApiOperation({ summary: 'Get NLP-based recommendations' })
  @ApiBody({ description: 'NLP recommendation request' })
  @ApiResponse({ status: HttpStatus.OK, description: 'NLP-based recommendations' })
  @Post('nlp-recommendations')
  async getNLPRecommendations(@Body() request: NLPRecommendationRequest) {
    try {
      const recommendations = await this.generativeAIService.getNLPRecommendations(
        request.query,
        request.userId,
        request.limit || 10,
        request.context,
      );
      
      return {
        recommendations,
        query: request.query,
        count: recommendations.length,
      };
    } catch (error) {
      this.logger.error(`Error getting NLP recommendations: ${error.message}`);
      throw new HttpException('Failed to get NLP recommendations', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get item from database
   * @param itemId Item ID
   * @returns Item
   */
  private async getItem(itemId: string): Promise<any> {
    // In a real implementation, this would fetch the item from the database
    // For now, we'll return mock data
    return {
      id: itemId,
      title: 'Wellness Retreat',
      type: 'retreat',
      description: 'A relaxing wellness retreat in a beautiful natural setting.',
      categories: ['wellness', 'meditation', 'yoga'],
      tags: ['relaxation', 'mindfulness', 'nature'],
      attributes: {
        indoor: true,
        outdoor: true,
        relaxing: true,
        energizing: false,
        focusing: true,
        morning: false,
        afternoon: true,
        evening: true,
        weekend: true,
        weekday: false,
      },
      duration: '3 days',
      location: 'Mountain Resort',
      price: '$1200',
    };
  }
}
