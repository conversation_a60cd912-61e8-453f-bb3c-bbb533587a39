import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { TransparencyService } from '../services/transparency.service';
import {
  TransparencyConfig,
  ExplanationType,
  UserControlType,
  UserControlSettings,
  TransparencyReport,
} from '../interfaces/transparency.interface';

/**
 * Controller for transparency features
 */
@ApiTags('Transparency')
@Controller('recommendation/transparency')
export class TransparencyController {
  private readonly logger = new Logger(TransparencyController.name);

  constructor(private readonly transparencyService: TransparencyService) {}

  /**
   * Get transparency configuration
   */
  @ApiOperation({ summary: 'Get transparency configuration' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Transparency configuration' })
  @Get('config')
  getTransparencyConfig() {
    try {
      return this.transparencyService.getTransparencyConfig();
    } catch (error) {
      this.logger.error(`Error getting transparency configuration: ${error.message}`);
      throw new HttpException('Failed to get transparency configuration', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update transparency configuration
   */
  @ApiOperation({ summary: 'Update transparency configuration' })
  @ApiBody({ description: 'Transparency configuration' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Updated transparency configuration' })
  @Put('config')
  updateTransparencyConfig(@Body() config: Partial<TransparencyConfig>) {
    try {
      return this.transparencyService.updateTransparencyConfig(config);
    } catch (error) {
      this.logger.error(`Error updating transparency configuration: ${error.message}`);
      throw new HttpException('Failed to update transparency configuration', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get user control settings
   */
  @ApiOperation({ summary: 'Get user control settings' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'User control settings' })
  @Get('user-controls/:userId')
  getUserControlSettings(@Param('userId') userId: string) {
    try {
      return this.transparencyService.getUserControlSettings(userId);
    } catch (error) {
      this.logger.error(`Error getting user control settings: ${error.message}`);
      throw new HttpException('Failed to get user control settings', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update user control settings
   */
  @ApiOperation({ summary: 'Update user control settings' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiBody({ description: 'User control settings' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Updated user control settings' })
  @Put('user-controls/:userId')
  updateUserControlSettings(
    @Param('userId') userId: string,
    @Body() settings: Partial<UserControlSettings>,
  ) {
    try {
      return this.transparencyService.updateUserControlSettings(userId, settings);
    } catch (error) {
      this.logger.error(`Error updating user control settings: ${error.message}`);
      throw new HttpException('Failed to update user control settings', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Generate explanations for recommendations
   */
  @ApiOperation({ summary: 'Generate explanations for recommendations' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiBody({ description: 'Items to explain' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Explanations for recommendations' })
  @Post('explanations/:userId')
  async generateExplanations(
    @Param('userId') userId: string,
    @Body() body: { items: any[]; modelInfo?: any; context?: Record<string, any> },
  ) {
    try {
      const explanations = await this.transparencyService.generateExplanations(
        body.items,
        userId,
        body.modelInfo,
        body.context,
      );
      
      return explanations;
    } catch (error) {
      this.logger.error(`Error generating explanations: ${error.message}`);
      throw new HttpException('Failed to generate explanations', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Generate a transparency report
   */
  @ApiOperation({ summary: 'Generate a transparency report' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiParam({ name: 'recommendationId', description: 'Recommendation ID' })
  @ApiBody({ description: 'Report data' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Transparency report' })
  @Post('report/:userId/:recommendationId')
  async generateTransparencyReport(
    @Param('userId') userId: string,
    @Param('recommendationId') recommendationId: string,
    @Body() body: {
      items: any[];
      explanations?: Record<string, any[]>;
      modelInfo?: any;
      userSettings?: UserControlSettings;
    },
  ) {
    try {
      // Generate explanations if not provided
      let explanations = body.explanations;
      if (!explanations) {
        explanations = await this.transparencyService.generateExplanations(
          body.items,
          userId,
          body.modelInfo,
        );
      }
      
      // Generate the report
      const report = await this.transparencyService.generateTransparencyReport(
        userId,
        recommendationId,
        body.items,
        explanations,
        body.modelInfo,
        body.userSettings,
      );
      
      return report;
    } catch (error) {
      this.logger.error(`Error generating transparency report: ${error.message}`);
      throw new HttpException('Failed to generate transparency report', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
