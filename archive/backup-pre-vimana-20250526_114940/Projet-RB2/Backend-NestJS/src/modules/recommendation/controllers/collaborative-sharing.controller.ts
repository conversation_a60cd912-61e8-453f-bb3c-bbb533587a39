import { Controller, Get, Post, Put, Body, Param, Query, UseGuards, Logger, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CollaborativeSharingService, SharedRecommendationStatus } from '../services/collaborative-sharing.service';
import { RecommendationType } from '../enums/recommendation-type.enum';

/**
 * Contrôleur pour le partage collaboratif de recommandations
 */
@ApiTags('collaborative-sharing')
@Controller('collaborative-sharing')
export class CollaborativeSharingController {
  private readonly logger = new Logger(CollaborativeSharingController.name);

  constructor(private readonly collaborativeSharingService: CollaborativeSharingService) {}

  /**
   * Partage une recommandation avec un autre utilisateur
   * @param req Requête avec les informations de l'utilisateur
   * @param data Données du partage
   * @returns Recommandation partagée
   */
  @Post('share')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Partager une recommandation avec un autre utilisateur' })
  @ApiResponse({ status: 201, description: 'Recommandation partagée' })
  async shareRecommendation(
    @Req() req,
    @Body() data: {
      toUserId: string;
      itemId: string;
      itemType: RecommendationType;
      message?: string;
    },
  ) {
    const fromUserId = req.user.id;
    const { toUserId, itemId, itemType, message } = data;
    
    this.logger.log(`Partage d'une recommandation de ${fromUserId} à ${toUserId}`);
    
    return this.collaborativeSharingService.shareRecommendation(
      fromUserId,
      toUserId,
      itemId,
      itemType,
      message,
    );
  }

  /**
   * Récupère les recommandations partagées avec l'utilisateur
   * @param req Requête avec les informations de l'utilisateur
   * @param status Statut des recommandations à récupérer
   * @param limit Nombre maximum de recommandations à récupérer
   * @returns Liste des recommandations partagées
   */
  @Get('received')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les recommandations partagées avec l\'utilisateur' })
  @ApiResponse({ status: 200, description: 'Liste des recommandations partagées' })
  async getSharedRecommendations(
    @Req() req,
    @Query('status') status?: SharedRecommendationStatus,
    @Query('limit') limit?: number,
  ) {
    const userId = req.user.id;
    
    this.logger.log(`Récupération des recommandations partagées avec l'utilisateur ${userId}`);
    
    return this.collaborativeSharingService.getSharedRecommendations(
      userId,
      status,
      limit ? parseInt(limit.toString(), 10) : undefined,
    );
  }

  /**
   * Récupère les recommandations partagées par l'utilisateur
   * @param req Requête avec les informations de l'utilisateur
   * @param limit Nombre maximum de recommandations à récupérer
   * @returns Liste des recommandations partagées
   */
  @Get('sent')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les recommandations partagées par l\'utilisateur' })
  @ApiResponse({ status: 200, description: 'Liste des recommandations partagées' })
  async getSharedByUserRecommendations(
    @Req() req,
    @Query('limit') limit?: number,
  ) {
    const userId = req.user.id;
    
    this.logger.log(`Récupération des recommandations partagées par l'utilisateur ${userId}`);
    
    return this.collaborativeSharingService.getSharedByUserRecommendations(
      userId,
      limit ? parseInt(limit.toString(), 10) : undefined,
    );
  }

  /**
   * Met à jour le statut d'une recommandation partagée
   * @param req Requête avec les informations de l'utilisateur
   * @param id ID de la recommandation partagée
   * @param data Données de mise à jour
   * @returns Recommandation partagée mise à jour
   */
  @Put(':id/status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour le statut d\'une recommandation partagée' })
  @ApiResponse({ status: 200, description: 'Recommandation partagée mise à jour' })
  async updateSharedRecommendationStatus(
    @Req() req,
    @Param('id') id: string,
    @Body() data: {
      status: SharedRecommendationStatus;
    },
  ) {
    const userId = req.user.id;
    const { status } = data;
    
    this.logger.log(`Mise à jour du statut de la recommandation partagée ${id} à ${status}`);
    
    return this.collaborativeSharingService.updateSharedRecommendationStatus(
      id,
      userId,
      status,
    );
  }
}
