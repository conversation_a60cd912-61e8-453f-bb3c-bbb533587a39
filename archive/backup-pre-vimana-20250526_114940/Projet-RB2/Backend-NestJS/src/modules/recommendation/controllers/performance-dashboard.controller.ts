import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Query,
  Body,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { PerformanceMonitoringService } from '../services/performance-monitoring.service';
import {
  MetricCategory,
  AlertSeverity,
  PerformanceDataQuery,
} from '../interfaces/performance-monitoring.interface';

/**
 * Controller for the performance dashboard
 */
@ApiTags('Performance Dashboard')
@Controller('recommendation/performance')
export class PerformanceDashboardController {
  private readonly logger = new Logger(PerformanceDashboardController.name);

  constructor(private readonly performanceService: PerformanceMonitoringService) {}

  /**
   * Get all metrics
   */
  @ApiOperation({ summary: 'Get all metrics' })
  @ApiQuery({ name: 'category', required: false, enum: MetricCategory })
  @ApiResponse({ status: HttpStatus.OK, description: 'List of metrics' })
  @Get('metrics')
  getMetrics(@Query('category') category?: MetricCategory) {
    try {
      return this.performanceService.getMetrics(category);
    } catch (error) {
      this.logger.error(`Error getting metrics: ${error.message}`);
      throw new HttpException('Failed to get metrics', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get a specific metric
   */
  @ApiOperation({ summary: 'Get a specific metric' })
  @ApiParam({ name: 'id', description: 'Metric ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Metric details' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Metric not found' })
  @Get('metrics/:id')
  getMetric(@Param('id') id: string) {
    try {
      const metric = this.performanceService.getMetric(id);
      if (!metric) {
        throw new HttpException('Metric not found', HttpStatus.NOT_FOUND);
      }
      return metric;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Error getting metric ${id}: ${error.message}`);
      throw new HttpException('Failed to get metric', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get metric values
   */
  @ApiOperation({ summary: 'Get metric values' })
  @ApiParam({ name: 'id', description: 'Metric ID' })
  @ApiQuery({ name: 'startDate', required: false, description: 'Start date (ISO format)' })
  @ApiQuery({ name: 'endDate', required: false, description: 'End date (ISO format)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Maximum number of values to return' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Metric values' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Metric not found' })
  @Get('metrics/:id/values')
  getMetricValues(
    @Param('id') id: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('limit') limit?: number,
  ) {
    try {
      const startDateObj = startDate ? new Date(startDate) : undefined;
      const endDateObj = endDate ? new Date(endDate) : undefined;

      return this.performanceService.getMetricValues(id, startDateObj, endDateObj, limit);
    } catch (error) {
      this.logger.error(`Error getting metric values for ${id}: ${error.message}`);
      throw new HttpException('Failed to get metric values', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get all alerts
   */
  @ApiOperation({ summary: 'Get all alerts' })
  @ApiQuery({ name: 'metricId', required: false, description: 'Filter by metric ID' })
  @ApiQuery({ name: 'severity', required: false, enum: AlertSeverity })
  @ApiQuery({ name: 'resolved', required: false, description: 'Filter by resolved status' })
  @ApiQuery({ name: 'startDate', required: false, description: 'Start date (ISO format)' })
  @ApiQuery({ name: 'endDate', required: false, description: 'End date (ISO format)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Maximum number of alerts to return' })
  @ApiResponse({ status: HttpStatus.OK, description: 'List of alerts' })
  @Get('alerts')
  getAlerts(
    @Query('metricId') metricId?: string,
    @Query('severity') severity?: AlertSeverity,
    @Query('resolved') resolved?: boolean,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('limit') limit?: number,
  ) {
    try {
      const startDateObj = startDate ? new Date(startDate) : undefined;
      const endDateObj = endDate ? new Date(endDate) : undefined;
      const resolvedBool = resolved !== undefined ? resolved === true || resolved === 'true' : undefined;

      return this.performanceService.getAlerts(metricId, severity, resolvedBool, startDateObj, endDateObj, limit);
    } catch (error) {
      this.logger.error(`Error getting alerts: ${error.message}`);
      throw new HttpException('Failed to get alerts', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get a specific alert
   */
  @ApiOperation({ summary: 'Get a specific alert' })
  @ApiParam({ name: 'id', description: 'Alert ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Alert details' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Alert not found' })
  @Get('alerts/:id')
  getAlert(@Param('id') id: string) {
    try {
      const alert = this.performanceService.getAlert(id);
      if (!alert) {
        throw new HttpException('Alert not found', HttpStatus.NOT_FOUND);
      }
      return alert;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Error getting alert ${id}: ${error.message}`);
      throw new HttpException('Failed to get alert', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Acknowledge an alert
   */
  @ApiOperation({ summary: 'Acknowledge an alert' })
  @ApiParam({ name: 'id', description: 'Alert ID' })
  @ApiBody({ description: 'Acknowledgement details', required: false })
  @ApiResponse({ status: HttpStatus.OK, description: 'Alert acknowledged' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Alert not found' })
  @Put('alerts/:id/acknowledge')
  acknowledgeAlert(@Param('id') id: string, @Body() body: { acknowledgedBy?: string }) {
    try {
      const alert = this.performanceService.acknowledgeAlert(id, body?.acknowledgedBy);
      if (!alert) {
        throw new HttpException('Alert not found', HttpStatus.NOT_FOUND);
      }
      return alert;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Error acknowledging alert ${id}: ${error.message}`);
      throw new HttpException('Failed to acknowledge alert', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Resolve an alert
   */
  @ApiOperation({ summary: 'Resolve an alert' })
  @ApiParam({ name: 'id', description: 'Alert ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Alert resolved' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Alert not found' })
  @Put('alerts/:id/resolve')
  resolveAlert(@Param('id') id: string) {
    try {
      const alert = this.performanceService.resolveAlert(id);
      if (!alert) {
        throw new HttpException('Alert not found', HttpStatus.NOT_FOUND);
      }
      return alert;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Error resolving alert ${id}: ${error.message}`);
      throw new HttpException('Failed to resolve alert', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Generate a performance report
   */
  @ApiOperation({ summary: 'Generate a performance report' })
  @ApiBody({ description: 'Report configuration' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Generated report' })
  @Post('reports')
  generateReport(@Body() body: {
    name: string;
    description?: string;
    startDate: string;
    endDate: string;
    metricIds?: string[];
  }) {
    try {
      const startDate = new Date(body.startDate);
      const endDate = new Date(body.endDate);

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        throw new HttpException('Invalid date format', HttpStatus.BAD_REQUEST);
      }

      if (startDate > endDate) {
        throw new HttpException('Start date must be before end date', HttpStatus.BAD_REQUEST);
      }

      return this.performanceService.generateReport(
        body.name,
        body.description || '',
        startDate,
        endDate,
        body.metricIds,
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Error generating report: ${error.message}`);
      throw new HttpException('Failed to generate report', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Record a metric value
   */
  @ApiOperation({ summary: 'Record a metric value' })
  @ApiParam({ name: 'id', description: 'Metric ID' })
  @ApiBody({ description: 'Metric value details' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Metric value recorded' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Metric not found' })
  @Post('metrics/:id/values')
  recordMetricValue(
    @Param('id') id: string,
    @Body() body: {
      value: number;
      timestamp?: string;
      metadata?: Record<string, any>;
    },
  ) {
    try {
      const timestamp = body.timestamp ? new Date(body.timestamp) : new Date();

      if (isNaN(timestamp.getTime())) {
        throw new HttpException('Invalid timestamp format', HttpStatus.BAD_REQUEST);
      }

      const result = this.performanceService.recordMetricValue(id, body.value, timestamp, body.metadata);

      if (!result) {
        throw new HttpException('Metric not found', HttpStatus.NOT_FOUND);
      }

      return { success: true, metricId: id, value: body.value, timestamp };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Error recording metric value for ${id}: ${error.message}`);
      throw new HttpException('Failed to record metric value', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Query performance data
   */
  @ApiOperation({ summary: 'Query performance data' })
  @ApiBody({ description: 'Query parameters' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Query results' })
  @Post('query')
  queryPerformanceData(@Body() query: PerformanceDataQuery) {
    try {
      // Convert string dates to Date objects
      if (query.startDate && typeof query.startDate === 'string') {
        query.startDate = new Date(query.startDate);
      }

      if (query.endDate && typeof query.endDate === 'string') {
        query.endDate = new Date(query.endDate);
      }

      // Get metrics based on query
      let metrics = this.performanceService.getMetrics();

      // Filter by categories if specified
      if (query.categories && query.categories.length > 0) {
        metrics = metrics.filter(metric => query.categories.includes(metric.category));
      }

      // Filter by specific metrics if specified
      if (query.metrics && query.metrics.length > 0) {
        metrics = metrics.filter(metric => query.metrics.includes(metric.id));
      }

      // Get values for each metric
      const results = metrics.map(metric => {
        const values = this.performanceService.getMetricValues(
          metric.id,
          query.startDate as Date,
          query.endDate as Date,
          query.limit,
        );

        return {
          metricId: metric.id,
          metricName: metric.name,
          category: metric.category,
          unit: metric.unit,
          values,
        };
      });

      return results;
    } catch (error) {
      this.logger.error(`Error querying performance data: ${error.message}`);
      throw new HttpException('Failed to query performance data', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}