import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  ParseEnumPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../../../common/decorators';
import { ParseObjectIdPipe } from '../../../common/pipes/parse-object-id.pipe';
import { ExternalDataService } from '../services/external-data.service';
import { ExternalDataType, ExternalDataOptions } from '../interfaces/external-data.interface';
import { RecommendationType } from '../enums/recommendation-type.enum';

@ApiTags('external-data')
@Controller('external-data')
export class ExternalDataController {
  constructor(private readonly externalDataService: ExternalDataService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer des données externes' })
  @ApiQuery({ name: 'type', enum: RecommendationType, required: false })
  @ApiQuery({ name: 'includeTypes', enum: ExternalDataType, isArray: true, required: false })
  @ApiQuery({ name: 'limit', type: Number, required: false })
  @ApiResponse({ status: 200, description: 'Données externes récupérées avec succès.' })
  async getExternalData(
    @CurrentUser('id') userId: string,
    @Query('type') type?: RecommendationType,
    @Query('includeTypes') includeTypes?: ExternalDataType[],
    @Query('limit') limit?: number,
  ) {
    const options: ExternalDataOptions = {
      userId,
      recommendationType: type,
      includeTypes,
      limit: limit ? parseInt(limit.toString(), 10) : undefined,
    };

    return this.externalDataService.getExternalDataWithOptions(options);
  }

  @Get('location')
  @ApiOperation({ summary: 'Récupérer des données externes basées sur la localisation' })
  @ApiQuery({ name: 'location', type: String, required: false, description: 'Nom de la localisation (ville, pays)' })
  @ApiQuery({ name: 'latitude', type: Number, required: false })
  @ApiQuery({ name: 'longitude', type: Number, required: false })
  @ApiQuery({ name: 'includeTypes', enum: ExternalDataType, isArray: true, required: false })
  @ApiQuery({ name: 'limit', type: Number, required: false })
  @ApiResponse({ status: 200, description: 'Données externes basées sur la localisation récupérées avec succès.' })
  async getLocationBasedData(
    @Query('location') location?: string,
    @Query('latitude') latitude?: number,
    @Query('longitude') longitude?: number,
    @Query('includeTypes') includeTypes?: ExternalDataType[],
    @Query('limit') limit?: number,
  ) {
    // Vérifier si on a une localisation
    if (!location && (!latitude || !longitude)) {
      return [];
    }

    const options: Partial<ExternalDataOptions> = {
      includeTypes,
      limit: limit ? parseInt(limit.toString(), 10) : undefined,
    };

    // Utiliser soit le nom de la localisation, soit les coordonnées
    const locationParam = location || { latitude, longitude };

    return this.externalDataService.getLocationBasedData(locationParam, options);
  }

  @Post('refresh')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Rafraîchir les données externes' })
  @ApiResponse({ status: 200, description: 'Données externes rafraîchies avec succès.' })
  async refreshExternalData() {
    await this.externalDataService.refreshExternalData();
    return { success: true, message: 'Données externes rafraîchies avec succès' };
  }

  @Get('sources')
  @ApiOperation({ summary: 'Récupérer les sources de données externes disponibles' })
  @ApiResponse({ status: 200, description: 'Sources de données externes récupérées avec succès.' })
  async getExternalDataSources() {
    // Cette méthode n'existe pas encore dans le service, il faudrait l'ajouter
    // Pour l'instant, on retourne les types de données externes
    return Object.values(ExternalDataType).map(type => ({
      type,
      name: this.getExternalDataTypeName(type),
      description: this.getExternalDataTypeDescription(type),
    }));
  }

  /**
   * Obtient le nom d'un type de données externes
   * @param type Type de données externes
   * @returns Nom du type
   */
  private getExternalDataTypeName(type: ExternalDataType): string {
    switch (type) {
      case ExternalDataType.TREND:
        return 'Tendances';
      case ExternalDataType.NEWS:
        return 'Actualités';
      case ExternalDataType.WEATHER:
        return 'Météo';
      case ExternalDataType.EVENT:
        return 'Événements';
      case ExternalDataType.TRANSPORT:
        return 'Transport';
      case ExternalDataType.HEALTH:
        return 'Santé';
      case ExternalDataType.ENVIRONMENT:
        return 'Environnement';
      default:
        return type;
    }
  }

  /**
   * Obtient la description d'un type de données externes
   * @param type Type de données externes
   * @returns Description du type
   */
  private getExternalDataTypeDescription(type: ExternalDataType): string {
    switch (type) {
      case ExternalDataType.TREND:
        return 'Tendances et sujets populaires';
      case ExternalDataType.NEWS:
        return 'Actualités et articles récents';
      case ExternalDataType.WEATHER:
        return 'Données météorologiques';
      case ExternalDataType.EVENT:
        return 'Événements locaux et manifestations';
      case ExternalDataType.TRANSPORT:
        return 'Options de transport et itinéraires';
      case ExternalDataType.HEALTH:
        return 'Informations sur la santé et le bien-être';
      case ExternalDataType.ENVIRONMENT:
        return 'Données environnementales';
      default:
        return 'Données externes';
    }
  }
}
