import {
  Controller,
  Get,
  Put,
  Post,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { PerformanceOptimizationService } from '../services/performance-optimization.service';
import {
  CacheConfig,
  CacheStrategy,
  CacheInvalidationStrategy,
  CacheKeyStrategy,
  DatabaseOptimizationConfig,
  QueryOptimizationStrategy,
  IndexOptimizationStrategy,
  DataPartitioningStrategy,
} from '../interfaces/performance-optimization.interface';

/**
 * Controller for performance optimization features
 */
@ApiTags('Performance Optimization')
@Controller('recommendation/performance')
export class PerformanceOptimizationController {
  private readonly logger = new Logger(PerformanceOptimizationController.name);

  constructor(private readonly performanceOptimizationService: PerformanceOptimizationService) {}

  /**
   * Get cache configuration
   */
  @ApiOperation({ summary: 'Get cache configuration' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Cache configuration' })
  @Get('cache/config')
  getCacheConfig() {
    try {
      return this.performanceOptimizationService.getCacheConfig();
    } catch (error) {
      this.logger.error(`Error getting cache configuration: ${error.message}`);
      throw new HttpException('Failed to get cache configuration', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update cache configuration
   */
  @ApiOperation({ summary: 'Update cache configuration' })
  @ApiBody({ description: 'Cache configuration' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Updated cache configuration' })
  @Put('cache/config')
  updateCacheConfig(@Body() config: Partial<CacheConfig>) {
    try {
      return this.performanceOptimizationService.updateCacheConfig(config);
    } catch (error) {
      this.logger.error(`Error updating cache configuration: ${error.message}`);
      throw new HttpException('Failed to update cache configuration', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get database optimization configuration
   */
  @ApiOperation({ summary: 'Get database optimization configuration' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Database optimization configuration' })
  @Get('database/config')
  getDatabaseOptimizationConfig() {
    try {
      return this.performanceOptimizationService.getDatabaseOptimizationConfig();
    } catch (error) {
      this.logger.error(`Error getting database optimization configuration: ${error.message}`);
      throw new HttpException('Failed to get database optimization configuration', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update database optimization configuration
   */
  @ApiOperation({ summary: 'Update database optimization configuration' })
  @ApiBody({ description: 'Database optimization configuration' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Updated database optimization configuration' })
  @Put('database/config')
  updateDatabaseOptimizationConfig(@Body() config: Partial<DatabaseOptimizationConfig>) {
    try {
      return this.performanceOptimizationService.updateDatabaseOptimizationConfig(config);
    } catch (error) {
      this.logger.error(`Error updating database optimization configuration: ${error.message}`);
      throw new HttpException('Failed to update database optimization configuration', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Invalidate cache
   */
  @ApiOperation({ summary: 'Invalidate cache' })
  @ApiParam({ name: 'key', description: 'Cache key (optional)', required: false })
  @ApiResponse({ status: HttpStatus.OK, description: 'Cache invalidated' })
  @Delete('cache/:key?')
  invalidateCache(@Param('key') key?: string) {
    try {
      this.performanceOptimizationService.invalidateCache(key);
      return { success: true, message: key ? `Cache invalidated for key: ${key}` : 'All cache invalidated' };
    } catch (error) {
      this.logger.error(`Error invalidating cache: ${error.message}`);
      throw new HttpException('Failed to invalidate cache', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Generate cache key
   */
  @ApiOperation({ summary: 'Generate cache key' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiQuery({ name: 'params', description: 'Request parameters (JSON string)', required: false })
  @ApiResponse({ status: HttpStatus.OK, description: 'Generated cache key' })
  @Get('cache/key/:userId')
  generateCacheKey(@Param('userId') userId: string, @Query('params') paramsStr?: string) {
    try {
      const params = paramsStr ? JSON.parse(paramsStr) : undefined;
      const key = this.performanceOptimizationService.generateCacheKey(userId, params);
      return { key };
    } catch (error) {
      this.logger.error(`Error generating cache key: ${error.message}`);
      throw new HttpException('Failed to generate cache key', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Process batch
   */
  @ApiOperation({ summary: 'Process items in batch' })
  @ApiBody({ description: 'Items to process' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Processed items' })
  @Post('batch')
  async processBatch(@Body() body: { items: any[] }) {
    try {
      const result = await this.performanceOptimizationService.processBatch(
        body.items,
        async (batch) => {
          // Mock processing function
          return batch.map(item => ({ ...item, processed: true }));
        },
      );
      
      return {
        originalCount: body.items.length,
        processedCount: result.length,
        items: result,
      };
    } catch (error) {
      this.logger.error(`Error processing batch: ${error.message}`);
      throw new HttpException('Failed to process batch', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
