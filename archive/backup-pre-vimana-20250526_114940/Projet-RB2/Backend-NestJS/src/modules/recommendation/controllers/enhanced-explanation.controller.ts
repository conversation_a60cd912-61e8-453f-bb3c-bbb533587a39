import { Controller, Post, Body, UseGuards, Req, HttpException, HttpStatus, Lo<PERSON>, Get, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { EnhancedExplanationService } from '../services/enhanced-explanation.service';
import { EnhancedExplanationRequestDto, EnhancedExplanationDto } from '../dto/enhanced-explanation.dto';
import { RecommendationType } from '../enums/recommendation-type.enum';

interface RequestWithUser {
  user: {
    id: string;
    roles: string[];
  };
}

/**
 * Contrôleur pour les explications améliorées des recommandations
 */
@ApiTags('recommendation-explanations')
@Controller('recommendations/explanations')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class EnhancedExplanationController {
  private readonly logger = new Logger(EnhancedExplanationController.name);

  constructor(private readonly explanationService: EnhancedExplanationService) {}

  /**
   * Obtenir une explication améliorée pour une recommandation
   */
  @Post()
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Obtenir une explication améliorée pour une recommandation' })
  @ApiResponse({ status: 200, description: 'Explication générée avec succès', type: EnhancedExplanationDto })
  @ApiResponse({ status: 400, description: 'Données de requête invalides' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 404, description: 'Recommandation non trouvée' })
  async getEnhancedExplanation(
    @Body() requestDto: EnhancedExplanationRequestDto,
    @Req() req: RequestWithUser,
  ) {
    try {
      this.logger.log(`Demande d'explication pour la recommandation ${requestDto.recommendationId} de type ${requestDto.recommendationType}`);

      const explanation = await this.explanationService.generateEnhancedExplanation(
        requestDto.recommendationId,
        requestDto.recommendationType,
        req.user.id,
        {
          includeVisualizations: requestDto.includeVisualizations,
          detailLevel: requestDto.detailLevel,
          language: requestDto.language,
        },
      );

      return {
        success: true,
        data: explanation,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la génération de l'explication: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de la génération de l\'explication',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Obtenir une explication améliorée pour une recommandation (méthode GET)
   */
  @Get(':type/:id')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Obtenir une explication améliorée pour une recommandation' })
  @ApiParam({ name: 'type', description: 'Type de recommandation', enum: RecommendationType })
  @ApiParam({ name: 'id', description: 'ID de la recommandation' })
  @ApiQuery({ name: 'includeVisualizations', required: false, type: Boolean, description: 'Inclure des visualisations dans l\'explication' })
  @ApiQuery({ name: 'detailLevel', required: false, enum: ['BASIC', 'STANDARD', 'DETAILED'], description: 'Niveau de détail de l\'explication' })
  @ApiQuery({ name: 'language', required: false, description: 'Langue de l\'explication (code ISO)' })
  @ApiResponse({ status: 200, description: 'Explication générée avec succès', type: EnhancedExplanationDto })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 404, description: 'Recommandation non trouvée' })
  async getEnhancedExplanationGet(
    @Param('type') type: RecommendationType,
    @Param('id') id: string,
    @Query('includeVisualizations') includeVisualizations?: boolean,
    @Query('detailLevel') detailLevel?: 'BASIC' | 'STANDARD' | 'DETAILED',
    @Query('language') language?: string,
    @Req() req: RequestWithUser,
  ) {
    try {
      this.logger.log(`Demande d'explication pour la recommandation ${id} de type ${type}`);

      const explanation = await this.explanationService.generateEnhancedExplanation(
        id,
        type,
        req.user.id,
        {
          includeVisualizations,
          detailLevel,
          language,
        },
      );

      return {
        success: true,
        data: explanation,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la génération de l'explication: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de la génération de l\'explication',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
