import {
  <PERSON>,
  Get,
  Param,
  Query,
  UseGuards,
  Request,
  Post,
  Body,
  HttpStatus,
  HttpException,
  Logger,
  Put,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, Api<PERSON><PERSON>erAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { SocialVideoRecommendationService } from '../services/social-video-recommendation.service';
import { RecommendationOptions } from '../interfaces/recommendation-options.interface';
import { SocialVideoPreferencesDto } from '../dto/social-video-preferences.dto';
import { SocialVideoInteractionDto, SocialVideoContentType, SocialVideoInteractionType } from '../dto/social-video-interaction.dto';

@ApiTags('social-video-recommendations')
@Controller('recommendations/social-video')
export class SocialVideoRecommendationController {
  private readonly logger = new Logger(SocialVideoRecommendationController.name);

  constructor(
    private readonly socialVideoRecommendationService: SocialVideoRecommendationService,
  ) {}

  @Get('for-you')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir des recommandations de vidéos personnalisées' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre maximum de recommandations à retourner' })
  @ApiQuery({ name: 'cursor', required: false, type: String, description: 'Curseur pour la pagination' })
  @ApiResponse({ status: 200, description: 'Recommandations récupérées avec succès' })
  async getPersonalizedRecommendations(
    @Request() req,
    @Query('limit') limit?: number,
    @Query('cursor') cursor?: string,
  ) {
    try {
      this.logger.log(`Récupération des recommandations personnalisées pour l'utilisateur ${req.user.id}`);

      const options: RecommendationOptions = {
        limit: limit ? parseInt(limit.toString(), 10) : 10,
        cursor,
      };

      const recommendations = await this.socialVideoRecommendationService.getVideoRecommendations(
        req.user.id,
        options,
      );

      return {
        recommendations,
        nextCursor: recommendations.length === options.limit ? recommendations[recommendations.length - 1].id : null,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des recommandations personnalisées: ${error.message}`);
      throw new HttpException(
        'Erreur lors de la récupération des recommandations',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('trending')
  @ApiOperation({ summary: 'Obtenir des recommandations de vidéos tendance' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre maximum de recommandations à retourner' })
  @ApiQuery({ name: 'cursor', required: false, type: String, description: 'Curseur pour la pagination' })
  @ApiResponse({ status: 200, description: 'Recommandations récupérées avec succès' })
  async getTrendingRecommendations(
    @Query('limit') limit?: number,
    @Query('cursor') cursor?: string,
  ) {
    try {
      this.logger.log('Récupération des recommandations de vidéos tendance');

      const options: RecommendationOptions = {
        limit: limit ? parseInt(limit.toString(), 10) : 10,
        cursor,
      };

      const recommendations = await this.socialVideoRecommendationService.getTrendingVideos(options);

      return {
        recommendations,
        nextCursor: recommendations.length === options.limit ? recommendations[recommendations.length - 1].id : null,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des recommandations tendance: ${error.message}`);
      throw new HttpException(
        'Erreur lors de la récupération des recommandations tendance',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('similar')
  @ApiOperation({ summary: 'Obtenir des recommandations de vidéos similaires' })
  @ApiQuery({ name: 'contentId', required: true, type: String, description: 'ID du contenu' })
  @ApiQuery({ name: 'contentType', required: true, enum: ['video', 'post', 'livestream'], description: 'Type de contenu' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre maximum de recommandations à retourner' })
  @ApiQuery({ name: 'cursor', required: false, type: String, description: 'Curseur pour la pagination' })
  @ApiResponse({ status: 200, description: 'Recommandations récupérées avec succès' })
  async getSimilarContent(
    @Query('contentId') contentId: string,
    @Query('contentType') contentType: 'video' | 'post' | 'livestream',
    @Query('limit') limit?: number,
    @Query('cursor') cursor?: string,
  ) {
    try {
      this.logger.log(`Récupération des recommandations similaires pour ${contentType} ${contentId}`);

      if (!contentId || !contentType) {
        throw new HttpException(
          'contentId et contentType sont requis',
          HttpStatus.BAD_REQUEST,
        );
      }

      if (contentType !== 'video' && contentType !== 'post' && contentType !== 'livestream') {
        throw new HttpException(
          'contentType doit être video, post ou livestream',
          HttpStatus.BAD_REQUEST,
        );
      }

      const options: RecommendationOptions = {
        limit: limit ? parseInt(limit.toString(), 10) : 10,
        cursor,
      };

      let recommendations = [];

      if (contentType === 'video') {
        recommendations = await this.socialVideoRecommendationService.getSimilarVideos(contentId, options);
      } else {
        // Pour les autres types de contenu, nous pourrions implémenter des méthodes spécifiques
        throw new HttpException(
          `Les recommandations pour le type ${contentType} ne sont pas encore implémentées`,
          HttpStatus.NOT_IMPLEMENTED,
        );
      }

      return {
        recommendations,
        nextCursor: recommendations.length === options.limit ? recommendations[recommendations.length - 1].id : null,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des recommandations similaires: ${error.message}`);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Erreur lors de la récupération des recommandations similaires',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('feedback')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Fournir un feedback sur une recommandation' })
  @ApiResponse({ status: 200, description: 'Feedback enregistré avec succès' })
  async provideRecommendationFeedback(
    @Request() req,
    @Body() body: { recommendationId: string; feedback: 'like' | 'dislike' | 'not_interested' | 'hide_creator' | 'report' },
  ) {
    try {
      this.logger.log(`Enregistrement du feedback pour la recommandation ${body.recommendationId}`);

      if (!body.recommendationId || !body.feedback) {
        throw new HttpException(
          'recommendationId et feedback sont requis',
          HttpStatus.BAD_REQUEST,
        );
      }

      const validFeedbacks = ['like', 'dislike', 'not_interested', 'hide_creator', 'report'];
      if (!validFeedbacks.includes(body.feedback)) {
        throw new HttpException(
          `feedback doit être l'un des suivants: ${validFeedbacks.join(', ')}`,
          HttpStatus.BAD_REQUEST,
        );
      }

      // Ici, nous pourrions implémenter l'enregistrement du feedback
      // Pour l'instant, nous simulons une réponse réussie

      return {
        success: true,
        message: 'Feedback enregistré avec succès',
      };
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement du feedback: ${error.message}`);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Erreur lors de l\'enregistrement du feedback',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('interactions')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Enregistrer une interaction avec un contenu' })
  @ApiResponse({ status: 201, description: 'Interaction enregistrée avec succès' })
  async recordInteraction(
    @Request() req,
    @Body() interactionDto: SocialVideoInteractionDto,
  ) {
    try {
      this.logger.log(`Enregistrement d'une interaction ${interactionDto.interactionType} pour l'utilisateur ${req.user.id}`);

      // Ici, nous pourrions implémenter l'enregistrement de l'interaction
      // Pour l'instant, nous simulons une réponse réussie

      return {
        success: true,
        message: 'Interaction enregistrée avec succès',
        interaction: {
          userId: req.user.id,
          contentId: interactionDto.contentId,
          contentType: interactionDto.contentType,
          interactionType: interactionDto.interactionType,
          metadata: interactionDto.metadata || {},
          timestamp: new Date(),
        },
      };
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de l'interaction: ${error.message}`);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Erreur lors de l\'enregistrement de l\'interaction',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('preferences')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les préférences de recommandation de l\'utilisateur' })
  @ApiResponse({ status: 200, description: 'Préférences récupérées avec succès' })
  async getPreferences(@Request() req) {
    try {
      this.logger.log(`Récupération des préférences pour l'utilisateur ${req.user.id}`);

      // Ici, nous pourrions implémenter la récupération des préférences
      // Pour l'instant, nous simulons une réponse avec des préférences par défaut

      return {
        contentTypes: ['video', 'post'],
        categories: ['yoga', 'meditation', 'wellness'],
        excludedTags: [],
        excludedCreators: [],
        preferredLanguages: ['fr', 'en'],
        preferredDuration: 'medium',
        preferNewContent: true,
        preferFollowingContent: true,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des préférences: ${error.message}`);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Erreur lors de la récupération des préférences',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('preferences')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour les préférences de recommandation de l\'utilisateur' })
  @ApiResponse({ status: 200, description: 'Préférences mises à jour avec succès' })
  async updatePreferences(
    @Request() req,
    @Body() preferencesDto: SocialVideoPreferencesDto,
  ) {
    try {
      this.logger.log(`Mise à jour des préférences pour l'utilisateur ${req.user.id}`);

      // Ici, nous pourrions implémenter la mise à jour des préférences
      // Pour l'instant, nous simulons une réponse réussie

      return {
        success: true,
        message: 'Préférences mises à jour avec succès',
        preferences: {
          ...preferencesDto,
          updatedAt: new Date(),
        },
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour des préférences: ${error.message}`);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Erreur lors de la mise à jour des préférences',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
