import {
  Controller,
  Get,
  Post,
  Body,
  HttpStatus,
  HttpException,
  Logger,
  Res,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { Response } from 'express';
import { TestingDocumentationService } from '../services/testing-documentation.service';

/**
 * Controller for testing and documentation
 */
@ApiTags('Testing and Documentation')
@Controller('recommendation/testing-docs')
export class TestingDocumentationController {
  private readonly logger = new Logger(TestingDocumentationController.name);

  constructor(private readonly testingDocumentationService: TestingDocumentationService) {}

  /**
   * Run all tests
   */
  @ApiOperation({ summary: 'Run all tests' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Test results' })
  @Post('run-tests')
  async runAllTests() {
    try {
      const results = await this.testingDocumentationService.runAllTests();
      return results;
    } catch (error) {
      this.logger.error(`Error running tests: ${error.message}`);
      throw new HttpException('Failed to run tests', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Generate all documentation
   */
  @ApiOperation({ summary: 'Generate all documentation' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Documentation generation result' })
  @Post('generate-docs')
  async generateAllDocumentation() {
    try {
      const documentation = await this.testingDocumentationService.generateAllDocumentation();
      return documentation;
    } catch (error) {
      this.logger.error(`Error generating documentation: ${error.message}`);
      throw new HttpException('Failed to generate documentation', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get test results summary
   */
  @ApiOperation({ summary: 'Get test results summary' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Test results summary' })
  @Get('test-results/summary')
  async getTestResultsSummary() {
    try {
      // Run tests first
      const results = await this.testingDocumentationService.runAllTests();
      
      // Return only the summary
      return {
        summary: results.summary,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`Error getting test results summary: ${error.message}`);
      throw new HttpException('Failed to get test results summary', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get service documentation
   */
  @ApiOperation({ summary: 'Get service documentation' })
  @ApiBody({ description: 'Service name' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Service documentation' })
  @Post('docs/service')
  async getServiceDocumentation(@Body() body: { service: string }) {
    try {
      // Generate all documentation first
      const documentation = await this.testingDocumentationService.generateAllDocumentation();
      
      // Return only the requested service documentation
      const serviceDoc = documentation.services[body.service];
      
      if (!serviceDoc) {
        throw new HttpException(`Documentation for service ${body.service} not found`, HttpStatus.NOT_FOUND);
      }
      
      return serviceDoc;
    } catch (error) {
      this.logger.error(`Error getting service documentation: ${error.message}`);
      throw new HttpException('Failed to get service documentation', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get API documentation
   */
  @ApiOperation({ summary: 'Get API documentation' })
  @ApiResponse({ status: HttpStatus.OK, description: 'API documentation' })
  @Get('docs/api')
  async getAPIDocumentation() {
    try {
      // Generate all documentation first
      const documentation = await this.testingDocumentationService.generateAllDocumentation();
      
      // Return only the API documentation
      return documentation.api;
    } catch (error) {
      this.logger.error(`Error getting API documentation: ${error.message}`);
      throw new HttpException('Failed to get API documentation', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get system overview documentation
   */
  @ApiOperation({ summary: 'Get system overview documentation' })
  @ApiResponse({ status: HttpStatus.OK, description: 'System overview documentation' })
  @Get('docs/overview')
  async getSystemOverviewDocumentation() {
    try {
      // Generate all documentation first
      const documentation = await this.testingDocumentationService.generateAllDocumentation();
      
      // Return only the system overview documentation
      return documentation.overview;
    } catch (error) {
      this.logger.error(`Error getting system overview documentation: ${error.message}`);
      throw new HttpException('Failed to get system overview documentation', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get integration guide
   */
  @ApiOperation({ summary: 'Get integration guide' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Integration guide' })
  @Get('docs/integration')
  async getIntegrationGuide() {
    try {
      // Generate all documentation first
      const documentation = await this.testingDocumentationService.generateAllDocumentation();
      
      // Return only the integration guide
      return documentation.integration;
    } catch (error) {
      this.logger.error(`Error getting integration guide: ${error.message}`);
      throw new HttpException('Failed to get integration guide', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Download full documentation
   */
  @ApiOperation({ summary: 'Download full documentation' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Full documentation' })
  @Get('docs/download')
  async downloadFullDocumentation(@Res() res: Response) {
    try {
      // Generate all documentation first
      const documentation = await this.testingDocumentationService.generateAllDocumentation();
      
      // Set response headers for download
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', 'attachment; filename=full-documentation.json');
      
      // Send documentation as download
      res.send(JSON.stringify(documentation, null, 2));
    } catch (error) {
      this.logger.error(`Error downloading full documentation: ${error.message}`);
      throw new HttpException('Failed to download full documentation', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get system health check
   */
  @ApiOperation({ summary: 'Get system health check' })
  @ApiResponse({ status: HttpStatus.OK, description: 'System health check' })
  @Get('health')
  async getSystemHealthCheck() {
    try {
      // Run a quick test to check system health
      const testResults = await this.testingDocumentationService.runAllTests();
      
      // Calculate overall health
      const summary = testResults.summary;
      const healthStatus = summary.successRate >= 90 ? 'healthy' : (summary.successRate >= 70 ? 'degraded' : 'unhealthy');
      
      return {
        status: healthStatus,
        successRate: summary.successRate,
        timestamp: new Date(),
        services: Object.keys(testResults).filter(key => key !== 'summary').map(service => ({
          name: service,
          status: testResults[service].summary.successRate >= 90 ? 'healthy' : (testResults[service].summary.successRate >= 70 ? 'degraded' : 'unhealthy'),
          successRate: testResults[service].summary.successRate,
        })),
      };
    } catch (error) {
      this.logger.error(`Error getting system health check: ${error.message}`);
      throw new HttpException('Failed to get system health check', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
