import { 
  Controller, 
  Get, 
  Put, 
  Delete, 
  Body, 
  Param, 
  UseGuards, 
  Logger,
  HttpStatus,
  HttpCode,
  NotFoundException,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth, 
  ApiParam,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { ExplanationPreferencesService } from '../services/explanation-preferences.service';
import { 
  UpdateExplanationPreferencesDto, 
  ExplanationPreferencesResponseDto,
} from '../dto/explanation-preferences.dto';

@ApiTags('Explanation Preferences')
@Controller('recommendation/explanation-preferences')
export class ExplanationPreferencesController {
  private readonly logger = new Logger(ExplanationPreferencesController.name);

  constructor(
    private readonly preferencesService: ExplanationPreferencesService,
  ) {}

  /**
   * Récupère les préférences d'explication de l'utilisateur connecté
   * @param userId ID de l'utilisateur connecté
   * @returns Préférences d'explication
   */
  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les préférences d\'explication de l\'utilisateur connecté' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Préférences d\'explication récupérées avec succès', 
    type: ExplanationPreferencesResponseDto,
  })
  async getUserPreferences(
    @CurrentUser('id') userId: string,
  ) {
    this.logger.log(`Récupération des préférences d'explication pour l'utilisateur ${userId}`);
    return this.preferencesService.getUserPreferences(userId);
  }

  /**
   * Récupère les préférences d'explication d'un utilisateur spécifique (admin uniquement)
   * @param userId ID de l'utilisateur
   * @returns Préférences d'explication
   */
  @Get(':userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les préférences d\'explication d\'un utilisateur spécifique (admin uniquement)' })
  @ApiParam({ name: 'userId', description: 'ID de l\'utilisateur' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Préférences d\'explication récupérées avec succès', 
    type: ExplanationPreferencesResponseDto,
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Utilisateur non trouvé',
  })
  async getSpecificUserPreferences(
    @Param('userId') userId: string,
  ) {
    this.logger.log(`Récupération des préférences d'explication pour l'utilisateur ${userId} (par un admin)`);
    return this.preferencesService.getUserPreferences(userId);
  }

  /**
   * Met à jour les préférences d'explication de l'utilisateur connecté
   * @param userId ID de l'utilisateur connecté
   * @param updateDto DTO de mise à jour des préférences
   * @returns Préférences d'explication mises à jour
   */
  @Put()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour les préférences d\'explication de l\'utilisateur connecté' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Préférences d\'explication mises à jour avec succès', 
    type: ExplanationPreferencesResponseDto,
  })
  async updateUserPreferences(
    @CurrentUser('id') userId: string,
    @Body() updateDto: UpdateExplanationPreferencesDto,
  ) {
    this.logger.log(`Mise à jour des préférences d'explication pour l'utilisateur ${userId}`);
    return this.preferencesService.updateUserPreferences(userId, updateDto);
  }

  /**
   * Met à jour les préférences d'explication d'un utilisateur spécifique (admin uniquement)
   * @param userId ID de l'utilisateur
   * @param updateDto DTO de mise à jour des préférences
   * @returns Préférences d'explication mises à jour
   */
  @Put(':userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour les préférences d\'explication d\'un utilisateur spécifique (admin uniquement)' })
  @ApiParam({ name: 'userId', description: 'ID de l\'utilisateur' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Préférences d\'explication mises à jour avec succès', 
    type: ExplanationPreferencesResponseDto,
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Utilisateur non trouvé',
  })
  async updateSpecificUserPreferences(
    @Param('userId') userId: string,
    @Body() updateDto: UpdateExplanationPreferencesDto,
  ) {
    this.logger.log(`Mise à jour des préférences d'explication pour l'utilisateur ${userId} (par un admin)`);
    return this.preferencesService.updateUserPreferences(userId, updateDto);
  }

  /**
   * Supprime les préférences d'explication de l'utilisateur connecté
   * @param userId ID de l'utilisateur connecté
   * @returns Résultat de la suppression
   */
  @Delete()
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Supprimer les préférences d\'explication de l\'utilisateur connecté' })
  @ApiResponse({ 
    status: HttpStatus.NO_CONTENT, 
    description: 'Préférences d\'explication supprimées avec succès',
  })
  async deleteUserPreferences(
    @CurrentUser('id') userId: string,
  ) {
    this.logger.log(`Suppression des préférences d'explication pour l'utilisateur ${userId}`);
    await this.preferencesService.deleteUserPreferences(userId);
  }

  /**
   * Supprime les préférences d'explication d'un utilisateur spécifique (admin uniquement)
   * @param userId ID de l'utilisateur
   * @returns Résultat de la suppression
   */
  @Delete(':userId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Supprimer les préférences d\'explication d\'un utilisateur spécifique (admin uniquement)' })
  @ApiParam({ name: 'userId', description: 'ID de l\'utilisateur' })
  @ApiResponse({ 
    status: HttpStatus.NO_CONTENT, 
    description: 'Préférences d\'explication supprimées avec succès',
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Utilisateur non trouvé',
  })
  async deleteSpecificUserPreferences(
    @Param('userId') userId: string,
  ) {
    this.logger.log(`Suppression des préférences d'explication pour l'utilisateur ${userId} (par un admin)`);
    const result = await this.preferencesService.deleteUserPreferences(userId);
    
    if (!result.success) {
      throw new NotFoundException(`Préférences d'explication pour l'utilisateur ${userId} non trouvées`);
    }
  }
}
