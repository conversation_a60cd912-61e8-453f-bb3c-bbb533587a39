import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { ExplanationTranslationService, Translation, TranslatedTemplate } from '../services/explanation-translation.service';

/**
 * DTO pour la traduction de texte
 */
class TranslateTextDto {
  /** Texte à traduire */
  text: string;
  
  /** Langue source */
  sourceLanguage: string;
  
  /** Langue cible */
  targetLanguage: string;
}

/**
 * DTO pour la traduction de template
 */
class TranslateTemplateDto {
  /** ID du template à traduire */
  templateId: string;
  
  /** Langue cible */
  targetLanguage: string;
}

/**
 * DTO pour la traduction d'explication
 */
class TranslateExplanationDto {
  /** ID de l'explication à traduire */
  explanationId: string;
  
  /** Langue cible */
  targetLanguage: string;
}

@ApiTags('Explanation Translation')
@Controller('recommendation/explanation-translation')
export class ExplanationTranslationController {
  private readonly logger = new Logger(ExplanationTranslationController.name);
  
  constructor(
    private readonly translationService: ExplanationTranslationService,
  ) {}
  
  /**
   * Traduit un texte
   * @param translateDto DTO de traduction
   * @returns Traduction
   */
  @Post('text')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Traduire un texte' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Texte traduit avec succès',
  })
  async translateText(
    @Body() translateDto: TranslateTextDto,
  ): Promise<Translation> {
    this.logger.log(`Traduction de texte de ${translateDto.sourceLanguage} vers ${translateDto.targetLanguage}`);
    return this.translationService.translateText(
      translateDto.text,
      translateDto.sourceLanguage,
      translateDto.targetLanguage,
    );
  }
  
  /**
   * Traduit un template d'explication
   * @param translateDto DTO de traduction
   * @returns Template traduit
   */
  @Post('template')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Traduire un template d\'explication' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Template traduit avec succès',
  })
  async translateTemplate(
    @Body() translateDto: TranslateTemplateDto,
  ): Promise<TranslatedTemplate> {
    this.logger.log(`Traduction du template ${translateDto.templateId} vers ${translateDto.targetLanguage}`);
    return this.translationService.translateTemplate(
      translateDto.templateId,
      translateDto.targetLanguage,
    );
  }
  
  /**
   * Traduit une explication
   * @param translateDto DTO de traduction
   * @returns Explication traduite
   */
  @Post('explanation')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Traduire une explication' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Explication traduite avec succès',
  })
  async translateExplanation(
    @Body() translateDto: TranslateExplanationDto,
  ): Promise<any> {
    this.logger.log(`Traduction de l'explication ${translateDto.explanationId} vers ${translateDto.targetLanguage}`);
    return this.translationService.translateExplanation(
      translateDto.explanationId,
      translateDto.targetLanguage,
    );
  }
  
  /**
   * Traduit tous les templates vers toutes les langues supportées
   * @returns Nombre de templates traduits
   */
  @Post('translate-all-templates')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Traduire tous les templates vers toutes les langues supportées' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Templates traduits avec succès',
  })
  async translateAllTemplates(): Promise<{ count: number }> {
    this.logger.log('Traduction de tous les templates');
    const count = await this.translationService.translateAllTemplates();
    return { count };
  }
  
  /**
   * Génère une explication dans la langue préférée de l'utilisateur
   * @param userId ID de l'utilisateur
   * @param recommendationId ID de la recommandation
   * @returns Explication générée
   */
  @Get('user-language-explanation')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Générer une explication dans la langue préférée de l\'utilisateur' })
  @ApiQuery({ name: 'recommendationId', description: 'ID de la recommandation' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Explication générée avec succès',
  })
  async generateExplanationInUserLanguage(
    @CurrentUser('id') userId: string,
    @Query('recommendationId') recommendationId: string,
  ): Promise<any> {
    this.logger.log(`Génération d'une explication dans la langue préférée de l'utilisateur ${userId} pour la recommandation ${recommendationId}`);
    return this.translationService.generateExplanationInUserLanguage(
      userId,
      recommendationId,
    );
  }
}
