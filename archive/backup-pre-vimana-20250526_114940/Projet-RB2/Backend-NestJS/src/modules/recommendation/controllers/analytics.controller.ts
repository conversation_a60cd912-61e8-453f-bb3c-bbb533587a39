import { Controller, Get, Query, Param, UseGuards, Logger, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { AnalyticsService } from '../services/analytics.service';

/**
 * Contrôleur pour l'analyse des recommandations
 */
@ApiTags('recommendation-analytics')
@Controller('recommendation-analytics')
export class AnalyticsController {
  private readonly logger = new Logger(AnalyticsController.name);

  constructor(private readonly analyticsService: AnalyticsService) {}

  /**
   * Récupère les métriques globales des recommandations
   * @param startDate Date de début de la période d'analyse
   * @param endDate Date de fin de la période d'analyse
   * @returns Métriques globales
   */
  @Get('global')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les métriques globales des recommandations' })
  @ApiResponse({ status: 200, description: 'Métriques globales' })
  async getGlobalMetrics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.log(`Récupération des métriques globales du ${startDate || 'début'} au ${endDate || 'aujourd\'hui'}`);
    
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    
    return this.analyticsService.getGlobalMetrics(start, end);
  }

  /**
   * Récupère les métriques des recommandations pour l'utilisateur courant
   * @param req Requête avec les informations de l'utilisateur
   * @param startDate Date de début de la période d'analyse
   * @param endDate Date de fin de la période d'analyse
   * @returns Métriques de l'utilisateur
   */
  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les métriques des recommandations pour l\'utilisateur courant' })
  @ApiResponse({ status: 200, description: 'Métriques de l\'utilisateur' })
  async getCurrentUserMetrics(
    @Req() req,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const userId = req.user.id;
    
    this.logger.log(`Récupération des métriques pour l'utilisateur ${userId} du ${startDate || 'début'} au ${endDate || 'aujourd\'hui'}`);
    
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    
    return this.analyticsService.getUserMetrics(userId, start, end);
  }

  /**
   * Récupère les métriques des recommandations pour un utilisateur spécifique
   * @param userId ID de l'utilisateur
   * @param startDate Date de début de la période d'analyse
   * @param endDate Date de fin de la période d'analyse
   * @returns Métriques de l'utilisateur
   */
  @Get('users/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les métriques des recommandations pour un utilisateur spécifique' })
  @ApiResponse({ status: 200, description: 'Métriques de l\'utilisateur' })
  async getUserMetrics(
    @Param('userId') userId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.log(`Récupération des métriques pour l'utilisateur ${userId} du ${startDate || 'début'} au ${endDate || 'aujourd\'hui'}`);
    
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    
    return this.analyticsService.getUserMetrics(userId, start, end);
  }

  /**
   * Génère manuellement un rapport d'analyse des recommandations
   * @param startDate Date de début de la période d'analyse
   * @param endDate Date de fin de la période d'analyse
   * @returns Rapport d'analyse
   */
  @Get('generate-report')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Générer manuellement un rapport d\'analyse des recommandations' })
  @ApiResponse({ status: 200, description: 'Rapport d\'analyse' })
  async generateReport(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.log(`Génération manuelle d'un rapport d'analyse du ${startDate || 'début'} au ${endDate || 'aujourd\'hui'}`);
    
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();
    
    const metrics = await this.analyticsService.getGlobalMetrics(start, end);
    
    return {
      type: 'MANUAL',
      startDate: start,
      endDate: end,
      metrics,
    };
  }
}
