import {
  Controller,
  Get,
  Put,
  Post,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { ScalabilityService } from '../services/scalability.service';
import {
  ScalabilityConfig,
  HorizontalScalingConfig,
  HorizontalScalingStrategy,
  ScalingTrigger,
  ScalingTriggerType,
  LoadBalancingConfig,
  LoadBalancingAlgorithm,
  ShardingConfig,
  ShardingStrategy,
  RateLimitingConfig,
  RateLimitStrategy,
  CircuitBreakerConfig,
  CircuitBreakerStrategy,
  FallbackStrategyConfig,
  FallbackStrategy,
} from '../interfaces/scalability.interface';

/**
 * Controller for scalability features
 */
@ApiTags('Scalability')
@Controller('recommendation/scalability')
export class ScalabilityController {
  private readonly logger = new Logger(ScalabilityController.name);

  constructor(private readonly scalabilityService: ScalabilityService) {}

  /**
   * Get scalability configuration
   */
  @ApiOperation({ summary: 'Get scalability configuration' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Scalability configuration' })
  @Get('config')
  getScalabilityConfig() {
    try {
      return this.scalabilityService.getScalabilityConfig();
    } catch (error) {
      this.logger.error(`Error getting scalability configuration: ${error.message}`);
      throw new HttpException('Failed to get scalability configuration', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update scalability configuration
   */
  @ApiOperation({ summary: 'Update scalability configuration' })
  @ApiBody({ description: 'Scalability configuration' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Updated scalability configuration' })
  @Put('config')
  updateScalabilityConfig(@Body() config: Partial<ScalabilityConfig>) {
    try {
      return this.scalabilityService.updateScalabilityConfig(config);
    } catch (error) {
      this.logger.error(`Error updating scalability configuration: ${error.message}`);
      throw new HttpException('Failed to update scalability configuration', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Check if request is allowed by rate limiter
   */
  @ApiOperation({ summary: 'Check if request is allowed by rate limiter' })
  @ApiParam({ name: 'endpoint', description: 'Endpoint name' })
  @ApiQuery({ name: 'userId', description: 'User ID', required: false })
  @ApiResponse({ status: HttpStatus.OK, description: 'Request allowed status' })
  @Get('rate-limit/:endpoint')
  isRequestAllowed(@Param('endpoint') endpoint: string, @Query('userId') userId?: string) {
    try {
      const allowed = this.scalabilityService.isRequestAllowed(endpoint, userId);
      return { allowed };
    } catch (error) {
      this.logger.error(`Error checking rate limit: ${error.message}`);
      throw new HttpException('Failed to check rate limit', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Check if service is available (circuit breaker)
   */
  @ApiOperation({ summary: 'Check if service is available (circuit breaker)' })
  @ApiParam({ name: 'service', description: 'Service name' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Service availability status' })
  @Get('circuit-breaker/:service')
  isServiceAvailable(@Param('service') service: string) {
    try {
      const available = this.scalabilityService.isServiceAvailable(service);
      return { available };
    } catch (error) {
      this.logger.error(`Error checking service availability: ${error.message}`);
      throw new HttpException('Failed to check service availability', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Record success for service (circuit breaker)
   */
  @ApiOperation({ summary: 'Record success for service (circuit breaker)' })
  @ApiParam({ name: 'service', description: 'Service name' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Success recorded' })
  @Post('circuit-breaker/:service/success')
  recordSuccess(@Param('service') service: string) {
    try {
      this.scalabilityService.recordSuccess(service);
      return { success: true, message: `Success recorded for service: ${service}` };
    } catch (error) {
      this.logger.error(`Error recording success: ${error.message}`);
      throw new HttpException('Failed to record success', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Record failure for service (circuit breaker)
   */
  @ApiOperation({ summary: 'Record failure for service (circuit breaker)' })
  @ApiParam({ name: 'service', description: 'Service name' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Failure recorded' })
  @Post('circuit-breaker/:service/failure')
  recordFailure(@Param('service') service: string) {
    try {
      this.scalabilityService.recordFailure(service);
      return { success: true, message: `Failure recorded for service: ${service}` };
    } catch (error) {
      this.logger.error(`Error recording failure: ${error.message}`);
      throw new HttpException('Failed to record failure', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get fallback recommendations
   */
  @ApiOperation({ summary: 'Get fallback recommendations' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiBody({ description: 'Context', required: false })
  @ApiResponse({ status: HttpStatus.OK, description: 'Fallback recommendations' })
  @Post('fallback/:userId')
  async getFallbackRecommendations(
    @Param('userId') userId: string,
    @Body() body?: { context?: Record<string, any> },
  ) {
    try {
      const recommendations = await this.scalabilityService.getFallbackRecommendations(userId, body?.context);
      return {
        count: recommendations.length,
        recommendations,
      };
    } catch (error) {
      this.logger.error(`Error getting fallback recommendations: ${error.message}`);
      throw new HttpException('Failed to get fallback recommendations', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
