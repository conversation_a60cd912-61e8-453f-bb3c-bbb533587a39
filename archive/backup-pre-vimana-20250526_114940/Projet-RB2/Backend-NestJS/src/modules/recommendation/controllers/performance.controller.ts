import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { PerformanceOptimizationService, CacheLevel, CacheStrategy } from '../services/performance/performance-optimization.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';

@ApiTags('performance')
@Controller('recommendation/performance')
@UseGuards(JwtAuthGuard, RolesGuard)
export class PerformanceController {
  constructor(private readonly performanceService: PerformanceOptimizationService) {}
  
  @Get('stats')
  @Roles('admin', 'data_scientist', 'developer')
  @ApiOperation({ summary: 'Get performance statistics' })
  @ApiResponse({ status: 200, description: 'Performance statistics' })
  async getPerformanceStats() {
    return this.performanceService.getPerformanceStats();
  }
  
  @Post('cache/invalidate')
  @Roles('admin', 'developer')
  @ApiOperation({ summary: 'Invalidate cache' })
  @ApiQuery({ name: 'configKey', description: 'Cache configuration key' })
  @ApiResponse({ status: 200, description: 'Cache invalidated' })
  async invalidateCache(@Query('configKey') configKey: string) {
    await this.performanceService.invalidateCache(configKey);
    return { success: true };
  }
  
  @Post('cache/preload')
  @Roles('admin', 'developer')
  @ApiOperation({ summary: 'Preload cache' })
  @ApiQuery({ name: 'configKey', description: 'Cache configuration key' })
  @ApiResponse({ status: 200, description: 'Cache preloaded' })
  async preloadCache(@Query('configKey') configKey: string) {
    await this.performanceService.preloadCache(configKey);
    return { success: true };
  }
  
  @Post('cache/entry/invalidate')
  @Roles('admin', 'developer')
  @ApiOperation({ summary: 'Invalidate cache entry' })
  @ApiResponse({ status: 200, description: 'Cache entry invalidated' })
  async invalidateCacheEntry(
    @Body() data: {
      key: string;
      configKey: string;
    },
  ) {
    await this.performanceService.invalidateCacheEntry(data.key, data.configKey);
    return { success: true };
  }
  
  @Post('cache/entry')
  @Roles('admin', 'developer')
  @ApiOperation({ summary: 'Set cache entry' })
  @ApiResponse({ status: 200, description: 'Cache entry set' })
  async setCacheEntry(
    @Body() data: {
      key: string;
      value: any;
      configKey: string;
      ttl?: number;
    },
  ) {
    await this.performanceService.setCachedValue(
      data.key,
      data.value,
      data.configKey,
      data.ttl,
    );
    return { success: true };
  }
  
  @Get('cache/entry')
  @Roles('admin', 'developer')
  @ApiOperation({ summary: 'Get cache entry' })
  @ApiQuery({ name: 'key', description: 'Cache key' })
  @ApiQuery({ name: 'configKey', description: 'Cache configuration key' })
  @ApiResponse({ status: 200, description: 'Cache entry' })
  async getCacheEntry(
    @Query('key') key: string,
    @Query('configKey') configKey: string,
  ) {
    const value = await this.performanceService.getCachedValue(key, configKey);
    return { key, value };
  }
}
