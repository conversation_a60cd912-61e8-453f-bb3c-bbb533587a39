import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Body, 
  Query, 
  UseGuards, 
  Logger,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth, 
  ApiQuery, 
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { ExplanationLearningIntegrationService } from '../services/explanation-learning-integration.service';
import { ConfigService } from '@nestjs/config';

/**
 * Contrôleur pour l'intégration des tests A/B et de l'apprentissage continu
 */
@ApiTags('explanation-learning')
@Controller('explanation-learning')
export class ExplanationLearningIntegrationController {
  private readonly logger = new Logger(ExplanationLearningIntegrationController.name);

  constructor(
    private readonly explanationLearningService: ExplanationLearningIntegrationService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Récupère la configuration actuelle
   * @returns Configuration
   */
  @Get('config')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer la configuration actuelle' })
  @ApiResponse({ 
    status: 200, 
    description: 'Configuration récupérée avec succès',
    schema: {
      type: 'object',
      properties: {
        enabled: { type: 'boolean' },
        minInteractionsForOptimization: { type: 'number' },
        minConfidenceLevel: { type: 'number' },
        optimizationInterval: { type: 'number' },
        learningRate: { type: 'number' },
        templateOptimizationEnabled: { type: 'boolean' },
        factorWeightOptimizationEnabled: { type: 'boolean' },
        autoDeployWinners: { type: 'boolean' },
        autoDeployThreshold: { type: 'number' },
      },
    },
  })
  async getConfiguration() {
    this.logger.log('Récupération de la configuration');
    
    return {
      enabled: this.configService.get<boolean>('recommendation.explanationLearning.enabled', true),
      minInteractionsForOptimization: this.configService.get<number>('recommendation.explanationLearning.minInteractionsForOptimization', 100),
      minConfidenceLevel: this.configService.get<number>('recommendation.explanationLearning.minConfidenceLevel', 0.8),
      optimizationInterval: this.configService.get<number>('recommendation.explanationLearning.optimizationInterval', 24),
      learningRate: this.configService.get<number>('recommendation.explanationLearning.learningRate', 0.1),
      templateOptimizationEnabled: this.configService.get<boolean>('recommendation.explanationLearning.templateOptimizationEnabled', true),
      factorWeightOptimizationEnabled: this.configService.get<boolean>('recommendation.explanationLearning.factorWeightOptimizationEnabled', true),
      autoDeployWinners: this.configService.get<boolean>('recommendation.explanationLearning.autoDeployWinners', false),
      autoDeployThreshold: this.configService.get<number>('recommendation.explanationLearning.autoDeployThreshold', 0.15),
    };
  }

  /**
   * Met à jour la configuration
   * @param config Nouvelle configuration
   * @returns Configuration mise à jour
   */
  @Put('config')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour la configuration' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        enabled: { type: 'boolean' },
        minInteractionsForOptimization: { type: 'number' },
        minConfidenceLevel: { type: 'number' },
        optimizationInterval: { type: 'number' },
        learningRate: { type: 'number' },
        templateOptimizationEnabled: { type: 'boolean' },
        factorWeightOptimizationEnabled: { type: 'boolean' },
        autoDeployWinners: { type: 'boolean' },
        autoDeployThreshold: { type: 'number' },
      },
    },
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Configuration mise à jour avec succès',
    schema: {
      type: 'object',
      properties: {
        enabled: { type: 'boolean' },
        minInteractionsForOptimization: { type: 'number' },
        minConfidenceLevel: { type: 'number' },
        optimizationInterval: { type: 'number' },
        learningRate: { type: 'number' },
        templateOptimizationEnabled: { type: 'boolean' },
        factorWeightOptimizationEnabled: { type: 'boolean' },
        autoDeployWinners: { type: 'boolean' },
        autoDeployThreshold: { type: 'number' },
      },
    },
  })
  async updateConfiguration(@Body() config: any) {
    this.logger.log('Mise à jour de la configuration');
    
    // Dans une implémentation réelle, nous mettrions à jour la configuration dans la base de données
    // ou dans un fichier de configuration, mais pour cet exemple, nous retournons simplement la configuration
    return config;
  }

  /**
   * Récupère les métriques
   * @returns Métriques
   */
  @Get('metrics')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les métriques' })
  @ApiResponse({ 
    status: 200, 
    description: 'Métriques récupérées avec succès',
    schema: {
      type: 'object',
      properties: {
        totalOptimizations: { type: 'number' },
        lastOptimizationDate: { type: 'string', format: 'date-time', nullable: true },
        averageImprovement: { type: 'number' },
        factorWeightUpdates: { type: 'number' },
        templateUpdates: { type: 'number' },
        deployedVariants: { type: 'number' },
        learningEvents: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              timestamp: { type: 'string', format: 'date-time' },
              description: { type: 'string' },
              improvement: { type: 'number' },
              source: { type: 'string' },
            },
          },
        },
        performanceHistory: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              date: { type: 'string', format: 'date' },
              interactionRate: { type: 'number' },
              conversionRate: { type: 'number' },
            },
          },
        },
      },
    },
  })
  async getMetrics() {
    this.logger.log('Récupération des métriques');
    
    // Dans une implémentation réelle, nous récupérerions les métriques depuis la base de données
    // mais pour cet exemple, nous retournons des données fictives
    return {
      totalOptimizations: 12,
      lastOptimizationDate: new Date().toISOString(),
      averageImprovement: 0.18,
      factorWeightUpdates: 45,
      templateUpdates: 8,
      deployedVariants: 5,
      learningEvents: [
        {
          timestamp: new Date().toISOString(),
          description: 'Optimisation des poids des facteurs',
          improvement: 0.15,
          source: 'explanation_ab_test',
        },
        {
          timestamp: new Date(Date.now() - 86400000).toISOString(),
          description: 'Mise à jour des templates d\'explication',
          improvement: 0.08,
          source: 'explanation_ab_test',
        },
        {
          timestamp: new Date(Date.now() - 172800000).toISOString(),
          description: 'Déploiement de la variante "Détaillée"',
          improvement: 0.22,
          source: 'explanation_ab_test',
        },
      ],
      performanceHistory: [
        {
          date: '2023-06-01',
          interactionRate: 0.42,
          conversionRate: 0.18,
        },
        {
          date: '2023-06-08',
          interactionRate: 0.45,
          conversionRate: 0.19,
        },
        {
          date: '2023-06-15',
          interactionRate: 0.48,
          conversionRate: 0.21,
        },
        {
          date: '2023-06-22',
          interactionRate: 0.52,
          conversionRate: 0.23,
        },
        {
          date: '2023-06-29',
          interactionRate: 0.55,
          conversionRate: 0.25,
        },
      ],
    };
  }

  /**
   * Déclenche une optimisation manuelle
   * @returns Résultat de l'optimisation
   */
  @Post('optimize')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Déclencher une optimisation manuelle' })
  @ApiResponse({ 
    status: 200, 
    description: 'Optimisation déclenchée avec succès',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
      },
    },
  })
  async triggerOptimization() {
    this.logger.log('Déclenchement d\'une optimisation manuelle');
    
    // Dans une implémentation réelle, nous déclencherions une optimisation
    // mais pour cet exemple, nous retournons simplement un succès
    return {
      success: true,
      message: 'Optimisation déclenchée avec succès',
    };
  }

  /**
   * Récupère l'historique des optimisations
   * @param startDate Date de début (optionnelle)
   * @param endDate Date de fin (optionnelle)
   * @returns Historique des optimisations
   */
  @Get('history')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer l\'historique des optimisations' })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiResponse({ 
    status: 200, 
    description: 'Historique récupéré avec succès',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          timestamp: { type: 'string', format: 'date-time' },
          testId: { type: 'string' },
          testName: { type: 'string' },
          winnerVariantId: { type: 'string' },
          winnerVariantName: { type: 'string' },
          improvement: { type: 'number' },
          confidenceLevel: { type: 'number' },
          appliedChanges: {
            type: 'array',
            items: { type: 'string' },
          },
        },
      },
    },
  })
  async getOptimizationHistory(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.log(`Récupération de l'historique des optimisations (${startDate} - ${endDate})`);
    
    // Dans une implémentation réelle, nous récupérerions l'historique depuis la base de données
    // mais pour cet exemple, nous retournons des données fictives
    return [
      {
        timestamp: new Date().toISOString(),
        testId: '123e4567-e89b-12d3-a456-426614174000',
        testName: 'Test de niveau de détail des explications',
        winnerVariantId: '123e4567-e89b-12d3-a456-426614174001',
        winnerVariantName: 'Variante détaillée',
        improvement: 0.15,
        confidenceLevel: 0.95,
        appliedChanges: [
          'Mise à jour des poids des facteurs',
          'Mise à jour des templates d\'explication',
        ],
      },
      {
        timestamp: new Date(Date.now() - 86400000).toISOString(),
        testId: '123e4567-e89b-12d3-a456-426614174002',
        testName: 'Test de présentation visuelle des explications',
        winnerVariantId: '123e4567-e89b-12d3-a456-426614174003',
        winnerVariantName: 'Variante visuelle',
        improvement: 0.08,
        confidenceLevel: 0.92,
        appliedChanges: [
          'Mise à jour des templates d\'explication',
        ],
      },
    ];
  }

  /**
   * Réinitialise les optimisations
   * @returns Résultat de la réinitialisation
   */
  @Post('reset')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Réinitialiser les optimisations' })
  @ApiResponse({ 
    status: 200, 
    description: 'Optimisations réinitialisées avec succès',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
      },
    },
  })
  async resetOptimizations() {
    this.logger.log('Réinitialisation des optimisations');
    
    // Dans une implémentation réelle, nous réinitialiserions les optimisations
    // mais pour cet exemple, nous retournons simplement un succès
    return {
      success: true,
      message: 'Optimisations réinitialisées avec succès',
    };
  }
}
