import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { ExplanationController } from './explanation.controller';
import { ExplanationService } from '../services/explanation.service';
import { ContinuousLearningService } from '../services/continuous-learning.service';
import { ExplanationEventDto } from '../dto/explanation.dto';

describe('ExplanationController', () => {
  let controller: ExplanationController;
  let explanationService: ExplanationService;
  let continuousLearningService: ContinuousLearningService;

  const mockExplanationService = {
    generateExplanation: jest.fn(),
  };

  const mockContinuousLearningService = {
    getUserModel: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      controllers: [ExplanationController],
      providers: [
        {
          provide: ExplanationService,
          useValue: mockExplanationService,
        },
        {
          provide: ContinuousLearningService,
          useValue: mockContinuousLearningService,
        },
      ],
    }).compile();

    controller = module.get<ExplanationController>(ExplanationController);
    explanationService = module.get<ExplanationService>(ExplanationService);
    continuousLearningService = module.get<ContinuousLearningService>(ContinuousLearningService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getExplanation', () => {
    it('should return an explanation for a valid recommendation', async () => {
      const userId = 'user123';
      const recommendationId = 'rec123';
      const query = {};

      // Mock the private method getRecommendation
      jest.spyOn(controller as any, 'getRecommendation').mockResolvedValue({
        id: recommendationId,
        userId,
        itemId: 'item123',
        itemType: 'RETREAT',
        strategy: 'HYBRID',
        score: 0.85,
        sources: ['content-based', 'collaborative'],
      });

      // Mock the explanation service
      mockExplanationService.generateExplanation.mockResolvedValue({
        text: 'This is an explanation',
        factors: [
          {
            type: 'CATEGORY_MATCH',
            name: 'Category',
            value: 'Yoga',
            weight: 0.8,
          },
        ],
        sources: ['content-based', 'collaborative'],
        confidence: 0.9,
      });

      const result = await controller.getExplanation(userId, recommendationId, query);

      expect(result).toBeDefined();
      expect(result.recommendationId).toBe(recommendationId);
      expect(result.userId).toBe(userId);
      expect(result.summary).toBe('This is an explanation');
      expect(result.factors).toHaveLength(1);
      expect(result.factors[0].type).toBe('CATEGORY_MATCH');
      expect(mockExplanationService.generateExplanation).toHaveBeenCalledWith(
        userId,
        expect.objectContaining({ id: recommendationId }),
      );
    });

    it('should throw an exception if the recommendation is not found', async () => {
      const userId = 'user123';
      const recommendationId = 'nonexistent';
      const query = {};

      // Mock the private method getRecommendation to return null
      jest.spyOn(controller as any, 'getRecommendation').mockResolvedValue(null);

      await expect(controller.getExplanation(userId, recommendationId, query)).rejects.toThrow(
        new HttpException('Recommandation non trouvée', HttpStatus.NOT_FOUND),
      );
    });
  });

  describe('recordExplanationEvent', () => {
    it('should record an explanation event for a valid recommendation', async () => {
      const userId = 'user123';
      const recommendationId = 'rec123';
      const eventDto: ExplanationEventDto = {
        recommendationId,
        eventType: 'view',
      };

      // Mock the private methods
      jest.spyOn(controller as any, 'getRecommendation').mockResolvedValue({
        id: recommendationId,
        userId,
      });
      jest.spyOn(controller as any, 'recordEvent').mockResolvedValue(undefined);

      const result = await controller.recordExplanationEvent(userId, recommendationId, eventDto);

      expect(result).toBeDefined();
      expect(result.statusCode).toBe(HttpStatus.CREATED);
      expect(result.message).toContain('Événement d\'explication enregistré avec succès');
    });

    it('should throw an exception if the recommendation is not found', async () => {
      const userId = 'user123';
      const recommendationId = 'nonexistent';
      const eventDto: ExplanationEventDto = {
        recommendationId,
        eventType: 'view',
      };

      // Mock the private method getRecommendation to return null
      jest.spyOn(controller as any, 'getRecommendation').mockResolvedValue(null);

      await expect(controller.recordExplanationEvent(userId, recommendationId, eventDto)).rejects.toThrow(
        new HttpException('Recommandation non trouvée', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw an exception if the recommendation IDs do not match', async () => {
      const userId = 'user123';
      const recommendationId = 'rec123';
      const eventDto: ExplanationEventDto = {
        recommendationId: 'different-id',
        eventType: 'view',
      };

      // Mock the private method getRecommendation
      jest.spyOn(controller as any, 'getRecommendation').mockResolvedValue({
        id: recommendationId,
        userId,
      });

      await expect(controller.recordExplanationEvent(userId, recommendationId, eventDto)).rejects.toThrow(
        new HttpException(
          'L\'ID de la recommandation dans le corps de la requête ne correspond pas à celui de l\'URL',
          HttpStatus.BAD_REQUEST,
        ),
      );
    });
  });

  describe('getExplanationStats', () => {
    it('should return explanation statistics for valid dates', async () => {
      const startDate = '2023-01-01T00:00:00Z';
      const endDate = '2023-01-31T23:59:59Z';

      const result = await controller.getExplanationStats(startDate, endDate);

      expect(result).toBeDefined();
      expect(result.totalExplanations).toBeDefined();
      expect(result.byStrategy).toBeDefined();
      expect(result.byItemType).toBeDefined();
      expect(result.topFactors).toBeDefined();
      expect(result.interactionRate).toBeDefined();
      expect(result.conversionRate).toBeDefined();
    });

    it('should throw an exception for invalid dates', async () => {
      const startDate = 'invalid-date';
      const endDate = '2023-01-31T23:59:59Z';

      await expect(controller.getExplanationStats(startDate, endDate)).rejects.toThrow(
        new HttpException('Dates invalides', HttpStatus.BAD_REQUEST),
      );
    });

    it('should throw an exception if start date is after end date', async () => {
      const startDate = '2023-02-01T00:00:00Z';
      const endDate = '2023-01-31T23:59:59Z';

      await expect(controller.getExplanationStats(startDate, endDate)).rejects.toThrow(
        new HttpException('La date de début doit être antérieure à la date de fin', HttpStatus.BAD_REQUEST),
      );
    });
  });
});
