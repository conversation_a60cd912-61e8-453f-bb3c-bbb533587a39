import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { ContextDetectionService, UserContext } from '../services/context-detection.service';
import { ContextualRecommendationService, ContextualRecommendationOptions } from '../services/contextual-recommendation.service';
import { SeasonalRecommendationService, SeasonalRecommendationOptions } from '../services/seasonal-recommendation.service';

/**
 * DTO pour les options de recommandation contextuelle
 */
class ContextualRecommendationOptionsDto implements Partial<ContextualRecommendationOptions> {
  /** Facteur d'influence du contexte (0-1) */
  contextFactor?: number;
  
  /** Nombre maximum de recommandations */
  maxRecommendations?: number;
  
  /** Types de contexte à prendre en compte */
  contextTypes?: Array<'location' | 'weather' | 'season' | 'time' | 'events' | 'cultural'>;
  
  /** Filtres supplémentaires */
  filters?: Record<string, any>;
}

/**
 * DTO pour les options de recommandation saisonnière
 */
class SeasonalRecommendationOptionsDto implements Partial<SeasonalRecommendationOptions> {
  /** Facteur d'influence de la saison (0-1) */
  seasonFactor?: number;
  
  /** Nombre maximum de recommandations */
  maxRecommendations?: number;
  
  /** Inclure les recommandations pour la saison suivante */
  includeNextSeason?: boolean;
  
  /** Facteur d'influence de la saison suivante (0-1) */
  nextSeasonFactor?: number;
  
  /** Filtres supplémentaires */
  filters?: Record<string, any>;
}

/**
 * Contrôleur pour les recommandations contextuelles et saisonnières
 */
@ApiTags('Contextual Recommendations')
@Controller('recommendation/contextual')
export class ContextualRecommendationController {
  private readonly logger = new Logger(ContextualRecommendationController.name);
  
  constructor(
    private readonly contextDetectionService: ContextDetectionService,
    private readonly contextualRecommendationService: ContextualRecommendationService,
    private readonly seasonalRecommendationService: SeasonalRecommendationService,
  ) {}
  
  /**
   * Récupère le contexte d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param forceRefresh Forcer le rafraîchissement du contexte
   * @returns Contexte utilisateur
   */
  @Get('context/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer le contexte d\'un utilisateur' })
  @ApiParam({ name: 'userId', description: 'ID de l\'utilisateur' })
  @ApiQuery({ name: 'forceRefresh', required: false, type: Boolean })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Contexte récupéré avec succès',
  })
  async getUserContext(
    @Param('userId') userId: string,
    @Query('forceRefresh') forceRefresh?: boolean,
  ): Promise<UserContext> {
    this.logger.log(`Récupération du contexte pour l'utilisateur ${userId}`);
    return this.contextDetectionService.detectUserContext(userId, forceRefresh);
  }
  
  /**
   * Récupère le contexte de l'utilisateur courant
   * @param user Utilisateur courant
   * @param forceRefresh Forcer le rafraîchissement du contexte
   * @returns Contexte utilisateur
   */
  @Get('my-context')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer le contexte de l\'utilisateur courant' })
  @ApiQuery({ name: 'forceRefresh', required: false, type: Boolean })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Contexte récupéré avec succès',
  })
  async getMyContext(
    @CurrentUser('id') userId: string,
    @Query('forceRefresh') forceRefresh?: boolean,
  ): Promise<UserContext> {
    this.logger.log(`Récupération du contexte pour l'utilisateur courant ${userId}`);
    return this.contextDetectionService.detectUserContext(userId, forceRefresh);
  }
  
  /**
   * Récupère des recommandations contextuelles pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param options Options de recommandation
   * @returns Recommandations contextuelles
   */
  @Post('recommendations/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer des recommandations contextuelles pour un utilisateur' })
  @ApiParam({ name: 'userId', description: 'ID de l\'utilisateur' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recommandations récupérées avec succès',
  })
  async getContextualRecommendations(
    @Param('userId') userId: string,
    @Body() options: ContextualRecommendationOptionsDto,
  ): Promise<any[]> {
    this.logger.log(`Récupération des recommandations contextuelles pour l'utilisateur ${userId}`);
    return this.contextualRecommendationService.getContextualRecommendations(userId, options);
  }
  
  /**
   * Récupère des recommandations contextuelles pour l'utilisateur courant
   * @param user Utilisateur courant
   * @param contextFactor Facteur d'influence du contexte
   * @param maxRecommendations Nombre maximum de recommandations
   * @returns Recommandations contextuelles
   */
  @Get('my-recommendations')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer des recommandations contextuelles pour l\'utilisateur courant' })
  @ApiQuery({ name: 'contextFactor', required: false, type: Number })
  @ApiQuery({ name: 'maxRecommendations', required: false, type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recommandations récupérées avec succès',
  })
  async getMyContextualRecommendations(
    @CurrentUser('id') userId: string,
    @Query('contextFactor') contextFactor?: number,
    @Query('maxRecommendations') maxRecommendations?: number,
  ): Promise<any[]> {
    this.logger.log(`Récupération des recommandations contextuelles pour l'utilisateur courant ${userId}`);
    
    const options: ContextualRecommendationOptionsDto = {
      contextFactor,
      maxRecommendations,
    };
    
    return this.contextualRecommendationService.getContextualRecommendations(userId, options);
  }
  
  /**
   * Récupère des recommandations saisonnières pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param options Options de recommandation
   * @returns Recommandations saisonnières
   */
  @Post('seasonal/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer des recommandations saisonnières pour un utilisateur' })
  @ApiParam({ name: 'userId', description: 'ID de l\'utilisateur' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recommandations récupérées avec succès',
  })
  async getSeasonalRecommendations(
    @Param('userId') userId: string,
    @Body() options: SeasonalRecommendationOptionsDto,
  ): Promise<any[]> {
    this.logger.log(`Récupération des recommandations saisonnières pour l'utilisateur ${userId}`);
    return this.seasonalRecommendationService.getSeasonalRecommendations(userId, options);
  }
  
  /**
   * Récupère des recommandations saisonnières pour l'utilisateur courant
   * @param user Utilisateur courant
   * @param seasonFactor Facteur d'influence de la saison
   * @param includeNextSeason Inclure les recommandations pour la saison suivante
   * @param maxRecommendations Nombre maximum de recommandations
   * @returns Recommandations saisonnières
   */
  @Get('my-seasonal')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer des recommandations saisonnières pour l\'utilisateur courant' })
  @ApiQuery({ name: 'seasonFactor', required: false, type: Number })
  @ApiQuery({ name: 'includeNextSeason', required: false, type: Boolean })
  @ApiQuery({ name: 'maxRecommendations', required: false, type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recommandations récupérées avec succès',
  })
  async getMySeasonalRecommendations(
    @CurrentUser('id') userId: string,
    @Query('seasonFactor') seasonFactor?: number,
    @Query('includeNextSeason') includeNextSeason?: boolean,
    @Query('maxRecommendations') maxRecommendations?: number,
  ): Promise<any[]> {
    this.logger.log(`Récupération des recommandations saisonnières pour l'utilisateur courant ${userId}`);
    
    const options: SeasonalRecommendationOptionsDto = {
      seasonFactor,
      includeNextSeason,
      maxRecommendations,
    };
    
    return this.seasonalRecommendationService.getSeasonalRecommendations(userId, options);
  }
  
  /**
   * Récupère des recommandations pour la saison actuelle
   * @param user Utilisateur courant
   * @param maxRecommendations Nombre maximum de recommandations
   * @returns Recommandations pour la saison actuelle
   */
  @Get('current-season')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer des recommandations pour la saison actuelle' })
  @ApiQuery({ name: 'maxRecommendations', required: false, type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recommandations récupérées avec succès',
  })
  async getCurrentSeasonRecommendations(
    @CurrentUser('id') userId: string,
    @Query('maxRecommendations') maxRecommendations?: number,
  ): Promise<any[]> {
    this.logger.log(`Récupération des recommandations pour la saison actuelle pour l'utilisateur ${userId}`);
    
    const options: SeasonalRecommendationOptionsDto = {
      seasonFactor: 0.8,
      includeNextSeason: false,
      maxRecommendations,
    };
    
    return this.seasonalRecommendationService.getSeasonalRecommendations(userId, options);
  }
  
  /**
   * Récupère des recommandations pour la saison suivante
   * @param user Utilisateur courant
   * @param maxRecommendations Nombre maximum de recommandations
   * @returns Recommandations pour la saison suivante
   */
  @Get('next-season')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer des recommandations pour la saison suivante' })
  @ApiQuery({ name: 'maxRecommendations', required: false, type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recommandations récupérées avec succès',
  })
  async getNextSeasonRecommendations(
    @CurrentUser('id') userId: string,
    @Query('maxRecommendations') maxRecommendations?: number,
  ): Promise<any[]> {
    this.logger.log(`Récupération des recommandations pour la saison suivante pour l'utilisateur ${userId}`);
    
    // Récupérer le contexte de l'utilisateur
    const userContext = await this.contextDetectionService.detectUserContext(userId);
    const seasonData = userContext.seasonData;
    
    if (!seasonData) {
      return [];
    }
    
    // Récupérer les recommandations de base
    const baseRecommendations = await this.contextualRecommendationService.getContextualRecommendations(userId, {
      contextFactor: 0.3,
      maxRecommendations: maxRecommendations || 20,
      contextTypes: ['location', 'weather', 'time', 'events', 'cultural'],
    });
    
    // Filtrer les recommandations pour la saison suivante
    const nextSeasonRecommendations = baseRecommendations.filter(recommendation => {
      return recommendation.metadata?.season === seasonData.nextSeason;
    });
    
    return nextSeasonRecommendations.slice(0, maxRecommendations || 10);
  }
}
