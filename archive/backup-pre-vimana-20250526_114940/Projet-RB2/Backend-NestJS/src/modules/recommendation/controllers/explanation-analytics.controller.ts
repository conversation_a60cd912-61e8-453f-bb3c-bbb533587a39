import {
  Controller,
  Get,
  Query,
  UseGuards,
  HttpStatus,
  Logger,
  ParseArrayPipe,
  ParseEnumPipe,
  Optional,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import {
  ExplanationAnalyticsService,
  ExplanationMetrics,
  ExplanationTrends,
  ExplanationImpact,
  AnalyticsFilterParams,
} from '../services/explanation-analytics.service';

@ApiTags('Explanation Analytics')
@Controller('recommendation/explanation-analytics')
export class ExplanationAnalyticsController {
  private readonly logger = new Logger(ExplanationAnalyticsController.name);
  
  constructor(
    private readonly analyticsService: ExplanationAnalyticsService,
  ) {}
  
  /**
   * Récupère les métriques d'explication
   * @param startDate Date de début
   * @param endDate Date de fin
   * @param factorTypes Types de facteur
   * @param userSegments Segments utilisateur
   * @param languages Langues
   * @param explanationTypes Types d'explication
   * @param explanationStyles Styles d'explication
   * @returns Métriques d'explication
   */
  @Get('metrics')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les métriques d\'explication' })
  @ApiQuery({ name: 'startDate', required: false, type: Date })
  @ApiQuery({ name: 'endDate', required: false, type: Date })
  @ApiQuery({ name: 'factorTypes', required: false, type: [String] })
  @ApiQuery({ name: 'userSegments', required: false, type: [String] })
  @ApiQuery({ name: 'languages', required: false, type: [String] })
  @ApiQuery({ name: 'explanationTypes', required: false, type: [String] })
  @ApiQuery({ name: 'explanationStyles', required: false, type: [String] })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Métriques récupérées avec succès',
  })
  async getMetrics(
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
    @Query('factorTypes', new ParseArrayPipe({ items: String, separator: ',', optional: true }))
    factorTypes?: string[],
    @Query('userSegments', new ParseArrayPipe({ items: String, separator: ',', optional: true }))
    userSegments?: string[],
    @Query('languages', new ParseArrayPipe({ items: String, separator: ',', optional: true }))
    languages?: string[],
    @Query('explanationTypes', new ParseArrayPipe({ items: String, separator: ',', optional: true }))
    explanationTypes?: string[],
    @Query('explanationStyles', new ParseArrayPipe({ items: String, separator: ',', optional: true }))
    explanationStyles?: string[],
  ): Promise<ExplanationMetrics> {
    this.logger.log('Récupération des métriques d\'explication');
    
    const filters: AnalyticsFilterParams = {
      startDate,
      endDate,
      factorTypes,
      userSegments,
      languages,
      explanationTypes,
      explanationStyles,
    };
    
    return this.analyticsService.getExplanationMetrics(filters);
  }
  
  /**
   * Récupère les tendances d'explication
   * @param period Période de temps
   * @param startDate Date de début
   * @param endDate Date de fin
   * @param factorTypes Types de facteur
   * @param userSegments Segments utilisateur
   * @param languages Langues
   * @param explanationTypes Types d'explication
   * @param explanationStyles Styles d'explication
   * @returns Tendances d'explication
   */
  @Get('trends')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les tendances d\'explication' })
  @ApiQuery({ name: 'period', required: false, enum: ['day', 'week', 'month'] })
  @ApiQuery({ name: 'startDate', required: false, type: Date })
  @ApiQuery({ name: 'endDate', required: false, type: Date })
  @ApiQuery({ name: 'factorTypes', required: false, type: [String] })
  @ApiQuery({ name: 'userSegments', required: false, type: [String] })
  @ApiQuery({ name: 'languages', required: false, type: [String] })
  @ApiQuery({ name: 'explanationTypes', required: false, type: [String] })
  @ApiQuery({ name: 'explanationStyles', required: false, type: [String] })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tendances récupérées avec succès',
  })
  async getTrends(
    @Query('period', new ParseEnumPipe({ enum: ['day', 'week', 'month'], optional: true }))
    period?: 'day' | 'week' | 'month',
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
    @Query('factorTypes', new ParseArrayPipe({ items: String, separator: ',', optional: true }))
    factorTypes?: string[],
    @Query('userSegments', new ParseArrayPipe({ items: String, separator: ',', optional: true }))
    userSegments?: string[],
    @Query('languages', new ParseArrayPipe({ items: String, separator: ',', optional: true }))
    languages?: string[],
    @Query('explanationTypes', new ParseArrayPipe({ items: String, separator: ',', optional: true }))
    explanationTypes?: string[],
    @Query('explanationStyles', new ParseArrayPipe({ items: String, separator: ',', optional: true }))
    explanationStyles?: string[],
  ): Promise<ExplanationTrends> {
    this.logger.log(`Récupération des tendances d'explication pour la période ${period}`);
    
    const filters: AnalyticsFilterParams = {
      startDate,
      endDate,
      factorTypes,
      userSegments,
      languages,
      explanationTypes,
      explanationStyles,
    };
    
    return this.analyticsService.getExplanationTrends(period, filters);
  }
  
  /**
   * Récupère l'impact des explications
   * @param startDate Date de début
   * @param endDate Date de fin
   * @param factorTypes Types de facteur
   * @param userSegments Segments utilisateur
   * @param languages Langues
   * @param explanationTypes Types d'explication
   * @param explanationStyles Styles d'explication
   * @returns Impact des explications
   */
  @Get('impact')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer l\'impact des explications' })
  @ApiQuery({ name: 'startDate', required: false, type: Date })
  @ApiQuery({ name: 'endDate', required: false, type: Date })
  @ApiQuery({ name: 'factorTypes', required: false, type: [String] })
  @ApiQuery({ name: 'userSegments', required: false, type: [String] })
  @ApiQuery({ name: 'languages', required: false, type: [String] })
  @ApiQuery({ name: 'explanationTypes', required: false, type: [String] })
  @ApiQuery({ name: 'explanationStyles', required: false, type: [String] })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Impact récupéré avec succès',
  })
  async getImpact(
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
    @Query('factorTypes', new ParseArrayPipe({ items: String, separator: ',', optional: true }))
    factorTypes?: string[],
    @Query('userSegments', new ParseArrayPipe({ items: String, separator: ',', optional: true }))
    userSegments?: string[],
    @Query('languages', new ParseArrayPipe({ items: String, separator: ',', optional: true }))
    languages?: string[],
    @Query('explanationTypes', new ParseArrayPipe({ items: String, separator: ',', optional: true }))
    explanationTypes?: string[],
    @Query('explanationStyles', new ParseArrayPipe({ items: String, separator: ',', optional: true }))
    explanationStyles?: string[],
  ): Promise<ExplanationImpact> {
    this.logger.log('Récupération de l\'impact des explications');
    
    const filters: AnalyticsFilterParams = {
      startDate,
      endDate,
      factorTypes,
      userSegments,
      languages,
      explanationTypes,
      explanationStyles,
    };
    
    return this.analyticsService.getExplanationImpact(filters);
  }
}
