import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  <PERSON>piBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { ReinforcementLearningService } from '../services/reinforcement-learning.service';
import {
  CreateRLAgentDto,
  UpdateRLAgentDto,
  RLAgentResponseDto,
  RecordExplanationInteractionDto,
  RLRewardDto,
  RewardFunctionConfigDto,
  RLStatsDto,
} from '../dto/reinforcement-learning.dto';
import { RLAgentState } from '../interfaces/reinforcement-learning.interface';

@ApiTags('Reinforcement Learning')
@Controller('recommendation/reinforcement-learning')
export class ReinforcementLearningController {
  private readonly logger = new Logger(ReinforcementLearningController.name);

  constructor(private readonly rlService: ReinforcementLearningService) {}

  /**
   * Récupère tous les agents d'apprentissage par renforcement
   * @returns Liste des agents
   */
  @Get('agents')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer tous les agents d\'apprentissage par renforcement' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Liste des agents récupérée avec succès',
    type: [RLAgentResponseDto],
  })
  async getAllAgents(): Promise<RLAgentResponseDto[]> {
    this.logger.log('Récupération de tous les agents');
    const agents = await this.rlService.getAllAgents();
    return agents;
  }

  /**
   * Récupère un agent par son ID
   * @param agentId ID de l'agent
   * @returns Agent
   */
  @Get('agents/:agentId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer un agent par son ID' })
  @ApiParam({ name: 'agentId', description: 'ID de l\'agent' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Agent récupéré avec succès',
    type: RLAgentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Agent non trouvé',
  })
  async getAgent(@Param('agentId') agentId: string): Promise<RLAgentResponseDto> {
    this.logger.log(`Récupération de l'agent ${agentId}`);
    return this.rlService.getAgent(agentId);
  }

  /**
   * Crée un nouvel agent d'apprentissage par renforcement
   * @param createDto DTO de création d'agent
   * @returns Agent créé
   */
  @Post('agents')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Créer un nouvel agent d\'apprentissage par renforcement' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Agent créé avec succès',
    type: RLAgentResponseDto,
  })
  async createAgent(@Body() createDto: CreateRLAgentDto): Promise<RLAgentResponseDto> {
    this.logger.log(`Création d'un nouvel agent: ${createDto.name}`);
    return this.rlService.createAgent(createDto);
  }

  /**
   * Met à jour un agent
   * @param agentId ID de l'agent
   * @param updateDto DTO de mise à jour
   * @returns Agent mis à jour
   */
  @Put('agents/:agentId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour un agent' })
  @ApiParam({ name: 'agentId', description: 'ID de l\'agent' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Agent mis à jour avec succès',
    type: RLAgentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Agent non trouvé',
  })
  async updateAgent(
    @Param('agentId') agentId: string,
    @Body() updateDto: UpdateRLAgentDto,
  ): Promise<RLAgentResponseDto> {
    this.logger.log(`Mise à jour de l'agent ${agentId}`);
    return this.rlService.updateAgent(agentId, updateDto);
  }

  /**
   * Supprime un agent
   * @param agentId ID de l'agent
   * @returns Résultat de la suppression
   */
  @Delete('agents/:agentId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Supprimer un agent' })
  @ApiParam({ name: 'agentId', description: 'ID de l\'agent' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Agent supprimé avec succès',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Agent non trouvé',
  })
  async deleteAgent(@Param('agentId') agentId: string): Promise<void> {
    this.logger.log(`Suppression de l'agent ${agentId}`);
    await this.rlService.deleteAgent(agentId);
  }

  /**
   * Démarre l'apprentissage d'un agent
   * @param agentId ID de l'agent
   * @returns Agent mis à jour
   */
  @Post('agents/:agentId/start')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Démarrer l\'apprentissage d\'un agent' })
  @ApiParam({ name: 'agentId', description: 'ID de l\'agent' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Apprentissage démarré avec succès',
    type: RLAgentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Agent non trouvé',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'L\'agent ne peut pas démarrer l\'apprentissage depuis son état actuel',
  })
  async startLearning(@Param('agentId') agentId: string): Promise<RLAgentResponseDto> {
    this.logger.log(`Démarrage de l'apprentissage pour l'agent ${agentId}`);
    return this.rlService.startLearning(agentId);
  }

  /**
   * Met en pause l'apprentissage d'un agent
   * @param agentId ID de l'agent
   * @returns Agent mis à jour
   */
  @Post('agents/:agentId/pause')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre en pause l\'apprentissage d\'un agent' })
  @ApiParam({ name: 'agentId', description: 'ID de l\'agent' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Apprentissage mis en pause avec succès',
    type: RLAgentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Agent non trouvé',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'L\'agent n\'est pas en cours d\'apprentissage',
  })
  async pauseLearning(@Param('agentId') agentId: string): Promise<RLAgentResponseDto> {
    this.logger.log(`Mise en pause de l'apprentissage pour l'agent ${agentId}`);
    return this.rlService.pauseLearning(agentId);
  }

  /**
   * Arrête l'apprentissage d'un agent
   * @param agentId ID de l'agent
   * @returns Agent mis à jour
   */
  @Post('agents/:agentId/stop')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Arrêter l\'apprentissage d\'un agent' })
  @ApiParam({ name: 'agentId', description: 'ID de l\'agent' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Apprentissage arrêté avec succès',
    type: RLAgentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Agent non trouvé',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'L\'agent n\'est pas en cours d\'apprentissage ou en pause',
  })
  async stopLearning(@Param('agentId') agentId: string): Promise<RLAgentResponseDto> {
    this.logger.log(`Arrêt de l'apprentissage pour l'agent ${agentId}`);
    return this.rlService.stopLearning(agentId);
  }

  /**
   * Enregistre une interaction avec une explication
   * @param userId ID de l'utilisateur
   * @param interactionDto DTO de l'interaction
   * @returns Récompense calculée
   */
  @Post('interactions')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Enregistrer une interaction avec une explication' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Interaction enregistrée avec succès',
    type: RLRewardDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Explication non trouvée',
  })
  async recordInteraction(
    @CurrentUser('id') userId: string,
    @Body() interactionDto: RecordExplanationInteractionDto,
  ): Promise<RLRewardDto> {
    this.logger.log(`Enregistrement d'une interaction pour l'utilisateur ${userId} avec l'explication ${interactionDto.explanationId}`);
    return this.rlService.recordInteraction(userId, interactionDto);
  }

  /**
   * Configure la fonction de récompense pour un agent
   * @param agentId ID de l'agent
   * @param configDto DTO de configuration
   * @returns Résultat de la configuration
   */
  @Put('agents/:agentId/reward-function')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Configurer la fonction de récompense pour un agent' })
  @ApiParam({ name: 'agentId', description: 'ID de l\'agent' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Fonction de récompense configurée avec succès',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Agent non trouvé',
  })
  async configureRewardFunction(
    @Param('agentId') agentId: string,
    @Body() configDto: RewardFunctionConfigDto,
  ): Promise<{ success: boolean }> {
    this.logger.log(`Configuration de la fonction de récompense pour l'agent ${agentId}`);
    return this.rlService.configureRewardFunction(agentId, configDto);
  }

  /**
   * Récupère les statistiques globales d'apprentissage par renforcement
   * @returns Statistiques globales
   */
  @Get('stats')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les statistiques globales d\'apprentissage par renforcement' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Statistiques récupérées avec succès',
    type: RLStatsDto,
  })
  async getStats(): Promise<RLStatsDto> {
    this.logger.log('Récupération des statistiques globales');
    
    // Pour l'instant, retourner des statistiques fictives
    // Dans une implémentation réelle, ces statistiques seraient calculées à partir des données réelles
    return {
      totalAgents: 5,
      totalEpisodes: 10000,
      totalInteractions: 50000,
      globalAverageReward: 0.75,
      averageConvergenceRate: 0.92,
      statsByAgentType: {
        Q_LEARNING: { count: 2, averageReward: 0.7 },
        DQN: { count: 3, averageReward: 0.8 },
      },
      statsByInteractionType: {
        CLICK: { count: 30000, averageReward: 0.6 },
        FEEDBACK: { count: 20000, averageReward: 0.9 },
      },
    };
  }
}
