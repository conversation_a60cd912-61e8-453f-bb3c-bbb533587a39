import { Controller, Post, Body, UseGuards, Req, HttpException, HttpStatus, Logger, Get, Param, Query, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { FeedbackService, FeedbackType, ReportReason } from '../services/feedback.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import {
  RecordFeedbackDto,
  DetailedFeedbackDto,
  ReportRecommendationDto,
  FeedbackResponseDto,
  FeedbackQueryOptionsDto
} from '../dto/feedback.dto';

interface RequestWithUser {
  user: {
    id: string;
    roles: string[];
  };
}

/**
 * Contrôleur pour le système de feedback utilisateur
 */
@ApiTags('recommendation-feedback')
@Controller('recommendations/feedback')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class FeedbackController {
  private readonly logger = new Logger(FeedbackController.name);

  constructor(private readonly feedbackService: FeedbackService) {}

  /**
   * Enregistrer un feedback utilisateur simple
   */
  @Post()
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Enregistrer un feedback utilisateur simple sur une recommandation' })
  @ApiResponse({ status: 201, description: 'Feedback enregistré avec succès', type: FeedbackResponseDto })
  @ApiResponse({ status: 400, description: 'Données de requête invalides' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async recordFeedback(
    @Body() feedbackDto: RecordFeedbackDto,
    @Req() req: RequestWithUser,
  ) {
    try {
      this.logger.log(`Enregistrement d'un feedback de type ${feedbackDto.feedbackType} pour la recommandation ${feedbackDto.recommendationId}`);

      const result = await this.feedbackService.recordFeedback({
        userId: req.user.id,
        recommendationId: feedbackDto.recommendationId,
        recommendationType: feedbackDto.recommendationType,
        feedbackType: feedbackDto.feedbackType,
        comment: feedbackDto.comment,
        rating: feedbackDto.rating,
        metadata: feedbackDto.metadata,
      });

      return result;
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement du feedback: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de l\'enregistrement du feedback',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Enregistrer un feedback utilisateur détaillé
   */
  @Post('detailed')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Enregistrer un feedback utilisateur détaillé sur une recommandation' })
  @ApiResponse({ status: 201, description: 'Feedback détaillé enregistré avec succès', type: FeedbackResponseDto })
  @ApiResponse({ status: 400, description: 'Données de requête invalides' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async recordDetailedFeedback(
    @Body() feedbackDto: DetailedFeedbackDto,
    @Req() req: RequestWithUser,
  ) {
    try {
      this.logger.log(`Enregistrement d'un feedback détaillé pour la recommandation ${feedbackDto.recommendationId}`);

      const result = await this.feedbackService.recordDetailedFeedback({
        userId: req.user.id,
        recommendationId: feedbackDto.recommendationId,
        recommendationType: feedbackDto.recommendationType,
        feedbackType: feedbackDto.feedbackType,
        comment: feedbackDto.comment,
        rating: feedbackDto.rating,
        aspects: feedbackDto.aspects,
        isUseful: feedbackDto.isUseful,
        hasActedUpon: feedbackDto.hasActedUpon,
        suggestions: feedbackDto.suggestions,
        metadata: feedbackDto.metadata,
      });

      return result;
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement du feedback détaillé: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de l\'enregistrement du feedback détaillé',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Signaler une recommandation inappropriée
   */
  @Post('report')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Signaler une recommandation inappropriée' })
  @ApiResponse({ status: 201, description: 'Signalement enregistré avec succès', type: FeedbackResponseDto })
  @ApiResponse({ status: 400, description: 'Données de requête invalides' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async reportRecommendation(
    @Body() reportDto: ReportRecommendationDto,
    @Req() req: RequestWithUser,
  ) {
    try {
      this.logger.log(`Signalement d'une recommandation ${reportDto.recommendationId} pour la raison ${reportDto.reason}`);

      const result = await this.feedbackService.reportRecommendation({
        userId: req.user.id,
        recommendationId: reportDto.recommendationId,
        recommendationType: reportDto.recommendationType,
        reason: reportDto.reason as ReportReason,
        description: reportDto.description,
        metadata: reportDto.metadata,
      });

      return result;
    } catch (error) {
      this.logger.error(`Erreur lors du signalement de la recommandation: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors du signalement de la recommandation',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupérer les feedbacks d'un utilisateur pour une recommandation
   */
  @Get(':type/:id')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Récupérer les feedbacks d\'un utilisateur pour une recommandation' })
  @ApiParam({ name: 'type', description: 'Type de recommandation', enum: RecommendationType })
  @ApiParam({ name: 'id', description: 'ID de la recommandation' })
  @ApiResponse({ status: 200, description: 'Feedbacks récupérés avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async getUserFeedbackForRecommendation(
    @Param('type') type: RecommendationType,
    @Param('id') id: string,
    @Req() req: RequestWithUser,
  ) {
    try {
      this.logger.log(`Récupération des feedbacks pour la recommandation ${id} de type ${type}`);

      const feedbacks = await this.feedbackService.getUserFeedbackForRecommendation(
        req.user.id,
        id,
        type,
      );

      return {
        success: true,
        data: feedbacks,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des feedbacks: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de la récupération des feedbacks',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupérer tous les feedbacks d'un utilisateur
   */
  @Get()
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Récupérer tous les feedbacks d\'un utilisateur' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'feedbackType', required: false, enum: FeedbackType, description: 'Type de feedback' })
  @ApiQuery({ name: 'recommendationType', required: false, enum: RecommendationType, description: 'Type de recommandation' })
  @ApiResponse({ status: 200, description: 'Feedbacks récupérés avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async getUserFeedbacks(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('feedbackType') feedbackType?: FeedbackType,
    @Query('recommendationType') recommendationType?: RecommendationType,
    @Req() req: RequestWithUser,
  ) {
    try {
      this.logger.log(`Récupération des feedbacks de l'utilisateur ${req.user.id}`);

      const result = await this.feedbackService.getUserFeedbacks(
        req.user.id,
        {
          page,
          limit,
          feedbackType,
          recommendationType,
        },
      );

      return {
        success: true,
        ...result,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des feedbacks: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de la récupération des feedbacks',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Supprimer un feedback
   */
  @Delete(':id')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Supprimer un feedback' })
  @ApiParam({ name: 'id', description: 'ID du feedback' })
  @ApiResponse({ status: 200, description: 'Feedback supprimé avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  @ApiResponse({ status: 404, description: 'Feedback non trouvé' })
  async deleteFeedback(
    @Param('id') id: string,
    @Req() req: RequestWithUser,
  ) {
    try {
      this.logger.log(`Suppression du feedback ${id}`);

      const result = await this.feedbackService.deleteFeedback(id, req.user.id);

      return result;
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression du feedback: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de la suppression du feedback',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupérer les signalements pour une recommandation
   */
  @Get('reports/:type/:id')
  @Roles('ADMIN', 'MODERATOR')
  @ApiOperation({ summary: 'Récupérer les signalements pour une recommandation' })
  @ApiParam({ name: 'type', description: 'Type de recommandation', enum: RecommendationType })
  @ApiParam({ name: 'id', description: 'ID de la recommandation' })
  @ApiResponse({ status: 200, description: 'Liste des signalements' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async getReportsForRecommendation(
    @Param('id') id: string,
    @Param('type') type: RecommendationType,
    @Req() req: RequestWithUser,
  ) {
    try {
      this.logger.log(`Récupération des signalements pour la recommandation ${id} de type ${type}`);

      // Vérifier que l'utilisateur a le rôle ADMIN ou MODERATOR
      if (!req.user.roles.includes('ADMIN') && !req.user.roles.includes('MODERATOR')) {
        throw new HttpException('Accès interdit', HttpStatus.FORBIDDEN);
      }

      const reports = await this.feedbackService.getReportsForRecommendation(id, type);

      return {
        success: true,
        data: reports,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des signalements: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de la récupération des signalements',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
