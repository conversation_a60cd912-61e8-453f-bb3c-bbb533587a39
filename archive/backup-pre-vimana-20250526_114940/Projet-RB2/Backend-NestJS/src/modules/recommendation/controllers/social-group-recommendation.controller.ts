import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { GroupRecommendationService, GroupAggregationStrategy } from '../services/group-recommendation.service';
import { SocialRecommendationService } from '../services/social-recommendation.service';
import { CollaborativePlanningService } from '../services/collaborative-planning.service';

/**
 * DTO pour les options de recommandation de groupe
 */
class GroupRecommendationOptionsDto {
  /** IDs des utilisateurs du groupe */
  userIds: string[];
  
  /** Stratégie d'agrégation */
  aggregationStrategy?: GroupAggregationStrategy;
  
  /** Poids des utilisateurs (pour la stratégie pondérée) */
  userWeights?: Record<string, number>;
  
  /** Nombre maximum de recommandations */
  maxRecommendations?: number;
  
  /** Seuil de satisfaction minimum */
  minSatisfactionThreshold?: number;
  
  /** Filtres supplémentaires */
  filters?: Record<string, any>;
}

/**
 * DTO pour les options de recommandation sociale
 */
class SocialRecommendationOptionsDto {
  /** Facteur d'influence sociale (0-1) */
  socialFactor?: number;
  
  /** Nombre maximum de recommandations */
  maxRecommendations?: number;
  
  /** Types de relations à considérer */
  relationTypes?: Array<'friend' | 'follower' | 'following' | 'colleague' | 'family'>;
  
  /** Profondeur du réseau social (1-3) */
  networkDepth?: number;
  
  /** Filtres supplémentaires */
  filters?: Record<string, any>;
}

/**
 * DTO pour les options de création de plan collaboratif
 */
class CreateCollaborativePlanDto {
  /** Titre du plan */
  title: string;
  
  /** Description du plan */
  description?: string;
  
  /** IDs des participants */
  participantIds: string[];
  
  /** Date de début */
  startDate?: Date;
  
  /** Date de fin */
  endDate?: Date;
  
  /** Stratégie d'agrégation */
  aggregationStrategy?: GroupAggregationStrategy;
  
  /** Poids des utilisateurs */
  userWeights?: Record<string, number>;
  
  /** Nombre maximum de recommandations */
  maxRecommendations?: number;
  
  /** Filtres supplémentaires */
  filters?: Record<string, any>;
}

/**
 * DTO pour les options de vote
 */
class VoteOptionsDto {
  /** ID de la recommandation */
  recommendationId: string;
  
  /** Type de vote */
  voteType: 'upvote' | 'downvote' | 'neutral';
  
  /** Commentaire */
  comment?: string;
}

/**
 * DTO pour les options de finalisation de plan
 */
class FinalizePlanOptionsDto {
  /** IDs des recommandations sélectionnées */
  selectedRecommendationIds: string[];
  
  /** Commentaire */
  comment?: string;
}

/**
 * Contrôleur pour les recommandations de groupe et sociales
 */
@ApiTags('Social and Group Recommendations')
@Controller('recommendation/social-group')
export class SocialGroupRecommendationController {
  private readonly logger = new Logger(SocialGroupRecommendationController.name);
  
  constructor(
    private readonly groupRecommendationService: GroupRecommendationService,
    private readonly socialRecommendationService: SocialRecommendationService,
    private readonly collaborativePlanningService: CollaborativePlanningService,
  ) {}
  
  /**
   * Récupère des recommandations de groupe
   * @param options Options de recommandation de groupe
   * @returns Recommandations de groupe
   */
  @Post('group')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer des recommandations de groupe' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recommandations récupérées avec succès',
  })
  async getGroupRecommendations(
    @Body() options: GroupRecommendationOptionsDto,
  ): Promise<any[]> {
    this.logger.log(`Récupération des recommandations de groupe pour ${options.userIds.length} utilisateurs`);
    return this.groupRecommendationService.getGroupRecommendations(options);
  }
  
  /**
   * Récupère des recommandations sociales pour l'utilisateur courant
   * @param user Utilisateur courant
   * @param options Options de recommandation sociale
   * @returns Recommandations sociales
   */
  @Post('social')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer des recommandations sociales pour l\'utilisateur courant' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recommandations récupérées avec succès',
  })
  async getSocialRecommendations(
    @CurrentUser('id') userId: string,
    @Body() options: SocialRecommendationOptionsDto,
  ): Promise<any[]> {
    this.logger.log(`Récupération des recommandations sociales pour l'utilisateur ${userId}`);
    return this.socialRecommendationService.getSocialRecommendations({
      userId,
      ...options,
    });
  }
  
  /**
   * Récupère des recommandations sociales pour l'utilisateur courant (GET)
   * @param user Utilisateur courant
   * @param socialFactor Facteur d'influence sociale
   * @param maxRecommendations Nombre maximum de recommandations
   * @returns Recommandations sociales
   */
  @Get('my-social')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer des recommandations sociales pour l\'utilisateur courant' })
  @ApiQuery({ name: 'socialFactor', required: false, type: Number })
  @ApiQuery({ name: 'maxRecommendations', required: false, type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recommandations récupérées avec succès',
  })
  async getMySocialRecommendations(
    @CurrentUser('id') userId: string,
    @Query('socialFactor') socialFactor?: number,
    @Query('maxRecommendations') maxRecommendations?: number,
  ): Promise<any[]> {
    this.logger.log(`Récupération des recommandations sociales pour l'utilisateur courant ${userId}`);
    
    const options: SocialRecommendationOptionsDto = {
      socialFactor: socialFactor !== undefined ? parseFloat(socialFactor.toString()) : undefined,
      maxRecommendations: maxRecommendations !== undefined ? parseInt(maxRecommendations.toString(), 10) : undefined,
    };
    
    return this.socialRecommendationService.getSocialRecommendations({
      userId,
      ...options,
    });
  }
  
  /**
   * Crée un plan collaboratif
   * @param user Utilisateur courant
   * @param options Options de création de plan collaboratif
   * @returns Plan collaboratif
   */
  @Post('collaborative-plan')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Créer un plan collaboratif' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Plan créé avec succès',
  })
  async createCollaborativePlan(
    @CurrentUser('id') userId: string,
    @Body() options: CreateCollaborativePlanDto,
  ): Promise<any> {
    this.logger.log(`Création d'un plan collaboratif par l'utilisateur ${userId}`);
    return this.collaborativePlanningService.createCollaborativePlan({
      ...options,
      creatorId: userId,
    });
  }
  
  /**
   * Récupère un plan collaboratif
   * @param planId ID du plan
   * @returns Plan collaboratif
   */
  @Get('collaborative-plan/:planId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer un plan collaboratif' })
  @ApiParam({ name: 'planId', description: 'ID du plan collaboratif' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Plan récupéré avec succès',
  })
  async getCollaborativePlan(
    @Param('planId') planId: string,
  ): Promise<any> {
    this.logger.log(`Récupération du plan collaboratif ${planId}`);
    return this.collaborativePlanningService.getCollaborativePlan(planId);
  }
  
  /**
   * Récupère les plans collaboratifs de l'utilisateur courant
   * @param user Utilisateur courant
   * @returns Plans collaboratifs
   */
  @Get('my-collaborative-plans')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les plans collaboratifs de l\'utilisateur courant' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Plans récupérés avec succès',
  })
  async getMyCollaborativePlans(
    @CurrentUser('id') userId: string,
  ): Promise<any[]> {
    this.logger.log(`Récupération des plans collaboratifs de l'utilisateur ${userId}`);
    return this.collaborativePlanningService.getUserCollaborativePlans(userId);
  }
  
  /**
   * Vote pour une recommandation dans un plan collaboratif
   * @param planId ID du plan
   * @param user Utilisateur courant
   * @param options Options de vote
   * @returns Vote
   */
  @Post('collaborative-plan/:planId/vote')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Voter pour une recommandation dans un plan collaboratif' })
  @ApiParam({ name: 'planId', description: 'ID du plan collaboratif' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Vote enregistré avec succès',
  })
  async voteForRecommendation(
    @Param('planId') planId: string,
    @CurrentUser('id') userId: string,
    @Body() options: VoteOptionsDto,
  ): Promise<any> {
    this.logger.log(`Vote de l'utilisateur ${userId} pour la recommandation ${options.recommendationId} dans le plan ${planId}`);
    return this.collaborativePlanningService.voteForRecommendation({
      planId,
      userId,
      recommendationId: options.recommendationId,
      voteType: options.voteType,
      comment: options.comment,
    });
  }
  
  /**
   * Finalise un plan collaboratif
   * @param planId ID du plan
   * @param user Utilisateur courant
   * @param options Options de finalisation
   * @returns Plan finalisé
   */
  @Post('collaborative-plan/:planId/finalize')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Finaliser un plan collaboratif' })
  @ApiParam({ name: 'planId', description: 'ID du plan collaboratif' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Plan finalisé avec succès',
  })
  async finalizePlan(
    @Param('planId') planId: string,
    @CurrentUser('id') userId: string,
    @Body() options: FinalizePlanOptionsDto,
  ): Promise<any> {
    this.logger.log(`Finalisation du plan collaboratif ${planId} par l'utilisateur ${userId}`);
    return this.collaborativePlanningService.finalizePlan({
      planId,
      userId,
      selectedRecommendationIds: options.selectedRecommendationIds,
      comment: options.comment,
    });
  }
  
  /**
   * Annule un plan collaboratif
   * @param planId ID du plan
   * @param user Utilisateur courant
   * @param reason Raison de l'annulation
   * @returns Plan annulé
   */
  @Post('collaborative-plan/:planId/cancel')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Annuler un plan collaboratif' })
  @ApiParam({ name: 'planId', description: 'ID du plan collaboratif' })
  @ApiQuery({ name: 'reason', required: false, type: String })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Plan annulé avec succès',
  })
  async cancelPlan(
    @Param('planId') planId: string,
    @CurrentUser('id') userId: string,
    @Query('reason') reason?: string,
  ): Promise<any> {
    this.logger.log(`Annulation du plan collaboratif ${planId} par l'utilisateur ${userId}`);
    return this.collaborativePlanningService.cancelPlan(planId, userId, reason);
  }
}
