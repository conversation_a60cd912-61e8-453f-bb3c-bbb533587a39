import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { DiversityFilterService, DiversityMetrics, DiversificationOptions } from '../services/diversity-filter.service';
import { FairnessService, FairnessMetrics, FairnessOptions } from '../services/fairness.service';
import { RecommendationService } from '../services/recommendation.service';

/**
 * DTO pour les options de diversification
 */
class DiversificationOptionsDto implements Partial<DiversificationOptions> {
  /** Facteur d'équilibre entre pertinence et diversité (0-1) */
  diversityFactor?: number;
  
  /** Taille maximale de la liste de recommandations */
  maxRecommendations?: number;
  
  /** Inclure des recommandations surprenantes */
  includeSurprising?: boolean;
  
  /** Nombre de recommandations surprenantes à inclure */
  surprisingCount?: number;
}

/**
 * DTO pour les options d'équité
 */
class FairnessOptionsDto implements Partial<FairnessOptions> {
  /** Facteur d'équilibre entre pertinence et équité (0-1) */
  fairnessFactor?: number;
  
  /** Taille maximale de la liste de recommandations */
  maxRecommendations?: number;
  
  /** Appliquer une correction d'équité */
  applyFairnessCorrection?: boolean;
}

/**
 * Contrôleur pour la diversité et l'équité des recommandations
 */
@ApiTags('Diversity and Fairness')
@Controller('recommendation/diversity-fairness')
export class DiversityFairnessController {
  private readonly logger = new Logger(DiversityFairnessController.name);
  
  constructor(
    private readonly diversityService: DiversityFilterService,
    private readonly fairnessService: FairnessService,
    private readonly recommendationService: RecommendationService,
  ) {}
  
  /**
   * Récupère les métriques de diversité pour un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Métriques de diversité
   */
  @Get('diversity-metrics/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les métriques de diversité pour un utilisateur' })
  @ApiParam({ name: 'userId', description: 'ID de l\'utilisateur' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Métriques de diversité récupérées avec succès',
  })
  async getDiversityMetrics(
    @Param('userId') userId: string,
  ): Promise<DiversityMetrics> {
    this.logger.log(`Récupération des métriques de diversité pour l'utilisateur ${userId}`);
    
    // Récupérer les recommandations pour l'utilisateur
    const recommendations = await this.recommendationService.getRecommendationsForUser(userId);
    
    // Calculer les métriques de diversité
    return this.diversityService.calculateDiversityMetrics(recommendations);
  }
  
  /**
   * Récupère les métriques d'équité pour un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Métriques d'équité
   */
  @Get('fairness-metrics/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les métriques d\'équité pour un utilisateur' })
  @ApiParam({ name: 'userId', description: 'ID de l\'utilisateur' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Métriques d\'équité récupérées avec succès',
  })
  async getFairnessMetrics(
    @Param('userId') userId: string,
  ): Promise<FairnessMetrics> {
    this.logger.log(`Récupération des métriques d'équité pour l'utilisateur ${userId}`);
    
    // Récupérer les recommandations pour l'utilisateur
    const recommendations = await this.recommendationService.getRecommendationsForUser(userId);
    
    // Calculer les métriques d'équité
    return this.fairnessService.calculateFairnessMetrics(recommendations);
  }
  
  /**
   * Applique la diversification aux recommandations d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param options Options de diversification
   * @returns Recommandations diversifiées
   */
  @Post('diversify/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Appliquer la diversification aux recommandations d\'un utilisateur' })
  @ApiParam({ name: 'userId', description: 'ID de l\'utilisateur' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recommandations diversifiées avec succès',
  })
  async diversifyRecommendations(
    @Param('userId') userId: string,
    @Body() options: DiversificationOptionsDto,
  ): Promise<any[]> {
    this.logger.log(`Diversification des recommandations pour l'utilisateur ${userId}`);
    
    // Récupérer les recommandations pour l'utilisateur
    const recommendations = await this.recommendationService.getRecommendationsForUser(userId);
    
    // Appliquer la diversification
    return this.diversityService.diversifyRecommendations(userId, recommendations, options);
  }
  
  /**
   * Applique l'équité aux recommandations d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param options Options d'équité
   * @returns Recommandations équitables
   */
  @Post('apply-fairness/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Appliquer l\'équité aux recommandations d\'un utilisateur' })
  @ApiParam({ name: 'userId', description: 'ID de l\'utilisateur' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recommandations équitables avec succès',
  })
  async applyFairness(
    @Param('userId') userId: string,
    @Body() options: FairnessOptionsDto,
  ): Promise<any[]> {
    this.logger.log(`Application de l'équité aux recommandations pour l'utilisateur ${userId}`);
    
    // Récupérer les recommandations pour l'utilisateur
    const recommendations = await this.recommendationService.getRecommendationsForUser(userId);
    
    // Appliquer l'équité
    return this.fairnessService.applyFairness(userId, recommendations, options);
  }
  
  /**
   * Récupère des recommandations diversifiées et équitables pour l'utilisateur courant
   * @param user Utilisateur courant
   * @param diversityFactor Facteur de diversité
   * @param fairnessFactor Facteur d'équité
   * @param includeSurprising Inclure des recommandations surprenantes
   * @returns Recommandations diversifiées et équitables
   */
  @Get('recommendations')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer des recommandations diversifiées et équitables' })
  @ApiQuery({ name: 'diversityFactor', required: false, type: Number })
  @ApiQuery({ name: 'fairnessFactor', required: false, type: Number })
  @ApiQuery({ name: 'includeSurprising', required: false, type: Boolean })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recommandations récupérées avec succès',
  })
  async getDiverseFairRecommendations(
    @CurrentUser('id') userId: string,
    @Query('diversityFactor') diversityFactor?: number,
    @Query('fairnessFactor') fairnessFactor?: number,
    @Query('includeSurprising') includeSurprising?: boolean,
  ): Promise<any[]> {
    this.logger.log(`Récupération de recommandations diversifiées et équitables pour l'utilisateur ${userId}`);
    
    // Récupérer les recommandations pour l'utilisateur
    const recommendations = await this.recommendationService.getRecommendationsForUser(userId);
    
    // Appliquer la diversification
    const diversifiedRecommendations = await this.diversityService.diversifyRecommendations(
      userId,
      recommendations,
      {
        diversityFactor,
        includeSurprising,
      },
    );
    
    // Appliquer l'équité
    return this.fairnessService.applyFairness(
      userId,
      diversifiedRecommendations,
      {
        fairnessFactor,
      },
    );
  }
  
  /**
   * Récupère des recommandations surprenantes pour l'utilisateur courant
   * @param user Utilisateur courant
   * @param count Nombre de recommandations à récupérer
   * @returns Recommandations surprenantes
   */
  @Get('discover')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer des recommandations surprenantes' })
  @ApiQuery({ name: 'count', required: false, type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recommandations surprenantes récupérées avec succès',
  })
  async getDiscoverRecommendations(
    @CurrentUser('id') userId: string,
    @Query('count') count?: number,
  ): Promise<any[]> {
    this.logger.log(`Récupération de recommandations surprenantes pour l'utilisateur ${userId}`);
    
    // Récupérer les recommandations pour l'utilisateur
    const recommendations = await this.recommendationService.getRecommendationsForUser(userId);
    
    // Appliquer la diversification avec un fort facteur de diversité et des recommandations surprenantes
    return this.diversityService.diversifyRecommendations(
      userId,
      recommendations,
      {
        diversityFactor: 0.8,
        includeSurprising: true,
        surprisingCount: count || 5,
        maxRecommendations: count || 5,
      },
    );
  }
}
