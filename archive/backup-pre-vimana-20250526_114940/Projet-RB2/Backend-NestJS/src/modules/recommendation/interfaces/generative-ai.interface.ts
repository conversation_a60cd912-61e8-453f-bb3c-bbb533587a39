/**
 * Interfaces for the generative AI integration
 */

/**
 * Generative AI configuration
 */
export interface GenerativeAIConfig {
  /**
   * Enable/disable generative AI features
   */
  enabled: boolean;

  /**
   * AI model configuration
   */
  modelConfig: AIModelConfig;

  /**
   * Explanation generation configuration
   */
  explanationConfig: ExplanationGenerationConfig;

  /**
   * Content generation configuration
   */
  contentGenerationConfig: ContentGenerationConfig;

  /**
   * NLP recommendation configuration
   */
  nlpRecommendationConfig: NLPRecommendationConfig;

  /**
   * Rate limiting configuration
   */
  rateLimiting: AIRateLimitingConfig;

  /**
   * Additional AI parameters
   */
  params?: Record<string, any>;
}

/**
 * AI model configuration
 */
export interface AIModelConfig {
  /**
   * Model provider
   */
  provider: AIModelProvider;

  /**
   * Model name/version
   */
  model: string;

  /**
   * API key (if applicable)
   */
  apiKey?: string;

  /**
   * Model endpoint URL
   */
  endpoint?: string;

  /**
   * Model parameters
   */
  parameters?: Record<string, any>;

  /**
   * Fallback model configuration
   */
  fallback?: {
    /**
     * Fallback model provider
     */
    provider: AIModelProvider;

    /**
     * Fallback model name/version
     */
    model: string;
  };
}

/**
 * AI model provider
 */
export enum AIModelProvider {
  /**
   * OpenAI
   */
  OPENAI = 'OPENAI',

  /**
   * Google
   */
  GOOGLE = 'GOOGLE',

  /**
   * Anthropic
   */
  ANTHROPIC = 'ANTHROPIC',

  /**
   * Hugging Face
   */
  HUGGINGFACE = 'HUGGINGFACE',

  /**
   * Local model
   */
  LOCAL = 'LOCAL',

  /**
   * Custom provider
   */
  CUSTOM = 'CUSTOM',
}

/**
 * Explanation generation configuration
 */
export interface ExplanationGenerationConfig {
  /**
   * Enable/disable explanation generation
   */
  enabled: boolean;

  /**
   * Explanation types to generate
   */
  types: ExplanationGenerationType[];

  /**
   * Explanation tone
   */
  tone: ExplanationTone;

  /**
   * Explanation length
   */
  length: ExplanationLength;

  /**
   * Explanation personalization level
   */
  personalization: ExplanationPersonalization;

  /**
   * Explanation templates
   */
  templates?: Record<string, string>;
}

/**
 * Explanation generation type
 */
export enum ExplanationGenerationType {
  /**
   * Feature-based explanation
   */
  FEATURE_BASED = 'FEATURE_BASED',

  /**
   * User history-based explanation
   */
  HISTORY_BASED = 'HISTORY_BASED',

  /**
   * Social-based explanation
   */
  SOCIAL_BASED = 'SOCIAL_BASED',

  /**
   * Context-based explanation
   */
  CONTEXT_BASED = 'CONTEXT_BASED',

  /**
   * Narrative explanation
   */
  NARRATIVE = 'NARRATIVE',
}

/**
 * Explanation tone
 */
export enum ExplanationTone {
  /**
   * Formal tone
   */
  FORMAL = 'FORMAL',

  /**
   * Casual tone
   */
  CASUAL = 'CASUAL',

  /**
   * Enthusiastic tone
   */
  ENTHUSIASTIC = 'ENTHUSIASTIC',

  /**
   * Professional tone
   */
  PROFESSIONAL = 'PROFESSIONAL',

  /**
   * Friendly tone
   */
  FRIENDLY = 'FRIENDLY',
}

/**
 * Explanation length
 */
export enum ExplanationLength {
  /**
   * Brief explanation
   */
  BRIEF = 'BRIEF',

  /**
   * Detailed explanation
   */
  DETAILED = 'DETAILED',

  /**
   * Comprehensive explanation
   */
  COMPREHENSIVE = 'COMPREHENSIVE',
}

/**
 * Explanation personalization
 */
export enum ExplanationPersonalization {
  /**
   * Generic personalization
   */
  GENERIC = 'GENERIC',

  /**
   * Moderate personalization
   */
  MODERATE = 'MODERATE',

  /**
   * High personalization
   */
  HIGH = 'HIGH',
}

/**
 * Content generation configuration
 */
export interface ContentGenerationConfig {
  /**
   * Enable/disable content generation
   */
  enabled: boolean;

  /**
   * Content types to generate
   */
  types: ContentGenerationType[];

  /**
   * Maximum content length
   */
  maxLength: number;

  /**
   * Content generation triggers
   */
  triggers: ContentGenerationTrigger[];
}

/**
 * Content generation type
 */
export enum ContentGenerationType {
  /**
   * Item descriptions
   */
  DESCRIPTION = 'DESCRIPTION',

  /**
   * Item summaries
   */
  SUMMARY = 'SUMMARY',

  /**
   * Item highlights
   */
  HIGHLIGHTS = 'HIGHLIGHTS',

  /**
   * Personalized tips
   */
  TIPS = 'TIPS',

  /**
   * Related content
   */
  RELATED = 'RELATED',
}

/**
 * Content generation trigger
 */
export enum ContentGenerationTrigger {
  /**
   * On recommendation
   */
  ON_RECOMMENDATION = 'ON_RECOMMENDATION',

  /**
   * On item view
   */
  ON_ITEM_VIEW = 'ON_ITEM_VIEW',

  /**
   * On user request
   */
  ON_USER_REQUEST = 'ON_USER_REQUEST',

  /**
   * Scheduled
   */
  SCHEDULED = 'SCHEDULED',
}

/**
 * NLP recommendation configuration
 */
export interface NLPRecommendationConfig {
  /**
   * Enable/disable NLP recommendations
   */
  enabled: boolean;

  /**
   * NLP processing strategies
   */
  strategies: NLPProcessingStrategy[];

  /**
   * Text sources to analyze
   */
  textSources: NLPTextSource[];

  /**
   * Language support
   */
  languages: string[];
}

/**
 * NLP processing strategy
 */
export enum NLPProcessingStrategy {
  /**
   * Keyword extraction
   */
  KEYWORD_EXTRACTION = 'KEYWORD_EXTRACTION',

  /**
   * Sentiment analysis
   */
  SENTIMENT_ANALYSIS = 'SENTIMENT_ANALYSIS',

  /**
   * Entity recognition
   */
  ENTITY_RECOGNITION = 'ENTITY_RECOGNITION',

  /**
   * Topic modeling
   */
  TOPIC_MODELING = 'TOPIC_MODELING',

  /**
   * Semantic similarity
   */
  SEMANTIC_SIMILARITY = 'SEMANTIC_SIMILARITY',
}

/**
 * NLP text source
 */
export enum NLPTextSource {
  /**
   * User reviews
   */
  USER_REVIEWS = 'USER_REVIEWS',

  /**
   * Item descriptions
   */
  ITEM_DESCRIPTIONS = 'ITEM_DESCRIPTIONS',

  /**
   * User queries
   */
  USER_QUERIES = 'USER_QUERIES',

  /**
   * User feedback
   */
  USER_FEEDBACK = 'USER_FEEDBACK',

  /**
   * Social media
   */
  SOCIAL_MEDIA = 'SOCIAL_MEDIA',
}

/**
 * AI rate limiting configuration
 */
export interface AIRateLimitingConfig {
  /**
   * Enable/disable rate limiting
   */
  enabled: boolean;

  /**
   * Maximum requests per minute
   */
  requestsPerMinute: number;

  /**
   * Maximum tokens per request
   */
  maxTokensPerRequest: number;

  /**
   * Maximum tokens per day
   */
  maxTokensPerDay: number;
}
