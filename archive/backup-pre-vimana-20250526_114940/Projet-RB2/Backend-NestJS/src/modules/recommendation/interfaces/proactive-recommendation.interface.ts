/**
 * Interfaces for the proactive recommendation system
 */

/**
 * Proactive recommendation configuration
 */
export interface ProactiveRecommendationConfig {
  /**
   * Enable/disable proactive recommendations
   */
  enabled: boolean;

  /**
   * Needs prediction configuration
   */
  needsPrediction: NeedsPredictionConfig;

  /**
   * Intelligent notification configuration
   */
  intelligentNotification: IntelligentNotificationConfig;

  /**
   * Anticipatory recommendation configuration
   */
  anticipatoryRecommendation: AnticipatoryRecommendationConfig;

  /**
   * User interruption configuration
   */
  userInterruption: UserInterruptionConfig;

  /**
   * Additional proactive parameters
   */
  params?: Record<string, any>;
}

/**
 * Needs prediction configuration
 */
export interface NeedsPredictionConfig {
  /**
   * Enable/disable needs prediction
   */
  enabled: boolean;

  /**
   * Prediction strategies
   */
  strategies: NeedsPredictionStrategy[];

  /**
   * Prediction confidence threshold
   */
  confidenceThreshold: number;

  /**
   * Prediction horizon (in days)
   */
  predictionHorizon: number;

  /**
   * User needs categories
   */
  needsCategories: NeedsCategory[];
}

/**
 * Needs prediction strategy
 */
export enum NeedsPredictionStrategy {
  /**
   * Pattern-based prediction
   */
  PATTERN_BASED = 'PATTERN_BASED',

  /**
   * Time-based prediction
   */
  TIME_BASED = 'TIME_BASED',

  /**
   * Event-based prediction
   */
  EVENT_BASED = 'EVENT_BASED',

  /**
   * Collaborative prediction
   */
  COLLABORATIVE = 'COLLABORATIVE',

  /**
   * ML-based prediction
   */
  ML_BASED = 'ML_BASED',
}

/**
 * Needs category
 */
export interface NeedsCategory {
  /**
   * Category ID
   */
  id: string;

  /**
   * Category name
   */
  name: string;

  /**
   * Category description
   */
  description: string;

  /**
   * Category priority
   */
  priority: number;

  /**
   * Category indicators
   */
  indicators: string[];
}

/**
 * Intelligent notification configuration
 */
export interface IntelligentNotificationConfig {
  /**
   * Enable/disable intelligent notifications
   */
  enabled: boolean;

  /**
   * Notification channels
   */
  channels: NotificationChannel[];

  /**
   * Notification frequency limits
   */
  frequencyLimits: NotificationFrequencyLimits;

  /**
   * Notification timing strategy
   */
  timingStrategy: NotificationTimingStrategy;

  /**
   * Notification content strategy
   */
  contentStrategy: NotificationContentStrategy;

  /**
   * Notification priority levels
   */
  priorityLevels: NotificationPriorityLevel[];
}

/**
 * Notification channel
 */
export interface NotificationChannel {
  /**
   * Channel type
   */
  type: NotificationChannelType;

  /**
   * Channel enabled/disabled
   */
  enabled: boolean;

  /**
   * Channel priority
   */
  priority: number;

  /**
   * Channel configuration
   */
  config?: Record<string, any>;
}

/**
 * Notification channel type
 */
export enum NotificationChannelType {
  /**
   * In-app notification
   */
  IN_APP = 'IN_APP',

  /**
   * Push notification
   */
  PUSH = 'PUSH',

  /**
   * Email notification
   */
  EMAIL = 'EMAIL',

  /**
   * SMS notification
   */
  SMS = 'SMS',

  /**
   * Webhook notification
   */
  WEBHOOK = 'WEBHOOK',
}

/**
 * Notification frequency limits
 */
export interface NotificationFrequencyLimits {
  /**
   * Maximum notifications per day
   */
  maxPerDay: number;

  /**
   * Maximum notifications per week
   */
  maxPerWeek: number;

  /**
   * Minimum time between notifications (in minutes)
   */
  minTimeBetween: number;

  /**
   * Per-channel limits
   */
  perChannel?: Record<NotificationChannelType, {
    maxPerDay: number;
    maxPerWeek: number;
    minTimeBetween: number;
  }>;
}

/**
 * Notification timing strategy
 */
export enum NotificationTimingStrategy {
  /**
   * Immediate notification
   */
  IMMEDIATE = 'IMMEDIATE',

  /**
   * User activity-based timing
   */
  USER_ACTIVITY = 'USER_ACTIVITY',

  /**
   * Time of day-based timing
   */
  TIME_OF_DAY = 'TIME_OF_DAY',

  /**
   * ML-optimized timing
   */
  ML_OPTIMIZED = 'ML_OPTIMIZED',
}

/**
 * Notification content strategy
 */
export enum NotificationContentStrategy {
  /**
   * Static content
   */
  STATIC = 'STATIC',

  /**
   * Template-based content
   */
  TEMPLATE = 'TEMPLATE',

  /**
   * Personalized content
   */
  PERSONALIZED = 'PERSONALIZED',

  /**
   * AI-generated content
   */
  AI_GENERATED = 'AI_GENERATED',
}

/**
 * Notification priority level
 */
export interface NotificationPriorityLevel {
  /**
   * Level ID
   */
  id: string;

  /**
   * Level name
   */
  name: string;

  /**
   * Level description
   */
  description: string;

  /**
   * Level value
   */
  value: number;

  /**
   * Level configuration
   */
  config?: Record<string, any>;
}

/**
 * Anticipatory recommendation configuration
 */
export interface AnticipatoryRecommendationConfig {
  /**
   * Enable/disable anticipatory recommendations
   */
  enabled: boolean;

  /**
   * Anticipation strategies
   */
  strategies: AnticipationStrategy[];

  /**
   * Anticipation horizon (in days)
   */
  anticipationHorizon: number;

  /**
   * Anticipation confidence threshold
   */
  confidenceThreshold: number;

  /**
   * Anticipation triggers
   */
  triggers: AnticipationTrigger[];
}

/**
 * Anticipation strategy
 */
export enum AnticipationStrategy {
  /**
   * Sequence-based anticipation
   */
  SEQUENCE_BASED = 'SEQUENCE_BASED',

  /**
   * Calendar-based anticipation
   */
  CALENDAR_BASED = 'CALENDAR_BASED',

  /**
   * Event-based anticipation
   */
  EVENT_BASED = 'EVENT_BASED',

  /**
   * Trend-based anticipation
   */
  TREND_BASED = 'TREND_BASED',

  /**
   * ML-based anticipation
   */
  ML_BASED = 'ML_BASED',
}

/**
 * Anticipation trigger
 */
export enum AnticipationTrigger {
  /**
   * User login
   */
  USER_LOGIN = 'USER_LOGIN',

  /**
   * Scheduled trigger
   */
  SCHEDULED = 'SCHEDULED',

  /**
   * Event occurrence
   */
  EVENT = 'EVENT',

  /**
   * Context change
   */
  CONTEXT_CHANGE = 'CONTEXT_CHANGE',

  /**
   * User request
   */
  USER_REQUEST = 'USER_REQUEST',
}

/**
 * User interruption configuration
 */
export interface UserInterruptionConfig {
  /**
   * Enable/disable user interruption control
   */
  enabled: boolean;

  /**
   * Interruption sensitivity
   */
  sensitivity: InterruptionSensitivity;

  /**
   * Do not disturb periods
   */
  doNotDisturbPeriods: DoNotDisturbPeriod[];

  /**
   * User override enabled/disabled
   */
  userOverride: boolean;
}

/**
 * Interruption sensitivity
 */
export enum InterruptionSensitivity {
  /**
   * Low sensitivity
   */
  LOW = 'LOW',

  /**
   * Medium sensitivity
   */
  MEDIUM = 'MEDIUM',

  /**
   * High sensitivity
   */
  HIGH = 'HIGH',
}

/**
 * Do not disturb period
 */
export interface DoNotDisturbPeriod {
  /**
   * Period ID
   */
  id: string;

  /**
   * Period name
   */
  name: string;

  /**
   * Start time (HH:MM)
   */
  startTime: string;

  /**
   * End time (HH:MM)
   */
  endTime: string;

  /**
   * Days of week (0-6, 0 = Sunday)
   */
  daysOfWeek: number[];

  /**
   * Period enabled/disabled
   */
  enabled: boolean;
}

/**
 * Proactive recommendation
 */
export interface ProactiveRecommendation {
  /**
   * Recommendation ID
   */
  id: string;

  /**
   * User ID
   */
  userId: string;

  /**
   * Recommended items
   */
  items: any[];

  /**
   * Recommendation reason
   */
  reason: ProactiveRecommendationReason;

  /**
   * Recommendation confidence
   */
  confidence: number;

  /**
   * Recommendation priority
   */
  priority: number;

  /**
   * Recommendation expiration
   */
  expiresAt: Date;

  /**
   * Recommendation metadata
   */
  metadata?: Record<string, any>;
}

/**
 * Proactive recommendation reason
 */
export interface ProactiveRecommendationReason {
  /**
   * Reason type
   */
  type: ProactiveRecommendationReasonType;

  /**
   * Reason description
   */
  description: string;

  /**
   * Reason evidence
   */
  evidence?: any;
}

/**
 * Proactive recommendation reason type
 */
export enum ProactiveRecommendationReasonType {
  /**
   * Predicted need
   */
  PREDICTED_NEED = 'PREDICTED_NEED',

  /**
   * Upcoming event
   */
  UPCOMING_EVENT = 'UPCOMING_EVENT',

  /**
   * Pattern detected
   */
  PATTERN_DETECTED = 'PATTERN_DETECTED',

  /**
   * Context change
   */
  CONTEXT_CHANGE = 'CONTEXT_CHANGE',

  /**
   * Time-based trigger
   */
  TIME_BASED = 'TIME_BASED',
}
