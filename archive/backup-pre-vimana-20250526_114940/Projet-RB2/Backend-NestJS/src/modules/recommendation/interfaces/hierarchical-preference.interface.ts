/**
 * Interfaces for the hierarchical preference system
 */

/**
 * Preference value with confidence score
 */
export interface PreferenceValue {
  value: string | number | boolean;
  confidence: number; // 0-1 score indicating confidence in this preference
  source: PreferenceSource;
  timestamp: Date;
  expiresAt?: Date; // Optional expiration date for temporary preferences
  metadata?: Record<string, any>;
}

/**
 * Source of a preference
 */
export enum PreferenceSource {
  EXPLICIT = 'EXPLICIT', // Directly specified by the user
  IMPLICIT = 'IMPLICIT', // Inferred from user behavior
  INHERITED = 'INHERITED', // Inherited from a parent category
  DEFAULT = 'DEFAULT', // System default
  TEMPORARY = 'TEMPORARY', // Temporary preference (e.g., for a session)
}

/**
 * Preference node in the hierarchy
 */
export interface PreferenceNode {
  id: string;
  path: string[]; // Path from root to this node
  key: string; // Preference key
  values: PreferenceValue[]; // Multiple values with confidence scores
  children: PreferenceNode[]; // Child preferences
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Preference tree for a user
 */
export interface PreferenceTree {
  userId: string;
  root: PreferenceNode;
  version: number; // Incremented on each update
  lastFullUpdate: Date;
  metadata?: Record<string, any>;
}

/**
 * Preference update operation
 */
export interface PreferenceUpdateOperation {
  type: 'add' | 'update' | 'remove';
  path: string[]; // Path to the preference node
  key?: string; // For add operations
  value?: PreferenceValue; // For add/update operations
  metadata?: Record<string, any>;
}

/**
 * Preference conflict resolution strategy
 */
export enum ConflictResolutionStrategy {
  HIGHEST_CONFIDENCE = 'HIGHEST_CONFIDENCE', // Use the value with the highest confidence
  MOST_RECENT = 'MOST_RECENT', // Use the most recently added/updated value
  EXPLICIT_OVER_IMPLICIT = 'EXPLICIT_OVER_IMPLICIT', // Explicit preferences override implicit ones
  COMBINE = 'COMBINE', // Combine values (e.g., weighted average)
  PARENT_OVERRIDE = 'PARENT_OVERRIDE', // Parent preferences override child preferences
  CHILD_OVERRIDE = 'CHILD_OVERRIDE', // Child preferences override parent preferences
}

/**
 * Preference query options
 */
export interface PreferenceQueryOptions {
  includeSources?: PreferenceSource[]; // Only include preferences from these sources
  excludeSources?: PreferenceSource[]; // Exclude preferences from these sources
  minConfidence?: number; // Minimum confidence score
  includeExpired?: boolean; // Include expired preferences
  includeMetadata?: boolean; // Include metadata in the results
  resolveConflicts?: ConflictResolutionStrategy; // How to resolve conflicts
  maxDepth?: number; // Maximum depth to traverse in the hierarchy
  includeInherited?: boolean; // Include inherited preferences
}

/**
 * Preference inference rule
 */
export interface PreferenceInferenceRule {
  id: string;
  name: string;
  description?: string;
  condition: {
    type: 'interaction' | 'profile' | 'context' | 'combination';
    parameters: Record<string, any>;
  };
  action: {
    path: string[];
    key: string;
    value: any;
    confidence: number;
    metadata?: Record<string, any>;
  };
  priority: number; // Higher priority rules are evaluated first
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Preference evolution rule
 */
export interface PreferenceEvolutionRule {
  id: string;
  name: string;
  description?: string;
  condition: {
    type: 'time_based' | 'interaction_based' | 'context_based' | 'combination';
    parameters: Record<string, any>;
  };
  action: {
    type: 'decay' | 'boost' | 'reset' | 'modify';
    parameters: Record<string, any>;
  };
  priority: number;
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Preference event
 */
export interface PreferenceEvent {
  id: string;
  userId: string;
  type: 'created' | 'updated' | 'removed' | 'inferred' | 'evolved';
  path: string[];
  key: string;
  oldValue?: PreferenceValue;
  newValue?: PreferenceValue;
  source: PreferenceSource;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * Preference snapshot
 */
export interface PreferenceSnapshot {
  id: string;
  userId: string;
  tree: PreferenceTree;
  createdAt: Date;
  reason: string;
  metadata?: Record<string, any>;
}
