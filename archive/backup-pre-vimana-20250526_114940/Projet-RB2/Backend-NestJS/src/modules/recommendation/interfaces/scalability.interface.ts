/**
 * Interfaces for the scalability system
 */

/**
 * Scalability configuration
 */
export interface ScalabilityConfig {
  /**
   * Enable/disable scalability features
   */
  enabled: boolean;

  /**
   * Horizontal scaling configuration
   */
  horizontalScaling: HorizontalScalingConfig;

  /**
   * Load balancing configuration
   */
  loadBalancing: LoadBalancingConfig;

  /**
   * Sharding configuration
   */
  sharding: ShardingConfig;

  /**
   * Rate limiting configuration
   */
  rateLimiting: RateLimitingConfig;

  /**
   * Circuit breaker configuration
   */
  circuitBreaker: CircuitBreakerConfig;

  /**
   * Fallback strategy configuration
   */
  fallbackStrategy: FallbackStrategyConfig;

  /**
   * Additional scalability parameters
   */
  params?: Record<string, any>;
}

/**
 * Horizontal scaling configuration
 */
export interface HorizontalScalingConfig {
  /**
   * Enable/disable horizontal scaling
   */
  enabled: boolean;

  /**
   * Scaling strategy
   */
  strategy: HorizontalScalingStrategy;

  /**
   * Minimum number of instances
   */
  minInstances: number;

  /**
   * Maximum number of instances
   */
  maxInstances: number;

  /**
   * Scaling triggers
   */
  triggers: ScalingTrigger[];

  /**
   * Cooldown period after scaling (in seconds)
   */
  cooldownPeriod: number;
}

/**
 * Horizontal scaling strategy
 */
export enum HorizontalScalingStrategy {
  /**
   * Manual scaling
   */
  MANUAL = 'MANUAL',

  /**
   * Schedule-based scaling
   */
  SCHEDULED = 'SCHEDULED',

  /**
   * Metric-based scaling
   */
  METRIC_BASED = 'METRIC_BASED',

  /**
   * Predictive scaling
   */
  PREDICTIVE = 'PREDICTIVE',
}

/**
 * Scaling trigger
 */
export interface ScalingTrigger {
  /**
   * Trigger type
   */
  type: ScalingTriggerType;

  /**
   * Metric to monitor
   */
  metric?: string;

  /**
   * Threshold for scaling up
   */
  scaleUpThreshold?: number;

  /**
   * Threshold for scaling down
   */
  scaleDownThreshold?: number;

  /**
   * Duration the threshold must be exceeded (in seconds)
   */
  duration: number;

  /**
   * Schedule for scheduled scaling
   */
  schedule?: string;

  /**
   * Additional trigger parameters
   */
  params?: Record<string, any>;
}

/**
 * Scaling trigger type
 */
export enum ScalingTriggerType {
  /**
   * CPU utilization
   */
  CPU = 'CPU',

  /**
   * Memory utilization
   */
  MEMORY = 'MEMORY',

  /**
   * Request rate
   */
  REQUEST_RATE = 'REQUEST_RATE',

  /**
   * Response time
   */
  RESPONSE_TIME = 'RESPONSE_TIME',

  /**
   * Queue length
   */
  QUEUE_LENGTH = 'QUEUE_LENGTH',

  /**
   * Schedule-based
   */
  SCHEDULE = 'SCHEDULE',

  /**
   * Custom metric
   */
  CUSTOM = 'CUSTOM',
}

/**
 * Load balancing configuration
 */
export interface LoadBalancingConfig {
  /**
   * Enable/disable load balancing
   */
  enabled: boolean;

  /**
   * Load balancing algorithm
   */
  algorithm: LoadBalancingAlgorithm;

  /**
   * Session affinity configuration
   */
  sessionAffinity: boolean;

  /**
   * Health check configuration
   */
  healthCheck: {
    /**
     * Enable/disable health checks
     */
    enabled: boolean;

    /**
     * Health check interval (in seconds)
     */
    interval: number;

    /**
     * Health check timeout (in seconds)
     */
    timeout: number;

    /**
     * Number of consecutive failures before marking as unhealthy
     */
    failureThreshold: number;

    /**
     * Number of consecutive successes before marking as healthy
     */
    successThreshold: number;
  };
}

/**
 * Load balancing algorithm
 */
export enum LoadBalancingAlgorithm {
  /**
   * Round robin
   */
  ROUND_ROBIN = 'ROUND_ROBIN',

  /**
   * Least connections
   */
  LEAST_CONNECTIONS = 'LEAST_CONNECTIONS',

  /**
   * Weighted round robin
   */
  WEIGHTED_ROUND_ROBIN = 'WEIGHTED_ROUND_ROBIN',

  /**
   * IP hash
   */
  IP_HASH = 'IP_HASH',

  /**
   * Random
   */
  RANDOM = 'RANDOM',
}

/**
 * Sharding configuration
 */
export interface ShardingConfig {
  /**
   * Enable/disable sharding
   */
  enabled: boolean;

  /**
   * Sharding strategy
   */
  strategy: ShardingStrategy;

  /**
   * Number of shards
   */
  shardCount: number;

  /**
   * Shard key
   */
  shardKey: string;

  /**
   * Replication factor
   */
  replicationFactor: number;
}

/**
 * Sharding strategy
 */
export enum ShardingStrategy {
  /**
   * Hash-based sharding
   */
  HASH = 'HASH',

  /**
   * Range-based sharding
   */
  RANGE = 'RANGE',

  /**
   * Directory-based sharding
   */
  DIRECTORY = 'DIRECTORY',

  /**
   * Geography-based sharding
   */
  GEOGRAPHY = 'GEOGRAPHY',
}

/**
 * Rate limiting configuration
 */
export interface RateLimitingConfig {
  /**
   * Enable/disable rate limiting
   */
  enabled: boolean;

  /**
   * Rate limit strategy
   */
  strategy: RateLimitStrategy;

  /**
   * Maximum requests per window
   */
  limit: number;

  /**
   * Time window (in seconds)
   */
  windowSec: number;

  /**
   * Delay excess requests instead of rejecting
   */
  delayExcess: boolean;
}

/**
 * Rate limit strategy
 */
export enum RateLimitStrategy {
  /**
   * Fixed window
   */
  FIXED_WINDOW = 'FIXED_WINDOW',

  /**
   * Sliding window
   */
  SLIDING_WINDOW = 'SLIDING_WINDOW',

  /**
   * Token bucket
   */
  TOKEN_BUCKET = 'TOKEN_BUCKET',

  /**
   * Leaky bucket
   */
  LEAKY_BUCKET = 'LEAKY_BUCKET',
}

/**
 * Circuit breaker configuration
 */
export interface CircuitBreakerConfig {
  /**
   * Enable/disable circuit breaker
   */
  enabled: boolean;

  /**
   * Failure threshold (percentage)
   */
  failureThreshold: number;

  /**
   * Reset timeout (in milliseconds)
   */
  resetTimeout: number;

  /**
   * Minimum number of requests before tripping
   */
  minimumRequests: number;

  /**
   * Circuit breaker strategy
   */
  strategy: CircuitBreakerStrategy;
}

/**
 * Circuit breaker strategy
 */
export enum CircuitBreakerStrategy {
  /**
   * Count-based
   */
  COUNT_BASED = 'COUNT_BASED',

  /**
   * Time-based
   */
  TIME_BASED = 'TIME_BASED',

  /**
   * Rate-based
   */
  RATE_BASED = 'RATE_BASED',
}

/**
 * Fallback strategy configuration
 */
export interface FallbackStrategyConfig {
  /**
   * Enable/disable fallback strategy
   */
  enabled: boolean;

  /**
   * Fallback strategies in order of preference
   */
  strategies: FallbackStrategy[];

  /**
   * Timeout for fallback (in milliseconds)
   */
  timeout: number;
}

/**
 * Fallback strategy
 */
export enum FallbackStrategy {
  /**
   * Return cached results
   */
  CACHED_RESULTS = 'CACHED_RESULTS',

  /**
   * Return popular items
   */
  POPULAR_ITEMS = 'POPULAR_ITEMS',

  /**
   * Return random items
   */
  RANDOM_ITEMS = 'RANDOM_ITEMS',

  /**
   * Return default recommendations
   */
  DEFAULT_RECOMMENDATIONS = 'DEFAULT_RECOMMENDATIONS',

  /**
   * Return empty results
   */
  EMPTY_RESULTS = 'EMPTY_RESULTS',
}
