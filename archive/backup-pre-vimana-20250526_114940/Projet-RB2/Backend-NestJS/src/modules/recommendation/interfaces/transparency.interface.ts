/**
 * Interfaces for the transparency system
 */

/**
 * Transparency configuration
 */
export interface TransparencyConfig {
  /**
   * Enable/disable transparency features
   */
  enabled: boolean;

  /**
   * Explanation types to include
   */
  explanationTypes: ExplanationType[];

  /**
   * Level of detail for explanations
   */
  detailLevel: 'basic' | 'detailed' | 'technical';

  /**
   * Whether to include model information in explanations
   */
  includeModelInfo: boolean;

  /**
   * Whether to include feature importance in explanations
   */
  includeFeatureImportance: boolean;

  /**
   * Whether to include confidence scores in explanations
   */
  includeConfidenceScores: boolean;

  /**
   * Whether to include alternative recommendations
   */
  includeAlternatives: boolean;

  /**
   * Number of alternative recommendations to include
   */
  numAlternatives?: number;

  /**
   * Whether to include diversity and fairness information
   */
  includeDiversityFairnessInfo: boolean;

  /**
   * User controls to include
   */
  userControls: UserControlType[];

  /**
   * Additional parameters for transparency
   */
  params?: Record<string, any>;
}

/**
 * Explanation type
 */
export enum ExplanationType {
  /**
   * Content-based explanations
   */
  CONTENT_BASED = 'CONTENT_BASED',

  /**
   * Collaborative filtering explanations
   */
  COLLABORATIVE = 'COLLABORATIVE',

  /**
   * User history-based explanations
   */
  USER_HISTORY = 'USER_HISTORY',

  /**
   * Demographic-based explanations
   */
  DEMOGRAPHIC = 'DEMOGRAPHIC',

  /**
   * Knowledge-based explanations
   */
  KNOWLEDGE_BASED = 'KNOWLEDGE_BASED',

  /**
   * Social explanations
   */
  SOCIAL = 'SOCIAL',

  /**
   * Contextual explanations
   */
  CONTEXTUAL = 'CONTEXTUAL',
}

/**
 * User control type
 */
export enum UserControlType {
  /**
   * Allow users to adjust feature weights
   */
  FEATURE_WEIGHTS = 'FEATURE_WEIGHTS',

  /**
   * Allow users to exclude certain items or categories
   */
  EXCLUSIONS = 'EXCLUSIONS',

  /**
   * Allow users to include certain items or categories
   */
  INCLUSIONS = 'INCLUSIONS',

  /**
   * Allow users to adjust diversity settings
   */
  DIVERSITY = 'DIVERSITY',

  /**
   * Allow users to provide explicit feedback
   */
  FEEDBACK = 'FEEDBACK',

  /**
   * Allow users to reset their recommendation profile
   */
  RESET = 'RESET',

  /**
   * Allow users to view and edit their preference data
   */
  PREFERENCE_EDITING = 'PREFERENCE_EDITING',

  /**
   * Allow users to view their recommendation history
   */
  HISTORY = 'HISTORY',
}

/**
 * Explanation
 */
export interface Explanation {
  /**
   * Type of explanation
   */
  type: ExplanationType;

  /**
   * Explanation text
   */
  text: string;

  /**
   * Confidence score for this explanation (0-1)
   */
  confidence?: number;

  /**
   * Importance of this explanation (0-1)
   */
  importance?: number;

  /**
   * Supporting evidence for this explanation
   */
  evidence?: ExplanationEvidence[];

  /**
   * Additional information for this explanation
   */
  additionalInfo?: Record<string, any>;
}

/**
 * Explanation evidence
 */
export interface ExplanationEvidence {
  /**
   * Type of evidence
   */
  type: 'feature' | 'item' | 'user' | 'interaction' | 'statistic';

  /**
   * Description of the evidence
   */
  description: string;

  /**
   * Value of the evidence
   */
  value: any;

  /**
   * Importance of this evidence (0-1)
   */
  importance?: number;
}

/**
 * User control settings
 */
export interface UserControlSettings {
  /**
   * Feature weights set by the user
   */
  featureWeights?: Record<string, number>;

  /**
   * Items or categories excluded by the user
   */
  exclusions?: {
    items?: string[];
    categories?: string[];
    attributes?: Record<string, any[]>;
  };

  /**
   * Items or categories included by the user
   */
  inclusions?: {
    items?: string[];
    categories?: string[];
    attributes?: Record<string, any[]>;
  };

  /**
   * Diversity settings set by the user
   */
  diversitySettings?: {
    enabled: boolean;
    level: 'low' | 'medium' | 'high';
    dimensions?: Record<string, number>;
  };

  /**
   * Other user settings
   */
  otherSettings?: Record<string, any>;
}

/**
 * Transparency report
 */
export interface TransparencyReport {
  /**
   * User ID
   */
  userId: string;

  /**
   * Recommendation ID
   */
  recommendationId: string;

  /**
   * Timestamp of the report
   */
  timestamp: Date;

  /**
   * Model information
   */
  modelInfo?: {
    name: string;
    version: string;
    type: string;
    trainingDate?: Date;
    performance?: Record<string, number>;
  };

  /**
   * Feature importance
   */
  featureImportance?: Record<string, number>;

  /**
   * Explanations for the recommendations
   */
  explanations: Record<string, Explanation[]>;

  /**
   * Alternative recommendations
   */
  alternatives?: {
    items: string[];
    reason: string;
  }[];

  /**
   * Diversity and fairness information
   */
  diversityFairnessInfo?: {
    diversityScore: number;
    fairnessMetrics: Record<string, number>;
  };

  /**
   * User control settings that affected these recommendations
   */
  userControlSettings?: UserControlSettings;
}
