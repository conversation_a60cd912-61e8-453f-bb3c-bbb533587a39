/**
 * Interfaces for the diversity and fairness system
 */

/**
 * Diversity configuration
 */
export interface DiversityConfig {
  /**
   * Enable/disable diversity enhancement
   */
  enabled: boolean;

  /**
   * Diversity dimensions to consider
   */
  dimensions: DiversityDimension[];

  /**
   * Minimum diversity score (0-1) required for recommendations
   */
  minDiversityScore: number;

  /**
   * Maximum similarity allowed between items in recommendations
   */
  maxItemSimilarity: number;

  /**
   * Weight of diversity in the final recommendation score (0-1)
   */
  diversityWeight: number;

  /**
   * Strategy for enhancing diversity
   */
  strategy: DiversityStrategy;

  /**
   * Additional parameters for the diversity strategy
   */
  strategyParams?: Record<string, any>;
}

/**
 * Diversity dimension
 */
export interface DiversityDimension {
  /**
   * Name of the dimension
   */
  name: string;

  /**
   * Weight of this dimension in the diversity calculation (0-1)
   */
  weight: number;

  /**
   * Field in the item data that corresponds to this dimension
   */
  field: string;

  /**
   * Type of the dimension
   */
  type: 'categorical' | 'numerical' | 'text' | 'tags';

  /**
   * For categorical dimensions, the list of possible values
   */
  categories?: string[];

  /**
   * For numerical dimensions, the range of possible values
   */
  range?: {
    min: number;
    max: number;
  };

  /**
   * Additional parameters for this dimension
   */
  params?: Record<string, any>;
}

/**
 * Diversity strategy
 */
export enum DiversityStrategy {
  /**
   * Maximize the diversity of the recommendations
   */
  MAXIMIZE_DIVERSITY = 'MAXIMIZE_DIVERSITY',

  /**
   * Balance relevance and diversity
   */
  BALANCE = 'BALANCE',

  /**
   * Use a greedy approach to select diverse items
   */
  GREEDY = 'GREEDY',

  /**
   * Use a determinantal point process to select diverse items
   */
  DPP = 'DPP',

  /**
   * Use clustering to select diverse items
   */
  CLUSTERING = 'CLUSTERING',
}

/**
 * Fairness configuration
 */
export interface FairnessConfig {
  /**
   * Enable/disable fairness constraints
   */
  enabled: boolean;

  /**
   * Protected attributes to consider for fairness
   */
  protectedAttributes: ProtectedAttribute[];

  /**
   * Fairness metrics to optimize for
   */
  metrics: FairnessMetric[];

  /**
   * Fairness constraints
   */
  constraints: FairnessConstraint[];

  /**
   * Strategy for enforcing fairness
   */
  strategy: FairnessStrategy;

  /**
   * Additional parameters for the fairness strategy
   */
  strategyParams?: Record<string, any>;
}

/**
 * Protected attribute
 */
export interface ProtectedAttribute {
  /**
   * Name of the attribute
   */
  name: string;

  /**
   * Field in the user or item data that corresponds to this attribute
   */
  field: string;

  /**
   * Type of the attribute
   */
  type: 'binary' | 'categorical' | 'numerical';

  /**
   * For categorical attributes, the list of possible values
   */
  categories?: string[];

  /**
   * For numerical attributes, the range of possible values
   */
  range?: {
    min: number;
    max: number;
  };

  /**
   * Whether this attribute applies to users or items
   */
  appliesTo: 'user' | 'item' | 'both';

  /**
   * Additional parameters for this attribute
   */
  params?: Record<string, any>;
}

/**
 * Fairness metric
 */
export enum FairnessMetric {
  /**
   * Statistical parity - equal representation across groups
   */
  STATISTICAL_PARITY = 'STATISTICAL_PARITY',

  /**
   * Equal opportunity - equal true positive rates across groups
   */
  EQUAL_OPPORTUNITY = 'EQUAL_OPPORTUNITY',

  /**
   * Equalized odds - equal true positive and false positive rates across groups
   */
  EQUALIZED_ODDS = 'EQUALIZED_ODDS',

  /**
   * Disparate impact - ratio of positive outcomes between groups
   */
  DISPARATE_IMPACT = 'DISPARATE_IMPACT',

  /**
   * Calibration - predictions should be well-calibrated across groups
   */
  CALIBRATION = 'CALIBRATION',
}

/**
 * Fairness constraint
 */
export interface FairnessConstraint {
  /**
   * Metric to constrain
   */
  metric: FairnessMetric;

  /**
   * Protected attribute this constraint applies to
   */
  attribute: string;

  /**
   * Threshold for the constraint
   */
  threshold: number;

  /**
   * Whether the metric should be above or below the threshold
   */
  direction: 'above' | 'below';

  /**
   * Weight of this constraint in the optimization (0-1)
   */
  weight: number;
}

/**
 * Fairness strategy
 */
export enum FairnessStrategy {
  /**
   * Pre-processing - modify the training data to ensure fairness
   */
  PRE_PROCESSING = 'PRE_PROCESSING',

  /**
   * In-processing - modify the algorithm to ensure fairness
   */
  IN_PROCESSING = 'IN_PROCESSING',

  /**
   * Post-processing - modify the recommendations to ensure fairness
   */
  POST_PROCESSING = 'POST_PROCESSING',
}

/**
 * Diversity and fairness metrics
 */
export interface DiversityFairnessMetrics {
  /**
   * Overall diversity score (0-1)
   */
  diversityScore: number;

  /**
   * Diversity scores by dimension
   */
  dimensionScores: Record<string, number>;

  /**
   * Fairness metrics
   */
  fairnessMetrics: Record<string, number>;

  /**
   * Whether all fairness constraints are satisfied
   */
  constraintsSatisfied: boolean;

  /**
   * Details about constraint violations
   */
  constraintViolations?: {
    attribute: string;
    metric: FairnessMetric;
    value: number;
    threshold: number;
    direction: 'above' | 'below';
  }[];
}
