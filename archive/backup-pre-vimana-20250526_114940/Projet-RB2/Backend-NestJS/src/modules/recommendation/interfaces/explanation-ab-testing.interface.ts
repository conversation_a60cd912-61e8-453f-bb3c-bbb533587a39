import { ExplanationVariantType } from '@prisma/client';

/**
 * Interface pour un test A/B d'explications
 */
export interface ExplanationABTestInterface {
  /** ID du test */
  id: string;
  
  /** Nom du test */
  name: string;
  
  /** Description du test */
  description: string;
  
  /** Date de début du test */
  startDate: Date;
  
  /** Date de fin du test */
  endDate: Date;
  
  /** Statut du test (ACTIVE, PAUSED, COMPLETED, CANCELLED) */
  status: string;
  
  /** Date de création */
  createdAt: Date;
  
  /** Date de mise à jour */
  updatedAt: Date;
  
  /** Variantes du test */
  variants?: ExplanationVariantInterface[];
  
  /** Métriques du test */
  metrics?: ExplanationABTestMetricsInterface[];
}

/**
 * Interface pour une variante d'explication
 */
export interface ExplanationVariantInterface {
  /** ID de la variante */
  id: string;
  
  /** ID du test */
  testId: string;
  
  /** Nom de la variante */
  name: string;
  
  /** Description de la variante */
  description: string;
  
  /** Type de variante */
  type: ExplanationVariantType;
  
  /** Configuration de la variante */
  configuration: Record<string, any>;
  
  /** Allocation de la variante (0-1) */
  allocation: number;
  
  /** Date de création */
  createdAt: Date;
  
  /** Date de mise à jour */
  updatedAt: Date;
}

/**
 * Interface pour une assignation de test A/B
 */
export interface ExplanationABTestAssignmentInterface {
  /** ID de l'assignation */
  id: string;
  
  /** ID de l'utilisateur */
  userId: string;
  
  /** ID du test */
  testId: string;
  
  /** ID de la variante */
  variantId: string;
  
  /** Date d'assignation */
  assignedAt: Date;
}

/**
 * Interface pour une interaction avec un test A/B
 */
export interface ExplanationABTestInteractionInterface {
  /** ID de l'interaction */
  id: string;
  
  /** ID de l'utilisateur */
  userId: string;
  
  /** ID du test */
  testId: string;
  
  /** ID de la variante */
  variantId: string;
  
  /** ID de la recommandation */
  recommendationId: string;
  
  /** Type d'interaction (VIEW, EXPAND, CLICK, DISMISS, FEEDBACK) */
  interactionType: string;
  
  /** Données supplémentaires */
  data: Record<string, any>;
  
  /** Timestamp de l'interaction */
  timestamp: Date;
}

/**
 * Interface pour les métriques d'un test A/B
 */
export interface ExplanationABTestMetricsInterface {
  /** ID des métriques */
  id: string;
  
  /** ID du test */
  testId: string;
  
  /** Timestamp des métriques */
  timestamp: Date;
  
  /** Métriques */
  metrics: {
    /** Nombre total d'impressions */
    impressions: Record<string, number>;
    
    /** Nombre total d'interactions */
    interactions: Record<string, number>;
    
    /** Taux d'interaction */
    interactionRate: Record<string, number>;
    
    /** Taux de conversion */
    conversionRate: Record<string, number>;
    
    /** Temps moyen passé sur les explications */
    averageTimeSpent: Record<string, number>;
    
    /** Score de satisfaction */
    satisfactionScore: Record<string, number>;
    
    /** Métriques supplémentaires */
    [key: string]: Record<string, any>;
  };
}

/**
 * Interface pour les résultats d'un test A/B
 */
export interface ExplanationABTestResultsInterface {
  /** ID du test */
  testId: string;
  
  /** Nom du test */
  testName: string;
  
  /** Période du test */
  period: {
    start: Date;
    end: Date;
  };
  
  /** Nombre total d'utilisateurs */
  totalUsers: number;
  
  /** Résultats par variante */
  variantResults: {
    /** ID de la variante */
    variantId: string;
    
    /** Nom de la variante */
    variantName: string;
    
    /** Type de variante */
    variantType: ExplanationVariantType;
    
    /** Nombre d'utilisateurs assignés à cette variante */
    userCount: number;
    
    /** Nombre d'impressions */
    impressions: number;
    
    /** Nombre d'interactions */
    interactions: number;
    
    /** Taux d'interaction */
    interactionRate: number;
    
    /** Taux de conversion */
    conversionRate: number;
    
    /** Temps moyen passé sur les explications */
    averageTimeSpent: number;
    
    /** Score de satisfaction */
    satisfactionScore: number;
    
    /** Métriques supplémentaires */
    [key: string]: any;
  }[];
  
  /** Variante gagnante */
  winner?: {
    /** ID de la variante */
    variantId: string;
    
    /** Nom de la variante */
    variantName: string;
    
    /** Amélioration par rapport à la variante de contrôle */
    improvement: number;
    
    /** Niveau de confiance */
    confidenceLevel: number;
  };
  
  /** Recommandations */
  recommendations: string[];
}
