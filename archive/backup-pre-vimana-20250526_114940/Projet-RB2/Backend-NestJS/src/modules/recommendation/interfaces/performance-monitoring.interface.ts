/**
 * Interfaces for the performance monitoring system
 */

/**
 * Performance metric value
 */
export interface MetricValue {
  value: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * Performance metric
 */
export interface PerformanceMetric {
  id: string;
  name: string;
  description?: string;
  category: MetricCategory;
  unit: string;
  values: MetricValue[];
  thresholds?: {
    warning?: number;
    critical?: number;
    direction: 'above' | 'below';
  };
  metadata?: Record<string, any>;
}

/**
 * Metric category
 */
export enum MetricCategory {
  ACCURACY = 'ACCURACY',
  RELEVANCE = 'RELEVANCE',
  DIVERSITY = 'DIVERSITY',
  COVERAGE = 'COVERAGE',
  NOVELTY = 'NOVELTY',
  SERENDIPITY = 'SERENDIPITY',
  PERFORMANCE = 'PERFORMANCE',
  BUSINESS = 'BUSINESS',
  USER_SATISFACTION = 'USER_SATISFACTION',
  SYSTEM = 'SYSTEM',
}

/**
 * Performance alert
 */
export interface PerformanceAlert {
  id: string;
  metricId: string;
  metricName: string;
  severity: AlertSeverity;
  message: string;
  timestamp: Date;
  value: number;
  threshold: number;
  acknowledged: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
  resolved: boolean;
  resolvedAt?: Date;
  metadata?: Record<string, any>;
}

/**
 * Alert severity
 */
export enum AlertSeverity {
  INFO = 'INFO',
  WARNING = 'WARNING',
  CRITICAL = 'CRITICAL',
}

/**
 * Performance report
 */
export interface PerformanceReport {
  id: string;
  name: string;
  description?: string;
  startDate: Date;
  endDate: Date;
  metrics: {
    metricId: string;
    metricName: string;
    category: MetricCategory;
    values: MetricValue[];
    summary: {
      min: number;
      max: number;
      avg: number;
      median: number;
      trend: 'up' | 'down' | 'stable';
    };
  }[];
  alerts: PerformanceAlert[];
  recommendations?: string[];
  createdAt: Date;
  createdBy?: string;
  metadata?: Record<string, any>;
}

/**
 * Performance dashboard configuration
 */
export interface DashboardConfig {
  id: string;
  name: string;
  description?: string;
  layout: {
    widgets: DashboardWidget[];
  };
  filters?: {
    startDate?: Date;
    endDate?: Date;
    categories?: MetricCategory[];
    metrics?: string[];
    customFilters?: Record<string, any>;
  };
  refreshInterval?: number; // in seconds
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
  isDefault?: boolean;
  metadata?: Record<string, any>;
}

/**
 * Dashboard widget
 */
export interface DashboardWidget {
  id: string;
  type: WidgetType;
  title: string;
  description?: string;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  config: {
    metrics?: string[];
    visualization?: 'line' | 'bar' | 'pie' | 'gauge' | 'table' | 'number';
    timeRange?: 'hour' | 'day' | 'week' | 'month' | 'year' | 'custom';
    customTimeRange?: {
      start: Date;
      end: Date;
    };
    aggregation?: 'none' | 'sum' | 'avg' | 'min' | 'max' | 'count';
    limit?: number;
    sortBy?: string;
    sortDirection?: 'asc' | 'desc';
    showLegend?: boolean;
    showLabels?: boolean;
    colorScheme?: string;
    thresholds?: {
      warning?: number;
      critical?: number;
      direction: 'above' | 'below';
    };
    alertsConfig?: {
      showAcknowledged?: boolean;
      showResolved?: boolean;
      severities?: AlertSeverity[];
    };
    customConfig?: Record<string, any>;
  };
  metadata?: Record<string, any>;
}

/**
 * Widget type
 */
export enum WidgetType {
  METRIC_CHART = 'METRIC_CHART',
  METRIC_VALUE = 'METRIC_VALUE',
  ALERTS_LIST = 'ALERTS_LIST',
  ALERTS_SUMMARY = 'ALERTS_SUMMARY',
  RECOMMENDATIONS_LIST = 'RECOMMENDATIONS_LIST',
  CUSTOM = 'CUSTOM',
}

/**
 * Performance data query
 */
export interface PerformanceDataQuery {
  metrics?: string[];
  categories?: MetricCategory[];
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  aggregation?: 'none' | 'sum' | 'avg' | 'min' | 'max' | 'count';
  interval?: 'minute' | 'hour' | 'day' | 'week' | 'month';
  filters?: Record<string, any>;
}

/**
 * Alert configuration
 */
export interface AlertConfig {
  id: string;
  metricId: string;
  name: string;
  description?: string;
  thresholds: {
    warning?: number;
    critical?: number;
    direction: 'above' | 'below';
  };
  enabled: boolean;
  notificationChannels?: string[];
  cooldownMinutes?: number;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}
