/**
 * Interfaces for the continuous learning pipeline
 */

import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';

/**
 * Raw interaction data from users
 */
export interface RawInteractionData {
  userId: string;
  itemId: string;
  itemType: RecommendationType;
  interactionType: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * Processed feature data ready for model training
 */
export interface ProcessedFeatureData {
  userId: string;
  itemId: string;
  itemType: RecommendationType;
  features: Record<string, number>;
  label: number;
  timestamp: Date;
}

/**
 * Model training configuration
 */
export interface ModelTrainingConfig {
  modelType: string;
  hyperparameters: Record<string, any>;
  validationSplit: number;
  earlyStoppingPatience: number;
  maxEpochs: number;
  batchSize: number;
  learningRate: number;
  regularization?: number;
}

/**
 * Model version information
 */
export interface ModelVersion {
  id: string;
  modelType: string;
  version: string;
  createdAt: Date;
  trainedWith: {
    dataSize: number;
    dataRange: {
      start: Date;
      end: Date;
    };
    hyperparameters: Record<string, any>;
  };
  performance: {
    trainingMetrics: Record<string, number>;
    validationMetrics: Record<string, number>;
    testMetrics?: Record<string, number>;
  };
  status: 'training' | 'ready' | 'deployed' | 'archived' | 'failed';
}

/**
 * Model comparison result
 */
export interface ModelComparisonResult {
  baselineModelId: string;
  candidateModelId: string;
  metrics: {
    name: string;
    baselineValue: number;
    candidateValue: number;
    difference: number;
    percentImprovement: number;
    isSignificant: boolean;
    pValue?: number;
  }[];
  recommendation: 'deploy' | 'reject' | 'more_testing';
  confidence: number;
}

/**
 * Feature extraction configuration
 */
export interface FeatureExtractionConfig {
  userFeatures: {
    includeDemographics: boolean;
    includePreferences: boolean;
    includeInteractionHistory: boolean;
    historyWindowDays: number;
  };
  itemFeatures: {
    includeMetadata: boolean;
    includePopularity: boolean;
    includeCategoricalFeatures: boolean;
    includeNumericalFeatures: boolean;
  };
  contextFeatures: {
    includeTimeOfDay: boolean;
    includeDayOfWeek: boolean;
    includeSeason: boolean;
    includeLocation: boolean;
    includeDevice: boolean;
  };
}

/**
 * Pipeline execution status
 */
export interface PipelineExecutionStatus {
  id: string;
  startTime: Date;
  endTime?: Date;
  status: 'running' | 'completed' | 'failed';
  steps: {
    name: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    startTime?: Date;
    endTime?: Date;
    error?: string;
    metrics?: Record<string, any>;
  }[];
  error?: string;
  result?: any;
}

/**
 * Learning event for tracking pipeline activities
 */
export interface LearningPipelineEvent {
  id: string;
  eventType: 'pipeline_started' | 'pipeline_completed' | 'pipeline_failed' | 'model_training_started' | 'model_training_completed' | 'model_deployed' | 'model_rollback';
  timestamp: Date;
  data: Record<string, any>;
}

/**
 * Model deployment configuration
 */
export interface ModelDeploymentConfig {
  modelId: string;
  deploymentStrategy: 'immediate' | 'gradual' | 'a_b_test';
  trafficPercentage?: number;
  rollbackThreshold?: {
    metric: string;
    value: number;
    operator: 'lt' | 'gt' | 'eq' | 'lte' | 'gte';
  };
  monitoringPeriod?: number; // in hours
}

/**
 * Data preprocessing configuration
 */
export interface DataPreprocessingConfig {
  cleansingOptions: {
    removeOutliers: boolean;
    fillMissingValues: boolean;
    normalizeFeatures: boolean;
  };
  samplingOptions: {
    balanceClasses: boolean;
    undersampleMajorityClass: boolean;
    oversampleMinorityClass: boolean;
  };
  transformationOptions: {
    applyPCA: boolean;
    applyScaling: boolean;
    applyEncoding: boolean;
  };
}
