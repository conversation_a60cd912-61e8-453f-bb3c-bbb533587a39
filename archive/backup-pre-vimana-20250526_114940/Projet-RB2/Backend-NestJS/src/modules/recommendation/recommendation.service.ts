import { Injectable, Logger } from '@nestjs/common';
import { RecommendationService as CoreRecommendationService } from './services/recommendation.service';
import { AnalyticsIntegrationService } from './services/analytics-integration.service';
import { FeedbackService } from './services/feedback.service';
import { ContextualRecommendationService } from './services/contextual-recommendation.service';
import { ContinuousLearningService } from './services/continuous-learning.service';
import { GetRecommendationsDto } from './dto/get-recommendations.dto';
import { RecordInteractionDto } from './dto/record-interaction.dto';
import { RecommendationResponseDto } from './dto/recommendation-response.dto';

/**
 * Service principal de recommandation qui orchestre tous les sous-services
 */
@Injectable()
export class RecommendationService {
  private readonly logger = new Logger(RecommendationService.name);

  constructor(
    private readonly coreRecommendationService: CoreRecommendationService,
    private readonly analyticsIntegrationService: AnalyticsIntegrationService,
    private readonly feedbackService: FeedbackService,
    private readonly contextualRecommendationService: ContextualRecommendationService,
    private readonly continuousLearningService: ContinuousLearningService,
  ) {}

  /**
   * Obtenir des recommandations personnalisées pour un utilisateur
   */
  async getRecommendations(
    userId: string,
    dto: GetRecommendationsDto,
  ): Promise<RecommendationResponseDto> {
    try {
      this.logger.log(`Getting recommendations for user ${userId}`);

      // Obtenir les recommandations contextuelles
      const recommendations = await this.contextualRecommendationService.getContextualRecommendations(
        userId,
        dto,
      );

      // Enregistrer l'événement pour l'analytics
      await this.analyticsIntegrationService.trackRecommendationRequest(userId, dto);

      return recommendations;
    } catch (error) {
      this.logger.error(`Failed to get recommendations for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Enregistrer une interaction utilisateur avec une recommandation
   */
  async recordInteraction(
    userId: string,
    dto: RecordInteractionDto,
  ): Promise<void> {
    try {
      this.logger.log(`Recording interaction for user ${userId}`);

      // Enregistrer le feedback
      await this.feedbackService.recordFeedback(userId, {
        recommendationId: dto.recommendationId,
        interactionType: dto.interactionType,
        rating: dto.rating,
        feedback: dto.feedback,
      });

      // Déclencher l'apprentissage continu
      await this.continuousLearningService.processInteraction(userId, dto);

      // Mettre à jour les analytics
      await this.analyticsIntegrationService.trackInteraction(userId, dto);
    } catch (error) {
      this.logger.error(`Failed to record interaction for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Obtenir les métriques de performance du système de recommandation
   */
  async getPerformanceMetrics(userId?: string): Promise<any> {
    try {
      return await this.analyticsIntegrationService.getPerformanceMetrics(userId);
    } catch (error) {
      this.logger.error('Failed to get performance metrics:', error);
      throw error;
    }
  }

  /**
   * Déclencher un entraînement du modèle
   */
  async triggerModelTraining(): Promise<void> {
    try {
      this.logger.log('Triggering model training');
      await this.continuousLearningService.triggerTraining();
    } catch (error) {
      this.logger.error('Failed to trigger model training:', error);
      throw error;
    }
  }
}
