import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from '../../prisma/prisma.module';
import { UsersModule } from '../users/users.module';
import { LearningModule } from '../learning/learning.module';

// Controllers
import { RecommendationController } from './controllers/recommendation.controller.refactored';
import { SocialVideoRecommendationController } from './controllers/social-video-recommendation.controller';

// Services
import { RecommendationService } from './services/recommendation.service.enhanced';
import { ContentBasedService } from './services/content-based.service';
import { CollaborativeFilteringService } from './services/collaborative-filtering.service';
import { HybridRecommendationService } from './services/hybrid-recommendation.service';
import { PersonalizationService } from './services/personalization.service';
import { SocialVideoRecommendationService } from './services/social-video-recommendation.service';
import { AgentRbIntegrationService } from './services/agent-rb-integration.service';

// Clients
import { AgentRbClient } from './clients/agent-rb-client';

/**
 * Module de recommandation
 */
@Module({
  imports: [
    PrismaModule,
    UsersModule,
    LearningModule,
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    ConfigModule,
  ],
  controllers: [
    RecommendationController,
    SocialVideoRecommendationController,
  ],
  providers: [
    RecommendationService,
    ContentBasedService,
    CollaborativeFilteringService,
    HybridRecommendationService,
    PersonalizationService,
    SocialVideoRecommendationService,
    AgentRbIntegrationService,
    AgentRbClient,
  ],
  exports: [
    RecommendationService,
    SocialVideoRecommendationService,
    AgentRbIntegrationService,
  ],
})
export class RecommendationModule {}
