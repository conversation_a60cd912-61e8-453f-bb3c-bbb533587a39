# API de Recommandation - Documentation

Cette documentation décrit l'API de recommandation refactorisée pour le projet Retreat And Be.

## Vue d'ensemble

L'API de recommandation fournit des fonctionnalités pour obtenir des recommandations personnalisées, des éléments similaires, et pour enregistrer les interactions des utilisateurs. Elle utilise différentes stratégies de recommandation (basée sur le contenu, filtrage collaboratif, hybride) pour fournir des suggestions pertinentes aux utilisateurs.

## Base URL

```
/api/v1/recommendations
```

## Authentification

Toutes les requêtes à l'API nécessitent une authentification via un token JWT. Le token doit être inclus dans l'en-tête `Authorization` de la requête :

```
Authorization: Bearer <token>
```

## Endpoints

### Obtenir des recommandations personnalisées

```
GET /api/v1/recommendations
```

Récupère des recommandations personnalisées pour l'utilisateur connecté en fonction de ses préférences et de son historique d'interactions.

#### Paramètres de requête

| Paramètre    | Type    | Description                                                | Défaut     |
|--------------|---------|------------------------------------------------------------|-----------:|
| type         | string  | Type d'élément à recommander (RETREAT, PARTNER, COURSE)    | RETREAT    |
| strategy     | string  | Stratégie de recommandation (CONTENT_BASED, COLLABORATIVE, HYBRID) | HYBRID |
| hybridMethod | string  | Méthode hybride (WEIGHTED, SWITCHING, CASCADE)             | WEIGHTED   |
| limit        | integer | Nombre maximum de recommandations                          | 10         |
| page         | integer | Numéro de page pour la pagination                          | 1          |
| filters      | object  | Filtres à appliquer aux recommandations                    | {}         |
| excludeIds   | array   | IDs des éléments à exclure                                 | []         |
| includeMetadata | boolean | Inclure les métadonnées détaillées                      | true       |
| sortBy       | string  | Champ sur lequel trier les résultats                       | score      |
| sortOrder    | string  | Ordre de tri (asc, desc)                                   | desc       |

#### Exemple de requête

```
GET /api/v1/recommendations?type=RETREAT&limit=5&page=1
```

#### Exemple de réponse

```json
{
  "items": [
    {
      "id": "60d21b4667d0d8992e610c85",
      "type": "RETREAT",
      "title": "Retraite de Yoga dans les Alpes",
      "description": "Une semaine de détente et de reconnexion avec la nature...",
      "score": 0.95,
      "sources": ["content-based", "collaborative"],
      "reasons": ["Basé sur vos préférences", "Similaire à vos activités précédentes"],
      "imageUrl": "https://example.com/images/retreat-123.jpg",
      "url": "https://retreatandbe.com/retreats/retraite-yoga-alpes",
      "metadata": {
        "category": "Yoga",
        "level": "INTERMEDIATE",
        "location": "Chamonix, France",
        "duration": 7,
        "tags": ["yoga", "meditation", "mountains"],
        "price": 1200,
        "currency": "EUR",
        "startDate": "2023-07-15",
        "endDate": "2023-07-22",
        "reviewCount": 42,
        "averageRating": 4.7,
        "available": true,
        "remainingSpots": 5
      }
    },
    // ... autres recommandations
  ],
  "meta": {
    "total": 100,
    "page": 1,
    "limit": 5,
    "totalPages": 20
  }
}
```

### Obtenir des recommandations tendance

```
GET /api/v1/recommendations/trending
```

Récupère les éléments les plus populaires ou tendance, indépendamment des préférences de l'utilisateur.

#### Paramètres de requête

Mêmes paramètres que pour l'endpoint principal, à l'exception de `strategy` et `hybridMethod` qui ne sont pas applicables.

#### Exemple de requête

```
GET /api/v1/recommendations/trending?type=RETREAT&limit=5
```

#### Exemple de réponse

Format identique à l'endpoint principal.

### Obtenir des éléments similaires

```
GET /api/v1/recommendations/similar/:type/:itemId
```

Récupère des éléments similaires à un élément spécifié, basé sur le contenu et les caractéristiques.

#### Paramètres de chemin

| Paramètre | Type   | Description                                     |
|-----------|--------|-------------------------------------------------|
| type      | string | Type d'élément (RETREAT, PARTNER, COURSE)       |
| itemId    | string | ID de l'élément de référence                    |

#### Paramètres de requête

Mêmes paramètres que pour l'endpoint principal, à l'exception de `type`, `strategy` et `hybridMethod` qui ne sont pas applicables.

#### Exemple de requête

```
GET /api/v1/recommendations/similar/RETREAT/60d21b4667d0d8992e610c85?limit=5
```

#### Exemple de réponse

Format identique à l'endpoint principal.

### Enregistrer une interaction

```
POST /api/v1/recommendations/interactions
```

Enregistre une interaction de l'utilisateur avec un élément (vue, like, etc.).

#### Corps de la requête

```json
{
  "itemId": "60d21b4667d0d8992e610c85",
  "type": "RETREAT",
  "interactionType": "VIEW",
  "metadata": {
    "duration": 300,
    "progress": 0.5
  }
}
```

| Champ          | Type   | Description                                     | Requis |
|----------------|--------|-------------------------------------------------|--------|
| itemId         | string | ID de l'élément                                 | Oui    |
| type           | string | Type d'élément (RETREAT, PARTNER, COURSE)       | Oui    |
| interactionType| string | Type d'interaction (VIEW, LIKE, ENROLL, etc.)   | Oui    |
| metadata       | object | Métadonnées additionnelles de l'interaction     | Non    |

#### Exemple de réponse

```json
{
  "success": true
}
```

### Mettre à jour les préférences

```
POST /api/v1/recommendations/preferences
```

Met à jour les préférences de recommandation de l'utilisateur.

#### Corps de la requête

```json
{
  "recommendationStrategy": "HYBRID",
  "hybridMethod": "WEIGHTED",
  "preferredTypes": ["RETREAT", "COURSE"],
  "preferredCategories": ["Yoga", "Meditation"],
  "excludedCategories": ["Fitness"],
  "preferredLevels": ["BEGINNER", "INTERMEDIATE"],
  "preferredLocations": ["France", "Italy"],
  "preferredTags": ["yoga", "meditation", "wellness"],
  "maxRecommendations": 10,
  "includeMetadata": true
}
```

Tous les champs sont optionnels.

#### Exemple de réponse

```json
{
  "success": true,
  "preferences": {
    "recommendationStrategy": "HYBRID",
    "hybridMethod": "WEIGHTED",
    "preferredTypes": ["RETREAT", "COURSE"],
    "preferredCategories": ["Yoga", "Meditation"],
    "excludedCategories": ["Fitness"],
    "preferredLevels": ["BEGINNER", "INTERMEDIATE"],
    "preferredLocations": ["France", "Italy"],
    "preferredTags": ["yoga", "meditation", "wellness"],
    "maxRecommendations": 10,
    "includeMetadata": true
  }
}
```

## Codes d'erreur

| Code | Description                                                |
|------|------------------------------------------------------------|
| 400  | Requête invalide (paramètres manquants ou incorrects)      |
| 401  | Non authentifié                                            |
| 403  | Non autorisé                                               |
| 404  | Ressource non trouvée                                      |
| 500  | Erreur interne du serveur                                  |

## Intégration avec Agent-RB

L'API de recommandation s'intègre avec le service Agent-RB pour récupérer les détails des éléments recommandés. Cette intégration est gérée par le client `AgentRbClient`, qui communique avec Agent-RB via des requêtes HTTP.

### Configuration

L'intégration avec Agent-RB nécessite les variables d'environnement suivantes :

- `AGENT_RB_SERVICE_URL` : URL du service Agent-RB
- `AGENT_RB_TIMEOUT_MS` (optionnel) : Timeout pour les requêtes à Agent-RB (défaut : 30000 ms)

## Stratégies de recommandation

L'API prend en charge trois stratégies de recommandation :

1. **Basée sur le contenu (CONTENT_BASED)** : Recommande des éléments similaires à ceux que l'utilisateur a aimés dans le passé, basé sur les caractéristiques des éléments.

2. **Filtrage collaboratif (COLLABORATIVE)** : Recommande des éléments que des utilisateurs similaires ont aimés.

3. **Hybride (HYBRID)** : Combine les approches basées sur le contenu et le filtrage collaboratif. Trois méthodes hybrides sont disponibles :
   - **WEIGHTED** : Combine les scores des deux approches avec une pondération.
   - **SWITCHING** : Utilise l'une ou l'autre approche en fonction du nombre d'interactions de l'utilisateur.
   - **CASCADE** : Utilise une approche pour filtrer les éléments, puis l'autre pour les classer.

## Pagination

Tous les endpoints qui retournent des listes supportent la pagination via les paramètres `limit` et `page`. La réponse inclut des métadonnées de pagination dans le champ `meta`.
