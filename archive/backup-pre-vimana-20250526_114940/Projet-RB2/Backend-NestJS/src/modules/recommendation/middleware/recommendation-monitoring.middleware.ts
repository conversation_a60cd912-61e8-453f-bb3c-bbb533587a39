import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { RecommendationMonitoringService } from '../../monitoring/services/recommendation-monitoring.service';
import { RecommendationType } from '../enums/recommendation-type.enum';

/**
 * Middleware pour mesurer les performances des requêtes de recommandation
 */
@Injectable()
export class RecommendationMonitoringMiddleware implements NestMiddleware {
  private readonly logger = new Logger(RecommendationMonitoringMiddleware.name);

  constructor(
    private readonly monitoringService: RecommendationMonitoringService,
  ) {}

  /**
   * Middleware pour mesurer les performances des requêtes de recommandation
   * @param req Requête
   * @param res Réponse
   * @param next Fonction suivante
   */
  use(req: Request, res: Response, next: NextFunction): void {
    // Récupérer l'URL de la requête
    const url = req.originalUrl;
    
    // Vérifier si c'est une requête de recommandation
    if (!url.includes('/api/v1/recommendations')) {
      return next();
    }
    
    // Récupérer le type d'élément
    const type = (req.query.type as string || 'RETREAT') as RecommendationType;
    
    // Récupérer la stratégie
    const strategy = req.query.strategy as string || 'HYBRID';
    
    // Déterminer l'endpoint
    let endpoint = 'recommendations';
    
    if (url.includes('/trending')) {
      endpoint = 'trending';
    } else if (url.includes('/similar')) {
      endpoint = 'similar';
    }
    
    // Mesurer le temps de réponse
    const startTime = Date.now();
    
    // Intercepter la réponse
    const originalSend = res.send;
    res.send = function(body): Response {
      // Restaurer la méthode send
      res.send = originalSend;
      
      // Calculer le temps de réponse
      const responseTime = Date.now() - startTime;
      
      // Enregistrer la requête
      if (res.statusCode >= 400) {
        // Erreur
        monitoringService.recordRecommendationRequest(
          type,
          strategy,
          endpoint,
          responseTime,
          {
            code: res.statusCode.toString(),
            message: typeof body === 'string' ? body : JSON.stringify(body),
          },
        );
      } else {
        // Succès
        monitoringService.recordRecommendationRequest(
          type,
          strategy,
          endpoint,
          responseTime,
        );
      }
      
      // Enregistrer le hit du cache
      const cacheHit = req.query.cache !== 'false' && res.getHeader('X-Cache') === 'HIT';
      monitoringService.recordCacheHit(type, endpoint, cacheHit);
      
      // Appeler la méthode send originale
      return originalSend.call(this, body);
    };
    
    next();
  }
}
