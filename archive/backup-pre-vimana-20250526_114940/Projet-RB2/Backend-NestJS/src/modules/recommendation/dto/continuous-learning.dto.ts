import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEnum, IsObject, IsOptional, IsDateString, IsUUID } from 'class-validator';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { Type } from 'class-transformer';

/**
 * DTO pour les requêtes de métriques d'apprentissage
 */
export class LearningMetricsRequestDto {
  @ApiProperty({
    description: 'Date de début de la période',
    example: '2023-01-01T00:00:00Z',
  })
  @IsDateString()
  startDate: string;

  @ApiProperty({
    description: 'Date de fin de la période',
    example: '2023-01-31T23:59:59Z',
  })
  @IsDateString()
  endDate: string;
}

/**
 * DTO pour les requêtes de modèle utilisateur
 */
export class UserModelRequestDto {
  @ApiProperty({
    description: 'ID de l\'utilisateur',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  userId: string;
}

/**
 * DTO pour les événements d'apprentissage
 */
export class LearningEventDto {
  @ApiProperty({
    description: 'Type d\'événement',
    example: 'model_update',
    enum: ['model_update', 'behavior_change', 'preference_change', 'outlier_detected'],
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(['model_update', 'behavior_change', 'preference_change', 'outlier_detected'])
  eventType: 'model_update' | 'behavior_change' | 'preference_change' | 'outlier_detected';

  @ApiProperty({
    description: 'Données de l\'événement',
    example: { category: 'yoga', changeType: 'increase', magnitude: 0.3 },
  })
  @IsObject()
  data: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Impact estimé sur les recommandations (0-1)',
    example: 0.5,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  estimatedImpact?: number;
}

/**
 * DTO pour les paramètres d'apprentissage continu
 */
export class ContinuousLearningParamsDto {
  @ApiPropertyOptional({
    description: 'Taux d\'apprentissage pour les mises à jour incrémentielles',
    example: 0.1,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  learningRate?: number;

  @ApiPropertyOptional({
    description: 'Facteur d\'oubli pour les anciennes interactions',
    example: 0.95,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  forgettingFactor?: number;

  @ApiPropertyOptional({
    description: 'Seuil pour détecter les changements de comportement',
    example: 0.3,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  changeDetectionThreshold?: number;

  @ApiPropertyOptional({
    description: 'Fenêtre temporelle pour l\'analyse des interactions récentes (en heures)',
    example: 24,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  recentInteractionsWindow?: number;

  @ApiPropertyOptional({
    description: 'Nombre minimum d\'interactions pour déclencher une mise à jour',
    example: 5,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  minInteractionsForUpdate?: number;

  @ApiPropertyOptional({
    description: 'Intervalle de mise à jour des modèles (en minutes)',
    example: 15,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  modelUpdateInterval?: number;

  @ApiPropertyOptional({
    description: 'Poids des différents types d\'interactions',
    example: {
      VIEW: 0.1,
      LIKE: 0.5,
      DISLIKE: -0.5,
      BOOKMARK: 0.7,
      SHARE: 0.8,
      PURCHASE: 1.0,
    },
  })
  @IsOptional()
  @IsObject()
  interactionWeights?: Record<string, number>;

  @ApiPropertyOptional({
    description: 'Activer/désactiver la détection des comportements aberrants',
    example: true,
  })
  @IsOptional()
  enableOutlierDetection?: boolean;

  @ApiPropertyOptional({
    description: 'Seuil pour la détection des comportements aberrants',
    example: 2.5,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  outlierDetectionThreshold?: number;
}

/**
 * DTO pour les réponses de métriques d'apprentissage
 */
export class LearningMetricsResponseDto {
  @ApiProperty({
    description: 'ID de l\'utilisateur',
    example: '123e4567-e89b-12d3-a456-************',
  })
  userId: string;

  @ApiProperty({
    description: 'Période d\'analyse',
    example: {
      start: '2023-01-01T00:00:00Z',
      end: '2023-01-31T23:59:59Z',
    },
  })
  period: {
    start: Date;
    end: Date;
  };

  @ApiProperty({
    description: 'Nombre de mises à jour du modèle',
    example: 5,
  })
  modelUpdates: number;

  @ApiProperty({
    description: 'Changements détectés',
    example: [
      {
        type: 'preference',
        timestamp: '2023-01-15T10:30:00Z',
        description: 'Augmentation de l\'intérêt pour la catégorie yoga',
        magnitude: 0.3,
      },
    ],
  })
  detectedChanges: {
    type: 'preference' | 'behavior' | 'context';
    timestamp: Date;
    description: string;
    magnitude: number;
  }[];

  @ApiProperty({
    description: 'Performances avant/après apprentissage',
    example: {
      precisionBefore: 0.75,
      precisionAfter: 0.82,
      recallBefore: 0.68,
      recallAfter: 0.73,
      f1ScoreBefore: 0.71,
      f1ScoreAfter: 0.77,
    },
  })
  performance: {
    precisionBefore: number;
    precisionAfter: number;
    recallBefore: number;
    recallAfter: number;
    f1ScoreBefore: number;
    f1ScoreAfter: number;
  };

  @ApiProperty({
    description: 'Statistiques des interactions',
    example: {
      total: 120,
      byType: {
        VIEW: 80,
        LIKE: 25,
        BOOKMARK: 10,
        SHARE: 5,
      },
      byStrategy: {
        CONTENT_BASED: 50,
        COLLABORATIVE: 40,
        HYBRID: 30,
      },
      byItemType: {
        RETREAT: 70,
        COURSE: 50,
      },
    },
  })
  interactionStats: {
    total: number;
    byType: Record<string, number>;
    byStrategy: Record<string, number>;
    byItemType: Record<string, number>;
  };
}

/**
 * DTO pour les réponses de modèle utilisateur
 */
export class UserModelResponseDto {
  @ApiProperty({
    description: 'ID de l\'utilisateur',
    example: '123e4567-e89b-12d3-a456-************',
  })
  userId: string;

  @ApiProperty({
    description: 'Dernière mise à jour',
    example: '2023-01-31T15:30:00Z',
  })
  lastUpdated: Date;

  @ApiProperty({
    description: 'Préférences de l\'utilisateur',
    example: {
      'language': 'fr',
      'notifications': true,
      'theme': 'dark',
    },
  })
  preferences: Record<string, any>;

  @ApiProperty({
    description: 'Intérêts par catégorie',
    example: {
      'yoga': 0.8,
      'meditation': 0.6,
      'nutrition': 0.4,
    },
  })
  categoryInterests: Record<string, number>;

  @ApiProperty({
    description: 'Intérêts par tag',
    example: {
      'mindfulness': 0.9,
      'stress-relief': 0.7,
      'beginner': 0.5,
    },
  })
  tagInterests: Record<string, number>;

  @ApiProperty({
    description: 'Contexte utilisateur',
    example: {
      location: {
        country: 'France',
        city: 'Paris',
      },
      device: 'mobile',
      preferredTimeOfDay: [18, 19, 20],
      preferredDayOfWeek: [1, 6],
    },
  })
  context: Record<string, any>;

  @ApiProperty({
    description: 'Métriques du modèle',
    example: {
      precision: 0.82,
      recall: 0.73,
      f1Score: 0.77,
      updates: 15,
    },
  })
  metrics: {
    precision: number;
    recall: number;
    f1Score: number;
    updates: number;
  };
}
