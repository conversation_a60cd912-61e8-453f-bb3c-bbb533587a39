import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON>y, IsString, IsN<PERSON>ber, IsBoolean } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { HybridMethod } from '../enums/hybrid-method.enum';

export class UpdatePreferencesDto {
  @ApiPropertyOptional({
    description: 'Stratégie de recommandation préférée',
    enum: RecommendationStrategy,
    example: RecommendationStrategy.HYBRID,
  })
  @IsEnum(RecommendationStrategy)
  @IsOptional()
  recommendationStrategy?: RecommendationStrategy;

  @ApiPropertyOptional({
    description: 'Méthode hybride préférée',
    enum: HybridMethod,
    example: HybridMethod.WEIGHTED,
  })
  @IsEnum(HybridMethod)
  @IsOptional()
  hybridMethod?: HybridMethod;

  @ApiPropertyOptional({
    description: 'Types de recommandation préférés',
    type: [String],
    enum: RecommendationType,
    example: [RecommendationType.COURSE, RecommendationType.RETREAT],
  })
  @IsArray()
  @IsEnum(RecommendationType, { each: true })
  @IsOptional()
  preferredTypes?: RecommendationType[];

  @ApiPropertyOptional({
    description: 'Catégories préférées',
    type: [String],
    example: ['Yoga', 'Meditation'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  preferredCategories?: string[];

  @ApiPropertyOptional({
    description: 'Catégories exclues',
    type: [String],
    example: ['Fitness'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  excludedCategories?: string[];

  @ApiPropertyOptional({
    description: 'Niveaux préférés',
    type: [String],
    example: ['BEGINNER', 'INTERMEDIATE'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  preferredLevels?: string[];

  @ApiPropertyOptional({
    description: 'Localisations préférées',
    type: [String],
    example: ['Paris', 'London'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  preferredLocations?: string[];

  @ApiPropertyOptional({
    description: 'Tags préférés',
    type: [String],
    example: ['yoga', 'meditation', 'wellness'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  preferredTags?: string[];

  @ApiPropertyOptional({
    description: 'Nombre maximum de recommandations',
    example: 10,
  })
  @IsNumber()
  @IsOptional()
  maxRecommendations?: number;

  @ApiPropertyOptional({
    description: 'Inclure les métadonnées dans les recommandations',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  includeMetadata?: boolean;
}
