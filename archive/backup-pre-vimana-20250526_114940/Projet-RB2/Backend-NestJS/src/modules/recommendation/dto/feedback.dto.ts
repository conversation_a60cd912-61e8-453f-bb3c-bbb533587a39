import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsNumber, IsObject, IsArray, IsBoolean, IsUUID, Min, Max, IsNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { FeedbackType } from '../services/feedback.service';

/**
 * DTO pour l'enregistrement d'un feedback simple
 */
export class RecordFeedbackDto {
  @ApiProperty({
    description: 'ID de la recommandation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  recommendationId: string;

  @ApiProperty({
    description: 'Type de recommandation',
    enum: RecommendationType,
    example: RecommendationType.RETREAT,
  })
  @IsEnum(RecommendationType)
  recommendationType: RecommendationType;

  @ApiProperty({
    description: 'Type de feedback',
    enum: FeedbackType,
    example: FeedbackType.RELEVANT,
  })
  @IsEnum(FeedbackType)
  feedbackType: FeedbackType;

  @ApiPropertyOptional({
    description: 'Commentaire optionnel',
    example: 'Cette recommandation était très pertinente pour moi.',
  })
  @IsOptional()
  @IsString()
  comment?: string;

  @ApiPropertyOptional({
    description: 'Note (de 1 à 5)',
    example: 4.5,
    minimum: 1,
    maximum: 5,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(5)
  @Type(() => Number)
  rating?: number;

  @ApiPropertyOptional({
    description: 'Métadonnées supplémentaires',
    example: { source: 'homepage', sessionId: '12345' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO pour l'enregistrement d'un feedback détaillé
 */
export class DetailedFeedbackDto extends RecordFeedbackDto {
  @ApiProperty({
    description: 'Note (de 1 à 5)',
    example: 4.5,
    minimum: 1,
    maximum: 5,
  })
  @IsNumber()
  @Min(1)
  @Max(5)
  @Type(() => Number)
  rating: number;

  @ApiProperty({
    description: 'Commentaire détaillé',
    example: 'Cette recommandation était très pertinente pour moi car elle correspond parfaitement à mes intérêts en yoga et méditation.',
  })
  @IsString()
  @IsNotEmpty()
  comment: string;

  @ApiPropertyOptional({
    description: 'Aspects spécifiques évalués',
    example: [
      { name: 'pertinence', rating: 5 },
      { name: 'originalité', rating: 4 },
      { name: 'timing', rating: 3 },
    ],
    type: [Object],
  })
  @IsOptional()
  @IsArray()
  aspects?: { name: string; rating: number }[];

  @ApiPropertyOptional({
    description: 'Indique si l\'utilisateur a trouvé la recommandation utile',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isUseful?: boolean;

  @ApiPropertyOptional({
    description: 'Indique si l\'utilisateur a agi sur la recommandation',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  hasActedUpon?: boolean;

  @ApiPropertyOptional({
    description: 'Suggestions d\'amélioration',
    example: 'J\'aimerais voir plus de recommandations de ce type mais avec des options de prix plus variées.',
  })
  @IsOptional()
  @IsString()
  suggestions?: string;
}

/**
 * DTO pour le signalement d'une recommandation inappropriée
 */
export class ReportRecommendationDto {
  @ApiProperty({
    description: 'ID de la recommandation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  recommendationId: string;

  @ApiProperty({
    description: 'Type de recommandation',
    enum: RecommendationType,
    example: RecommendationType.RETREAT,
  })
  @IsEnum(RecommendationType)
  recommendationType: RecommendationType;

  @ApiProperty({
    description: 'Raison du signalement',
    example: 'INAPPROPRIATE',
    enum: ['INAPPROPRIATE', 'MISLEADING', 'OFFENSIVE', 'SPAM', 'OTHER'],
  })
  @IsString()
  @IsEnum(['INAPPROPRIATE', 'MISLEADING', 'OFFENSIVE', 'SPAM', 'OTHER'])
  reason: 'INAPPROPRIATE' | 'MISLEADING' | 'OFFENSIVE' | 'SPAM' | 'OTHER';

  @ApiProperty({
    description: 'Description détaillée du problème',
    example: 'Cette recommandation contient du contenu inapproprié car...',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiPropertyOptional({
    description: 'Métadonnées supplémentaires',
    example: { browser: 'Chrome', device: 'Desktop' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO pour la réponse de feedback
 */
export class FeedbackResponseDto {
  @ApiProperty({
    description: 'Indique si l\'opération a réussi',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Message de l\'opération',
    example: 'Feedback enregistré avec succès',
  })
  message: string;

  @ApiProperty({
    description: 'Données du feedback',
    example: {
      id: '123e4567-e89b-12d3-a456-************',
      userId: '123e4567-e89b-12d3-a456-************',
      recommendationId: '123e4567-e89b-12d3-a456-************',
      recommendationType: 'RETREAT',
      feedbackType: 'RELEVANT',
      comment: 'Très pertinent',
      rating: 4.5,
      createdAt: '2023-06-15T10:30:00Z',
    },
  })
  data: any;
}

/**
 * DTO pour les options de pagination et filtrage des feedbacks
 */
export class FeedbackQueryOptionsDto {
  @ApiPropertyOptional({
    description: 'Numéro de page',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  page?: number;

  @ApiPropertyOptional({
    description: 'Nombre d\'éléments par page',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Type de feedback',
    enum: FeedbackType,
    example: FeedbackType.RELEVANT,
  })
  @IsOptional()
  @IsEnum(FeedbackType)
  feedbackType?: FeedbackType;

  @ApiPropertyOptional({
    description: 'Type de recommandation',
    enum: RecommendationType,
    example: RecommendationType.RETREAT,
  })
  @IsOptional()
  @IsEnum(RecommendationType)
  recommendationType?: RecommendationType;
}
