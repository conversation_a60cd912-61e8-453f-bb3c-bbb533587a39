import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsEnum, IsOptional, IsString } from 'class-validator';

export enum ContentDuration {
  SHORT = 'short',
  MEDIUM = 'medium',
  LONG = 'long',
}

export class SocialVideoPreferencesDto {
  @ApiProperty({
    description: 'Types de contenu préférés',
    example: ['video', 'post', 'livestream'],
    required: false,
    isArray: true,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  contentTypes?: string[];

  @ApiProperty({
    description: 'Catégories préférées',
    example: ['yoga', 'meditation', 'fitness'],
    required: false,
    isArray: true,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  categories?: string[];

  @ApiProperty({
    description: 'Tags exclus',
    example: ['politics', 'violence'],
    required: false,
    isArray: true,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  excludedTags?: string[];

  @ApiProperty({
    description: 'Créateurs exclus',
    example: ['creator-id-1', 'creator-id-2'],
    required: false,
    isArray: true,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  excludedCreators?: string[];

  @ApiProperty({
    description: 'Langues préférées',
    example: ['fr', 'en'],
    required: false,
    isArray: true,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  preferredLanguages?: string[];

  @ApiProperty({
    description: 'Durée préférée',
    enum: ContentDuration,
    example: ContentDuration.MEDIUM,
    required: false,
  })
  @IsOptional()
  @IsEnum(ContentDuration)
  preferredDuration?: ContentDuration;

  @ApiProperty({
    description: 'Préférer le contenu récent',
    example: true,
    required: false,
    type: Boolean,
  })
  @IsOptional()
  @IsBoolean()
  preferNewContent?: boolean;

  @ApiProperty({
    description: 'Préférer le contenu des créateurs suivis',
    example: true,
    required: false,
    type: Boolean,
  })
  @IsOptional()
  @IsBoolean()
  preferFollowingContent?: boolean;
}
