import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { 
  IsDate, 
  IsEnum, 
  IsNumber, 
  IsObject, 
  IsOptional, 
  IsString, 
  IsArray,
  IsBoolean,
  ValidateNested,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';

/**
 * DTO pour les paramètres d'évaluation
 */
export class EvaluationParametersDto {
  @ApiPropertyOptional({
    description: 'Période d\'évaluation',
    example: {
      start: '2023-01-01T00:00:00Z',
      end: '2023-01-31T23:59:59Z',
    },
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => PeriodDto)
  period?: PeriodDto;

  @ApiPropertyOptional({
    description: 'Type de recommandation à évaluer',
    enum: RecommendationType,
  })
  @IsOptional()
  @IsEnum(RecommendationType)
  recommendationType?: RecommendationType;

  @ApiPropertyOptional({
    description: 'Stratégie de recommandation à évaluer',
    enum: RecommendationStrategy,
  })
  @IsOptional()
  @IsEnum(RecommendationStrategy)
  recommendationStrategy?: RecommendationStrategy;

  @ApiPropertyOptional({
    description: 'Comparer avec d\'autres stratégies',
    enum: RecommendationStrategy,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(RecommendationStrategy, { each: true })
  compareWithStrategies?: RecommendationStrategy[];

  @ApiPropertyOptional({
    description: 'Taille de l\'échantillon pour l\'évaluation',
    minimum: 1,
    default: 1000,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  sampleSize?: number;

  @ApiPropertyOptional({
    description: 'Métriques à inclure',
    example: {
      accuracy: true,
      engagement: true,
      business: true,
      diversity: true,
      performance: true,
      custom: false,
    },
  })
  @IsOptional()
  @IsObject()
  includeMetrics?: {
    accuracy?: boolean;
    engagement?: boolean;
    business?: boolean;
    diversity?: boolean;
    performance?: boolean;
    custom?: boolean;
  };

  @ApiPropertyOptional({
    description: 'Seuils d\'alerte',
    example: {
      precision: 0.7,
      recall: 0.6,
      f1Score: 0.65,
      clickThroughRate: 0.05,
      conversionRate: 0.02,
      diversityScore: 0.6,
      fairnessScore: 0.7,
      responseTime: 200,
      errorRate: 0.01,
    },
  })
  @IsOptional()
  @IsObject()
  alertThresholds?: {
    precision?: number;
    recall?: number;
    f1Score?: number;
    clickThroughRate?: number;
    conversionRate?: number;
    diversityScore?: number;
    fairnessScore?: number;
    responseTime?: number;
    errorRate?: number;
  };
}

/**
 * DTO pour une période
 */
export class PeriodDto {
  @ApiProperty({
    description: 'Date de début',
    example: '2023-01-01T00:00:00Z',
  })
  @Type(() => Date)
  @IsDate()
  start: Date;

  @ApiProperty({
    description: 'Date de fin',
    example: '2023-01-31T23:59:59Z',
  })
  @Type(() => Date)
  @IsDate()
  end: Date;
}

/**
 * DTO pour les métriques de précision
 */
export class AccuracyMetricsDto {
  @ApiProperty({
    description: 'Précision (vrais positifs / (vrais positifs + faux positifs))',
    minimum: 0,
    maximum: 1,
    example: 0.85,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  precision: number;

  @ApiProperty({
    description: 'Rappel (vrais positifs / (vrais positifs + faux négatifs))',
    minimum: 0,
    maximum: 1,
    example: 0.78,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  recall: number;

  @ApiProperty({
    description: 'Score F1 (2 * (précision * rappel) / (précision + rappel))',
    minimum: 0,
    maximum: 1,
    example: 0.81,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  f1Score: number;

  @ApiPropertyOptional({
    description: 'Précision moyenne (MAP)',
    minimum: 0,
    maximum: 1,
    example: 0.79,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  meanAveragePrecision?: number;

  @ApiPropertyOptional({
    description: 'Gain cumulatif actualisé normalisé (NDCG)',
    minimum: 0,
    maximum: 1,
    example: 0.83,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  ndcg?: number;

  @ApiPropertyOptional({
    description: 'Erreur quadratique moyenne (MSE)',
    minimum: 0,
    example: 0.15,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  meanSquaredError?: number;

  @ApiPropertyOptional({
    description: 'Erreur absolue moyenne (MAE)',
    minimum: 0,
    example: 0.12,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  meanAbsoluteError?: number;

  @ApiPropertyOptional({
    description: 'Matrice de confusion',
    example: {
      truePositives: 850,
      falsePositives: 150,
      trueNegatives: 900,
      falseNegatives: 100,
    },
  })
  @IsOptional()
  @IsObject()
  confusionMatrix?: {
    truePositives: number;
    falsePositives: number;
    trueNegatives: number;
    falseNegatives: number;
  };
}

/**
 * DTO pour les métriques d'engagement
 */
export class EngagementMetricsDto {
  @ApiProperty({
    description: 'Taux de clics (CTR)',
    minimum: 0,
    maximum: 1,
    example: 0.12,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  clickThroughRate: number;

  @ApiProperty({
    description: 'Temps moyen passé (en secondes)',
    minimum: 0,
    example: 120,
  })
  @IsNumber()
  @Min(0)
  averageTimeSpent: number;

  @ApiProperty({
    description: 'Taux de rebond',
    minimum: 0,
    maximum: 1,
    example: 0.35,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  bounceRate: number;

  @ApiProperty({
    description: 'Nombre moyen de pages vues',
    minimum: 0,
    example: 3.5,
  })
  @IsNumber()
  @Min(0)
  averagePageViews: number;

  @ApiProperty({
    description: 'Taux d\'interaction',
    minimum: 0,
    maximum: 1,
    example: 0.25,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  interactionRate: number;

  @ApiProperty({
    description: 'Détails des interactions par type',
    example: {
      VIEW: 1000,
      CLICK: 120,
      LIKE: 50,
      BOOKMARK: 30,
      SHARE: 15,
    },
  })
  @IsObject()
  interactionsByType: Record<string, number>;

  @ApiPropertyOptional({
    description: 'Taux de retour',
    minimum: 0,
    maximum: 1,
    example: 0.4,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  returnRate?: number;

  @ApiProperty({
    description: 'Score d\'engagement global (0-1)',
    minimum: 0,
    maximum: 1,
    example: 0.65,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  overallEngagementScore: number;
}

/**
 * DTO pour les métriques commerciales
 */
export class BusinessMetricsDto {
  @ApiProperty({
    description: 'Taux de conversion',
    minimum: 0,
    maximum: 1,
    example: 0.05,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  conversionRate: number;

  @ApiProperty({
    description: 'Revenu généré',
    minimum: 0,
    example: 12500,
  })
  @IsNumber()
  @Min(0)
  revenue: number;

  @ApiPropertyOptional({
    description: 'Valeur moyenne des commandes',
    minimum: 0,
    example: 250,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  averageOrderValue?: number;

  @ApiPropertyOptional({
    description: 'Retour sur investissement (ROI)',
    example: 3.5,
  })
  @IsOptional()
  @IsNumber()
  roi?: number;

  @ApiPropertyOptional({
    description: 'Coût par acquisition (CPA)',
    minimum: 0,
    example: 50,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  costPerAcquisition?: number;

  @ApiPropertyOptional({
    description: 'Valeur vie client (LTV)',
    minimum: 0,
    example: 1200,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  customerLifetimeValue?: number;

  @ApiPropertyOptional({
    description: 'Taux de rétention',
    minimum: 0,
    maximum: 1,
    example: 0.7,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  retentionRate?: number;

  @ApiProperty({
    description: 'Impact sur les revenus (%)',
    example: 15,
  })
  @IsNumber()
  revenueImpact: number;
}

/**
 * DTO pour les métriques de diversité et d'équité
 */
export class DiversityMetricsDto {
  @ApiProperty({
    description: 'Score de diversité (0-1)',
    minimum: 0,
    maximum: 1,
    example: 0.75,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  diversityScore: number;

  @ApiProperty({
    description: 'Score d\'équité (0-1)',
    minimum: 0,
    maximum: 1,
    example: 0.8,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  fairnessScore: number;

  @ApiProperty({
    description: 'Score de couverture du catalogue (0-1)',
    minimum: 0,
    maximum: 1,
    example: 0.6,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  catalogCoverage: number;

  @ApiPropertyOptional({
    description: 'Entropie des recommandations',
    minimum: 0,
    example: 3.2,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  entropy?: number;

  @ApiPropertyOptional({
    description: 'Indice de Gini',
    minimum: 0,
    maximum: 1,
    example: 0.4,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  giniIndex?: number;

  @ApiPropertyOptional({
    description: 'Score de surprise',
    minimum: 0,
    maximum: 1,
    example: 0.5,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  surpriseScore?: number;

  @ApiPropertyOptional({
    description: 'Score de nouveauté',
    minimum: 0,
    maximum: 1,
    example: 0.6,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  noveltyScore?: number;

  @ApiPropertyOptional({
    description: 'Biais détectés',
    example: [
      {
        type: 'price',
        description: 'Biais vers les produits à prix élevé',
        severity: 0.7,
      },
    ],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DetectedBiasDto)
  detectedBiases?: DetectedBiasDto[];
}

/**
 * DTO pour un biais détecté
 */
export class DetectedBiasDto {
  @ApiProperty({
    description: 'Type de biais',
    example: 'price',
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'Description du biais',
    example: 'Biais vers les produits à prix élevé',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Sévérité du biais (0-1)',
    minimum: 0,
    maximum: 1,
    example: 0.7,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  severity: number;
}

/**
 * DTO pour les métriques de performance
 */
export class PerformanceMetricsDto {
  @ApiProperty({
    description: 'Temps de réponse moyen (ms)',
    minimum: 0,
    example: 150,
  })
  @IsNumber()
  @Min(0)
  averageResponseTime: number;

  @ApiProperty({
    description: 'Temps de réponse au 95e percentile (ms)',
    minimum: 0,
    example: 250,
  })
  @IsNumber()
  @Min(0)
  p95ResponseTime: number;

  @ApiProperty({
    description: 'Temps de réponse au 99e percentile (ms)',
    minimum: 0,
    example: 350,
  })
  @IsNumber()
  @Min(0)
  p99ResponseTime: number;

  @ApiProperty({
    description: 'Utilisation CPU (%)',
    minimum: 0,
    maximum: 100,
    example: 45,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  cpuUsage: number;

  @ApiProperty({
    description: 'Utilisation mémoire (MB)',
    minimum: 0,
    example: 512,
  })
  @IsNumber()
  @Min(0)
  memoryUsage: number;

  @ApiProperty({
    description: 'Nombre de requêtes par seconde',
    minimum: 0,
    example: 50,
  })
  @IsNumber()
  @Min(0)
  requestsPerSecond: number;

  @ApiProperty({
    description: 'Taux d\'erreur (%)',
    minimum: 0,
    maximum: 100,
    example: 0.5,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  errorRate: number;

  @ApiPropertyOptional({
    description: 'Taux d\'utilisation du cache (%)',
    minimum: 0,
    maximum: 100,
    example: 85,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  cacheHitRate?: number;

  @ApiProperty({
    description: 'Temps moyen de génération des recommandations (ms)',
    minimum: 0,
    example: 120,
  })
  @IsNumber()
  @Min(0)
  averageGenerationTime: number;
}

/**
 * DTO pour les métriques d'évaluation complètes
 */
export class EvaluationMetricsDto {
  @ApiPropertyOptional({
    description: 'ID de l\'évaluation',
  })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({
    description: 'Timestamp de l\'évaluation',
  })
  @Type(() => Date)
  @IsDate()
  timestamp: Date;

  @ApiProperty({
    description: 'Période d\'évaluation',
    type: PeriodDto,
  })
  @ValidateNested()
  @Type(() => PeriodDto)
  period: PeriodDto;

  @ApiPropertyOptional({
    description: 'Type de recommandation évalué',
    enum: RecommendationType,
  })
  @IsOptional()
  @IsEnum(RecommendationType)
  recommendationType?: RecommendationType;

  @ApiPropertyOptional({
    description: 'Stratégie de recommandation évaluée',
    enum: RecommendationStrategy,
  })
  @IsOptional()
  @IsEnum(RecommendationStrategy)
  recommendationStrategy?: RecommendationStrategy;

  @ApiProperty({
    description: 'Métriques de précision',
    type: AccuracyMetricsDto,
  })
  @ValidateNested()
  @Type(() => AccuracyMetricsDto)
  accuracyMetrics: AccuracyMetricsDto;

  @ApiProperty({
    description: 'Métriques d\'engagement',
    type: EngagementMetricsDto,
  })
  @ValidateNested()
  @Type(() => EngagementMetricsDto)
  engagementMetrics: EngagementMetricsDto;

  @ApiProperty({
    description: 'Métriques commerciales',
    type: BusinessMetricsDto,
  })
  @ValidateNested()
  @Type(() => BusinessMetricsDto)
  businessMetrics: BusinessMetricsDto;

  @ApiProperty({
    description: 'Métriques de diversité et d\'équité',
    type: DiversityMetricsDto,
  })
  @ValidateNested()
  @Type(() => DiversityMetricsDto)
  diversityMetrics: DiversityMetricsDto;

  @ApiProperty({
    description: 'Métriques de performance',
    type: PerformanceMetricsDto,
  })
  @ValidateNested()
  @Type(() => PerformanceMetricsDto)
  performanceMetrics: PerformanceMetricsDto;

  @ApiPropertyOptional({
    description: 'Métriques personnalisées',
  })
  @IsOptional()
  @IsObject()
  customMetrics?: Record<string, any>;
}
