import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { 
  IsEnum, 
  IsString, 
  IsArray, 
  IsOptional, 
  IsObject, 
  ValidateNested,
  IsBoolean,
  IsIn,
} from 'class-validator';
import { Type } from 'class-transformer';
import { 
  ExplanationStyle, 
  DetailLevel, 
  ExplanationFormat,
} from '../interfaces/explanation-preferences.interface';

/**
 * DTO pour les préférences culturelles
 */
export class CulturalPreferencesDto {
  @ApiPropertyOptional({
    description: 'Région culturelle',
    example: 'western',
  })
  @IsOptional()
  @IsString()
  region?: string;

  @ApiPropertyOptional({
    description: 'Style de communication préféré',
    enum: ['direct', 'indirect', 'contextual'],
    example: 'direct',
  })
  @IsOptional()
  @IsIn(['direct', 'indirect', 'contextual'])
  communicationStyle?: 'direct' | 'indirect' | 'contextual';

  @ApiPropertyOptional({
    description: 'Préférences pour les exemples et métaphores',
    type: [String],
    example: ['sports', 'nature', 'technology'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  examplePreferences?: string[];

  @ApiPropertyOptional({
    description: 'Sensibilité aux références culturelles',
    enum: ['low', 'medium', 'high'],
    example: 'medium',
  })
  @IsOptional()
  @IsIn(['low', 'medium', 'high'])
  culturalSensitivity?: 'low' | 'medium' | 'high';
}

/**
 * DTO pour les préférences d'accessibilité
 */
export class AccessibilityPreferencesDto {
  @ApiPropertyOptional({
    description: 'Préférence pour un texte à contraste élevé',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  highContrast?: boolean;

  @ApiPropertyOptional({
    description: 'Préférence pour un texte plus grand',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  largeText?: boolean;

  @ApiPropertyOptional({
    description: 'Préférence pour des descriptions alternatives des éléments visuels',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  screenReaderOptimized?: boolean;

  @ApiPropertyOptional({
    description: 'Préférence pour éviter les animations',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  reduceMotion?: boolean;

  @ApiPropertyOptional({
    description: 'Préférence pour un langage simplifié',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  simplifiedLanguage?: boolean;
}

/**
 * DTO pour la mise à jour des préférences d'explication
 */
export class UpdateExplanationPreferencesDto {
  @ApiPropertyOptional({
    description: 'Style d\'explication préféré',
    enum: ExplanationStyle,
    example: ExplanationStyle.CONVERSATIONAL,
  })
  @IsOptional()
  @IsEnum(ExplanationStyle)
  preferredStyle?: ExplanationStyle;

  @ApiPropertyOptional({
    description: 'Niveau de détail préféré',
    enum: DetailLevel,
    example: DetailLevel.MODERATE,
  })
  @IsOptional()
  @IsEnum(DetailLevel)
  detailLevel?: DetailLevel;

  @ApiPropertyOptional({
    description: 'Format d\'explication préféré',
    type: [String],
    enum: ExplanationFormat,
    example: [ExplanationFormat.MIXED],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(ExplanationFormat, { each: true })
  preferredFormat?: ExplanationFormat[];

  @ApiPropertyOptional({
    description: 'Facteurs à mettre en évidence',
    type: [String],
    example: ['similarity', 'popularity'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  highlightedFactors?: string[];

  @ApiPropertyOptional({
    description: 'Facteurs à masquer',
    type: [String],
    example: ['price'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  hiddenFactors?: string[];

  @ApiPropertyOptional({
    description: 'Langue préférée pour les explications',
    example: 'fr',
  })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiPropertyOptional({
    description: 'Préférences culturelles',
    type: CulturalPreferencesDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => CulturalPreferencesDto)
  culturalPreferences?: CulturalPreferencesDto;

  @ApiPropertyOptional({
    description: 'Préférences d\'accessibilité',
    type: AccessibilityPreferencesDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AccessibilityPreferencesDto)
  accessibilityPreferences?: AccessibilityPreferencesDto;

  @ApiPropertyOptional({
    description: 'Métadonnées supplémentaires',
    example: { source: 'user_survey' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO pour la réponse des préférences d'explication
 */
export class ExplanationPreferencesResponseDto {
  @ApiProperty({
    description: 'ID de l\'utilisateur',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  userId: string;

  @ApiProperty({
    description: 'Style d\'explication préféré',
    enum: ExplanationStyle,
    example: ExplanationStyle.CONVERSATIONAL,
  })
  preferredStyle: ExplanationStyle;

  @ApiProperty({
    description: 'Niveau de détail préféré',
    enum: DetailLevel,
    example: DetailLevel.MODERATE,
  })
  detailLevel: DetailLevel;

  @ApiProperty({
    description: 'Format d\'explication préféré',
    type: [String],
    enum: ExplanationFormat,
    example: [ExplanationFormat.MIXED],
  })
  preferredFormat: ExplanationFormat[];

  @ApiProperty({
    description: 'Facteurs à mettre en évidence',
    type: [String],
    example: ['similarity', 'popularity'],
  })
  highlightedFactors: string[];

  @ApiProperty({
    description: 'Facteurs à masquer',
    type: [String],
    example: ['price'],
  })
  hiddenFactors: string[];

  @ApiProperty({
    description: 'Langue préférée pour les explications',
    example: 'fr',
  })
  language: string;

  @ApiProperty({
    description: 'Préférences culturelles',
    type: CulturalPreferencesDto,
  })
  culturalPreferences: CulturalPreferencesDto;

  @ApiProperty({
    description: 'Préférences d\'accessibilité',
    type: AccessibilityPreferencesDto,
  })
  accessibilityPreferences: AccessibilityPreferencesDto;

  @ApiPropertyOptional({
    description: 'Métadonnées supplémentaires',
    example: { source: 'user_survey' },
  })
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'Date de création',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Date de mise à jour',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}
