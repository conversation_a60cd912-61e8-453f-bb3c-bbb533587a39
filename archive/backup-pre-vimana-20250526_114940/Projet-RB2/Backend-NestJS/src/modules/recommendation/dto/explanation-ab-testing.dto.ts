import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { 
  IsString, 
  IsNotEmpty, 
  IsEnum, 
  IsObject, 
  IsOptional, 
  IsDateString, 
  IsUUID, 
  IsNumber, 
  IsArray, 
  ValidateNested, 
  Min, 
  Max,
  ArrayMinSize,
  ArrayMaxSize,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ExplanationVariantType } from '@prisma/client';

/**
 * DTO pour la création d'une variante d'explication
 */
export class CreateExplanationVariantDto {
  @ApiProperty({
    description: 'Nom de la variante',
    example: 'Variante détaillée',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Description de la variante',
    example: 'Version détaillée des explications avec tous les facteurs',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Type de variante',
    enum: ExplanationVariantType,
    example: ExplanationVariantType.DETAILED,
  })
  @IsEnum(ExplanationVariantType)
  type: ExplanationVariantType;

  @ApiProperty({
    description: 'Configuration de la variante',
    example: {
      maxFactors: 5,
      includeNegativeFactors: true,
      detailLevel: 'detailed',
      showConfidence: true,
    },
  })
  @IsObject()
  configuration: Record<string, any>;

  @ApiProperty({
    description: 'Allocation de la variante (0-1)',
    example: 0.5,
    minimum: 0,
    maximum: 1,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  allocation: number;
}

/**
 * DTO pour la création d'un test A/B d'explications
 */
export class CreateExplanationABTestDto {
  @ApiProperty({
    description: 'Nom du test',
    example: 'Test de niveau de détail des explications',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Description du test',
    example: 'Comparer différents niveaux de détail dans les explications de recommandation',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Date de début du test',
    example: '2023-07-01T00:00:00Z',
  })
  @IsDateString()
  startDate: string;

  @ApiProperty({
    description: 'Date de fin du test',
    example: '2023-07-31T23:59:59Z',
  })
  @IsDateString()
  endDate: string;

  @ApiProperty({
    description: 'Variantes du test',
    type: [CreateExplanationVariantDto],
    minItems: 2,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateExplanationVariantDto)
  @ArrayMinSize(2)
  variants: CreateExplanationVariantDto[];
}

/**
 * DTO pour la mise à jour d'un test A/B d'explications
 */
export class UpdateExplanationABTestDto {
  @ApiPropertyOptional({
    description: 'Nom du test',
    example: 'Test de niveau de détail des explications (mis à jour)',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiPropertyOptional({
    description: 'Description du test',
    example: 'Comparer différents niveaux de détail dans les explications de recommandation (mis à jour)',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  description?: string;

  @ApiPropertyOptional({
    description: 'Date de début du test',
    example: '2023-07-01T00:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Date de fin du test',
    example: '2023-07-31T23:59:59Z',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Statut du test',
    example: 'PAUSED',
    enum: ['ACTIVE', 'PAUSED', 'COMPLETED', 'CANCELLED'],
  })
  @IsOptional()
  @IsString()
  @IsEnum(['ACTIVE', 'PAUSED', 'COMPLETED', 'CANCELLED'])
  status?: string;
}

/**
 * DTO pour la mise à jour d'une variante d'explication
 */
export class UpdateExplanationVariantDto {
  @ApiPropertyOptional({
    description: 'Nom de la variante',
    example: 'Variante détaillée (mis à jour)',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiPropertyOptional({
    description: 'Description de la variante',
    example: 'Version détaillée des explications avec tous les facteurs (mis à jour)',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  description?: string;

  @ApiPropertyOptional({
    description: 'Configuration de la variante',
    example: {
      maxFactors: 7,
      includeNegativeFactors: true,
      detailLevel: 'detailed',
      showConfidence: true,
    },
  })
  @IsOptional()
  @IsObject()
  configuration?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Allocation de la variante (0-1)',
    example: 0.6,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  allocation?: number;
}

/**
 * DTO pour l'enregistrement d'une interaction avec un test A/B
 */
export class RecordExplanationABTestInteractionDto {
  @ApiProperty({
    description: 'ID de la recommandation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  recommendationId: string;

  @ApiProperty({
    description: 'Type d\'interaction',
    example: 'VIEW',
    enum: ['VIEW', 'EXPAND', 'CLICK', 'DISMISS', 'FEEDBACK'],
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(['VIEW', 'EXPAND', 'CLICK', 'DISMISS', 'FEEDBACK'])
  interactionType: string;

  @ApiPropertyOptional({
    description: 'Données supplémentaires',
    example: {
      timeSpent: 15,
      feedbackRating: 4,
      comment: 'Très utile',
    },
  })
  @IsOptional()
  @IsObject()
  data?: Record<string, any>;
}

/**
 * DTO pour la réponse d'un test A/B d'explications
 */
export class ExplanationABTestResponseDto {
  @ApiProperty({
    description: 'ID du test',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Nom du test',
    example: 'Test de niveau de détail des explications',
  })
  name: string;

  @ApiProperty({
    description: 'Description du test',
    example: 'Comparer différents niveaux de détail dans les explications de recommandation',
  })
  description: string;

  @ApiProperty({
    description: 'Date de début du test',
    example: '2023-07-01T00:00:00Z',
  })
  startDate: Date;

  @ApiProperty({
    description: 'Date de fin du test',
    example: '2023-07-31T23:59:59Z',
  })
  endDate: Date;

  @ApiProperty({
    description: 'Statut du test',
    example: 'ACTIVE',
  })
  status: string;

  @ApiProperty({
    description: 'Variantes du test',
    type: [ExplanationVariantResponseDto],
  })
  variants: ExplanationVariantResponseDto[];

  @ApiProperty({
    description: 'Date de création',
    example: '2023-06-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Date de mise à jour',
    example: '2023-06-15T10:30:00Z',
  })
  updatedAt: Date;
}

/**
 * DTO pour la réponse d'une variante d'explication
 */
export class ExplanationVariantResponseDto {
  @ApiProperty({
    description: 'ID de la variante',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'ID du test',
    example: '123e4567-e89b-12d3-a456-************',
  })
  testId: string;

  @ApiProperty({
    description: 'Nom de la variante',
    example: 'Variante détaillée',
  })
  name: string;

  @ApiProperty({
    description: 'Description de la variante',
    example: 'Version détaillée des explications avec tous les facteurs',
  })
  description: string;

  @ApiProperty({
    description: 'Type de variante',
    enum: ExplanationVariantType,
    example: ExplanationVariantType.DETAILED,
  })
  type: ExplanationVariantType;

  @ApiProperty({
    description: 'Configuration de la variante',
    example: {
      maxFactors: 5,
      includeNegativeFactors: true,
      detailLevel: 'detailed',
      showConfidence: true,
    },
  })
  configuration: Record<string, any>;

  @ApiProperty({
    description: 'Allocation de la variante (0-1)',
    example: 0.5,
  })
  allocation: number;

  @ApiProperty({
    description: 'Date de création',
    example: '2023-06-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Date de mise à jour',
    example: '2023-06-15T10:30:00Z',
  })
  updatedAt: Date;
}

/**
 * DTO pour la réponse des résultats d'un test A/B
 */
export class ExplanationABTestResultsResponseDto {
  @ApiProperty({
    description: 'ID du test',
    example: '123e4567-e89b-12d3-a456-************',
  })
  testId: string;

  @ApiProperty({
    description: 'Nom du test',
    example: 'Test de niveau de détail des explications',
  })
  testName: string;

  @ApiProperty({
    description: 'Période du test',
    example: {
      start: '2023-07-01T00:00:00Z',
      end: '2023-07-31T23:59:59Z',
    },
  })
  period: {
    start: Date;
    end: Date;
  };

  @ApiProperty({
    description: 'Nombre total d\'utilisateurs',
    example: 1250,
  })
  totalUsers: number;

  @ApiProperty({
    description: 'Résultats par variante',
    type: [ExplanationVariantResultsDto],
  })
  variantResults: ExplanationVariantResultsDto[];

  @ApiPropertyOptional({
    description: 'Variante gagnante',
    type: ExplanationVariantWinnerDto,
  })
  winner?: ExplanationVariantWinnerDto;

  @ApiProperty({
    description: 'Recommandations',
    example: [
      'La variante détaillée a montré une amélioration significative du taux de conversion',
      'Envisager d\'implémenter la variante détaillée comme option par défaut',
    ],
  })
  recommendations: string[];
}

/**
 * DTO pour les résultats d'une variante
 */
export class ExplanationVariantResultsDto {
  @ApiProperty({
    description: 'ID de la variante',
    example: '123e4567-e89b-12d3-a456-************',
  })
  variantId: string;

  @ApiProperty({
    description: 'Nom de la variante',
    example: 'Variante détaillée',
  })
  variantName: string;

  @ApiProperty({
    description: 'Type de variante',
    enum: ExplanationVariantType,
    example: ExplanationVariantType.DETAILED,
  })
  variantType: ExplanationVariantType;

  @ApiProperty({
    description: 'Nombre d\'utilisateurs assignés à cette variante',
    example: 625,
  })
  userCount: number;

  @ApiProperty({
    description: 'Nombre d\'impressions',
    example: 1850,
  })
  impressions: number;

  @ApiProperty({
    description: 'Nombre d\'interactions',
    example: 780,
  })
  interactions: number;

  @ApiProperty({
    description: 'Taux d\'interaction',
    example: 0.42,
  })
  interactionRate: number;

  @ApiProperty({
    description: 'Taux de conversion',
    example: 0.18,
  })
  conversionRate: number;

  @ApiProperty({
    description: 'Temps moyen passé sur les explications (en secondes)',
    example: 25.3,
  })
  averageTimeSpent: number;

  @ApiProperty({
    description: 'Score de satisfaction',
    example: 4.2,
  })
  satisfactionScore: number;
}

/**
 * DTO pour la variante gagnante
 */
export class ExplanationVariantWinnerDto {
  @ApiProperty({
    description: 'ID de la variante',
    example: '123e4567-e89b-12d3-a456-************',
  })
  variantId: string;

  @ApiProperty({
    description: 'Nom de la variante',
    example: 'Variante détaillée',
  })
  variantName: string;

  @ApiProperty({
    description: 'Amélioration par rapport à la variante de contrôle',
    example: 0.15,
  })
  improvement: number;

  @ApiProperty({
    description: 'Niveau de confiance',
    example: 0.95,
  })
  confidenceLevel: number;
}
