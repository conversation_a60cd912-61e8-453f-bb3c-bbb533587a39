import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator';

export enum SocialVideoInteractionType {
  VIEW = 'VIEW',
  LIKE = 'LIKE',
  DISLIKE = 'DISLIKE',
  SHARE = 'SHARE',
  COMMENT = 'COMMENT',
  SAVE = 'SAVE',
  WATCH_COMPLETE = 'WATCH_COMPLETE',
  WATCH_PARTIAL = 'WATCH_PARTIAL',
  FOLLOW_CREATOR = 'FOLLOW_CREATOR',
  UNFOLLOW_CREATOR = 'UNFOLLOW_CREATOR',
}

export enum SocialVideoContentType {
  VIDEO = 'VIDEO',
  POST = 'POST',
  LIVESTREAM = 'LIVESTREAM',
  STORY = 'STORY',
}

export class SocialVideoInteractionDto {
  @ApiProperty({
    description: 'ID du contenu',
    example: 'video-123',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  contentId: string;

  @ApiProperty({
    description: 'Type de contenu',
    enum: SocialVideoContentType,
    example: SocialVideoContentType.VIDEO,
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(SocialVideoContentType)
  contentType: SocialVideoContentType;

  @ApiProperty({
    description: 'Type d\'interaction',
    enum: SocialVideoInteractionType,
    example: SocialVideoInteractionType.VIEW,
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(SocialVideoInteractionType)
  interactionType: SocialVideoInteractionType;

  @ApiProperty({
    description: 'Métadonnées de l\'interaction',
    example: { watchDuration: 120, watchPercentage: 0.75 },
    required: false,
    type: Object,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
