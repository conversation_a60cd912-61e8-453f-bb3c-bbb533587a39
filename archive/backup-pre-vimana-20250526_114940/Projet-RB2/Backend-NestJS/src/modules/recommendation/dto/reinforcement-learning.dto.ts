import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsNumber,
  IsObject,
  IsArray,
  IsBoolean,
  IsOptional,
  IsDateString,
  Min,
  Max,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  RLAgentType,
  RLAgentState,
  ExplorationStrategy,
} from '../interfaces/reinforcement-learning.interface';

/**
 * DTO pour la configuration d'un agent d'apprentissage par renforcement
 */
export class RLAgentConfigDto {
  @ApiProperty({
    description: 'Taux d\'apprentissage (alpha)',
    minimum: 0,
    maximum: 1,
    example: 0.1,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  learningRate: number;

  @ApiProperty({
    description: 'Facteur d\'actualisation (gamma)',
    minimum: 0,
    maximum: 1,
    example: 0.9,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  discountFactor: number;

  @ApiProperty({
    description: 'Taux d\'exploration (epsilon)',
    minimum: 0,
    maximum: 1,
    example: 0.1,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  explorationRate: number;

  @ApiProperty({
    description: 'Stratégie d\'exploration',
    enum: ExplorationStrategy,
    example: ExplorationStrategy.EPSILON_GREEDY,
  })
  @IsEnum(ExplorationStrategy)
  explorationStrategy: ExplorationStrategy;

  @ApiProperty({
    description: 'Taille de la mémoire d\'expérience',
    minimum: 1,
    example: 10000,
  })
  @IsNumber()
  @Min(1)
  experienceMemorySize: number;

  @ApiProperty({
    description: 'Taille du lot d\'apprentissage',
    minimum: 1,
    example: 32,
  })
  @IsNumber()
  @Min(1)
  batchSize: number;

  @ApiPropertyOptional({
    description: 'Fréquence de mise à jour du réseau cible (pour DQN)',
    minimum: 1,
    example: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  targetNetworkUpdateFrequency?: number;

  @ApiPropertyOptional({
    description: 'Paramètres spécifiques à l\'agent',
    example: { hiddenLayerSize: 64 },
  })
  @IsOptional()
  @IsObject()
  agentSpecificParams?: Record<string, any>;
}

/**
 * DTO pour la création d'un agent d'apprentissage par renforcement
 */
export class CreateRLAgentDto {
  @ApiProperty({
    description: 'Nom de l\'agent',
    example: 'ExplanationOptimizer',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Description de l\'agent',
    example: 'Agent d\'optimisation des explications par apprentissage par renforcement',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Type d\'agent',
    enum: RLAgentType,
    example: RLAgentType.DQN,
  })
  @IsEnum(RLAgentType)
  agentType: RLAgentType;

  @ApiProperty({
    description: 'Configuration de l\'agent',
    type: RLAgentConfigDto,
  })
  @ValidateNested()
  @Type(() => RLAgentConfigDto)
  config: RLAgentConfigDto;
}

/**
 * DTO pour la mise à jour d'un agent d'apprentissage par renforcement
 */
export class UpdateRLAgentDto {
  @ApiPropertyOptional({
    description: 'Nom de l\'agent',
    example: 'ExplanationOptimizer V2',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Description de l\'agent',
    example: 'Version améliorée de l\'agent d\'optimisation des explications',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'État de l\'agent',
    enum: RLAgentState,
    example: RLAgentState.LEARNING,
  })
  @IsOptional()
  @IsEnum(RLAgentState)
  state?: RLAgentState;

  @ApiPropertyOptional({
    description: 'Configuration de l\'agent',
    type: RLAgentConfigDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => RLAgentConfigDto)
  config?: RLAgentConfigDto;
}

/**
 * DTO pour une composante de récompense
 */
export class RewardComponentDto {
  @ApiProperty({
    description: 'Nom de la composante',
    example: 'clickThrough',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Valeur de la composante',
    example: 0.8,
  })
  @IsNumber()
  value: number;

  @ApiProperty({
    description: 'Poids de la composante',
    example: 0.5,
  })
  @IsNumber()
  weight: number;
}

/**
 * DTO pour une récompense
 */
export class RLRewardDto {
  @ApiProperty({
    description: 'Valeur de la récompense',
    example: 0.75,
  })
  @IsNumber()
  value: number;

  @ApiProperty({
    description: 'Composantes de la récompense',
    type: [RewardComponentDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RewardComponentDto)
  components: RewardComponentDto[];

  @ApiPropertyOptional({
    description: 'Métadonnées de la récompense',
    example: { source: 'userFeedback' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO pour l'enregistrement d'une interaction avec une explication
 */
export class RecordExplanationInteractionDto {
  @ApiProperty({
    description: 'ID de l\'explication',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  explanationId: string;

  @ApiProperty({
    description: 'Type d\'interaction',
    example: 'CLICK',
  })
  @IsString()
  interactionType: string;

  @ApiProperty({
    description: 'Données de l\'interaction',
    example: { factorClicked: 'similarity' },
  })
  @IsObject()
  data: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Durée de l\'interaction (en secondes)',
    example: 5.2,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  duration?: number;
}

/**
 * DTO pour la réponse d'un agent d'apprentissage par renforcement
 */
export class RLAgentResponseDto {
  @ApiProperty({
    description: 'ID de l\'agent',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Nom de l\'agent',
    example: 'ExplanationOptimizer',
  })
  name: string;

  @ApiProperty({
    description: 'Description de l\'agent',
    example: 'Agent d\'optimisation des explications par apprentissage par renforcement',
  })
  description: string;

  @ApiProperty({
    description: 'Type d\'agent',
    enum: RLAgentType,
    example: RLAgentType.DQN,
  })
  agentType: RLAgentType;

  @ApiProperty({
    description: 'État de l\'agent',
    enum: RLAgentState,
    example: RLAgentState.LEARNING,
  })
  state: RLAgentState;

  @ApiProperty({
    description: 'Configuration de l\'agent',
    type: RLAgentConfigDto,
  })
  config: RLAgentConfigDto;

  @ApiProperty({
    description: 'Statistiques de performance',
    example: {
      totalEpisodes: 1000,
      totalSteps: 50000,
      cumulativeReward: 7500,
      averageRewardPerEpisode: 7.5,
      convergenceRate: 0.95,
      currentExplorationRate: 0.05,
      rewardHistory: [5.2, 6.1, 7.3, 7.8, 8.0],
      errorHistory: [0.5, 0.4, 0.3, 0.2, 0.1],
    },
  })
  stats: {
    totalEpisodes: number;
    totalSteps: number;
    cumulativeReward: number;
    averageRewardPerEpisode: number;
    convergenceRate: number;
    currentExplorationRate: number;
    rewardHistory: number[];
    errorHistory: number[];
    agentSpecificMetrics?: Record<string, any>;
  };

  @ApiProperty({
    description: 'Date de création',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Date de dernière mise à jour',
    example: '2023-01-02T00:00:00.000Z',
  })
  updatedAt: string;
}

/**
 * DTO pour la configuration de la fonction de récompense
 */
export class RewardFunctionConfigDto {
  @ApiProperty({
    description: 'Poids des composantes de récompense',
    example: {
      clickThrough: 0.5,
      timeSpent: 0.3,
      explicitFeedback: 0.8,
      conversion: 1.0,
    },
  })
  @IsObject()
  componentWeights: Record<string, number>;

  @ApiPropertyOptional({
    description: 'Paramètres supplémentaires',
    example: {
      minTimeThreshold: 5,
      maxRewardValue: 10,
    },
  })
  @IsOptional()
  @IsObject()
  additionalParams?: Record<string, any>;
}

/**
 * DTO pour les statistiques d'apprentissage par renforcement
 */
export class RLStatsDto {
  @ApiProperty({
    description: 'Nombre total d\'agents',
    example: 5,
  })
  totalAgents: number;

  @ApiProperty({
    description: 'Nombre total d\'épisodes',
    example: 10000,
  })
  totalEpisodes: number;

  @ApiProperty({
    description: 'Nombre total d\'interactions',
    example: 50000,
  })
  totalInteractions: number;

  @ApiProperty({
    description: 'Récompense moyenne globale',
    example: 0.75,
  })
  globalAverageReward: number;

  @ApiProperty({
    description: 'Taux de convergence moyen',
    example: 0.92,
  })
  averageConvergenceRate: number;

  @ApiProperty({
    description: 'Statistiques par type d\'agent',
    example: {
      Q_LEARNING: { count: 2, averageReward: 0.7 },
      DQN: { count: 3, averageReward: 0.8 },
    },
  })
  statsByAgentType: Record<string, { count: number; averageReward: number }>;

  @ApiProperty({
    description: 'Statistiques par type d\'interaction',
    example: {
      CLICK: { count: 30000, averageReward: 0.6 },
      FEEDBACK: { count: 20000, averageReward: 0.9 },
    },
  })
  statsByInteractionType: Record<string, { count: number; averageReward: number }>;
}
