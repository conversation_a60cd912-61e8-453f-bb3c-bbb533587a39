import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { RecommendationMonitoringService } from '../../monitoring/services/recommendation-monitoring.service';
import { RecommendationType } from '../enums/recommendation-type.enum';

/**
 * Intercepteur pour mesurer les performances des requêtes de recommandation
 */
@Injectable()
export class RecommendationMonitoringInterceptor implements NestInterceptor {
  private readonly logger = new Logger(RecommendationMonitoringInterceptor.name);

  constructor(
    private readonly monitoringService: RecommendationMonitoringService,
  ) {}

  /**
   * Intercepte les requêtes de recommandation
   * @param context Contexte d'exécution
   * @param next Gestionnaire d'appel
   * @returns Observable
   */
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const req = context.switchToHttp().getRequest();
    const res = context.switchToHttp().getResponse();
    
    // Récupérer l'URL de la requête
    const url = req.originalUrl;
    
    // Vérifier si c'est une requête de recommandation
    if (!url.includes('/api/v1/recommendations')) {
      return next.handle();
    }
    
    // Récupérer le type d'élément
    const type = (req.query.type as string || 'RETREAT') as RecommendationType;
    
    // Récupérer la stratégie
    const strategy = req.query.strategy as string || 'HYBRID';
    
    // Déterminer l'endpoint
    let endpoint = 'recommendations';
    
    if (url.includes('/trending')) {
      endpoint = 'trending';
    } else if (url.includes('/similar')) {
      endpoint = 'similar';
    } else if (url.includes('/interactions')) {
      endpoint = 'interactions';
    }
    
    // Mesurer le temps de réponse
    const startTime = Date.now();
    
    return next.handle().pipe(
      tap(() => {
        // Calculer le temps de réponse
        const responseTime = Date.now() - startTime;
        
        // Enregistrer la requête
        this.monitoringService.recordRecommendationRequest(
          type,
          strategy,
          endpoint,
          responseTime,
        );
        
        // Enregistrer le hit du cache
        const cacheHit = req.query.cache !== 'false' && res.getHeader('X-Cache') === 'HIT';
        this.monitoringService.recordCacheHit(type, endpoint, cacheHit);
        
        // Si c'est une interaction, l'enregistrer
        if (endpoint === 'interactions' && req.body && req.body.interactionType) {
          this.monitoringService.recordInteraction(
            req.body.type || type,
            req.body.interactionType,
          );
        }
      }),
      catchError(error => {
        // Calculer le temps de réponse
        const responseTime = Date.now() - startTime;
        
        // Enregistrer l'erreur
        this.monitoringService.recordRecommendationRequest(
          type,
          strategy,
          endpoint,
          responseTime,
          {
            code: error.status || '500',
            message: error.message || 'Internal Server Error',
          },
        );
        
        throw error;
      }),
    );
  }
}
