import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ContextDetectionService, SeasonData } from './context-detection.service';
import { RecommendationService } from './recommendation.service';
import { ExternalDataService } from './external-data.service';

/**
 * Interface pour les options de recommandation saisonnière
 */
export interface SeasonalRecommendationOptions {
  /** Facteur d'influence de la saison (0-1) */
  seasonFactor: number;
  
  /** Nombre maximum de recommandations */
  maxRecommendations: number;
  
  /** Inclure les recommandations pour la saison suivante */
  includeNextSeason: boolean;
  
  /** Facteur d'influence de la saison suivante (0-1) */
  nextSeasonFactor: number;
  
  /** Filtres supplémentaires */
  filters?: Record<string, any>;
}

/**
 * Interface pour les règles saisonnières
 */
export interface SeasonalRule {
  /** Saison */
  season: 'spring' | 'summer' | 'autumn' | 'winter';
  
  /** Ajustement de score */
  scoreAdjustment: number;
  
  /** Filtres à appliquer */
  filters?: Record<string, any>;
  
  /** Catégories recommandées */
  recommendedCategories?: string[];
  
  /** Activités recommandées */
  recommendedActivities?: string[];
  
  /** Thèmes recommandés */
  recommendedThemes?: string[];
}

/**
 * Service pour les recommandations saisonnières
 */
@Injectable()
export class SeasonalRecommendationService {
  private readonly logger = new Logger(SeasonalRecommendationService.name);
  private readonly seasonalRules: Record<string, SeasonalRule>;
  
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly contextDetectionService: ContextDetectionService,
    private readonly recommendationService: RecommendationService,
    private readonly externalDataService: ExternalDataService,
  ) {
    // Initialiser les règles saisonnières
    this.seasonalRules = this.initializeSeasonalRules();
    this.logger.log(`Initialized seasonal rules for ${Object.keys(this.seasonalRules).length} seasons`);
  }
  
  /**
   * Récupère des recommandations saisonnières pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param options Options de recommandation
   * @returns Recommandations saisonnières
   */
  async getSeasonalRecommendations(
    userId: string,
    options?: Partial<SeasonalRecommendationOptions>,
  ): Promise<any[]> {
    try {
      // Fusionner les options avec les valeurs par défaut
      const fullOptions: SeasonalRecommendationOptions = {
        seasonFactor: options?.seasonFactor !== undefined ? options.seasonFactor : 0.6,
        maxRecommendations: options?.maxRecommendations || 10,
        includeNextSeason: options?.includeNextSeason !== undefined ? options.includeNextSeason : true,
        nextSeasonFactor: options?.nextSeasonFactor !== undefined ? options.nextSeasonFactor : 0.3,
        filters: options?.filters || {},
      };
      
      // Détecter le contexte de l'utilisateur pour obtenir les données de saison
      const userContext = await this.contextDetectionService.detectUserContext(userId);
      const seasonData = userContext.seasonData;
      
      if (!seasonData) {
        this.logger.warn(`No season data available for user ${userId}`);
        // Retourner les recommandations standard
        const baseRecommendations = await this.recommendationService.getRecommendationsForUser(userId);
        return baseRecommendations.slice(0, fullOptions.maxRecommendations);
      }
      
      // Récupérer les recommandations de base
      const baseRecommendations = await this.recommendationService.getRecommendationsForUser(userId);
      
      // Appliquer les règles saisonnières aux recommandations
      const seasonalRecommendations = this.applySeasonalRules(
        baseRecommendations,
        seasonData,
        fullOptions,
      );
      
      // Enrichir les recommandations avec des données externes
      const enrichedRecommendations = await this.externalDataService.enrichRecommendations(
        userId,
        seasonalRecommendations,
      );
      
      // Émettre un événement de recommandation saisonnière
      this.eventEmitter.emit('recommendation.seasonal', {
        userId,
        currentSeason: seasonData.currentSeason,
        nextSeason: seasonData.nextSeason,
        recommendationCount: enrichedRecommendations.length,
      });
      
      return enrichedRecommendations.slice(0, fullOptions.maxRecommendations);
    } catch (error) {
      this.logger.error(`Error getting seasonal recommendations: ${error.message}`);
      
      // En cas d'erreur, retourner les recommandations de base
      const baseRecommendations = await this.recommendationService.getRecommendationsForUser(userId);
      return baseRecommendations.slice(0, options?.maxRecommendations || 10);
    }
  }
  
  /**
   * Applique les règles saisonnières aux recommandations
   * @param recommendations Recommandations de base
   * @param seasonData Données de saison
   * @param options Options de recommandation
   * @returns Recommandations saisonnières
   */
  private applySeasonalRules(
    recommendations: any[],
    seasonData: SeasonData,
    options: SeasonalRecommendationOptions,
  ): any[] {
    // Récupérer les règles pour la saison actuelle et la saison suivante
    const currentSeasonRule = this.seasonalRules[seasonData.currentSeason];
    const nextSeasonRule = options.includeNextSeason ? this.seasonalRules[seasonData.nextSeason] : null;
    
    if (!currentSeasonRule) {
      this.logger.warn(`No seasonal rule found for season ${seasonData.currentSeason}`);
      return recommendations;
    }
    
    // Appliquer les règles saisonnières à chaque recommandation
    const scoredRecommendations = recommendations.map(recommendation => {
      let seasonalScore = 0;
      let seasonalFilters: Record<string, any> = {};
      let seasonalCategories: string[] = [];
      let seasonalActivities: string[] = [];
      let seasonalThemes: string[] = [];
      
      // Appliquer la règle de la saison actuelle
      seasonalScore += this.calculateSeasonalScore(recommendation, currentSeasonRule) * options.seasonFactor;
      
      if (currentSeasonRule.filters) {
        seasonalFilters = { ...seasonalFilters, ...currentSeasonRule.filters };
      }
      
      if (currentSeasonRule.recommendedCategories) {
        seasonalCategories = [...seasonalCategories, ...currentSeasonRule.recommendedCategories];
      }
      
      if (currentSeasonRule.recommendedActivities) {
        seasonalActivities = [...seasonalActivities, ...currentSeasonRule.recommendedActivities];
      }
      
      if (currentSeasonRule.recommendedThemes) {
        seasonalThemes = [...seasonalThemes, ...currentSeasonRule.recommendedThemes];
      }
      
      // Appliquer la règle de la saison suivante si nécessaire
      if (nextSeasonRule && options.includeNextSeason) {
        // Ajuster le score en fonction de la proximité de la saison suivante
        const daysRemainingFactor = Math.max(0, Math.min(1, 1 - (seasonData.daysRemainingInCurrentSeason / 90)));
        const nextSeasonAdjustment = this.calculateSeasonalScore(recommendation, nextSeasonRule) * options.nextSeasonFactor * daysRemainingFactor;
        
        seasonalScore += nextSeasonAdjustment;
        
        if (nextSeasonRule.filters) {
          seasonalFilters = { ...seasonalFilters, ...nextSeasonRule.filters };
        }
        
        if (nextSeasonRule.recommendedCategories) {
          seasonalCategories = [...seasonalCategories, ...nextSeasonRule.recommendedCategories];
        }
        
        if (nextSeasonRule.recommendedActivities) {
          seasonalActivities = [...seasonalActivities, ...nextSeasonRule.recommendedActivities];
        }
        
        if (nextSeasonRule.recommendedThemes) {
          seasonalThemes = [...seasonalThemes, ...nextSeasonRule.recommendedThemes];
        }
      }
      
      // Calculer le score final
      const baseScore = recommendation.score || 0.5;
      const finalScore = baseScore * (1 + seasonalScore);
      
      // Ajouter les informations saisonnières à la recommandation
      return {
        ...recommendation,
        score: finalScore,
        seasonalScore,
        seasonalFilters,
        seasonalCategories,
        seasonalActivities,
        seasonalThemes,
        seasonApplied: seasonalScore !== 0,
        currentSeason: seasonData.currentSeason,
        nextSeason: seasonData.nextSeason,
        daysRemainingInCurrentSeason: seasonData.daysRemainingInCurrentSeason,
      };
    });
    
    // Filtrer les recommandations selon les filtres saisonniers
    const filteredRecommendations = this.applySeasonalFilters(
      scoredRecommendations,
      options.filters,
    );
    
    // Trier les recommandations par score
    return filteredRecommendations.sort((a, b) => b.score - a.score);
  }
  
  /**
   * Calcule le score saisonnier d'une recommandation
   * @param recommendation Recommandation
   * @param rule Règle saisonnière
   * @returns Score saisonnier
   */
  private calculateSeasonalScore(recommendation: any, rule: SeasonalRule): number {
    let score = 0;
    
    // Vérifier si la recommandation correspond à la saison
    if (recommendation.metadata?.season === rule.season) {
      score += rule.scoreAdjustment;
    }
    
    // Vérifier si la catégorie est recommandée pour la saison
    if (rule.recommendedCategories && recommendation.category) {
      if (rule.recommendedCategories.includes(recommendation.category)) {
        score += 0.2;
      }
    }
    
    // Vérifier si les activités sont recommandées pour la saison
    if (rule.recommendedActivities && recommendation.metadata?.activities) {
      const activities = Array.isArray(recommendation.metadata.activities)
        ? recommendation.metadata.activities
        : [recommendation.metadata.activities];
      
      for (const activity of activities) {
        if (rule.recommendedActivities.includes(activity)) {
          score += 0.1;
        }
      }
    }
    
    // Vérifier si les thèmes sont recommandés pour la saison
    if (rule.recommendedThemes && recommendation.metadata?.themes) {
      const themes = Array.isArray(recommendation.metadata.themes)
        ? recommendation.metadata.themes
        : [recommendation.metadata.themes];
      
      for (const theme of themes) {
        if (rule.recommendedThemes.includes(theme)) {
          score += 0.1;
        }
      }
    }
    
    return score;
  }
  
  /**
   * Applique les filtres saisonniers aux recommandations
   * @param recommendations Recommandations
   * @param filters Filtres
   * @returns Recommandations filtrées
   */
  private applySeasonalFilters(recommendations: any[], filters: Record<string, any>): any[] {
    if (!filters || Object.keys(filters).length === 0) {
      return recommendations;
    }
    
    return recommendations.filter(recommendation => {
      // Vérifier chaque filtre
      for (const [key, value] of Object.entries(filters)) {
        // Vérifier si la recommandation a la propriété
        if (!(key in recommendation)) {
          continue;
        }
        
        // Vérifier si la valeur correspond
        if (Array.isArray(value)) {
          // Si le filtre est un tableau, vérifier si la valeur est dans le tableau
          if (!value.includes(recommendation[key])) {
            return false;
          }
        } else if (typeof value === 'object' && value !== null) {
          // Si le filtre est un objet, vérifier les opérateurs
          for (const [op, opValue] of Object.entries(value)) {
            switch (op) {
              case 'eq':
                if (recommendation[key] !== opValue) return false;
                break;
              case 'neq':
                if (recommendation[key] === opValue) return false;
                break;
              case 'gt':
                if (recommendation[key] <= opValue) return false;
                break;
              case 'gte':
                if (recommendation[key] < opValue) return false;
                break;
              case 'lt':
                if (recommendation[key] >= opValue) return false;
                break;
              case 'lte':
                if (recommendation[key] > opValue) return false;
                break;
              case 'in':
                if (!Array.isArray(opValue) || !opValue.includes(recommendation[key])) return false;
                break;
              case 'nin':
                if (!Array.isArray(opValue) || opValue.includes(recommendation[key])) return false;
                break;
              case 'contains':
                if (typeof recommendation[key] !== 'string' || !recommendation[key].includes(opValue)) return false;
                break;
            }
          }
        } else {
          // Sinon, vérifier l'égalité
          if (recommendation[key] !== value) {
            return false;
          }
        }
      }
      
      return true;
    });
  }
  
  /**
   * Initialise les règles saisonnières
   * @returns Règles saisonnières
   */
  private initializeSeasonalRules(): Record<string, SeasonalRule> {
    return {
      spring: {
        season: 'spring',
        scoreAdjustment: 0.3,
        filters: {
          'metadata.season': 'spring',
        },
        recommendedCategories: ['yoga', 'meditation', 'detox', 'wellness'],
        recommendedActivities: ['hiking', 'gardening', 'outdoor', 'nature'],
        recommendedThemes: ['renewal', 'growth', 'cleansing', 'balance'],
      },
      summer: {
        season: 'summer',
        scoreAdjustment: 0.3,
        filters: {
          'metadata.season': 'summer',
        },
        recommendedCategories: ['fitness', 'adventure', 'water', 'outdoor'],
        recommendedActivities: ['swimming', 'hiking', 'beach', 'water sports'],
        recommendedThemes: ['energy', 'joy', 'adventure', 'vitality'],
      },
      autumn: {
        season: 'autumn',
        scoreAdjustment: 0.3,
        filters: {
          'metadata.season': 'autumn',
        },
        recommendedCategories: ['mindfulness', 'yoga', 'wellness', 'nutrition'],
        recommendedActivities: ['hiking', 'cooking', 'meditation', 'reading'],
        recommendedThemes: ['reflection', 'transformation', 'harvest', 'gratitude'],
      },
      winter: {
        season: 'winter',
        scoreAdjustment: 0.3,
        filters: {
          'metadata.season': 'winter',
        },
        recommendedCategories: ['wellness', 'spa', 'meditation', 'yoga'],
        recommendedActivities: ['indoor', 'hot springs', 'sauna', 'cooking'],
        recommendedThemes: ['rest', 'reflection', 'renewal', 'warmth'],
      },
    };
  }
}
