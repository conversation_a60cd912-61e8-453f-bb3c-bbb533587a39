import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationOptions } from '../interfaces/recommendation-options.interface';
import { ContentBasedService } from './content-based.service';
import { CollaborativeFilteringService } from './collaborative-filtering.service';

/**
 * Service de recommandations en temps réel
 * Génère des recommandations basées sur le comportement immédiat de l'utilisateur
 */
@Injectable()
export class RealtimeRecommendationService {
  private readonly logger = new Logger(RealtimeRecommendationService.name);
  
  // Stockage en mémoire des sessions utilisateur
  private readonly userSessions = new Map<string, UserSession>();
  
  // Durée de vie d'une session (en millisecondes)
  private readonly sessionTTL = 30 * 60 * 1000; // 30 minutes
  
  // Intervalle de nettoyage des sessions expirées (en millisecondes)
  private readonly cleanupInterval = 5 * 60 * 1000; // 5 minutes
  
  constructor(
    private readonly prisma: PrismaService,
    private readonly contentBasedService: ContentBasedService,
    private readonly collaborativeFilteringService: CollaborativeFilteringService,
  ) {
    // Nettoyer périodiquement les sessions expirées
    setInterval(() => this.cleanupExpiredSessions(), this.cleanupInterval);
  }
  
  /**
   * Génère des recommandations en temps réel pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Liste des recommandations
   */
  async getRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ) {
    try {
      this.logger.log(`Génération de recommandations en temps réel de type ${type} pour l'utilisateur ${userId}`);
      
      // Récupérer ou créer la session utilisateur
      const session = this.getOrCreateSession(userId);
      
      // Récupérer les interactions récentes de l'utilisateur
      const recentInteractions = session.interactions;
      
      if (recentInteractions.length === 0) {
        this.logger.log(`Aucune interaction récente pour l'utilisateur ${userId}, utilisation des recommandations standard`);
        
        // Utiliser le service de filtrage collaboratif comme fallback
        return this.collaborativeFilteringService.getRecommendations(userId, type, options);
      }
      
      // Analyser les interactions récentes pour déterminer les intérêts actuels
      const currentInterests = this.analyzeRecentInteractions(recentInteractions);
      
      // Générer des recommandations basées sur les intérêts actuels
      const recommendations = await this.generateRecommendationsFromInterests(
        userId,
        type,
        currentInterests,
        options,
      );
      
      this.logger.log(`${recommendations.length} recommandations en temps réel générées pour l'utilisateur ${userId}`);
      
      // Ajouter la source aux recommandations
      return recommendations.map(recommendation => ({
        ...recommendation,
        sources: [...(recommendation.sources || []), 'realtime'],
      }));
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des recommandations en temps réel: ${error.message}`);
      
      // Utiliser le service de filtrage collaboratif comme fallback
      return this.collaborativeFilteringService.getRecommendations(userId, type, options);
    }
  }
  
  /**
   * Compte le nombre de recommandations disponibles
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Nombre de recommandations
   */
  async countRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ): Promise<number> {
    // Utiliser le service de filtrage collaboratif comme fallback
    return this.collaborativeFilteringService.countRecommendations(userId, type, options);
  }
  
  /**
   * Enregistre une interaction utilisateur en temps réel
   * @param userId ID de l'utilisateur
   * @param itemId ID de l'item
   * @param itemType Type de l'item
   * @param interactionType Type d'interaction
   * @param metadata Métadonnées de l'interaction
   */
  async trackInteraction(
    userId: string,
    itemId: string,
    itemType: RecommendationType,
    interactionType: string,
    metadata: Record<string, any> = {},
  ): Promise<void> {
    try {
      this.logger.debug(`Enregistrement d'une interaction ${interactionType} pour l'utilisateur ${userId} sur l'item ${itemId}`);
      
      // Récupérer ou créer la session utilisateur
      const session = this.getOrCreateSession(userId);
      
      // Récupérer les détails de l'item
      const itemDetails = await this.getItemDetails(itemId, itemType);
      
      // Ajouter l'interaction à la session
      session.interactions.push({
        userId,
        itemId,
        itemType,
        interactionType,
        timestamp: Date.now(),
        metadata,
        itemDetails,
      });
      
      // Mettre à jour le timestamp de la session
      session.lastActivity = Date.now();
      
      // Limiter le nombre d'interactions stockées (garder les 20 plus récentes)
      if (session.interactions.length > 20) {
        session.interactions = session.interactions.slice(-20);
      }
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de l'interaction: ${error.message}`);
    }
  }
  
  /**
   * Récupère ou crée une session utilisateur
   * @param userId ID de l'utilisateur
   * @returns Session utilisateur
   */
  private getOrCreateSession(userId: string): UserSession {
    // Récupérer la session existante
    let session = this.userSessions.get(userId);
    
    // Créer une nouvelle session si nécessaire
    if (!session) {
      session = {
        userId,
        interactions: [],
        lastActivity: Date.now(),
      };
      
      this.userSessions.set(userId, session);
      this.logger.debug(`Nouvelle session créée pour l'utilisateur ${userId}`);
    }
    
    return session;
  }
  
  /**
   * Nettoie les sessions expirées
   */
  private cleanupExpiredSessions(): void {
    const now = Date.now();
    let count = 0;
    
    for (const [userId, session] of this.userSessions.entries()) {
      if (now - session.lastActivity > this.sessionTTL) {
        this.userSessions.delete(userId);
        count++;
      }
    }
    
    if (count > 0) {
      this.logger.debug(`${count} sessions expirées nettoyées`);
    }
  }
  
  /**
   * Récupère les détails d'un item
   * @param itemId ID de l'item
   * @param itemType Type de l'item
   * @returns Détails de l'item
   */
  private async getItemDetails(itemId: string, itemType: RecommendationType): Promise<ItemDetails | null> {
    try {
      // Récupérer les détails en fonction du type d'item
      switch (itemType) {
        case RecommendationType.COURSE:
          const course = await this.prisma.course.findUnique({
            where: { id: itemId },
            select: {
              id: true,
              title: true,
              description: true,
              category: true,
              level: true,
              metadata: true,
            },
          });
          
          if (course) {
            return {
              id: course.id,
              title: course.title,
              category: course.category,
              tags: course.metadata?.tags || [],
              attributes: {
                level: course.level,
                ...course.metadata,
              },
            };
          }
          break;
          
        case RecommendationType.RETREAT:
          const retreat = await this.prisma.retreat.findUnique({
            where: { id: itemId },
            select: {
              id: true,
              title: true,
              description: true,
              location: true,
              startDate: true,
              endDate: true,
              metadata: true,
            },
          });
          
          if (retreat) {
            return {
              id: retreat.id,
              title: retreat.title,
              category: retreat.metadata?.category || 'retreat',
              tags: retreat.metadata?.tags || [],
              attributes: {
                location: retreat.location,
                startDate: retreat.startDate,
                endDate: retreat.endDate,
                ...retreat.metadata,
              },
            };
          }
          break;
      }
      
      return null;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des détails de l'item: ${error.message}`);
      return null;
    }
  }
  
  /**
   * Analyse les interactions récentes pour déterminer les intérêts actuels
   * @param interactions Liste des interactions récentes
   * @returns Intérêts actuels
   */
  private analyzeRecentInteractions(interactions: RealtimeInteraction[]): CurrentInterests {
    // Initialiser les intérêts
    const interests: CurrentInterests = {
      categories: new Map(),
      tags: new Map(),
      attributes: new Map(),
      items: new Map(),
    };
    
    // Poids des différents types d'interaction
    const interactionWeights = {
      VIEW: 1,
      LIKE: 3,
      BOOKMARK: 2,
      SHARE: 2,
      ENROLL: 4,
      PURCHASE: 5,
    };
    
    // Analyser chaque interaction
    for (const interaction of interactions) {
      // Récupérer le poids de l'interaction
      const weight = interactionWeights[interaction.interactionType as keyof typeof interactionWeights] || 1;
      
      // Récupérer les détails de l'item
      const itemDetails = interaction.itemDetails;
      if (!itemDetails) continue;
      
      // Mettre à jour les intérêts par catégorie
      if (itemDetails.category) {
        const currentWeight = interests.categories.get(itemDetails.category) || 0;
        interests.categories.set(itemDetails.category, currentWeight + weight);
      }
      
      // Mettre à jour les intérêts par tag
      if (itemDetails.tags && Array.isArray(itemDetails.tags)) {
        for (const tag of itemDetails.tags) {
          const currentWeight = interests.tags.get(tag) || 0;
          interests.tags.set(tag, currentWeight + weight);
        }
      }
      
      // Mettre à jour les intérêts par attribut
      if (itemDetails.attributes) {
        for (const [key, value] of Object.entries(itemDetails.attributes)) {
          if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
            const attributeKey = `${key}:${value}`;
            const currentWeight = interests.attributes.get(attributeKey) || 0;
            interests.attributes.set(attributeKey, currentWeight + weight);
          }
        }
      }
      
      // Mettre à jour les intérêts par item
      const currentItemWeight = interests.items.get(itemDetails.id) || 0;
      interests.items.set(itemDetails.id, currentItemWeight + weight);
    }
    
    return interests;
  }
  
  /**
   * Génère des recommandations basées sur les intérêts actuels
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param interests Intérêts actuels
   * @param options Options de recommandation
   * @returns Liste des recommandations
   */
  private async generateRecommendationsFromInterests(
    userId: string,
    type: RecommendationType,
    interests: CurrentInterests,
    options: RecommendationOptions = {},
  ): Promise<any[]> {
    // Construire les filtres basés sur les intérêts
    const filters: Record<string, any> = {
      ...options.filters,
    };
    
    // Récupérer les catégories les plus intéressantes
    const topCategories = this.getTopItems(interests.categories, 3);
    if (topCategories.length > 0) {
      filters.category = {
        in: topCategories,
      };
    }
    
    // Récupérer les tags les plus intéressants
    const topTags = this.getTopItems(interests.tags, 5);
    if (topTags.length > 0) {
      filters.metadata = {
        path: ['tags'],
        array_contains: topTags,
      };
    }
    
    // Exclure les items déjà vus
    const viewedItems = Array.from(interests.items.keys());
    if (viewedItems.length > 0) {
      filters.id = {
        notIn: viewedItems,
      };
    }
    
    // Utiliser le service basé sur le contenu avec les filtres personnalisés
    const recommendations = await this.contentBasedService.getRecommendations(
      userId,
      type,
      {
        ...options,
        filters,
        limit: (options.limit || 10) * 2, // Récupérer plus de recommandations pour avoir plus de choix
      },
    );
    
    // Trier les recommandations en fonction des intérêts actuels
    const scoredRecommendations = recommendations.map(recommendation => {
      let interestScore = 0;
      
      // Score basé sur la catégorie
      if (recommendation.metadata?.category) {
        const categoryWeight = interests.categories.get(recommendation.metadata.category) || 0;
        interestScore += categoryWeight * 0.5;
      }
      
      // Score basé sur les tags
      if (recommendation.metadata?.tags && Array.isArray(recommendation.metadata.tags)) {
        for (const tag of recommendation.metadata.tags) {
          const tagWeight = interests.tags.get(tag) || 0;
          interestScore += tagWeight * 0.3;
        }
      }
      
      // Score basé sur les attributs
      if (recommendation.metadata) {
        for (const [key, value] of Object.entries(recommendation.metadata)) {
          if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
            const attributeKey = `${key}:${value}`;
            const attributeWeight = interests.attributes.get(attributeKey) || 0;
            interestScore += attributeWeight * 0.2;
          }
        }
      }
      
      // Combiner le score d'intérêt avec le score de recommandation
      const finalScore = recommendation.score * 0.7 + (interestScore / 10) * 0.3;
      
      return {
        ...recommendation,
        score: finalScore,
        interestScore,
      };
    });
    
    // Trier les recommandations par score final et limiter le nombre de résultats
    const limit = options.limit || 10;
    return scoredRecommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }
  
  /**
   * Récupère les éléments les plus importants d'une map
   * @param map Map d'éléments avec leur poids
   * @param count Nombre d'éléments à récupérer
   * @returns Liste des éléments les plus importants
   */
  private getTopItems(map: Map<string, number>, count: number): string[] {
    return Array.from(map.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, count)
      .map(([key]) => key);
  }
}

/**
 * Interface pour une session utilisateur
 */
interface UserSession {
  userId: string;
  interactions: RealtimeInteraction[];
  lastActivity: number;
}

/**
 * Interface pour une interaction en temps réel
 */
interface RealtimeInteraction {
  userId: string;
  itemId: string;
  itemType: RecommendationType;
  interactionType: string;
  timestamp: number;
  metadata: Record<string, any>;
  itemDetails: ItemDetails | null;
}

/**
 * Interface pour les détails d'un item
 */
interface ItemDetails {
  id: string;
  title: string;
  category: string;
  tags: string[];
  attributes: Record<string, any>;
}

/**
 * Interface pour les intérêts actuels
 */
interface CurrentInterests {
  categories: Map<string, number>;
  tags: Map<string, number>;
  attributes: Map<string, number>;
  items: Map<string, number>;
}
