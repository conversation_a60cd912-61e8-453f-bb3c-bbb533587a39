import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ExplanationService } from './explanation.service';
import { ExplanationPreferencesService } from './explanation-preferences.service';

/**
 * Interface pour une traduction
 */
export interface Translation {
  /** Texte source */
  sourceText: string;
  
  /** Langue source */
  sourceLanguage: string;
  
  /** Texte traduit */
  translatedText: string;
  
  /** Langue cible */
  targetLanguage: string;
  
  /** Qualité de la traduction (0-1) */
  quality?: number;
  
  /** Métadonnées supplémentaires */
  metadata?: Record<string, any>;
}

/**
 * Interface pour un template d'explication traduit
 */
export interface TranslatedTemplate {
  /** ID du template original */
  originalId: string;
  
  /** Nom du template */
  name: string;
  
  /** Description du template */
  description: string;
  
  /** Type de facteur */
  factorType: string;
  
  /** Template traduit */
  template: string;
  
  /** Variables du template */
  variables: string[];
  
  /** Langue du template */
  language: string;
}

/**
 * Service pour la traduction des explications
 */
@Injectable()
export class ExplanationTranslationService {
  private readonly logger = new Logger(ExplanationTranslationService.name);
  private readonly supportedLanguages: string[];
  private readonly defaultLanguage: string;
  private readonly translationApiKey: string;
  private readonly translationApiUrl: string;
  private readonly translationCache: Map<string, Translation> = new Map();
  
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly eventEmitter: EventEmitter2,
    private readonly explanationService: ExplanationService,
    private readonly preferencesService: ExplanationPreferencesService,
  ) {
    this.supportedLanguages = this.configService.get<string[]>('recommendation.translation.supportedLanguages', ['fr', 'en', 'es', 'de', 'it']);
    this.defaultLanguage = this.configService.get<string>('app.defaultLanguage', 'fr');
    this.translationApiKey = this.configService.get<string>('recommendation.translation.apiKey', '');
    this.translationApiUrl = this.configService.get<string>('recommendation.translation.apiUrl', '');
    
    // Écouter les événements de création/mise à jour de templates
    this.eventEmitter.on('explanation.template.created', this.handleTemplateCreated.bind(this));
    this.eventEmitter.on('explanation.template.updated', this.handleTemplateUpdated.bind(this));
  }
  
  /**
   * Traduit un texte vers une langue cible
   * @param text Texte à traduire
   * @param sourceLanguage Langue source
   * @param targetLanguage Langue cible
   * @returns Traduction
   */
  async translateText(
    text: string,
    sourceLanguage: string,
    targetLanguage: string,
  ): Promise<Translation> {
    // Si les langues sont identiques, retourner le texte original
    if (sourceLanguage === targetLanguage) {
      return {
        sourceText: text,
        sourceLanguage,
        translatedText: text,
        targetLanguage,
        quality: 1,
      };
    }
    
    // Vérifier si la traduction est en cache
    const cacheKey = `${sourceLanguage}:${targetLanguage}:${text}`;
    if (this.translationCache.has(cacheKey)) {
      return this.translationCache.get(cacheKey);
    }
    
    try {
      // Utiliser l'API de traduction
      const response = await firstValueFrom(
        this.httpService.post(
          this.translationApiUrl,
          {
            q: text,
            source: sourceLanguage,
            target: targetLanguage,
            format: 'text',
          },
          {
            headers: {
              'Authorization': `Bearer ${this.translationApiKey}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );
      
      const translation: Translation = {
        sourceText: text,
        sourceLanguage,
        translatedText: response.data.translatedText,
        targetLanguage,
        quality: response.data.quality || 0.8,
        metadata: {
          provider: 'external-api',
          timestamp: new Date().toISOString(),
        },
      };
      
      // Mettre en cache la traduction
      this.translationCache.set(cacheKey, translation);
      
      return translation;
    } catch (error) {
      this.logger.error(`Erreur lors de la traduction: ${error.message}`);
      
      // En cas d'erreur, utiliser une traduction de secours
      return this.fallbackTranslation(text, sourceLanguage, targetLanguage);
    }
  }
  
  /**
   * Traduit un template d'explication
   * @param templateId ID du template à traduire
   * @param targetLanguage Langue cible
   * @returns Template traduit
   */
  async translateTemplate(
    templateId: string,
    targetLanguage: string,
  ): Promise<TranslatedTemplate> {
    // Récupérer le template original
    const originalTemplate = await this.prisma.explanationTemplate.findUnique({
      where: { id: templateId },
    });
    
    if (!originalTemplate) {
      throw new Error(`Template avec l'ID ${templateId} non trouvé`);
    }
    
    // Si le template est déjà dans la langue cible, le retourner directement
    if (originalTemplate.language === targetLanguage) {
      return {
        originalId: originalTemplate.id,
        name: originalTemplate.name,
        description: originalTemplate.description,
        factorType: originalTemplate.factorType,
        template: originalTemplate.template,
        variables: originalTemplate.variables,
        language: originalTemplate.language,
      };
    }
    
    // Vérifier si une traduction existe déjà
    const existingTranslation = await this.prisma.explanationTemplate.findFirst({
      where: {
        name: originalTemplate.name,
        language: targetLanguage,
      },
    });
    
    if (existingTranslation) {
      return {
        originalId: originalTemplate.id,
        name: existingTranslation.name,
        description: existingTranslation.description,
        factorType: existingTranslation.factorType,
        template: existingTranslation.template,
        variables: existingTranslation.variables,
        language: existingTranslation.language,
      };
    }
    
    // Traduire le template
    const [nameTranslation, descriptionTranslation, templateTranslation] = await Promise.all([
      this.translateText(originalTemplate.name, originalTemplate.language, targetLanguage),
      this.translateText(originalTemplate.description, originalTemplate.language, targetLanguage),
      this.translateText(originalTemplate.template, originalTemplate.language, targetLanguage),
    ]);
    
    // Créer le template traduit
    const translatedTemplate = await this.prisma.explanationTemplate.create({
      data: {
        name: nameTranslation.translatedText,
        description: descriptionTranslation.translatedText,
        factorType: originalTemplate.factorType,
        template: templateTranslation.translatedText,
        variables: originalTemplate.variables,
        language: targetLanguage,
      },
    });
    
    // Émettre un événement de traduction de template
    this.eventEmitter.emit('explanation.template.translated', {
      originalId: originalTemplate.id,
      translatedId: translatedTemplate.id,
      sourceLanguage: originalTemplate.language,
      targetLanguage,
    });
    
    return {
      originalId: originalTemplate.id,
      name: translatedTemplate.name,
      description: translatedTemplate.description,
      factorType: translatedTemplate.factorType,
      template: translatedTemplate.template,
      variables: translatedTemplate.variables,
      language: translatedTemplate.language,
    };
  }
  
  /**
   * Traduit une explication complète
   * @param explanationId ID de l'explication à traduire
   * @param targetLanguage Langue cible
   * @returns Explication traduite
   */
  async translateExplanation(
    explanationId: string,
    targetLanguage: string,
  ): Promise<any> {
    // Récupérer l'explication
    const explanation = await this.prisma.explanation.findUnique({
      where: { id: explanationId },
      include: {
        recommendation: true,
      },
    });
    
    if (!explanation) {
      throw new Error(`Explication avec l'ID ${explanationId} non trouvée`);
    }
    
    // Traduire le texte de l'explication
    const translation = await this.translateText(
      explanation.text,
      explanation.language || this.defaultLanguage,
      targetLanguage,
    );
    
    // Mettre à jour l'explication avec la traduction
    const updatedExplanation = await this.prisma.explanation.update({
      where: { id: explanationId },
      data: {
        text: translation.translatedText,
        language: targetLanguage,
        metadata: {
          ...explanation.metadata,
          translation: {
            sourceLanguage: translation.sourceLanguage,
            targetLanguage: translation.targetLanguage,
            quality: translation.quality,
            timestamp: new Date().toISOString(),
          },
        },
      },
    });
    
    return updatedExplanation;
  }
  
  /**
   * Traduit tous les templates vers toutes les langues supportées
   * @returns Nombre de templates traduits
   */
  async translateAllTemplates(): Promise<number> {
    // Récupérer tous les templates dans la langue par défaut
    const templates = await this.prisma.explanationTemplate.findMany({
      where: {
        language: this.defaultLanguage,
      },
    });
    
    let translatedCount = 0;
    
    // Pour chaque template et chaque langue supportée
    for (const template of templates) {
      for (const language of this.supportedLanguages) {
        // Ne pas traduire vers la langue source
        if (language === this.defaultLanguage) {
          continue;
        }
        
        try {
          await this.translateTemplate(template.id, language);
          translatedCount++;
        } catch (error) {
          this.logger.error(`Erreur lors de la traduction du template ${template.id} vers ${language}: ${error.message}`);
        }
      }
    }
    
    return translatedCount;
  }
  
  /**
   * Génère une explication dans la langue préférée de l'utilisateur
   * @param userId ID de l'utilisateur
   * @param recommendationId ID de la recommandation
   * @returns Explication générée
   */
  async generateExplanationInUserLanguage(
    userId: string,
    recommendationId: string,
  ): Promise<any> {
    // Récupérer les préférences de l'utilisateur
    const preferences = await this.preferencesService.getUserPreferences(userId);
    
    // Générer l'explication dans la langue par défaut
    const explanation = await this.explanationService.generateExplanation(
      recommendationId,
      userId,
    );
    
    // Si l'explication est déjà dans la langue préférée, la retourner directement
    if (explanation.language === preferences.language) {
      return explanation;
    }
    
    // Sinon, traduire l'explication
    return this.translateExplanation(explanation.id, preferences.language);
  }
  
  /**
   * Gère l'événement de création d'un template
   * @param payload Payload de l'événement
   */
  private async handleTemplateCreated(payload: { templateId: string }): Promise<void> {
    // Traduire le nouveau template vers toutes les langues supportées
    for (const language of this.supportedLanguages) {
      // Ne pas traduire vers la langue source
      if (language === this.defaultLanguage) {
        continue;
      }
      
      try {
        await this.translateTemplate(payload.templateId, language);
      } catch (error) {
        this.logger.error(`Erreur lors de la traduction automatique du template ${payload.templateId} vers ${language}: ${error.message}`);
      }
    }
  }
  
  /**
   * Gère l'événement de mise à jour d'un template
   * @param payload Payload de l'événement
   */
  private async handleTemplateUpdated(payload: { templateId: string }): Promise<void> {
    // Mettre à jour les traductions existantes
    const originalTemplate = await this.prisma.explanationTemplate.findUnique({
      where: { id: payload.templateId },
    });
    
    if (!originalTemplate || originalTemplate.language !== this.defaultLanguage) {
      return;
    }
    
    // Récupérer toutes les traductions existantes
    const translations = await this.prisma.explanationTemplate.findMany({
      where: {
        name: originalTemplate.name,
        language: {
          not: this.defaultLanguage,
        },
      },
    });
    
    // Mettre à jour chaque traduction
    for (const translation of translations) {
      try {
        const [nameTranslation, descriptionTranslation, templateTranslation] = await Promise.all([
          this.translateText(originalTemplate.name, this.defaultLanguage, translation.language),
          this.translateText(originalTemplate.description, this.defaultLanguage, translation.language),
          this.translateText(originalTemplate.template, this.defaultLanguage, translation.language),
        ]);
        
        await this.prisma.explanationTemplate.update({
          where: { id: translation.id },
          data: {
            name: nameTranslation.translatedText,
            description: descriptionTranslation.translatedText,
            template: templateTranslation.translatedText,
            variables: originalTemplate.variables,
          },
        });
      } catch (error) {
        this.logger.error(`Erreur lors de la mise à jour de la traduction ${translation.id}: ${error.message}`);
      }
    }
  }
  
  /**
   * Fournit une traduction de secours en cas d'erreur avec l'API externe
   * @param text Texte à traduire
   * @param sourceLanguage Langue source
   * @param targetLanguage Langue cible
   * @returns Traduction de secours
   */
  private fallbackTranslation(
    text: string,
    sourceLanguage: string,
    targetLanguage: string,
  ): Translation {
    // Utiliser une traduction simple basée sur des règles
    // Dans une implémentation réelle, on pourrait utiliser une bibliothèque de traduction locale
    
    this.logger.log(`Utilisation de la traduction de secours pour ${sourceLanguage} -> ${targetLanguage}`);
    
    return {
      sourceText: text,
      sourceLanguage,
      translatedText: text, // Dans une implémentation réelle, on ferait une vraie traduction
      targetLanguage,
      quality: 0.5,
      metadata: {
        provider: 'fallback',
        timestamp: new Date().toISOString(),
      },
    };
  }
}
