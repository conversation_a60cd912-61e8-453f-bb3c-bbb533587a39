import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { RecommendationService } from './recommendation.service';

/**
 * Interface pour les stratégies d'agrégation de groupe
 */
export enum GroupAggregationStrategy {
  /** Moyenne des scores */
  AVERAGE = 'average',
  
  /** Minimum des scores (satisfaction de tous) */
  LEAST_MISERY = 'least_misery',
  
  /** Maximum des scores (satisfaction d'au moins un) */
  MOST_PLEASURE = 'most_pleasure',
  
  /** Moyenne sans les extrêmes */
  AVERAGE_WITHOUT_MISERY = 'average_without_misery',
  
  /** Médiane des scores */
  MEDIAN = 'median',
  
  /** Multiplicatif (produit des scores) */
  MULTIPLICATIVE = 'multiplicative',
  
  /** Basé sur la variance (favorise le consensus) */
  CONSENSUS = 'consensus',
  
  /** Pondéré par utilisateur */
  WEIGHTED = 'weighted',
}

/**
 * Interface pour les options de recommandation de groupe
 */
export interface GroupRecommendationOptions {
  /** IDs des utilisateurs du groupe */
  userIds: string[];
  
  /** Stratégie d'agrégation */
  aggregationStrategy: GroupAggregationStrategy;
  
  /** Poids des utilisateurs (pour la stratégie pondérée) */
  userWeights?: Record<string, number>;
  
  /** Nombre maximum de recommandations */
  maxRecommendations?: number;
  
  /** Seuil de satisfaction minimum */
  minSatisfactionThreshold?: number;
  
  /** Filtres supplémentaires */
  filters?: Record<string, any>;
}

/**
 * Interface pour les métriques de satisfaction de groupe
 */
export interface GroupSatisfactionMetrics {
  /** Score de satisfaction global */
  overallSatisfaction: number;
  
  /** Score de satisfaction minimum */
  minSatisfaction: number;
  
  /** Score de satisfaction maximum */
  maxSatisfaction: number;
  
  /** Score de satisfaction moyen */
  averageSatisfaction: number;
  
  /** Écart-type de satisfaction */
  satisfactionStdDev: number;
  
  /** Satisfaction par utilisateur */
  satisfactionByUser: Record<string, number>;
  
  /** Nombre d'utilisateurs satisfaits (score > 0.5) */
  satisfiedUsersCount: number;
  
  /** Pourcentage d'utilisateurs satisfaits */
  satisfiedUsersPercentage: number;
}

/**
 * Service pour les recommandations de groupe
 */
@Injectable()
export class GroupRecommendationService {
  private readonly logger = new Logger(GroupRecommendationService.name);
  
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly recommendationService: RecommendationService,
  ) {}
  
  /**
   * Génère des recommandations pour un groupe d'utilisateurs
   * @param options Options de recommandation de groupe
   * @returns Recommandations de groupe
   */
  async getGroupRecommendations(options: GroupRecommendationOptions): Promise<any[]> {
    try {
      const { userIds, aggregationStrategy, userWeights, maxRecommendations = 10, minSatisfactionThreshold = 0.5, filters } = options;
      
      // Vérifier que le groupe contient au moins 2 utilisateurs
      if (!userIds || userIds.length < 2) {
        throw new Error('Un groupe doit contenir au moins 2 utilisateurs');
      }
      
      // Récupérer les recommandations individuelles pour chaque utilisateur
      const userRecommendations = await this.getUserRecommendations(userIds);
      
      // Agréger les recommandations
      const aggregatedRecommendations = this.aggregateRecommendations(
        userRecommendations,
        aggregationStrategy,
        userWeights,
      );
      
      // Filtrer les recommandations selon le seuil de satisfaction minimum
      const filteredRecommendations = aggregatedRecommendations.filter(
        recommendation => recommendation.groupScore >= minSatisfactionThreshold,
      );
      
      // Appliquer les filtres supplémentaires
      const finalRecommendations = this.applyFilters(filteredRecommendations, filters);
      
      // Trier par score de groupe
      const sortedRecommendations = finalRecommendations.sort(
        (a, b) => b.groupScore - a.groupScore,
      );
      
      // Limiter le nombre de recommandations
      const limitedRecommendations = sortedRecommendations.slice(0, maxRecommendations);
      
      // Calculer les métriques de satisfaction
      const satisfactionMetrics = this.calculateGroupSatisfactionMetrics(
        limitedRecommendations,
        userIds.length,
      );
      
      // Émettre un événement de recommandation de groupe
      this.eventEmitter.emit('recommendation.group', {
        userIds,
        aggregationStrategy,
        recommendationCount: limitedRecommendations.length,
        satisfactionMetrics,
      });
      
      return limitedRecommendations;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des recommandations de groupe: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Récupère les recommandations individuelles pour chaque utilisateur
   * @param userIds IDs des utilisateurs
   * @returns Recommandations par utilisateur
   */
  private async getUserRecommendations(userIds: string[]): Promise<Record<string, any[]>> {
    const userRecommendations: Record<string, any[]> = {};
    
    // Récupérer les recommandations pour chaque utilisateur
    for (const userId of userIds) {
      try {
        const recommendations = await this.recommendationService.getRecommendationsForUser(userId);
        userRecommendations[userId] = recommendations;
      } catch (error) {
        this.logger.error(`Erreur lors de la récupération des recommandations pour l'utilisateur ${userId}: ${error.message}`);
        userRecommendations[userId] = [];
      }
    }
    
    return userRecommendations;
  }
  
  /**
   * Agrège les recommandations individuelles en recommandations de groupe
   * @param userRecommendations Recommandations par utilisateur
   * @param strategy Stratégie d'agrégation
   * @param userWeights Poids des utilisateurs
   * @returns Recommandations agrégées
   */
  private aggregateRecommendations(
    userRecommendations: Record<string, any[]>,
    strategy: GroupAggregationStrategy,
    userWeights?: Record<string, number>,
  ): any[] {
    // Créer un ensemble de toutes les recommandations
    const allRecommendationIds = new Set<string>();
    Object.values(userRecommendations).forEach(recommendations => {
      recommendations.forEach(recommendation => {
        allRecommendationIds.add(recommendation.id);
      });
    });
    
    // Créer un dictionnaire des scores par recommandation et par utilisateur
    const scoresByRecommendation: Record<string, Record<string, number>> = {};
    
    Object.entries(userRecommendations).forEach(([userId, recommendations]) => {
      recommendations.forEach(recommendation => {
        if (!scoresByRecommendation[recommendation.id]) {
          scoresByRecommendation[recommendation.id] = {};
        }
        scoresByRecommendation[recommendation.id][userId] = recommendation.score || 0.5;
      });
    });
    
    // Compléter les scores manquants avec une valeur par défaut (0.1)
    Object.keys(scoresByRecommendation).forEach(recommendationId => {
      Object.keys(userRecommendations).forEach(userId => {
        if (!scoresByRecommendation[recommendationId][userId]) {
          scoresByRecommendation[recommendationId][userId] = 0.1;
        }
      });
    });
    
    // Agréger les scores selon la stratégie choisie
    const aggregatedRecommendations: any[] = [];
    
    for (const recommendationId of allRecommendationIds) {
      const userScores = scoresByRecommendation[recommendationId] || {};
      const scores = Object.values(userScores);
      
      if (scores.length === 0) {
        continue;
      }
      
      // Récupérer les détails de la recommandation
      let recommendationDetails = null;
      for (const recommendations of Object.values(userRecommendations)) {
        const recommendation = recommendations.find(r => r.id === recommendationId);
        if (recommendation) {
          recommendationDetails = recommendation;
          break;
        }
      }
      
      if (!recommendationDetails) {
        continue;
      }
      
      // Calculer le score agrégé selon la stratégie
      let groupScore = 0;
      
      switch (strategy) {
        case GroupAggregationStrategy.AVERAGE:
          groupScore = this.calculateAverage(scores);
          break;
        
        case GroupAggregationStrategy.LEAST_MISERY:
          groupScore = Math.min(...scores);
          break;
        
        case GroupAggregationStrategy.MOST_PLEASURE:
          groupScore = Math.max(...scores);
          break;
        
        case GroupAggregationStrategy.AVERAGE_WITHOUT_MISERY:
          // Exclure les scores inférieurs à 0.3
          const filteredScores = scores.filter(score => score >= 0.3);
          groupScore = filteredScores.length > 0 ? this.calculateAverage(filteredScores) : 0;
          break;
        
        case GroupAggregationStrategy.MEDIAN:
          groupScore = this.calculateMedian(scores);
          break;
        
        case GroupAggregationStrategy.MULTIPLICATIVE:
          groupScore = scores.reduce((product, score) => product * score, 1);
          // Normaliser le score multiplicatif
          groupScore = Math.pow(groupScore, 1 / scores.length);
          break;
        
        case GroupAggregationStrategy.CONSENSUS:
          const mean = this.calculateAverage(scores);
          const variance = this.calculateVariance(scores, mean);
          // Favoriser les recommandations avec une faible variance (consensus élevé)
          groupScore = mean * (1 - Math.sqrt(variance));
          break;
        
        case GroupAggregationStrategy.WEIGHTED:
          if (!userWeights) {
            // Si aucun poids n'est fourni, utiliser la moyenne simple
            groupScore = this.calculateAverage(scores);
          } else {
            // Calculer la moyenne pondérée
            let weightedSum = 0;
            let totalWeight = 0;
            
            Object.entries(userScores).forEach(([userId, score]) => {
              const weight = userWeights[userId] || 1;
              weightedSum += score * weight;
              totalWeight += weight;
            });
            
            groupScore = totalWeight > 0 ? weightedSum / totalWeight : 0;
          }
          break;
        
        default:
          groupScore = this.calculateAverage(scores);
      }
      
      // Ajouter la recommandation agrégée
      aggregatedRecommendations.push({
        ...recommendationDetails,
        groupScore,
        userScores,
        aggregationStrategy: strategy,
      });
    }
    
    return aggregatedRecommendations;
  }
  
  /**
   * Applique des filtres aux recommandations
   * @param recommendations Recommandations
   * @param filters Filtres
   * @returns Recommandations filtrées
   */
  private applyFilters(recommendations: any[], filters?: Record<string, any>): any[] {
    if (!filters || Object.keys(filters).length === 0) {
      return recommendations;
    }
    
    return recommendations.filter(recommendation => {
      // Vérifier chaque filtre
      for (const [key, value] of Object.entries(filters)) {
        // Vérifier si la recommandation a la propriété
        if (!(key in recommendation)) {
          continue;
        }
        
        // Vérifier si la valeur correspond
        if (Array.isArray(value)) {
          // Si le filtre est un tableau, vérifier si la valeur est dans le tableau
          if (!value.includes(recommendation[key])) {
            return false;
          }
        } else if (typeof value === 'object' && value !== null) {
          // Si le filtre est un objet, vérifier les opérateurs
          for (const [op, opValue] of Object.entries(value)) {
            switch (op) {
              case 'eq':
                if (recommendation[key] !== opValue) return false;
                break;
              case 'neq':
                if (recommendation[key] === opValue) return false;
                break;
              case 'gt':
                if (recommendation[key] <= opValue) return false;
                break;
              case 'gte':
                if (recommendation[key] < opValue) return false;
                break;
              case 'lt':
                if (recommendation[key] >= opValue) return false;
                break;
              case 'lte':
                if (recommendation[key] > opValue) return false;
                break;
              case 'in':
                if (!Array.isArray(opValue) || !opValue.includes(recommendation[key])) return false;
                break;
              case 'nin':
                if (!Array.isArray(opValue) || opValue.includes(recommendation[key])) return false;
                break;
              case 'contains':
                if (typeof recommendation[key] !== 'string' || !recommendation[key].includes(opValue)) return false;
                break;
            }
          }
        } else {
          // Sinon, vérifier l'égalité
          if (recommendation[key] !== value) {
            return false;
          }
        }
      }
      
      return true;
    });
  }
  
  /**
   * Calcule les métriques de satisfaction du groupe
   * @param recommendations Recommandations
   * @param userCount Nombre d'utilisateurs
   * @returns Métriques de satisfaction
   */
  private calculateGroupSatisfactionMetrics(
    recommendations: any[],
    userCount: number,
  ): GroupSatisfactionMetrics {
    if (recommendations.length === 0) {
      return {
        overallSatisfaction: 0,
        minSatisfaction: 0,
        maxSatisfaction: 0,
        averageSatisfaction: 0,
        satisfactionStdDev: 0,
        satisfactionByUser: {},
        satisfiedUsersCount: 0,
        satisfiedUsersPercentage: 0,
      };
    }
    
    // Calculer la satisfaction moyenne par utilisateur
    const satisfactionByUser: Record<string, number> = {};
    const userIds = Object.keys(recommendations[0].userScores || {});
    
    userIds.forEach(userId => {
      const userSatisfaction = recommendations.reduce(
        (sum, recommendation) => sum + (recommendation.userScores?.[userId] || 0),
        0,
      ) / recommendations.length;
      
      satisfactionByUser[userId] = userSatisfaction;
    });
    
    // Calculer les métriques globales
    const satisfactionValues = Object.values(satisfactionByUser);
    const minSatisfaction = Math.min(...satisfactionValues);
    const maxSatisfaction = Math.max(...satisfactionValues);
    const averageSatisfaction = this.calculateAverage(satisfactionValues);
    const satisfactionStdDev = Math.sqrt(this.calculateVariance(satisfactionValues, averageSatisfaction));
    
    // Calculer le nombre d'utilisateurs satisfaits
    const satisfiedUsersCount = satisfactionValues.filter(satisfaction => satisfaction >= 0.5).length;
    const satisfiedUsersPercentage = (satisfiedUsersCount / userCount) * 100;
    
    // Calculer la satisfaction globale
    const overallSatisfaction = recommendations.reduce(
      (sum, recommendation) => sum + recommendation.groupScore,
      0,
    ) / recommendations.length;
    
    return {
      overallSatisfaction,
      minSatisfaction,
      maxSatisfaction,
      averageSatisfaction,
      satisfactionStdDev,
      satisfactionByUser,
      satisfiedUsersCount,
      satisfiedUsersPercentage,
    };
  }
  
  /**
   * Calcule la moyenne d'un tableau de nombres
   * @param values Valeurs
   * @returns Moyenne
   */
  private calculateAverage(values: number[]): number {
    if (values.length === 0) {
      return 0;
    }
    
    return values.reduce((sum, value) => sum + value, 0) / values.length;
  }
  
  /**
   * Calcule la médiane d'un tableau de nombres
   * @param values Valeurs
   * @returns Médiane
   */
  private calculateMedian(values: number[]): number {
    if (values.length === 0) {
      return 0;
    }
    
    const sortedValues = [...values].sort((a, b) => a - b);
    const middle = Math.floor(sortedValues.length / 2);
    
    if (sortedValues.length % 2 === 0) {
      return (sortedValues[middle - 1] + sortedValues[middle]) / 2;
    } else {
      return sortedValues[middle];
    }
  }
  
  /**
   * Calcule la variance d'un tableau de nombres
   * @param values Valeurs
   * @param mean Moyenne
   * @returns Variance
   */
  private calculateVariance(values: number[], mean: number): number {
    if (values.length === 0) {
      return 0;
    }
    
    return values.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / values.length;
  }
}
