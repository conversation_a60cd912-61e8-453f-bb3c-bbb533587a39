import { Test, TestingModule } from '@nestjs/testing';
import { ModerationIntegrationService } from './moderation-integration.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { of, throwError } from 'rxjs';
import { HttpException } from '@nestjs/common';

describe('ModerationIntegrationService', () => {
  let service: ModerationIntegrationService;
  let httpService: HttpService;
  let configService: ConfigService;

  const mockPrismaService = {
    moderationLog: {
      create: jest.fn(),
    },
  };

  const mockConfigService = {
    get: jest.fn((key, defaultValue) => {
      if (key === 'MODERATION_SERVICE_URL') return 'http://moderation-service';
      if (key === 'MODERATION_SERVICE_API_KEY') return 'test-api-key';
      return defaultValue;
    }),
  };

  const mockHttpService = {
    post: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ModerationIntegrationService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
      ],
    }).compile();

    service = module.get<ModerationIntegrationService>(ModerationIntegrationService);
    httpService = module.get<HttpService>(HttpService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('filterRecommendations', () => {
    it('should return the original recommendations if moderation service is not available', async () => {
      // Mock the service as unavailable
      jest.spyOn(configService, 'get').mockImplementation((key) => {
        if (key === 'MODERATION_SERVICE_URL') return 'http://localhost:3002';
        if (key === 'MODERATION_SERVICE_API_KEY') return 'default-api-key';
        return null;
      });

      const recommendations = [
        { id: 'rec1', title: 'Recommendation 1' },
        { id: 'rec2', title: 'Recommendation 2' },
      ];

      const result = await service.filterRecommendations(recommendations, RecommendationType.RETREAT);

      expect(result).toEqual(recommendations);
    });

    it('should filter out rejected recommendations', async () => {
      // Mock the moderation service response
      mockHttpService.post.mockReturnValue(
        of({
          data: {
            statuses: {
              rec1: 'APPROVED',
              rec2: 'REJECTED',
              rec3: 'PENDING',
            },
          },
        }),
      );

      const recommendations = [
        { id: 'rec1', title: 'Recommendation 1' },
        { id: 'rec2', title: 'Recommendation 2' },
        { id: 'rec3', title: 'Recommendation 3' },
      ];

      const result = await service.filterRecommendations(recommendations, RecommendationType.RETREAT);

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('rec1');
      expect(result[1].id).toBe('rec3');
    });

    it('should return original recommendations on error', async () => {
      // Mock an error response
      mockHttpService.post.mockReturnValue(
        throwError(() => new Error('Service unavailable')),
      );

      const recommendations = [
        { id: 'rec1', title: 'Recommendation 1' },
        { id: 'rec2', title: 'Recommendation 2' },
      ];

      const result = await service.filterRecommendations(recommendations, RecommendationType.RETREAT);

      expect(result).toEqual(recommendations);
    });
  });

  describe('isRecommendationAllowed', () => {
    it('should return true if moderation service is not available', async () => {
      // Mock the service as unavailable
      jest.spyOn(configService, 'get').mockImplementation((key) => {
        if (key === 'MODERATION_SERVICE_URL') return 'http://localhost:3002';
        if (key === 'MODERATION_SERVICE_API_KEY') return 'default-api-key';
        return null;
      });

      const result = await service.isRecommendationAllowed('rec1', RecommendationType.RETREAT);

      expect(result).toBe(true);
    });

    it('should return true for approved or pending recommendations', async () => {
      // Mock the moderation service response
      mockHttpService.post.mockReturnValue(
        of({
          data: {
            statuses: {
              rec1: 'APPROVED',
            },
          },
        }),
      );

      const result = await service.isRecommendationAllowed('rec1', RecommendationType.RETREAT);

      expect(result).toBe(true);

      // Test for pending
      mockHttpService.post.mockReturnValue(
        of({
          data: {
            statuses: {
              rec2: 'PENDING',
            },
          },
        }),
      );

      const result2 = await service.isRecommendationAllowed('rec2', RecommendationType.RETREAT);

      expect(result2).toBe(true);
    });

    it('should return false for rejected recommendations', async () => {
      // Mock the moderation service response
      mockHttpService.post.mockReturnValue(
        of({
          data: {
            statuses: {
              rec3: 'REJECTED',
            },
          },
        }),
      );

      const result = await service.isRecommendationAllowed('rec3', RecommendationType.RETREAT);

      expect(result).toBe(false);
    });
  });

  describe('reportRecommendation', () => {
    it('should report to moderation service if available', async () => {
      // Mock the moderation service response
      mockHttpService.post.mockReturnValue(
        of({
          data: {
            success: true,
            message: 'Report received',
          },
        }),
      );

      const result = await service.reportRecommendation(
        'rec1',
        RecommendationType.RETREAT,
        'user1',
        'Inappropriate content',
      );

      expect(result.success).toBe(true);
      expect(mockHttpService.post).toHaveBeenCalledWith(
        'http://moderation-service/api/moderation/report',
        expect.any(Object),
        expect.any(Object),
      );
    });

    it('should store report locally if moderation service is unavailable', async () => {
      // Mock an error response
      mockHttpService.post.mockReturnValue(
        throwError(() => new Error('Service unavailable')),
      );

      const result = await service.reportRecommendation(
        'rec1',
        RecommendationType.RETREAT,
        'user1',
        'Inappropriate content',
      );

      expect(result.success).toBe(true);
      expect(result.message).toBe('Signalement enregistré localement');
      expect(result.data).toBeDefined();
    });
  });
});
