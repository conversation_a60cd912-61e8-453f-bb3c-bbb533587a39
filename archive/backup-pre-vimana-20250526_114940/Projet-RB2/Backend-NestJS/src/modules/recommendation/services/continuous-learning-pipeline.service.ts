import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { <PERSON>ron } from '@nestjs/schedule';
import {
  PipelineExecutionStatus,
  ModelVersion,
  ModelDeploymentConfig,
  LearningPipelineEvent
} from '../interfaces/continuous-learning-pipeline.interface';
import { DataPreprocessingService } from './data-preprocessing.service';
import { FeatureExtractionService } from './feature-extraction.service';
import { ModelTrainingService } from './model-training.service';
import { v4 as uuidv4 } from 'uuid';

/**
 * Service for orchestrating the continuous learning pipeline
 */
@Injectable()
export class ContinuousLearningPipelineService {
  private readonly logger = new Logger(ContinuousLearningPipelineService.name);
  private isRunning = false;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly dataPreprocessingService: DataPreprocessingService,
    private readonly featureExtractionService: FeatureExtractionService,
    private readonly modelTrainingService: ModelTrainingService,
  ) {
    this.logger.log('Initializing continuous learning pipeline service');
  }

  /**
   * Run the continuous learning pipeline
   * @returns Pipeline execution status
   */
  async runPipeline(): Promise<PipelineExecutionStatus> {
    if (this.isRunning) {
      this.logger.warn('Pipeline is already running, skipping this execution');
      return {
        id: 'already-running',
        startTime: new Date(),
        status: 'failed',
        steps: [],
        error: 'Pipeline is already running',
      };
    }

    this.isRunning = true;
    const pipelineId = uuidv4();
    const startTime = new Date();

    // Create pipeline execution record
    const pipelineStatus: PipelineExecutionStatus = {
      id: pipelineId,
      startTime,
      status: 'running',
      steps: [
        { name: 'data_collection', status: 'pending' },
        { name: 'data_preprocessing', status: 'pending' },
        { name: 'feature_extraction', status: 'pending' },
        { name: 'model_training', status: 'pending' },
        { name: 'model_evaluation', status: 'pending' },
        { name: 'model_deployment', status: 'pending' },
      ],
    };

    // Emit pipeline started event
    this.emitPipelineEvent('pipeline_started', {
      pipelineId,
      startTime,
    });

    try {
      // Step 1: Data Collection
      this.updateStepStatus(pipelineStatus, 'data_collection', 'running');
      const rawData = await this.collectData();
      this.updateStepStatus(pipelineStatus, 'data_collection', 'completed', {
        dataSize: rawData.length,
      });

      // Step 2: Data Preprocessing
      this.updateStepStatus(pipelineStatus, 'data_preprocessing', 'running');
      const preprocessedData = await this.dataPreprocessingService.preprocessData(rawData);
      this.updateStepStatus(pipelineStatus, 'data_preprocessing', 'completed', {
        inputSize: rawData.length,
        outputSize: preprocessedData.length,
      });

      // Step 3: Feature Extraction
      this.updateStepStatus(pipelineStatus, 'feature_extraction', 'running');
      const featureData = await this.featureExtractionService.extractFeatures(preprocessedData);
      this.updateStepStatus(pipelineStatus, 'feature_extraction', 'completed', {
        inputSize: preprocessedData.length,
        outputSize: featureData.length,
        featureCount: Object.keys(featureData[0]?.features || {}).length,
      });

      // Step 4: Model Training
      this.updateStepStatus(pipelineStatus, 'model_training', 'running');
      const modelVersion = await this.modelTrainingService.trainModel(featureData);
      this.updateStepStatus(pipelineStatus, 'model_training', 'completed', {
        modelId: modelVersion.id,
        modelVersion: modelVersion.version,
        modelType: modelVersion.modelType,
        trainingMetrics: modelVersion.performance.trainingMetrics,
      });

      // Step 5: Model Evaluation
      this.updateStepStatus(pipelineStatus, 'model_evaluation', 'running');
      const evaluationResult = await this.evaluateModel(modelVersion);
      this.updateStepStatus(pipelineStatus, 'model_evaluation', 'completed', {
        evaluationResult,
      });

      // Step 6: Model Deployment (if evaluation is successful)
      if (evaluationResult.shouldDeploy) {
        this.updateStepStatus(pipelineStatus, 'model_deployment', 'running');
        const deploymentResult = await this.deployModel(modelVersion);
        this.updateStepStatus(pipelineStatus, 'model_deployment', 'completed', {
          deploymentResult,
        });
      } else {
        this.updateStepStatus(pipelineStatus, 'model_deployment', 'completed', {
          skipped: true,
          reason: 'Model did not meet deployment criteria',
        });
      }

      // Update pipeline status
      pipelineStatus.status = 'completed';
      pipelineStatus.endTime = new Date();

      // Emit pipeline completed event
      this.emitPipelineEvent('pipeline_completed', {
        pipelineId,
        startTime,
        endTime: pipelineStatus.endTime,
        steps: pipelineStatus.steps,
      });

      // Save pipeline execution record
      await this.savePipelineExecution(pipelineStatus);

      this.logger.log(`Pipeline ${pipelineId} completed successfully`);

      return pipelineStatus;
    } catch (error) {
      this.logger.error(`Error running pipeline: ${error.message}`);

      // Update pipeline status
      pipelineStatus.status = 'failed';
      pipelineStatus.endTime = new Date();
      pipelineStatus.error = error.message;

      // Find the current step and mark it as failed
      const currentStep = pipelineStatus.steps.find(step => step.status === 'running');
      if (currentStep) {
        currentStep.status = 'failed';
        currentStep.error = error.message;
        currentStep.endTime = new Date();
      }

      // Emit pipeline failed event
      this.emitPipelineEvent('pipeline_failed', {
        pipelineId,
        startTime,
        endTime: pipelineStatus.endTime,
        error: error.message,
        steps: pipelineStatus.steps,
      });

      // Save pipeline execution record
      await this.savePipelineExecution(pipelineStatus);

      return pipelineStatus;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Scheduled job to run the pipeline periodically
   */
  @Cron('0 0 * * *') // Run at midnight every day
  async scheduledPipelineRun() {
    this.logger.log('Starting scheduled pipeline run');
    await this.runPipeline();
  }

  /**
   * Collect raw data for the pipeline
   * @returns Raw interaction data
   */
  private async collectData(): Promise<any[]> {
    this.logger.log('Collecting data for pipeline');

    // Get the date range for data collection
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30); // Last 30 days

    // Collect user interactions
    const interactions = await this.prisma.userInteraction.findMany({
      where: {
        timestamp: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            preferences: true,
          },
        },
      },
    });

    this.logger.log(`Collected ${interactions.length} interactions`);

    return interactions;
  }

  /**
   * Update the status of a pipeline step
   * @param pipelineStatus Pipeline execution status
   * @param stepName Name of the step
   * @param status New status
   * @param metrics Optional metrics for the step
   */
  private updateStepStatus(
    pipelineStatus: PipelineExecutionStatus,
    stepName: string,
    status: 'pending' | 'running' | 'completed' | 'failed',
    metrics?: Record<string, any>,
  ): void {
    const step = pipelineStatus.steps.find(s => s.name === stepName);

    if (step) {
      step.status = status;

      if (status === 'running') {
        step.startTime = new Date();
      } else if (status === 'completed' || status === 'failed') {
        step.endTime = new Date();
      }

      if (metrics) {
        step.metrics = metrics;
      }
    }
  }

  /**
   * Evaluate a trained model
   * @param modelVersion Model version to evaluate
   * @returns Evaluation result
   */
  private async evaluateModel(modelVersion: ModelVersion): Promise<{ shouldDeploy: boolean; metrics: Record<string, any> }> {
    this.logger.log(`Evaluating model ${modelVersion.version}`);

    // Get the current production model
    const currentModel = await this.prisma.modelVersion.findFirst({
      where: {
        status: 'deployed',
        modelType: modelVersion.modelType,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // If there's no current model, deploy this one
    if (!currentModel) {
      return {
        shouldDeploy: true,
        metrics: {
          reason: 'No current model deployed',
        },
      };
    }

    // Compare the new model with the current model
    const comparison = this.compareModels(modelVersion, currentModel);

    // Decide whether to deploy based on improvement threshold
    const improvementThreshold = this.configService.get<number>('MODEL_IMPROVEMENT_THRESHOLD', 0.05); // 5% improvement

    const shouldDeploy = comparison.percentImprovement > improvementThreshold;

    return {
      shouldDeploy,
      metrics: {
        currentModelId: currentModel.id,
        currentModelVersion: currentModel.version,
        comparison,
        improvementThreshold,
      },
    };
  }

  /**
   * Compare two model versions
   * @param newModel New model version
   * @param currentModel Current model version
   * @returns Comparison result
   */
  private compareModels(newModel: any, currentModel: any): { percentImprovement: number; metrics: Record<string, any> } {
    const newMetrics = newModel.performance.validationMetrics;
    const currentMetrics = currentModel.performance.validationMetrics;

    // Calculate improvement for each metric
    const metricComparisons: Record<string, any> = {};
    let totalImprovement = 0;
    let metricCount = 0;

    for (const [metric, value] of Object.entries(newMetrics)) {
      if (currentMetrics[metric] !== undefined) {
        const improvement = value - currentMetrics[metric];
        const percentImprovement = currentMetrics[metric] !== 0
          ? (improvement / currentMetrics[metric]) * 100
          : 0;

        metricComparisons[metric] = {
          newValue: value,
          currentValue: currentMetrics[metric],
          improvement,
          percentImprovement,
        };

        totalImprovement += percentImprovement;
        metricCount++;
      }
    }

    // Calculate average improvement
    const percentImprovement = metricCount > 0 ? totalImprovement / metricCount : 0;

    return {
      percentImprovement,
      metrics: metricComparisons,
    };
  }

  /**
   * Deploy a model to production
   * @param modelVersion Model version to deploy
   * @returns Deployment result
   */
  private async deployModel(modelVersion: ModelVersion): Promise<any> {
    this.logger.log(`Deploying model ${modelVersion.version}`);

    try {
      // Get deployment configuration
      const deploymentConfig: ModelDeploymentConfig = {
        modelId: modelVersion.id,
        deploymentStrategy: this.configService.get<'immediate' | 'gradual' | 'a_b_test'>('MODEL_DEPLOYMENT_STRATEGY', 'immediate'),
        trafficPercentage: this.configService.get<number>('MODEL_DEPLOYMENT_TRAFFIC_PERCENTAGE', 100),
        monitoringPeriod: this.configService.get<number>('MODEL_DEPLOYMENT_MONITORING_PERIOD', 24),
      };

      // Update the current production model(s) to archived
      if (deploymentConfig.deploymentStrategy === 'immediate') {
        await this.prisma.modelVersion.updateMany({
          where: {
            modelType: modelVersion.modelType,
            status: 'deployed',
          },
          data: {
            status: 'archived',
          },
        });
      }

      // Update the new model to deployed
      await this.prisma.modelVersion.update({
        where: {
          id: modelVersion.id,
        },
        data: {
          status: 'deployed',
        },
      });

      // Emit model deployed event
      this.emitPipelineEvent('model_deployed', {
        modelId: modelVersion.id,
        modelVersion: modelVersion.version,
        modelType: modelVersion.modelType,
        deploymentConfig,
        timestamp: new Date(),
      });

      return {
        success: true,
        modelId: modelVersion.id,
        modelVersion: modelVersion.version,
        deploymentConfig,
      };
    } catch (error) {
      this.logger.error(`Error deploying model: ${error.message}`);
      throw error;
    }
  }

  /**
   * Save pipeline execution record to database
   * @param pipelineStatus Pipeline execution status
   */
  private async savePipelineExecution(pipelineStatus: PipelineExecutionStatus): Promise<void> {
    try {
      await this.prisma.pipelineExecution.create({
        data: {
          id: pipelineStatus.id,
          startTime: pipelineStatus.startTime,
          endTime: pipelineStatus.endTime,
          status: pipelineStatus.status,
          steps: pipelineStatus.steps,
          error: pipelineStatus.error,
          result: pipelineStatus.result,
        },
      });
    } catch (error) {
      this.logger.error(`Error saving pipeline execution: ${error.message}`);
    }
  }

  /**
   * Emit a pipeline event
   * @param eventType Type of event
   * @param data Event data
   */
  private emitPipelineEvent(
    eventType: 'pipeline_started' | 'pipeline_completed' | 'pipeline_failed' | 'model_training_started' | 'model_training_completed' | 'model_deployed' | 'model_rollback',
    data: Record<string, any>,
  ): void {
    const event: LearningPipelineEvent = {
      id: uuidv4(),
      eventType,
      timestamp: new Date(),
      data,
    };

    this.eventEmitter.emit(`learning.pipeline.${eventType}`, event);
  }
}