import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationOptions } from '../interfaces/recommendation-options.interface';

@Injectable()
export class CollaborativeFilteringService {
  private readonly logger = new Logger(CollaborativeFilteringService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Génère des recommandations par filtrage collaboratif pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Liste des recommandations
   */
  async getRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ) {
    try {
      this.logger.log(`Génération de recommandations par filtrage collaboratif de type ${type} pour l'utilisateur ${userId}`);
      
      // Récupérer les utilisateurs similaires
      const similarUsers = await this.getSimilarUsers(userId);
      
      if (similarUsers.length === 0) {
        this.logger.log(`Aucun utilisateur similaire trouvé pour l'utilisateur ${userId}`);
        return [];
      }
      
      // Générer les recommandations selon le type
      let recommendations;
      switch (type) {
        case RecommendationType.COURSE:
          recommendations = await this.getRecommendedCourses(userId, similarUsers, options);
          break;
        case RecommendationType.RETREAT:
          recommendations = await this.getRecommendedRetreats(userId, similarUsers, options);
          break;
        case RecommendationType.PARTNER:
          recommendations = await this.getRecommendedPartners(userId, similarUsers, options);
          break;
        default:
          throw new Error(`Type de recommandation non pris en charge: ${type}`);
      }
      
      this.logger.log(`${recommendations.length} recommandations par filtrage collaboratif générées pour l'utilisateur ${userId}`);
      return recommendations;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des recommandations par filtrage collaboratif: ${error.message}`);
      throw error;
    }
  }

  /**
   * Met à jour le modèle de recommandation pour un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Résultat de la mise à jour
   */
  async updateUserModel(userId: string) {
    try {
      this.logger.log(`Mise à jour du modèle de recommandation par filtrage collaboratif pour l'utilisateur ${userId}`);
      
      // Dans une implémentation réelle, on mettrait à jour la matrice de similarité des utilisateurs
      // ou on recalculerait les vecteurs d'utilisateurs pour les algorithmes de factorisation matricielle
      
      this.logger.log(`Modèle de recommandation par filtrage collaboratif mis à jour pour l'utilisateur ${userId}`);
      return { success: true };
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du modèle de recommandation: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * Récupère les utilisateurs similaires à un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Liste des utilisateurs similaires avec leur score de similarité
   */
  private async getSimilarUsers(userId: string) {
    // Récupérer les interactions de l'utilisateur
    const userInteractions = await this.prisma.userInteraction.findMany({
      where: { userId },
      select: {
        itemId: true,
        itemType: true,
        interactionType: true,
      },
    });
    
    if (userInteractions.length === 0) {
      return [];
    }
    
    // Récupérer les IDs des éléments avec lesquels l'utilisateur a interagi
    const userItemIds = userInteractions.map(interaction => interaction.itemId);
    
    // Récupérer les utilisateurs qui ont interagi avec les mêmes éléments
    const otherUsersInteractions = await this.prisma.userInteraction.findMany({
      where: {
        userId: { not: userId },
        itemId: { in: userItemIds },
      },
      select: {
        userId: true,
        itemId: true,
        itemType: true,
        interactionType: true,
      },
    });
    
    // Regrouper les interactions par utilisateur
    const userInteractionsMap = new Map();
    userInteractions.forEach(interaction => {
      userInteractionsMap.set(interaction.itemId, interaction);
    });
    
    const otherUsersInteractionsMap = new Map();
    otherUsersInteractions.forEach(interaction => {
      if (!otherUsersInteractionsMap.has(interaction.userId)) {
        otherUsersInteractionsMap.set(interaction.userId, []);
      }
      otherUsersInteractionsMap.get(interaction.userId).push(interaction);
    });
    
    // Calculer la similarité entre l'utilisateur et les autres utilisateurs
    const similarUsers = [];
    
    otherUsersInteractionsMap.forEach((interactions, otherUserId) => {
      // Calculer le score de similarité
      let similarity = 0;
      let commonItems = 0;
      
      interactions.forEach(interaction => {
        if (userInteractionsMap.has(interaction.itemId)) {
          commonItems++;
          
          // Vérifier si les types d'interaction sont similaires
          const userInteraction = userInteractionsMap.get(interaction.itemId);
          if (userInteraction.interactionType === interaction.interactionType) {
            similarity += 1;
          } else {
            // Donner un score partiel pour des types d'interaction différents
            similarity += 0.5;
          }
        }
      });
      
      // Normaliser le score de similarité
      const normalizedSimilarity = commonItems > 0 ? similarity / commonItems : 0;
      
      if (normalizedSimilarity > 0) {
        similarUsers.push({
          userId: otherUserId,
          similarity: normalizedSimilarity,
          commonItems,
        });
      }
    });
    
    // Trier les utilisateurs par similarité
    return similarUsers.sort((a, b) => b.similarity - a.similarity);
  }

  /**
   * Récupère les cours recommandés pour un utilisateur basé sur des utilisateurs similaires
   * @param userId ID de l'utilisateur
   * @param similarUsers Liste des utilisateurs similaires
   * @param options Options de recommandation
   * @returns Liste des cours recommandés
   */
  private async getRecommendedCourses(
    userId: string,
    similarUsers: any[],
    options: RecommendationOptions = {},
  ) {
    const limit = options.limit || 10;
    
    // Récupérer les cours déjà suivis par l'utilisateur
    const userEnrollments = await this.prisma.enrollment.findMany({
      where: { userId },
      select: { courseId: true },
    });
    
    const enrolledCourseIds = userEnrollments.map(enrollment => enrollment.courseId);
    
    // Récupérer les cours suivis par les utilisateurs similaires
    const similarUserIds = similarUsers.map(user => user.userId);
    
    const similarUsersEnrollments = await this.prisma.enrollment.findMany({
      where: {
        userId: { in: similarUserIds },
        courseId: { notIn: enrolledCourseIds },
      },
      include: {
        course: true,
      },
    });
    
    // Calculer le score de chaque cours
    const courseScores = new Map();
    
    similarUsersEnrollments.forEach(enrollment => {
      const courseId = enrollment.courseId;
      const similarUser = similarUsers.find(user => user.userId === enrollment.userId);
      
      if (!courseScores.has(courseId)) {
        courseScores.set(courseId, {
          course: enrollment.course,
          score: 0,
          users: [],
        });
      }
      
      const courseScore = courseScores.get(courseId);
      courseScore.score += similarUser.similarity;
      courseScore.users.push(similarUser.userId);
    });
    
    // Transformer les scores en recommandations
    const recommendations = Array.from(courseScores.values()).map(({ course, score, users }) => ({
      id: course.id,
      type: RecommendationType.COURSE,
      title: course.title,
      description: course.description,
      score,
      metadata: {
        category: course.category,
        level: course.level,
        tags: course.tags,
        similarUsers: users,
      },
    }));
    
    // Trier les recommandations par score et limiter le nombre de résultats
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * Récupère les retraites recommandées pour un utilisateur basé sur des utilisateurs similaires
   * @param userId ID de l'utilisateur
   * @param similarUsers Liste des utilisateurs similaires
   * @param options Options de recommandation
   * @returns Liste des retraites recommandées
   */
  private async getRecommendedRetreats(
    userId: string,
    similarUsers: any[],
    options: RecommendationOptions = {},
  ) {
    const limit = options.limit || 10;
    
    // Simuler la récupération des retraites suivies par les utilisateurs similaires
    // Dans une implémentation réelle, on utiliserait une requête à la base de données
    
    // Récupérer les interactions de l'utilisateur avec des retraites
    const userInteractions = await this.prisma.userInteraction.findMany({
      where: {
        userId,
        itemType: RecommendationType.RETREAT,
      },
      select: {
        itemId: true,
      },
    });
    
    const userRetreatIds = userInteractions.map(interaction => interaction.itemId);
    
    // Simuler les retraites
    const retreats = Array.from({ length: 20 }, (_, i) => ({
      id: `retreat-${i + 1}`,
      title: `Retraite ${i + 1}`,
      description: `Description de la retraite ${i + 1}`,
      category: ['Yoga', 'Meditation', 'Wellness', 'Fitness', 'Nutrition'][i % 5],
      location: ['Bali', 'Thailand', 'France', 'Spain', 'Portugal', 'Italy', 'Greece', 'Morocco'][i % 8],
      duration: [3, 5, 7, 10, 14][i % 5],
      tags: [
        ['yoga', 'meditation', 'wellness'],
        ['fitness', 'nutrition', 'wellness'],
        ['meditation', 'nature', 'silence'],
        ['yoga', 'beach', 'relaxation'],
        ['nutrition', 'detox', 'health'],
      ][i % 5],
    })).filter(retreat => !userRetreatIds.includes(retreat.id));
    
    // Simuler les interactions des utilisateurs similaires avec des retraites
    const similarUsersInteractions = [];
    
    similarUsers.forEach(similarUser => {
      // Chaque utilisateur similaire a interagi avec 1 à 3 retraites
      const numInteractions = Math.floor(Math.random() * 3) + 1;
      
      for (let i = 0; i < numInteractions; i++) {
        const retreatIndex = Math.floor(Math.random() * retreats.length);
        
        similarUsersInteractions.push({
          userId: similarUser.userId,
          retreat: retreats[retreatIndex],
          similarity: similarUser.similarity,
        });
      }
    });
    
    // Calculer le score de chaque retraite
    const retreatScores = new Map();
    
    similarUsersInteractions.forEach(interaction => {
      const retreatId = interaction.retreat.id;
      
      if (!retreatScores.has(retreatId)) {
        retreatScores.set(retreatId, {
          retreat: interaction.retreat,
          score: 0,
          users: [],
        });
      }
      
      const retreatScore = retreatScores.get(retreatId);
      retreatScore.score += interaction.similarity;
      retreatScore.users.push(interaction.userId);
    });
    
    // Transformer les scores en recommandations
    const recommendations = Array.from(retreatScores.values()).map(({ retreat, score, users }) => ({
      id: retreat.id,
      type: RecommendationType.RETREAT,
      title: retreat.title,
      description: retreat.description,
      score,
      metadata: {
        category: retreat.category,
        location: retreat.location,
        duration: retreat.duration,
        tags: retreat.tags,
        similarUsers: users,
      },
    }));
    
    // Trier les recommandations par score et limiter le nombre de résultats
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * Récupère les partenaires recommandés pour un utilisateur basé sur des utilisateurs similaires
   * @param userId ID de l'utilisateur
   * @param similarUsers Liste des utilisateurs similaires
   * @param options Options de recommandation
   * @returns Liste des partenaires recommandés
   */
  private async getRecommendedPartners(
    userId: string,
    similarUsers: any[],
    options: RecommendationOptions = {},
  ) {
    const limit = options.limit || 10;
    
    // Simuler la récupération des partenaires suivis par les utilisateurs similaires
    // Dans une implémentation réelle, on utiliserait une requête à la base de données
    
    // Récupérer les interactions de l'utilisateur avec des partenaires
    const userInteractions = await this.prisma.userInteraction.findMany({
      where: {
        userId,
        itemType: RecommendationType.PARTNER,
      },
      select: {
        itemId: true,
      },
    });
    
    const userPartnerIds = userInteractions.map(interaction => interaction.itemId);
    
    // Simuler les partenaires
    const partners = Array.from({ length: 15 }, (_, i) => ({
      id: `partner-${i + 1}`,
      title: `Partenaire ${i + 1}`,
      description: `Description du partenaire ${i + 1}`,
      specialty: ['Yoga', 'Meditation', 'Wellness', 'Fitness', 'Nutrition'][i % 5],
      location: ['Paris', 'London', 'Berlin', 'Madrid', 'Rome', 'Amsterdam', 'Brussels'][i % 7],
      tags: [
        ['yoga', 'meditation', 'wellness'],
        ['fitness', 'nutrition', 'wellness'],
        ['meditation', 'coaching', 'mindfulness'],
        ['yoga', 'pilates', 'stretching'],
        ['nutrition', 'detox', 'health'],
      ][i % 5],
      rating: (Math.random() * 2 + 3).toFixed(1),
    })).filter(partner => !userPartnerIds.includes(partner.id));
    
    // Simuler les interactions des utilisateurs similaires avec des partenaires
    const similarUsersInteractions = [];
    
    similarUsers.forEach(similarUser => {
      // Chaque utilisateur similaire a interagi avec 1 à 3 partenaires
      const numInteractions = Math.floor(Math.random() * 3) + 1;
      
      for (let i = 0; i < numInteractions; i++) {
        const partnerIndex = Math.floor(Math.random() * partners.length);
        
        similarUsersInteractions.push({
          userId: similarUser.userId,
          partner: partners[partnerIndex],
          similarity: similarUser.similarity,
        });
      }
    });
    
    // Calculer le score de chaque partenaire
    const partnerScores = new Map();
    
    similarUsersInteractions.forEach(interaction => {
      const partnerId = interaction.partner.id;
      
      if (!partnerScores.has(partnerId)) {
        partnerScores.set(partnerId, {
          partner: interaction.partner,
          score: 0,
          users: [],
        });
      }
      
      const partnerScore = partnerScores.get(partnerId);
      partnerScore.score += interaction.similarity;
      partnerScore.users.push(interaction.userId);
    });
    
    // Transformer les scores en recommandations
    const recommendations = Array.from(partnerScores.values()).map(({ partner, score, users }) => ({
      id: partner.id,
      type: RecommendationType.PARTNER,
      title: partner.title,
      description: partner.description,
      score,
      metadata: {
        specialty: partner.specialty,
        location: partner.location,
        tags: partner.tags,
        rating: partner.rating,
        similarUsers: users,
      },
    }));
    
    // Trier les recommandations par score et limiter le nombre de résultats
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }
}
