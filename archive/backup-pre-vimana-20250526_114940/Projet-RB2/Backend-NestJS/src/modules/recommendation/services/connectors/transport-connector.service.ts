import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ExternalData, ExternalDataType } from '../../interfaces/external-data.interface';
import { RecommendationType } from '../../enums/recommendation-type.enum';

/**
 * Service de connexion aux APIs de transport
 * Récupère les données de transport pour enrichir les recommandations
 */
@Injectable()
export class TransportConnectorService {
  private readonly logger = new Logger(TransportConnectorService.name);
  private readonly googleMapsApiKey: string;
  private readonly sncfApiKey: string;
  private readonly enabled: boolean;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.googleMapsApiKey = this.configService.get<string>('recommendation.externalData.apiKeys.googleMaps', '');
    this.sncfApiKey = this.configService.get<string>('recommendation.externalData.apiKeys.sncf', '');
    this.enabled = this.configService.get<boolean>('recommendation.externalData.sources.transport.enabled', false);
    
    this.logger.log(`TransportConnectorService initialized with enabled=${this.enabled}`);
  }

  /**
   * Récupère les options de transport entre deux points
   * @param origin Point de départ (adresse ou coordonnées)
   * @param destination Point d'arrivée (adresse ou coordonnées)
   * @param departureTime Heure de départ
   * @param transportModes Modes de transport à inclure
   * @returns Données de transport formatées
   */
  async getTransportOptions(
    origin: string | { lat: number; lng: number },
    destination: string | { lat: number; lng: number },
    departureTime?: Date,
    transportModes: string[] = ['driving', 'transit', 'walking']
  ): Promise<ExternalData[]> {
    if (!this.enabled || !this.googleMapsApiKey) {
      this.logger.warn('Transport connector is disabled or Google Maps API key is missing');
      return [];
    }

    try {
      const results: ExternalData[] = [];

      // Récupérer les options pour chaque mode de transport
      for (const mode of transportModes) {
        const transportData = await this.getGoogleMapsDirections(origin, destination, mode, departureTime);
        if (transportData) {
          results.push(transportData);
        }
      }

      return results;
    } catch (error) {
      this.logger.error(`Error fetching transport options: ${error.message}`);
      return [];
    }
  }

  /**
   * Récupère les horaires de train entre deux gares
   * @param originStation Gare de départ
   * @param destinationStation Gare d'arrivée
   * @param date Date du voyage
   * @returns Données d'horaires de train formatées
   */
  async getTrainSchedules(
    originStation: string,
    destinationStation: string,
    date?: Date
  ): Promise<ExternalData[]> {
    if (!this.enabled || !this.sncfApiKey) {
      this.logger.warn('Transport connector is disabled or SNCF API key is missing');
      return [];
    }

    try {
      // Construire les paramètres de la requête
      const params: Record<string, any> = {
        from: originStation,
        to: destinationStation,
      };

      // Ajouter la date si fournie
      if (date) {
        params.datetime = date.toISOString();
      }

      // Effectuer la requête
      const response = await firstValueFrom(
        this.httpService.get('https://api.sncf.com/v1/coverage/sncf/journeys', {
          params,
          headers: {
            Authorization: `Basic ${Buffer.from(this.sncfApiKey + ':').toString('base64')}`,
          },
        })
      );

      // Vérifier la réponse
      if (response.status !== 200) {
        throw new Error(`SNCF API returned status ${response.status}`);
      }

      // Transformer les données
      return this.transformTrainData(response.data, originStation, destinationStation);
    } catch (error) {
      this.logger.error(`Error fetching train schedules: ${error.message}`);
      return [];
    }
  }

  /**
   * Récupère les itinéraires via Google Maps Directions API
   * @param origin Point de départ
   * @param destination Point d'arrivée
   * @param mode Mode de transport
   * @param departureTime Heure de départ
   * @returns Données de transport formatées
   */
  private async getGoogleMapsDirections(
    origin: string | { lat: number; lng: number },
    destination: string | { lat: number; lng: number },
    mode: string,
    departureTime?: Date
  ): Promise<ExternalData | null> {
    try {
      // Formater l'origine et la destination
      const originStr = typeof origin === 'string' ? origin : `${origin.lat},${origin.lng}`;
      const destinationStr = typeof destination === 'string' ? destination : `${destination.lat},${destination.lng}`;

      // Construire les paramètres de la requête
      const params: Record<string, any> = {
        origin: originStr,
        destination: destinationStr,
        mode,
        key: this.googleMapsApiKey,
      };

      // Ajouter l'heure de départ si fournie
      if (departureTime) {
        params.departure_time = Math.floor(departureTime.getTime() / 1000);
      }

      // Effectuer la requête
      const response = await firstValueFrom(
        this.httpService.get('https://maps.googleapis.com/maps/api/directions/json', { params })
      );

      // Vérifier la réponse
      if (response.status !== 200 || response.data.status !== 'OK') {
        throw new Error(`Google Maps API returned status ${response.data.status}`);
      }

      // Transformer les données
      return this.transformGoogleMapsData(response.data, mode);
    } catch (error) {
      this.logger.error(`Error fetching Google Maps directions: ${error.message}`);
      return null;
    }
  }

  /**
   * Transforme les données Google Maps en format standardisé
   * @param data Données brutes de l'API
   * @param mode Mode de transport
   * @returns Données de transport formatées
   */
  private transformGoogleMapsData(data: any, mode: string): ExternalData {
    try {
      const route = data.routes[0];
      const leg = route.legs[0];
      
      // Extraire les informations principales
      const distance = leg.distance.text;
      const duration = leg.duration.text;
      const startAddress = leg.start_address;
      const endAddress = leg.end_address;
      
      // Créer le contenu
      const content = `Distance: ${distance}, Durée: ${duration}`;
      
      // Déterminer le titre en fonction du mode
      let title: string;
      switch (mode) {
        case 'driving':
          title = `Trajet en voiture de ${startAddress} à ${endAddress}`;
          break;
        case 'transit':
          title = `Trajet en transports en commun de ${startAddress} à ${endAddress}`;
          break;
        case 'walking':
          title = `Trajet à pied de ${startAddress} à ${endAddress}`;
          break;
        case 'bicycling':
          title = `Trajet à vélo de ${startAddress} à ${endAddress}`;
          break;
        default:
          title = `Trajet de ${startAddress} à ${endAddress}`;
      }
      
      // Créer l'objet de données externes
      return {
        type: ExternalDataType.TRANSPORT,
        source: 'Google Maps',
        title,
        content,
        url: `https://www.google.com/maps/dir/?api=1&origin=${encodeURIComponent(startAddress)}&destination=${encodeURIComponent(endAddress)}&travelmode=${mode}`,
        relevanceScore: 0.85,
        metadata: {
          mode,
          distance: {
            text: leg.distance.text,
            value: leg.distance.value, // en mètres
          },
          duration: {
            text: leg.duration.text,
            value: leg.duration.value, // en secondes
          },
          startLocation: leg.start_location,
          endLocation: leg.end_location,
          startAddress,
          endAddress,
          steps: leg.steps.map(step => ({
            distance: step.distance.text,
            duration: step.duration.text,
            instructions: step.html_instructions,
            travelMode: step.travel_mode,
            ...(step.transit_details ? {
              transitDetails: {
                arrivalStop: step.transit_details.arrival_stop.name,
                departureStop: step.transit_details.departure_stop.name,
                line: step.transit_details.line.name,
                vehicle: step.transit_details.line.vehicle.type,
              },
            } : {}),
          })),
          polyline: route.overview_polyline.points,
        },
        applicableTypes: [
          RecommendationType.RETREAT,
          RecommendationType.ACTIVITY,
          RecommendationType.EVENT,
        ],
      };
    } catch (error) {
      this.logger.error(`Error transforming Google Maps data: ${error.message}`);
      throw error;
    }
  }

  /**
   * Transforme les données SNCF en format standardisé
   * @param data Données brutes de l'API
   * @param originStation Gare de départ
   * @param destinationStation Gare d'arrivée
   * @returns Données de transport formatées
   */
  private transformTrainData(data: any, originStation: string, destinationStation: string): ExternalData[] {
    try {
      const journeys = data.journeys || [];
      
      return journeys.map(journey => {
        // Extraire les informations principales
        const departureDate = new Date(journey.departure_date_time);
        const arrivalDate = new Date(journey.arrival_date_time);
        const duration = journey.duration; // en secondes
        
        // Formater la durée
        const durationHours = Math.floor(duration / 3600);
        const durationMinutes = Math.floor((duration % 3600) / 60);
        const durationFormatted = `${durationHours}h${durationMinutes.toString().padStart(2, '0')}`;
        
        // Extraire les sections
        const sections = journey.sections || [];
        const trainSections = sections.filter(section => section.type === 'public_transport');
        
        // Créer le contenu
        const content = `Départ: ${departureDate.toLocaleTimeString()}, Arrivée: ${arrivalDate.toLocaleTimeString()}, Durée: ${durationFormatted}`;
        
        // Créer l'objet de données externes
        return {
          type: ExternalDataType.TRANSPORT,
          source: 'SNCF',
          title: `Train de ${originStation} à ${destinationStation}`,
          content,
          relevanceScore: 0.8,
          metadata: {
            departureDate,
            arrivalDate,
            duration,
            durationFormatted,
            transfers: journey.nb_transfers,
            co2Emission: journey.co2_emission?.value,
            trains: trainSections.map(section => ({
              type: section.display_informations?.commercial_mode || 'Train',
              number: section.display_informations?.headsign,
              departureStop: section.from?.name,
              arrivalStop: section.to?.name,
              departureTime: new Date(section.departure_date_time),
              arrivalTime: new Date(section.arrival_date_time),
            })),
          },
          applicableTypes: [
            RecommendationType.RETREAT,
            RecommendationType.ACTIVITY,
            RecommendationType.EVENT,
          ],
        };
      });
    } catch (error) {
      this.logger.error(`Error transforming SNCF data: ${error.message}`);
      return [];
    }
  }
}
