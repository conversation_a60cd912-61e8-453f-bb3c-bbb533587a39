import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  DiversityConfig,
  DiversityDimension,
  DiversityStrategy,
  FairnessConfig,
  ProtectedAttribute,
  FairnessMetric,
  FairnessConstraint,
  FairnessStrategy,
  DiversityFairnessMetrics,
} from '../interfaces/diversity-fairness.interface';

/**
 * Service for managing diversity and fairness in recommendations
 */
@Injectable()
export class DiversityFairnessService {
  private readonly logger = new Logger(DiversityFairnessService.name);
  private diversityConfig: DiversityConfig;
  private fairnessConfig: FairnessConfig;

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    // Initialize configurations
    this.initializeConfigurations();
  }

  /**
   * Initialize diversity and fairness configurations
   */
  private initializeConfigurations(): void {
    try {
      // Initialize diversity configuration
      this.diversityConfig = {
        enabled: this.configService.get<boolean>('DIVERSITY_ENABLED', true),
        dimensions: this.getDefaultDiversityDimensions(),
        minDiversityScore: this.configService.get<number>('MIN_DIVERSITY_SCORE', 0.3),
        maxItemSimilarity: this.configService.get<number>('MAX_ITEM_SIMILARITY', 0.8),
        diversityWeight: this.configService.get<number>('DIVERSITY_WEIGHT', 0.3),
        strategy: this.configService.get<DiversityStrategy>('DIVERSITY_STRATEGY', DiversityStrategy.BALANCE),
        strategyParams: {
          tradeoffFactor: this.configService.get<number>('DIVERSITY_TRADEOFF_FACTOR', 0.5),
        },
      };

      // Initialize fairness configuration
      this.fairnessConfig = {
        enabled: this.configService.get<boolean>('FAIRNESS_ENABLED', true),
        protectedAttributes: this.getDefaultProtectedAttributes(),
        metrics: this.getDefaultFairnessMetrics(),
        constraints: this.getDefaultFairnessConstraints(),
        strategy: this.configService.get<FairnessStrategy>('FAIRNESS_STRATEGY', FairnessStrategy.POST_PROCESSING),
        strategyParams: {
          maxIterations: this.configService.get<number>('FAIRNESS_MAX_ITERATIONS', 10),
          convergenceThreshold: this.configService.get<number>('FAIRNESS_CONVERGENCE_THRESHOLD', 0.01),
        },
      };

      this.logger.log('Diversity and fairness configurations initialized');
    } catch (error) {
      this.logger.error(`Error initializing configurations: ${error.message}`);
    }
  }

  /**
   * Get default diversity dimensions
   * @returns Default diversity dimensions
   */
  private getDefaultDiversityDimensions(): DiversityDimension[] {
    return [
      {
        name: 'category',
        weight: 0.4,
        field: 'categories',
        type: 'categorical',
        categories: ['wellness', 'meditation', 'yoga', 'fitness', 'nutrition', 'mindfulness', 'spirituality'],
      },
      {
        name: 'tags',
        weight: 0.3,
        field: 'tags',
        type: 'tags',
      },
      {
        name: 'location',
        weight: 0.2,
        field: 'location',
        type: 'categorical',
        categories: ['urban', 'rural', 'mountain', 'beach', 'forest', 'desert'],
      },
      {
        name: 'price',
        weight: 0.1,
        field: 'price',
        type: 'numerical',
        range: {
          min: 0,
          max: 10000,
        },
      },
    ];
  }

  /**
   * Get default protected attributes
   * @returns Default protected attributes
   */
  private getDefaultProtectedAttributes(): ProtectedAttribute[] {
    return [
      {
        name: 'gender',
        field: 'gender',
        type: 'categorical',
        categories: ['male', 'female', 'non-binary', 'other'],
        appliesTo: 'user',
      },
      {
        name: 'age',
        field: 'age',
        type: 'numerical',
        range: {
          min: 18,
          max: 100,
        },
        appliesTo: 'user',
      },
      {
        name: 'location',
        field: 'location',
        type: 'categorical',
        categories: ['urban', 'rural', 'suburban'],
        appliesTo: 'user',
      },
      {
        name: 'price_tier',
        field: 'price_tier',
        type: 'categorical',
        categories: ['budget', 'mid-range', 'premium', 'luxury'],
        appliesTo: 'item',
      },
    ];
  }

  /**
   * Get default fairness metrics
   * @returns Default fairness metrics
   */
  private getDefaultFairnessMetrics(): FairnessMetric[] {
    return [
      FairnessMetric.STATISTICAL_PARITY,
      FairnessMetric.DISPARATE_IMPACT,
    ];
  }

  /**
   * Get default fairness constraints
   * @returns Default fairness constraints
   */
  private getDefaultFairnessConstraints(): FairnessConstraint[] {
    return [
      {
        metric: FairnessMetric.DISPARATE_IMPACT,
        attribute: 'gender',
        threshold: 0.8,
        direction: 'above',
        weight: 1.0,
      },
      {
        metric: FairnessMetric.STATISTICAL_PARITY,
        attribute: 'location',
        threshold: 0.1,
        direction: 'below',
        weight: 0.8,
      },
    ];
  }

  /**
   * Get the current diversity configuration
   * @returns Diversity configuration
   */
  getDiversityConfig(): DiversityConfig {
    return this.diversityConfig;
  }

  /**
   * Update the diversity configuration
   * @param config New diversity configuration
   * @returns Updated diversity configuration
   */
  updateDiversityConfig(config: Partial<DiversityConfig>): DiversityConfig {
    this.diversityConfig = {
      ...this.diversityConfig,
      ...config,
    };

    this.logger.log('Diversity configuration updated');
    this.eventEmitter.emit('diversity.config.updated', this.diversityConfig);

    return this.diversityConfig;
  }

  /**
   * Get the current fairness configuration
   * @returns Fairness configuration
   */
  getFairnessConfig(): FairnessConfig {
    return this.fairnessConfig;
  }

  /**
   * Update the fairness configuration
   * @param config New fairness configuration
   * @returns Updated fairness configuration
   */
  updateFairnessConfig(config: Partial<FairnessConfig>): FairnessConfig {
    this.fairnessConfig = {
      ...this.fairnessConfig,
      ...config,
    };

    this.logger.log('Fairness configuration updated');
    this.eventEmitter.emit('fairness.config.updated', this.fairnessConfig);

    return this.fairnessConfig;
  }

  /**
   * Enhance diversity in recommendations
   * @param items Original recommended items
   * @param userId User ID
   * @param context Recommendation context
   * @returns Diversified recommendations
   */
  async enhanceDiversity(
    items: any[],
    userId: string,
    context?: Record<string, any>,
  ): Promise<any[]> {
    if (!this.diversityConfig.enabled || items.length <= 1) {
      return items;
    }

    try {
      this.logger.debug(`Enhancing diversity for ${items.length} items for user ${userId}`);

      // Calculate item similarities
      const similarityMatrix = await this.calculateItemSimilarities(items);

      // Apply diversity strategy
      let diversifiedItems: any[];
      switch (this.diversityConfig.strategy) {
        case DiversityStrategy.MAXIMIZE_DIVERSITY:
          diversifiedItems = this.maximizeDiversity(items, similarityMatrix);
          break;
        case DiversityStrategy.BALANCE:
          diversifiedItems = this.balanceDiversity(items, similarityMatrix);
          break;
        case DiversityStrategy.GREEDY:
          diversifiedItems = this.greedyDiversity(items, similarityMatrix);
          break;
        case DiversityStrategy.CLUSTERING:
          diversifiedItems = await this.clusteringDiversity(items);
          break;
        default:
          diversifiedItems = items;
      }

      // Calculate diversity metrics
      const metrics = this.calculateDiversityMetrics(diversifiedItems);

      // Emit event with metrics
      this.eventEmitter.emit('diversity.enhanced', {
        userId,
        originalCount: items.length,
        diversifiedCount: diversifiedItems.length,
        metrics,
      });

      return diversifiedItems;
    } catch (error) {
      this.logger.error(`Error enhancing diversity: ${error.message}`);
      return items; // Return original items in case of error
    }
  }

  /**
   * Calculate similarity matrix between items
   * @param items Items to calculate similarities for
   * @returns Similarity matrix
   */
  private async calculateItemSimilarities(items: any[]): Promise<number[][]> {
    const n = items.length;
    const matrix: number[][] = Array(n).fill(0).map(() => Array(n).fill(0));

    // Calculate similarity for each pair of items
    for (let i = 0; i < n; i++) {
      // Diagonal is always 1 (item is identical to itself)
      matrix[i][i] = 1;

      for (let j = i + 1; j < n; j++) {
        // Calculate similarity based on dimensions
        const similarity = this.calculateItemPairSimilarity(items[i], items[j]);

        // Similarity is symmetric
        matrix[i][j] = similarity;
        matrix[j][i] = similarity;
      }
    }

    return matrix;
  }

  /**
   * Calculate similarity between two items
   * @param item1 First item
   * @param item2 Second item
   * @returns Similarity score (0-1)
   */
  private calculateItemPairSimilarity(item1: any, item2: any): number {
    let totalSimilarity = 0;
    let totalWeight = 0;

    // Calculate similarity for each dimension
    for (const dimension of this.diversityConfig.dimensions) {
      const weight = dimension.weight;
      totalWeight += weight;

      // Get values for this dimension
      const value1 = item1[dimension.field];
      const value2 = item2[dimension.field];

      // Skip if either value is missing
      if (value1 === undefined || value2 === undefined) {
        continue;
      }

      // Calculate similarity based on dimension type
      let similarity: number;
      switch (dimension.type) {
        case 'categorical':
          similarity = value1 === value2 ? 1 : 0;
          break;
        case 'numerical':
          const range = dimension.range?.max - dimension.range?.min || 1;
          similarity = 1 - Math.abs(value1 - value2) / range;
          break;
        case 'tags':
          similarity = this.calculateTagSimilarity(value1, value2);
          break;
        case 'text':
          similarity = this.calculateTextSimilarity(value1, value2);
          break;
        default:
          similarity = 0;
      }

      totalSimilarity += similarity * weight;
    }

    return totalWeight > 0 ? totalSimilarity / totalWeight : 0;
  }

  /**
   * Calculate similarity between two tag sets
   * @param tags1 First tag set
   * @param tags2 Second tag set
   * @returns Similarity score (0-1)
   */
  private calculateTagSimilarity(tags1: string | string[], tags2: string | string[]): number {
    // Convert to arrays if needed
    const tagsArray1 = Array.isArray(tags1) ? tags1 : [tags1];
    const tagsArray2 = Array.isArray(tags2) ? tags2 : [tags2];

    // Handle empty arrays
    if (tagsArray1.length === 0 || tagsArray2.length === 0) {
      return 0;
    }

    // Calculate Jaccard similarity: intersection size / union size
    const set1 = new Set(tagsArray1);
    const set2 = new Set(tagsArray2);

    // Calculate intersection
    const intersection = new Set([...set1].filter(tag => set2.has(tag)));

    // Calculate union
    const union = new Set([...set1, ...set2]);

    return intersection.size / union.size;
  }

  /**
   * Calculate similarity between two text strings
   * @param text1 First text
   * @param text2 Second text
   * @returns Similarity score (0-1)
   */
  private calculateTextSimilarity(text1: string, text2: string): number {
    // Simple implementation using word overlap
    // In a real system, you might use more sophisticated methods like cosine similarity with TF-IDF

    // Normalize and tokenize texts
    const words1 = this.tokenizeText(text1);
    const words2 = this.tokenizeText(text2);

    // Handle empty arrays
    if (words1.length === 0 || words2.length === 0) {
      return 0;
    }

    // Calculate word overlap
    const set1 = new Set(words1);
    const set2 = new Set(words2);

    // Calculate intersection
    const intersection = new Set([...set1].filter(word => set2.has(word)));

    // Calculate union
    const union = new Set([...set1, ...set2]);

    return intersection.size / union.size;
  }

  /**
   * Tokenize text into words
   * @param text Text to tokenize
   * @returns Array of words
   */
  private tokenizeText(text: string): string[] {
    if (!text) return [];

    // Convert to lowercase and split by non-alphanumeric characters
    return text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 0);
  }

  /**
   * Maximize diversity using a greedy algorithm
   * @param items Original items
   * @param similarityMatrix Similarity matrix
   * @returns Diversified items
   */
  private maximizeDiversity(items: any[], similarityMatrix: number[][]): any[] {
    if (items.length <= 1) {
      return items;
    }

    const n = items.length;
    const selected: boolean[] = Array(n).fill(false);
    const result: any[] = [];

    // Start with the first item
    selected[0] = true;
    result.push(items[0]);

    // Greedily select the most diverse items
    while (result.length < n) {
      let bestIndex = -1;
      let minMaxSimilarity = Infinity;

      // For each unselected item
      for (let i = 0; i < n; i++) {
        if (selected[i]) continue;

        // Calculate maximum similarity to any selected item
        let maxSimilarity = -Infinity;
        for (let j = 0; j < n; j++) {
          if (!selected[j]) continue;
          maxSimilarity = Math.max(maxSimilarity, similarityMatrix[i][j]);
        }

        // Select the item with the minimum maximum similarity
        if (maxSimilarity < minMaxSimilarity) {
          minMaxSimilarity = maxSimilarity;
          bestIndex = i;
        }
      }

      if (bestIndex === -1) break;

      selected[bestIndex] = true;
      result.push(items[bestIndex]);
    }

    return result;
  }

  /**
   * Balance diversity and relevance
   * @param items Original items (assumed to be sorted by relevance)
   * @param similarityMatrix Similarity matrix
   * @returns Diversified items
   */
  private balanceDiversity(items: any[], similarityMatrix: number[][]): any[] {
    if (items.length <= 1) {
      return items;
    }

    const n = items.length;
    const selected: boolean[] = Array(n).fill(false);
    const result: any[] = [];

    // Get the tradeoff factor from config (0 = all relevance, 1 = all diversity)
    const tradeoffFactor = this.diversityConfig.strategyParams?.tradeoffFactor || 0.5;

    // Start with the first (most relevant) item
    selected[0] = true;
    result.push(items[0]);

    // Select remaining items
    while (result.length < n) {
      let bestIndex = -1;
      let bestScore = -Infinity;

      // For each unselected item
      for (let i = 0; i < n; i++) {
        if (selected[i]) continue;

        // Calculate diversity component: average dissimilarity to selected items
        let totalDissimilarity = 0;
        for (let j = 0; j < n; j++) {
          if (!selected[j]) continue;
          totalDissimilarity += (1 - similarityMatrix[i][j]);
        }
        const avgDissimilarity = totalDissimilarity / result.length;

        // Calculate relevance component: inverse of index (higher index = lower relevance)
        const relevance = 1 - (i / n);

        // Combine diversity and relevance with tradeoff factor
        const score = (tradeoffFactor * avgDissimilarity) + ((1 - tradeoffFactor) * relevance);

        if (score > bestScore) {
          bestScore = score;
          bestIndex = i;
        }
      }

      if (bestIndex === -1) break;

      selected[bestIndex] = true;
      result.push(items[bestIndex]);
    }

    return result;
  }

  /**
   * Greedy diversity algorithm
   * @param items Original items
   * @param similarityMatrix Similarity matrix
   * @returns Diversified items
   */
  private greedyDiversity(items: any[], similarityMatrix: number[][]): any[] {
    if (items.length <= 1) {
      return items;
    }

    const n = items.length;
    const result: any[] = [items[0]]; // Start with the first item
    const maxSimilarityThreshold = this.diversityConfig.maxItemSimilarity;

    // For each remaining item
    for (let i = 1; i < n; i++) {
      const item = items[i];

      // Check if this item is too similar to any already selected item
      let tooSimilar = false;
      for (let j = 0; j < result.length; j++) {
        const selectedIndex = items.indexOf(result[j]);
        if (similarityMatrix[i][selectedIndex] > maxSimilarityThreshold) {
          tooSimilar = true;
          break;
        }
      }

      // Add the item if it's not too similar to any selected item
      if (!tooSimilar) {
        result.push(item);
      }
    }

    return result;
  }

  /**
   * Clustering-based diversity
   * @param items Original items
   * @returns Diversified items
   */
  private async clusteringDiversity(items: any[]): Promise<any[]> {
    if (items.length <= 1) {
      return items;
    }

    // In a real implementation, this would use a clustering algorithm
    // For simplicity, we'll just return a subset of items

    // Get features for each item
    const features = items.map(item => this.extractItemFeatures(item));

    // Perform simple clustering (in a real system, use k-means or similar)
    const clusters = this.simpleCluster(features, Math.min(5, Math.ceil(items.length / 3)));

    // Select representative items from each cluster
    const result: any[] = [];
    for (const cluster of clusters) {
      if (cluster.length > 0) {
        // Select the first item from each cluster
        result.push(items[cluster[0]]);
      }
    }

    // Add remaining items in order of relevance until we have the original count
    let i = 0;
    while (result.length < items.length && i < items.length) {
      if (!result.includes(items[i])) {
        result.push(items[i]);
      }
      i++;
    }

    return result;
  }

  /**
   * Extract features from an item for clustering
   * @param item Item to extract features from
   * @returns Feature vector
   */
  private extractItemFeatures(item: any): number[] {
    const features: number[] = [];

    // Extract features based on dimensions
    for (const dimension of this.diversityConfig.dimensions) {
      const value = item[dimension.field];

      if (value === undefined) {
        // Use a default value if the field is missing
        features.push(0);
        continue;
      }

      switch (dimension.type) {
        case 'numerical':
          // Normalize numerical values to [0, 1]
          const min = dimension.range?.min || 0;
          const max = dimension.range?.max || 1;
          const normalizedValue = (value - min) / (max - min);
          features.push(normalizedValue);
          break;

        case 'categorical':
          // One-hot encode categorical values
          const categories = dimension.categories || [];
          const categoryIndex = categories.indexOf(value);
          if (categoryIndex >= 0) {
            for (let i = 0; i < categories.length; i++) {
              features.push(i === categoryIndex ? 1 : 0);
            }
          } else {
            // Unknown category
            for (let i = 0; i < categories.length; i++) {
              features.push(0);
            }
          }
          break;

        case 'tags':
          // For tags, we'll just use a single feature representing the number of tags
          const tags = Array.isArray(value) ? value : [value];
          features.push(tags.length / 10); // Normalize by assuming max 10 tags
          break;

        case 'text':
          // For text, we'll just use a single feature representing the length
          features.push(value.length / 1000); // Normalize by assuming max 1000 chars
          break;
      }
    }

    return features;
  }

  /**
   * Simple clustering algorithm
   * @param features Feature vectors
   * @param k Number of clusters
   * @returns Clusters (arrays of indices)
   */
  private simpleCluster(features: number[][], k: number): number[][] {
    if (features.length <= k) {
      // If we have fewer items than clusters, each item is its own cluster
      return features.map((_, i) => [i]);
    }

    // Initialize clusters with random centroids
    const centroids: number[][] = [];
    const usedIndices = new Set<number>();

    // Select k random items as initial centroids
    while (centroids.length < k) {
      const index = Math.floor(Math.random() * features.length);
      if (!usedIndices.has(index)) {
        usedIndices.add(index);
        centroids.push([...features[index]]);
      }
    }

    // Initialize clusters
    const clusters: number[][] = Array(k).fill(0).map(() => []);

    // Assign each item to the nearest centroid
    for (let i = 0; i < features.length; i++) {
      let minDistance = Infinity;
      let bestCluster = 0;

      for (let j = 0; j < k; j++) {
        const distance = this.euclideanDistance(features[i], centroids[j]);
        if (distance < minDistance) {
          minDistance = distance;
          bestCluster = j;
        }
      }

      clusters[bestCluster].push(i);
    }

    return clusters;
  }

  /**
   * Calculate Euclidean distance between two vectors
   * @param a First vector
   * @param b Second vector
   * @returns Euclidean distance
   */
  private euclideanDistance(a: number[], b: number[]): number {
    if (a.length !== b.length) {
      throw new Error('Vectors must have the same length');
    }

    let sum = 0;
    for (let i = 0; i < a.length; i++) {
      const diff = a[i] - b[i];
      sum += diff * diff;
    }

    return Math.sqrt(sum);
  }

  /**
   * Calculate diversity metrics for a set of items
   * @param items Items to calculate metrics for
   * @returns Diversity metrics
   */
  private calculateDiversityMetrics(items: any[]): any {
    if (items.length <= 1) {
      return {
        diversityScore: 1,
        dimensionScores: {},
      };
    }

    // Calculate similarity matrix
    const similarityMatrix = this.calculateItemSimilaritiesSync(items);

    // Calculate average pairwise similarity
    let totalSimilarity = 0;
    let pairCount = 0;

    for (let i = 0; i < items.length; i++) {
      for (let j = i + 1; j < items.length; j++) {
        totalSimilarity += similarityMatrix[i][j];
        pairCount++;
      }
    }

    const avgSimilarity = pairCount > 0 ? totalSimilarity / pairCount : 0;
    const diversityScore = 1 - avgSimilarity;

    // Calculate diversity by dimension
    const dimensionScores: Record<string, number> = {};
    for (const dimension of this.diversityConfig.dimensions) {
      dimensionScores[dimension.name] = this.calculateDimensionDiversity(items, dimension);
    }

    return {
      diversityScore,
      dimensionScores,
    };
  }

  /**
   * Calculate similarity matrix between items (synchronous version)
   * @param items Items to calculate similarities for
   * @returns Similarity matrix
   */
  private calculateItemSimilaritiesSync(items: any[]): number[][] {
    const n = items.length;
    const matrix: number[][] = Array(n).fill(0).map(() => Array(n).fill(0));

    // Calculate similarity for each pair of items
    for (let i = 0; i < n; i++) {
      // Diagonal is always 1 (item is identical to itself)
      matrix[i][i] = 1;

      for (let j = i + 1; j < n; j++) {
        // Calculate similarity based on dimensions
        const similarity = this.calculateItemPairSimilarity(items[i], items[j]);

        // Similarity is symmetric
        matrix[i][j] = similarity;
        matrix[j][i] = similarity;
      }
    }

    return matrix;
  }

  /**
   * Calculate diversity for a specific dimension
   * @param items Items to calculate diversity for
   * @param dimension Dimension to calculate diversity for
   * @returns Diversity score (0-1)
   */
  private calculateDimensionDiversity(items: any[], dimension: DiversityDimension): number {
    if (items.length <= 1) {
      return 1;
    }

    switch (dimension.type) {
      case 'categorical':
        return this.calculateCategoricalDiversity(items, dimension);
      case 'numerical':
        return this.calculateNumericalDiversity(items, dimension);
      case 'tags':
        return this.calculateTagsDiversity(items, dimension);
      case 'text':
        return this.calculateTextDiversity(items, dimension);
      default:
        return 0;
    }
  }

  /**
   * Calculate diversity for a categorical dimension
   * @param items Items to calculate diversity for
   * @param dimension Dimension to calculate diversity for
   * @returns Diversity score (0-1)
   */
  private calculateCategoricalDiversity(items: any[], dimension: DiversityDimension): number {
    // Count occurrences of each category
    const counts: Record<string, number> = {};
    let totalCount = 0;

    for (const item of items) {
      const value = item[dimension.field];
      if (value !== undefined) {
        counts[value] = (counts[value] || 0) + 1;
        totalCount++;
      }
    }

    if (totalCount === 0) {
      return 1;
    }

    // Calculate entropy
    let entropy = 0;
    for (const category in counts) {
      const probability = counts[category] / totalCount;
      entropy -= probability * Math.log2(probability);
    }

    // Normalize entropy to [0, 1]
    const maxEntropy = Math.log2(Object.keys(counts).length);
    return maxEntropy > 0 ? entropy / maxEntropy : 1;
  }

  /**
   * Calculate diversity for a numerical dimension
   * @param items Items to calculate diversity for
   * @param dimension Dimension to calculate diversity for
   * @returns Diversity score (0-1)
   */
  private calculateNumericalDiversity(items: any[], dimension: DiversityDimension): number {
    // Get all values
    const values: number[] = [];
    for (const item of items) {
      const value = item[dimension.field];
      if (value !== undefined && typeof value === 'number') {
        values.push(value);
      }
    }

    if (values.length <= 1) {
      return 1;
    }

    // Calculate range
    const min = Math.min(...values);
    const max = Math.max(...values);
    const range = dimension.range?.max - dimension.range?.min || max - min;

    if (range === 0) {
      return 0; // All values are the same
    }

    // Calculate standard deviation
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);

    // Normalize to [0, 1]
    return Math.min(1, stdDev / (range / 4)); // Assuming a normal distribution
  }

  /**
   * Calculate diversity for a tags dimension
   * @param items Items to calculate diversity for
   * @param dimension Dimension to calculate diversity for
   * @returns Diversity score (0-1)
   */
  private calculateTagsDiversity(items: any[], dimension: DiversityDimension): number {
    // Collect all tags
    const allTags = new Set<string>();
    const itemTags: string[][] = [];

    for (const item of items) {
      const tags = item[dimension.field];
      if (tags !== undefined) {
        const tagArray = Array.isArray(tags) ? tags : [tags];
        itemTags.push(tagArray);

        for (const tag of tagArray) {
          allTags.add(tag);
        }
      } else {
        itemTags.push([]);
      }
    }

    if (allTags.size === 0) {
      return 1;
    }

    // Calculate average Jaccard distance between tag sets
    let totalDistance = 0;
    let pairCount = 0;

    for (let i = 0; i < itemTags.length; i++) {
      for (let j = i + 1; j < itemTags.length; j++) {
        const similarity = this.calculateTagSimilarity(itemTags[i], itemTags[j]);
        totalDistance += (1 - similarity);
        pairCount++;
      }
    }

    return pairCount > 0 ? totalDistance / pairCount : 1;
  }

  /**
   * Calculate diversity for a text dimension
   * @param items Items to calculate diversity for
   * @param dimension Dimension to calculate diversity for
   * @returns Diversity score (0-1)
   */
  private calculateTextDiversity(items: any[], dimension: DiversityDimension): number {
    // Collect all texts
    const texts: string[] = [];

    for (const item of items) {
      const text = item[dimension.field];
      if (text !== undefined && typeof text === 'string') {
        texts.push(text);
      } else {
        texts.push('');
      }
    }

    if (texts.every(t => t === '')) {
      return 1;
    }

    // Calculate average text similarity
    let totalSimilarity = 0;
    let pairCount = 0;

    for (let i = 0; i < texts.length; i++) {
      for (let j = i + 1; j < texts.length; j++) {
        if (texts[i] === '' || texts[j] === '') continue;

        const similarity = this.calculateTextSimilarity(texts[i], texts[j]);
        totalSimilarity += similarity;
        pairCount++;
      }
    }

    const avgSimilarity = pairCount > 0 ? totalSimilarity / pairCount : 0;
    return 1 - avgSimilarity;
  }

  /**
   * Apply fairness constraints to recommendations
   * @param items Original recommended items
   * @param userId User ID
   * @param context Recommendation context
   * @returns Fair recommendations
   */
  async applyFairness(
    items: any[],
    userId: string,
    context?: Record<string, any>,
  ): Promise<any[]> {
    if (!this.fairnessConfig.enabled || items.length <= 1) {
      return items;
    }

    try {
      this.logger.debug(`Applying fairness constraints for ${items.length} items for user ${userId}`);

      // Get user information
      const user = await this.getUserInfo(userId);

      // Apply fairness strategy
      let fairItems: any[];
      switch (this.fairnessConfig.strategy) {
        case FairnessStrategy.PRE_PROCESSING:
          // Pre-processing is done before recommendations are generated
          fairItems = items;
          break;
        case FairnessStrategy.IN_PROCESSING:
          // In-processing is done during recommendation generation
          fairItems = items;
          break;
        case FairnessStrategy.POST_PROCESSING:
          fairItems = await this.postProcessingFairness(items, user);
          break;
        default:
          fairItems = items;
      }

      // Calculate fairness metrics
      const metrics = await this.calculateFairnessMetrics(fairItems, user);

      // Emit event with metrics
      this.eventEmitter.emit('fairness.applied', {
        userId,
        originalCount: items.length,
        fairCount: fairItems.length,
        metrics,
      });

      return fairItems;
    } catch (error) {
      this.logger.error(`Error applying fairness: ${error.message}`);
      return items; // Return original items in case of error
    }
  }

  /**
   * Get user information
   * @param userId User ID
   * @returns User information
   */
  private async getUserInfo(userId: string): Promise<any> {
    try {
      // In a real implementation, this would fetch user data from the database
      // For now, we'll return mock data
      return {
        id: userId,
        gender: 'female',
        age: 35,
        location: 'urban',
        preferences: {
          categories: ['wellness', 'meditation'],
          priceRange: 'mid-range',
        },
      };
    } catch (error) {
      this.logger.error(`Error getting user info for ${userId}: ${error.message}`);
      return {};
    }
  }

  /**
   * Apply post-processing fairness
   * @param items Original items
   * @param user User information
   * @returns Fair items
   */
  private async postProcessingFairness(items: any[], user: any): Promise<any[]> {
    if (items.length <= 1) {
      return items;
    }

    // Group items by protected attributes
    const groupedItems = this.groupItemsByProtectedAttributes(items);

    // Calculate current distribution
    const currentDistribution = this.calculateDistribution(groupedItems);

    // Calculate target distribution based on fairness constraints
    const targetDistribution = this.calculateTargetDistribution(currentDistribution, user);

    // Re-rank items to match target distribution
    return this.reRankForFairness(items, groupedItems, currentDistribution, targetDistribution);
  }

  /**
   * Group items by protected attributes
   * @param items Items to group
   * @returns Grouped items
   */
  private groupItemsByProtectedAttributes(items: any[]): Record<string, any[]> {
    const result: Record<string, any[]> = {};

    // For each protected attribute that applies to items
    for (const attribute of this.fairnessConfig.protectedAttributes) {
      if (attribute.appliesTo !== 'item' && attribute.appliesTo !== 'both') {
        continue;
      }

      const field = attribute.field;

      // Group items by this attribute
      const groups: Record<string, any[]> = {};

      for (const item of items) {
        const value = item[field];
        if (value === undefined) {
          continue;
        }

        const valueStr = String(value);
        if (!groups[valueStr]) {
          groups[valueStr] = [];
        }

        groups[valueStr].push(item);
      }

      result[attribute.name] = groups;
    }

    return result;
  }

  /**
   * Calculate distribution of items across protected attributes
   * @param groupedItems Grouped items
   * @returns Distribution
   */
  private calculateDistribution(groupedItems: Record<string, any[]>): Record<string, Record<string, number>> {
    const result: Record<string, Record<string, number>> = {};

    for (const [attribute, groups] of Object.entries(groupedItems)) {
      result[attribute] = {};

      let total = 0;
      for (const [value, items] of Object.entries(groups)) {
        total += items.length;
      }

      if (total === 0) {
        continue;
      }

      for (const [value, items] of Object.entries(groups)) {
        result[attribute][value] = items.length / total;
      }
    }

    return result;
  }

  /**
   * Calculate target distribution based on fairness constraints
   * @param currentDistribution Current distribution
   * @param user User information
   * @returns Target distribution
   */
  private calculateTargetDistribution(
    currentDistribution: Record<string, Record<string, number>>,
    user: any,
  ): Record<string, Record<string, number>> {
    const result: Record<string, Record<string, number>> = {};

    // For each attribute in the current distribution
    for (const [attribute, distribution] of Object.entries(currentDistribution)) {
      result[attribute] = { ...distribution };

      // Find constraints for this attribute
      const constraints = this.fairnessConfig.constraints.filter(c => c.attribute === attribute);

      if (constraints.length === 0) {
        continue;
      }

      // Apply each constraint
      for (const constraint of constraints) {
        switch (constraint.metric) {
          case FairnessMetric.STATISTICAL_PARITY:
            // Statistical parity: all groups should have equal representation
            const values = Object.keys(distribution);
            const targetProbability = 1 / values.length;

            for (const value of values) {
              result[attribute][value] = targetProbability;
            }
            break;

          case FairnessMetric.DISPARATE_IMPACT:
            // Disparate impact: ratio between groups should be above threshold
            this.applyDisparateImpactConstraint(result[attribute], constraint.threshold);
            break;

          // Other metrics would be implemented here
        }
      }
    }

    return result;
  }

  /**
   * Apply disparate impact constraint
   * @param distribution Distribution to modify
   * @param threshold Threshold
   */
  private applyDisparateImpactConstraint(distribution: Record<string, number>, threshold: number): void {
    const values = Object.keys(distribution);
    if (values.length <= 1) {
      return;
    }

    // Find min and max probabilities
    let minProb = Infinity;
    let maxProb = -Infinity;

    for (const value of values) {
      minProb = Math.min(minProb, distribution[value]);
      maxProb = Math.max(maxProb, distribution[value]);
    }

    // Calculate current ratio
    const ratio = minProb / maxProb;

    // If ratio is below threshold, adjust probabilities
    if (ratio < threshold) {
      // Calculate new probabilities
      const newMin = maxProb * threshold;

      // Adjust all probabilities
      let totalAdjustment = 0;

      for (const value of values) {
        if (distribution[value] < newMin) {
          totalAdjustment += newMin - distribution[value];
          distribution[value] = newMin;
        }
      }

      // Normalize to ensure sum is 1
      const sum = Object.values(distribution).reduce((a, b) => a + b, 0);
      for (const value of values) {
        distribution[value] /= sum;
      }
    }
  }

  /**
   * Re-rank items to match target distribution
   * @param items Original items
   * @param groupedItems Grouped items
   * @param currentDistribution Current distribution
   * @param targetDistribution Target distribution
   * @returns Re-ranked items
   */
  private reRankForFairness(
    items: any[],
    groupedItems: Record<string, any[]>,
    currentDistribution: Record<string, Record<string, number>>,
    targetDistribution: Record<string, Record<string, number>>,
  ): any[] {
    // For simplicity, we'll focus on a single attribute
    // In a real system, you would need to balance multiple attributes

    // Find the attribute with the most unfair distribution
    let mostUnfairAttribute = '';
    let maxDifference = -Infinity;

    for (const attribute in currentDistribution) {
      const current = currentDistribution[attribute];
      const target = targetDistribution[attribute];

      // Calculate total difference
      let totalDiff = 0;
      for (const value in current) {
        if (target[value] !== undefined) {
          totalDiff += Math.abs(current[value] - target[value]);
        }
      }

      if (totalDiff > maxDifference) {
        maxDifference = totalDiff;
        mostUnfairAttribute = attribute;
      }
    }

    if (!mostUnfairAttribute || maxDifference < 0.1) {
      // If the difference is small, return the original items
      return items;
    }

    // Re-rank based on the most unfair attribute
    const result: any[] = [];
    const remainingItems = [...items];
    const targetCounts: Record<string, number> = {};

    // Calculate target counts for each value
    const target = targetDistribution[mostUnfairAttribute];
    for (const value in target) {
      targetCounts[value] = Math.round(target[value] * items.length);
    }

    // Select items to match target counts
    for (const value in targetCounts) {
      const targetCount = targetCounts[value];
      const group = groupedItems[mostUnfairAttribute][value] || [];

      // Sort group by original rank
      group.sort((a, b) => items.indexOf(a) - items.indexOf(b));

      // Add items from this group up to the target count
      for (let i = 0; i < Math.min(targetCount, group.length); i++) {
        result.push(group[i]);
        const index = remainingItems.indexOf(group[i]);
        if (index >= 0) {
          remainingItems.splice(index, 1);
        }
      }
    }

    // Add remaining items in their original order
    for (const item of items) {
      if (remainingItems.includes(item)) {
        result.push(item);
      }
    }

    return result;
  }

  /**
   * Calculate fairness metrics
   * @param items Items to calculate metrics for
   * @param user User information
   * @returns Fairness metrics
   */
  private async calculateFairnessMetrics(items: any[], user: any): Promise<any> {
    if (items.length <= 1) {
      return {
        fairnessMetrics: {},
        constraintsSatisfied: true,
      };
    }

    // Group items by protected attributes
    const groupedItems = this.groupItemsByProtectedAttributes(items);

    // Calculate distribution
    const distribution = this.calculateDistribution(groupedItems);

    // Calculate metrics for each attribute and constraint
    const metrics: Record<string, number> = {};
    const violations: any[] = [];

    for (const constraint of this.fairnessConfig.constraints) {
      const attribute = constraint.attribute;
      const attrDistribution = distribution[attribute];

      if (!attrDistribution) {
        continue;
      }

      // Calculate metric value
      let metricValue: number;
      switch (constraint.metric) {
        case FairnessMetric.STATISTICAL_PARITY:
          metricValue = this.calculateStatisticalParity(attrDistribution);
          break;
        case FairnessMetric.DISPARATE_IMPACT:
          metricValue = this.calculateDisparateImpact(attrDistribution);
          break;
        default:
          metricValue = 0;
      }

      // Store metric
      const metricName = `${constraint.metric}_${attribute}`;
      metrics[metricName] = metricValue;

      // Check if constraint is satisfied
      const satisfied = constraint.direction === 'above'
        ? metricValue >= constraint.threshold
        : metricValue <= constraint.threshold;

      if (!satisfied) {
        violations.push({
          attribute,
          metric: constraint.metric,
          value: metricValue,
          threshold: constraint.threshold,
          direction: constraint.direction,
        });
      }
    }

    return {
      fairnessMetrics: metrics,
      constraintsSatisfied: violations.length === 0,
      constraintViolations: violations,
    };
  }

  /**
   * Calculate statistical parity
   * @param distribution Distribution
   * @returns Statistical parity value
   */
  private calculateStatisticalParity(distribution: Record<string, number>): number {
    const values = Object.values(distribution);
    if (values.length <= 1) {
      return 1;
    }

    // Statistical parity is the difference between the max and min probabilities
    const max = Math.max(...values);
    const min = Math.min(...values);

    return 1 - (max - min);
  }

  /**
   * Calculate disparate impact
   * @param distribution Distribution
   * @returns Disparate impact value
   */
  private calculateDisparateImpact(distribution: Record<string, number>): number {
    const values = Object.values(distribution);
    if (values.length <= 1) {
      return 1;
    }

    // Disparate impact is the ratio between the min and max probabilities
    const max = Math.max(...values);
    const min = Math.min(...values);

    return max > 0 ? min / max : 1;
  }
}