import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { OnEvent } from '@nestjs/event-emitter';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { 
  ContinuousLearningParams, 
  LearningMetrics, 
  LearningEvent, 
  UserModel 
} from '../interfaces/continuous-learning.interface';

/**
 * Service d'apprentissage continu pour les recommandations
 * Adapte les modèles de recommandation en fonction des interactions utilisateur en temps réel
 */
@Injectable()
export class ContinuousLearningService implements OnModuleInit {
  private readonly logger = new Logger(ContinuousLearningService.name);
  private readonly params: ContinuousLearningParams;
  private readonly userModels: Map<string, UserModel> = new Map();
  private readonly interactionQueue: any[] = [];
  private processingQueue = false;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly schedulerRegistry: SchedulerRegistry,
  ) {
    // Initialiser les paramètres d'apprentissage à partir de la configuration
    this.params = {
      learningRate: this.configService.get<number>('recommendation.continuousLearning.learningRate', 0.1),
      forgettingFactor: this.configService.get<number>('recommendation.continuousLearning.forgettingFactor', 0.95),
      changeDetectionThreshold: this.configService.get<number>('recommendation.continuousLearning.changeDetectionThreshold', 0.3),
      recentInteractionsWindow: this.configService.get<number>('recommendation.continuousLearning.recentInteractionsWindow', 24),
      minInteractionsForUpdate: this.configService.get<number>('recommendation.continuousLearning.minInteractionsForUpdate', 5),
      modelUpdateInterval: this.configService.get<number>('recommendation.continuousLearning.modelUpdateInterval', 15),
      interactionWeights: this.configService.get<Record<string, number>>('recommendation.continuousLearning.interactionWeights', {
        VIEW: 0.1,
        LIKE: 0.5,
        DISLIKE: -0.5,
        BOOKMARK: 0.7,
        SHARE: 0.8,
        PURCHASE: 1.0,
        RECOMMENDATION_VIEW: 0.05,
      }),
      enableOutlierDetection: this.configService.get<boolean>('recommendation.continuousLearning.enableOutlierDetection', true),
      outlierDetectionThreshold: this.configService.get<number>('recommendation.continuousLearning.outlierDetectionThreshold', 2.5),
    };
  }

  /**
   * Initialisation du module
   */
  async onModuleInit() {
    this.logger.log('Initialisation du service d\'apprentissage continu');
    
    // Charger les modèles utilisateur existants
    await this.loadUserModels();
    
    // Configurer le traitement périodique de la file d'attente
    const queueInterval = setInterval(() => this.processInteractionQueue(), 5000);
    this.schedulerRegistry.addInterval('interactionQueueProcessor', queueInterval);
    
    this.logger.log('Service d\'apprentissage continu initialisé');
  }

  /**
   * Charge les modèles utilisateur depuis la base de données
   */
  private async loadUserModels() {
    try {
      this.logger.log('Chargement des modèles utilisateur');
      
      // Récupérer les modèles utilisateur depuis la base de données
      const userModelEntities = await this.prisma.userModel.findMany({
        include: {
          preferences: true,
          categoryInterests: true,
          tagInterests: true,
        },
      });
      
      // Convertir les entités en modèles utilisateur
      for (const entity of userModelEntities) {
        const userModel: UserModel = {
          userId: entity.userId,
          lastUpdated: entity.updatedAt,
          preferences: entity.preferences.reduce((acc, pref) => {
            acc[pref.key] = pref.value;
            return acc;
          }, {}),
          categoryInterests: entity.categoryInterests.reduce((acc, cat) => {
            acc[cat.category] = cat.interestLevel;
            return acc;
          }, {}),
          tagInterests: entity.tagInterests.reduce((acc, tag) => {
            acc[tag.tag] = tag.interestLevel;
            return acc;
          }, {}),
          recentInteractions: [],
          context: entity.context as any,
          metrics: entity.metrics as any,
        };
        
        this.userModels.set(entity.userId, userModel);
      }
      
      // Charger les interactions récentes pour chaque utilisateur
      await this.loadRecentInteractions();
      
      this.logger.log(`${this.userModels.size} modèles utilisateur chargés`);
    } catch (error) {
      this.logger.error(`Erreur lors du chargement des modèles utilisateur: ${error.message}`);
    }
  }

  /**
   * Charge les interactions récentes pour tous les utilisateurs
   */
  private async loadRecentInteractions() {
    try {
      const windowHours = this.params.recentInteractionsWindow;
      const cutoffDate = new Date(Date.now() - windowHours * 60 * 60 * 1000);
      
      // Récupérer les interactions récentes
      const recentInteractions = await this.prisma.userInteraction.findMany({
        where: {
          createdAt: {
            gte: cutoffDate,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
      
      // Ajouter les interactions aux modèles utilisateur
      for (const interaction of recentInteractions) {
        let userModel = this.userModels.get(interaction.userId);
        
        // Créer un nouveau modèle si nécessaire
        if (!userModel) {
          userModel = this.createInitialUserModel(interaction.userId);
          this.userModels.set(interaction.userId, userModel);
        }
        
        // Ajouter l'interaction au modèle
        userModel.recentInteractions.push({
          itemId: interaction.itemId,
          itemType: interaction.itemType as RecommendationType,
          interactionType: interaction.interactionType,
          timestamp: interaction.createdAt,
          weight: this.getInteractionWeight(interaction.interactionType),
        });
      }
      
      this.logger.log(`${recentInteractions.length} interactions récentes chargées`);
    } catch (error) {
      this.logger.error(`Erreur lors du chargement des interactions récentes: ${error.message}`);
    }
  }

  /**
   * Crée un modèle utilisateur initial
   * @param userId ID de l'utilisateur
   * @returns Modèle utilisateur initial
   */
  private createInitialUserModel(userId: string): UserModel {
    return {
      userId,
      lastUpdated: new Date(),
      preferences: {},
      categoryInterests: {},
      tagInterests: {},
      recentInteractions: [],
      context: {},
      metrics: {
        precision: 0,
        recall: 0,
        f1Score: 0,
        updates: 0,
      },
    };
  }

  /**
   * Retourne le poids d'un type d'interaction
   * @param interactionType Type d'interaction
   * @returns Poids de l'interaction
   */
  private getInteractionWeight(interactionType: string): number {
    return this.params.interactionWeights[interactionType] || 0.1;
  }

  /**
   * Traite une nouvelle interaction utilisateur
   * @param userId ID de l'utilisateur
   * @param itemId ID de l'élément
   * @param itemType Type d'élément
   * @param interactionType Type d'interaction
   * @param metadata Métadonnées de l'interaction
   */
  async processInteraction(
    userId: string,
    itemId: string,
    itemType: RecommendationType,
    interactionType: string,
    metadata: Record<string, any> = {},
  ) {
    try {
      // Ajouter l'interaction à la file d'attente
      this.interactionQueue.push({
        userId,
        itemId,
        itemType,
        interactionType,
        timestamp: new Date(),
        metadata,
      });
      
      this.logger.debug(`Interaction ajoutée à la file d'attente: ${userId} - ${itemId} - ${interactionType}`);
      
      // Déclencher le traitement de la file d'attente si nécessaire
      if (!this.processingQueue && this.interactionQueue.length >= this.params.minInteractionsForUpdate) {
        this.processInteractionQueue();
      }
    } catch (error) {
      this.logger.error(`Erreur lors du traitement de l'interaction: ${error.message}`);
    }
  }

  /**
   * Traite la file d'attente des interactions
   */
  private async processInteractionQueue() {
    if (this.processingQueue || this.interactionQueue.length === 0) {
      return;
    }
    
    this.processingQueue = true;
    
    try {
      this.logger.debug(`Traitement de ${this.interactionQueue.length} interactions en attente`);
      
      // Regrouper les interactions par utilisateur
      const userInteractions = new Map<string, any[]>();
      
      for (const interaction of this.interactionQueue) {
        if (!userInteractions.has(interaction.userId)) {
          userInteractions.set(interaction.userId, []);
        }
        userInteractions.get(interaction.userId)!.push(interaction);
      }
      
      // Traiter les interactions pour chaque utilisateur
      for (const [userId, interactions] of userInteractions.entries()) {
        await this.updateUserModel(userId, interactions);
      }
      
      // Vider la file d'attente
      this.interactionQueue.length = 0;
      
      this.logger.debug('Traitement de la file d\'attente terminé');
    } catch (error) {
      this.logger.error(`Erreur lors du traitement de la file d'attente: ${error.message}`);
    } finally {
      this.processingQueue = false;
    }
  }

  /**
   * Met à jour le modèle d'un utilisateur avec de nouvelles interactions
   * @param userId ID de l'utilisateur
   * @param interactions Nouvelles interactions
   */
  private async updateUserModel(userId: string, interactions: any[]) {
    try {
      // Récupérer ou créer le modèle utilisateur
      let userModel = this.userModels.get(userId);
      if (!userModel) {
        userModel = this.createInitialUserModel(userId);
        this.userModels.set(userId, userModel);
      }
      
      // Ajouter les nouvelles interactions
      for (const interaction of interactions) {
        userModel.recentInteractions.push({
          itemId: interaction.itemId,
          itemType: interaction.itemType,
          interactionType: interaction.interactionType,
          timestamp: interaction.timestamp,
          weight: this.getInteractionWeight(interaction.interactionType),
        });
      }
      
      // Limiter le nombre d'interactions récentes
      const maxInteractions = 100;
      if (userModel.recentInteractions.length > maxInteractions) {
        userModel.recentInteractions.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
        userModel.recentInteractions = userModel.recentInteractions.slice(0, maxInteractions);
      }
      
      // Mettre à jour les intérêts par catégorie et par tag
      await this.updateInterests(userModel, interactions);
      
      // Détecter les changements de comportement
      const behaviorChanges = this.detectBehaviorChanges(userModel, interactions);
      if (behaviorChanges.length > 0) {
        this.logger.log(`Changements de comportement détectés pour l'utilisateur ${userId}: ${behaviorChanges.length}`);
        // Enregistrer les événements de changement de comportement
        for (const change of behaviorChanges) {
          await this.recordLearningEvent(userId, 'behavior_change', change);
        }
      }
      
      // Mettre à jour la date de dernière mise à jour
      userModel.lastUpdated = new Date();
      userModel.metrics.updates += 1;
      
      // Sauvegarder le modèle utilisateur
      await this.saveUserModel(userModel);
      
      this.logger.debug(`Modèle utilisateur mis à jour pour ${userId}`);
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du modèle utilisateur ${userId}: ${error.message}`);
    }
  }

  /**
   * Met à jour les intérêts par catégorie et par tag
   * @param userModel Modèle utilisateur
   * @param interactions Nouvelles interactions
   */
  private async updateInterests(userModel: UserModel, interactions: any[]) {
    try {
      // Récupérer les détails des éléments
      const itemIds = [...new Set(interactions.map(i => i.itemId))];
      const items = await this.prisma.item.findMany({
        where: {
          id: {
            in: itemIds,
          },
        },
        select: {
          id: true,
          category: true,
          tags: true,
        },
      });
      
      // Créer une map des éléments pour un accès rapide
      const itemMap = new Map(items.map(item => [item.id, item]));
      
      // Mettre à jour les intérêts par catégorie
      for (const interaction of interactions) {
        const item = itemMap.get(interaction.itemId);
        if (!item) continue;
        
        // Mettre à jour l'intérêt pour la catégorie
        const category = item.category;
        if (category) {
          const currentInterest = userModel.categoryInterests[category] || 0;
          const interactionWeight = this.getInteractionWeight(interaction.interactionType);
          const newInterest = currentInterest * this.params.forgettingFactor + interactionWeight * this.params.learningRate;
          userModel.categoryInterests[category] = newInterest;
        }
        
        // Mettre à jour l'intérêt pour les tags
        for (const tag of item.tags || []) {
          const currentInterest = userModel.tagInterests[tag] || 0;
          const interactionWeight = this.getInteractionWeight(interaction.interactionType);
          const newInterest = currentInterest * this.params.forgettingFactor + interactionWeight * this.params.learningRate;
          userModel.tagInterests[tag] = newInterest;
        }
      }
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour des intérêts: ${error.message}`);
    }
  }

  /**
   * Détecte les changements de comportement
   * @param userModel Modèle utilisateur
   * @param interactions Nouvelles interactions
   * @returns Liste des changements détectés
   */
  private detectBehaviorChanges(userModel: UserModel, interactions: any[]): any[] {
    const changes = [];
    
    // Implémenter la détection des changements de comportement
    // Cette implémentation est simplifiée et devrait être améliorée
    
    return changes;
  }

  /**
   * Enregistre un événement d'apprentissage
   * @param userId ID de l'utilisateur
   * @param eventType Type d'événement
   * @param data Données de l'événement
   */
  private async recordLearningEvent(
    userId: string,
    eventType: 'model_update' | 'behavior_change' | 'preference_change' | 'outlier_detected',
    data: Record<string, any>,
  ) {
    try {
      await this.prisma.learningEvent.create({
        data: {
          userId,
          eventType,
          data,
          estimatedImpact: data.magnitude || 0.5,
        },
      });
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de l'événement d'apprentissage: ${error.message}`);
    }
  }

  /**
   * Sauvegarde un modèle utilisateur dans la base de données
   * @param userModel Modèle utilisateur à sauvegarder
   */
  private async saveUserModel(userModel: UserModel) {
    try {
      // Mettre à jour ou créer le modèle utilisateur
      await this.prisma.userModel.upsert({
        where: {
          userId: userModel.userId,
        },
        update: {
          context: userModel.context,
          metrics: userModel.metrics,
          updatedAt: new Date(),
          // Mettre à jour les préférences, intérêts par catégorie et par tag
          preferences: {
            deleteMany: {},
            createMany: {
              data: Object.entries(userModel.preferences).map(([key, value]) => ({
                userId: userModel.userId,
                key,
                value,
              })),
            },
          },
          categoryInterests: {
            deleteMany: {},
            createMany: {
              data: Object.entries(userModel.categoryInterests).map(([category, interestLevel]) => ({
                userId: userModel.userId,
                category,
                interestLevel,
              })),
            },
          },
          tagInterests: {
            deleteMany: {},
            createMany: {
              data: Object.entries(userModel.tagInterests).map(([tag, interestLevel]) => ({
                userId: userModel.userId,
                tag,
                interestLevel,
              })),
            },
          },
        },
        create: {
          userId: userModel.userId,
          context: userModel.context,
          metrics: userModel.metrics,
          preferences: {
            createMany: {
              data: Object.entries(userModel.preferences).map(([key, value]) => ({
                userId: userModel.userId,
                key,
                value,
              })),
            },
          },
          categoryInterests: {
            createMany: {
              data: Object.entries(userModel.categoryInterests).map(([category, interestLevel]) => ({
                userId: userModel.userId,
                category,
                interestLevel,
              })),
            },
          },
          tagInterests: {
            createMany: {
              data: Object.entries(userModel.tagInterests).map(([tag, interestLevel]) => ({
                userId: userModel.userId,
                tag,
                interestLevel,
              })),
            },
          },
        },
      });
    } catch (error) {
      this.logger.error(`Erreur lors de la sauvegarde du modèle utilisateur: ${error.message}`);
    }
  }

  /**
   * Mise à jour périodique des modèles
   * Exécutée toutes les 15 minutes
   */
  @Cron(CronExpression.EVERY_15_MINUTES)
  async periodicModelUpdate() {
    this.logger.log('Mise à jour périodique des modèles');
    
    try {
      // Traiter la file d'attente des interactions
      await this.processInteractionQueue();
      
      // Mettre à jour les métriques des modèles
      await this.updateModelMetrics();
      
      this.logger.log('Mise à jour périodique des modèles terminée');
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour périodique des modèles: ${error.message}`);
    }
  }

  /**
   * Met à jour les métriques des modèles
   */
  private async updateModelMetrics() {
    // Implémenter la mise à jour des métriques des modèles
  }

  /**
   * Récupère le modèle d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Modèle de l'utilisateur ou null si non trouvé
   */
  async getUserModel(userId: string): Promise<UserModel | null> {
    return this.userModels.get(userId) || null;
  }

  /**
   * Récupère les métriques d'apprentissage d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param startDate Date de début
   * @param endDate Date de fin
   * @returns Métriques d'apprentissage
   */
  async getLearningMetrics(
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<LearningMetrics | null> {
    try {
      // Récupérer le modèle utilisateur
      const userModel = this.userModels.get(userId);
      if (!userModel) {
        return null;
      }
      
      // Récupérer les événements d'apprentissage
      const events = await this.prisma.learningEvent.findMany({
        where: {
          userId,
          timestamp: {
            gte: startDate,
            lte: endDate,
          },
        },
        orderBy: {
          timestamp: 'asc',
        },
      });
      
      // Récupérer les interactions
      const interactions = await this.prisma.userInteraction.findMany({
        where: {
          userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });
      
      // Calculer les statistiques des interactions
      const interactionStats = {
        total: interactions.length,
        byType: {},
        byStrategy: {},
        byItemType: {},
      };
      
      for (const interaction of interactions) {
        // Compter par type d'interaction
        interactionStats.byType[interaction.interactionType] = 
          (interactionStats.byType[interaction.interactionType] || 0) + 1;
        
        // Compter par type d'élément
        interactionStats.byItemType[interaction.itemType] = 
          (interactionStats.byItemType[interaction.itemType] || 0) + 1;
        
        // Compter par stratégie (si disponible dans les métadonnées)
        if (interaction.metadata && interaction.metadata.strategy) {
          interactionStats.byStrategy[interaction.metadata.strategy] = 
            (interactionStats.byStrategy[interaction.metadata.strategy] || 0) + 1;
        }
      }
      
      // Construire les métriques d'apprentissage
      const metrics: LearningMetrics = {
        userId,
        period: {
          start: startDate,
          end: endDate,
        },
        modelUpdates: events.filter(e => e.eventType === 'model_update').length,
        detectedChanges: events
          .filter(e => e.eventType === 'behavior_change' || e.eventType === 'preference_change')
          .map(e => ({
            type: e.eventType === 'behavior_change' ? 'behavior' : 'preference',
            timestamp: e.timestamp,
            description: e.data.description || '',
            magnitude: e.estimatedImpact,
          })),
        performance: {
          precisionBefore: userModel.metrics.precision,
          precisionAfter: userModel.metrics.precision,
          recallBefore: userModel.metrics.recall,
          recallAfter: userModel.metrics.recall,
          f1ScoreBefore: userModel.metrics.f1Score,
          f1ScoreAfter: userModel.metrics.f1Score,
        },
        interactionStats,
      };
      
      return metrics;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des métriques d'apprentissage: ${error.message}`);
      return null;
    }
  }

  /**
   * Réagit à un événement d'interaction utilisateur
   * @param payload Données de l'événement
   */
  @OnEvent('user.interaction')
  async handleUserInteraction(payload: any) {
    this.logger.debug(`Événement d'interaction utilisateur reçu: ${payload.userId} - ${payload.itemId} - ${payload.interactionType}`);
    
    await this.processInteraction(
      payload.userId,
      payload.itemId,
      payload.itemType,
      payload.interactionType,
      payload.metadata,
    );
  }
}
