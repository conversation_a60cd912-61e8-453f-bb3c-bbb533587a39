import { Injectable, Logger } from '@nestjs/common';
import { RecommendationOptions } from '../interfaces/recommendation-options.interface';

/**
 * Service de diversification des recommandations
 * Assure une diversité dans les recommandations pour éviter l'effet "bulle de filtre"
 */
@Injectable()
export class DiversityFilterService {
  private readonly logger = new Logger(DiversityFilterService.name);
  
  /**
   * Applique un filtre de diversité aux recommandations
   * @param recommendations Liste des recommandations
   * @param options Options de recommandation
   * @returns Liste des recommandations diversifiées
   */
  applyDiversityFilter(recommendations: any[], options: RecommendationOptions = {}) {
    if (!recommendations || recommendations.length === 0) {
      return recommendations;
    }
    
    // Vérifier si la diversification est activée
    const diversification = options.diversification || {};
    if (diversification.enabled === false) {
      return recommendations;
    }
    
    // Récupérer les paramètres de diversification
    const method = diversification.method || 'MMR';
    const weight = diversification.weight || 0.3; // Poids par défaut de la diversité
    
    // Appliquer la méthode de diversification appropriée
    switch (method) {
      case 'DETERMINANTAL':
        return this.applyDeterminantalPointProcess(recommendations, weight);
      case 'CLUSTERING':
        return this.applyClustering(recommendations, weight);
      case 'MMR':
      default:
        return this.applyMaximalMarginalRelevance(recommendations, weight);
    }
  }
  
  /**
   * Applique l'algorithme de Maximal Marginal Relevance (MMR)
   * @param recommendations Liste des recommandations
   * @param lambda Poids de la diversité (entre 0 et 1)
   * @returns Liste des recommandations diversifiées
   */
  private applyMaximalMarginalRelevance(recommendations: any[], lambda: number) {
    this.logger.log(`Application de l'algorithme MMR avec lambda=${lambda}`);
    
    // Copier les recommandations pour ne pas modifier l'original
    const originalRecommendations = [...recommendations];
    
    // Trier les recommandations par score
    originalRecommendations.sort((a, b) => b.score - a.score);
    
    // Initialiser la liste des recommandations diversifiées avec le premier élément
    const diversifiedRecommendations = [originalRecommendations[0]];
    const remainingRecommendations = originalRecommendations.slice(1);
    
    // Ajouter les éléments un par un en maximisant la diversité
    while (diversifiedRecommendations.length < originalRecommendations.length) {
      let bestScore = -Infinity;
      let bestIndex = -1;
      
      // Parcourir les recommandations restantes
      for (let i = 0; i < remainingRecommendations.length; i++) {
        const candidate = remainingRecommendations[i];
        
        // Calculer la similarité maximale avec les éléments déjà sélectionnés
        let maxSimilarity = -Infinity;
        for (const selected of diversifiedRecommendations) {
          const similarity = this.calculateSimilarity(candidate, selected);
          maxSimilarity = Math.max(maxSimilarity, similarity);
        }
        
        // Calculer le score MMR
        const mmrScore = lambda * candidate.score - (1 - lambda) * maxSimilarity;
        
        // Mettre à jour le meilleur candidat
        if (mmrScore > bestScore) {
          bestScore = mmrScore;
          bestIndex = i;
        }
      }
      
      // Ajouter le meilleur candidat à la liste
      if (bestIndex !== -1) {
        diversifiedRecommendations.push(remainingRecommendations[bestIndex]);
        remainingRecommendations.splice(bestIndex, 1);
      } else {
        break;
      }
    }
    
    return diversifiedRecommendations;
  }
  
  /**
   * Applique l'algorithme de Determinantal Point Process (DPP)
   * @param recommendations Liste des recommandations
   * @param weight Poids de la diversité
   * @returns Liste des recommandations diversifiées
   */
  private applyDeterminantalPointProcess(recommendations: any[], weight: number) {
    this.logger.log(`Application de l'algorithme DPP avec poids=${weight}`);
    
    // Implémentation simplifiée de DPP
    // Dans une implémentation réelle, on utiliserait des bibliothèques d'algèbre linéaire
    
    // Utiliser MMR comme fallback pour cette implémentation
    return this.applyMaximalMarginalRelevance(recommendations, weight);
  }
  
  /**
   * Applique l'algorithme de clustering pour la diversification
   * @param recommendations Liste des recommandations
   * @param weight Poids de la diversité
   * @returns Liste des recommandations diversifiées
   */
  private applyClustering(recommendations: any[], weight: number) {
    this.logger.log(`Application de l'algorithme de clustering avec poids=${weight}`);
    
    // Implémentation simplifiée de clustering
    // Dans une implémentation réelle, on utiliserait des algorithmes de clustering comme k-means
    
    // Utiliser MMR comme fallback pour cette implémentation
    return this.applyMaximalMarginalRelevance(recommendations, weight);
  }
  
  /**
   * Calcule la similarité entre deux recommandations
   * @param rec1 Première recommandation
   * @param rec2 Deuxième recommandation
   * @returns Score de similarité (entre 0 et 1)
   */
  private calculateSimilarity(rec1: any, rec2: any): number {
    // Calculer la similarité basée sur les métadonnées
    const metadata1 = rec1.metadata || {};
    const metadata2 = rec2.metadata || {};
    
    let similarityScore = 0;
    let factorsCount = 0;
    
    // Similarité des catégories
    if (metadata1.category && metadata2.category) {
      factorsCount++;
      if (metadata1.category === metadata2.category) {
        similarityScore += 1;
      }
    }
    
    // Similarité des localisations
    if (metadata1.location && metadata2.location) {
      factorsCount++;
      if (metadata1.location.country === metadata2.location.country) {
        similarityScore += 1;
      }
    }
    
    // Similarité des tags
    if (metadata1.tags && metadata2.tags) {
      factorsCount++;
      
      const tags1 = new Set(metadata1.tags);
      const tags2 = new Set(metadata2.tags);
      
      const commonTags = [...tags1].filter(tag => tags2.has(tag));
      const allTags = new Set([...tags1, ...tags2]);
      
      similarityScore += commonTags.length / allTags.size;
    }
    
    // Normaliser le score
    return factorsCount > 0 ? similarityScore / factorsCount : 0;
  }
}
