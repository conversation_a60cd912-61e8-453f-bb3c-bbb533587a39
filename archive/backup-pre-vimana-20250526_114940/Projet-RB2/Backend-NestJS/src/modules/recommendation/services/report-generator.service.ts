import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { AnalyticsService, GlobalMetrics, UserMetrics } from './analytics.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import * as fs from 'fs';
import * as path from 'path';
import * as PDFDocument from 'pdfkit';

/**
 * Service de génération de rapports PDF
 * Génère des rapports PDF sur les recommandations
 */
@Injectable()
export class ReportGeneratorService {
  private readonly logger = new Logger(ReportGeneratorService.name);
  private readonly reportsDir: string;

  constructor(
    private readonly prisma: PrismaService,
    private readonly analyticsService: AnalyticsService,
    private readonly configService: ConfigService,
  ) {
    // Créer le répertoire des rapports s'il n'existe pas
    this.reportsDir = this.configService.get<string>('recommendation.reports.directory', 'reports');
    
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
    }
    
    this.logger.log(`ReportGeneratorService initialized with reportsDir=${this.reportsDir}`);
  }

  /**
   * Génère un rapport PDF global sur les recommandations
   * @param startDate Date de début de la période d'analyse
   * @param endDate Date de fin de la période d'analyse
   * @returns Chemin du fichier PDF généré
   */
  async generateGlobalReport(
    startDate?: Date,
    endDate?: Date,
  ): Promise<string> {
    try {
      // Définir la période d'analyse
      const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 derniers jours par défaut
      const end = endDate || new Date();
      
      this.logger.log(`Génération d'un rapport global du ${start.toISOString()} au ${end.toISOString()}`);
      
      // Récupérer les métriques globales
      const metrics = await this.analyticsService.getGlobalMetrics(start, end);
      
      // Générer le nom du fichier
      const fileName = `global_report_${start.toISOString().split('T')[0]}_to_${end.toISOString().split('T')[0]}.pdf`;
      const filePath = path.join(this.reportsDir, fileName);
      
      // Générer le PDF
      await this.generateGlobalReportPDF(metrics, filePath);
      
      this.logger.log(`Rapport global généré avec succès: ${filePath}`);
      
      return filePath;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération du rapport global: ${error.message}`);
      throw error;
    }
  }

  /**
   * Génère un rapport PDF pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param startDate Date de début de la période d'analyse
   * @param endDate Date de fin de la période d'analyse
   * @returns Chemin du fichier PDF généré
   */
  async generateUserReport(
    userId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<string> {
    try {
      // Définir la période d'analyse
      const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 derniers jours par défaut
      const end = endDate || new Date();
      
      this.logger.log(`Génération d'un rapport pour l'utilisateur ${userId} du ${start.toISOString()} au ${end.toISOString()}`);
      
      // Récupérer les métriques de l'utilisateur
      const metrics = await this.analyticsService.getUserMetrics(userId, start, end);
      
      // Récupérer les détails de l'utilisateur
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          email: true,
          profile: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
      });
      
      // Générer le nom du fichier
      const userName = user?.profile?.firstName ? `${user.profile.firstName}_${user.profile.lastName || ''}` : user?.email || userId;
      const fileName = `user_report_${userName.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_${start.toISOString().split('T')[0]}_to_${end.toISOString().split('T')[0]}.pdf`;
      const filePath = path.join(this.reportsDir, fileName);
      
      // Générer le PDF
      await this.generateUserReportPDF(metrics, user, filePath);
      
      this.logger.log(`Rapport utilisateur généré avec succès: ${filePath}`);
      
      return filePath;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération du rapport utilisateur: ${error.message}`);
      throw error;
    }
  }

  /**
   * Génère un rapport PDF global sur les recommandations
   * @param metrics Métriques globales
   * @param filePath Chemin du fichier PDF à générer
   */
  private async generateGlobalReportPDF(
    metrics: GlobalMetrics,
    filePath: string,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Créer le document PDF
        const doc = new PDFDocument({ margin: 50 });
        
        // Créer le flux d'écriture
        const stream = fs.createWriteStream(filePath);
        doc.pipe(stream);
        
        // Ajouter le titre
        doc.fontSize(25).text('Rapport Global des Recommandations', { align: 'center' });
        doc.moveDown();
        
        // Ajouter la période
        doc.fontSize(14).text(`Période: ${metrics.period.start.toLocaleDateString()} - ${metrics.period.end.toLocaleDateString()}`, { align: 'center' });
        doc.moveDown();
        
        // Ajouter les métriques globales
        doc.fontSize(18).text('Métriques Globales');
        doc.moveDown();
        
        doc.fontSize(12).text(`Nombre total de recommandations: ${metrics.totalRecommendations}`);
        doc.text(`Nombre total d'interactions: ${metrics.totalInteractions}`);
        doc.moveDown();
        
        // Ajouter les taux de conversion
        doc.fontSize(14).text('Taux de Conversion');
        doc.moveDown();
        
        doc.fontSize(12).text(`Taux de clics (CTR): ${metrics.rates.clickThroughRate.toFixed(2)}%`);
        doc.text(`Taux de conversion: ${metrics.rates.conversionRate.toFixed(2)}%`);
        doc.text(`Taux d'engagement: ${metrics.rates.engagementRate.toFixed(2)}%`);
        doc.moveDown();
        
        // Ajouter les interactions par type
        doc.fontSize(14).text('Interactions par Type');
        doc.moveDown();
        
        Object.entries(metrics.interactionCounts).forEach(([type, count]) => {
          doc.fontSize(12).text(`${type}: ${count}`);
        });
        doc.moveDown();
        
        // Ajouter les métriques par type de recommandation
        doc.fontSize(18).text('Métriques par Type de Recommandation');
        doc.moveDown();
        
        metrics.byType.forEach(typeMetric => {
          doc.fontSize(14).text(`Type: ${typeMetric.type}`);
          doc.fontSize(12).text(`Nombre de recommandations: ${typeMetric.totalRecommendations}`);
          doc.text(`Nombre d'interactions: ${typeMetric.totalInteractions}`);
          doc.text(`Taux de conversion: ${typeMetric.rates.conversionRate.toFixed(2)}%`);
          doc.moveDown();
        });
        
        // Ajouter les métriques par stratégie de recommandation
        doc.fontSize(18).text('Métriques par Stratégie de Recommandation');
        doc.moveDown();
        
        metrics.byStrategy.forEach(strategyMetric => {
          doc.fontSize(14).text(`Stratégie: ${strategyMetric.strategy}`);
          doc.fontSize(12).text(`Nombre de recommandations: ${strategyMetric.totalRecommendations}`);
          doc.text(`Nombre d'interactions: ${strategyMetric.totalInteractions}`);
          doc.text(`Taux de conversion: ${strategyMetric.rates.conversionRate.toFixed(2)}%`);
          doc.moveDown();
        });
        
        // Ajouter les métriques quotidiennes
        doc.fontSize(18).text('Métriques Quotidiennes');
        doc.moveDown();
        
        // Créer un tableau simplifié
        const tableTop = doc.y;
        const tableLeft = 50;
        const colWidth = 100;
        
        // En-têtes du tableau
        doc.fontSize(12).text('Date', tableLeft, tableTop);
        doc.text('Recommandations', tableLeft + colWidth, tableTop);
        doc.text('Interactions', tableLeft + 2 * colWidth, tableTop);
        
        // Ligne de séparation
        doc.moveTo(tableLeft, tableTop + 20)
           .lineTo(tableLeft + 3 * colWidth, tableTop + 20)
           .stroke();
        
        // Données du tableau
        let rowTop = tableTop + 30;
        metrics.daily.slice(0, 10).forEach(day => { // Limiter à 10 jours pour la lisibilité
          doc.fontSize(10).text(day.date.toLocaleDateString(), tableLeft, rowTop);
          doc.text(day.totalRecommendations.toString(), tableLeft + colWidth, rowTop);
          doc.text(day.totalInteractions.toString(), tableLeft + 2 * colWidth, rowTop);
          rowTop += 20;
        });
        
        // Finaliser le document
        doc.end();
        
        // Attendre la fin de l'écriture
        stream.on('finish', () => {
          resolve();
        });
        
        stream.on('error', (error) => {
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Génère un rapport PDF pour un utilisateur
   * @param metrics Métriques de l'utilisateur
   * @param user Détails de l'utilisateur
   * @param filePath Chemin du fichier PDF à générer
   */
  private async generateUserReportPDF(
    metrics: UserMetrics,
    user: any,
    filePath: string,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Créer le document PDF
        const doc = new PDFDocument({ margin: 50 });
        
        // Créer le flux d'écriture
        const stream = fs.createWriteStream(filePath);
        doc.pipe(stream);
        
        // Ajouter le titre
        doc.fontSize(25).text('Rapport Personnalisé des Recommandations', { align: 'center' });
        doc.moveDown();
        
        // Ajouter les informations de l'utilisateur
        const userName = user?.profile?.firstName ? `${user.profile.firstName} ${user.profile.lastName || ''}` : user?.email || metrics.userId;
        doc.fontSize(16).text(`Utilisateur: ${userName}`, { align: 'center' });
        doc.moveDown();
        
        // Ajouter la période
        doc.fontSize(14).text(`Période: ${metrics.period.start.toLocaleDateString()} - ${metrics.period.end.toLocaleDateString()}`, { align: 'center' });
        doc.moveDown();
        
        // Ajouter les métriques de l'utilisateur
        doc.fontSize(18).text('Métriques Personnelles');
        doc.moveDown();
        
        doc.fontSize(12).text(`Nombre total de recommandations: ${metrics.totalRecommendations}`);
        doc.text(`Nombre total d'interactions: ${metrics.totalInteractions}`);
        doc.moveDown();
        
        // Ajouter les taux de conversion
        doc.fontSize(14).text('Taux de Conversion');
        doc.moveDown();
        
        doc.fontSize(12).text(`Taux de clics (CTR): ${metrics.rates.clickThroughRate.toFixed(2)}%`);
        doc.text(`Taux de conversion: ${metrics.rates.conversionRate.toFixed(2)}%`);
        doc.text(`Taux d'engagement: ${metrics.rates.engagementRate.toFixed(2)}%`);
        doc.moveDown();
        
        // Ajouter les interactions par type
        doc.fontSize(14).text('Interactions par Type');
        doc.moveDown();
        
        Object.entries(metrics.interactionCounts).forEach(([type, count]) => {
          doc.fontSize(12).text(`${type}: ${count}`);
        });
        doc.moveDown();
        
        // Ajouter les interactions par catégorie
        doc.fontSize(14).text('Interactions par Catégorie');
        doc.moveDown();
        
        metrics.categoryInteractions.forEach(category => {
          const preferredText = category.isPreferred ? ' (préférée)' : '';
          doc.fontSize(12).text(`${category.category}${preferredText}: ${category.count}`);
        });
        doc.moveDown();
        
        // Ajouter les préférences de l'utilisateur
        doc.fontSize(18).text('Préférences de l\'Utilisateur');
        doc.moveDown();
        
        // Stratégie préférée
        if (metrics.preferences.recommendationStrategy) {
          doc.fontSize(12).text(`Stratégie préférée: ${metrics.preferences.recommendationStrategy}`);
        }
        
        // Catégories préférées
        if (metrics.preferences.preferredCategories && metrics.preferences.preferredCategories.length > 0) {
          doc.fontSize(12).text(`Catégories préférées: ${metrics.preferences.preferredCategories.join(', ')}`);
        }
        
        // Tags préférés
        if (metrics.preferences.preferredTags && metrics.preferences.preferredTags.length > 0) {
          doc.fontSize(12).text(`Tags préférés: ${metrics.preferences.preferredTags.join(', ')}`);
        }
        
        // Finaliser le document
        doc.end();
        
        // Attendre la fin de l'écriture
        stream.on('finish', () => {
          resolve();
        });
        
        stream.on('error', (error) => {
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
}
