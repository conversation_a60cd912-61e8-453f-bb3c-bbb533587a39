import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { Prisma } from '@prisma/client';

/**
 * Service d'optimisation des requêtes pour les grands volumes de données
 * Fournit des méthodes pour optimiser les requêtes Prisma et gérer la pagination
 */
@Injectable()
export class QueryOptimizationService {
  private readonly logger = new Logger(QueryOptimizationService.name);
  private readonly defaultPageSize: number;
  private readonly maxPageSize: number;
  private readonly enableQueryCache: boolean;
  private readonly queryCacheTTL: number; // en secondes
  private readonly queryCache: Map<string, { data: any; timestamp: number }> = new Map();

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {
    this.defaultPageSize = this.configService.get<number>('recommendation.queryOptimization.defaultPageSize', 50);
    this.maxPageSize = this.configService.get<number>('recommendation.queryOptimization.maxPageSize', 500);
    this.enableQueryCache = this.configService.get<boolean>('recommendation.queryOptimization.enableQueryCache', true);
    this.queryCacheTTL = this.configService.get<number>('recommendation.queryOptimization.queryCacheTTL', 300); // 5 minutes par défaut
  }

  /**
   * Exécute une requête optimisée avec pagination et mise en cache
   * @param model Modèle Prisma à interroger
   * @param options Options de requête
   * @returns Résultats paginés
   */
  async executeOptimizedQuery<T>(
    model: any,
    options: {
      where?: any;
      select?: any;
      include?: any;
      orderBy?: any;
      page?: number;
      pageSize?: number;
      cacheKey?: string;
      forceFresh?: boolean;
    },
  ): Promise<{
    data: T[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    const {
      where = {},
      select,
      include,
      orderBy,
      page = 1,
      pageSize = this.defaultPageSize,
      cacheKey,
      forceFresh = false,
    } = options;

    // Limiter la taille de la page
    const limitedPageSize = Math.min(pageSize, this.maxPageSize);
    
    // Calculer le décalage pour la pagination
    const skip = (page - 1) * limitedPageSize;

    // Vérifier si les résultats sont en cache
    if (this.enableQueryCache && cacheKey && !forceFresh) {
      const cachedResult = this.queryCache.get(cacheKey);
      if (cachedResult && Date.now() - cachedResult.timestamp < this.queryCacheTTL * 1000) {
        this.logger.debug(`Utilisation des résultats en cache pour la clé: ${cacheKey}`);
        return cachedResult.data;
      }
    }

    // Exécuter la requête de comptage et la requête de données en parallèle
    const [total, data] = await Promise.all([
      model.count({ where }),
      model.findMany({
        where,
        select,
        include,
        orderBy,
        skip,
        take: limitedPageSize,
      }),
    ]);

    // Calculer le nombre total de pages
    const totalPages = Math.ceil(total / limitedPageSize);

    const result = {
      data,
      total,
      page,
      pageSize: limitedPageSize,
      totalPages,
    };

    // Mettre en cache les résultats si nécessaire
    if (this.enableQueryCache && cacheKey) {
      this.queryCache.set(cacheKey, {
        data: result,
        timestamp: Date.now(),
      });
    }

    return result;
  }

  /**
   * Exécute une requête optimisée avec curseur pour les grands ensembles de données
   * @param model Modèle Prisma à interroger
   * @param options Options de requête
   * @returns Résultats avec curseur
   */
  async executeOptimizedCursorQuery<T>(
    model: any,
    options: {
      where?: any;
      select?: any;
      include?: any;
      orderBy: any; // Obligatoire pour les requêtes avec curseur
      cursor?: any;
      take?: number;
      cacheKey?: string;
      forceFresh?: boolean;
    },
  ): Promise<{
    data: T[];
    nextCursor: any | null;
    prevCursor: any | null;
  }> {
    const {
      where = {},
      select,
      include,
      orderBy,
      cursor,
      take = this.defaultPageSize,
      cacheKey,
      forceFresh = false,
    } = options;

    // Limiter la taille de la page
    const limitedTake = Math.min(take, this.maxPageSize);

    // Vérifier si les résultats sont en cache
    if (this.enableQueryCache && cacheKey && !forceFresh) {
      const cachedResult = this.queryCache.get(cacheKey);
      if (cachedResult && Date.now() - cachedResult.timestamp < this.queryCacheTTL * 1000) {
        this.logger.debug(`Utilisation des résultats en cache pour la clé: ${cacheKey}`);
        return cachedResult.data;
      }
    }

    // Exécuter la requête avec curseur
    const data = await model.findMany({
      where,
      select,
      include,
      orderBy,
      cursor: cursor ? { id: cursor } : undefined,
      take: limitedTake + 1, // +1 pour vérifier s'il y a une page suivante
      skip: cursor ? 1 : 0, // Sauter le curseur actuel si présent
    });

    // Déterminer s'il y a une page suivante
    const hasNextPage = data.length > limitedTake;
    
    // Supprimer l'élément supplémentaire si présent
    if (hasNextPage) {
      data.pop();
    }

    // Déterminer les curseurs suivant et précédent
    const nextCursor = hasNextPage ? data[data.length - 1]?.id : null;
    const prevCursor = cursor;

    const result = {
      data,
      nextCursor,
      prevCursor,
    };

    // Mettre en cache les résultats si nécessaire
    if (this.enableQueryCache && cacheKey) {
      this.queryCache.set(cacheKey, {
        data: result,
        timestamp: Date.now(),
      });
    }

    return result;
  }

  /**
   * Exécute une requête par lots pour traiter de grands ensembles de données
   * @param model Modèle Prisma à interroger
   * @param options Options de requête
   * @param batchProcessor Fonction de traitement par lots
   * @returns Résultat du traitement par lots
   */
  async executeBatchQuery<T, R>(
    model: any,
    options: {
      where?: any;
      select?: any;
      orderBy?: any;
      batchSize?: number;
    },
    batchProcessor: (batch: T[]) => Promise<R[]>,
  ): Promise<R[]> {
    const {
      where = {},
      select,
      orderBy,
      batchSize = 100,
    } = options;

    // Compter le nombre total d'éléments
    const total = await model.count({ where });
    
    // Initialiser les résultats
    const results: R[] = [];
    
    // Traiter par lots
    for (let offset = 0; offset < total; offset += batchSize) {
      const batch = await model.findMany({
        where,
        select,
        orderBy,
        skip: offset,
        take: batchSize,
      });
      
      // Traiter le lot
      const batchResults = await batchProcessor(batch);
      
      // Ajouter les résultats du lot
      results.push(...batchResults);
      
      this.logger.debug(`Traitement du lot ${offset / batchSize + 1}/${Math.ceil(total / batchSize)}`);
    }
    
    return results;
  }

  /**
   * Invalide le cache pour une clé spécifique
   * @param cacheKey Clé de cache à invalider
   */
  invalidateCache(cacheKey: string): void {
    if (this.queryCache.has(cacheKey)) {
      this.queryCache.delete(cacheKey);
      this.logger.debug(`Cache invalidé pour la clé: ${cacheKey}`);
    }
  }

  /**
   * Invalide tout le cache
   */
  invalidateAllCache(): void {
    this.queryCache.clear();
    this.logger.debug('Cache entièrement invalidé');
  }

  /**
   * Génère une clé de cache basée sur les paramètres de requête
   * @param prefix Préfixe de la clé
   * @param params Paramètres de requête
   * @returns Clé de cache
   */
  generateCacheKey(prefix: string, params: any): string {
    return `${prefix}:${JSON.stringify(params)}`;
  }
}
