import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationOptions } from '../interfaces/recommendation-options.interface';
import { FactorizationMethod } from '../enums/factorization-method.enum';

/**
 * Service de recommandation basé sur la factorisation matricielle
 */
@Injectable()
export class MatrixFactorizationService {
  private readonly logger = new Logger(MatrixFactorizationService.name);
  
  // Matrices de facteurs latents
  private userFactors: Map<string, number[]> = new Map();
  private itemFactors: Map<string, number[]> = new Map();
  
  // Paramètres par défaut
  private readonly DEFAULT_NUM_FACTORS = 20;
  private readonly DEFAULT_REGULARIZATION = 0.1;
  private readonly DEFAULT_NUM_ITERATIONS = 50;
  private readonly DEFAULT_LEARNING_RATE = 0.01;
  
  constructor(private readonly prisma: PrismaService) {}
  
  /**
   * Génère des recommandations par factorisation matricielle pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Liste des recommandations
   */
  async getRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ) {
    try {
      this.logger.log(`Génération de recommandations par factorisation matricielle de type ${type} pour l'utilisateur ${userId}`);
      
      // Vérifier si les modèles sont entraînés
      if (!this.userFactors.has(userId)) {
        await this.trainModel(userId, type, options);
      }
      
      // Récupérer les facteurs de l'utilisateur
      const userFactors = this.userFactors.get(userId);
      if (!userFactors) {
        this.logger.warn(`Aucun facteur trouvé pour l'utilisateur ${userId}`);
        return [];
      }
      
      // Récupérer tous les items du type demandé
      const items = await this.getItemsByType(type);
      
      // Calculer les scores pour chaque item
      const recommendations = [];
      for (const item of items) {
        // Vérifier si l'item doit être exclu
        if (options.excludeIds && options.excludeIds.includes(item.id)) {
          continue;
        }
        
        // Récupérer les facteurs de l'item
        const itemFactors = this.itemFactors.get(item.id);
        if (!itemFactors) {
          continue;
        }
        
        // Calculer le score (produit scalaire)
        const score = this.dotProduct(userFactors, itemFactors);
        
        // Ajouter à la liste des recommandations
        recommendations.push({
          id: item.id,
          type,
          title: item.title,
          description: item.description,
          score,
          sources: ['matrix-factorization'],
          metadata: item.metadata || {},
        });
      }
      
      // Trier les recommandations par score et limiter le nombre de résultats
      const limit = options.limit || 10;
      return recommendations
        .sort((a, b) => b.score - a.score)
        .slice(0, limit);
    } catch (error) {
      this.logger.error(`Erreur lors de la génération de recommandations: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Compte le nombre de recommandations disponibles
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Nombre de recommandations
   */
  async countRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ): Promise<number> {
    // Récupérer tous les items du type demandé
    const items = await this.getItemsByType(type);
    
    // Filtrer les items exclus
    if (options.excludeIds && options.excludeIds.length > 0) {
      return items.filter(item => !options.excludeIds.includes(item.id)).length;
    }
    
    return items.length;
  }
  
  /**
   * Entraîne le modèle de factorisation matricielle
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   */
  private async trainModel(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ) {
    this.logger.log(`Entraînement du modèle de factorisation matricielle pour l'utilisateur ${userId}`);
    
    // Récupérer les paramètres
    const numFactors = options.numFactors || this.DEFAULT_NUM_FACTORS;
    const regularization = options.regularization || this.DEFAULT_REGULARIZATION;
    const numIterations = options.numIterations || this.DEFAULT_NUM_ITERATIONS;
    const learningRate = options.learningRate || this.DEFAULT_LEARNING_RATE;
    const factorizationMethod = options.factorizationMethod || FactorizationMethod.SVD;
    
    // Récupérer les interactions utilisateur-item
    const interactions = await this.getUserItemInteractions(userId, type);
    
    // Récupérer tous les utilisateurs et items
    const users = new Set([userId, ...interactions.map(i => i.userId)]);
    const items = new Set(interactions.map(i => i.itemId));
    
    // Initialiser les facteurs aléatoirement
    users.forEach(uid => {
      if (!this.userFactors.has(uid)) {
        this.userFactors.set(uid, this.randomFactors(numFactors));
      }
    });
    
    items.forEach(iid => {
      if (!this.itemFactors.has(iid)) {
        this.itemFactors.set(iid, this.randomFactors(numFactors));
      }
    });
    
    // Entraîner le modèle selon la méthode choisie
    switch (factorizationMethod) {
      case FactorizationMethod.ALS:
        await this.trainALS(interactions, numFactors, regularization, numIterations);
        break;
      case FactorizationMethod.NMF:
        await this.trainNMF(interactions, numFactors, numIterations, learningRate);
        break;
      case FactorizationMethod.BPR:
        await this.trainBPR(interactions, numFactors, learningRate, regularization, numIterations);
        break;
      case FactorizationMethod.SVD:
      default:
        await this.trainSVD(interactions, numFactors, learningRate, regularization, numIterations);
        break;
    }
    
    this.logger.log(`Modèle de factorisation matricielle entraîné pour l'utilisateur ${userId}`);
  }
  
  /**
   * Entraîne le modèle avec l'algorithme SVD
   */
  private async trainSVD(
    interactions: any[],
    numFactors: number,
    learningRate: number,
    regularization: number,
    numIterations: number,
  ) {
    // Implémentation simplifiée de SVD avec descente de gradient stochastique
    for (let iter = 0; iter < numIterations; iter++) {
      let rmse = 0;
      let count = 0;
      
      // Parcourir toutes les interactions
      for (const interaction of interactions) {
        const userId = interaction.userId;
        const itemId = interaction.itemId;
        const rating = this.getRatingFromInteraction(interaction);
        
        // Récupérer les facteurs
        const userFactors = this.userFactors.get(userId);
        const itemFactors = this.itemFactors.get(itemId);
        
        if (!userFactors || !itemFactors) continue;
        
        // Calculer la prédiction
        const prediction = this.dotProduct(userFactors, itemFactors);
        
        // Calculer l'erreur
        const error = rating - prediction;
        rmse += error * error;
        count++;
        
        // Mettre à jour les facteurs
        for (let f = 0; f < numFactors; f++) {
          const userFactor = userFactors[f];
          const itemFactor = itemFactors[f];
          
          userFactors[f] += learningRate * (error * itemFactor - regularization * userFactor);
          itemFactors[f] += learningRate * (error * userFactor - regularization * itemFactor);
        }
        
        // Mettre à jour les facteurs dans les maps
        this.userFactors.set(userId, userFactors);
        this.itemFactors.set(itemId, itemFactors);
      }
      
      // Calculer l'erreur moyenne
      if (count > 0) {
        rmse = Math.sqrt(rmse / count);
        if (iter % 10 === 0) {
          this.logger.debug(`Itération ${iter}, RMSE: ${rmse}`);
        }
      }
    }
  }
  
  /**
   * Entraîne le modèle avec l'algorithme ALS (Alternating Least Squares)
   */
  private async trainALS(
    interactions: any[],
    numFactors: number,
    regularization: number,
    numIterations: number,
  ) {
    // Implémentation simplifiée d'ALS
    // Dans une implémentation réelle, on utiliserait des bibliothèques d'algèbre linéaire
    this.logger.log('Entraînement avec ALS - version simplifiée');
    
    // Utiliser SVD comme fallback pour cette implémentation
    await this.trainSVD(interactions, numFactors, 0.01, regularization, numIterations);
  }
  
  /**
   * Entraîne le modèle avec l'algorithme NMF (Non-negative Matrix Factorization)
   */
  private async trainNMF(
    interactions: any[],
    numFactors: number,
    numIterations: number,
    learningRate: number,
  ) {
    // Implémentation simplifiée de NMF
    this.logger.log('Entraînement avec NMF - version simplifiée');
    
    // Utiliser SVD comme fallback pour cette implémentation
    await this.trainSVD(interactions, numFactors, learningRate, 0.1, numIterations);
  }
  
  /**
   * Entraîne le modèle avec l'algorithme BPR (Bayesian Personalized Ranking)
   */
  private async trainBPR(
    interactions: any[],
    numFactors: number,
    learningRate: number,
    regularization: number,
    numIterations: number,
  ) {
    // Implémentation simplifiée de BPR
    this.logger.log('Entraînement avec BPR - version simplifiée');
    
    // Utiliser SVD comme fallback pour cette implémentation
    await this.trainSVD(interactions, numFactors, learningRate, regularization, numIterations);
  }
  
  /**
   * Récupère les interactions utilisateur-item
   */
  private async getUserItemInteractions(userId: string, type: RecommendationType) {
    // Simuler la récupération des interactions
    // Dans une implémentation réelle, on utiliserait une requête à la base de données
    return [];
  }
  
  /**
   * Récupère les items par type
   */
  private async getItemsByType(type: RecommendationType) {
    // Simuler la récupération des items
    // Dans une implémentation réelle, on utiliserait une requête à la base de données
    return [];
  }
  
  /**
   * Calcule le produit scalaire de deux vecteurs
   */
  private dotProduct(vec1: number[], vec2: number[]): number {
    let sum = 0;
    for (let i = 0; i < vec1.length; i++) {
      sum += vec1[i] * vec2[i];
    }
    return sum;
  }
  
  /**
   * Génère un vecteur de facteurs aléatoires
   */
  private randomFactors(numFactors: number): number[] {
    return Array.from({ length: numFactors }, () => Math.random() * 0.1);
  }
  
  /**
   * Convertit une interaction en score numérique
   */
  private getRatingFromInteraction(interaction: any): number {
    // Convertir le type d'interaction en score numérique
    switch (interaction.interactionType) {
      case 'VIEW':
        return 1;
      case 'LIKE':
        return 3;
      case 'BOOKMARK':
        return 4;
      case 'PURCHASE':
      case 'ENROLL':
        return 5;
      default:
        return 0;
    }
  }
}
