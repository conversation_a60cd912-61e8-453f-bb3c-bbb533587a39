import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ExternalDataService } from './external-data.service';

/**
 * Interface pour les coordonnées géographiques
 */
export interface GeoCoordinates {
  /** Latitude */
  latitude: number;
  
  /** Longitude */
  longitude: number;
}

/**
 * Interface pour les données météorologiques
 */
export interface WeatherData {
  /** Température en degrés Celsius */
  temperature: number;
  
  /** Condition météorologique (ensoleillé, nuageux, pluvieux, etc.) */
  condition: string;
  
  /** Humidité en pourcentage */
  humidity: number;
  
  /** Vitesse du vent en km/h */
  windSpeed: number;
  
  /** Précipitations en mm */
  precipitation: number;
  
  /** Indice UV */
  uvIndex: number;
  
  /** Heure de lever du soleil */
  sunrise: string;
  
  /** Heure de coucher du soleil */
  sunset: string;
}

/**
 * Interface pour un événement local
 */
export interface LocalEvent {
  /** ID de l'événement */
  id: string;
  
  /** Nom de l'événement */
  name: string;
  
  /** Description de l'événement */
  description: string;
  
  /** Date de début */
  startDate: string;
  
  /** Date de fin */
  endDate: string;
  
  /** Lieu */
  location: string;
  
  /** Catégorie */
  category: string;
  
  /** URL de l'image */
  imageUrl?: string;
  
  /** URL de l'événement */
  eventUrl?: string;
}

/**
 * Interface pour les données de saison
 */
export interface SeasonData {
  /** Saison actuelle */
  currentSeason: 'spring' | 'summer' | 'autumn' | 'winter';
  
  /** Saison prochaine */
  nextSeason: 'spring' | 'summer' | 'autumn' | 'winter';
  
  /** Date de début de la saison actuelle */
  currentSeasonStartDate: string;
  
  /** Date de fin de la saison actuelle */
  currentSeasonEndDate: string;
  
  /** Jours restants dans la saison actuelle */
  daysRemainingInCurrentSeason: number;
  
  /** Saisons dans l'hémisphère nord */
  northernHemisphere: {
    spring: { start: string; end: string };
    summer: { start: string; end: string };
    autumn: { start: string; end: string };
    winter: { start: string; end: string };
  };
  
  /** Saisons dans l'hémisphère sud */
  southernHemisphere: {
    spring: { start: string; end: string };
    summer: { start: string; end: string };
    autumn: { start: string; end: string };
    winter: { start: string; end: string };
  };
}

/**
 * Interface pour les données culturelles
 */
export interface CulturalData {
  /** Jours fériés */
  holidays: Array<{
    name: string;
    date: string;
    description: string;
    country: string;
  }>;
  
  /** Événements culturels */
  culturalEvents: Array<{
    name: string;
    startDate: string;
    endDate: string;
    description: string;
    country: string;
  }>;
  
  /** Festivals */
  festivals: Array<{
    name: string;
    startDate: string;
    endDate: string;
    description: string;
    location: string;
  }>;
}

/**
 * Interface pour le contexte utilisateur
 */
export interface UserContext {
  /** ID de l'utilisateur */
  userId: string;
  
  /** Localisation */
  location?: {
    /** Pays */
    country: string;
    
    /** Ville */
    city: string;
    
    /** Coordonnées */
    coordinates: GeoCoordinates;
  };
  
  /** Données météorologiques */
  weather?: WeatherData;
  
  /** Événements locaux */
  localEvents?: LocalEvent[];
  
  /** Données de saison */
  seasonData?: SeasonData;
  
  /** Données culturelles */
  culturalData?: CulturalData;
  
  /** Appareil */
  device?: {
    /** Type d'appareil */
    type: 'mobile' | 'tablet' | 'desktop';
    
    /** Système d'exploitation */
    os: string;
    
    /** Navigateur */
    browser: string;
  };
  
  /** Heure locale */
  localTime?: {
    /** Heure */
    hour: number;
    
    /** Minute */
    minute: number;
    
    /** Jour de la semaine */
    dayOfWeek: number;
    
    /** Période de la journée */
    timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
  };
  
  /** Dernière mise à jour */
  lastUpdated: string;
}

/**
 * Service pour la détection de contexte utilisateur
 */
@Injectable()
export class ContextDetectionService {
  private readonly logger = new Logger(ContextDetectionService.name);
  private readonly weatherApiKey: string;
  private readonly weatherApiUrl: string;
  private readonly eventsApiKey: string;
  private readonly eventsApiUrl: string;
  private readonly contextCache: Map<string, UserContext> = new Map();
  private readonly cacheTtl: number = 3600000; // 1 heure en millisecondes
  
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly eventEmitter: EventEmitter2,
    private readonly externalDataService: ExternalDataService,
  ) {
    this.weatherApiKey = this.configService.get<string>('recommendation.context.weatherApiKey', '');
    this.weatherApiUrl = this.configService.get<string>('recommendation.context.weatherApiUrl', '');
    this.eventsApiKey = this.configService.get<string>('recommendation.context.eventsApiKey', '');
    this.eventsApiUrl = this.configService.get<string>('recommendation.context.eventsApiUrl', '');
  }
  
  /**
   * Détecte le contexte d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param forceRefresh Forcer le rafraîchissement du contexte
   * @returns Contexte utilisateur
   */
  async detectUserContext(userId: string, forceRefresh: boolean = false): Promise<UserContext> {
    try {
      // Vérifier si le contexte est en cache et à jour
      const cachedContext = this.contextCache.get(userId);
      const now = new Date();
      
      if (
        !forceRefresh &&
        cachedContext &&
        (now.getTime() - new Date(cachedContext.lastUpdated).getTime() < this.cacheTtl)
      ) {
        return cachedContext;
      }
      
      // Récupérer les informations de l'utilisateur
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          country: true,
          city: true,
          latitude: true,
          longitude: true,
          preferences: true,
        },
      });
      
      if (!user) {
        throw new Error(`Utilisateur avec l'ID ${userId} non trouvé`);
      }
      
      // Initialiser le contexte utilisateur
      const userContext: UserContext = {
        userId,
        lastUpdated: now.toISOString(),
      };
      
      // Ajouter la localisation si disponible
      if (user.city && user.country && user.latitude && user.longitude) {
        userContext.location = {
          country: user.country,
          city: user.city,
          coordinates: {
            latitude: user.latitude,
            longitude: user.longitude,
          },
        };
        
        // Récupérer les données météorologiques
        userContext.weather = await this.getWeatherData(userContext.location.coordinates);
        
        // Récupérer les événements locaux
        userContext.localEvents = await this.getLocalEvents(userContext.location);
      }
      
      // Récupérer les données de saison
      userContext.seasonData = this.getSeasonData(userContext.location?.coordinates);
      
      // Récupérer les données culturelles
      if (user.country) {
        userContext.culturalData = await this.getCulturalData(user.country);
      }
      
      // Détecter l'appareil et l'heure locale
      userContext.localTime = this.getLocalTime(userContext.location?.coordinates);
      
      // Mettre en cache le contexte
      this.contextCache.set(userId, userContext);
      
      // Émettre un événement de mise à jour du contexte
      this.eventEmitter.emit('user.context.updated', {
        userId,
        context: userContext,
      });
      
      return userContext;
    } catch (error) {
      this.logger.error(`Erreur lors de la détection du contexte utilisateur: ${error.message}`);
      
      // Retourner un contexte minimal en cas d'erreur
      return {
        userId,
        lastUpdated: new Date().toISOString(),
      };
    }
  }
  
  /**
   * Récupère les données météorologiques pour une localisation
   * @param coordinates Coordonnées géographiques
   * @returns Données météorologiques
   */
  private async getWeatherData(coordinates: GeoCoordinates): Promise<WeatherData> {
    try {
      if (!this.weatherApiKey || !this.weatherApiUrl) {
        return this.getMockWeatherData(coordinates);
      }
      
      const response = await firstValueFrom(
        this.httpService.get(this.weatherApiUrl, {
          params: {
            lat: coordinates.latitude,
            lon: coordinates.longitude,
            appid: this.weatherApiKey,
            units: 'metric',
          },
        }),
      );
      
      const data = response.data;
      
      return {
        temperature: data.main.temp,
        condition: data.weather[0].main,
        humidity: data.main.humidity,
        windSpeed: data.wind.speed,
        precipitation: data.rain ? data.rain['1h'] || 0 : 0,
        uvIndex: data.uvi || 0,
        sunrise: new Date(data.sys.sunrise * 1000).toISOString(),
        sunset: new Date(data.sys.sunset * 1000).toISOString(),
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des données météorologiques: ${error.message}`);
      return this.getMockWeatherData(coordinates);
    }
  }
  
  /**
   * Récupère les événements locaux pour une localisation
   * @param location Localisation
   * @returns Événements locaux
   */
  private async getLocalEvents(location: UserContext['location']): Promise<LocalEvent[]> {
    try {
      if (!this.eventsApiKey || !this.eventsApiUrl || !location) {
        return this.getMockLocalEvents(location);
      }
      
      const response = await firstValueFrom(
        this.httpService.get(this.eventsApiUrl, {
          params: {
            location: `${location.city},${location.country}`,
            apikey: this.eventsApiKey,
          },
        }),
      );
      
      const events = response.data._embedded?.events || [];
      
      return events.map((event: any) => ({
        id: event.id,
        name: event.name,
        description: event.description || '',
        startDate: event.dates.start.dateTime,
        endDate: event.dates.end?.dateTime || event.dates.start.dateTime,
        location: event._embedded?.venues[0]?.name || '',
        category: event.classifications[0]?.segment?.name || '',
        imageUrl: event.images[0]?.url,
        eventUrl: event.url,
      }));
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des événements locaux: ${error.message}`);
      return this.getMockLocalEvents(location);
    }
  }
  
  /**
   * Récupère les données de saison
   * @param coordinates Coordonnées géographiques
   * @returns Données de saison
   */
  private getSeasonData(coordinates?: GeoCoordinates): SeasonData {
    const now = new Date();
    const year = now.getFullYear();
    
    // Dates des saisons dans l'hémisphère nord
    const northernHemisphere = {
      spring: {
        start: `${year}-03-20`,
        end: `${year}-06-20`,
      },
      summer: {
        start: `${year}-06-21`,
        end: `${year}-09-22`,
      },
      autumn: {
        start: `${year}-09-23`,
        end: `${year}-12-20`,
      },
      winter: {
        start: `${year}-12-21`,
        end: `${year + 1}-03-19`,
      },
    };
    
    // Dates des saisons dans l'hémisphère sud
    const southernHemisphere = {
      spring: {
        start: `${year}-09-23`,
        end: `${year}-12-20`,
      },
      summer: {
        start: `${year}-12-21`,
        end: `${year + 1}-03-19`,
      },
      autumn: {
        start: `${year}-03-20`,
        end: `${year}-06-20`,
      },
      winter: {
        start: `${year}-06-21`,
        end: `${year}-09-22`,
      },
    };
    
    // Déterminer l'hémisphère
    const isNorthernHemisphere = !coordinates || coordinates.latitude >= 0;
    const hemisphere = isNorthernHemisphere ? northernHemisphere : southernHemisphere;
    
    // Déterminer la saison actuelle
    let currentSeason: 'spring' | 'summer' | 'autumn' | 'winter' = 'spring';
    let nextSeason: 'spring' | 'summer' | 'autumn' | 'winter' = 'summer';
    let currentSeasonStartDate = '';
    let currentSeasonEndDate = '';
    
    const nowStr = now.toISOString().split('T')[0];
    
    if (nowStr >= hemisphere.spring.start && nowStr <= hemisphere.spring.end) {
      currentSeason = 'spring';
      nextSeason = 'summer';
      currentSeasonStartDate = hemisphere.spring.start;
      currentSeasonEndDate = hemisphere.spring.end;
    } else if (nowStr >= hemisphere.summer.start && nowStr <= hemisphere.summer.end) {
      currentSeason = 'summer';
      nextSeason = 'autumn';
      currentSeasonStartDate = hemisphere.summer.start;
      currentSeasonEndDate = hemisphere.summer.end;
    } else if (nowStr >= hemisphere.autumn.start && nowStr <= hemisphere.autumn.end) {
      currentSeason = 'autumn';
      nextSeason = 'winter';
      currentSeasonStartDate = hemisphere.autumn.start;
      currentSeasonEndDate = hemisphere.autumn.end;
    } else {
      currentSeason = 'winter';
      nextSeason = 'spring';
      currentSeasonStartDate = hemisphere.winter.start;
      currentSeasonEndDate = hemisphere.winter.end;
    }
    
    // Calculer les jours restants dans la saison actuelle
    const endDate = new Date(currentSeasonEndDate);
    const daysRemainingInCurrentSeason = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    return {
      currentSeason,
      nextSeason,
      currentSeasonStartDate,
      currentSeasonEndDate,
      daysRemainingInCurrentSeason,
      northernHemisphere,
      southernHemisphere,
    };
  }
  
  /**
   * Récupère les données culturelles pour un pays
   * @param country Pays
   * @returns Données culturelles
   */
  private async getCulturalData(country: string): Promise<CulturalData> {
    try {
      // Dans une implémentation réelle, on récupérerait ces données depuis une API
      // Pour l'instant, on utilise des données fictives
      
      return {
        holidays: [
          {
            name: 'Nouvel An',
            date: `${new Date().getFullYear()}-01-01`,
            description: 'Jour de l\'an',
            country,
          },
          {
            name: 'Fête du Travail',
            date: `${new Date().getFullYear()}-05-01`,
            description: 'Journée internationale des travailleurs',
            country,
          },
        ],
        culturalEvents: [
          {
            name: 'Festival de Cannes',
            startDate: `${new Date().getFullYear()}-05-14`,
            endDate: `${new Date().getFullYear()}-05-25`,
            description: 'Festival international du film',
            country: 'France',
          },
        ],
        festivals: [
          {
            name: 'Coachella',
            startDate: `${new Date().getFullYear()}-04-10`,
            endDate: `${new Date().getFullYear()}-04-19`,
            description: 'Festival de musique et d\'arts',
            location: 'Indio, Californie',
          },
        ],
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des données culturelles: ${error.message}`);
      
      return {
        holidays: [],
        culturalEvents: [],
        festivals: [],
      };
    }
  }
  
  /**
   * Récupère l'heure locale
   * @param coordinates Coordonnées géographiques
   * @returns Heure locale
   */
  private getLocalTime(coordinates?: GeoCoordinates): UserContext['localTime'] {
    const now = new Date();
    
    // Dans une implémentation réelle, on utiliserait les coordonnées pour déterminer le fuseau horaire
    // Pour l'instant, on utilise l'heure locale du serveur
    
    const hour = now.getHours();
    const minute = now.getMinutes();
    const dayOfWeek = now.getDay();
    
    let timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
    
    if (hour >= 5 && hour < 12) {
      timeOfDay = 'morning';
    } else if (hour >= 12 && hour < 18) {
      timeOfDay = 'afternoon';
    } else if (hour >= 18 && hour < 22) {
      timeOfDay = 'evening';
    } else {
      timeOfDay = 'night';
    }
    
    return {
      hour,
      minute,
      dayOfWeek,
      timeOfDay,
    };
  }
  
  /**
   * Génère des données météorologiques fictives
   * @param coordinates Coordonnées géographiques
   * @returns Données météorologiques fictives
   */
  private getMockWeatherData(coordinates: GeoCoordinates): WeatherData {
    // Générer des données météorologiques aléatoires
    const conditions = ['sunny', 'cloudy', 'rainy', 'snowy', 'foggy'];
    const randomCondition = conditions[Math.floor(Math.random() * conditions.length)];
    
    return {
      temperature: Math.floor(Math.random() * 30) + 5, // 5-35°C
      condition: randomCondition,
      humidity: Math.floor(Math.random() * 60) + 30, // 30-90%
      windSpeed: Math.floor(Math.random() * 20) + 5, // 5-25 km/h
      precipitation: Math.floor(Math.random() * 10), // 0-10 mm
      uvIndex: Math.floor(Math.random() * 10) + 1, // 1-11
      sunrise: new Date(new Date().setHours(6, 0, 0, 0)).toISOString(),
      sunset: new Date(new Date().setHours(20, 0, 0, 0)).toISOString(),
    };
  }
  
  /**
   * Génère des événements locaux fictifs
   * @param location Localisation
   * @returns Événements locaux fictifs
   */
  private getMockLocalEvents(location?: UserContext['location']): LocalEvent[] {
    const city = location?.city || 'Paris';
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    return [
      {
        id: '1',
        name: `Concert à ${city}`,
        description: 'Un concert de musique classique',
        startDate: now.toISOString(),
        endDate: tomorrow.toISOString(),
        location: `Salle de concert de ${city}`,
        category: 'Music',
        imageUrl: 'https://example.com/concert.jpg',
        eventUrl: 'https://example.com/event/1',
      },
      {
        id: '2',
        name: `Exposition d'art à ${city}`,
        description: 'Une exposition d\'art contemporain',
        startDate: now.toISOString(),
        endDate: tomorrow.toISOString(),
        location: `Musée d'art de ${city}`,
        category: 'Art',
        imageUrl: 'https://example.com/art.jpg',
        eventUrl: 'https://example.com/event/2',
      },
    ];
  }
}
