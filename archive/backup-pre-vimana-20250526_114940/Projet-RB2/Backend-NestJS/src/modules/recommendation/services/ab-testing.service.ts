import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { ABTestGroup } from '../enums/ab-test-group.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { Cron, CronExpression } from '@nestjs/schedule';
import { createHash } from 'crypto';

/**
 * Service d'A/B testing pour les recommandations
 * Permet de comparer l'efficacité des différents algorithmes de recommandation
 */
@Injectable()
export class ABTestingService {
  private readonly logger = new Logger(ABTestingService.name);
  private readonly enabled: boolean;
  private readonly testId: string;
  private readonly testGroups: ABTestGroup[];
  private readonly groupDistribution: Map<ABTestGroup, number>;
  private readonly strategyMapping: Map<ABTestGroup, RecommendationStrategy>;
  private readonly startDate: Date;
  private readonly endDate: Date;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {
    this.enabled = this.configService.get<boolean>('recommendation.abTesting.enabled', false);
    this.testId = this.configService.get<string>('recommendation.abTesting.testId', 'recommendation-test-1');
    
    // Configurer les groupes de test
    this.testGroups = [
      ABTestGroup.CONTROL,
      ABTestGroup.MATRIX_FACTORIZATION,
      ABTestGroup.CONTEXTUAL,
      ABTestGroup.DEEP_LEARNING,
    ];
    
    // Configurer la distribution des groupes (pourcentages)
    this.groupDistribution = new Map([
      [ABTestGroup.CONTROL, 40], // 40% des utilisateurs
      [ABTestGroup.MATRIX_FACTORIZATION, 20], // 20% des utilisateurs
      [ABTestGroup.CONTEXTUAL, 20], // 20% des utilisateurs
      [ABTestGroup.DEEP_LEARNING, 20], // 20% des utilisateurs
    ]);
    
    // Configurer le mapping entre les groupes et les stratégies
    this.strategyMapping = new Map([
      [ABTestGroup.CONTROL, RecommendationStrategy.HYBRID],
      [ABTestGroup.MATRIX_FACTORIZATION, RecommendationStrategy.MATRIX_FACTORIZATION],
      [ABTestGroup.CONTEXTUAL, RecommendationStrategy.CONTEXTUAL],
      [ABTestGroup.DEEP_LEARNING, RecommendationStrategy.DEEP_LEARNING],
    ]);
    
    // Configurer les dates de début et de fin du test
    const startDateStr = this.configService.get<string>('recommendation.abTesting.startDate');
    const endDateStr = this.configService.get<string>('recommendation.abTesting.endDate');
    
    this.startDate = startDateStr ? new Date(startDateStr) : new Date();
    this.endDate = endDateStr ? new Date(endDateStr) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 jours par défaut
    
    this.logger.log(`ABTestingService initialized with enabled=${this.enabled}, testId=${this.testId}`);
    if (this.enabled) {
      this.logger.log(`Test period: ${this.startDate.toISOString()} to ${this.endDate.toISOString()}`);
      this.logger.log(`Group distribution: ${JSON.stringify(Object.fromEntries(this.groupDistribution))}`);
    }
  }

  /**
   * Détermine si l'A/B testing est actif
   * @returns true si l'A/B testing est actif
   */
  isActive(): boolean {
    if (!this.enabled) {
      return false;
    }
    
    const now = new Date();
    return now >= this.startDate && now <= this.endDate;
  }

  /**
   * Détermine le groupe de test pour un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Groupe de test
   */
  async getTestGroup(userId: string): Promise<ABTestGroup | null> {
    if (!this.isActive()) {
      return null;
    }
    
    try {
      // Vérifier si l'utilisateur a déjà un groupe assigné
      const existingAssignment = await this.prisma.abTestAssignment.findUnique({
        where: {
          userId_testId: {
            userId,
            testId: this.testId,
          },
        },
      });
      
      if (existingAssignment) {
        return existingAssignment.group as ABTestGroup;
      }
      
      // Assigner un nouveau groupe
      const group = this.assignGroup(userId);
      
      // Enregistrer l'assignation
      await this.prisma.abTestAssignment.create({
        data: {
          userId,
          testId: this.testId,
          group,
          assignedAt: new Date(),
        },
      });
      
      this.logger.debug(`Assigned user ${userId} to group ${group}`);
      return group;
    } catch (error) {
      this.logger.error(`Error assigning test group: ${error.message}`);
      return ABTestGroup.CONTROL; // Fallback au groupe de contrôle en cas d'erreur
    }
  }

  /**
   * Récupère la stratégie de recommandation pour un groupe de test
   * @param group Groupe de test
   * @returns Stratégie de recommandation
   */
  getStrategyForGroup(group: ABTestGroup): RecommendationStrategy {
    return this.strategyMapping.get(group) || RecommendationStrategy.HYBRID;
  }

  /**
   * Enregistre une interaction avec une recommandation
   * @param userId ID de l'utilisateur
   * @param itemId ID de l'item
   * @param interactionType Type d'interaction
   * @param group Groupe de test
   */
  async trackInteraction(
    userId: string,
    itemId: string,
    interactionType: string,
    group: ABTestGroup,
  ): Promise<void> {
    if (!this.isActive()) {
      return;
    }
    
    try {
      await this.prisma.abTestInteraction.create({
        data: {
          userId,
          itemId,
          testId: this.testId,
          group,
          interactionType,
          timestamp: new Date(),
        },
      });
      
      this.logger.debug(`Tracked ${interactionType} interaction for user ${userId} in group ${group}`);
    } catch (error) {
      this.logger.error(`Error tracking interaction: ${error.message}`);
    }
  }

  /**
   * Assigne un groupe de test à un utilisateur en fonction de la distribution configurée
   * @param userId ID de l'utilisateur
   * @returns Groupe de test
   */
  private assignGroup(userId: string): ABTestGroup {
    // Générer un hash déterministe basé sur l'ID utilisateur et l'ID du test
    const hash = createHash('sha256')
      .update(`${userId}:${this.testId}`)
      .digest('hex');
    
    // Convertir le hash en nombre entre 0 et 99
    const hashValue = parseInt(hash.substring(0, 8), 16) % 100;
    
    // Assigner le groupe en fonction de la distribution
    let cumulativePercentage = 0;
    
    for (const [group, percentage] of this.groupDistribution.entries()) {
      cumulativePercentage += percentage;
      if (hashValue < cumulativePercentage) {
        return group;
      }
    }
    
    // Fallback au groupe de contrôle
    return ABTestGroup.CONTROL;
  }

  /**
   * Calcule et enregistre les métriques de l'A/B testing
   * Exécuté tous les jours à 4h du matin
   */
  @Cron(CronExpression.EVERY_DAY_AT_4AM)
  async calculateMetrics(): Promise<void> {
    if (!this.isActive()) {
      return;
    }
    
    this.logger.log('Calculating A/B testing metrics');
    
    try {
      // Récupérer les statistiques pour chaque groupe
      const metrics = await Promise.all(
        this.testGroups.map(async (group) => {
          // Nombre d'utilisateurs dans le groupe
          const userCount = await this.prisma.abTestAssignment.count({
            where: {
              testId: this.testId,
              group,
            },
          });
          
          // Nombre d'interactions par type
          const interactionCounts = await this.prisma.abTestInteraction.groupBy({
            by: ['interactionType'],
            where: {
              testId: this.testId,
              group,
            },
            _count: {
              interactionType: true,
            },
          });
          
          // Calculer les taux de conversion
          const viewCount = interactionCounts.find(i => i.interactionType === 'VIEW')?._count.interactionType || 0;
          const likeCount = interactionCounts.find(i => i.interactionType === 'LIKE')?._count.interactionType || 0;
          const bookmarkCount = interactionCounts.find(i => i.interactionType === 'BOOKMARK')?._count.interactionType || 0;
          const enrollCount = interactionCounts.find(i => i.interactionType === 'ENROLL')?._count.interactionType || 0;
          const purchaseCount = interactionCounts.find(i => i.interactionType === 'PURCHASE')?._count.interactionType || 0;
          
          const conversionRate = viewCount > 0 
            ? ((likeCount + bookmarkCount + enrollCount + purchaseCount) / viewCount) * 100 
            : 0;
          
          return {
            group,
            userCount,
            interactionCounts: Object.fromEntries(
              interactionCounts.map(i => [i.interactionType, i._count.interactionType])
            ),
            conversionRate,
          };
        })
      );
      
      // Enregistrer les métriques
      await this.prisma.abTestMetrics.create({
        data: {
          testId: this.testId,
          timestamp: new Date(),
          metrics: metrics,
        },
      });
      
      this.logger.log('A/B testing metrics calculated and saved');
    } catch (error) {
      this.logger.error(`Error calculating A/B testing metrics: ${error.message}`);
    }
  }

  /**
   * Récupère les résultats de l'A/B testing
   * @returns Résultats de l'A/B testing
   */
  async getResults(): Promise<any> {
    if (!this.enabled) {
      return { enabled: false };
    }
    
    try {
      // Récupérer les dernières métriques
      const latestMetrics = await this.prisma.abTestMetrics.findFirst({
        where: {
          testId: this.testId,
        },
        orderBy: {
          timestamp: 'desc',
        },
      });
      
      // Récupérer les assignations de groupe
      const groupAssignments = await this.prisma.abTestAssignment.groupBy({
        by: ['group'],
        where: {
          testId: this.testId,
        },
        _count: {
          userId: true,
        },
      });
      
      // Récupérer les interactions
      const interactions = await this.prisma.abTestInteraction.groupBy({
        by: ['group', 'interactionType'],
        where: {
          testId: this.testId,
        },
        _count: {
          id: true,
        },
      });
      
      return {
        enabled: this.enabled,
        active: this.isActive(),
        testId: this.testId,
        startDate: this.startDate,
        endDate: this.endDate,
        groups: this.testGroups,
        groupDistribution: Object.fromEntries(this.groupDistribution),
        strategyMapping: Object.fromEntries(this.strategyMapping),
        groupAssignments: groupAssignments.map(g => ({
          group: g.group,
          userCount: g._count.userId,
        })),
        interactions: interactions.map(i => ({
          group: i.group,
          interactionType: i.interactionType,
          count: i._count.id,
        })),
        latestMetrics: latestMetrics?.metrics || [],
      };
    } catch (error) {
      this.logger.error(`Error getting A/B testing results: ${error.message}`);
      return { enabled: this.enabled, error: error.message };
    }
  }
}
