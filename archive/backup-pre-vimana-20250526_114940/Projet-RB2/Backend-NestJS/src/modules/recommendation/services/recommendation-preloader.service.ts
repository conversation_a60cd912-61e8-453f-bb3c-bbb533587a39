import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationService } from './recommendation.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { RecommendationCacheService } from './recommendation-cache.service';

/**
 * Service de préchargement des recommandations
 * Précharge les recommandations pour les utilisateurs actifs afin d'améliorer les performances
 */
@Injectable()
export class RecommendationPreloaderService {
  private readonly logger = new Logger(RecommendationPreloaderService.name);
  private readonly enabled: boolean;
  private readonly batchSize: number;
  private readonly activeUserThreshold: number; // Nombre de jours pour considérer un utilisateur comme actif
  private readonly strategies: RecommendationStrategy[];
  private readonly types: RecommendationType[];
  private isRunning = false;

  constructor(
    private readonly prisma: PrismaService,
    private readonly recommendationService: RecommendationService,
    private readonly recommendationCacheService: RecommendationCacheService,
    private readonly configService: ConfigService,
  ) {
    this.enabled = this.configService.get<boolean>('recommendation.preloader.enabled', true);
    this.batchSize = this.configService.get<number>('recommendation.preloader.batchSize', 50);
    this.activeUserThreshold = this.configService.get<number>('recommendation.preloader.activeUserThreshold', 30);

    // Stratégies à précharger
    this.strategies = [
      RecommendationStrategy.HYBRID,
      RecommendationStrategy.CONTENT_BASED,
      RecommendationStrategy.MATRIX_FACTORIZATION,
    ];

    // Types à précharger
    this.types = [
      RecommendationType.COURSE,
      RecommendationType.RETREAT,
    ];

    this.logger.log(`RecommendationPreloaderService initialized with enabled=${this.enabled}, batchSize=${this.batchSize}`);
  }

  /**
   * Précharge les recommandations pour les utilisateurs actifs
   * Exécuté tous les jours à 3h du matin
   */
  @Cron(CronExpression.EVERY_DAY_AT_3AM)
  async preloadForActiveUsers(): Promise<void> {
    if (!this.enabled || this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.logger.log('Starting preloading recommendations for active users');

    try {
      // Récupérer d'abord les utilisateurs premium actifs (priorité plus élevée)
      const premiumUsers = await this.getPremiumActiveUsers();
      this.logger.log(`Found ${premiumUsers.length} premium active users for preloading`);

      // Précharger les recommandations pour les utilisateurs premium
      if (premiumUsers.length > 0) {
        for (let i = 0; i < premiumUsers.length; i += this.batchSize) {
          const batch = premiumUsers.slice(i, i + this.batchSize);
          await this.preloadBatch(batch);
          this.logger.log(`Preloaded recommendations for premium batch ${i / this.batchSize + 1}/${Math.ceil(premiumUsers.length / this.batchSize)}`);
        }
      }

      // Récupérer ensuite les utilisateurs standard actifs
      const activeUsers = await this.getActiveUsers();

      // Filtrer pour exclure les utilisateurs premium déjà traités
      const premiumUserIds = new Set(premiumUsers);
      const standardUsers = activeUsers.filter(userId => !premiumUserIds.has(userId));

      this.logger.log(`Found ${standardUsers.length} standard active users for preloading`);

      // Précharger les recommandations pour les utilisateurs standard
      if (standardUsers.length > 0) {
        for (let i = 0; i < standardUsers.length; i += this.batchSize) {
          const batch = standardUsers.slice(i, i + this.batchSize);
          await this.preloadBatch(batch);
          this.logger.log(`Preloaded recommendations for standard batch ${i / this.batchSize + 1}/${Math.ceil(standardUsers.length / this.batchSize)}`);
        }
      }

      this.logger.log('Finished preloading recommendations for all active users');
    } catch (error) {
      this.logger.error(`Error preloading recommendations: ${error.message}`);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Précharge les recommandations pour un utilisateur spécifique
   * @param userId ID de l'utilisateur
   */
  async preloadForUser(userId: string): Promise<void> {
    if (!this.enabled) {
      return;
    }

    this.logger.log(`Preloading recommendations for user ${userId}`);

    try {
      // Précharger pour chaque type et stratégie
      for (const type of this.types) {
        for (const strategy of this.strategies) {
          await this.preloadRecommendation(userId, type, strategy);
        }
      }

      this.logger.log(`Finished preloading recommendations for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error preloading recommendations for user ${userId}: ${error.message}`);
    }
  }

  /**
   * Précharge une recommandation spécifique pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param strategy Stratégie de recommandation
   */
  private async preloadRecommendation(
    userId: string,
    type: RecommendationType,
    strategy: RecommendationStrategy,
  ): Promise<void> {
    try {
      // Générer les recommandations
      const recommendations = await this.recommendationService.getRecommendations(
        userId,
        type,
        {
          strategy,
          limit: 20,
          includeMetadata: true,
        },
      );

      this.logger.debug(`Preloaded ${recommendations.length} ${strategy} recommendations of type ${type} for user ${userId}`);
    } catch (error) {
      this.logger.warn(`Error preloading ${strategy} recommendations of type ${type} for user ${userId}: ${error.message}`);
    }
  }

  /**
   * Précharge les recommandations pour un lot d'utilisateurs
   * @param userIds IDs des utilisateurs
   */
  private async preloadBatch(userIds: string[]): Promise<void> {
    // Précharger par type et stratégie pour tous les utilisateurs en même temps
    // Cette approche est plus efficace que de précharger utilisateur par utilisateur
    for (const type of this.types) {
      for (const strategy of this.strategies) {
        await this.preloadBatchForTypeAndStrategy(userIds, type, strategy);
      }
    }
  }

  /**
   * Précharge les recommandations d'un type et d'une stratégie spécifiques pour un lot d'utilisateurs
   * @param userIds IDs des utilisateurs
   * @param type Type de recommandation
   * @param strategy Stratégie de recommandation
   */
  private async preloadBatchForTypeAndStrategy(
    userIds: string[],
    type: RecommendationType,
    strategy: RecommendationStrategy,
  ): Promise<void> {
    try {
      this.logger.debug(`Préchargement en batch pour ${userIds.length} utilisateurs (type=${type}, strategy=${strategy})`);

      // Diviser en sous-lots pour éviter de surcharger le système
      const subBatchSize = 10;

      for (let i = 0; i < userIds.length; i += subBatchSize) {
        const subBatch = userIds.slice(i, i + subBatchSize);

        // Précharger en parallèle pour chaque utilisateur du sous-lot
        await Promise.all(
          subBatch.map(userId =>
            this.preloadRecommendation(userId, type, strategy)
          )
        );
      }
    } catch (error) {
      this.logger.error(`Erreur lors du préchargement en batch (type=${type}, strategy=${strategy}): ${error.message}`);
    }
  }

  /**
   * Récupère les utilisateurs actifs
   * @returns Liste des IDs des utilisateurs actifs
   */
  private async getActiveUsers(): Promise<string[]> {
    // Calculer la date limite pour considérer un utilisateur comme actif
    const activeThreshold = new Date();
    activeThreshold.setDate(activeThreshold.getDate() - this.activeUserThreshold);

    // Récupérer les utilisateurs qui ont eu une interaction récente
    // Optimisation: utiliser une requête plus efficace avec agrégation
    const activeUsers = await this.prisma.$queryRaw<{ userId: string; interactionCount: number }[]>`
      SELECT "userId", COUNT(*) as "interactionCount"
      FROM "UserInteraction"
      WHERE "createdAt" >= ${activeThreshold}
      GROUP BY "userId"
      ORDER BY "interactionCount" DESC
      LIMIT 1000
    `;

    // Trier par nombre d'interactions pour prioriser les utilisateurs les plus actifs
    return activeUsers.map(user => user.userId);
  }

  /**
   * Récupère les utilisateurs premium actifs
   * Priorité plus élevée pour le préchargement
   * @returns Liste des IDs des utilisateurs premium actifs
   */
  private async getPremiumActiveUsers(): Promise<string[]> {
    // Calculer la date limite pour considérer un utilisateur comme actif
    const activeThreshold = new Date();
    activeThreshold.setDate(activeThreshold.getDate() - this.activeUserThreshold);

    // Récupérer les utilisateurs premium qui ont eu une interaction récente
    const premiumUsers = await this.prisma.user.findMany({
      where: {
        subscriptionStatus: 'PREMIUM',
        userInteractions: {
          some: {
            createdAt: {
              gte: activeThreshold,
            },
          },
        },
      },
      select: {
        id: true,
      },
      take: 500, // Limiter le nombre d'utilisateurs premium
    });

    return premiumUsers.map(user => user.id);
  }

  /**
   * Précharge manuellement les recommandations pour tous les utilisateurs actifs
   * Peut être appelé via une API d'administration
   */
  async manualPreload(): Promise<{ success: boolean; message: string }> {
    if (this.isRunning) {
      return { success: false, message: 'Preloading is already running' };
    }

    // Lancer le préchargement en arrière-plan
    this.preloadForActiveUsers().catch(error => {
      this.logger.error(`Error in manual preload: ${error.message}`);
    });

    return { success: true, message: 'Preloading started in background' };
  }
}
