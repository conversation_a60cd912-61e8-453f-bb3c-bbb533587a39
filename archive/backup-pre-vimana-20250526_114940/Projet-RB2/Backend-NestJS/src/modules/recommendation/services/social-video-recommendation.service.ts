import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationOptions } from '../interfaces/recommendation-options.interface';
import { ContentBasedService } from './content-based.service';

@Injectable()
export class SocialVideoRecommendationService {
  private readonly logger = new Logger(SocialVideoRecommendationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly contentBasedService: ContentBasedService,
  ) {}

  /**
   * Génère des recommandations de vidéos pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param options Options de recommandation
   * @returns Liste des recommandations de vidéos
   */
  async getVideoRecommendations(
    userId: string,
    options: RecommendationOptions = {},
  ) {
    try {
      this.logger.log(`Génération de recommandations de vidéos pour l'utilisateur ${userId}`);

      // Récupérer le profil de l'utilisateur
      const userProfile = await this.getUserProfile(userId);

      // Récupérer les vidéos déjà vues par l'utilisateur
      const viewedVideos = await this.prisma.userInteraction.findMany({
        where: {
          userId,
          itemType: 'VIDEO',
          interactionType: 'VIEW',
        },
        select: {
          itemId: true,
        },
      });

      const viewedVideoIds = viewedVideos.map(interaction => interaction.itemId);

      // Récupérer toutes les vidéos non vues
      const allVideos = await this.prisma.video.findMany({
        where: {
          id: { notIn: viewedVideoIds },
        },
        include: {
          creator: true,
          tags: true,
          _count: {
            select: {
              likes: true,
              comments: true,
              views: true,
            },
          },
        },
        take: options.limit ? options.limit * 3 : 30, // Récupérer plus de vidéos pour le filtrage
      });

      // Calculer le score de chaque vidéo
      const scoredVideos = allVideos.map(video => {
        let score = 0;

        // Score basé sur la catégorie
        if (video.category && userProfile.categories) {
          const categoryScore = userProfile.categories[video.category] || 0;
          score += categoryScore * 0.3;
        }

        // Score basé sur les tags
        if (video.tags && userProfile.tags) {
          const tagScores = video.tags.map(tag => userProfile.tags[tag.name] || 0);
          const avgTagScore = tagScores.length > 0
            ? tagScores.reduce((sum, score) => sum + score, 0) / tagScores.length
            : 0;
          score += avgTagScore * 0.2;
        }

        // Score basé sur la popularité
        const popularityScore = (
          (video._count.views / 1000) * 0.4 +
          (video._count.likes / 100) * 0.4 +
          (video._count.comments / 10) * 0.2
        ) / 10;
        score += popularityScore * 0.2;

        // Score basé sur la fraîcheur
        const ageInDays = (Date.now() - new Date(video.createdAt).getTime()) / (1000 * 60 * 60 * 24);
        const freshnessScore = Math.max(0, 1 - (ageInDays / 30)); // Décroît linéairement sur 30 jours
        score += freshnessScore * 0.2;

        // Score basé sur les créateurs suivis
        if (userProfile.followedCreators && userProfile.followedCreators.includes(video.creatorId)) {
          score += 0.1;
        }

        return {
          id: video.id,
          type: 'VIDEO',
          title: video.title,
          description: video.description,
          thumbnailUrl: video.thumbnailUrl,
          duration: video.duration,
          createdAt: video.createdAt,
          views: video._count.views,
          likes: video._count.likes,
          comments: video._count.comments,
          creator: {
            id: video.creator.id,
            name: video.creator.name,
            username: video.creator.username,
            avatarUrl: video.creator.avatarUrl,
            isVerified: video.creator.isVerified,
          },
          tags: video.tags.map(tag => tag.name),
          category: video.category,
          score,
          isNew: ageInDays < 7,
          isTrending: (video._count.views > 1000 && video._count.likes > 100) || popularityScore > 0.7,
          recommendationReason: this.generateRecommendationReason(video, userProfile),
        };
      });

      // Trier les vidéos par score et limiter le nombre de résultats
      const recommendations = scoredVideos
        .sort((a, b) => b.score - a.score)
        .slice(0, options.limit || 10);

      this.logger.log(`${recommendations.length} recommandations de vidéos générées pour l'utilisateur ${userId}`);
      return recommendations;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des recommandations de vidéos: ${error.message}`);
      throw error;
    }
  }

  /**
   * Génère des recommandations de vidéos tendance
   * @param options Options de recommandation
   * @returns Liste des recommandations de vidéos tendance
   */
  async getTrendingVideos(options: RecommendationOptions = {}) {
    try {
      this.logger.log('Génération de recommandations de vidéos tendance');

      const limit = options.limit || 10;

      // Récupérer les vidéos les plus populaires
      const trendingVideos = await this.prisma.video.findMany({
        orderBy: [
          { viewCount: 'desc' },
          { likeCount: 'desc' },
        ],
        include: {
          creator: true,
          tags: true,
          _count: {
            select: {
              likes: true,
              comments: true,
              views: true,
            },
          },
        },
        take: limit,
      });

      // Transformer les résultats
      const recommendations = trendingVideos.map(video => {
        const ageInDays = (Date.now() - new Date(video.createdAt).getTime()) / (1000 * 60 * 60 * 24);

        return {
          id: video.id,
          type: 'VIDEO',
          title: video.title,
          description: video.description,
          thumbnailUrl: video.thumbnailUrl,
          duration: video.duration,
          createdAt: video.createdAt,
          views: video._count.views,
          likes: video._count.likes,
          comments: video._count.comments,
          creator: {
            id: video.creator.id,
            name: video.creator.name,
            username: video.creator.username,
            avatarUrl: video.creator.avatarUrl,
            isVerified: video.creator.isVerified,
          },
          tags: video.tags.map(tag => tag.name),
          category: video.category,
          score: 1 - (0.1 * Math.floor(Math.random() * 5)), // Score aléatoire entre 0.5 et 1
          isNew: ageInDays < 7,
          isTrending: true,
          recommendationReason: 'Trending now',
        };
      });

      this.logger.log(`${recommendations.length} recommandations de vidéos tendance générées`);
      return recommendations;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des recommandations de vidéos tendance: ${error.message}`);
      throw error;
    }
  }

  /**
   * Génère des recommandations de vidéos similaires à une vidéo
   * @param videoId ID de la vidéo
   * @param options Options de recommandation
   * @returns Liste des recommandations de vidéos similaires
   */
  async getSimilarVideos(videoId: string, options: RecommendationOptions = {}) {
    try {
      this.logger.log(`Génération de vidéos similaires à ${videoId}`);

      const limit = options.limit || 10;

      // Récupérer la vidéo
      const video = await this.prisma.video.findUnique({
        where: { id: videoId },
        include: {
          tags: true,
        },
      });

      if (!video) {
        throw new Error(`Vidéo ${videoId} non trouvée`);
      }

      // Récupérer toutes les vidéos sauf celle spécifiée
      const allVideos = await this.prisma.video.findMany({
        where: {
          id: { not: videoId },
        },
        include: {
          creator: true,
          tags: true,
          _count: {
            select: {
              likes: true,
              comments: true,
              views: true,
            },
          },
        },
        take: options.limit ? options.limit * 3 : 30, // Récupérer plus de vidéos pour le filtrage
      });

      // Calculer la similarité de chaque vidéo
      const similarVideos = allVideos.map(otherVideo => {
        // Similarité basée sur la catégorie
        let categorySimilarity = 0;
        if (video.category && otherVideo.category && video.category === otherVideo.category) {
          categorySimilarity = 1;
        }

        // Similarité basée sur les tags
        const videoTags = new Set(video.tags.map(tag => tag.name));
        const otherVideoTags = new Set(otherVideo.tags.map(tag => tag.name));

        const commonTags = [...videoTags].filter(tag => otherVideoTags.has(tag));
        const allTags = new Set([...videoTags, ...otherVideoTags]);

        const tagSimilarity = allTags.size > 0 ? commonTags.length / allTags.size : 0;

        // Similarité basée sur le créateur
        const creatorSimilarity = video.creatorId === otherVideo.creatorId ? 1 : 0;

        // Score de similarité global
        const similarityScore = (
          categorySimilarity * 0.3 +
          tagSimilarity * 0.5 +
          creatorSimilarity * 0.2
        );

        const ageInDays = (Date.now() - new Date(otherVideo.createdAt).getTime()) / (1000 * 60 * 60 * 24);

        return {
          id: otherVideo.id,
          type: 'VIDEO',
          title: otherVideo.title,
          description: otherVideo.description,
          thumbnailUrl: otherVideo.thumbnailUrl,
          duration: otherVideo.duration,
          createdAt: otherVideo.createdAt,
          views: otherVideo._count.views,
          likes: otherVideo._count.likes,
          comments: otherVideo._count.comments,
          creator: {
            id: otherVideo.creator.id,
            name: otherVideo.creator.name,
            username: otherVideo.creator.username,
            avatarUrl: otherVideo.creator.avatarUrl,
            isVerified: otherVideo.creator.isVerified,
          },
          tags: otherVideo.tags.map(tag => tag.name),
          category: otherVideo.category,
          score: similarityScore,
          isNew: ageInDays < 7,
          isTrending: (otherVideo._count.views > 1000 && otherVideo._count.likes > 100),
          recommendationReason: this.generateSimilarityReason(video, otherVideo),
        };
      });

      // Trier les vidéos par similarité et limiter le nombre de résultats
      const recommendations = similarVideos
        .sort((a, b) => b.score - a.score)
        .slice(0, limit);

      this.logger.log(`${recommendations.length} vidéos similaires générées pour ${videoId}`);
      return recommendations;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération de vidéos similaires: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère le profil d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Profil de l'utilisateur
   */
  private async getUserProfile(userId: string) {
    // Récupérer le profil de l'utilisateur
    let userProfile = await this.prisma.userProfile.findUnique({
      where: { userId },
    });

    // Si le profil n'existe pas, utiliser un profil vide
    if (!userProfile) {
      return {};
    }

    // Récupérer les créateurs suivis
    const followedCreators = await this.prisma.follow.findMany({
      where: { followerId: userId },
      select: { followingId: true },
    });

    return {
      ...userProfile.preferences,
      followedCreators: followedCreators.map(follow => follow.followingId),
    };
  }

  /**
   * Génère une raison de recommandation pour une vidéo
   * @param video Vidéo recommandée
   * @param userProfile Profil de l'utilisateur
   * @returns Raison de recommandation
   */
  private generateRecommendationReason(video: any, userProfile: any) {
    // Raisons possibles
    const reasons = [];

    // Raison basée sur la catégorie
    if (video.category && userProfile.categories && userProfile.categories[video.category]) {
      reasons.push(`Based on your interest in ${video.category}`);
    }

    // Raison basée sur les tags
    if (video.tags && userProfile.tags) {
      const matchingTags = video.tags.filter(tag => userProfile.tags[tag.name]);
      if (matchingTags.length > 0) {
        reasons.push(`Because you're interested in ${matchingTags[0].name}`);
      }
    }

    // Raison basée sur le créateur
    if (userProfile.followedCreators && userProfile.followedCreators.includes(video.creatorId)) {
      reasons.push(`From ${video.creator.name} who you follow`);
    }

    // Raison basée sur la popularité
    if (video._count.views > 1000 && video._count.likes > 100) {
      reasons.push('Popular in your community');
    }

    // Raison basée sur la fraîcheur
    const ageInDays = (Date.now() - new Date(video.createdAt).getTime()) / (1000 * 60 * 60 * 24);
    if (ageInDays < 3) {
      reasons.push('New release');
    }

    // Sélectionner une raison aléatoire ou une raison par défaut
    return reasons.length > 0
      ? reasons[Math.floor(Math.random() * reasons.length)]
      : 'Recommended for you';
  }

  /**
   * Génère une raison de similarité entre deux vidéos
   * @param video1 Première vidéo
   * @param video2 Deuxième vidéo
   * @returns Raison de similarité
   */
  private generateSimilarityReason(video1: any, video2: any) {
    // Raisons possibles
    const reasons = [];

    // Raison basée sur la catégorie
    if (video1.category && video2.category && video1.category === video2.category) {
      reasons.push(`Similar ${video1.category} content`);
    }

    // Raison basée sur les tags
    const video1Tags = new Set(video1.tags.map(tag => tag.name));
    const video2Tags = new Set(video2.tags.map(tag => tag.name));

    const commonTags = [...video1Tags].filter(tag => video2Tags.has(tag));
    if (commonTags.length > 0) {
      reasons.push(`Also tagged with ${commonTags[0]}`);
    }

    // Raison basée sur le créateur
    if (video1.creatorId === video2.creatorId) {
      reasons.push(`More from ${video2.creator.name}`);
    }

    // Sélectionner une raison aléatoire ou une raison par défaut
    return reasons.length > 0
      ? reasons[Math.floor(Math.random() * reasons.length)]
      : 'Similar content you might like';
  }
}
