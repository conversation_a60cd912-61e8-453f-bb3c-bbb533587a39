import { Test, TestingModule } from '@nestjs/testing';
import { QueryOptimizationService } from './query-optimization.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';

describe('QueryOptimizationService', () => {
  let service: QueryOptimizationService;
  let prismaService: PrismaService;
  let configService: ConfigService;

  // Mock du modèle Prisma
  const mockPrismaModel = {
    findMany: jest.fn(),
    count: jest.fn(),
  };

  beforeEach(async () => {
    // Créer des mocks pour les dépendances
    const mockPrismaService = {
      recommendation: mockPrismaModel,
      explanation: mockPrismaModel,
      $transaction: jest.fn((callback) => callback()),
    };

    const mockConfigService = {
      get: jest.fn((key, defaultValue) => {
        const config = {
          'recommendation.queryOptimization.defaultPageSize': 50,
          'recommendation.queryOptimization.maxPageSize': 500,
          'recommendation.queryOptimization.enableQueryCache': true,
          'recommendation.queryOptimization.queryCacheTTL': 300,
        };
        return config[key] !== undefined ? config[key] : defaultValue;
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QueryOptimizationService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    // Désactiver les logs pendant les tests
    jest.spyOn(Logger.prototype, 'debug').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});

    service = module.get<QueryOptimizationService>(QueryOptimizationService);
    prismaService = module.get<PrismaService>(PrismaService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('executeOptimizedQuery', () => {
    it('should execute a paginated query with correct parameters', async () => {
      // Configurer les mocks
      mockPrismaModel.count.mockResolvedValue(100);
      mockPrismaModel.findMany.mockResolvedValue([{ id: 1 }, { id: 2 }]);

      // Exécuter la requête
      const result = await service.executeOptimizedQuery(mockPrismaModel, {
        where: { userId: '123' },
        page: 2,
        pageSize: 10,
      });

      // Vérifier que les méthodes ont été appelées avec les bons paramètres
      expect(mockPrismaModel.count).toHaveBeenCalledWith({
        where: { userId: '123' },
      });
      expect(mockPrismaModel.findMany).toHaveBeenCalledWith({
        where: { userId: '123' },
        select: undefined,
        include: undefined,
        orderBy: undefined,
        skip: 10, // (page - 1) * pageSize
        take: 10,
      });

      // Vérifier le résultat
      expect(result).toEqual({
        data: [{ id: 1 }, { id: 2 }],
        total: 100,
        page: 2,
        pageSize: 10,
        totalPages: 10,
      });
    });

    it('should limit page size to maxPageSize', async () => {
      // Configurer les mocks
      mockPrismaModel.count.mockResolvedValue(1000);
      mockPrismaModel.findMany.mockResolvedValue([{ id: 1 }]);

      // Exécuter la requête avec une taille de page trop grande
      const result = await service.executeOptimizedQuery(mockPrismaModel, {
        where: {},
        pageSize: 1000, // Supérieur à maxPageSize (500)
      });

      // Vérifier que la taille de page a été limitée
      expect(mockPrismaModel.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          take: 500, // Limité à maxPageSize
        }),
      );

      // Vérifier le résultat
      expect(result.pageSize).toBe(500);
    });

    it('should use cache when available', async () => {
      // Configurer les mocks
      mockPrismaModel.count.mockResolvedValue(100);
      mockPrismaModel.findMany.mockResolvedValue([{ id: 1 }]);

      // Première requête (mise en cache)
      await service.executeOptimizedQuery(mockPrismaModel, {
        where: { userId: '123' },
        cacheKey: 'test-cache-key',
      });

      // Réinitialiser les mocks
      mockPrismaModel.count.mockClear();
      mockPrismaModel.findMany.mockClear();

      // Deuxième requête (utilisation du cache)
      await service.executeOptimizedQuery(mockPrismaModel, {
        where: { userId: '123' },
        cacheKey: 'test-cache-key',
      });

      // Vérifier que les méthodes n'ont pas été appelées à nouveau
      expect(mockPrismaModel.count).not.toHaveBeenCalled();
      expect(mockPrismaModel.findMany).not.toHaveBeenCalled();
    });

    it('should ignore cache when forceFresh is true', async () => {
      // Configurer les mocks
      mockPrismaModel.count.mockResolvedValue(100);
      mockPrismaModel.findMany.mockResolvedValue([{ id: 1 }]);

      // Première requête (mise en cache)
      await service.executeOptimizedQuery(mockPrismaModel, {
        where: { userId: '123' },
        cacheKey: 'test-cache-key',
      });

      // Réinitialiser les mocks
      mockPrismaModel.count.mockClear();
      mockPrismaModel.findMany.mockClear();

      // Deuxième requête avec forceFresh
      await service.executeOptimizedQuery(mockPrismaModel, {
        where: { userId: '123' },
        cacheKey: 'test-cache-key',
        forceFresh: true,
      });

      // Vérifier que les méthodes ont été appelées à nouveau
      expect(mockPrismaModel.count).toHaveBeenCalled();
      expect(mockPrismaModel.findMany).toHaveBeenCalled();
    });
  });

  describe('executeOptimizedCursorQuery', () => {
    it('should execute a cursor-based query with correct parameters', async () => {
      // Configurer les mocks
      mockPrismaModel.findMany.mockResolvedValue([{ id: 1 }, { id: 2 }]);

      // Exécuter la requête
      const result = await service.executeOptimizedCursorQuery(mockPrismaModel, {
        where: { userId: '123' },
        orderBy: { createdAt: 'desc' },
        cursor: '456',
        take: 10,
      });

      // Vérifier que les méthodes ont été appelées avec les bons paramètres
      expect(mockPrismaModel.findMany).toHaveBeenCalledWith({
        where: { userId: '123' },
        select: undefined,
        include: undefined,
        orderBy: { createdAt: 'desc' },
        cursor: { id: '456' },
        take: 11, // take + 1 pour vérifier s'il y a une page suivante
        skip: 1, // Sauter le curseur actuel
      });

      // Vérifier le résultat
      expect(result).toEqual({
        data: [{ id: 1 }, { id: 2 }],
        nextCursor: 2,
        prevCursor: '456',
      });
    });

    it('should handle next cursor correctly when there are more results', async () => {
      // Configurer les mocks pour simuler plus de résultats que demandés
      mockPrismaModel.findMany.mockResolvedValue([{ id: 1 }, { id: 2 }, { id: 3 }]);

      // Exécuter la requête avec take = 2
      const result = await service.executeOptimizedCursorQuery(mockPrismaModel, {
        orderBy: { createdAt: 'desc' },
        take: 2,
      });

      // Vérifier que le nextCursor est défini et que le dernier élément a été supprimé
      expect(result.data.length).toBe(2);
      expect(result.data).toEqual([{ id: 1 }, { id: 2 }]);
      expect(result.nextCursor).toBe(2);
    });
  });

  describe('executeBatchQuery', () => {
    it('should process data in batches', async () => {
      // Configurer les mocks
      mockPrismaModel.count.mockResolvedValue(250);
      mockPrismaModel.findMany
        .mockResolvedValueOnce([{ id: 1 }, { id: 2 }])
        .mockResolvedValueOnce([{ id: 3 }, { id: 4 }])
        .mockResolvedValueOnce([{ id: 5 }]);

      // Fonction de traitement par lots
      const batchProcessor = jest.fn((batch) => Promise.resolve(batch.map(item => ({ processed: item.id }))));

      // Exécuter la requête par lots
      const result = await service.executeBatchQuery(
        mockPrismaModel,
        {
          where: { userId: '123' },
          batchSize: 2,
        },
        batchProcessor,
      );

      // Vérifier que findMany a été appelé pour chaque lot
      expect(mockPrismaModel.findMany).toHaveBeenCalledTimes(3);
      expect(mockPrismaModel.findMany).toHaveBeenNthCalledWith(1, {
        where: { userId: '123' },
        select: undefined,
        orderBy: undefined,
        skip: 0,
        take: 2,
      });
      expect(mockPrismaModel.findMany).toHaveBeenNthCalledWith(2, {
        where: { userId: '123' },
        select: undefined,
        orderBy: undefined,
        skip: 2,
        take: 2,
      });
      expect(mockPrismaModel.findMany).toHaveBeenNthCalledWith(3, {
        where: { userId: '123' },
        select: undefined,
        orderBy: undefined,
        skip: 4,
        take: 2,
      });

      // Vérifier que le processeur a été appelé pour chaque lot
      expect(batchProcessor).toHaveBeenCalledTimes(3);

      // Vérifier le résultat
      expect(result).toEqual([
        { processed: 1 },
        { processed: 2 },
        { processed: 3 },
        { processed: 4 },
        { processed: 5 },
      ]);
    });
  });

  describe('Cache management', () => {
    it('should invalidate specific cache key', async () => {
      // Configurer les mocks
      mockPrismaModel.count.mockResolvedValue(100);
      mockPrismaModel.findMany.mockResolvedValue([{ id: 1 }]);

      // Première requête (mise en cache)
      await service.executeOptimizedQuery(mockPrismaModel, {
        where: { userId: '123' },
        cacheKey: 'test-cache-key',
      });

      // Invalider le cache
      service.invalidateCache('test-cache-key');

      // Réinitialiser les mocks
      mockPrismaModel.count.mockClear();
      mockPrismaModel.findMany.mockClear();

      // Deuxième requête (le cache a été invalidé)
      await service.executeOptimizedQuery(mockPrismaModel, {
        where: { userId: '123' },
        cacheKey: 'test-cache-key',
      });

      // Vérifier que les méthodes ont été appelées à nouveau
      expect(mockPrismaModel.count).toHaveBeenCalled();
      expect(mockPrismaModel.findMany).toHaveBeenCalled();
    });

    it('should invalidate all cache', async () => {
      // Configurer les mocks
      mockPrismaModel.count.mockResolvedValue(100);
      mockPrismaModel.findMany.mockResolvedValue([{ id: 1 }]);

      // Première requête (mise en cache)
      await service.executeOptimizedQuery(mockPrismaModel, {
        where: { userId: '123' },
        cacheKey: 'test-cache-key-1',
      });

      // Deuxième requête (mise en cache)
      await service.executeOptimizedQuery(mockPrismaModel, {
        where: { userId: '456' },
        cacheKey: 'test-cache-key-2',
      });

      // Invalider tout le cache
      service.invalidateAllCache();

      // Réinitialiser les mocks
      mockPrismaModel.count.mockClear();
      mockPrismaModel.findMany.mockClear();

      // Troisième requête (le cache a été invalidé)
      await service.executeOptimizedQuery(mockPrismaModel, {
        where: { userId: '123' },
        cacheKey: 'test-cache-key-1',
      });

      // Quatrième requête (le cache a été invalidé)
      await service.executeOptimizedQuery(mockPrismaModel, {
        where: { userId: '456' },
        cacheKey: 'test-cache-key-2',
      });

      // Vérifier que les méthodes ont été appelées à nouveau pour les deux requêtes
      expect(mockPrismaModel.count).toHaveBeenCalledTimes(2);
      expect(mockPrismaModel.findMany).toHaveBeenCalledTimes(2);
    });

    it('should generate consistent cache keys', () => {
      // Générer des clés de cache
      const key1 = service.generateCacheKey('test', { a: 1, b: 2 });
      const key2 = service.generateCacheKey('test', { a: 1, b: 2 });
      const key3 = service.generateCacheKey('test', { b: 2, a: 1 });
      const key4 = service.generateCacheKey('test', { a: 1, b: 3 });

      // Vérifier que les clés sont cohérentes
      expect(key1).toBe(key2);
      expect(key1).toBe(key3);
      expect(key1).not.toBe(key4);
    });
  });
});
