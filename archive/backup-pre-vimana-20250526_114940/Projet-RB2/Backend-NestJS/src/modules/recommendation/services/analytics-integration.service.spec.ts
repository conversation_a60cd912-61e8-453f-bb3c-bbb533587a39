import { Test, TestingModule } from '@nestjs/testing';
import { AnalyticsIntegrationService, AnalyticsEventType } from './analytics-integration.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { of, throwError } from 'rxjs';
import { HttpException } from '@nestjs/common';

describe('AnalyticsIntegrationService', () => {
  let service: AnalyticsIntegrationService;
  let httpService: HttpService;
  let configService: ConfigService;
  let eventEmitter: EventEmitter2;

  const mockPrismaService = {
    // Mocks pour Prisma si nécessaire
  };

  const mockConfigService = {
    get: jest.fn((key, defaultValue) => {
      if (key === 'ANALYTICS_SERVICE_URL') return 'http://analytics-service';
      if (key === 'ANALYTICS_SERVICE_API_KEY') return 'test-api-key';
      return defaultValue;
    }),
  };

  const mockHttpService = {
    post: jest.fn(),
  };

  const mockEventEmitter = {
    emit: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AnalyticsIntegrationService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
      ],
    }).compile();

    service = module.get<AnalyticsIntegrationService>(AnalyticsIntegrationService);
    httpService = module.get<HttpService>(HttpService);
    configService = module.get<ConfigService>(ConfigService);
    eventEmitter = module.get<EventEmitter2>(EventEmitter2);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('trackEvent', () => {
    it('should send event to analytics service if available', async () => {
      // Mock the analytics service response
      mockHttpService.post.mockReturnValue(
        of({
          data: {
            success: true,
            message: 'Event recorded',
          },
        }),
      );

      const eventType = AnalyticsEventType.RECOMMENDATION_VIEWED;
      const eventData = {
        userId: 'user1',
        recommendationId: 'rec1',
        recommendationType: RecommendationType.RETREAT,
      };

      const result = await service.trackEvent(eventType, eventData);

      expect(result).toBeDefined();
      expect(mockHttpService.post).toHaveBeenCalledWith(
        'http://analytics-service/api/analytics/events',
        expect.any(Object),
        expect.any(Object),
      );
      expect(mockEventEmitter.emit).toHaveBeenCalledWith('analytics.event', expect.any(Object));
    });

    it('should store event locally if analytics service is unavailable', async () => {
      // Mock an error response
      mockHttpService.post.mockReturnValue(
        throwError(() => new Error('Service unavailable')),
      );

      const eventType = AnalyticsEventType.RECOMMENDATION_VIEWED;
      const eventData = {
        userId: 'user1',
        recommendationId: 'rec1',
        recommendationType: RecommendationType.RETREAT,
      };

      const result = await service.trackEvent(eventType, eventData);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBe('Événement d\'analytics enregistré localement');
      expect(result.data).toBeDefined();
      expect(mockEventEmitter.emit).toHaveBeenCalledWith('analytics.event', expect.any(Object));
    });
  });

  describe('trackRecommendationGenerated', () => {
    it('should track recommendation generation event', async () => {
      // Mock the trackEvent method
      jest.spyOn(service, 'trackEvent').mockResolvedValue({
        success: true,
        message: 'Event recorded',
      });

      const userId = 'user1';
      const recommendationIds = ['rec1', 'rec2', 'rec3'];
      const recommendationType = RecommendationType.RETREAT;
      const metadata = { strategy: 'content-based' };

      const result = await service.trackRecommendationGenerated(
        userId,
        recommendationIds,
        recommendationType,
        metadata,
      );

      expect(result).toBeDefined();
      expect(service.trackEvent).toHaveBeenCalledWith(
        AnalyticsEventType.RECOMMENDATION_GENERATED,
        expect.objectContaining({
          userId,
          recommendationType,
          metadata: expect.objectContaining({
            strategy: 'content-based',
            recommendationIds,
            count: 3,
          }),
        }),
      );
    });
  });

  describe('trackRecommendationViewed', () => {
    it('should track recommendation view event', async () => {
      // Mock the trackEvent method
      jest.spyOn(service, 'trackEvent').mockResolvedValue({
        success: true,
        message: 'Event recorded',
      });

      const userId = 'user1';
      const recommendationId = 'rec1';
      const recommendationType = RecommendationType.RETREAT;
      const metadata = { source: 'homepage' };

      const result = await service.trackRecommendationViewed(
        userId,
        recommendationId,
        recommendationType,
        metadata,
      );

      expect(result).toBeDefined();
      expect(service.trackEvent).toHaveBeenCalledWith(
        AnalyticsEventType.RECOMMENDATION_VIEWED,
        expect.objectContaining({
          userId,
          recommendationId,
          recommendationType,
          metadata,
        }),
      );
    });
  });

  describe('trackRecommendationFeedback', () => {
    it('should track recommendation feedback event', async () => {
      // Mock the trackEvent method
      jest.spyOn(service, 'trackEvent').mockResolvedValue({
        success: true,
        message: 'Event recorded',
      });

      const userId = 'user1';
      const recommendationId = 'rec1';
      const recommendationType = RecommendationType.RETREAT;
      const feedbackType = 'LIKE';
      const metadata = { rating: 5 };

      const result = await service.trackRecommendationFeedback(
        userId,
        recommendationId,
        recommendationType,
        feedbackType,
        metadata,
      );

      expect(result).toBeDefined();
      expect(service.trackEvent).toHaveBeenCalledWith(
        AnalyticsEventType.RECOMMENDATION_FEEDBACK,
        expect.objectContaining({
          userId,
          recommendationId,
          recommendationType,
          feedbackType,
          metadata,
        }),
      );
    });
  });

  describe('trackExplanationViewed', () => {
    it('should track explanation view event', async () => {
      // Mock the trackEvent method
      jest.spyOn(service, 'trackEvent').mockResolvedValue({
        success: true,
        message: 'Event recorded',
      });

      const userId = 'user1';
      const recommendationId = 'rec1';
      const recommendationType = RecommendationType.RETREAT;
      const explanationId = 'exp1';
      const metadata = { detailLevel: 'DETAILED' };

      const result = await service.trackExplanationViewed(
        userId,
        recommendationId,
        recommendationType,
        explanationId,
        metadata,
      );

      expect(result).toBeDefined();
      expect(service.trackEvent).toHaveBeenCalledWith(
        AnalyticsEventType.EXPLANATION_VIEWED,
        expect.objectContaining({
          userId,
          recommendationId,
          recommendationType,
          explanationId,
          metadata,
        }),
      );
    });
  });
});
