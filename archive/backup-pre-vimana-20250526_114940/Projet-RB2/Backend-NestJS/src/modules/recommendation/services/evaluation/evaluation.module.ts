import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from '../../../../prisma/prisma.module';
import { EvaluationFrameworkService } from './evaluation-framework.service';
import { MetricsService } from './metrics.service';
import { UserSimulationService } from './user-simulation.service';
import { VisualizationService } from './visualization.service';

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
  ],
  providers: [
    EvaluationFrameworkService,
    MetricsService,
    UserSimulationService,
    VisualizationService,
  ],
  exports: [
    EvaluationFrameworkService,
    MetricsService,
    UserSimulationService,
    VisualizationService,
  ],
})
export class EvaluationModule {}
