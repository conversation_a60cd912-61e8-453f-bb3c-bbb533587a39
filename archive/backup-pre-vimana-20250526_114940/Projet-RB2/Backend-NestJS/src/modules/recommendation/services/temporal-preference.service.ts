import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { Cron } from '@nestjs/schedule';
import { v4 as uuidv4 } from 'uuid';
import { HierarchicalPreferenceService } from './hierarchical-preference.service';
import {
  PreferenceValue,
  PreferenceSource,
  PreferenceEvolutionRule,
  PreferenceEvent,
  PreferenceTree,
} from '../interfaces/hierarchical-preference.interface';

/**
 * Service for managing temporal evolution of user preferences
 */
@Injectable()
export class TemporalPreferenceService {
  private readonly logger = new Logger(TemporalPreferenceService.name);
  private readonly evolutionRules: PreferenceEvolutionRule[] = [];
  private readonly evolutionEnabled: boolean;
  private readonly preferenceAgingEnabled: boolean;
  private readonly preferenceAgingFactor: number;
  private readonly preferenceAgingInterval: number; // in days
  private readonly seasonalPreferencesEnabled: boolean;
  private readonly currentSeason: string;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly preferenceService: HierarchicalPreferenceService,
  ) {
    this.evolutionEnabled = this.configService.get<boolean>('PREFERENCE_EVOLUTION_ENABLED', true);
    this.preferenceAgingEnabled = this.configService.get<boolean>('PREFERENCE_AGING_ENABLED', true);
    this.preferenceAgingFactor = this.configService.get<number>('PREFERENCE_AGING_FACTOR', 0.95);
    this.preferenceAgingInterval = this.configService.get<number>('PREFERENCE_AGING_INTERVAL', 30);
    this.seasonalPreferencesEnabled = this.configService.get<boolean>('SEASONAL_PREFERENCES_ENABLED', true);
    this.currentSeason = this.determineCurrentSeason();

    // Load evolution rules
    this.loadEvolutionRules().catch(error => {
      this.logger.error(`Error loading evolution rules: ${error.message}`);
    });
  }

  /**
   * Load evolution rules from the database
   */
  private async loadEvolutionRules(): Promise<void> {
    try {
      // In a real implementation, this would load rules from the database
      // For now, we'll create some sample rules
      this.evolutionRules.push(
        this.createTimeBasedRule(
          'seasonal_preference_boost',
          'Boost seasonal preferences',
          {
            seasonType: 'current',
            boostFactor: 1.2,
          },
          ['preferences', 'seasonal', this.currentSeason.toLowerCase()],
          1,
        ),
        this.createTimeBasedRule(
          'preference_decay',
          'Decay old preferences',
          {
            olderThanDays: 90,
            decayFactor: 0.9,
          },
          ['preferences'],
          2,
        ),
        this.createInteractionBasedRule(
          'recent_interaction_boost',
          'Boost preferences based on recent interactions',
          {
            interactionType: 'PURCHASE',
            timeWindowDays: 7,
            boostFactor: 1.3,
          },
          ['preferences', 'categories'],
          3,
        ),
      );

      this.logger.log(`Loaded ${this.evolutionRules.length} evolution rules`);
    } catch (error) {
      this.logger.error(`Error loading evolution rules: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a time-based evolution rule
   */
  private createTimeBasedRule(
    id: string,
    description: string,
    parameters: any,
    path: string[],
    priority: number,
  ): PreferenceEvolutionRule {
    return {
      id,
      name: id,
      description,
      condition: {
        type: 'time_based',
        parameters,
      },
      action: {
        type: parameters.boostFactor ? 'boost' : (parameters.decayFactor ? 'decay' : 'modify'),
        parameters,
      },
      priority,
      enabled: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  /**
   * Create an interaction-based evolution rule
   */
  private createInteractionBasedRule(
    id: string,
    description: string,
    parameters: any,
    path: string[],
    priority: number,
  ): PreferenceEvolutionRule {
    return {
      id,
      name: id,
      description,
      condition: {
        type: 'interaction_based',
        parameters,
      },
      action: {
        type: parameters.boostFactor ? 'boost' : (parameters.decayFactor ? 'decay' : 'modify'),
        parameters,
      },
      priority,
      enabled: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  /**
   * Determine the current season based on the date
   * @returns Current season
   */
  private determineCurrentSeason(): string {
    const now = new Date();
    const month = now.getMonth();

    // Northern hemisphere seasons
    if (month >= 2 && month <= 4) {
      return 'SPRING';
    } else if (month >= 5 && month <= 7) {
      return 'SUMMER';
    } else if (month >= 8 && month <= 10) {
      return 'FALL';
    } else {
      return 'WINTER';
    }
  }

  /**
   * Apply aging to user preferences
   * @param userId User ID
   */
  async applyPreferenceAging(userId: string): Promise<void> {
    if (!this.preferenceAgingEnabled) {
      return;
    }

    try {
      this.logger.debug(`Applying preference aging for user ${userId}`);

      // Get the user's preference tree
      const tree = await this.preferenceService.getPreferenceTree(userId);

      // Apply aging to all preferences
      const operations = this.generateAgingOperations(tree);

      if (operations.length === 0) {
        this.logger.debug(`No preferences to age for user ${userId}`);
        return;
      }

      // Update the preferences
      await this.preferenceService.updatePreferences(userId, operations);

      this.logger.debug(`Applied aging to ${operations.length} preferences for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error applying preference aging for user ${userId}: ${error.message}`);
    }
  }

  /**
   * Generate aging operations for a preference tree
   * @param tree Preference tree
   * @returns Update operations
   */
  private generateAgingOperations(tree: PreferenceTree): any[] {
    const operations = [];
    const now = new Date();

    // Recursively process the tree
    this.processNodeForAging(tree.root, operations, now);

    return operations;
  }

  /**
   * Process a node for aging
   * @param node Preference node
   * @param operations Update operations
   * @param now Current date
   */
  private processNodeForAging(node: any, operations: any[], now: Date): void {
    // Process values in this node
    for (const value of node.values) {
      // Skip non-numeric values
      if (typeof value.value !== 'number') {
        continue;
      }

      // Skip values that are not confidence scores
      if (value.value < 0 || value.value > 1) {
        continue;
      }

      // Calculate days since last update
      const lastUpdate = new Date(value.timestamp);
      const daysSinceUpdate = (now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60 * 24);

      // Apply aging if enough time has passed
      if (daysSinceUpdate >= this.preferenceAgingInterval) {
        // Calculate how many aging intervals have passed
        const agingIntervals = Math.floor(daysSinceUpdate / this.preferenceAgingInterval);

        // Apply aging factor for each interval
        const newValue = value.value * Math.pow(this.preferenceAgingFactor, agingIntervals);

        // Only update if the change is significant
        if (Math.abs(newValue - value.value) > 0.01) {
          operations.push({
            type: 'update',
            path: node.path,
            value: {
              ...value,
              value: newValue,
              timestamp: now,
              metadata: {
                ...value.metadata,
                aged: true,
                originalValue: value.value,
                agingFactor: this.preferenceAgingFactor,
                agingIntervals,
              },
            },
          });
        }
      }
    }

    // Process child nodes
    for (const child of node.children) {
      this.processNodeForAging(child, operations, now);
    }
  }

  /**
   * Apply seasonal preference adjustments
   * @param userId User ID
   */
  async applySeasonalPreferences(userId: string): Promise<void> {
    if (!this.seasonalPreferencesEnabled) {
      return;
    }

    try {
      this.logger.debug(`Applying seasonal preferences for user ${userId}`);

      // Get the current season
      const currentSeason = this.determineCurrentSeason();

      // Create or update seasonal preferences
      const seasonalPreferences = {
        type: 'update',
        path: ['preferences', 'seasonal', currentSeason.toLowerCase()],
        value: {
          value: true,
          confidence: 0.9,
          source: PreferenceSource.IMPLICIT,
          timestamp: new Date(),
          metadata: {
            reason: 'seasonal_adjustment',
            season: currentSeason,
          },
        },
      };

      // Update the preferences
      await this.preferenceService.updatePreferences(userId, [seasonalPreferences]);

      this.logger.debug(`Applied seasonal preferences for user ${userId}: ${currentSeason}`);
    } catch (error) {
      this.logger.error(`Error applying seasonal preferences for user ${userId}: ${error.message}`);
    }
  }

  /**
   * Apply evolution rules to user preferences
   * @param userId User ID
   */
  async applyEvolutionRules(userId: string): Promise<void> {
    if (!this.evolutionEnabled) {
      return;
    }

    try {
      this.logger.debug(`Applying evolution rules for user ${userId}`);

      // Get the user's preference tree
      const tree = await this.preferenceService.getPreferenceTree(userId);

      // Get recent user interactions
      const recentInteractions = await this.getRecentUserInteractions(userId);

      // Apply each rule
      const operations = [];

      for (const rule of this.evolutionRules) {
        if (!rule.enabled) {
          continue;
        }

        const ruleOperations = await this.applyEvolutionRule(userId, rule, tree, recentInteractions);
        operations.push(...ruleOperations);
      }

      if (operations.length === 0) {
        this.logger.debug(`No evolution rules applied for user ${userId}`);
        return;
      }

      // Update the preferences
      await this.preferenceService.updatePreferences(userId, operations);

      this.logger.debug(`Applied ${operations.length} evolution operations for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error applying evolution rules for user ${userId}: ${error.message}`);
    }
  }

  /**
   * Apply an evolution rule
   * @param userId User ID
   * @param rule Evolution rule
   * @param tree Preference tree
   * @param recentInteractions Recent user interactions
   * @returns Update operations
   */
  private async applyEvolutionRule(
    userId: string,
    rule: PreferenceEvolutionRule,
    tree: PreferenceTree,
    recentInteractions: any[],
  ): Promise<any[]> {
    try {
      const operations = [];

      switch (rule.condition.type) {
        case 'time_based':
          this.applyTimeBasedRule(rule, tree, operations);
          break;

        case 'interaction_based':
          this.applyInteractionBasedRule(rule, tree, recentInteractions, operations);
          break;

        case 'context_based':
          await this.applyContextBasedRule(userId, rule, tree, operations);
          break;
      }

      return operations;
    } catch (error) {
      this.logger.error(`Error applying evolution rule ${rule.id}: ${error.message}`);
      return [];
    }
  }

  /**
   * Apply a time-based evolution rule
   * @param rule Evolution rule
   * @param tree Preference tree
   * @param operations Update operations
   */
  private applyTimeBasedRule(
    rule: PreferenceEvolutionRule,
    tree: PreferenceTree,
    operations: any[],
  ): void {
    const params = rule.condition.parameters;
    const now = new Date();

    // Handle seasonal preferences
    if (params.seasonType === 'current') {
      const currentSeason = this.determineCurrentSeason();
      const seasonalPath = ['preferences', 'seasonal', currentSeason.toLowerCase()];

      operations.push({
        type: 'update',
        path: seasonalPath,
        value: {
          value: true,
          confidence: 0.9,
          source: PreferenceSource.IMPLICIT,
          timestamp: now,
          metadata: {
            reason: 'seasonal_boost',
            season: currentSeason,
            ruleId: rule.id,
          },
        },
      });

      return;
    }

    // Handle preference decay for old preferences
    if (params.olderThanDays) {
      this.findOldPreferences(tree.root, operations, now, params.olderThanDays, params.decayFactor, rule.id);
    }
  }

  /**
   * Find old preferences for decay
   * @param node Preference node
   * @param operations Update operations
   * @param now Current date
   * @param olderThanDays Age threshold in days
   * @param decayFactor Decay factor
   * @param ruleId Rule ID
   */
  private findOldPreferences(
    node: any,
    operations: any[],
    now: Date,
    olderThanDays: number,
    decayFactor: number,
    ruleId: string,
  ): void {
    // Process values in this node
    for (const value of node.values) {
      // Skip non-numeric values
      if (typeof value.value !== 'number') {
        continue;
      }

      // Calculate days since last update
      const lastUpdate = new Date(value.timestamp);
      const daysSinceUpdate = (now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60 * 24);

      // Apply decay if the preference is old enough
      if (daysSinceUpdate >= olderThanDays) {
        const newValue = value.value * decayFactor;

        // Only update if the change is significant
        if (Math.abs(newValue - value.value) > 0.01) {
          operations.push({
            type: 'update',
            path: node.path,
            value: {
              ...value,
              value: newValue,
              timestamp: now,
              metadata: {
                ...value.metadata,
                decayed: true,
                originalValue: value.value,
                decayFactor,
                ruleId,
              },
            },
          });
        }
      }
    }

    // Process child nodes
    for (const child of node.children) {
      this.findOldPreferences(child, operations, now, olderThanDays, decayFactor, ruleId);
    }
  }

  /**
   * Apply an interaction-based evolution rule
   * @param rule Evolution rule
   * @param tree Preference tree
   * @param recentInteractions Recent user interactions
   * @param operations Update operations
   */
  private applyInteractionBasedRule(
    rule: PreferenceEvolutionRule,
    tree: PreferenceTree,
    recentInteractions: any[],
    operations: any[],
  ): void {
    const params = rule.condition.parameters;
    const now = new Date();

    // Filter interactions by type and time window
    const filteredInteractions = recentInteractions.filter(interaction => {
      if (params.interactionType && interaction.interactionType !== params.interactionType) {
        return false;
      }

      if (params.timeWindowDays) {
        const interactionDate = new Date(interaction.timestamp);
        const daysSince = (now.getTime() - interactionDate.getTime()) / (1000 * 60 * 60 * 24);
        return daysSince <= params.timeWindowDays;
      }

      return true;
    });

    if (filteredInteractions.length === 0) {
      return;
    }

    // Group interactions by category/topic
    const groupedInteractions = this.groupInteractionsByProperty(filteredInteractions);

    // Apply boost to relevant preferences
    for (const [property, count] of Object.entries(groupedInteractions)) {
      if (count < (params.minCount || 1)) {
        continue;
      }

      // Find the relevant preference node
      const path = [...rule.action.path];
      if (property) {
        path.push(property);
      }

      // Get the current preference value
      const node = this.findNodeAtPath(tree.root, path);
      if (!node) {
        continue;
      }

      // Apply boost to each value
      for (const value of node.values) {
        // Skip non-numeric values
        if (typeof value.value !== 'number') {
          continue;
        }

        const boostFactor = params.boostFactor || 1.2;
        const newValue = Math.min(1, value.value * boostFactor);

        // Only update if the change is significant
        if (Math.abs(newValue - value.value) > 0.01) {
          operations.push({
            type: 'update',
            path,
            value: {
              ...value,
              value: newValue,
              timestamp: now,
              metadata: {
                ...value.metadata,
                boosted: true,
                originalValue: value.value,
                boostFactor,
                interactionCount: count,
                ruleId: rule.id,
              },
            },
          });
        }
      }
    }
  }

  /**
   * Apply a context-based evolution rule
   * @param userId User ID
   * @param rule Evolution rule
   * @param tree Preference tree
   * @param operations Update operations
   */
  private async applyContextBasedRule(
    userId: string,
    rule: PreferenceEvolutionRule,
    tree: PreferenceTree,
    operations: any[],
  ): Promise<void> {
    const params = rule.condition.parameters;
    const now = new Date();

    // Get user context
    const userContext = await this.getUserContext(userId);
    if (!userContext) {
      return;
    }

    // Check if the context matches the rule
    const contextField = params.contextField;
    if (!contextField || !userContext[contextField]) {
      return;
    }

    const contextValue = userContext[contextField];

    // Apply the rule based on the context
    const path = [...rule.action.path];
    if (contextValue) {
      path.push(contextValue.toString().toLowerCase());
    }

    operations.push({
      type: 'update',
      path,
      value: {
        value: true,
        confidence: 0.8,
        source: PreferenceSource.IMPLICIT,
        timestamp: now,
        metadata: {
          reason: 'context_based',
          contextField,
          contextValue,
          ruleId: rule.id,
        },
      },
    });
  }

  /**
   * Find a node at the specified path
   * @param root Root node
   * @param path Path to find
   * @returns Node at the path or null if not found
   */
  private findNodeAtPath(root: any, path: string[]): any {
    if (path.length === 0) {
      return root;
    }

    let currentNode = root;
    for (let i = 0; i < path.length; i++) {
      const segment = path[i];
      const childNode = currentNode.children.find(child => child.key === segment);
      if (!childNode) {
        return null;
      }
      currentNode = childNode;
    }

    return currentNode;
  }

  /**
   * Group interactions by a property (category, topic, etc.)
   * @param interactions User interactions
   * @returns Map of property values to counts
   */
  private groupInteractionsByProperty(interactions: any[]): Record<string, number> {
    const result: Record<string, number> = {};

    for (const interaction of interactions) {
      // Try to extract categories
      if (interaction.item?.categories) {
        const categories = Array.isArray(interaction.item.categories)
          ? interaction.item.categories
          : [interaction.item.categories];

        for (const category of categories) {
          result[category] = (result[category] || 0) + 1;
        }
      }

      // Try to extract tags/topics
      if (interaction.item?.tags) {
        const tags = Array.isArray(interaction.item.tags)
          ? interaction.item.tags
          : [interaction.item.tags];

        for (const tag of tags) {
          result[tag] = (result[tag] || 0) + 1;
        }
      }

      // Try to extract location
      if (interaction.item?.location) {
        result[interaction.item.location] = (result[interaction.item.location] || 0) + 1;
      }
    }

    return result;
  }

  /**
   * Get recent user interactions
   * @param userId User ID
   * @returns Recent user interactions
   */
  private async getRecentUserInteractions(userId: string): Promise<any[]> {
    try {
      // In a real implementation, this would fetch interactions from the database
      // For now, we'll return mock data
      return [
        {
          userId,
          interactionType: 'VIEW',
          itemId: 'item1',
          itemType: 'retreat',
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
          item: {
            categories: ['wellness', 'meditation'],
            tags: ['relaxation', 'mindfulness'],
            location: 'mountain',
          },
        },
        {
          userId,
          interactionType: 'BOOKMARK',
          itemId: 'item2',
          itemType: 'course',
          timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
          item: {
            categories: ['education', 'self-improvement'],
            tags: ['learning', 'skills'],
          },
        },
        {
          userId,
          interactionType: 'PURCHASE',
          itemId: 'item3',
          itemType: 'retreat',
          timestamp: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
          item: {
            categories: ['wellness', 'yoga'],
            tags: ['relaxation', 'exercise'],
            location: 'beach',
          },
        },
      ];
    } catch (error) {
      this.logger.error(`Error getting recent interactions for user ${userId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Get user context
   * @param userId User ID
   * @returns User context
   */
  private async getUserContext(userId: string): Promise<any> {
    try {
      // In a real implementation, this would fetch context from the database or session
      // For now, we'll return mock data
      return {
        location: 'Paris',
        device: 'mobile',
        time: new Date().getHours(),
        season: this.determineCurrentSeason(),
        language: 'fr',
      };
    } catch (error) {
      this.logger.error(`Error getting context for user ${userId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Scheduled task to apply preference aging to all users
   */
  @Cron('0 0 * * *') // Run at midnight every day
  async scheduledPreferenceAging(): Promise<void> {
    if (!this.preferenceAgingEnabled) {
      return;
    }

    try {
      this.logger.log('Running scheduled preference aging');

      // Get all users
      const users = await this.getAllUsers();

      // Apply aging to each user
      for (const user of users) {
        await this.applyPreferenceAging(user.id);
      }

      this.logger.log(`Completed preference aging for ${users.length} users`);
    } catch (error) {
      this.logger.error(`Error in scheduled preference aging: ${error.message}`);
    }
  }

  /**
   * Scheduled task to apply seasonal preferences to all users
   */
  @Cron('0 0 1 * *') // Run at midnight on the first day of each month
  async scheduledSeasonalPreferences(): Promise<void> {
    if (!this.seasonalPreferencesEnabled) {
      return;
    }

    try {
      this.logger.log('Running scheduled seasonal preference updates');

      // Get all users
      const users = await this.getAllUsers();

      // Apply seasonal preferences to each user
      for (const user of users) {
        await this.applySeasonalPreferences(user.id);
      }

      this.logger.log(`Updated seasonal preferences for ${users.length} users`);
    } catch (error) {
      this.logger.error(`Error in scheduled seasonal preference updates: ${error.message}`);
    }
  }

  /**
   * Scheduled task to apply evolution rules to all users
   */
  @Cron('0 0 * * 0') // Run at midnight every Sunday
  async scheduledPreferenceEvolution(): Promise<void> {
    if (!this.evolutionEnabled) {
      return;
    }

    try {
      this.logger.log('Running scheduled preference evolution');

      // Get all users
      const users = await this.getAllUsers();

      // Apply evolution rules to each user
      for (const user of users) {
        await this.applyEvolutionRules(user.id);
      }

      this.logger.log(`Applied preference evolution for ${users.length} users`);
    } catch (error) {
      this.logger.error(`Error in scheduled preference evolution: ${error.message}`);
    }
  }

  /**
   * Get all users
   * @returns List of users
   */
  private async getAllUsers(): Promise<any[]> {
    try {
      // In a real implementation, this would fetch users from the database
      // For now, we'll return mock data
      return [
        { id: 'user1', name: 'User 1' },
        { id: 'user2', name: 'User 2' },
        { id: 'user3', name: 'User 3' },
      ];
    } catch (error) {
      this.logger.error(`Error getting all users: ${error.message}`);
      return [];
    }
  }

  /**
   * Listen for user interaction events
   * @param event Interaction event
   */
  @OnEvent('user.interaction')
  async handleInteractionEvent(event: any): Promise<void> {
    if (!event.userId || !event.interactionType || !event.itemId || !event.itemType) {
      return;
    }

    // Check if we should apply evolution rules based on this interaction
    if (this.evolutionEnabled && this.shouldTriggerEvolution(event.interactionType)) {
      await this.applyEvolutionRules(event.userId);
    }
  }

  /**
   * Determine if an interaction should trigger preference evolution
   * @param interactionType Type of interaction
   * @returns Whether to trigger evolution
   */
  private shouldTriggerEvolution(interactionType: string): boolean {
    // High-value interactions should trigger evolution
    const highValueInteractions = ['PURCHASE', 'SUBSCRIBE', 'COMPLETE', 'REVIEW'];
    return highValueInteractions.includes(interactionType);
  }
}