import { Injectable, Logger, HttpException, HttpStatus, OnModuleInit } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { firstValueFrom } from 'rxjs';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { v4 as uuidv4 } from 'uuid';

/**
 * Types de notifications liées aux recommandations
 */
export enum RecommendationNotificationType {
  NEW_RECOMMENDATIONS = 'NEW_RECOMMENDATIONS',
  PERSONALIZED_PICKS = 'PERSONALIZED_PICKS',
  TRENDING_ITEMS = 'TRENDING_ITEMS',
  SIMILAR_ITEMS = 'SIMILAR_ITEMS',
  SEASONAL_RECOMMENDATIONS = 'SEASONAL_RECOMMENDATIONS',
  FEEDBACK_REQUEST = 'FEEDBACK_REQUEST',
  RECOMMENDATION_REPORT = 'RECOMMENDATION_REPORT',
}

/**
 * Interface pour les données de notification
 */
export interface NotificationData {
  userId: string;
  title: string;
  message: string;
  type: RecommendationNotificationType;
  recommendationIds?: string[];
  recommendationType?: RecommendationType;
  strategy?: RecommendationStrategy;
  metadata?: Record<string, any>;
  priority?: 'LOW' | 'NORMAL' | 'HIGH';
  actionUrl?: string;
  imageUrl?: string;
}

/**
 * Service d'intégration avec le service de notification
 */
@Injectable()
export class NotificationIntegrationService implements OnModuleInit {
  private readonly logger = new Logger(NotificationIntegrationService.name);
  private notificationServiceUrl: string;
  private apiKey: string;
  private notificationQueue: NotificationData[] = [];
  private isProcessingQueue = false;
  private readonly maxRetries = 3;
  private readonly retryDelay = 5000; // 5 secondes

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.notificationServiceUrl = this.configService.get<string>('NOTIFICATION_SERVICE_URL', 'http://localhost:3003');
    this.apiKey = this.configService.get<string>('NOTIFICATION_SERVICE_API_KEY', 'default-api-key');
  }

  /**
   * Initialisation du service
   */
  onModuleInit() {
    this.logger.log('Initialisation du service d\'intégration avec les notifications');
    this.setupEventListeners();

    // Démarrer le traitement de la file d'attente
    setInterval(() => this.processNotificationQueue(), 10000); // Toutes les 10 secondes
  }

  /**
   * Configure les écouteurs d'événements
   */
  private setupEventListeners() {
    // Écouter les événements de recommandation générée
    this.eventEmitter.on('recommendation.generated', (data) => {
      this.handleRecommendationGenerated(data);
    });

    // Écouter les événements de recommandation saisonnière
    this.eventEmitter.on('recommendation.seasonal', (data) => {
      this.handleSeasonalRecommendation(data);
    });

    // Écouter les événements de feedback sur les recommandations
    this.eventEmitter.on('recommendation.feedback', (data) => {
      this.handleRecommendationFeedback(data);
    });

    // Écouter les événements de signalement de recommandation
    this.eventEmitter.on('recommendation.report', (data) => {
      this.handleRecommendationReport(data);
    });
  }

  /**
   * Gère l'événement de génération de recommandation
   * @param data Données de l'événement
   */
  private async handleRecommendationGenerated(data: any) {
    try {
      this.logger.debug(`Événement de génération de recommandation reçu: ${JSON.stringify(data)}`);

      // Vérifier si l'utilisateur a des notifications activées
      const userPreferences = await this.getUserNotificationPreferences(data.userId);

      if (!userPreferences.enableRecommendationNotifications) {
        this.logger.debug(`L'utilisateur ${data.userId} a désactivé les notifications de recommandation`);
        return;
      }

      // Envoyer une notification à l'utilisateur
      await this.sendNotification({
        userId: data.userId,
        title: 'Nouvelles recommandations pour vous',
        message: `Nous avons ${data.recommendationIds.length} nouvelles recommandations qui pourraient vous intéresser.`,
        type: RecommendationNotificationType.NEW_RECOMMENDATIONS,
        recommendationIds: data.recommendationIds,
        recommendationType: data.recommendationType,
        strategy: data.strategy,
        metadata: data.metadata,
        priority: 'NORMAL',
        actionUrl: `/recommendations?type=${data.recommendationType.toLowerCase()}`,
      });
    } catch (error) {
      this.logger.error(`Erreur lors du traitement de l'événement de génération de recommandation: ${error.message}`);
    }
  }

  /**
   * Gère l'événement de recommandation saisonnière
   * @param data Données de l'événement
   */
  private async handleSeasonalRecommendation(data: any) {
    try {
      this.logger.debug(`Événement de recommandation saisonnière reçu: ${JSON.stringify(data)}`);

      // Vérifier si l'utilisateur a des notifications activées
      const userPreferences = await this.getUserNotificationPreferences(data.userId);

      if (!userPreferences.enableSeasonalNotifications) {
        this.logger.debug(`L'utilisateur ${data.userId} a désactivé les notifications saisonnières`);
        return;
      }

      // Envoyer une notification à l'utilisateur
      await this.sendNotification({
        userId: data.userId,
        title: `Recommandations pour la saison ${data.currentSeason}`,
        message: `Découvrez nos ${data.recommendationCount} recommandations spéciales pour cette saison.`,
        type: RecommendationNotificationType.SEASONAL_RECOMMENDATIONS,
        metadata: {
          currentSeason: data.currentSeason,
          nextSeason: data.nextSeason,
          count: data.recommendationCount,
        },
        priority: 'NORMAL',
        actionUrl: '/recommendations/seasonal',
        imageUrl: this.getSeasonalImageUrl(data.currentSeason),
      });
    } catch (error) {
      this.logger.error(`Erreur lors du traitement de l'événement de recommandation saisonnière: ${error.message}`);
    }
  }

  /**
   * Gère l'événement de feedback sur une recommandation
   * @param data Données de l'événement
   */
  private async handleRecommendationFeedback(data: any) {
    try {
      this.logger.debug(`Événement de feedback sur une recommandation reçu: ${JSON.stringify(data)}`);

      // Si c'est un feedback négatif, demander plus d'informations après un certain délai
      if (data.feedbackType === 'DISLIKE' || data.feedbackType === 'NOT_RELEVANT') {
        // Enregistrer une tâche pour envoyer une notification de demande de feedback détaillé après 24h
        await this.scheduleDetailedFeedbackRequest(data.userId, data.recommendationId, data.recommendationType);
      }
    } catch (error) {
      this.logger.error(`Erreur lors du traitement de l'événement de feedback: ${error.message}`);
    }
  }

  /**
   * Gère l'événement de signalement d'une recommandation
   * @param data Données de l'événement
   */
  private async handleRecommendationReport(data: any) {
    try {
      this.logger.debug(`Événement de signalement de recommandation reçu: ${JSON.stringify(data)}`);

      // Notifier les administrateurs
      await this.notifyAdminsAboutReport(data);

      // Confirmer la réception du signalement à l'utilisateur
      await this.sendNotification({
        userId: data.userId,
        title: 'Signalement reçu',
        message: 'Nous avons bien reçu votre signalement. Notre équipe va l\'examiner dans les plus brefs délais.',
        type: RecommendationNotificationType.RECOMMENDATION_REPORT,
        metadata: {
          recommendationId: data.recommendationId,
          recommendationType: data.recommendationType,
          reason: data.reason,
          timestamp: data.timestamp,
        },
        priority: 'LOW',
      });
    } catch (error) {
      this.logger.error(`Erreur lors du traitement de l'événement de signalement: ${error.message}`);
    }
  }

  /**
   * Envoie une notification
   * @param notificationData Données de la notification
   * @returns Résultat de l'opération
   */
  async sendNotification(notificationData: NotificationData): Promise<any> {
    try {
      this.logger.log(`Envoi d'une notification à l'utilisateur ${notificationData.userId}: ${notificationData.title}`);

      // Si le service de notification n'est pas disponible, mettre en file d'attente
      if (!this.isNotificationServiceAvailable()) {
        return this.queueNotification(notificationData);
      }

      // Préparer les données de la notification
      const payload = {
        userId: notificationData.userId,
        title: notificationData.title,
        content: notificationData.message,
        type: 'IN_APP', // Type de notification pour le service de notification
        data: {
          recommendationType: notificationData.recommendationType,
          recommendationIds: notificationData.recommendationIds,
          strategy: notificationData.strategy,
          type: notificationData.type,
          ...notificationData.metadata,
          priority: notificationData.priority || 'NORMAL',
          actionUrl: notificationData.actionUrl,
          imageUrl: notificationData.imageUrl,
        },
      };

      // Envoyer la notification au service de notification
      const { data } = await firstValueFrom(
        this.httpService.post(
          `${this.notificationServiceUrl}/api/notifications`,
          payload,
          {
            headers: {
              'Authorization': `Bearer ${this.apiKey}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      // Enregistrer la notification dans la base de données locale
      await this.storeNotificationLocally(notificationData);

      return data;
    } catch (error) {
      this.logger.error(`Erreur lors de l'envoi de la notification: ${error.message}`);

      // En cas d'erreur, mettre la notification en file d'attente
      await this.queueNotification(notificationData);

      throw new HttpException(
        'Erreur lors de l\'envoi de la notification',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Vérifie si le service de notification est disponible
   * @returns true si le service est disponible, false sinon
   */
  private isNotificationServiceAvailable(): boolean {
    // Cette méthode pourrait être améliorée pour vérifier réellement la disponibilité du service
    // Pour l'instant, on vérifie simplement si l'URL et l'API key sont définies
    return !!this.notificationServiceUrl && !!this.apiKey && this.notificationServiceUrl !== 'http://localhost:3003';
  }

  /**
   * Récupère les préférences de notification d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Préférences de notification
   */
  private async getUserNotificationPreferences(userId: string): Promise<any> {
    try {
      // Dans une implémentation réelle, on récupérerait les préférences depuis la base de données
      // Pour l'instant, on retourne des préférences par défaut
      return {
        enableRecommendationNotifications: true,
        enableSeasonalNotifications: true,
        enableFeedbackNotifications: true,
        notificationFrequency: 'DAILY',
        preferredChannels: ['IN_APP', 'EMAIL'],
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des préférences de notification: ${error.message}`);
      // En cas d'erreur, on suppose que les notifications sont activées
      return {
        enableRecommendationNotifications: true,
        enableSeasonalNotifications: true,
        enableFeedbackNotifications: true,
        notificationFrequency: 'DAILY',
        preferredChannels: ['IN_APP'],
      };
    }
  }

  /**
   * Enregistre une notification dans la base de données locale
   * @param notificationData Données de la notification
   */
  private async storeNotificationLocally(notificationData: NotificationData): Promise<void> {
    try {
      await this.prisma.notificationLog.create({
        data: {
          id: uuidv4(),
          userId: notificationData.userId,
          title: notificationData.title,
          content: notificationData.message,
          type: notificationData.type,
          metadata: {
            recommendationType: notificationData.recommendationType,
            recommendationIds: notificationData.recommendationIds,
            strategy: notificationData.strategy,
            ...notificationData.metadata,
          },
          status: 'SENT',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de la notification: ${error.message}`);
    }
  }

  /**
   * Met une notification en file d'attente pour un envoi ultérieur
   * @param notificationData Données de la notification
   */
  private async queueNotification(notificationData: NotificationData): Promise<void> {
    try {
      this.logger.log(`Mise en file d'attente d'une notification pour l'utilisateur ${notificationData.userId}`);

      // Ajouter la notification à la file d'attente
      this.notificationQueue.push({
        ...notificationData,
        metadata: {
          ...notificationData.metadata,
          queuedAt: new Date().toISOString(),
          retryCount: 0,
        },
      });

      // Enregistrer la notification en attente dans la base de données
      await this.prisma.notificationLog.create({
        data: {
          id: uuidv4(),
          userId: notificationData.userId,
          title: notificationData.title,
          content: notificationData.message,
          type: notificationData.type,
          metadata: {
            recommendationType: notificationData.recommendationType,
            recommendationIds: notificationData.recommendationIds,
            strategy: notificationData.strategy,
            ...notificationData.metadata,
            queuedAt: new Date().toISOString(),
          },
          status: 'QUEUED',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(`Erreur lors de la mise en file d'attente de la notification: ${error.message}`);
    }
  }

  /**
   * Traite la file d'attente des notifications
   */
  private async processNotificationQueue(): Promise<void> {
    // Éviter le traitement concurrent de la file d'attente
    if (this.isProcessingQueue || this.notificationQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    try {
      this.logger.log(`Traitement de la file d'attente des notifications (${this.notificationQueue.length} notifications)`);

      // Vérifier si le service de notification est disponible
      if (!this.isNotificationServiceAvailable()) {
        this.logger.warn('Service de notification non disponible, report du traitement de la file d\'attente');
        this.isProcessingQueue = false;
        return;
      }

      // Traiter les notifications une par une
      const notification = this.notificationQueue.shift();

      if (notification) {
        try {
          // Récupérer le nombre de tentatives
          const retryCount = notification.metadata?.retryCount || 0;

          // Vérifier si le nombre maximum de tentatives a été atteint
          if (retryCount >= this.maxRetries) {
            this.logger.warn(`Nombre maximum de tentatives atteint pour la notification à l'utilisateur ${notification.userId}`);

            // Mettre à jour le statut de la notification dans la base de données
            await this.prisma.notificationLog.updateMany({
              where: {
                userId: notification.userId,
                title: notification.title,
                content: notification.message,
                status: 'QUEUED',
              },
              data: {
                status: 'FAILED',
                updatedAt: new Date(),
                metadata: {
                  ...notification.metadata,
                  failedAt: new Date().toISOString(),
                  reason: 'MAX_RETRIES_EXCEEDED',
                },
              },
            });

            // Passer à la notification suivante
            this.isProcessingQueue = false;
            return;
          }

          // Envoyer la notification
          await this.sendNotification({
            ...notification,
            metadata: {
              ...notification.metadata,
              retryCount: retryCount + 1,
            },
          });
        } catch (error) {
          this.logger.error(`Erreur lors du traitement d'une notification en file d'attente: ${error.message}`);

          // Remettre la notification en file d'attente avec un compteur de tentatives incrémenté
          this.notificationQueue.push({
            ...notification,
            metadata: {
              ...notification.metadata,
              retryCount: (notification.metadata?.retryCount || 0) + 1,
              lastError: error.message,
            },
          });
        }
      }
    } catch (error) {
      this.logger.error(`Erreur lors du traitement de la file d'attente des notifications: ${error.message}`);
    } finally {
      this.isProcessingQueue = false;
    }
  }

  /**
   * Planifie une demande de feedback détaillé
   * @param userId ID de l'utilisateur
   * @param recommendationId ID de la recommandation
   * @param recommendationType Type de recommandation
   */
  private async scheduleDetailedFeedbackRequest(
    userId: string,
    recommendationId: string,
    recommendationType: RecommendationType,
  ): Promise<void> {
    try {
      // Dans une implémentation réelle, on utiliserait un système de tâches planifiées
      // Pour l'instant, on simule en enregistrant une tâche dans la base de données
      await this.prisma.scheduledTask.create({
        data: {
          id: uuidv4(),
          type: 'FEEDBACK_REQUEST',
          status: 'PENDING',
          scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24h plus tard
          data: {
            userId,
            recommendationId,
            recommendationType,
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      this.logger.log(`Demande de feedback détaillé planifiée pour l'utilisateur ${userId} et la recommandation ${recommendationId}`);
    } catch (error) {
      this.logger.error(`Erreur lors de la planification de la demande de feedback détaillé: ${error.message}`);
    }
  }

  /**
   * Notifie les administrateurs d'un signalement
   * @param reportData Données du signalement
   */
  private async notifyAdminsAboutReport(reportData: any): Promise<void> {
    try {
      // Récupérer la liste des administrateurs
      const admins = await this.getAdminUsers();

      // Envoyer une notification à chaque administrateur
      for (const admin of admins) {
        await this.sendNotification({
          userId: admin.id,
          title: 'Nouveau signalement de recommandation',
          message: `Un utilisateur a signalé une recommandation pour la raison: ${reportData.reason}`,
          type: RecommendationNotificationType.RECOMMENDATION_REPORT,
          metadata: {
            recommendationId: reportData.recommendationId,
            recommendationType: reportData.recommendationType,
            reason: reportData.reason,
            userId: reportData.userId,
            timestamp: reportData.timestamp,
          },
          priority: 'HIGH',
          actionUrl: `/admin/moderation/reports?id=${reportData.recommendationId}&type=${reportData.recommendationType}`,
        });
      }
    } catch (error) {
      this.logger.error(`Erreur lors de la notification des administrateurs: ${error.message}`);
    }
  }

  /**
   * Récupère la liste des utilisateurs administrateurs
   * @returns Liste des administrateurs
   */
  private async getAdminUsers(): Promise<any[]> {
    try {
      // Dans une implémentation réelle, on récupérerait les administrateurs depuis la base de données
      // Pour l'instant, on retourne une liste fictive
      return [
        { id: 'admin1', email: '<EMAIL>', role: 'ADMIN' },
        { id: 'admin2', email: '<EMAIL>', role: 'ADMIN' },
        { id: 'moderator1', email: '<EMAIL>', role: 'MODERATOR' },
      ];
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des administrateurs: ${error.message}`);
      return [];
    }
  }

  /**
   * Récupère l'URL de l'image pour une saison
   * @param season Saison
   * @returns URL de l'image
   */
  private getSeasonalImageUrl(season: string): string {
    const seasonMap = {
      'SPRING': '/assets/images/seasons/spring.jpg',
      'SUMMER': '/assets/images/seasons/summer.jpg',
      'FALL': '/assets/images/seasons/fall.jpg',
      'WINTER': '/assets/images/seasons/winter.jpg',
    };

    return seasonMap[season] || '/assets/images/seasons/default.jpg';
  }

  /**
   * Envoie une notification de recommandation à un utilisateur
   * @param userId ID de l'utilisateur
   * @param recommendationIds IDs des recommandations
   * @param recommendationType Type de recommandation
   * @param title Titre de la notification
   * @param message Message de la notification
   * @param metadata Métadonnées supplémentaires
   * @returns Résultat de l'opération
   */
  async sendRecommendationNotification(
    userId: string,
    recommendationIds: string[],
    recommendationType: RecommendationType,
    title?: string,
    message?: string,
    metadata?: Record<string, any>,
  ): Promise<any> {
    try {
      this.logger.log(`Envoi d'une notification de recommandation à l'utilisateur ${userId}`);

      // Vérifier si l'utilisateur a des notifications activées
      const userPreferences = await this.getUserNotificationPreferences(userId);

      if (!userPreferences.enableRecommendationNotifications) {
        this.logger.debug(`L'utilisateur ${userId} a désactivé les notifications de recommandation`);
        return;
      }

      // Construire le titre et le message par défaut si non fournis
      const defaultTitle = `${recommendationIds.length} nouvelles recommandations pour vous`;
      const defaultMessage = `Nous avons trouvé ${recommendationIds.length} recommandations qui pourraient vous intéresser.`;

      // Envoyer la notification
      return this.sendNotification({
        userId,
        title: title || defaultTitle,
        message: message || defaultMessage,
        type: RecommendationNotificationType.NEW_RECOMMENDATIONS,
        recommendationIds,
        recommendationType,
        metadata,
        priority: 'NORMAL',
        actionUrl: `/recommendations?type=${recommendationType.toLowerCase()}`,
      });
    } catch (error) {
      this.logger.error(`Erreur lors de l'envoi de la notification de recommandation: ${error.message}`);
      throw new HttpException(
        'Erreur lors de l\'envoi de la notification de recommandation',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Envoie une notification de demande de feedback
   * @param userId ID de l'utilisateur
   * @param recommendationId ID de la recommandation
   * @param recommendationType Type de recommandation
   * @returns Résultat de l'opération
   */
  async sendFeedbackRequestNotification(
    userId: string,
    recommendationId: string,
    recommendationType: RecommendationType,
  ): Promise<any> {
    try {
      this.logger.log(`Envoi d'une notification de demande de feedback à l'utilisateur ${userId}`);

      // Vérifier si l'utilisateur a des notifications activées
      const userPreferences = await this.getUserNotificationPreferences(userId);

      if (!userPreferences.enableFeedbackNotifications) {
        this.logger.debug(`L'utilisateur ${userId} a désactivé les notifications de feedback`);
        return;
      }

      // Envoyer la notification
      return this.sendNotification({
        userId,
        title: 'Votre avis nous intéresse',
        message: 'Que pensez-vous de la recommandation que nous vous avons envoyée récemment ?',
        type: RecommendationNotificationType.FEEDBACK_REQUEST,
        recommendationIds: [recommendationId],
        recommendationType,
        priority: 'LOW',
        actionUrl: `/recommendations/feedback?id=${recommendationId}&type=${recommendationType.toLowerCase()}`,
      });
    } catch (error) {
      this.logger.error(`Erreur lors de l'envoi de la notification de demande de feedback: ${error.message}`);
      throw new HttpException(
        'Erreur lors de l\'envoi de la notification de demande de feedback',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}