import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  RawInteractionData,
  ProcessedFeatureData,
  FeatureExtractionConfig
} from '../interfaces/continuous-learning-pipeline.interface';
import { RecommendationType } from '../enums/recommendation-type.enum';

/**
 * Service for extracting features from raw data for the continuous learning pipeline
 */
@Injectable()
export class FeatureExtractionService {
  private readonly logger = new Logger(FeatureExtractionService.name);
  private defaultConfig: FeatureExtractionConfig = {
    userFeatures: {
      includeDemographics: true,
      includePreferences: true,
      includeInteractionHistory: true,
      historyWindowDays: 90,
    },
    itemFeatures: {
      includeMetadata: true,
      includePopularity: true,
      includeCategoricalFeatures: true,
      includeNumericalFeatures: true,
    },
    contextFeatures: {
      includeTimeOfDay: true,
      includeDayOfWeek: true,
      includeSeason: true,
      includeLocation: true,
      includeDevice: true,
    },
  };

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    // Load configuration from environment or use defaults
    const configFromEnv = this.configService.get<FeatureExtractionConfig>('FEATURE_EXTRACTION_CONFIG');
    if (configFromEnv) {
      this.defaultConfig = {
        ...this.defaultConfig,
        ...configFromEnv,
      };
    }
  }

  /**
   * Extract features from raw interaction data
   * @param rawData Raw interaction data
   * @param config Feature extraction configuration
   * @returns Processed feature data
   */
  async extractFeatures(
    rawData: RawInteractionData[],
    config?: Partial<FeatureExtractionConfig>,
  ): Promise<ProcessedFeatureData[]> {
    try {
      this.logger.log(`Extracting features from ${rawData.length} raw interaction records`);

      // Merge provided config with defaults
      const mergedConfig: FeatureExtractionConfig = {
        ...this.defaultConfig,
        ...config,
      };

      const processedData: ProcessedFeatureData[] = [];

      // Process each interaction
      for (const interaction of rawData) {
        // Extract user features
        const userFeatures = mergedConfig.userFeatures.includeDemographics ||
                            mergedConfig.userFeatures.includePreferences ||
                            mergedConfig.userFeatures.includeInteractionHistory
          ? await this.extractUserFeatures(interaction.userId, mergedConfig.userFeatures)
          : {};

        // Extract item features
        const itemFeatures = mergedConfig.itemFeatures.includeMetadata ||
                            mergedConfig.itemFeatures.includePopularity ||
                            mergedConfig.itemFeatures.includeCategoricalFeatures ||
                            mergedConfig.itemFeatures.includeNumericalFeatures
          ? await this.extractItemFeatures(interaction.itemId, interaction.itemType, mergedConfig.itemFeatures)
          : {};

        // Extract context features
        const contextFeatures = mergedConfig.contextFeatures.includeTimeOfDay ||
                               mergedConfig.contextFeatures.includeDayOfWeek ||
                               mergedConfig.contextFeatures.includeSeason ||
                               mergedConfig.contextFeatures.includeLocation ||
                               mergedConfig.contextFeatures.includeDevice
          ? this.extractContextFeatures(interaction, mergedConfig.contextFeatures)
          : {};

        // Extract interaction features
        const interactionFeatures = this.extractInteractionFeatures(interaction);

        // Combine all features
        const features = {
          ...userFeatures,
          ...itemFeatures,
          ...contextFeatures,
          ...interactionFeatures,
        };

        // Determine label based on interaction type
        const label = this.getLabelFromInteraction(interaction.interactionType);

        processedData.push({
          userId: interaction.userId,
          itemId: interaction.itemId,
          itemType: interaction.itemType,
          features,
          label,
          timestamp: interaction.timestamp,
        });
      }

      this.logger.log(`Feature extraction complete. Generated ${processedData.length} processed records`);

      // Emit event for monitoring
      this.eventEmitter.emit('feature.extraction.completed', {
        inputSize: rawData.length,
        outputSize: processedData.length,
        config: mergedConfig,
        timestamp: new Date(),
      });

      return processedData;
    } catch (error) {
      this.logger.error(`Error extracting features: ${error.message}`);

      // Emit error event
      this.eventEmitter.emit('feature.extraction.error', {
        error: error.message,
        timestamp: new Date(),
      });

      throw error;
    }
  }

  /**
   * Extract features related to a user
   * @param userId User ID
   * @param config User feature extraction configuration
   * @returns User features
   */
  private async extractUserFeatures(
    userId: string,
    config: FeatureExtractionConfig['userFeatures'],
  ): Promise<Record<string, number>> {
    const features: Record<string, number> = {};

    try {
      // Extract demographic features
      if (config.includeDemographics) {
        const userProfile = await this.prisma.userProfile.findUnique({
          where: { userId },
          select: {
            age: true,
            gender: true,
            location: true,
            occupation: true,
            interests: true,
          },
        });

        if (userProfile) {
          // Age features
          if (userProfile.age) {
            features['user_age'] = userProfile.age;
            features['user_age_group_18_24'] = userProfile.age >= 18 && userProfile.age <= 24 ? 1 : 0;
            features['user_age_group_25_34'] = userProfile.age >= 25 && userProfile.age <= 34 ? 1 : 0;
            features['user_age_group_35_44'] = userProfile.age >= 35 && userProfile.age <= 44 ? 1 : 0;
            features['user_age_group_45_54'] = userProfile.age >= 45 && userProfile.age <= 54 ? 1 : 0;
            features['user_age_group_55_plus'] = userProfile.age >= 55 ? 1 : 0;
          }

          // Gender features (simplified)
          if (userProfile.gender) {
            features['user_gender_male'] = userProfile.gender === 'MALE' ? 1 : 0;
            features['user_gender_female'] = userProfile.gender === 'FEMALE' ? 1 : 0;
            features['user_gender_other'] = userProfile.gender === 'OTHER' ? 1 : 0;
          }
        }
      }

      // Extract preference features
      if (config.includePreferences) {
        const userPreferences = await this.prisma.userPreference.findMany({
          where: { userId },
          select: {
            category: true,
            value: true,
            strength: true,
          },
        });

        for (const pref of userPreferences) {
          features[`user_pref_${pref.category.toLowerCase()}_${pref.value.toLowerCase()}`] = pref.strength || 1;
        }
      }

      // Extract interaction history features
      if (config.includeInteractionHistory) {
        const historyStartDate = new Date();
        historyStartDate.setDate(historyStartDate.getDate() - config.historyWindowDays);

        const interactionHistory = await this.prisma.userInteraction.findMany({
          where: {
            userId,
            timestamp: {
              gte: historyStartDate,
            },
          },
          select: {
            itemType: true,
            interactionType: true,
            timestamp: true,
          },
        });

        // Count interactions by type
        const interactionCounts: Record<string, number> = {};

        for (const interaction of interactionHistory) {
          const key = `${interaction.itemType}_${interaction.interactionType}`;
          interactionCounts[key] = (interactionCounts[key] || 0) + 1;
        }

        // Add interaction counts as features
        for (const [key, count] of Object.entries(interactionCounts)) {
          features[`user_interaction_count_${key.toLowerCase()}`] = count;
        }

        // Calculate recency features
        const now = new Date();
        const recentInteractions = interactionHistory
          .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
          .slice(0, 10); // Last 10 interactions

        if (recentInteractions.length > 0) {
          const lastInteractionDays = (now.getTime() - recentInteractions[0].timestamp.getTime()) / (1000 * 60 * 60 * 24);
          features['user_days_since_last_interaction'] = lastInteractionDays;
        }
      }

      return features;
    } catch (error) {
      this.logger.error(`Error extracting user features for user ${userId}: ${error.message}`);
      return features; // Return whatever features we managed to extract
    }
  }

  /**
   * Extract features related to an item
   * @param itemId Item ID
   * @param itemType Item type
   * @param config Item feature extraction configuration
   * @returns Item features
   */
  private async extractItemFeatures(
    itemId: string,
    itemType: RecommendationType,
    config: FeatureExtractionConfig['itemFeatures'],
  ): Promise<Record<string, number>> {
    const features: Record<string, number> = {};

    try {
      // Add item type features
      features[`item_type_${itemType.toLowerCase()}`] = 1;

      // Extract metadata features
      if (config.includeMetadata) {
        let itemData: any = null;

        // Fetch item data based on type
        switch (itemType) {
          case RecommendationType.RETREAT:
            itemData = await this.prisma.retreat.findUnique({
              where: { id: itemId },
              select: {
                title: true,
                description: true,
                location: true,
                startDate: true,
                endDate: true,
                price: true,
                capacity: true,
                categories: true,
                tags: true,
              },
            });
            break;

          case RecommendationType.COURSE:
            itemData = await this.prisma.course.findUnique({
              where: { id: itemId },
              select: {
                title: true,
                description: true,
                instructor: true,
                duration: true,
                price: true,
                level: true,
                categories: true,
                tags: true,
              },
            });
            break;

          case RecommendationType.PARTNER:
            itemData = await this.prisma.partner.findUnique({
              where: { id: itemId },
              select: {
                name: true,
                description: true,
                services: true,
                location: true,
                rating: true,
                categories: true,
                tags: true,
              },
            });
            break;

          case RecommendationType.CONTENT:
            itemData = await this.prisma.content.findUnique({
              where: { id: itemId },
              select: {
                title: true,
                description: true,
                author: true,
                publishDate: true,
                type: true,
                categories: true,
                tags: true,
              },
            });
            break;
        }

        if (itemData) {
          // Add price feature if available
          if (itemData.price !== undefined) {
            features['item_price'] = itemData.price;

            // Price buckets
            features['item_price_low'] = itemData.price < 100 ? 1 : 0;
            features['item_price_medium'] = itemData.price >= 100 && itemData.price < 500 ? 1 : 0;
            features['item_price_high'] = itemData.price >= 500 ? 1 : 0;
          }

          // Add duration feature if available
          if (itemData.duration !== undefined) {
            features['item_duration'] = itemData.duration;
          } else if (itemData.startDate && itemData.endDate) {
            const start = new Date(itemData.startDate);
            const end = new Date(itemData.endDate);
            const durationDays = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
            features['item_duration_days'] = durationDays;
          }

          // Add category features
          if (itemData.categories) {
            const categories = Array.isArray(itemData.categories)
              ? itemData.categories
              : typeof itemData.categories === 'string'
                ? itemData.categories.split(',')
                : [];

            for (const category of categories) {
              features[`item_category_${category.toLowerCase().trim()}`] = 1;
            }
          }

          // Add tag features
          if (itemData.tags) {
            const tags = Array.isArray(itemData.tags)
              ? itemData.tags
              : typeof itemData.tags === 'string'
                ? itemData.tags.split(',')
                : [];

            for (const tag of tags) {
              features[`item_tag_${tag.toLowerCase().trim()}`] = 1;
            }
          }
        }
      }

      // Extract popularity features
      if (config.includePopularity) {
        // Get interaction counts
        const interactionCounts = await this.prisma.userInteraction.groupBy({
          by: ['interactionType'],
          where: {
            itemId,
            itemType,
          },
          _count: {
            interactionType: true,
          },
        });

        // Calculate total interactions
        let totalInteractions = 0;

        for (const count of interactionCounts) {
          const interactionType = count.interactionType;
          const countValue = count._count.interactionType;

          features[`item_interaction_count_${interactionType.toLowerCase()}`] = countValue;
          totalInteractions += countValue;
        }

        features['item_total_interactions'] = totalInteractions;

        // Get recent interaction rate (last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const recentInteractions = await this.prisma.userInteraction.count({
          where: {
            itemId,
            itemType,
            timestamp: {
              gte: thirtyDaysAgo,
            },
          },
        });

        features['item_recent_interactions'] = recentInteractions;
        features['item_popularity_trend'] = totalInteractions > 0
          ? recentInteractions / totalInteractions
          : 0;
      }

      return features;
    } catch (error) {
      this.logger.error(`Error extracting item features for item ${itemId} of type ${itemType}: ${error.message}`);
      return features; // Return whatever features we managed to extract
    }
  }

  /**
   * Extract features related to the context of an interaction
   * @param interaction Raw interaction data
   * @param config Context feature extraction configuration
   * @returns Context features
   */
  private extractContextFeatures(
    interaction: RawInteractionData,
    config: FeatureExtractionConfig['contextFeatures'],
  ): Record<string, number> {
    const features: Record<string, number> = {};
    const timestamp = interaction.timestamp;

    try {
      // Time of day features
      if (config.includeTimeOfDay) {
        const hour = timestamp.getHours();

        features['context_hour'] = hour;
        features['context_time_morning'] = hour >= 5 && hour < 12 ? 1 : 0;
        features['context_time_afternoon'] = hour >= 12 && hour < 17 ? 1 : 0;
        features['context_time_evening'] = hour >= 17 && hour < 22 ? 1 : 0;
        features['context_time_night'] = (hour >= 22 || hour < 5) ? 1 : 0;
      }

      // Day of week features
      if (config.includeDayOfWeek) {
        const dayOfWeek = timestamp.getDay(); // 0 = Sunday, 6 = Saturday

        features['context_day_of_week'] = dayOfWeek;
        features['context_weekday'] = dayOfWeek >= 1 && dayOfWeek <= 5 ? 1 : 0;
        features['context_weekend'] = dayOfWeek === 0 || dayOfWeek === 6 ? 1 : 0;
      }

      // Season features
      if (config.includeSeason) {
        const month = timestamp.getMonth(); // 0 = January, 11 = December

        // Northern hemisphere seasons
        features['context_season_spring'] = month >= 2 && month <= 4 ? 1 : 0;
        features['context_season_summer'] = month >= 5 && month <= 7 ? 1 : 0;
        features['context_season_fall'] = month >= 8 && month <= 10 ? 1 : 0;
        features['context_season_winter'] = month === 11 || month <= 1 ? 1 : 0;
      }

      // Location features
      if (config.includeLocation && interaction.metadata?.location) {
        const location = interaction.metadata.location;

        if (typeof location === 'string') {
          features[`context_location_${location.toLowerCase()}`] = 1;
        } else if (typeof location === 'object') {
          // If location is an object with country, region, etc.
          if (location.country) {
            features[`context_country_${location.country.toLowerCase()}`] = 1;
          }

          if (location.region) {
            features[`context_region_${location.region.toLowerCase()}`] = 1;
          }
        }
      }

      // Device features
      if (config.includeDevice && interaction.metadata?.device) {
        const device = interaction.metadata.device;

        if (typeof device === 'string') {
          features[`context_device_${device.toLowerCase()}`] = 1;
        } else if (typeof device === 'object') {
          // If device is an object with type, os, etc.
          if (device.type) {
            features[`context_device_type_${device.type.toLowerCase()}`] = 1;
          }

          if (device.os) {
            features[`context_device_os_${device.os.toLowerCase()}`] = 1;
          }

          if (device.browser) {
            features[`context_device_browser_${device.browser.toLowerCase()}`] = 1;
          }
        }
      }

      return features;
    } catch (error) {
      this.logger.error(`Error extracting context features: ${error.message}`);
      return features; // Return whatever features we managed to extract
    }
  }

  /**
   * Extract features related to the interaction itself
   * @param interaction Raw interaction data
   * @returns Interaction features
   */
  private extractInteractionFeatures(interaction: RawInteractionData): Record<string, number> {
    const features: Record<string, number> = {};

    try {
      // Interaction type feature
      features[`interaction_type_${interaction.interactionType.toLowerCase()}`] = 1;

      // Interaction recency
      const now = new Date();
      const diffInDays = (now.getTime() - interaction.timestamp.getTime()) / (1000 * 60 * 60 * 24);

      features['interaction_recency'] = Math.exp(-diffInDays / 30); // 30-day half-life

      // Interaction strength based on type
      features['interaction_strength'] = this.getInteractionStrength(interaction.interactionType);

      // Extract additional features from metadata
      if (interaction.metadata) {
        // Duration of interaction if available
        if (interaction.metadata.duration) {
          features['interaction_duration'] = interaction.metadata.duration;
        }

        // Depth of interaction if available (e.g., scroll depth, video watch percentage)
        if (interaction.metadata.depth) {
          features['interaction_depth'] = interaction.metadata.depth;
        }

        // Engagement level if available
        if (interaction.metadata.engagement) {
          features['interaction_engagement'] = interaction.metadata.engagement;
        }
      }

      return features;
    } catch (error) {
      this.logger.error(`Error extracting interaction features: ${error.message}`);
      return {}; // Return empty object on error
    }
  }

  /**
   * Get interaction strength based on type
   * @param interactionType Type of interaction
   * @returns Strength score between 0 and 1
   */
  private getInteractionStrength(interactionType: string): number {
    // Map interaction types to strength scores
    const strengthMap: Record<string, number> = {
      'VIEW': 0.2,
      'CLICK': 0.4,
      'BOOKMARK': 0.6,
      'PURCHASE': 0.8,
      'REVIEW': 0.9,
      'SHARE': 0.7,
      'LIKE': 0.6,
      'DISLIKE': 0.3,
      'COMMENT': 0.7,
      'FOLLOW': 0.6,
      'SUBSCRIBE': 0.8,
    };

    return strengthMap[interactionType] || 0.5;
  }

  /**
   * Get label value from interaction type
   * @param interactionType Type of interaction
   * @returns Label value (typically 0 or 1 for binary classification)
   */
  private getLabelFromInteraction(interactionType: string): number {
    // Map interaction types to label values
    // For binary classification: 1 = positive, 0 = negative
    const positiveInteractions = [
      'BOOKMARK',
      'PURCHASE',
      'REVIEW',
      'SHARE',
      'LIKE',
      'COMMENT',
      'FOLLOW',
      'SUBSCRIBE'
    ];

    return positiveInteractions.includes(interactionType) ? 1 : 0;
  }
}