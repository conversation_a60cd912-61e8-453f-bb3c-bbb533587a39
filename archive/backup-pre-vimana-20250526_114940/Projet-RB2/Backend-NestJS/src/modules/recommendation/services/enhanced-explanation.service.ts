import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { EnhancedExplanationDto, ExplanationFactorDto, ExplanationVisualizationDto } from '../dto/enhanced-explanation.dto';
import { AnalyticsIntegrationService } from './analytics-integration.service';
import { v4 as uuidv4 } from 'uuid';

/**
 * Service pour les explications améliorées des recommandations
 */
@Injectable()
export class EnhancedExplanationService {
  private readonly logger = new Logger(EnhancedExplanationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly analyticsService: AnalyticsIntegrationService,
  ) {}

  /**
   * Génère une explication améliorée pour une recommandation
   * @param recommendationId ID de la recommandation
   * @param recommendationType Type de recommandation
   * @param userId ID de l'utilisateur
   * @param options Options pour l'explication
   * @returns Explication améliorée
   */
  async generateEnhancedExplanation(
    recommendationId: string,
    recommendationType: RecommendationType,
    userId: string,
    options: {
      includeVisualizations?: boolean;
      detailLevel?: 'BASIC' | 'STANDARD' | 'DETAILED';
      language?: string;
    } = {},
  ): Promise<EnhancedExplanationDto> {
    try {
      this.logger.log(`Génération d'une explication améliorée pour la recommandation ${recommendationId} de type ${recommendationType}`);

      // Valeurs par défaut pour les options
      const includeVisualizations = options.includeVisualizations !== false;
      const detailLevel = options.detailLevel || 'STANDARD';
      const language = options.language || 'fr';

      // Vérifier si la recommandation existe
      const recommendation = await this.getRecommendation(recommendationId, recommendationType);
      if (!recommendation) {
        throw new NotFoundException(`Recommandation ${recommendationId} de type ${recommendationType} non trouvée`);
      }

      // Récupérer le profil utilisateur pour personnaliser l'explication
      const userProfile = await this.getUserProfile(userId);

      // Générer les facteurs d'explication
      const factors = await this.generateExplanationFactors(
        recommendationId,
        recommendationType,
        userId,
        detailLevel,
      );

      // Générer les visualisations si demandé
      let visualizations: ExplanationVisualizationDto[] = [];
      if (includeVisualizations) {
        visualizations = await this.generateExplanationVisualizations(
          recommendationId,
          recommendationType,
          factors,
        );
      }

      // Générer l'explication générale
      const generalExplanation = this.generateGeneralExplanation(
        recommendation,
        factors,
        language,
      );

      // Générer l'explication personnalisée si un profil utilisateur est disponible
      const personalizedExplanation = userProfile
        ? this.generatePersonalizedExplanation(recommendation, factors, userProfile, language)
        : undefined;

      // Créer un ID unique pour l'explication
      const explanationId = uuidv4();

      // Créer l'objet d'explication
      const explanation = {
        recommendationId,
        recommendationType,
        generalExplanation,
        personalizedExplanation,
        factors,
        visualizations,
        isPersonalized: !!personalizedExplanation,
        metadata: {
          detailLevel,
          language,
          timestamp: new Date().toISOString(),
          explanationId,
        },
      };

      // Suivre l'événement dans le service d'analytics
      try {
        await this.analyticsService.trackExplanationViewed(
          userId,
          recommendationId,
          recommendationType,
          explanationId,
          {
            detailLevel,
            language,
            isPersonalized: !!personalizedExplanation,
            factorsCount: factors.length,
            visualizationsCount: visualizations.length,
          },
        );
      } catch (analyticsError) {
        // Ne pas bloquer le processus si l'enregistrement dans analytics échoue
        this.logger.warn(`Erreur lors de l'enregistrement de la vue d'explication dans analytics: ${analyticsError.message}`);
      }

      return explanation;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération de l'explication: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère une recommandation par son ID et son type
   * @param recommendationId ID de la recommandation
   * @param recommendationType Type de recommandation
   * @returns Recommandation ou null si non trouvée
   */
  private async getRecommendation(
    recommendationId: string,
    recommendationType: RecommendationType,
  ): Promise<any> {
    // Cette méthode devrait être adaptée selon la structure de la base de données
    // Pour l'instant, on simule une récupération de recommandation
    try {
      let recommendation = null;

      switch (recommendationType) {
        case RecommendationType.RETREAT:
          recommendation = await this.prisma.retreat.findUnique({
            where: { id: recommendationId },
          });
          break;
        case RecommendationType.COURSE:
          recommendation = await this.prisma.course.findUnique({
            where: { id: recommendationId },
          });
          break;
        case RecommendationType.VIDEO:
          recommendation = await this.prisma.video.findUnique({
            where: { id: recommendationId },
          });
          break;
        case RecommendationType.ARTICLE:
          recommendation = await this.prisma.article.findUnique({
            where: { id: recommendationId },
          });
          break;
        case RecommendationType.EVENT:
          recommendation = await this.prisma.event.findUnique({
            where: { id: recommendationId },
          });
          break;
        default:
          return null;
      }

      return recommendation;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération de la recommandation: ${error.message}`);
      return null;
    }
  }

  /**
   * Récupère le profil utilisateur
   * @param userId ID de l'utilisateur
   * @returns Profil utilisateur ou null si non trouvé
   */
  private async getUserProfile(userId: string): Promise<any> {
    try {
      const userProfile = await this.prisma.user.findUnique({
        where: { id: userId },
        include: {
          preferences: true,
          interests: true,
        },
      });

      return userProfile;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération du profil utilisateur: ${error.message}`);
      return null;
    }
  }

  /**
   * Génère les facteurs d'explication pour une recommandation
   * @param recommendationId ID de la recommandation
   * @param recommendationType Type de recommandation
   * @param userId ID de l'utilisateur
   * @param detailLevel Niveau de détail
   * @returns Liste des facteurs d'explication
   */
  private async generateExplanationFactors(
    recommendationId: string,
    recommendationType: RecommendationType,
    userId: string,
    detailLevel: 'BASIC' | 'STANDARD' | 'DETAILED',
  ): Promise<ExplanationFactorDto[]> {
    // Cette méthode devrait être adaptée selon les algorithmes de recommandation utilisés
    // Pour l'instant, on simule des facteurs d'explication
    const factors: ExplanationFactorDto[] = [];

    // Ajouter des facteurs selon le niveau de détail
    if (detailLevel === 'BASIC' || detailLevel === 'STANDARD' || detailLevel === 'DETAILED') {
      factors.push({
        name: 'Similarité de contenu',
        description: 'Ce contenu est similaire à d\'autres contenus que vous avez appréciés',
        weight: 0.8,
        metadata: {
          similarityScore: 0.85,
        },
      });

      factors.push({
        name: 'Popularité',
        description: 'Ce contenu est populaire parmi les utilisateurs ayant des intérêts similaires aux vôtres',
        weight: 0.6,
        metadata: {
          popularityScore: 0.75,
        },
      });
    }

    if (detailLevel === 'STANDARD' || detailLevel === 'DETAILED') {
      factors.push({
        name: 'Préférences personnelles',
        description: 'Ce contenu correspond à vos préférences personnelles',
        weight: 0.7,
        metadata: {
          preferenceMatch: 0.82,
        },
      });
    }

    if (detailLevel === 'DETAILED') {
      factors.push({
        name: 'Saisonnalité',
        description: 'Ce contenu est particulièrement pertinent pour la saison actuelle',
        weight: 0.5,
        metadata: {
          seasonalityScore: 0.65,
        },
      });

      factors.push({
        name: 'Diversification',
        description: 'Ce contenu vous est recommandé pour diversifier vos découvertes',
        weight: 0.4,
        metadata: {
          diversityScore: 0.55,
        },
      });
    }

    return factors;
  }

  /**
   * Génère les visualisations pour une explication
   * @param recommendationId ID de la recommandation
   * @param recommendationType Type de recommandation
   * @param factors Facteurs d'explication
   * @returns Liste des visualisations
   */
  private async generateExplanationVisualizations(
    recommendationId: string,
    recommendationType: RecommendationType,
    factors: ExplanationFactorDto[],
  ): Promise<ExplanationVisualizationDto[]> {
    const visualizations: ExplanationVisualizationDto[] = [];

    try {
      // Récupérer des données supplémentaires pour enrichir les visualisations
      const recommendation = await this.getRecommendation(recommendationId, recommendationType);

      if (!recommendation) {
        throw new NotFoundException(`Recommandation ${recommendationId} de type ${recommendationType} non trouvée`);
      }

      // 1. Graphique à barres pour les facteurs d'influence
      visualizations.push({
        type: 'BAR_CHART',
        title: 'Facteurs de recommandation',
        description: 'Importance relative des différents facteurs dans cette recommandation',
        data: {
          labels: factors.map(factor => factor.name),
          values: factors.map(factor => factor.weight),
          colors: ['#4285F4', '#34A853', '#FBBC05', '#EA4335', '#8F00FF', '#00BCD4', '#FF5722'],
        },
      });

      // 2. Graphique radar pour les correspondances avec les préférences
      const preferenceCategories = ['Thème', 'Style', 'Difficulté', 'Durée', 'Prix', 'Localisation', 'Saison'];
      const preferenceValues = this.generatePreferenceMatchValues(recommendation, factors);

      visualizations.push({
        type: 'RADAR_CHART',
        title: 'Correspondance avec vos préférences',
        description: 'Comment cette recommandation correspond à différents aspects de vos préférences',
        data: {
          categories: preferenceCategories,
          values: preferenceValues,
        },
      });

      // 3. Graphique circulaire pour la répartition des facteurs par catégorie
      const factorCategories = this.groupFactorsByCategory(factors);

      visualizations.push({
        type: 'PIE_CHART',
        title: 'Répartition des facteurs par catégorie',
        description: 'Proportion des différentes catégories de facteurs dans cette recommandation',
        data: {
          labels: Object.keys(factorCategories),
          values: Object.values(factorCategories),
          colors: ['#4285F4', '#34A853', '#FBBC05', '#EA4335', '#8F00FF', '#00BCD4', '#FF5722'],
        },
      });

      // 4. Graphique de comparaison avec d'autres recommandations similaires
      const comparisonData = await this.generateComparisonData(recommendationId, recommendationType);

      if (comparisonData && comparisonData.labels.length > 0) {
        visualizations.push({
          type: 'COMPARISON_CHART',
          title: 'Comparaison avec d\'autres recommandations',
          description: 'Comment cette recommandation se compare à d\'autres recommandations similaires',
          data: comparisonData,
        });
      }

      // 5. Timeline des interactions passées (si disponible)
      const timelineData = await this.generateTimelineData(recommendationId, recommendationType);

      if (timelineData && timelineData.events.length > 0) {
        visualizations.push({
          type: 'TIMELINE',
          title: 'Historique des interactions',
          description: 'Vos interactions passées qui ont influencé cette recommandation',
          data: timelineData,
        });
      }

      return visualizations;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des visualisations: ${error.message}`);

      // En cas d'erreur, retourner au moins les visualisations de base
      return [
        {
          type: 'BAR_CHART',
          title: 'Facteurs de recommandation',
          description: 'Importance relative des différents facteurs dans cette recommandation',
          data: {
            labels: factors.map(factor => factor.name),
            values: factors.map(factor => factor.weight),
            colors: ['#4285F4', '#34A853', '#FBBC05', '#EA4335', '#8F00FF'],
          },
        },
        {
          type: 'RADAR_CHART',
          title: 'Correspondance avec vos préférences',
          description: 'Comment cette recommandation correspond à différents aspects de vos préférences',
          data: {
            categories: ['Thème', 'Style', 'Difficulté', 'Durée', 'Prix'],
            values: [0.9, 0.7, 0.8, 0.6, 0.5],
          },
        }
      ];
    }
  }

  /**
   * Génère une explication générale pour une recommandation
   * @param recommendation Recommandation
   * @param factors Facteurs d'explication
   * @param language Langue de l'explication
   * @returns Explication générale
   */
  private generateGeneralExplanation(
    recommendation: any,
    factors: ExplanationFactorDto[],
    language: string,
  ): string {
    // Cette méthode devrait être adaptée selon les besoins d'explication
    // Pour l'instant, on génère une explication simple
    if (language === 'fr') {
      return `Nous vous recommandons "${recommendation.title || recommendation.name}" car ${factors[0].description.toLowerCase()} et ${factors[1].description.toLowerCase()}.`;
    } else {
      return `We recommend "${recommendation.title || recommendation.name}" because ${factors[0].description.toLowerCase()} and ${factors[1].description.toLowerCase()}.`;
    }
  }

  /**
   * Génère une explication personnalisée pour une recommandation
   * @param recommendation Recommandation
   * @param factors Facteurs d'explication
   * @param userProfile Profil utilisateur
   * @param language Langue de l'explication
   * @returns Explication personnalisée
   */
  private generatePersonalizedExplanation(
    recommendation: any,
    factors: ExplanationFactorDto[],
    userProfile: any,
    language: string,
  ): string {
    try {
      const userName = userProfile.firstName || 'vous';
      const userInterests = userProfile.interests?.map(i => i.name).join(', ') || 'bien-être';

      // Récupérer les facteurs les plus importants
      const topFactors = [...factors]
        .sort((a, b) => b.weight - a.weight)
        .slice(0, 3);

      // Récupérer les préférences de l'utilisateur
      const userPreferences = this.extractUserPreferences(userProfile);

      // Récupérer l'historique des interactions
      const userHistory = this.extractUserHistory(userProfile);

      // Récupérer le contexte actuel
      const currentContext = this.extractCurrentContext(userProfile);

      // Construire l'explication personnalisée
      let explanation = '';

      if (language === 'fr') {
        // Introduction personnalisée
        explanation = `${userName}, cette recommandation est spécialement sélectionnée pour vous`;

        // Ajouter les facteurs principaux
        if (topFactors.length > 0) {
          explanation += ` car ${topFactors[0].description.toLowerCase()}`;

          if (topFactors.length > 1) {
            explanation += ` et ${topFactors[1].description.toLowerCase()}`;
          }
        }

        // Ajouter les intérêts
        if (userInterests) {
          explanation += `. Elle correspond parfaitement à vos intérêts en ${userInterests}`;
        }

        // Ajouter les préférences
        if (userPreferences.length > 0) {
          explanation += ` et à vos préférences pour ${userPreferences.join(', ')}`;
        }

        // Ajouter l'historique
        if (userHistory.length > 0) {
          explanation += `. Nous avons remarqué que vous avez récemment ${userHistory[0]}`;
        }

        // Ajouter le contexte
        if (currentContext.length > 0) {
          explanation += `. De plus, ${currentContext[0]}`;
        }

        // Finaliser l'explication
        explanation += '.';
      } else {
        // English version
        explanation = `${userName}, this recommendation is specially selected for you`;

        // Add main factors
        if (topFactors.length > 0) {
          explanation += ` because ${this.translateToEnglish(topFactors[0].description).toLowerCase()}`;

          if (topFactors.length > 1) {
            explanation += ` and ${this.translateToEnglish(topFactors[1].description).toLowerCase()}`;
          }
        }

        // Add interests
        if (userInterests) {
          explanation += `. It perfectly matches your interests in ${this.translateToEnglish(userInterests)}`;
        }

        // Add preferences
        if (userPreferences.length > 0) {
          explanation += ` and your preferences for ${userPreferences.map(p => this.translateToEnglish(p)).join(', ')}`;
        }

        // Add history
        if (userHistory.length > 0) {
          explanation += `. We noticed that you recently ${this.translateToEnglish(userHistory[0])}`;
        }

        // Add context
        if (currentContext.length > 0) {
          explanation += `. Additionally, ${this.translateToEnglish(currentContext[0])}`;
        }

        // Finalize explanation
        explanation += '.';
      }

      return explanation;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération de l'explication personnalisée: ${error.message}`);

      // Fallback to simple explanation
      const userName = userProfile.firstName || 'vous';
      const userInterests = userProfile.interests?.map(i => i.name).join(', ') || 'bien-être';

      if (language === 'fr') {
        return `${userName}, cette recommandation est spécialement pour vous car elle correspond à vos intérêts en ${userInterests} et à vos préférences personnelles.`;
      } else {
        return `${userName}, this recommendation is specially for you because it matches your interests in ${userInterests} and your personal preferences.`;
      }
    }
  }

  /**
   * Extrait les préférences de l'utilisateur
   * @param userProfile Profil utilisateur
   * @returns Liste des préférences
   */
  private extractUserPreferences(userProfile: any): string[] {
    const preferences: string[] = [];

    // Dans une implémentation réelle, on extrairait les préférences du profil utilisateur
    // Pour l'instant, on génère des préférences fictives
    if (Math.random() > 0.3) preferences.push('retraites de courte durée');
    if (Math.random() > 0.4) preferences.push('activités en plein air');
    if (Math.random() > 0.5) preferences.push('cuisine végétarienne');
    if (Math.random() > 0.6) preferences.push('hébergement confortable');
    if (Math.random() > 0.7) preferences.push('destinations proches');

    return preferences.slice(0, 2); // Limiter à 2 préférences
  }

  /**
   * Extrait l'historique des interactions de l'utilisateur
   * @param userProfile Profil utilisateur
   * @returns Liste des interactions
   */
  private extractUserHistory(userProfile: any): string[] {
    const history: string[] = [];

    // Dans une implémentation réelle, on extrairait l'historique des interactions
    // Pour l'instant, on génère un historique fictif
    const actions = [
      'consulté des retraites similaires',
      'participé à une retraite de yoga',
      'montré de l\'intérêt pour ce type d\'activité',
      'recherché des retraites dans cette région',
      'apprécié des retraites avec des thématiques similaires'
    ];

    return [actions[Math.floor(Math.random() * actions.length)]];
  }

  /**
   * Extrait le contexte actuel de l'utilisateur
   * @param userProfile Profil utilisateur
   * @returns Liste des éléments de contexte
   */
  private extractCurrentContext(userProfile: any): string[] {
    const context: string[] = [];

    // Dans une implémentation réelle, on extrairait le contexte actuel
    // Pour l'instant, on génère un contexte fictif
    const contextElements = [
      'cette retraite se déroule pendant une période qui vous convient habituellement',
      'la saison est idéale pour ce type d\'activité',
      'cette retraite est facilement accessible depuis votre localisation',
      'le prix correspond à votre budget habituel',
      'le niveau de difficulté est adapté à votre expérience'
    ];

    return [contextElements[Math.floor(Math.random() * contextElements.length)]];
  }

  /**
   * Traduit un texte en anglais (simulation)
   * @param text Texte à traduire
   * @returns Texte traduit
   */
  private translateToEnglish(text: string): string {
    // Dans une implémentation réelle, on utiliserait un service de traduction
    // Pour l'instant, on simule une traduction avec des remplacements simples
    return text
      .replace('retraites de courte durée', 'short retreats')
      .replace('activités en plein air', 'outdoor activities')
      .replace('cuisine végétarienne', 'vegetarian cuisine')
      .replace('hébergement confortable', 'comfortable accommodation')
      .replace('destinations proches', 'nearby destinations')
      .replace('consulté des retraites similaires', 'viewed similar retreats')
      .replace('participé à une retraite de yoga', 'participated in a yoga retreat')
      .replace('montré de l\'intérêt pour ce type d\'activité', 'shown interest in this type of activity')
      .replace('recherché des retraites dans cette région', 'searched for retreats in this region')
      .replace('apprécié des retraites avec des thématiques similaires', 'enjoyed retreats with similar themes')
      .replace('cette retraite se déroule pendant une période qui vous convient habituellement', 'this retreat takes place during a period that usually suits you')
      .replace('la saison est idéale pour ce type d\'activité', 'the season is ideal for this type of activity')
      .replace('cette retraite est facilement accessible depuis votre localisation', 'this retreat is easily accessible from your location')
      .replace('le prix correspond à votre budget habituel', 'the price matches your usual budget')
      .replace('le niveau de difficulté est adapté à votre expérience', 'the difficulty level is adapted to your experience')
      .replace('bien-être', 'wellness');
  }

  /**
   * Génère des valeurs de correspondance avec les préférences utilisateur
   * @param recommendation Recommandation
   * @param factors Facteurs d'explication
   * @returns Valeurs de correspondance
   */
  private generatePreferenceMatchValues(recommendation: any, factors: ExplanationFactorDto[]): number[] {
    // Dans une implémentation réelle, ces valeurs seraient calculées en fonction
    // des préférences de l'utilisateur et des caractéristiques de la recommandation
    // Pour l'instant, on génère des valeurs aléatoires mais réalistes
    return [
      this.getFactorWeightByName(factors, 'Thème') || this.randomBetween(0.7, 0.95),
      this.getFactorWeightByName(factors, 'Style') || this.randomBetween(0.6, 0.9),
      this.getFactorWeightByName(factors, 'Difficulté') || this.randomBetween(0.5, 0.85),
      this.getFactorWeightByName(factors, 'Durée') || this.randomBetween(0.6, 0.8),
      this.getFactorWeightByName(factors, 'Prix') || this.randomBetween(0.5, 0.75),
      this.getFactorWeightByName(factors, 'Localisation') || this.randomBetween(0.4, 0.8),
      this.getFactorWeightByName(factors, 'Saison') || this.randomBetween(0.5, 0.9),
    ];
  }

  /**
   * Récupère le poids d'un facteur par son nom
   * @param factors Liste des facteurs
   * @param name Nom du facteur
   * @returns Poids du facteur ou undefined si non trouvé
   */
  private getFactorWeightByName(factors: ExplanationFactorDto[], name: string): number | undefined {
    const factor = factors.find(f => f.name.toLowerCase().includes(name.toLowerCase()));
    return factor?.weight;
  }

  /**
   * Génère un nombre aléatoire entre min et max
   * @param min Valeur minimale
   * @param max Valeur maximale
   * @returns Nombre aléatoire
   */
  private randomBetween(min: number, max: number): number {
    return Math.round((min + Math.random() * (max - min)) * 100) / 100;
  }

  /**
   * Groupe les facteurs par catégorie
   * @param factors Liste des facteurs
   * @returns Objet avec les catégories et leurs poids cumulés
   */
  private groupFactorsByCategory(factors: ExplanationFactorDto[]): Record<string, number> {
    const categories: Record<string, number> = {};

    factors.forEach(factor => {
      // Déterminer la catégorie du facteur
      let category = 'Autre';

      if (factor.name.toLowerCase().includes('thème') || factor.name.toLowerCase().includes('catégorie')) {
        category = 'Thématique';
      } else if (factor.name.toLowerCase().includes('style') || factor.name.toLowerCase().includes('type')) {
        category = 'Style';
      } else if (factor.name.toLowerCase().includes('préférence') || factor.name.toLowerCase().includes('intérêt')) {
        category = 'Préférences';
      } else if (factor.name.toLowerCase().includes('popularité') || factor.name.toLowerCase().includes('tendance')) {
        category = 'Popularité';
      } else if (factor.name.toLowerCase().includes('similaire') || factor.name.toLowerCase().includes('recommandé')) {
        category = 'Similarité';
      } else if (factor.name.toLowerCase().includes('localisation') || factor.name.toLowerCase().includes('lieu')) {
        category = 'Localisation';
      }

      // Ajouter le poids à la catégorie
      if (!categories[category]) {
        categories[category] = 0;
      }

      categories[category] += factor.weight;
    });

    // Normaliser les poids pour qu'ils somment à 1
    const total = Object.values(categories).reduce((sum, weight) => sum + weight, 0);

    if (total > 0) {
      Object.keys(categories).forEach(category => {
        categories[category] = Math.round((categories[category] / total) * 100) / 100;
      });
    }

    return categories;
  }

  /**
   * Génère des données de comparaison avec d'autres recommandations
   * @param recommendationId ID de la recommandation
   * @param recommendationType Type de recommandation
   * @returns Données de comparaison
   */
  private async generateComparisonData(
    recommendationId: string,
    recommendationType: RecommendationType,
  ): Promise<{ labels: string[]; values: number[]; colors: string[] } | null> {
    try {
      // Dans une implémentation réelle, on récupérerait des recommandations similaires
      // Pour l'instant, on génère des données fictives
      return {
        labels: ['Cette recommandation', 'Moyenne des recommandations', 'Recommandation la plus populaire'],
        values: [0.85, 0.7, 0.9],
        colors: ['#4285F4', '#34A853', '#FBBC05'],
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des données de comparaison: ${error.message}`);
      return null;
    }
  }

  /**
   * Génère des données de timeline pour les interactions passées
   * @param recommendationId ID de la recommandation
   * @param recommendationType Type de recommandation
   * @returns Données de timeline
   */
  private async generateTimelineData(
    recommendationId: string,
    recommendationType: RecommendationType,
  ): Promise<{ events: { date: string; action: string; description: string }[] } | null> {
    try {
      // Dans une implémentation réelle, on récupérerait l'historique des interactions
      // Pour l'instant, on génère des données fictives
      const now = new Date();

      return {
        events: [
          {
            date: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            action: 'Recherche',
            description: 'Vous avez recherché des retraites similaires',
          },
          {
            date: new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000).toISOString(),
            action: 'Consultation',
            description: 'Vous avez consulté une retraite similaire',
          },
          {
            date: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            action: 'Interaction',
            description: 'Vous avez interagi avec du contenu similaire',
          },
        ],
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des données de timeline: ${error.message}`);
      return null;
    }
  }
}
