import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ExternalData, ExternalDataType } from '../../interfaces/external-data.interface';
import { RecommendationType } from '../../enums/recommendation-type.enum';

/**
 * Service de connexion à l'API météo
 * Récupère les données météorologiques pour enrichir les recommandations
 */
@Injectable()
export class WeatherConnectorService {
  private readonly logger = new Logger(WeatherConnectorService.name);
  private readonly apiKey: string;
  private readonly baseUrl: string;
  private readonly enabled: boolean;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.apiKey = this.configService.get<string>('recommendation.externalData.apiKeys.openWeatherMap', '');
    this.baseUrl = 'https://api.openweathermap.org/data/2.5';
    this.enabled = this.configService.get<boolean>('recommendation.externalData.sources.openWeatherMap.enabled', false);
    
    this.logger.log(`WeatherConnectorService initialized with enabled=${this.enabled}`);
  }

  /**
   * Récupère les données météo actuelles pour une localisation
   * @param location Localisation (ville ou coordonnées)
   * @param language Langue des données
   * @returns Données météo formatées
   */
  async getCurrentWeather(location: string | { lat: number; lon: number }, language: string = 'fr'): Promise<ExternalData[]> {
    if (!this.enabled || !this.apiKey) {
      this.logger.warn('Weather connector is disabled or API key is missing');
      return [];
    }

    try {
      // Construire les paramètres de la requête
      const params: Record<string, any> = {
        appid: this.apiKey,
        units: 'metric',
        lang: language,
      };

      // Ajouter la localisation
      if (typeof location === 'string') {
        params.q = location;
      } else {
        params.lat = location.lat;
        params.lon = location.lon;
      }

      // Effectuer la requête
      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/weather`, { params })
      );

      // Vérifier la réponse
      if (response.status !== 200) {
        throw new Error(`Weather API returned status ${response.status}`);
      }

      // Transformer les données
      return this.transformWeatherData(response.data);
    } catch (error) {
      this.logger.error(`Error fetching weather data: ${error.message}`);
      return [];
    }
  }

  /**
   * Récupère les prévisions météo pour une localisation
   * @param location Localisation (ville ou coordonnées)
   * @param days Nombre de jours de prévision
   * @param language Langue des données
   * @returns Données météo formatées
   */
  async getForecast(
    location: string | { lat: number; lon: number },
    days: number = 5,
    language: string = 'fr'
  ): Promise<ExternalData[]> {
    if (!this.enabled || !this.apiKey) {
      this.logger.warn('Weather connector is disabled or API key is missing');
      return [];
    }

    try {
      // Construire les paramètres de la requête
      const params: Record<string, any> = {
        appid: this.apiKey,
        units: 'metric',
        lang: language,
        cnt: days,
      };

      // Ajouter la localisation
      if (typeof location === 'string') {
        params.q = location;
      } else {
        params.lat = location.lat;
        params.lon = location.lon;
      }

      // Effectuer la requête
      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/forecast`, { params })
      );

      // Vérifier la réponse
      if (response.status !== 200) {
        throw new Error(`Weather API returned status ${response.status}`);
      }

      // Transformer les données
      return this.transformForecastData(response.data);
    } catch (error) {
      this.logger.error(`Error fetching weather forecast: ${error.message}`);
      return [];
    }
  }

  /**
   * Transforme les données météo actuelles en format standardisé
   * @param data Données brutes de l'API
   * @returns Données météo formatées
   */
  private transformWeatherData(data: any): ExternalData[] {
    try {
      const weather = data.weather[0];
      const main = data.main;
      const wind = data.wind;
      const sys = data.sys;
      const cityName = data.name;
      const country = sys.country;

      // Créer l'objet de données externes
      const externalData: ExternalData = {
        type: ExternalDataType.WEATHER,
        source: 'OpenWeatherMap',
        title: `Météo actuelle à ${cityName}, ${country}`,
        content: `${weather.description}. Température: ${main.temp}°C, ressenti: ${main.feels_like}°C`,
        relevanceScore: 0.8,
        metadata: {
          temperature: main.temp,
          feelsLike: main.feels_like,
          humidity: main.humidity,
          pressure: main.pressure,
          windSpeed: wind.speed,
          windDirection: wind.deg,
          weatherMain: weather.main,
          weatherDescription: weather.description,
          weatherIcon: weather.icon,
          cityName,
          country,
          coordinates: {
            lat: data.coord.lat,
            lon: data.coord.lon,
          },
          timestamp: new Date(data.dt * 1000),
          sunrise: new Date(sys.sunrise * 1000),
          sunset: new Date(sys.sunset * 1000),
        },
        applicableTypes: [
          RecommendationType.RETREAT,
          RecommendationType.ACTIVITY,
          RecommendationType.EVENT,
        ],
      };

      return [externalData];
    } catch (error) {
      this.logger.error(`Error transforming weather data: ${error.message}`);
      return [];
    }
  }

  /**
   * Transforme les données de prévision météo en format standardisé
   * @param data Données brutes de l'API
   * @returns Données météo formatées
   */
  private transformForecastData(data: any): ExternalData[] {
    try {
      const cityName = data.city.name;
      const country = data.city.country;
      const forecasts: ExternalData[] = [];

      // Regrouper les prévisions par jour
      const dailyForecasts = this.groupForecastsByDay(data.list);

      // Créer un objet de données externes pour chaque jour
      for (const [date, items] of Object.entries(dailyForecasts)) {
        const dayData = items[0];
        const weather = dayData.weather[0];
        const main = dayData.main;

        forecasts.push({
          type: ExternalDataType.WEATHER,
          source: 'OpenWeatherMap',
          title: `Prévisions météo pour ${cityName}, ${country} le ${date}`,
          content: `${weather.description}. Température: ${main.temp}°C, ressenti: ${main.feels_like}°C`,
          relevanceScore: 0.7,
          metadata: {
            date,
            forecasts: items.map(item => ({
              time: new Date(item.dt * 1000),
              temperature: item.main.temp,
              feelsLike: item.main.feels_like,
              humidity: item.main.humidity,
              pressure: item.main.pressure,
              weatherMain: item.weather[0].main,
              weatherDescription: item.weather[0].description,
              weatherIcon: item.weather[0].icon,
              windSpeed: item.wind.speed,
              windDirection: item.wind.deg,
            })),
            cityName,
            country,
            coordinates: {
              lat: data.city.coord.lat,
              lon: data.city.coord.lon,
            },
          },
          applicableTypes: [
            RecommendationType.RETREAT,
            RecommendationType.ACTIVITY,
            RecommendationType.EVENT,
          ],
        });
      }

      return forecasts;
    } catch (error) {
      this.logger.error(`Error transforming forecast data: ${error.message}`);
      return [];
    }
  }

  /**
   * Regroupe les prévisions par jour
   * @param forecasts Liste des prévisions
   * @returns Prévisions regroupées par jour
   */
  private groupForecastsByDay(forecasts: any[]): Record<string, any[]> {
    const dailyForecasts: Record<string, any[]> = {};

    for (const forecast of forecasts) {
      const date = new Date(forecast.dt * 1000).toISOString().split('T')[0];
      
      if (!dailyForecasts[date]) {
        dailyForecasts[date] = [];
      }
      
      dailyForecasts[date].push(forecast);
    }

    return dailyForecasts;
  }
}
