import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AgentRbClient } from '../clients/agent-rb-client.optimized';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationResponseDto, RecommendationMetadataDto } from '../dto/recommendation-response.dto';
import * as pLimit from 'p-limit';

/**
 * Service d'intégration optimisé avec Agent-RB pour le système de recommandation
 */
@Injectable()
export class AgentRbIntegrationService {
  private readonly logger = new Logger(AgentRbIntegrationService.name);
  private readonly concurrencyLimit: ReturnType<typeof pLimit>;
  private readonly maxConcurrentItems: number;

  constructor(
    private readonly agentRbClient: AgentRbClient,
    private readonly configService: ConfigService,
  ) {
    this.maxConcurrentItems = this.configService.get<number>('recommendation.performance.maxConcurrentItems', 20);
    this.concurrencyLimit = pLimit(this.maxConcurrentItems);
    
    this.logger.log(`AgentRbIntegrationService initialized with maxConcurrentItems=${this.maxConcurrentItems}`);
  }

  /**
   * Enrichit une liste de recommandations avec les données détaillées d'Agent-RB
   * @param recommendations Liste de recommandations à enrichir
   * @param type Type d'élément
   * @param includeMetadata Inclure les métadonnées détaillées
   * @returns Liste de recommandations enrichies
   */
  async enrichRecommendations(
    recommendations: any[],
    type: RecommendationType,
    includeMetadata: boolean = true,
  ): Promise<RecommendationResponseDto[]> {
    this.logger.debug(`Enriching ${recommendations.length} ${type} recommendations`);

    // Si la liste est vide, retourner immédiatement
    if (!recommendations || recommendations.length === 0) {
      return [];
    }

    try {
      // Enrichir chaque recommandation en parallèle avec limitation de concurrence
      const enrichedRecommendationsPromises = recommendations.map(recommendation =>
        this.concurrencyLimit(async () => {
          try {
            return await this.enrichRecommendation(recommendation, type, includeMetadata);
          } catch (error) {
            this.logger.error(`Error enriching recommendation ${recommendation.id}: ${error.message}`);
            // Retourner la recommandation non enrichie plutôt que de la perdre
            return this.createBasicRecommendationResponse(recommendation, type);
          }
        })
      );

      // Attendre que toutes les recommandations soient enrichies
      const enrichedRecommendations = await Promise.all(enrichedRecommendationsPromises);

      // Filtrer les recommandations nulles (en cas d'erreur)
      return enrichedRecommendations.filter(Boolean);
    } catch (error) {
      this.logger.error(`Error enriching recommendations: ${error.message}`);
      // En cas d'erreur globale, retourner les recommandations de base
      return recommendations.map(recommendation => 
        this.createBasicRecommendationResponse(recommendation, type)
      );
    }
  }

  /**
   * Enrichit une recommandation avec les données détaillées d'Agent-RB
   * @param recommendation Recommandation à enrichir
   * @param type Type d'élément
   * @param includeMetadata Inclure les métadonnées détaillées
   * @returns Recommandation enrichie
   */
  async enrichRecommendation(
    recommendation: any,
    type: RecommendationType,
    includeMetadata: boolean = true,
  ): Promise<RecommendationResponseDto> {
    this.logger.debug(`Enriching recommendation ${recommendation.id} of type ${type}`);

    try {
      // Récupérer les détails de l'élément depuis Agent-RB (avec cache)
      const itemDetails = await this.agentRbClient.getItem(type, recommendation.id);

      if (!itemDetails) {
        throw new NotFoundException(`Item ${recommendation.id} not found in Agent-RB`);
      }

      // Créer la réponse enrichie
      const enrichedRecommendation: RecommendationResponseDto = {
        id: recommendation.id,
        type: type,
        title: itemDetails.title || itemDetails.name || `${type} ${recommendation.id}`,
        description: itemDetails.description || undefined,
        score: recommendation.score || 0,
        sources: recommendation.sources || [],
        reasons: recommendation.reasons || [],
        imageUrl: itemDetails.imageUrl || itemDetails.image_url || undefined,
        url: this.generateItemUrl(type, recommendation.id),
      };

      // Ajouter les métadonnées si demandé
      if (includeMetadata) {
        enrichedRecommendation.metadata = this.extractMetadata(itemDetails, type);
      }

      return enrichedRecommendation;
    } catch (error) {
      this.logger.error(`Error enriching recommendation ${recommendation.id}: ${error.message}`);
      
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      // En cas d'erreur, retourner une recommandation de base
      return this.createBasicRecommendationResponse(recommendation, type);
    }
  }

  /**
   * Crée une réponse de recommandation de base à partir des données minimales
   * @param recommendation Données de base de la recommandation
   * @param type Type d'élément
   * @returns Réponse de recommandation de base
   */
  private createBasicRecommendationResponse(
    recommendation: any,
    type: RecommendationType,
  ): RecommendationResponseDto {
    return {
      id: recommendation.id,
      type: type,
      title: recommendation.title || `${type} ${recommendation.id}`,
      description: recommendation.description,
      score: recommendation.score || 0,
      sources: recommendation.sources || [],
      reasons: recommendation.reasons || [],
      url: this.generateItemUrl(type, recommendation.id),
    };
  }

  /**
   * Extrait les métadonnées d'un élément en fonction de son type
   * @param itemDetails Détails de l'élément
   * @param type Type d'élément
   * @returns Métadonnées de l'élément
   */
  private extractMetadata(
    itemDetails: any,
    type: RecommendationType,
  ): RecommendationMetadataDto {
    const metadata: RecommendationMetadataDto = {};

    // Extraire les métadonnées communes
    if (itemDetails.category) metadata.category = itemDetails.category;
    if (itemDetails.level) metadata.level = itemDetails.level;
    if (itemDetails.tags) metadata.tags = Array.isArray(itemDetails.tags) ? itemDetails.tags : [itemDetails.tags];
    if (itemDetails.price) metadata.price = parseFloat(itemDetails.price);
    if (itemDetails.currency) metadata.currency = itemDetails.currency;
    if (itemDetails.review_count || itemDetails.reviewCount) metadata.reviewCount = itemDetails.review_count || itemDetails.reviewCount;
    if (itemDetails.average_rating || itemDetails.averageRating) metadata.averageRating = itemDetails.average_rating || itemDetails.averageRating;
    if (itemDetails.available !== undefined) metadata.available = !!itemDetails.available;

    // Extraire les métadonnées spécifiques au type
    switch (type) {
      case RecommendationType.RETREAT:
        this.extractRetreatMetadata(itemDetails, metadata);
        break;
      case RecommendationType.PARTNER:
        this.extractPartnerMetadata(itemDetails, metadata);
        break;
      case RecommendationType.COURSE:
        this.extractCourseMetadata(itemDetails, metadata);
        break;
    }

    // Ajouter les propriétés additionnelles non extraites
    metadata.additionalProperties = this.extractAdditionalProperties(itemDetails, metadata);

    return metadata;
  }

  /**
   * Extrait les métadonnées spécifiques à une retraite
   * @param retreatDetails Détails de la retraite
   * @param metadata Objet de métadonnées à compléter
   */
  private extractRetreatMetadata(
    retreatDetails: any,
    metadata: RecommendationMetadataDto,
  ): void {
    if (retreatDetails.location) metadata.location = retreatDetails.location;
    if (retreatDetails.duration) metadata.duration = parseInt(retreatDetails.duration);
    if (retreatDetails.start_date || retreatDetails.startDate) metadata.startDate = retreatDetails.start_date || retreatDetails.startDate;
    if (retreatDetails.end_date || retreatDetails.endDate) metadata.endDate = retreatDetails.end_date || retreatDetails.endDate;
    if (retreatDetails.remaining_spots || retreatDetails.remainingSpots) metadata.remainingSpots = retreatDetails.remaining_spots || retreatDetails.remainingSpots;
  }

  /**
   * Extrait les métadonnées spécifiques à un partenaire
   * @param partnerDetails Détails du partenaire
   * @param metadata Objet de métadonnées à compléter
   */
  private extractPartnerMetadata(
    partnerDetails: any,
    metadata: RecommendationMetadataDto,
  ): void {
    if (partnerDetails.specialties) metadata.tags = Array.isArray(partnerDetails.specialties) ? partnerDetails.specialties : [partnerDetails.specialties];
    if (partnerDetails.location) metadata.location = partnerDetails.location;
    if (partnerDetails.experience_years) metadata.additionalProperties = { 
      ...metadata.additionalProperties,
      experienceYears: partnerDetails.experience_years 
    };
  }

  /**
   * Extrait les métadonnées spécifiques à un cours
   * @param courseDetails Détails du cours
   * @param metadata Objet de métadonnées à compléter
   */
  private extractCourseMetadata(
    courseDetails: any,
    metadata: RecommendationMetadataDto,
  ): void {
    if (courseDetails.duration) metadata.duration = parseInt(courseDetails.duration);
    if (courseDetails.difficulty) metadata.level = courseDetails.difficulty;
    if (courseDetails.prerequisites) metadata.additionalProperties = { 
      ...metadata.additionalProperties,
      prerequisites: courseDetails.prerequisites 
    };
  }

  /**
   * Extrait les propriétés additionnelles non déjà extraites
   * @param itemDetails Détails de l'élément
   * @param metadata Métadonnées déjà extraites
   * @returns Propriétés additionnelles
   */
  private extractAdditionalProperties(
    itemDetails: any,
    metadata: RecommendationMetadataDto,
  ): Record<string, any> {
    const additionalProps: Record<string, any> = {};
    const extractedKeys = new Set(Object.keys(metadata));
    
    // Propriétés à ignorer (déjà extraites ou non pertinentes)
    const ignoredKeys = new Set([
      'id', 'title', 'name', 'description', 'imageUrl', 'image_url', 
      'score', 'sources', 'reasons', 'url', 'created_at', 'updated_at'
    ]);
    
    // Extraire les propriétés non déjà extraites
    Object.entries(itemDetails).forEach(([key, value]) => {
      if (!extractedKeys.has(key) && !ignoredKeys.has(key) && value !== null && value !== undefined) {
        // Convertir les clés snake_case en camelCase
        const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
        additionalProps[camelKey] = value;
      }
    });
    
    return Object.keys(additionalProps).length > 0 ? additionalProps : undefined;
  }

  /**
   * Génère l'URL d'un élément en fonction de son type
   * @param type Type d'élément
   * @param itemId ID de l'élément
   * @returns URL de l'élément
   */
  private generateItemUrl(type: RecommendationType, itemId: string): string {
    const baseUrl = 'https://retreatandbe.com';
    
    switch (type) {
      case RecommendationType.RETREAT:
        return `${baseUrl}/retreats/${itemId}`;
      case RecommendationType.PARTNER:
        return `${baseUrl}/partners/${itemId}`;
      case RecommendationType.COURSE:
        return `${baseUrl}/courses/${itemId}`;
      default:
        return `${baseUrl}/${type.toLowerCase()}s/${itemId}`;
    }
  }

  /**
   * Précharge les éléments populaires pour améliorer les performances
   * @param type Type d'élément
   * @param itemIds IDs des éléments à précharger
   */
  async preloadPopularItems(type: RecommendationType, itemIds: string[]): Promise<void> {
    this.logger.log(`Preloading ${itemIds.length} popular ${type} items`);
    
    // Limiter le nombre d'éléments à précharger en parallèle
    const preloadPromises = itemIds.map(itemId =>
      this.concurrencyLimit(() => this.agentRbClient.getItem(type, itemId).catch(error => {
        this.logger.warn(`Failed to preload item ${itemId} of type ${type}: ${error.message}`);
        return null;
      }))
    );
    
    await Promise.all(preloadPromises);
    this.logger.log(`Preloading of ${type} items completed`);
  }
}
