import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { Cron } from '@nestjs/schedule';
import { v4 as uuidv4 } from 'uuid';
import {
  AdvancedContextConfig,
  ContextSource,
  ContextSourceType,
  ContextDetectionStrategy,
  ContextAdaptationStrategy,
  ContextWeightingConfig,
  ContextWeightingStrategy,
  ContextPersistenceConfig,
  ContextPersistenceStrategy,
  ContextData,
  ContextualRecommendationRequest,
  ContextualRecommendationResult,
} from '../interfaces/advanced-context.interface';
import { RecommendationService } from './recommendation.service';

/**
 * Service for advanced contextual recommendations
 */
@Injectable()
export class AdvancedContextService implements OnModuleInit {
  private readonly logger = new Logger(AdvancedContextService.name);
  private contextConfig: AdvancedContextConfig;
  private readonly contextCache: Map<string, ContextData> = new Map();
  private readonly weatherCache: Map<string, any> = new Map();
  private readonly localEventsCache: Map<string, any[]> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly recommendationService: RecommendationService,
  ) {
    // Initialize configurations
    this.initializeConfigurations();
  }

  /**
   * Initialize on module initialization
   */
  async onModuleInit() {
    try {
      // Initialize context sources
      await this.initializeContextSources();

      this.logger.log('Advanced context service initialized');
    } catch (error) {
      this.logger.error(`Error initializing advanced context service: ${error.message}`);
    }
  }

  /**
   * Initialize configurations
   */
  private initializeConfigurations(): void {
    try {
      // Initialize context configuration
      this.contextConfig = {
        enabled: this.configService.get<boolean>('ADVANCED_CONTEXT_ENABLED', true),
        contextSources: this.getDefaultContextSources(),
        detectionStrategies: [
          ContextDetectionStrategy.RULE_BASED,
          ContextDetectionStrategy.USER_PROVIDED,
          ContextDetectionStrategy.ML_BASED,
        ],
        adaptationStrategies: [
          ContextAdaptationStrategy.PRE_FILTERING,
          ContextAdaptationStrategy.CONTEXTUAL_MODELING,
        ],
        contextWeighting: {
          enabled: true,
          strategy: ContextWeightingStrategy.ADAPTIVE,
          defaultWeights: {
            [ContextSourceType.LOCATION]: 0.8,
            [ContextSourceType.WEATHER]: 0.7,
            [ContextSourceType.TIME_OF_DAY]: 0.6,
            [ContextSourceType.DAY_OF_WEEK]: 0.5,
            [ContextSourceType.SEASON]: 0.5,
            [ContextSourceType.DEVICE]: 0.3,
            [ContextSourceType.ACTIVITY]: 0.7,
            [ContextSourceType.LOCAL_EVENTS]: 0.6,
            [ContextSourceType.MOOD]: 0.8,
            [ContextSourceType.CALENDAR]: 0.7,
            [ContextSourceType.SOCIAL]: 0.5,
            [ContextSourceType.HEALTH]: 0.6,
            [ContextSourceType.CUSTOM]: 0.4,
          },
        },
        contextPersistence: {
          enabled: true,
          strategy: ContextPersistenceStrategy.IN_MEMORY,
          ttl: 3600, // 1 hour
        },
        params: {
          enableContextLearning: true,
          contextHistorySize: 10,
          contextSimilarityThreshold: 0.7,
        },
      };

      this.logger.log('Advanced context configurations initialized');
    } catch (error) {
      this.logger.error(`Error initializing configurations: ${error.message}`);
    }
  }

  /**
   * Get default context sources
   * @returns Default context sources
   */
  private getDefaultContextSources(): ContextSource[] {
    return [
      {
        type: ContextSourceType.LOCATION,
        name: 'User Location',
        enabled: true,
        priority: 9,
        refreshInterval: 3600, // 1 hour
      },
      {
        type: ContextSourceType.WEATHER,
        name: 'Weather Data',
        enabled: true,
        priority: 8,
        config: {
          provider: 'openweathermap',
          units: 'metric',
        },
        refreshInterval: 3600, // 1 hour
      },
      {
        type: ContextSourceType.TIME_OF_DAY,
        name: 'Time of Day',
        enabled: true,
        priority: 7,
        refreshInterval: 900, // 15 minutes
      },
      {
        type: ContextSourceType.DAY_OF_WEEK,
        name: 'Day of Week',
        enabled: true,
        priority: 6,
        refreshInterval: 86400, // 1 day
      },
      {
        type: ContextSourceType.SEASON,
        name: 'Season',
        enabled: true,
        priority: 6,
        refreshInterval: 86400, // 1 day
      },
      {
        type: ContextSourceType.DEVICE,
        name: 'User Device',
        enabled: true,
        priority: 4,
        refreshInterval: 86400, // 1 day
      },
      {
        type: ContextSourceType.ACTIVITY,
        name: 'User Activity',
        enabled: true,
        priority: 8,
        refreshInterval: 900, // 15 minutes
      },
      {
        type: ContextSourceType.LOCAL_EVENTS,
        name: 'Local Events',
        enabled: true,
        priority: 7,
        config: {
          radius: 10, // km
          categories: ['wellness', 'yoga', 'meditation', 'retreat'],
        },
        refreshInterval: 86400, // 1 day
      },
      {
        type: ContextSourceType.MOOD,
        name: 'User Mood',
        enabled: true,
        priority: 8,
        refreshInterval: 3600, // 1 hour
      },
      {
        type: ContextSourceType.CALENDAR,
        name: 'User Calendar',
        enabled: true,
        priority: 7,
        refreshInterval: 3600, // 1 hour
      },
      {
        type: ContextSourceType.SOCIAL,
        name: 'Social Context',
        enabled: true,
        priority: 6,
        refreshInterval: 3600, // 1 hour
      },
      {
        type: ContextSourceType.HEALTH,
        name: 'Health Data',
        enabled: true,
        priority: 7,
        refreshInterval: 3600, // 1 hour
      },
    ];
  }

  /**
   * Initialize context sources
   */
  private async initializeContextSources(): Promise<void> {
    try {
      // Initialize weather cache
      await this.refreshWeatherCache();

      // Initialize local events cache
      await this.refreshLocalEventsCache();

      this.logger.log('Context sources initialized');
    } catch (error) {
      this.logger.error(`Error initializing context sources: ${error.message}`);
    }
  }

  /**
   * Refresh weather cache
   */
  private async refreshWeatherCache(): Promise<void> {
    try {
      // In a real implementation, this would fetch weather data from an API
      // For now, we'll just populate with mock data
      const locations = ['Paris', 'London', 'New York', 'Tokyo', 'Sydney'];
      const weatherConditions = ['sunny', 'cloudy', 'rainy', 'snowy', 'windy'];
      const temperatures = [5, 10, 15, 20, 25, 30];

      for (const location of locations) {
        const weather = {
          location,
          condition: weatherConditions[Math.floor(Math.random() * weatherConditions.length)],
          temperature: temperatures[Math.floor(Math.random() * temperatures.length)],
          humidity: Math.floor(Math.random() * 100),
          timestamp: new Date(),
        };

        this.weatherCache.set(location, weather);
      }

      this.logger.debug('Weather cache refreshed');
    } catch (error) {
      this.logger.error(`Error refreshing weather cache: ${error.message}`);
    }
  }

  /**
   * Refresh local events cache
   */
  private async refreshLocalEventsCache(): Promise<void> {
    try {
      // In a real implementation, this would fetch local events from an API
      // For now, we'll just populate with mock data
      const locations = ['Paris', 'London', 'New York', 'Tokyo', 'Sydney'];
      const eventTypes = ['yoga', 'meditation', 'wellness', 'retreat', 'workshop'];

      for (const location of locations) {
        const events = [];

        // Generate 3-5 random events per location
        const eventCount = 3 + Math.floor(Math.random() * 3);

        for (let i = 0; i < eventCount; i++) {
          const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
          const startDate = new Date();
          startDate.setDate(startDate.getDate() + Math.floor(Math.random() * 14)); // Within next 2 weeks

          const event = {
            id: uuidv4(),
            title: `${eventType.charAt(0).toUpperCase() + eventType.slice(1)} Event`,
            type: eventType,
            location,
            startDate,
            endDate: new Date(startDate.getTime() + 3600000), // 1 hour later
            description: `A ${eventType} event in ${location}`,
          };

          events.push(event);
        }

        this.localEventsCache.set(location, events);
      }

      this.logger.debug('Local events cache refreshed');
    } catch (error) {
      this.logger.error(`Error refreshing local events cache: ${error.message}`);
    }

  /**
   * Get context data for a user
   * @param userId User ID
   * @param explicitContext Explicit context data
   * @returns Context data
   */
  async getContextData(userId: string, explicitContext?: Record<string, any>): Promise<ContextData> {
    try {
      // Check if context is cached and not expired
      const cachedContext = this.contextCache.get(userId);
      const now = new Date();

      if (cachedContext &&
          (now.getTime() - cachedContext.timestamp.getTime()) / 1000 < this.contextConfig.contextPersistence.ttl) {
        // Merge with explicit context if provided
        if (explicitContext) {
          return this.mergeContextData(cachedContext, explicitContext);
        }
        return cachedContext;
      }

      // Collect context data from all enabled sources
      const contextData: ContextData = {
        userId,
        timestamp: now,
        values: {} as Record<ContextSourceType, any>,
        confidence: {} as Record<ContextSourceType, number>,
        metadata: {},
      };

      // Get context from each enabled source
      for (const source of this.contextConfig.contextSources) {
        if (!source.enabled) {
          continue;
        }

        const contextValue = await this.getContextFromSource(userId, source.type, explicitContext);
        if (contextValue !== null) {
          contextData.values[source.type] = contextValue.value;
          contextData.confidence[source.type] = contextValue.confidence;
        }
      }

      // Store in cache if persistence is enabled
      if (this.contextConfig.contextPersistence.enabled) {
        this.contextCache.set(userId, contextData);
      }

      return contextData;
    } catch (error) {
      this.logger.error(`Error getting context data for user ${userId}: ${error.message}`);

      // Return basic context data in case of error
      return {
        userId,
        timestamp: new Date(),
        values: {} as Record<ContextSourceType, any>,
        confidence: {} as Record<ContextSourceType, number>,
        metadata: { error: error.message },
      };
    }
  }

  /**
   * Merge context data with explicit context
   * @param contextData Existing context data
   * @param explicitContext Explicit context data
   * @returns Merged context data
   */
  private mergeContextData(contextData: ContextData, explicitContext: Record<string, any>): ContextData {
    const result = { ...contextData };

    // Map explicit context keys to context source types
    const keyToSourceType: Record<string, ContextSourceType> = {
      location: ContextSourceType.LOCATION,
      weather: ContextSourceType.WEATHER,
      timeOfDay: ContextSourceType.TIME_OF_DAY,
      dayOfWeek: ContextSourceType.DAY_OF_WEEK,
      season: ContextSourceType.SEASON,
      device: ContextSourceType.DEVICE,
      activity: ContextSourceType.ACTIVITY,
      localEvents: ContextSourceType.LOCAL_EVENTS,
      mood: ContextSourceType.MOOD,
      calendar: ContextSourceType.CALENDAR,
      social: ContextSourceType.SOCIAL,
      health: ContextSourceType.HEALTH,
    };

    // Update values and confidence for explicit context
    for (const [key, value] of Object.entries(explicitContext)) {
      const sourceType = keyToSourceType[key];
      if (sourceType) {
        result.values[sourceType] = value;
        result.confidence[sourceType] = 1.0; // Explicit context has highest confidence
      } else if (key === 'custom') {
        result.values[ContextSourceType.CUSTOM] = value;
        result.confidence[ContextSourceType.CUSTOM] = 1.0;
      }
    }

    return result;
  }

  /**
   * Get context from a specific source
   * @param userId User ID
   * @param sourceType Source type
   * @param explicitContext Explicit context data
   * @returns Context value and confidence
   */
  private async getContextFromSource(
    userId: string,
    sourceType: ContextSourceType,
    explicitContext?: Record<string, any>,
  ): Promise<{ value: any; confidence: number } | null> {
    try {
      // Check if explicit context contains this source type
      if (explicitContext) {
        const keyToSourceType: Record<string, ContextSourceType> = {
          location: ContextSourceType.LOCATION,
          weather: ContextSourceType.WEATHER,
          timeOfDay: ContextSourceType.TIME_OF_DAY,
          dayOfWeek: ContextSourceType.DAY_OF_WEEK,
          season: ContextSourceType.SEASON,
          device: ContextSourceType.DEVICE,
          activity: ContextSourceType.ACTIVITY,
          localEvents: ContextSourceType.LOCAL_EVENTS,
          mood: ContextSourceType.MOOD,
          calendar: ContextSourceType.CALENDAR,
          social: ContextSourceType.SOCIAL,
          health: ContextSourceType.HEALTH,
        };

        for (const [key, type] of Object.entries(keyToSourceType)) {
          if (type === sourceType && explicitContext[key] !== undefined) {
            return { value: explicitContext[key], confidence: 1.0 };
          }
        }

        if (sourceType === ContextSourceType.CUSTOM && explicitContext.custom !== undefined) {
          return { value: explicitContext.custom, confidence: 1.0 };
        }
      }

      // Get context based on source type
      switch (sourceType) {
        case ContextSourceType.LOCATION:
          return await this.getUserLocation(userId);

        case ContextSourceType.WEATHER:
          return await this.getWeatherContext(userId);

        case ContextSourceType.TIME_OF_DAY:
          return this.getTimeOfDayContext();

        case ContextSourceType.DAY_OF_WEEK:
          return this.getDayOfWeekContext();

        case ContextSourceType.SEASON:
          return this.getSeasonContext();

        case ContextSourceType.DEVICE:
          return await this.getUserDeviceContext(userId);

        case ContextSourceType.ACTIVITY:
          return await this.getUserActivityContext(userId);

        case ContextSourceType.LOCAL_EVENTS:
          return await this.getLocalEventsContext(userId);

        case ContextSourceType.MOOD:
          return await this.getUserMoodContext(userId);

        case ContextSourceType.CALENDAR:
          return await this.getUserCalendarContext(userId);

        case ContextSourceType.SOCIAL:
          return await this.getSocialContext(userId);

        case ContextSourceType.HEALTH:
          return await this.getHealthContext(userId);

        default:
          return null;
      }
    } catch (error) {
      this.logger.error(`Error getting context from source ${sourceType} for user ${userId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Get user location
   * @param userId User ID
   * @returns User location and confidence
   */
  private async getUserLocation(userId: string): Promise<{ value: any; confidence: number } | null> {
    try {
      // In a real implementation, this would fetch user location from the database or a location service
      // For now, we'll return mock data
      const locations = ['Paris', 'London', 'New York', 'Tokyo', 'Sydney'];
      const location = locations[Math.floor(Math.random() * locations.length)];

      return {
        value: {
          city: location,
          country: this.getCountryForCity(location),
          coordinates: this.getCoordinatesForCity(location),
          locationType: 'urban',
        },
        confidence: 0.9,
      };
    } catch (error) {
      this.logger.error(`Error getting user location for ${userId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Get weather context
   * @param userId User ID
   * @returns Weather context and confidence
   */
  private async getWeatherContext(userId: string): Promise<{ value: any; confidence: number } | null> {
    try {
      // Get user location first
      const locationContext = await this.getUserLocation(userId);
      if (!locationContext) {
        return null;
      }

      const city = locationContext.value.city;

      // Get weather data from cache
      const weather = this.weatherCache.get(city);
      if (!weather) {
        return null;
      }

      return {
        value: weather,
        confidence: 0.8,
      };
    } catch (error) {
      this.logger.error(`Error getting weather context for ${userId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Get time of day context
   * @returns Time of day context and confidence
   */
  private getTimeOfDayContext(): { value: any; confidence: number } {
    try {
      const now = new Date();
      const hour = now.getHours();

      let timeOfDay: string;
      if (hour >= 5 && hour < 12) {
        timeOfDay = 'morning';
      } else if (hour >= 12 && hour < 17) {
        timeOfDay = 'afternoon';
      } else if (hour >= 17 && hour < 21) {
        timeOfDay = 'evening';
      } else {
        timeOfDay = 'night';
      }

      return {
        value: {
          timeOfDay,
          hour,
          minute: now.getMinutes(),
        },
        confidence: 1.0, // Time is always accurate
      };
    } catch (error) {
      this.logger.error(`Error getting time of day context: ${error.message}`);
      return {
        value: { timeOfDay: 'unknown' },
        confidence: 0.0,
      };
    }
  }

  /**
   * Get day of week context
   * @returns Day of week context and confidence
   */
  private getDayOfWeekContext(): { value: any; confidence: number } {
    try {
      const now = new Date();
      const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.

      const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const dayType = (dayOfWeek === 0 || dayOfWeek === 6) ? 'weekend' : 'weekday';

      return {
        value: {
          dayOfWeek,
          dayName: dayNames[dayOfWeek],
          dayType,
        },
        confidence: 1.0, // Day is always accurate
      };
    } catch (error) {
      this.logger.error(`Error getting day of week context: ${error.message}`);
      return {
        value: { dayOfWeek: -1, dayType: 'unknown' },
        confidence: 0.0,
      };
    }
  }

  /**
   * Get season context
   * @returns Season context and confidence
   */
  private getSeasonContext(): { value: any; confidence: number } {
    try {
      const now = new Date();
      const month = now.getMonth(); // 0 = January, 11 = December

      let season: string;
      // Northern hemisphere seasons
      if (month >= 2 && month <= 4) {
        season = 'spring';
      } else if (month >= 5 && month <= 7) {
        season = 'summer';
      } else if (month >= 8 && month <= 10) {
        season = 'fall';
      } else {
        season = 'winter';
      }

      return {
        value: {
          season,
          month,
        },
        confidence: 0.9, // High confidence but not perfect due to regional variations
      };
    } catch (error) {
      this.logger.error(`Error getting season context: ${error.message}`);
      return {
        value: { season: 'unknown' },
        confidence: 0.0,
      };
    }
  }

  /**
   * Get user device context
   * @param userId User ID
   * @returns User device context and confidence
   */
  private async getUserDeviceContext(userId: string): Promise<{ value: any; confidence: number } | null> {
    try {
      // In a real implementation, this would fetch user device info from the database or user agent
      // For now, we'll return mock data
      const deviceTypes = ['mobile', 'tablet', 'desktop'];
      const deviceType = deviceTypes[Math.floor(Math.random() * deviceTypes.length)];

      const operatingSystems = ['iOS', 'Android', 'Windows', 'macOS', 'Linux'];
      const os = operatingSystems[Math.floor(Math.random() * operatingSystems.length)];

      return {
        value: {
          deviceType,
          os,
          browser: 'Chrome',
          screenSize: deviceType === 'mobile' ? 'small' : (deviceType === 'tablet' ? 'medium' : 'large'),
        },
        confidence: 0.8,
      };
    } catch (error) {
      this.logger.error(`Error getting user device context for ${userId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Get user activity context
   * @param userId User ID
   * @returns User activity context and confidence
   */
  private async getUserActivityContext(userId: string): Promise<{ value: any; confidence: number } | null> {
    try {
      // In a real implementation, this would fetch user activity from the database or activity tracking service
      // For now, we'll return mock data
      const activities = ['browsing', 'searching', 'reading', 'planning', 'booking'];
      const activity = activities[Math.floor(Math.random() * activities.length)];

      const categories = ['wellness', 'yoga', 'meditation', 'fitness', 'nutrition'];
      const category = categories[Math.floor(Math.random() * categories.length)];

      return {
        value: {
          currentActivity: activity,
          category,
          duration: Math.floor(Math.random() * 30) + 5, // 5-35 minutes
          intensity: Math.floor(Math.random() * 5) + 1, // 1-5
        },
        confidence: 0.7,
      };
    } catch (error) {
      this.logger.error(`Error getting user activity context for ${userId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Get local events context
   * @param userId User ID
   * @returns Local events context and confidence
   */
  private async getLocalEventsContext(userId: string): Promise<{ value: any; confidence: number } | null> {
    try {
      // Get user location first
      const locationContext = await this.getUserLocation(userId);
      if (!locationContext) {
        return null;
      }

      const city = locationContext.value.city;

      // Get local events from cache
      const events = this.localEventsCache.get(city);
      if (!events || events.length === 0) {
        return null;
      }

      return {
        value: {
          events,
          count: events.length,
          nearestEvent: events.sort((a, b) => a.startDate.getTime() - b.startDate.getTime())[0],
        },
        confidence: 0.7,
      };
    } catch (error) {
      this.logger.error(`Error getting local events context for ${userId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Get user mood context
   * @param userId User ID
   * @returns User mood context and confidence
   */
  private async getUserMoodContext(userId: string): Promise<{ value: any; confidence: number } | null> {
    try {
      // In a real implementation, this would fetch user mood from the database or mood tracking service
      // For now, we'll return mock data
      const moods = ['relaxed', 'stressed', 'energetic', 'tired', 'focused', 'distracted'];
      const mood = moods[Math.floor(Math.random() * moods.length)];

      return {
        value: {
          mood,
          intensity: Math.floor(Math.random() * 5) + 1, // 1-5
          duration: Math.floor(Math.random() * 120) + 30, // 30-150 minutes
        },
        confidence: 0.6, // Mood detection is less reliable
      };
    } catch (error) {
      this.logger.error(`Error getting user mood context for ${userId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Get user calendar context
   * @param userId User ID
   * @returns User calendar context and confidence
   */
  private async getUserCalendarContext(userId: string): Promise<{ value: any; confidence: number } | null> {
    try {
      // In a real implementation, this would fetch user calendar from the database or calendar service
      // For now, we'll return mock data
      const now = new Date();
      const events = [];

      // Generate 0-3 random events
      const eventCount = Math.floor(Math.random() * 4);

      for (let i = 0; i < eventCount; i++) {
        const startTime = new Date(now.getTime() + (Math.floor(Math.random() * 48) + 1) * 3600000); // Within next 48 hours
        const duration = Math.floor(Math.random() * 120) + 30; // 30-150 minutes

        const eventTypes = ['meeting', 'appointment', 'class', 'personal', 'travel'];
        const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];

        events.push({
          id: uuidv4(),
          title: `${eventType.charAt(0).toUpperCase() + eventType.slice(1)} Event`,
          type: eventType,
          startTime,
          endTime: new Date(startTime.getTime() + duration * 60000),
          location: Math.random() > 0.5 ? 'remote' : 'in-person',
        });
      }

      return {
        value: {
          events,
          count: events.length,
          nextEvent: events.length > 0 ?
            events.sort((a, b) => a.startTime.getTime() - b.startTime.getTime())[0] : null,
          busyHours: Math.floor(Math.random() * 8) + 2, // 2-10 hours
        },
        confidence: 0.8,
      };
    } catch (error) {
      this.logger.error(`Error getting user calendar context for ${userId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Get social context
   * @param userId User ID
   * @returns Social context and confidence
   */
  private async getSocialContext(userId: string): Promise<{ value: any; confidence: number } | null> {
    try {
      // In a real implementation, this would fetch social context from the database or social graph
      // For now, we'll return mock data
      const socialNetworks = ['friends', 'family', 'colleagues', 'community'];
      const primaryNetwork = socialNetworks[Math.floor(Math.random() * socialNetworks.length)];

      return {
        value: {
          primaryNetwork,
          connectionCount: Math.floor(Math.random() * 500) + 50, // 50-550 connections
          activeConnections: Math.floor(Math.random() * 20) + 5, // 5-25 active connections
          groups: Math.floor(Math.random() * 10) + 1, // 1-10 groups
          upcomingSocialEvents: Math.floor(Math.random() * 3), // 0-2 upcoming social events
        },
        confidence: 0.6,
      };
    } catch (error) {
      this.logger.error(`Error getting social context for ${userId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Get health context
   * @param userId User ID
   * @returns Health context and confidence
   */
  private async getHealthContext(userId: string): Promise<{ value: any; confidence: number } | null> {
    try {
      // In a real implementation, this would fetch health data from the database or health tracking service
      // For now, we'll return mock data
      return {
        value: {
          activityLevel: Math.floor(Math.random() * 5) + 1, // 1-5
          stressLevel: Math.floor(Math.random() * 5) + 1, // 1-5
          sleepHours: Math.floor(Math.random() * 4) + 5, // 5-9 hours
          restingHeartRate: Math.floor(Math.random() * 20) + 60, // 60-80 bpm
          steps: Math.floor(Math.random() * 10000) + 2000, // 2000-12000 steps
        },
        confidence: 0.7,
      };
    } catch (error) {
      this.logger.error(`Error getting health context for ${userId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Get country for city
   * @param city City name
   * @returns Country name
   */
  private getCountryForCity(city: string): string {
    const cityToCountry: Record<string, string> = {
      'Paris': 'France',
      'London': 'United Kingdom',
      'New York': 'United States',
      'Tokyo': 'Japan',
      'Sydney': 'Australia',
    };

    return cityToCountry[city] || 'Unknown';
  }

  /**
   * Get coordinates for city
   * @param city City name
   * @returns Coordinates [latitude, longitude]
   */
  private getCoordinatesForCity(city: string): [number, number] {
    const cityToCoordinates: Record<string, [number, number]> = {
      'Paris': [48.8566, 2.3522],
      'London': [51.5074, -0.1278],
      'New York': [40.7128, -74.0060],
      'Tokyo': [35.6762, 139.6503],
      'Sydney': [-33.8688, 151.2093],
    };

    return cityToCoordinates[city] || [0, 0];
  }

  /**
   * Get advanced context configuration
   * @returns Advanced context configuration
   */
  getContextConfig(): AdvancedContextConfig {
    return this.contextConfig;
  }

  /**
   * Update advanced context configuration
   * @param config New advanced context configuration
   * @returns Updated advanced context configuration
   */
  updateContextConfig(config: Partial<AdvancedContextConfig>): AdvancedContextConfig {
    this.contextConfig = {
      ...this.contextConfig,
      ...config,
    };

    this.logger.log('Advanced context configuration updated');
    this.eventEmitter.emit('context.config.updated', this.contextConfig);

    return this.contextConfig;
  }

  /**
   * Get contextual recommendations
   * @param request Contextual recommendation request
   * @returns Contextual recommendation result
   */
  async getContextualRecommendations(
    request: ContextualRecommendationRequest,
  ): Promise<ContextualRecommendationResult> {
    try {
      if (!this.contextConfig.enabled) {
        // If advanced context is disabled, fall back to regular recommendations
        const items = await this.recommendationService.getRecommendations(request.userId, request.limit || 10);

        return {
          items,
          contextData: {
            userId: request.userId,
            timestamp: new Date(),
            values: {},
            confidence: {},
            metadata: { contextDisabled: true },
          },
          contextInfluence: {},
        };
      }

      // Get context data
      const contextData = await this.getContextData(request.userId, request.explicitContext);

      // Apply context adaptation
      const adaptedItems = await this.applyContextAdaptation(request.userId, contextData, request.limit || 10, request.adaptationParams);

      // Calculate context influence
      const contextInfluence = this.calculateContextInfluence(adaptedItems, contextData);

      return {
        items: adaptedItems,
        contextData,
        contextInfluence,
      };
    } catch (error) {
      this.logger.error(`Error getting contextual recommendations for user ${request.userId}: ${error.message}`);

      // Fall back to regular recommendations
      const items = await this.recommendationService.getRecommendations(request.userId, request.limit || 10);

      return {
        items,
        contextData: {
          userId: request.userId,
          timestamp: new Date(),
          values: {},
          confidence: {},
          metadata: { error: error.message },
        },
        contextInfluence: {},
      };
    }
  }

  /**
   * Apply context adaptation
   * @param userId User ID
   * @param contextData Context data
   * @param limit Number of recommendations to return
   * @param adaptationParams Adaptation parameters
   * @returns Adapted recommendations
   */
  private async applyContextAdaptation(
    userId: string,
    contextData: ContextData,
    limit: number,
    adaptationParams?: Record<string, any>,
  ): Promise<any[]> {
    try {
      // Choose adaptation strategy
      const strategy = this.chooseAdaptationStrategy(contextData, adaptationParams);

      switch (strategy) {
        case ContextAdaptationStrategy.PRE_FILTERING:
          return await this.applyPreFilteringAdaptation(userId, contextData, limit, adaptationParams);

        case ContextAdaptationStrategy.POST_FILTERING:
          return await this.applyPostFilteringAdaptation(userId, contextData, limit, adaptationParams);

        case ContextAdaptationStrategy.CONTEXTUAL_MODELING:
          return await this.applyContextualModelingAdaptation(userId, contextData, limit, adaptationParams);

        case ContextAdaptationStrategy.HYBRID:
          return await this.applyHybridAdaptation(userId, contextData, limit, adaptationParams);

        default:
          // Fall back to regular recommendations
          return await this.recommendationService.getRecommendations(userId, limit);
      }
    } catch (error) {
      this.logger.error(`Error applying context adaptation for user ${userId}: ${error.message}`);

      // Fall back to regular recommendations
      return await this.recommendationService.getRecommendations(userId, limit);
    }
  }

  /**
   * Choose adaptation strategy
   * @param contextData Context data
   * @param adaptationParams Adaptation parameters
   * @returns Adaptation strategy
   */
  private chooseAdaptationStrategy(
    contextData: ContextData,
    adaptationParams?: Record<string, any>,
  ): ContextAdaptationStrategy {
    // If strategy is specified in params, use it
    if (adaptationParams?.strategy) {
      return adaptationParams.strategy;
    }

    // Choose based on context data quality
    const confidenceValues = Object.values(contextData.confidence || {});
    if (confidenceValues.length === 0) {
      return ContextAdaptationStrategy.PRE_FILTERING;
    }

    const avgConfidence = confidenceValues.reduce((sum, val) => sum + val, 0) / confidenceValues.length;

    if (avgConfidence > 0.8) {
      return ContextAdaptationStrategy.CONTEXTUAL_MODELING;
    } else if (avgConfidence > 0.5) {
      return ContextAdaptationStrategy.HYBRID;
    } else {
      return ContextAdaptationStrategy.POST_FILTERING;
    }
  }

  /**
   * Apply pre-filtering adaptation
   * @param userId User ID
   * @param contextData Context data
   * @param limit Number of recommendations to return
   * @param adaptationParams Adaptation parameters
   * @returns Adapted recommendations
   */
  private async applyPreFilteringAdaptation(
    userId: string,
    contextData: ContextData,
    limit: number,
    adaptationParams?: Record<string, any>,
  ): Promise<any[]> {
    try {
      // Create context-based filters
      const filters: Record<string, any> = {};

      // Apply location filter if available
      if (contextData.values[ContextSourceType.LOCATION]) {
        const location = contextData.values[ContextSourceType.LOCATION];
        filters.location = location.city;
      }

      // Apply weather filter if available
      if (contextData.values[ContextSourceType.WEATHER]) {
        const weather = contextData.values[ContextSourceType.WEATHER];

        if (weather.condition === 'rainy' || weather.condition === 'snowy') {
          filters.indoor = true;
        }

        if (weather.temperature < 10) {
          filters.warmEnvironment = true;
        } else if (weather.temperature > 25) {
          filters.coolEnvironment = true;
        }
      }

      // Apply time of day filter if available
      if (contextData.values[ContextSourceType.TIME_OF_DAY]) {
        const timeOfDay = contextData.values[ContextSourceType.TIME_OF_DAY];
        filters.timeOfDay = timeOfDay.timeOfDay;
      }

      // Apply day of week filter if available
      if (contextData.values[ContextSourceType.DAY_OF_WEEK]) {
        const dayOfWeek = contextData.values[ContextSourceType.DAY_OF_WEEK];
        filters.dayType = dayOfWeek.dayType;
      }

      // Apply season filter if available
      if (contextData.values[ContextSourceType.SEASON]) {
        const season = contextData.values[ContextSourceType.SEASON];
        filters.season = season.season;
      }

      // Apply device filter if available
      if (contextData.values[ContextSourceType.DEVICE]) {
        const device = contextData.values[ContextSourceType.DEVICE];
        filters.deviceType = device.deviceType;
      }

      // Apply mood filter if available
      if (contextData.values[ContextSourceType.MOOD]) {
        const mood = contextData.values[ContextSourceType.MOOD];

        if (mood.mood === 'stressed') {
          filters.relaxing = true;
        } else if (mood.mood === 'tired') {
          filters.energizing = true;
        } else if (mood.mood === 'distracted') {
          filters.focusing = true;
        }
      }

      // Get recommendations with filters
      return await this.recommendationService.getRecommendations(userId, limit, { filters });
    } catch (error) {
      this.logger.error(`Error applying pre-filtering adaptation for user ${userId}: ${error.message}`);

      // Fall back to regular recommendations
      return await this.recommendationService.getRecommendations(userId, limit);
    }
  }

  /**
   * Apply post-filtering adaptation
   * @param userId User ID
   * @param contextData Context data
   * @param limit Number of recommendations to return
   * @param adaptationParams Adaptation parameters
   * @returns Adapted recommendations
   */
  private async applyPostFilteringAdaptation(
    userId: string,
    contextData: ContextData,
    limit: number,
    adaptationParams?: Record<string, any>,
  ): Promise<any[]> {
    try {
      // Get more recommendations than needed
      const extraLimit = limit * 3;
      const recommendations = await this.recommendationService.getRecommendations(userId, extraLimit);

      if (recommendations.length === 0) {
        return [];
      }

      // Score items based on context
      const scoredItems = recommendations.map(item => {
        const contextScore = this.calculateContextScore(item, contextData);
        return { item, contextScore };
      });

      // Sort by context score (descending)
      scoredItems.sort((a, b) => b.contextScore - a.contextScore);

      // Return top items
      return scoredItems.slice(0, limit).map(scored => scored.item);
    } catch (error) {
      this.logger.error(`Error applying post-filtering adaptation for user ${userId}: ${error.message}`);

      // Fall back to regular recommendations
      return await this.recommendationService.getRecommendations(userId, limit);
    }
  }

  /**
   * Apply contextual modeling adaptation
   * @param userId User ID
   * @param contextData Context data
   * @param limit Number of recommendations to return
   * @param adaptationParams Adaptation parameters
   * @returns Adapted recommendations
   */
  private async applyContextualModelingAdaptation(
    userId: string,
    contextData: ContextData,
    limit: number,
    adaptationParams?: Record<string, any>,
  ): Promise<any[]> {
    try {
      // In a real implementation, this would use a context-aware model
      // For now, we'll use a combination of pre-filtering and post-filtering

      // Create context-based filters (less strict than pre-filtering)
      const filters: Record<string, any> = {};

      // Apply location filter if available with high confidence
      if (contextData.values[ContextSourceType.LOCATION] &&
          contextData.confidence[ContextSourceType.LOCATION] > 0.8) {
        const location = contextData.values[ContextSourceType.LOCATION];
        filters.location = location.city;
      }

      // Apply season filter if available
      if (contextData.values[ContextSourceType.SEASON]) {
        const season = contextData.values[ContextSourceType.SEASON];
        filters.season = season.season;
      }

      // Get recommendations with filters
      const recommendations = await this.recommendationService.getRecommendations(
        userId,
        limit * 2,
        { filters },
      );

      // Score items based on context
      const scoredItems = recommendations.map(item => {
        const contextScore = this.calculateContextScore(item, contextData);
        return { item, contextScore };
      });

      // Sort by context score (descending)
      scoredItems.sort((a, b) => b.contextScore - a.contextScore);

      // Return top items
      return scoredItems.slice(0, limit).map(scored => scored.item);
    } catch (error) {
      this.logger.error(`Error applying contextual modeling adaptation for user ${userId}: ${error.message}`);

      // Fall back to regular recommendations
      return await this.recommendationService.getRecommendations(userId, limit);
    }
  }

  /**
   * Apply hybrid adaptation
   * @param userId User ID
   * @param contextData Context data
   * @param limit Number of recommendations to return
   * @param adaptationParams Adaptation parameters
   * @returns Adapted recommendations
   */
  private async applyHybridAdaptation(
    userId: string,
    contextData: ContextData,
    limit: number,
    adaptationParams?: Record<string, any>,
  ): Promise<any[]> {
    try {
      // Get recommendations from different strategies
      const preFilteringItems = await this.applyPreFilteringAdaptation(
        userId,
        contextData,
        Math.ceil(limit / 2),
        adaptationParams,
      );

      const contextualModelingItems = await this.applyContextualModelingAdaptation(
        userId,
        contextData,
        Math.ceil(limit / 2),
        adaptationParams,
      );

      // Combine and deduplicate
      const combinedItems = [...preFilteringItems];

      for (const item of contextualModelingItems) {
        if (!combinedItems.some(existing => existing.id === item.id)) {
          combinedItems.push(item);
        }
      }

      // If we don't have enough items, get more
      if (combinedItems.length < limit) {
        const moreItems = await this.recommendationService.getRecommendations(
          userId,
          limit - combinedItems.length,
        );

        for (const item of moreItems) {
          if (!combinedItems.some(existing => existing.id === item.id)) {
            combinedItems.push(item);
          }
        }
      }

      // Limit to requested number
      return combinedItems.slice(0, limit);
    } catch (error) {
      this.logger.error(`Error applying hybrid adaptation for user ${userId}: ${error.message}`);

      // Fall back to regular recommendations
      return await this.recommendationService.getRecommendations(userId, limit);
    }
  }

  /**
   * Calculate context score for an item
   * @param item Item to score
   * @param contextData Context data
   * @returns Context score (0-1)
   */
  private calculateContextScore(item: any, contextData: ContextData): number {
    let totalScore = 0;
    let totalWeight = 0;

    // Score based on location
    if (contextData.values[ContextSourceType.LOCATION]) {
      const locationScore = this.calculateLocationScore(item, contextData.values[ContextSourceType.LOCATION]);
      const weight = this.getContextWeight(ContextSourceType.LOCATION, contextData);

      totalScore += locationScore * weight;
      totalWeight += weight;
    }

    // Score based on weather
    if (contextData.values[ContextSourceType.WEATHER]) {
      const weatherScore = this.calculateWeatherScore(item, contextData.values[ContextSourceType.WEATHER]);
      const weight = this.getContextWeight(ContextSourceType.WEATHER, contextData);

      totalScore += weatherScore * weight;
      totalWeight += weight;
    }

    // Score based on time of day
    if (contextData.values[ContextSourceType.TIME_OF_DAY]) {
      const timeScore = this.calculateTimeScore(item, contextData.values[ContextSourceType.TIME_OF_DAY]);
      const weight = this.getContextWeight(ContextSourceType.TIME_OF_DAY, contextData);

      totalScore += timeScore * weight;
      totalWeight += weight;
    }

    // Score based on day of week
    if (contextData.values[ContextSourceType.DAY_OF_WEEK]) {
      const dayScore = this.calculateDayScore(item, contextData.values[ContextSourceType.DAY_OF_WEEK]);
      const weight = this.getContextWeight(ContextSourceType.DAY_OF_WEEK, contextData);

      totalScore += dayScore * weight;
      totalWeight += weight;
    }

    // Score based on season
    if (contextData.values[ContextSourceType.SEASON]) {
      const seasonScore = this.calculateSeasonScore(item, contextData.values[ContextSourceType.SEASON]);
      const weight = this.getContextWeight(ContextSourceType.SEASON, contextData);

      totalScore += seasonScore * weight;
      totalWeight += weight;
    }

    // Score based on device
    if (contextData.values[ContextSourceType.DEVICE]) {
      const deviceScore = this.calculateDeviceScore(item, contextData.values[ContextSourceType.DEVICE]);
      const weight = this.getContextWeight(ContextSourceType.DEVICE, contextData);

      totalScore += deviceScore * weight;
      totalWeight += weight;
    }

    // Score based on mood
    if (contextData.values[ContextSourceType.MOOD]) {
      const moodScore = this.calculateMoodScore(item, contextData.values[ContextSourceType.MOOD]);
      const weight = this.getContextWeight(ContextSourceType.MOOD, contextData);

      totalScore += moodScore * weight;
      totalWeight += weight;
    }

    // Return normalized score
    return totalWeight > 0 ? totalScore / totalWeight : 0;
  }

  /**
   * Get context weight
   * @param contextType Context type
   * @param contextData Context data
   * @returns Context weight
   */
  private getContextWeight(contextType: ContextSourceType, contextData: ContextData): number {
    // Get base weight from configuration
    const baseWeight = this.contextConfig.contextWeighting.defaultWeights[contextType] || 0.5;

    // Adjust weight based on confidence
    const confidence = contextData.confidence[contextType] || 0.5;

    // Apply weighting strategy
    switch (this.contextConfig.contextWeighting.strategy) {
      case ContextWeightingStrategy.STATIC:
        return baseWeight;

      case ContextWeightingStrategy.DYNAMIC:
        // In a real implementation, this would use user behavior data
        return baseWeight;

      case ContextWeightingStrategy.ADAPTIVE:
        // Adjust weight based on confidence
        return baseWeight * confidence;

      default:
        return baseWeight;
    }
  }

  /**
   * Calculate location score
   * @param item Item to score
   * @param location Location context
   * @returns Location score (0-1)
   */
  private calculateLocationScore(item: any, location: any): number {
    // In a real implementation, this would use more sophisticated location matching
    // For now, we'll use a simple match

    if (!item.location) {
      return 0.5; // Neutral score for items without location
    }

    // Exact city match
    if (item.location === location.city) {
      return 1.0;
    }

    // Country match
    if (item.country === location.country) {
      return 0.7;
    }

    // No match
    return 0.3;
  }

  /**
   * Calculate weather score
   * @param item Item to score
   * @param weather Weather context
   * @returns Weather score (0-1)
   */
  private calculateWeatherScore(item: any, weather: any): number {
    // In a real implementation, this would use more sophisticated weather matching
    // For now, we'll use simple rules

    if (!item.attributes) {
      return 0.5; // Neutral score for items without attributes
    }

    let score = 0.5;

    // Indoor/outdoor match
    if (weather.condition === 'rainy' || weather.condition === 'snowy') {
      if (item.attributes.indoor === true) {
        score += 0.3;
      } else if (item.attributes.outdoor === true) {
        score -= 0.2;
      }
    } else if (weather.condition === 'sunny') {
      if (item.attributes.outdoor === true) {
        score += 0.2;
      }
    }

    // Temperature match
    if (weather.temperature < 10) {
      if (item.attributes.warmEnvironment === true) {
        score += 0.2;
      }
    } else if (weather.temperature > 25) {
      if (item.attributes.coolEnvironment === true) {
        score += 0.2;
      }
    }

    // Clamp score to [0, 1]
    return Math.max(0, Math.min(1, score));
  }

  /**
   * Calculate time score
   * @param item Item to score
   * @param timeOfDay Time of day context
   * @returns Time score (0-1)
   */
  private calculateTimeScore(item: any, timeOfDay: any): number {
    // In a real implementation, this would use more sophisticated time matching
    // For now, we'll use simple rules

    if (!item.attributes) {
      return 0.5; // Neutral score for items without attributes
    }

    // Check if item has preferred time of day
    if (item.attributes.timeOfDay === timeOfDay.timeOfDay) {
      return 0.9;
    }

    // Check for specific time matches
    switch (timeOfDay.timeOfDay) {
      case 'morning':
        if (item.attributes.morning === true) {
          return 0.8;
        }
        break;

      case 'afternoon':
        if (item.attributes.afternoon === true) {
          return 0.8;
        }
        break;

      case 'evening':
        if (item.attributes.evening === true) {
          return 0.8;
        }
        break;

      case 'night':
        if (item.attributes.night === true) {
          return 0.8;
        }
        break;
    }

    // Default score
    return 0.5;
  }

  /**
   * Calculate day score
   * @param item Item to score
   * @param dayOfWeek Day of week context
   * @returns Day score (0-1)
   */
  private calculateDayScore(item: any, dayOfWeek: any): number {
    // In a real implementation, this would use more sophisticated day matching
    // For now, we'll use simple rules

    if (!item.attributes) {
      return 0.5; // Neutral score for items without attributes
    }

    // Check if item has preferred day type
    if (item.attributes.dayType === dayOfWeek.dayType) {
      return 0.9;
    }

    // Check for specific day matches
    if (dayOfWeek.dayType === 'weekend' && item.attributes.weekend === true) {
      return 0.8;
    }

    if (dayOfWeek.dayType === 'weekday' && item.attributes.weekday === true) {
      return 0.8;
    }

    // Default score
    return 0.5;
  }

  /**
   * Calculate season score
   * @param item Item to score
   * @param season Season context
   * @returns Season score (0-1)
   */
  private calculateSeasonScore(item: any, season: any): number {
    // In a real implementation, this would use more sophisticated season matching
    // For now, we'll use simple rules

    if (!item.attributes) {
      return 0.5; // Neutral score for items without attributes
    }

    // Check if item has preferred season
    if (item.attributes.season === season.season) {
      return 0.9;
    }

    // Check for specific season matches
    if (item.attributes[season.season] === true) {
      return 0.8;
    }

    // Default score
    return 0.5;
  }

  /**
   * Calculate device score
   * @param item Item to score
   * @param device Device context
   * @returns Device score (0-1)
   */
  private calculateDeviceScore(item: any, device: any): number {
    // In a real implementation, this would use more sophisticated device matching
    // For now, we'll use simple rules

    if (!item.attributes) {
      return 0.5; // Neutral score for items without attributes
    }

    // Check if item has preferred device type
    if (item.attributes.preferredDevice === device.deviceType) {
      return 0.9;
    }

    // Check for specific device matches
    if (device.deviceType === 'mobile' && item.attributes.mobileOptimized === true) {
      return 0.8;
    }

    if (device.deviceType === 'desktop' && item.attributes.desktopOptimized === true) {
      return 0.8;
    }

    // Default score
    return 0.5;
  }

  /**
   * Calculate mood score
   * @param item Item to score
   * @param mood Mood context
   * @returns Mood score (0-1)
   */
  private calculateMoodScore(item: any, mood: any): number {
    // In a real implementation, this would use more sophisticated mood matching
    // For now, we'll use simple rules

    if (!item.attributes) {
      return 0.5; // Neutral score for items without attributes
    }

    let score = 0.5;

    // Check for specific mood matches
    switch (mood.mood) {
      case 'relaxed':
        if (item.attributes.relaxing === true) {
          score += 0.3;
        }
        break;

      case 'stressed':
        if (item.attributes.relaxing === true) {
          score += 0.4;
        }
        if (item.attributes.energizing === true) {
          score -= 0.2;
        }
        break;

      case 'energetic':
        if (item.attributes.energizing === true) {
          score += 0.3;
        }
        break;

      case 'tired':
        if (item.attributes.relaxing === true) {
          score += 0.2;
        }
        if (item.attributes.energizing === true) {
          score += 0.2;
        }
        break;

      case 'focused':
        if (item.attributes.focusing === true) {
          score += 0.3;
        }
        break;

      case 'distracted':
        if (item.attributes.focusing === true) {
          score += 0.4;
        }
        break;
    }

    // Clamp score to [0, 1]
    return Math.max(0, Math.min(1, score));
  }

  /**
   * Calculate context influence
   * @param items Recommended items
   * @param contextData Context data
   * @returns Context influence
   */
  private calculateContextInfluence(items: any[], contextData: ContextData): Record<ContextSourceType, number> {
    const result: Record<ContextSourceType, number> = {} as Record<ContextSourceType, number>;

    // Initialize with zeros
    for (const type of Object.values(ContextSourceType)) {
      result[type] = 0;
    }

    // If no items, return zeros
    if (items.length === 0) {
      return result;
    }

    // Calculate influence for each context type
    for (const type of Object.values(ContextSourceType)) {
      if (contextData.values[type]) {
        // Calculate average score for this context type
        let totalScore = 0;

        for (const item of items) {
          let score = 0;

          switch (type) {
            case ContextSourceType.LOCATION:
              score = this.calculateLocationScore(item, contextData.values[type]);
              break;

            case ContextSourceType.WEATHER:
              score = this.calculateWeatherScore(item, contextData.values[type]);
              break;

            case ContextSourceType.TIME_OF_DAY:
              score = this.calculateTimeScore(item, contextData.values[type]);
              break;

            case ContextSourceType.DAY_OF_WEEK:
              score = this.calculateDayScore(item, contextData.values[type]);
              break;

            case ContextSourceType.SEASON:
              score = this.calculateSeasonScore(item, contextData.values[type]);
              break;

            case ContextSourceType.DEVICE:
              score = this.calculateDeviceScore(item, contextData.values[type]);
              break;

            case ContextSourceType.MOOD:
              score = this.calculateMoodScore(item, contextData.values[type]);
              break;

            default:
              score = 0.5;
          }

          totalScore += score;
        }

        // Calculate average and normalize to [0, 1]
        const avgScore = totalScore / items.length;

        // Normalize to influence (0.5 is neutral)
        result[type] = Math.max(0, (avgScore - 0.5) * 2);
      }
    }

    return result;
  }

  /**
   * Scheduled context refresh
   */
  @Cron('0 */15 * * * *') // Every 15 minutes
  async scheduledContextRefresh(): Promise<void> {
    try {
      this.logger.log('Running scheduled context refresh');

      // Refresh weather cache
      await this.refreshWeatherCache();

      // Refresh local events cache
      await this.refreshLocalEventsCache();

      this.logger.log('Context refresh completed');
    } catch (error) {
      this.logger.error(`Error in scheduled context refresh: ${error.message}`);
    }
  }
}
