import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { RecommendationService } from './recommendation.service';

/**
 * Interface pour les options de recommandation sociale
 */
export interface SocialRecommendationOptions {
  /** ID de l'utilisateur */
  userId: string;
  
  /** Facteur d'influence sociale (0-1) */
  socialFactor?: number;
  
  /** Nombre maximum de recommandations */
  maxRecommendations?: number;
  
  /** Types de relations à considérer */
  relationTypes?: Array<'friend' | 'follower' | 'following' | 'colleague' | 'family'>;
  
  /** Profondeur du réseau social (1-3) */
  networkDepth?: number;
  
  /** Filtres supplémentaires */
  filters?: Record<string, any>;
}

/**
 * Interface pour les métriques sociales
 */
export interface SocialMetrics {
  /** Nombre d'amis */
  friendCount: number;
  
  /** Nombre d'abonnés */
  followerCount: number;
  
  /** Nombre d'abonnements */
  followingCount: number;
  
  /** Nombre de collègues */
  colleagueCount: number;
  
  /** Nombre de membres de la famille */
  familyCount: number;
  
  /** Nombre total de connexions */
  totalConnections: number;
  
  /** Score d'influence sociale */
  influenceScore: number;
  
  /** Score de similarité sociale */
  similarityScore: number;
}

/**
 * Service pour les recommandations sociales
 */
@Injectable()
export class SocialRecommendationService {
  private readonly logger = new Logger(SocialRecommendationService.name);
  
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly recommendationService: RecommendationService,
  ) {}
  
  /**
   * Génère des recommandations sociales pour un utilisateur
   * @param options Options de recommandation sociale
   * @returns Recommandations sociales
   */
  async getSocialRecommendations(options: SocialRecommendationOptions): Promise<any[]> {
    try {
      const {
        userId,
        socialFactor = 0.7,
        maxRecommendations = 10,
        relationTypes = ['friend', 'follower', 'following', 'colleague', 'family'],
        networkDepth = 2,
        filters,
      } = options;
      
      // Récupérer le réseau social de l'utilisateur
      const socialNetwork = await this.getSocialNetwork(userId, relationTypes, networkDepth);
      
      if (Object.keys(socialNetwork).length === 0) {
        this.logger.log(`Aucune connexion sociale trouvée pour l'utilisateur ${userId}`);
        
        // Retourner les recommandations standard
        const standardRecommendations = await this.recommendationService.getRecommendationsForUser(userId);
        return standardRecommendations.slice(0, maxRecommendations);
      }
      
      // Récupérer les recommandations pour chaque connexion sociale
      const socialRecommendations = await this.getSocialRecommendations(userId, socialNetwork);
      
      // Récupérer les recommandations standard pour l'utilisateur
      const standardRecommendations = await this.recommendationService.getRecommendationsForUser(userId);
      
      // Fusionner les recommandations sociales et standard
      const mergedRecommendations = this.mergeSocialAndStandardRecommendations(
        socialRecommendations,
        standardRecommendations,
        socialFactor,
      );
      
      // Appliquer les filtres supplémentaires
      const filteredRecommendations = this.applyFilters(mergedRecommendations, filters);
      
      // Trier par score social
      const sortedRecommendations = filteredRecommendations.sort(
        (a, b) => b.socialScore - a.socialScore,
      );
      
      // Limiter le nombre de recommandations
      const limitedRecommendations = sortedRecommendations.slice(0, maxRecommendations);
      
      // Calculer les métriques sociales
      const socialMetrics = await this.calculateSocialMetrics(userId, socialNetwork);
      
      // Émettre un événement de recommandation sociale
      this.eventEmitter.emit('recommendation.social', {
        userId,
        socialFactor,
        recommendationCount: limitedRecommendations.length,
        socialMetrics,
      });
      
      return limitedRecommendations;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des recommandations sociales: ${error.message}`);
      
      // En cas d'erreur, retourner les recommandations standard
      const standardRecommendations = await this.recommendationService.getRecommendationsForUser(options.userId);
      return standardRecommendations.slice(0, options.maxRecommendations || 10);
    }
  }
  
  /**
   * Récupère le réseau social d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param relationTypes Types de relations à considérer
   * @param depth Profondeur du réseau social
   * @returns Réseau social
   */
  private async getSocialNetwork(
    userId: string,
    relationTypes: Array<'friend' | 'follower' | 'following' | 'colleague' | 'family'>,
    depth: number,
  ): Promise<Record<string, { userId: string; relationStrength: number; relationType: string }>> {
    const socialNetwork: Record<string, { userId: string; relationStrength: number; relationType: string }> = {};
    
    try {
      // Récupérer les connexions sociales directes
      const directConnections = await this.prisma.userConnection.findMany({
        where: {
          OR: [
            { userId, type: { in: relationTypes } },
            { connectedUserId: userId, type: { in: relationTypes } },
          ],
        },
        select: {
          userId: true,
          connectedUserId: true,
          type: true,
          strength: true,
        },
      });
      
      // Ajouter les connexions directes au réseau social
      directConnections.forEach(connection => {
        const connectedUserId = connection.userId === userId ? connection.connectedUserId : connection.userId;
        
        socialNetwork[connectedUserId] = {
          userId: connectedUserId,
          relationStrength: connection.strength || 1,
          relationType: connection.type,
        };
      });
      
      // Si la profondeur est supérieure à 1, récupérer les connexions indirectes
      if (depth > 1) {
        const directConnectionIds = Object.keys(socialNetwork);
        
        // Récupérer les connexions de niveau 2
        for (const connectedUserId of directConnectionIds) {
          const indirectConnections = await this.prisma.userConnection.findMany({
            where: {
              OR: [
                { userId: connectedUserId, type: { in: relationTypes } },
                { connectedUserId, type: { in: relationTypes } },
              ],
              NOT: {
                OR: [
                  { userId },
                  { connectedUserId: userId },
                ],
              },
            },
            select: {
              userId: true,
              connectedUserId: true,
              type: true,
              strength: true,
            },
          });
          
          // Ajouter les connexions indirectes au réseau social
          indirectConnections.forEach(connection => {
            const indirectUserId = connection.userId === connectedUserId ? connection.connectedUserId : connection.userId;
            
            // Éviter d'ajouter l'utilisateur lui-même ou des connexions déjà présentes
            if (indirectUserId !== userId && !socialNetwork[indirectUserId]) {
              socialNetwork[indirectUserId] = {
                userId: indirectUserId,
                // Réduire la force de la relation pour les connexions indirectes
                relationStrength: (connection.strength || 1) * 0.5,
                relationType: connection.type,
              };
            }
          });
        }
      }
      
      // Si la profondeur est supérieure à 2, récupérer les connexions de niveau 3
      if (depth > 2) {
        const level2ConnectionIds = Object.keys(socialNetwork).filter(
          id => !directConnections.some(
            conn => conn.userId === id || conn.connectedUserId === id
          )
        );
        
        // Récupérer les connexions de niveau 3
        for (const connectedUserId of level2ConnectionIds) {
          const indirectConnections = await this.prisma.userConnection.findMany({
            where: {
              OR: [
                { userId: connectedUserId, type: { in: relationTypes } },
                { connectedUserId, type: { in: relationTypes } },
              ],
              NOT: {
                OR: [
                  { userId },
                  { connectedUserId: userId },
                  { userId: { in: Object.keys(socialNetwork) } },
                  { connectedUserId: { in: Object.keys(socialNetwork) } },
                ],
              },
            },
            select: {
              userId: true,
              connectedUserId: true,
              type: true,
              strength: true,
            },
          });
          
          // Ajouter les connexions indirectes au réseau social
          indirectConnections.forEach(connection => {
            const indirectUserId = connection.userId === connectedUserId ? connection.connectedUserId : connection.userId;
            
            // Éviter d'ajouter l'utilisateur lui-même ou des connexions déjà présentes
            if (indirectUserId !== userId && !socialNetwork[indirectUserId]) {
              socialNetwork[indirectUserId] = {
                userId: indirectUserId,
                // Réduire encore plus la force de la relation pour les connexions de niveau 3
                relationStrength: (connection.strength || 1) * 0.25,
                relationType: connection.type,
              };
            }
          });
        }
      }
      
      return socialNetwork;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération du réseau social: ${error.message}`);
      return {};
    }
  }
  
  /**
   * Récupère les recommandations sociales
   * @param userId ID de l'utilisateur
   * @param socialNetwork Réseau social
   * @returns Recommandations sociales
   */
  private async getSocialRecommendations(
    userId: string,
    socialNetwork: Record<string, { userId: string; relationStrength: number; relationType: string }>,
  ): Promise<any[]> {
    const socialRecommendations: any[] = [];
    
    try {
      // Récupérer les recommandations pour chaque connexion sociale
      for (const connection of Object.values(socialNetwork)) {
        try {
          const recommendations = await this.recommendationService.getRecommendationsForUser(connection.userId);
          
          // Ajouter les recommandations sociales
          recommendations.forEach(recommendation => {
            const existingIndex = socialRecommendations.findIndex(r => r.id === recommendation.id);
            
            if (existingIndex === -1) {
              // Ajouter une nouvelle recommandation
              socialRecommendations.push({
                ...recommendation,
                socialScore: recommendation.score * connection.relationStrength,
                socialSources: [{
                  userId: connection.userId,
                  relationStrength: connection.relationStrength,
                  relationType: connection.relationType,
                }],
              });
            } else {
              // Mettre à jour une recommandation existante
              const existingRecommendation = socialRecommendations[existingIndex];
              const newSocialScore = Math.max(
                existingRecommendation.socialScore,
                recommendation.score * connection.relationStrength,
              );
              
              socialRecommendations[existingIndex] = {
                ...existingRecommendation,
                socialScore: newSocialScore,
                socialSources: [
                  ...existingRecommendation.socialSources,
                  {
                    userId: connection.userId,
                    relationStrength: connection.relationStrength,
                    relationType: connection.relationType,
                  },
                ],
              };
            }
          });
        } catch (error) {
          this.logger.error(`Erreur lors de la récupération des recommandations pour l'utilisateur ${connection.userId}: ${error.message}`);
        }
      }
      
      return socialRecommendations;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des recommandations sociales: ${error.message}`);
      return [];
    }
  }
  
  /**
   * Fusionne les recommandations sociales et standard
   * @param socialRecommendations Recommandations sociales
   * @param standardRecommendations Recommandations standard
   * @param socialFactor Facteur d'influence sociale
   * @returns Recommandations fusionnées
   */
  private mergeSocialAndStandardRecommendations(
    socialRecommendations: any[],
    standardRecommendations: any[],
    socialFactor: number,
  ): any[] {
    const mergedRecommendations: any[] = [];
    
    // Créer un ensemble des IDs des recommandations sociales
    const socialRecommendationIds = new Set(socialRecommendations.map(r => r.id));
    
    // Ajouter les recommandations sociales
    socialRecommendations.forEach(recommendation => {
      mergedRecommendations.push({
        ...recommendation,
        finalScore: recommendation.socialScore * socialFactor + (recommendation.score || 0.5) * (1 - socialFactor),
        isSocial: true,
      });
    });
    
    // Ajouter les recommandations standard qui ne sont pas déjà dans les recommandations sociales
    standardRecommendations.forEach(recommendation => {
      if (!socialRecommendationIds.has(recommendation.id)) {
        mergedRecommendations.push({
          ...recommendation,
          socialScore: 0,
          finalScore: recommendation.score || 0.5,
          isSocial: false,
        });
      }
    });
    
    return mergedRecommendations;
  }
  
  /**
   * Applique des filtres aux recommandations
   * @param recommendations Recommandations
   * @param filters Filtres
   * @returns Recommandations filtrées
   */
  private applyFilters(recommendations: any[], filters?: Record<string, any>): any[] {
    if (!filters || Object.keys(filters).length === 0) {
      return recommendations;
    }
    
    return recommendations.filter(recommendation => {
      // Vérifier chaque filtre
      for (const [key, value] of Object.entries(filters)) {
        // Vérifier si la recommandation a la propriété
        if (!(key in recommendation)) {
          continue;
        }
        
        // Vérifier si la valeur correspond
        if (Array.isArray(value)) {
          // Si le filtre est un tableau, vérifier si la valeur est dans le tableau
          if (!value.includes(recommendation[key])) {
            return false;
          }
        } else if (typeof value === 'object' && value !== null) {
          // Si le filtre est un objet, vérifier les opérateurs
          for (const [op, opValue] of Object.entries(value)) {
            switch (op) {
              case 'eq':
                if (recommendation[key] !== opValue) return false;
                break;
              case 'neq':
                if (recommendation[key] === opValue) return false;
                break;
              case 'gt':
                if (recommendation[key] <= opValue) return false;
                break;
              case 'gte':
                if (recommendation[key] < opValue) return false;
                break;
              case 'lt':
                if (recommendation[key] >= opValue) return false;
                break;
              case 'lte':
                if (recommendation[key] > opValue) return false;
                break;
              case 'in':
                if (!Array.isArray(opValue) || !opValue.includes(recommendation[key])) return false;
                break;
              case 'nin':
                if (!Array.isArray(opValue) || opValue.includes(recommendation[key])) return false;
                break;
              case 'contains':
                if (typeof recommendation[key] !== 'string' || !recommendation[key].includes(opValue)) return false;
                break;
            }
          }
        } else {
          // Sinon, vérifier l'égalité
          if (recommendation[key] !== value) {
            return false;
          }
        }
      }
      
      return true;
    });
  }
  
  /**
   * Calcule les métriques sociales d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param socialNetwork Réseau social
   * @returns Métriques sociales
   */
  private async calculateSocialMetrics(
    userId: string,
    socialNetwork: Record<string, { userId: string; relationStrength: number; relationType: string }>,
  ): Promise<SocialMetrics> {
    try {
      // Compter les connexions par type
      const connectionCounts = {
        friend: 0,
        follower: 0,
        following: 0,
        colleague: 0,
        family: 0,
      };
      
      Object.values(socialNetwork).forEach(connection => {
        switch (connection.relationType) {
          case 'friend':
            connectionCounts.friend++;
            break;
          case 'follower':
            connectionCounts.follower++;
            break;
          case 'following':
            connectionCounts.following++;
            break;
          case 'colleague':
            connectionCounts.colleague++;
            break;
          case 'family':
            connectionCounts.family++;
            break;
        }
      });
      
      // Calculer le score d'influence sociale
      const totalConnections = Object.values(connectionCounts).reduce((sum, count) => sum + count, 0);
      const influenceScore = Math.min(1, totalConnections / 100); // Normaliser à 1 pour 100 connexions
      
      // Calculer le score de similarité sociale
      const similarityScore = await this.calculateSocialSimilarityScore(userId, socialNetwork);
      
      return {
        friendCount: connectionCounts.friend,
        followerCount: connectionCounts.follower,
        followingCount: connectionCounts.following,
        colleagueCount: connectionCounts.colleague,
        familyCount: connectionCounts.family,
        totalConnections,
        influenceScore,
        similarityScore,
      };
    } catch (error) {
      this.logger.error(`Erreur lors du calcul des métriques sociales: ${error.message}`);
      
      return {
        friendCount: 0,
        followerCount: 0,
        followingCount: 0,
        colleagueCount: 0,
        familyCount: 0,
        totalConnections: 0,
        influenceScore: 0,
        similarityScore: 0,
      };
    }
  }
  
  /**
   * Calcule le score de similarité sociale d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param socialNetwork Réseau social
   * @returns Score de similarité sociale
   */
  private async calculateSocialSimilarityScore(
    userId: string,
    socialNetwork: Record<string, { userId: string; relationStrength: number; relationType: string }>,
  ): Promise<number> {
    try {
      // Récupérer les préférences de l'utilisateur
      const userPreferences = await this.prisma.userPreference.findMany({
        where: { userId },
        select: { category: true, value: true },
      });
      
      if (userPreferences.length === 0) {
        return 0;
      }
      
      // Récupérer les préférences des connexions sociales
      const socialUserIds = Object.keys(socialNetwork);
      const socialPreferences = await this.prisma.userPreference.findMany({
        where: { userId: { in: socialUserIds } },
        select: { userId: true, category: true, value: true },
      });
      
      // Calculer la similarité des préférences
      let totalSimilarity = 0;
      let totalComparisons = 0;
      
      for (const userPref of userPreferences) {
        const matchingPrefs = socialPreferences.filter(
          pref => pref.category === userPref.category
        );
        
        for (const matchingPref of matchingPrefs) {
          const similarity = this.calculatePreferenceSimilarity(
            userPref.value,
            matchingPref.value,
          );
          
          const relationStrength = socialNetwork[matchingPref.userId]?.relationStrength || 1;
          
          totalSimilarity += similarity * relationStrength;
          totalComparisons++;
        }
      }
      
      // Normaliser le score de similarité
      return totalComparisons > 0 ? totalSimilarity / totalComparisons : 0;
    } catch (error) {
      this.logger.error(`Erreur lors du calcul du score de similarité sociale: ${error.message}`);
      return 0;
    }
  }
  
  /**
   * Calcule la similarité entre deux valeurs de préférence
   * @param value1 Valeur 1
   * @param value2 Valeur 2
   * @returns Score de similarité
   */
  private calculatePreferenceSimilarity(value1: any, value2: any): number {
    // Si les valeurs sont des chaînes de caractères
    if (typeof value1 === 'string' && typeof value2 === 'string') {
      return value1 === value2 ? 1 : 0;
    }
    
    // Si les valeurs sont des nombres
    if (typeof value1 === 'number' && typeof value2 === 'number') {
      const diff = Math.abs(value1 - value2);
      const max = Math.max(Math.abs(value1), Math.abs(value2));
      return max > 0 ? 1 - (diff / max) : 1;
    }
    
    // Si les valeurs sont des booléens
    if (typeof value1 === 'boolean' && typeof value2 === 'boolean') {
      return value1 === value2 ? 1 : 0;
    }
    
    // Si les valeurs sont des tableaux
    if (Array.isArray(value1) && Array.isArray(value2)) {
      const intersection = value1.filter(v => value2.includes(v));
      const union = [...new Set([...value1, ...value2])];
      return union.length > 0 ? intersection.length / union.length : 0;
    }
    
    // Si les valeurs sont des objets
    if (typeof value1 === 'object' && typeof value2 === 'object' && value1 !== null && value2 !== null) {
      const keys1 = Object.keys(value1);
      const keys2 = Object.keys(value2);
      const intersection = keys1.filter(k => keys2.includes(k));
      const union = [...new Set([...keys1, ...keys2])];
      return union.length > 0 ? intersection.length / union.length : 0;
    }
    
    // Si les types sont différents
    return 0;
  }
}
