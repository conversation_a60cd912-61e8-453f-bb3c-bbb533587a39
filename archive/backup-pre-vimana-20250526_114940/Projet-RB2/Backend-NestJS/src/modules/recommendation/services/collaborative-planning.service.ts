import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { GroupRecommendationService, GroupAggregationStrategy } from './group-recommendation.service';

/**
 * Statut d'un plan collaboratif
 */
export enum CollaborativePlanStatus {
  /** En cours de création */
  DRAFT = 'draft',
  
  /** En attente de votes */
  VOTING = 'voting',
  
  /** Finalisé */
  FINALIZED = 'finalized',
  
  /** Annulé */
  CANCELLED = 'cancelled',
}

/**
 * Type de vote
 */
export enum VoteType {
  /** Pour */
  UPVOTE = 'upvote',
  
  /** Contre */
  DOWNVOTE = 'downvote',
  
  /** Neutre */
  NEUTRAL = 'neutral',
}

/**
 * Interface pour les options de création de plan collaboratif
 */
export interface CreateCollaborativePlanOptions {
  /** Titre du plan */
  title: string;
  
  /** Description du plan */
  description?: string;
  
  /** ID de l'utilisateur créateur */
  creatorId: string;
  
  /** IDs des participants */
  participantIds: string[];
  
  /** Date de début */
  startDate?: Date;
  
  /** Date de fin */
  endDate?: Date;
  
  /** Stratégie d'agrégation */
  aggregationStrategy?: GroupAggregationStrategy;
  
  /** Poids des utilisateurs */
  userWeights?: Record<string, number>;
  
  /** Nombre maximum de recommandations */
  maxRecommendations?: number;
  
  /** Filtres supplémentaires */
  filters?: Record<string, any>;
}

/**
 * Interface pour les options de vote
 */
export interface VoteOptions {
  /** ID du plan collaboratif */
  planId: string;
  
  /** ID de l'utilisateur */
  userId: string;
  
  /** ID de la recommandation */
  recommendationId: string;
  
  /** Type de vote */
  voteType: VoteType;
  
  /** Commentaire */
  comment?: string;
}

/**
 * Interface pour les options de finalisation de plan
 */
export interface FinalizePlanOptions {
  /** ID du plan collaboratif */
  planId: string;
  
  /** ID de l'utilisateur */
  userId: string;
  
  /** IDs des recommandations sélectionnées */
  selectedRecommendationIds: string[];
  
  /** Commentaire */
  comment?: string;
}

/**
 * Service pour la planification collaborative
 */
@Injectable()
export class CollaborativePlanningService {
  private readonly logger = new Logger(CollaborativePlanningService.name);
  
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly groupRecommendationService: GroupRecommendationService,
  ) {}
  
  /**
   * Crée un plan collaboratif
   * @param options Options de création
   * @returns Plan collaboratif
   */
  async createCollaborativePlan(options: CreateCollaborativePlanOptions): Promise<any> {
    try {
      const {
        title,
        description,
        creatorId,
        participantIds,
        startDate,
        endDate,
        aggregationStrategy = GroupAggregationStrategy.AVERAGE,
        userWeights,
        maxRecommendations = 10,
        filters,
      } = options;
      
      // Vérifier que le créateur est inclus dans les participants
      const allParticipantIds = [...new Set([creatorId, ...participantIds])];
      
      // Créer le plan collaboratif
      const plan = await this.prisma.collaborativePlan.create({
        data: {
          title,
          description: description || '',
          status: CollaborativePlanStatus.DRAFT,
          creatorId,
          startDate,
          endDate,
          aggregationStrategy,
          userWeights: userWeights || {},
          participants: {
            create: allParticipantIds.map(userId => ({
              userId,
              role: userId === creatorId ? 'CREATOR' : 'PARTICIPANT',
            })),
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        include: {
          participants: {
            select: {
              userId: true,
              role: true,
            },
          },
        },
      });
      
      // Générer des recommandations de groupe
      const groupRecommendations = await this.groupRecommendationService.getGroupRecommendations({
        userIds: allParticipantIds,
        aggregationStrategy,
        userWeights,
        maxRecommendations,
        filters,
      });
      
      // Ajouter les recommandations au plan
      for (const recommendation of groupRecommendations) {
        await this.prisma.collaborativePlanRecommendation.create({
          data: {
            planId: plan.id,
            recommendationId: recommendation.id,
            recommendationType: recommendation.type,
            score: recommendation.groupScore,
            userScores: recommendation.userScores || {},
            metadata: recommendation.metadata || {},
            createdAt: new Date(),
          },
        });
      }
      
      // Mettre à jour le statut du plan
      const updatedPlan = await this.prisma.collaborativePlan.update({
        where: { id: plan.id },
        data: {
          status: CollaborativePlanStatus.VOTING,
          updatedAt: new Date(),
        },
        include: {
          participants: {
            select: {
              userId: true,
              role: true,
            },
          },
        },
      });
      
      // Émettre un événement de création de plan
      this.eventEmitter.emit('collaborative-plan.created', {
        planId: plan.id,
        creatorId,
        participantIds: allParticipantIds,
        recommendationCount: groupRecommendations.length,
      });
      
      return updatedPlan;
    } catch (error) {
      this.logger.error(`Erreur lors de la création du plan collaboratif: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Récupère un plan collaboratif
   * @param planId ID du plan
   * @returns Plan collaboratif
   */
  async getCollaborativePlan(planId: string): Promise<any> {
    try {
      const plan = await this.prisma.collaborativePlan.findUnique({
        where: { id: planId },
        include: {
          participants: {
            select: {
              userId: true,
              role: true,
            },
          },
          recommendations: {
            include: {
              votes: {
                select: {
                  userId: true,
                  voteType: true,
                  comment: true,
                  createdAt: true,
                },
              },
            },
          },
          creator: {
            select: {
              id: true,
              username: true,
              email: true,
              profilePicture: true,
            },
          },
        },
      });
      
      if (!plan) {
        throw new Error(`Plan collaboratif avec l'ID ${planId} non trouvé`);
      }
      
      return plan;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération du plan collaboratif: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Récupère les plans collaboratifs d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Plans collaboratifs
   */
  async getUserCollaborativePlans(userId: string): Promise<any[]> {
    try {
      const plans = await this.prisma.collaborativePlan.findMany({
        where: {
          participants: {
            some: {
              userId,
            },
          },
        },
        include: {
          participants: {
            select: {
              userId: true,
              role: true,
            },
          },
          creator: {
            select: {
              id: true,
              username: true,
              email: true,
              profilePicture: true,
            },
          },
        },
        orderBy: {
          updatedAt: 'desc',
        },
      });
      
      return plans;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des plans collaboratifs de l'utilisateur: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Vote pour une recommandation dans un plan collaboratif
   * @param options Options de vote
   * @returns Vote
   */
  async voteForRecommendation(options: VoteOptions): Promise<any> {
    try {
      const { planId, userId, recommendationId, voteType, comment } = options;
      
      // Vérifier que l'utilisateur est un participant du plan
      const participant = await this.prisma.collaborativePlanParticipant.findUnique({
        where: {
          planId_userId: {
            planId,
            userId,
          },
        },
      });
      
      if (!participant) {
        throw new Error(`L'utilisateur ${userId} n'est pas un participant du plan ${planId}`);
      }
      
      // Vérifier que le plan est en phase de vote
      const plan = await this.prisma.collaborativePlan.findUnique({
        where: { id: planId },
      });
      
      if (!plan || plan.status !== CollaborativePlanStatus.VOTING) {
        throw new Error(`Le plan ${planId} n'est pas en phase de vote`);
      }
      
      // Vérifier que la recommandation existe dans le plan
      const planRecommendation = await this.prisma.collaborativePlanRecommendation.findUnique({
        where: {
          planId_recommendationId: {
            planId,
            recommendationId,
          },
        },
      });
      
      if (!planRecommendation) {
        throw new Error(`La recommandation ${recommendationId} n'existe pas dans le plan ${planId}`);
      }
      
      // Vérifier si l'utilisateur a déjà voté pour cette recommandation
      const existingVote = await this.prisma.collaborativePlanVote.findUnique({
        where: {
          planId_recommendationId_userId: {
            planId,
            recommendationId,
            userId,
          },
        },
      });
      
      let vote;
      
      if (existingVote) {
        // Mettre à jour le vote existant
        vote = await this.prisma.collaborativePlanVote.update({
          where: {
            planId_recommendationId_userId: {
              planId,
              recommendationId,
              userId,
            },
          },
          data: {
            voteType,
            comment: comment || existingVote.comment,
            updatedAt: new Date(),
          },
        });
      } else {
        // Créer un nouveau vote
        vote = await this.prisma.collaborativePlanVote.create({
          data: {
            planId,
            recommendationId,
            userId,
            voteType,
            comment: comment || '',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        });
      }
      
      // Émettre un événement de vote
      this.eventEmitter.emit('collaborative-plan.vote', {
        planId,
        userId,
        recommendationId,
        voteType,
      });
      
      return vote;
    } catch (error) {
      this.logger.error(`Erreur lors du vote pour une recommandation: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Finalise un plan collaboratif
   * @param options Options de finalisation
   * @returns Plan finalisé
   */
  async finalizePlan(options: FinalizePlanOptions): Promise<any> {
    try {
      const { planId, userId, selectedRecommendationIds, comment } = options;
      
      // Vérifier que l'utilisateur est le créateur du plan
      const plan = await this.prisma.collaborativePlan.findUnique({
        where: { id: planId },
      });
      
      if (!plan) {
        throw new Error(`Plan collaboratif avec l'ID ${planId} non trouvé`);
      }
      
      if (plan.creatorId !== userId) {
        throw new Error(`Seul le créateur du plan peut le finaliser`);
      }
      
      if (plan.status !== CollaborativePlanStatus.VOTING) {
        throw new Error(`Le plan ne peut être finalisé que s'il est en phase de vote`);
      }
      
      // Vérifier que les recommandations sélectionnées existent dans le plan
      for (const recommendationId of selectedRecommendationIds) {
        const planRecommendation = await this.prisma.collaborativePlanRecommendation.findUnique({
          where: {
            planId_recommendationId: {
              planId,
              recommendationId,
            },
          },
        });
        
        if (!planRecommendation) {
          throw new Error(`La recommandation ${recommendationId} n'existe pas dans le plan ${planId}`);
        }
      }
      
      // Mettre à jour le statut du plan
      const updatedPlan = await this.prisma.collaborativePlan.update({
        where: { id: planId },
        data: {
          status: CollaborativePlanStatus.FINALIZED,
          selectedRecommendationIds,
          finalComment: comment || '',
          finalizedAt: new Date(),
          updatedAt: new Date(),
        },
        include: {
          participants: {
            select: {
              userId: true,
              role: true,
            },
          },
          recommendations: {
            where: {
              recommendationId: {
                in: selectedRecommendationIds,
              },
            },
            include: {
              votes: {
                select: {
                  userId: true,
                  voteType: true,
                  comment: true,
                  createdAt: true,
                },
              },
            },
          },
          creator: {
            select: {
              id: true,
              username: true,
              email: true,
              profilePicture: true,
            },
          },
        },
      });
      
      // Émettre un événement de finalisation de plan
      this.eventEmitter.emit('collaborative-plan.finalized', {
        planId,
        userId,
        selectedRecommendationIds,
      });
      
      return updatedPlan;
    } catch (error) {
      this.logger.error(`Erreur lors de la finalisation du plan collaboratif: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Annule un plan collaboratif
   * @param planId ID du plan
   * @param userId ID de l'utilisateur
   * @param reason Raison de l'annulation
   * @returns Plan annulé
   */
  async cancelPlan(planId: string, userId: string, reason?: string): Promise<any> {
    try {
      // Vérifier que l'utilisateur est le créateur du plan
      const plan = await this.prisma.collaborativePlan.findUnique({
        where: { id: planId },
      });
      
      if (!plan) {
        throw new Error(`Plan collaboratif avec l'ID ${planId} non trouvé`);
      }
      
      if (plan.creatorId !== userId) {
        throw new Error(`Seul le créateur du plan peut l'annuler`);
      }
      
      if (plan.status === CollaborativePlanStatus.CANCELLED) {
        throw new Error(`Le plan est déjà annulé`);
      }
      
      // Mettre à jour le statut du plan
      const updatedPlan = await this.prisma.collaborativePlan.update({
        where: { id: planId },
        data: {
          status: CollaborativePlanStatus.CANCELLED,
          cancelReason: reason || '',
          cancelledAt: new Date(),
          updatedAt: new Date(),
        },
        include: {
          participants: {
            select: {
              userId: true,
              role: true,
            },
          },
          creator: {
            select: {
              id: true,
              username: true,
              email: true,
              profilePicture: true,
            },
          },
        },
      });
      
      // Émettre un événement d'annulation de plan
      this.eventEmitter.emit('collaborative-plan.cancelled', {
        planId,
        userId,
        reason,
      });
      
      return updatedPlan;
    } catch (error) {
      this.logger.error(`Erreur lors de l'annulation du plan collaboratif: ${error.message}`);
      throw error;
    }
  }
}
