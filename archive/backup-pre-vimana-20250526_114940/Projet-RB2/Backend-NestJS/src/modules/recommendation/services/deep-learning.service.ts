import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationOptions } from '../interfaces/recommendation-options.interface';
import { firstValueFrom } from 'rxjs';
import { Cron, CronExpression } from '@nestjs/schedule';

/**
 * Service d'apprentissage profond pour les recommandations
 * Utilise des modèles de deep learning pour générer des recommandations de haute qualité
 */
@Injectable()
export class DeepLearningService {
  private readonly logger = new Logger(DeepLearningService.name);
  private readonly enabled: boolean;
  private readonly apiUrl: string;
  private readonly apiKey: string;
  private readonly modelId: string;
  private readonly batchSize: number;
  private readonly embeddingSize: number;
  private readonly trainingEnabled: boolean;
  private readonly trainingInterval: number;
  private isTraining = false;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.enabled = this.configService.get<boolean>('recommendation.deepLearning.enabled', false);
    this.apiUrl = this.configService.get<string>('recommendation.deepLearning.apiUrl', 'http://ml-service:8000');
    this.apiKey = this.configService.get<string>('recommendation.deepLearning.apiKey', '');
    this.modelId = this.configService.get<string>('recommendation.deepLearning.modelId', 'neural-cf-v1');
    this.batchSize = this.configService.get<number>('recommendation.deepLearning.batchSize', 64);
    this.embeddingSize = this.configService.get<number>('recommendation.deepLearning.embeddingSize', 128);
    this.trainingEnabled = this.configService.get<boolean>('recommendation.deepLearning.training.enabled', false);
    this.trainingInterval = this.configService.get<number>('recommendation.deepLearning.training.interval', 7); // jours

    this.logger.log(`DeepLearningService initialized with enabled=${this.enabled}, modelId=${this.modelId}`);
  }

  /**
   * Génère des recommandations en utilisant un modèle de deep learning
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Liste des recommandations
   */
  async getRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ) {
    if (!this.enabled) {
      this.logger.warn('Deep learning recommendations are disabled');
      return [];
    }

    try {
      this.logger.log(`Generating deep learning recommendations of type ${type} for user ${userId}`);

      // Récupérer les embeddings de l'utilisateur
      const userEmbedding = await this.getUserEmbedding(userId);
      if (!userEmbedding) {
        this.logger.warn(`No embedding found for user ${userId}`);
        return [];
      }

      // Récupérer les items candidats
      const candidates = await this.getCandidateItems(type, options);
      if (candidates.length === 0) {
        this.logger.warn(`No candidate items found for type ${type}`);
        return [];
      }

      // Récupérer les prédictions du modèle
      const predictions = await this.getPredictions(userEmbedding, candidates, type);

      // Trier les prédictions par score et limiter le nombre de résultats
      const limit = options.limit || 10;
      const recommendations = predictions
        .sort((a, b) => b.score - a.score)
        .slice(0, limit)
        .map(prediction => ({
          id: prediction.itemId,
          type,
          title: prediction.title,
          description: prediction.description,
          score: prediction.score,
          sources: ['deep-learning'],
          metadata: prediction.metadata || {},
        }));

      this.logger.log(`Generated ${recommendations.length} deep learning recommendations for user ${userId}`);
      return recommendations;
    } catch (error) {
      this.logger.error(`Error generating deep learning recommendations: ${error.message}`);
      return [];
    }
  }

  /**
   * Compte le nombre de recommandations disponibles
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Nombre de recommandations
   */
  async countRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ): Promise<number> {
    if (!this.enabled) {
      return 0;
    }

    try {
      // Récupérer le nombre d'items candidats
      const candidates = await this.getCandidateItems(type, options);
      return candidates.length;
    } catch (error) {
      this.logger.error(`Error counting deep learning recommendations: ${error.message}`);
      return 0;
    }
  }

  /**
   * Récupère l'embedding d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Embedding de l'utilisateur
   */
  private async getUserEmbedding(userId: string): Promise<number[] | null> {
    try {
      // Vérifier si l'embedding existe déjà en base de données
      const userEmbedding = await this.prisma.userEmbedding.findUnique({
        where: {
          userId_modelId: {
            userId,
            modelId: this.modelId,
          },
        },
        select: {
          embedding: true,
        },
      });

      if (userEmbedding) {
        return userEmbedding.embedding as number[];
      }

      // Si l'embedding n'existe pas, le générer via l'API
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.apiUrl}/embeddings/user`,
          {
            userId,
            modelId: this.modelId,
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.apiKey}`,
            },
          },
        ),
      );

      const embedding = response.data.embedding;

      // Sauvegarder l'embedding en base de données
      await this.prisma.userEmbedding.upsert({
        where: {
          userId_modelId: {
            userId,
            modelId: this.modelId,
          },
        },
        update: {
          embedding,
          updatedAt: new Date(),
        },
        create: {
          userId,
          modelId: this.modelId,
          embedding,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      return embedding;
    } catch (error) {
      this.logger.error(`Error getting user embedding: ${error.message}`);
      return null;
    }
  }

  /**
   * Récupère les items candidats pour les recommandations
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Liste des items candidats
   */
  private async getCandidateItems(
    type: RecommendationType,
    options: RecommendationOptions = {},
  ): Promise<any[]> {
    try {
      // Construire la requête en fonction du type
      let items = [];
      const { excludeIds, filters } = options;

      switch (type) {
        case RecommendationType.COURSE:
          items = await this.prisma.course.findMany({
            where: {
              id: excludeIds ? { notIn: excludeIds } : undefined,
              ...filters,
            },
            select: {
              id: true,
              title: true,
              description: true,
              category: true,
              level: true,
              metadata: true,
              embedding: true,
            },
          });
          break;
        case RecommendationType.RETREAT:
          items = await this.prisma.retreat.findMany({
            where: {
              id: excludeIds ? { notIn: excludeIds } : undefined,
              ...filters,
            },
            select: {
              id: true,
              title: true,
              description: true,
              location: true,
              startDate: true,
              endDate: true,
              metadata: true,
              embedding: true,
            },
          });
          break;
        default:
          items = [];
      }

      return items;
    } catch (error) {
      this.logger.error(`Error getting candidate items: ${error.message}`);
      return [];
    }
  }

  /**
   * Récupère les prédictions du modèle
   * @param userEmbedding Embedding de l'utilisateur
   * @param candidates Items candidats
   * @param type Type de recommandation
   * @returns Liste des prédictions
   */
  private async getPredictions(
    userEmbedding: number[],
    candidates: any[],
    type: RecommendationType,
  ): Promise<any[]> {
    try {
      // Préparer les données pour l'API
      const requestData = {
        userEmbedding,
        items: candidates.map(item => ({
          itemId: item.id,
          itemType: type,
          embedding: item.embedding || null,
          features: {
            title: item.title,
            description: item.description,
            category: item.category,
            level: item.level,
            location: item.location,
            metadata: item.metadata,
          },
        })),
        modelId: this.modelId,
      };

      // Appeler l'API pour obtenir les prédictions
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.apiUrl}/predict`,
          requestData,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.apiKey}`,
            },
          },
        ),
      );

      // Transformer les prédictions en format attendu
      return response.data.predictions.map(prediction => ({
        itemId: prediction.itemId,
        score: prediction.score,
        title: candidates.find(c => c.id === prediction.itemId)?.title || '',
        description: candidates.find(c => c.id === prediction.itemId)?.description || '',
        metadata: candidates.find(c => c.id === prediction.itemId)?.metadata || {},
      }));
    } catch (error) {
      this.logger.error(`Error getting predictions: ${error.message}`);
      return [];
    }
  }

  /**
   * Entraîne le modèle de deep learning
   * Exécuté une fois par semaine
   */
  @Cron(CronExpression.EVERY_WEEK)
  async trainModel(): Promise<void> {
    if (!this.enabled || !this.trainingEnabled || this.isTraining) {
      return;
    }

    this.isTraining = true;
    this.logger.log(`Starting training for model ${this.modelId}`);

    try {
      // Récupérer les données d'entraînement
      const trainingData = await this.getTrainingData();
      if (trainingData.interactions.length === 0) {
        this.logger.warn('No training data available');
        return;
      }

      // Appeler l'API pour entraîner le modèle
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.apiUrl}/train`,
          {
            modelId: this.modelId,
            data: trainingData,
            parameters: {
              batchSize: this.batchSize,
              embeddingSize: this.embeddingSize,
              epochs: 10,
              learningRate: 0.001,
            },
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.apiKey}`,
            },
          },
        ),
      );

      this.logger.log(`Training completed for model ${this.modelId}: ${JSON.stringify(response.data.metrics)}`);
    } catch (error) {
      this.logger.error(`Error training model: ${error.message}`);
    } finally {
      this.isTraining = false;
    }
  }

  /**
   * Récupère les données d'entraînement
   * @returns Données d'entraînement
   */
  private async getTrainingData(): Promise<any> {
    try {
      // Récupérer les interactions récentes
      const interactions = await this.prisma.userInteraction.findMany({
        where: {
          createdAt: {
            gte: new Date(Date.now() - this.trainingInterval * 24 * 60 * 60 * 1000), // Interactions des X derniers jours
          },
        },
        select: {
          userId: true,
          itemId: true,
          itemType: true,
          interactionType: true,
          createdAt: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      // Récupérer les utilisateurs
      const userIds = [...new Set(interactions.map(i => i.userId))];
      const users = await this.prisma.user.findMany({
        where: {
          id: {
            in: userIds,
          },
        },
        select: {
          id: true,
          profile: {
            select: {
              preferences: true,
              location: true,
            },
          },
        },
      });

      // Récupérer les items
      const itemIds = [...new Set(interactions.map(i => i.itemId))];
      const courseIds = itemIds.filter(id => 
        interactions.some(i => i.itemId === id && i.itemType === RecommendationType.COURSE)
      );
      const retreatIds = itemIds.filter(id => 
        interactions.some(i => i.itemId === id && i.itemType === RecommendationType.RETREAT)
      );

      const courses = await this.prisma.course.findMany({
        where: {
          id: {
            in: courseIds,
          },
        },
        select: {
          id: true,
          title: true,
          description: true,
          category: true,
          level: true,
          metadata: true,
        },
      });

      const retreats = await this.prisma.retreat.findMany({
        where: {
          id: {
            in: retreatIds,
          },
        },
        select: {
          id: true,
          title: true,
          description: true,
          location: true,
          startDate: true,
          endDate: true,
          metadata: true,
        },
      });

      return {
        interactions,
        users,
        items: {
          courses,
          retreats,
        },
      };
    } catch (error) {
      this.logger.error(`Error getting training data: ${error.message}`);
      return {
        interactions: [],
        users: [],
        items: {
          courses: [],
          retreats: [],
        },
      };
    }
  }
}
