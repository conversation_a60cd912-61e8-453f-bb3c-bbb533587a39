import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Catégories de métriques
 */
export enum MetricCategory {
  /** Métriques de précision */
  ACCURACY = 'ACCURACY',
  
  /** Métriques de classement */
  RANKING = 'RANKING',
  
  /** Métriques de diversité */
  DIVERSITY = 'DIVERSITY',
  
  /** Métriques de nouveauté */
  NOVELTY = 'NOVELTY',
  
  /** Métriques de couverture */
  COVERAGE = 'COVERAGE',
  
  /** Métriques d'utilité commerciale */
  BUSINESS = 'BUSINESS',
  
  /** Métriques de performance */
  PERFORMANCE = 'PERFORMANCE',
}

/**
 * Définition d'une métrique
 */
export interface MetricDefinition {
  /** Identifiant de la métrique */
  id: string;
  
  /** Nom de la métrique */
  name: string;
  
  /** Description de la métrique */
  description: string;
  
  /** Catégorie de la métrique */
  category: MetricCategory;
  
  /** Fonction de calcul */
  calculate: (data: any, options?: any) => number | Promise<number>;
  
  /** Meilleure direction (higher = plus élevé est mieux, lower = plus bas est mieux) */
  direction: 'higher' | 'lower';
  
  /** Plage de valeurs typiques */
  range?: [number, number];
  
  /** Valeur de référence */
  benchmark?: number;
}

/**
 * Service de métriques
 * Fournit des méthodes pour calculer différentes métriques d'évaluation
 */
@Injectable()
export class MetricsService {
  private readonly logger = new Logger(MetricsService.name);
  private readonly metrics: Map<string, MetricDefinition> = new Map();
  
  constructor(private readonly configService: ConfigService) {
    this.logger.log('MetricsService initialized');
    this.registerMetrics();
  }
  
  /**
   * Enregistre toutes les métriques disponibles
   */
  private registerMetrics() {
    // Métriques de précision
    this.registerMetric({
      id: 'precision_at_k',
      name: 'Precision@k',
      description: 'Proportion d\'éléments pertinents parmi les k premiers éléments recommandés',
      category: MetricCategory.ACCURACY,
      calculate: this.calculatePrecisionAtK.bind(this),
      direction: 'higher',
      range: [0, 1],
      benchmark: 0.3,
    });
    
    this.registerMetric({
      id: 'recall_at_k',
      name: 'Recall@k',
      description: 'Proportion d\'éléments pertinents recommandés parmi tous les éléments pertinents',
      category: MetricCategory.ACCURACY,
      calculate: this.calculateRecallAtK.bind(this),
      direction: 'higher',
      range: [0, 1],
      benchmark: 0.2,
    });
    
    this.registerMetric({
      id: 'f1_score',
      name: 'F1-Score',
      description: 'Moyenne harmonique de la précision et du rappel',
      category: MetricCategory.ACCURACY,
      calculate: this.calculateF1Score.bind(this),
      direction: 'higher',
      range: [0, 1],
      benchmark: 0.25,
    });
    
    // Métriques de classement
    this.registerMetric({
      id: 'ndcg',
      name: 'NDCG',
      description: 'Normalized Discounted Cumulative Gain',
      category: MetricCategory.RANKING,
      calculate: this.calculateNDCG.bind(this),
      direction: 'higher',
      range: [0, 1],
      benchmark: 0.4,
    });
    
    this.registerMetric({
      id: 'mrr',
      name: 'MRR',
      description: 'Mean Reciprocal Rank',
      category: MetricCategory.RANKING,
      calculate: this.calculateMRR.bind(this),
      direction: 'higher',
      range: [0, 1],
      benchmark: 0.2,
    });
    
    this.registerMetric({
      id: 'map',
      name: 'MAP',
      description: 'Mean Average Precision',
      category: MetricCategory.RANKING,
      calculate: this.calculateMAP.bind(this),
      direction: 'higher',
      range: [0, 1],
      benchmark: 0.15,
    });
    
    // Métriques de diversité
    this.registerMetric({
      id: 'intra_list_similarity',
      name: 'Intra-List Similarity',
      description: 'Similarité moyenne entre les éléments d\'une liste de recommandations',
      category: MetricCategory.DIVERSITY,
      calculate: this.calculateIntraListSimilarity.bind(this),
      direction: 'lower',
      range: [0, 1],
      benchmark: 0.3,
    });
    
    this.registerMetric({
      id: 'category_coverage',
      name: 'Category Coverage',
      description: 'Proportion de catégories couvertes par les recommandations',
      category: MetricCategory.DIVERSITY,
      calculate: this.calculateCategoryCoverage.bind(this),
      direction: 'higher',
      range: [0, 1],
      benchmark: 0.5,
    });
    
    // Métriques d'utilité commerciale
    this.registerMetric({
      id: 'ctr',
      name: 'CTR',
      description: 'Click-Through Rate',
      category: MetricCategory.BUSINESS,
      calculate: this.calculateCTR.bind(this),
      direction: 'higher',
      range: [0, 0.5],
      benchmark: 0.05,
    });
    
    this.registerMetric({
      id: 'conversion_rate',
      name: 'Conversion Rate',
      description: 'Taux de conversion des recommandations',
      category: MetricCategory.BUSINESS,
      calculate: this.calculateConversionRate.bind(this),
      direction: 'higher',
      range: [0, 0.2],
      benchmark: 0.02,
    });
    
    this.registerMetric({
      id: 'revenue_per_recommendation',
      name: 'Revenue per Recommendation',
      description: 'Revenu moyen généré par recommandation',
      category: MetricCategory.BUSINESS,
      calculate: this.calculateRevenuePerRecommendation.bind(this),
      direction: 'higher',
      benchmark: 1.0,
    });
    
    // Métriques de performance
    this.registerMetric({
      id: 'response_time',
      name: 'Response Time',
      description: 'Temps de réponse moyen en millisecondes',
      category: MetricCategory.PERFORMANCE,
      calculate: this.calculateResponseTime.bind(this),
      direction: 'lower',
      benchmark: 200,
    });
    
    this.registerMetric({
      id: 'throughput',
      name: 'Throughput',
      description: 'Nombre de recommandations par seconde',
      category: MetricCategory.PERFORMANCE,
      calculate: this.calculateThroughput.bind(this),
      direction: 'higher',
      benchmark: 100,
    });
    
    this.logger.log(`Registered ${this.metrics.size} metrics`);
  }
  
  /**
   * Enregistre une métrique
   * @param metric Définition de la métrique
   */
  private registerMetric(metric: MetricDefinition) {
    this.metrics.set(metric.id, metric);
  }
  
  /**
   * Récupère une métrique par son identifiant
   * @param metricId Identifiant de la métrique
   * @returns Définition de la métrique
   */
  getMetric(metricId: string): MetricDefinition | undefined {
    return this.metrics.get(metricId);
  }
  
  /**
   * Récupère toutes les métriques
   * @param category Catégorie de métriques (optionnel)
   * @returns Liste des métriques
   */
  getAllMetrics(category?: MetricCategory): MetricDefinition[] {
    if (category) {
      return Array.from(this.metrics.values()).filter(metric => metric.category === category);
    }
    return Array.from(this.metrics.values());
  }
  
  /**
   * Calcule les métriques pour une évaluation hors ligne
   * @param algorithmId Identifiant de l'algorithme
   * @param data Données historiques
   * @param metricIds Identifiants des métriques à calculer
   * @returns Métriques calculées
   */
  async calculateOfflineMetrics(
    algorithmId: string,
    data: any[],
    metricIds?: string[],
  ): Promise<Record<string, number>> {
    const metrics: Record<string, number> = {};
    const metricsToCalculate = metricIds
      ? metricIds.map(id => this.getMetric(id)).filter(Boolean) as MetricDefinition[]
      : this.getAllMetrics();
    
    for (const metric of metricsToCalculate) {
      try {
        metrics[metric.id] = await metric.calculate(data, { algorithmId });
      } catch (error) {
        this.logger.error(`Error calculating metric ${metric.id}: ${error.message}`);
        metrics[metric.id] = NaN;
      }
    }
    
    return metrics;
  }
  
  /**
   * Calcule les métriques pour une évaluation en ligne
   * @param algorithmId Identifiant de l'algorithme
   * @param data Données d'interactions en ligne
   * @param metricIds Identifiants des métriques à calculer
   * @returns Métriques calculées
   */
  async calculateOnlineMetrics(
    algorithmId: string,
    data: any[],
    metricIds?: string[],
  ): Promise<Record<string, number>> {
    const metrics: Record<string, number> = {};
    const metricsToCalculate = metricIds
      ? metricIds.map(id => this.getMetric(id)).filter(Boolean) as MetricDefinition[]
      : this.getAllMetrics().filter(m => 
          m.category === MetricCategory.BUSINESS || 
          m.category === MetricCategory.PERFORMANCE
        );
    
    for (const metric of metricsToCalculate) {
      try {
        metrics[metric.id] = await metric.calculate(data, { algorithmId, online: true });
      } catch (error) {
        this.logger.error(`Error calculating metric ${metric.id}: ${error.message}`);
        metrics[metric.id] = NaN;
      }
    }
    
    return metrics;
  }
  
  /**
   * Calcule les métriques pour une évaluation par simulation
   * @param simulationResults Résultats de la simulation
   * @param metricIds Identifiants des métriques à calculer
   * @returns Métriques calculées
   */
  async calculateSimulationMetrics(
    simulationResults: any,
    metricIds?: string[],
  ): Promise<Record<string, number>> {
    const metrics: Record<string, number> = {};
    const metricsToCalculate = metricIds
      ? metricIds.map(id => this.getMetric(id)).filter(Boolean) as MetricDefinition[]
      : this.getAllMetrics();
    
    for (const metric of metricsToCalculate) {
      try {
        metrics[metric.id] = await metric.calculate(simulationResults, { simulation: true });
      } catch (error) {
        this.logger.error(`Error calculating metric ${metric.id}: ${error.message}`);
        metrics[metric.id] = NaN;
      }
    }
    
    return metrics;
  }
  
  // Implémentations des métriques
  
  private async calculatePrecisionAtK(data: any, options: any = {}): Promise<number> {
    // TODO: Implémenter le calcul de Precision@k
    return 0.3;
  }
  
  private async calculateRecallAtK(data: any, options: any = {}): Promise<number> {
    // TODO: Implémenter le calcul de Recall@k
    return 0.2;
  }
  
  private async calculateF1Score(data: any, options: any = {}): Promise<number> {
    // TODO: Implémenter le calcul de F1-Score
    return 0.25;
  }
  
  private async calculateNDCG(data: any, options: any = {}): Promise<number> {
    // TODO: Implémenter le calcul de NDCG
    return 0.4;
  }
  
  private async calculateMRR(data: any, options: any = {}): Promise<number> {
    // TODO: Implémenter le calcul de MRR
    return 0.2;
  }
  
  private async calculateMAP(data: any, options: any = {}): Promise<number> {
    // TODO: Implémenter le calcul de MAP
    return 0.15;
  }
  
  private async calculateIntraListSimilarity(data: any, options: any = {}): Promise<number> {
    // TODO: Implémenter le calcul de Intra-List Similarity
    return 0.3;
  }
  
  private async calculateCategoryCoverage(data: any, options: any = {}): Promise<number> {
    // TODO: Implémenter le calcul de Category Coverage
    return 0.5;
  }
  
  private async calculateCTR(data: any, options: any = {}): Promise<number> {
    // TODO: Implémenter le calcul de CTR
    return 0.05;
  }
  
  private async calculateConversionRate(data: any, options: any = {}): Promise<number> {
    // TODO: Implémenter le calcul de Conversion Rate
    return 0.02;
  }
  
  private async calculateRevenuePerRecommendation(data: any, options: any = {}): Promise<number> {
    // TODO: Implémenter le calcul de Revenue per Recommendation
    return 1.0;
  }
  
  private async calculateResponseTime(data: any, options: any = {}): Promise<number> {
    // TODO: Implémenter le calcul de Response Time
    return 200;
  }
  
  private async calculateThroughput(data: any, options: any = {}): Promise<number> {
    // TODO: Implémenter le calcul de Throughput
    return 100;
  }
}
