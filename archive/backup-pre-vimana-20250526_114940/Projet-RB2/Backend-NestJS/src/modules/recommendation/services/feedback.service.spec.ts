import { Test, TestingModule } from '@nestjs/testing';
import { FeedbackService, FeedbackType } from './feedback.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { HttpException } from '@nestjs/common';

describe('FeedbackService', () => {
  let service: FeedbackService;
  let eventEmitter: EventEmitter2;

  const mockPrismaService = {
    recommendationFeedback: {
      findFirst: jest.fn(),
      update: jest.fn(),
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      findUnique: jest.fn(),
      delete: jest.fn(),
    },
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockEventEmitter = {
    emit: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FeedbackService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
      ],
    }).compile();

    service = module.get<FeedbackService>(FeedbackService);
    eventEmitter = module.get<EventEmitter2>(EventEmitter2);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('recordFeedback', () => {
    it('should create a new feedback if it does not exist', async () => {
      const feedbackData = {
        userId: 'user1',
        recommendationId: 'rec1',
        recommendationType: RecommendationType.RETREAT,
        feedbackType: FeedbackType.LIKE,
        comment: 'Great recommendation!',
        rating: 5,
      };

      const result = await service.recordFeedback(feedbackData);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Feedback enregistré avec succès');
      expect(result.data).toBeDefined();
      expect(result.data.userId).toBe(feedbackData.userId);
      expect(result.data.recommendationId).toBe(feedbackData.recommendationId);
      expect(result.data.recommendationType).toBe(feedbackData.recommendationType);
      expect(result.data.feedbackType).toBe(feedbackData.feedbackType);
      expect(result.data.comment).toBe(feedbackData.comment);
      expect(result.data.rating).toBe(feedbackData.rating);
      expect(eventEmitter.emit).toHaveBeenCalledWith('recommendation.feedback', expect.any(Object));
    });

    it('should update an existing feedback', async () => {
      // First create a feedback
      const feedbackData = {
        userId: 'user1',
        recommendationId: 'rec1',
        recommendationType: RecommendationType.RETREAT,
        feedbackType: FeedbackType.LIKE,
        comment: 'Great recommendation!',
        rating: 5,
      };

      await service.recordFeedback(feedbackData);

      // Then update it
      const updatedFeedbackData = {
        ...feedbackData,
        comment: 'Updated comment',
        rating: 4,
      };

      const result = await service.recordFeedback(updatedFeedbackData);

      expect(result.success).toBe(true);
      expect(result.data.comment).toBe(updatedFeedbackData.comment);
      expect(result.data.rating).toBe(updatedFeedbackData.rating);
      expect(eventEmitter.emit).toHaveBeenCalledTimes(2);
    });
  });

  describe('getUserFeedbackForRecommendation', () => {
    it('should return feedbacks for a specific recommendation', async () => {
      // Create some feedbacks
      await service.recordFeedback({
        userId: 'user1',
        recommendationId: 'rec1',
        recommendationType: RecommendationType.RETREAT,
        feedbackType: FeedbackType.LIKE,
      });

      await service.recordFeedback({
        userId: 'user1',
        recommendationId: 'rec1',
        recommendationType: RecommendationType.RETREAT,
        feedbackType: FeedbackType.SAVE,
      });

      const result = await service.getUserFeedbackForRecommendation(
        'user1',
        'rec1',
        RecommendationType.RETREAT,
      );

      expect(result).toHaveLength(2);
      expect(result[0].recommendationId).toBe('rec1');
      expect(result[1].recommendationId).toBe('rec1');
    });
  });

  describe('getUserFeedbacks', () => {
    it('should return all feedbacks for a user with pagination', async () => {
      // Create some feedbacks
      await service.recordFeedback({
        userId: 'user1',
        recommendationId: 'rec1',
        recommendationType: RecommendationType.RETREAT,
        feedbackType: FeedbackType.LIKE,
      });

      await service.recordFeedback({
        userId: 'user1',
        recommendationId: 'rec2',
        recommendationType: RecommendationType.COURSE,
        feedbackType: FeedbackType.SAVE,
      });

      const result = await service.getUserFeedbacks('user1', { page: 1, limit: 10 });

      expect(result.data).toHaveLength(2);
      expect(result.pagination.total).toBe(2);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(10);
    });

    it('should filter feedbacks by type', async () => {
      // Create some feedbacks
      await service.recordFeedback({
        userId: 'user1',
        recommendationId: 'rec1',
        recommendationType: RecommendationType.RETREAT,
        feedbackType: FeedbackType.LIKE,
      });

      await service.recordFeedback({
        userId: 'user1',
        recommendationId: 'rec2',
        recommendationType: RecommendationType.COURSE,
        feedbackType: FeedbackType.SAVE,
      });

      const result = await service.getUserFeedbacks('user1', {
        feedbackType: FeedbackType.LIKE,
      });

      expect(result.data).toHaveLength(1);
      expect(result.data[0].feedbackType).toBe(FeedbackType.LIKE);
    });
  });

  describe('deleteFeedback', () => {
    it('should delete a feedback', async () => {
      // Create a feedback
      const feedbackResult = await service.recordFeedback({
        userId: 'user1',
        recommendationId: 'rec1',
        recommendationType: RecommendationType.RETREAT,
        feedbackType: FeedbackType.LIKE,
      });

      const feedbackId = feedbackResult.data.id;

      const result = await service.deleteFeedback(feedbackId, 'user1');

      expect(result.success).toBe(true);
      expect(result.message).toBe('Feedback supprimé avec succès');
    });

    it('should throw an error if feedback does not exist', async () => {
      await expect(service.deleteFeedback('non-existent-id', 'user1')).rejects.toThrow(HttpException);
    });

    it('should throw an error if user is not the owner', async () => {
      // Create a feedback
      const feedbackResult = await service.recordFeedback({
        userId: 'user1',
        recommendationId: 'rec1',
        recommendationType: RecommendationType.RETREAT,
        feedbackType: FeedbackType.LIKE,
      });

      const feedbackId = feedbackResult.data.id;

      await expect(service.deleteFeedback(feedbackId, 'user2')).rejects.toThrow(HttpException);
    });
  });
});
