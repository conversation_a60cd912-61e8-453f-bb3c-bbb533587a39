import { Test, TestingModule } from '@nestjs/testing';
import { DiversityFilterService } from './diversity-filter.service';
import { Logger } from '@nestjs/common';

describe('DiversityFilterService', () => {
  let service: DiversityFilterService;

  // Mock des données
  const mockRecommendations = [
    {
      id: 'item1',
      score: 0.9,
      metadata: {
        category: 'yoga',
        tags: ['beginner', 'morning', 'flexibility'],
        location: {
          country: 'France'
        }
      }
    },
    {
      id: 'item2',
      score: 0.85,
      metadata: {
        category: 'yoga',
        tags: ['intermediate', 'morning', 'strength'],
        location: {
          country: 'France'
        }
      }
    },
    {
      id: 'item3',
      score: 0.8,
      metadata: {
        category: 'meditation',
        tags: ['beginner', 'evening', 'mindfulness'],
        location: {
          country: 'Spain'
        }
      }
    },
    {
      id: 'item4',
      score: 0.75,
      metadata: {
        category: 'yoga',
        tags: ['advanced', 'evening', 'flexibility'],
        location: {
          country: 'Italy'
        }
      }
    },
    {
      id: 'item5',
      score: 0.7,
      metadata: {
        category: 'nutrition',
        tags: ['beginner', 'diet', 'health'],
        location: {
          country: 'Germany'
        }
      }
    }
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DiversityFilterService,
        {
          provide: Logger,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn()
          }
        }
      ],
    }).compile();

    service = module.get<DiversityFilterService>(DiversityFilterService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('applyDiversityFilter', () => {
    it('should return the original recommendations if diversification is disabled', () => {
      const options = {
        diversification: {
          enabled: false
        }
      };
      
      const result = service.applyDiversityFilter([...mockRecommendations], options);
      
      expect(result).toEqual(mockRecommendations);
    });
    
    it('should return the original recommendations if the list is empty', () => {
      const result = service.applyDiversityFilter([], {});
      
      expect(result).toEqual([]);
    });
    
    it('should apply MMR diversification by default', () => {
      // Espionner la méthode privée
      jest.spyOn<any, any>(service, 'applyMaximalMarginalRelevance');
      
      const options = {
        diversification: {
          enabled: true,
          weight: 0.3
        }
      };
      
      service.applyDiversityFilter([...mockRecommendations], options);
      
      expect((service as any).applyMaximalMarginalRelevance).toHaveBeenCalledWith(
        expect.any(Array),
        options.diversification.weight
      );
    });
    
    it('should apply the specified diversification method', () => {
      // Espionner les méthodes privées
      jest.spyOn<any, any>(service, 'applyDeterminantalPointProcess');
      
      const options = {
        diversification: {
          enabled: true,
          method: 'DETERMINANTAL',
          weight: 0.4
        }
      };
      
      service.applyDiversityFilter([...mockRecommendations], options);
      
      expect((service as any).applyDeterminantalPointProcess).toHaveBeenCalledWith(
        expect.any(Array),
        options.diversification.weight
      );
    });
  });

  describe('applyMaximalMarginalRelevance', () => {
    it('should diversify recommendations while maintaining relevance', () => {
      // Restaurer l'implémentation réelle pour ce test
      jest.spyOn<any, any>(service, 'applyMaximalMarginalRelevance').mockRestore();
      
      // Appeler la méthode privée directement pour le test
      const lambda = 0.5; // Équilibre entre pertinence et diversité
      const result = (service as any).applyMaximalMarginalRelevance([...mockRecommendations], lambda);
      
      // Vérifications
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(mockRecommendations.length);
      
      // Le premier élément devrait être le plus pertinent
      expect(result[0].id).toBe('item1');
      
      // Vérifier que les éléments diversifiés sont présents
      // Par exemple, on s'attend à ce que des éléments de différentes catégories soient inclus
      const categories = new Set(result.map(r => r.metadata.category));
      expect(categories.size).toBeGreaterThan(1);
      
      // Vérifier que les éléments de même catégorie mais avec des tags différents sont séparés
      const yogaItems = result.filter(r => r.metadata.category === 'yoga');
      if (yogaItems.length > 1) {
        // Vérifier que les items yoga ne sont pas consécutifs
        let consecutiveYogaItems = false;
        for (let i = 0; i < result.length - 1; i++) {
          if (result[i].metadata.category === 'yoga' && result[i + 1].metadata.category === 'yoga') {
            consecutiveYogaItems = true;
            break;
          }
        }
        expect(consecutiveYogaItems).toBe(false);
      }
    });
  });

  describe('calculateSimilarity', () => {
    it('should calculate similarity based on metadata', () => {
      // Restaurer l'implémentation réelle pour ce test
      jest.spyOn<any, any>(service, 'calculateSimilarity').mockRestore();
      
      // Items avec catégories identiques mais tags différents
      const item1 = mockRecommendations[0]; // yoga, beginner, morning, flexibility, France
      const item2 = mockRecommendations[1]; // yoga, intermediate, morning, strength, France
      
      const similarity = (service as any).calculateSimilarity(item1, item2);
      
      // La similarité devrait être élevée mais pas 1.0 (pas identiques)
      expect(similarity).toBeGreaterThan(0.5);
      expect(similarity).toBeLessThan(1.0);
      
      // Items avec catégories et pays différents
      const item3 = mockRecommendations[2]; // meditation, beginner, evening, mindfulness, Spain
      const similarity2 = (service as any).calculateSimilarity(item1, item3);
      
      // La similarité devrait être plus faible
      expect(similarity2).toBeLessThan(similarity);
    });
    
    it('should handle missing metadata', () => {
      // Restaurer l'implémentation réelle pour ce test
      jest.spyOn<any, any>(service, 'calculateSimilarity').mockRestore();
      
      const item1 = {
        id: 'item1',
        score: 0.9,
        metadata: {
          category: 'yoga'
          // Pas de tags ni de location
        }
      };
      
      const item2 = {
        id: 'item2',
        score: 0.8,
        metadata: {
          // Pas de catégorie
          tags: ['beginner', 'morning'],
          location: {
            country: 'France'
          }
        }
      };
      
      const similarity = (service as any).calculateSimilarity(item1, item2);
      
      // La similarité devrait être calculée sur les champs disponibles
      expect(similarity).toBeGreaterThanOrEqual(0);
      expect(similarity).toBeLessThanOrEqual(1);
    });
  });
});
