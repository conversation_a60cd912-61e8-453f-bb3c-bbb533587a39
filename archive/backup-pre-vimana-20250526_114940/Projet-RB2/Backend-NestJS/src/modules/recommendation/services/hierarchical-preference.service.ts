import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { v4 as uuidv4 } from 'uuid';
import {
  PreferenceTree,
  PreferenceNode,
  PreferenceValue,
  PreferenceSource,
  PreferenceUpdateOperation,
  PreferenceQueryOptions,
  ConflictResolutionStrategy,
  PreferenceEvent,
  PreferenceSnapshot,
} from '../interfaces/hierarchical-preference.interface';

/**
 * Service for managing hierarchical user preferences
 */
@Injectable()
export class HierarchicalPreferenceService {
  private readonly logger = new Logger(HierarchicalPreferenceService.name);
  private readonly defaultConflictStrategy: ConflictResolutionStrategy;
  private readonly cacheEnabled: boolean;
  private readonly cacheExpirationMs: number;
  private readonly preferenceCache: Map<string, { tree: PreferenceTree; expiresAt: number }> = new Map();

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.defaultConflictStrategy = this.configService.get<ConflictResolutionStrategy>(
      'PREFERENCE_CONFLICT_STRATEGY',
      ConflictResolutionStrategy.HIGHEST_CONFIDENCE,
    );
    this.cacheEnabled = this.configService.get<boolean>('PREFERENCE_CACHE_ENABLED', true);
    this.cacheExpirationMs = this.configService.get<number>('PREFERENCE_CACHE_EXPIRATION_MS', 5 * 60 * 1000); // 5 minutes
  }

  /**
   * Get the preference tree for a user
   * @param userId User ID
   * @returns Preference tree
   */
  async getPreferenceTree(userId: string): Promise<PreferenceTree> {
    try {
      // Check cache first if enabled
      if (this.cacheEnabled) {
        const cached = this.preferenceCache.get(userId);
        if (cached && cached.expiresAt > Date.now()) {
          return cached.tree;
        }
      }

      // Get from database
      const userPreferences = await this.prisma.userPreference.findMany({
        where: { userId },
        orderBy: { updatedAt: 'desc' },
      });

      // If no preferences found, create a new tree
      if (!userPreferences || userPreferences.length === 0) {
        return this.createEmptyPreferenceTree(userId);
      }

      // Build the tree from flat preferences
      const tree = this.buildPreferenceTree(userId, userPreferences);

      // Cache the result if enabled
      if (this.cacheEnabled) {
        this.preferenceCache.set(userId, {
          tree,
          expiresAt: Date.now() + this.cacheExpirationMs,
        });
      }

      return tree;
    } catch (error) {
      this.logger.error(`Error getting preference tree for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create an empty preference tree for a new user
   * @param userId User ID
   * @returns Empty preference tree
   */
  private createEmptyPreferenceTree(userId: string): PreferenceTree {
    const now = new Date();
    return {
      userId,
      root: {
        id: uuidv4(),
        path: [],
        key: 'root',
        values: [],
        children: [],
        createdAt: now,
        updatedAt: now,
      },
      version: 1,
      lastFullUpdate: now,
    };
  }

  /**
   * Build a preference tree from flat preferences
   * @param userId User ID
   * @param preferences Flat preferences from database
   * @returns Preference tree
   */
  private buildPreferenceTree(userId: string, preferences: any[]): PreferenceTree {
    const now = new Date();
    const root: PreferenceNode = {
      id: uuidv4(),
      path: [],
      key: 'root',
      values: [],
      children: [],
      createdAt: now,
      updatedAt: now,
    };

    // Group preferences by path
    const preferencesByPath: Record<string, any[]> = {};
    for (const pref of preferences) {
      const pathStr = pref.path || '';
      if (!preferencesByPath[pathStr]) {
        preferencesByPath[pathStr] = [];
      }
      preferencesByPath[pathStr].push(pref);
    }

    // Build the tree recursively
    this.buildTreeRecursive(root, preferencesByPath, '');

    return {
      userId,
      root,
      version: Math.max(...preferences.map(p => p.version || 0), 1),
      lastFullUpdate: now,
    };
  }

  /**
   * Recursively build a preference tree
   * @param node Current node
   * @param preferencesByPath Preferences grouped by path
   * @param currentPath Current path string
   */
  private buildTreeRecursive(node: PreferenceNode, preferencesByPath: Record<string, any[]>, currentPath: string): void {
    // Add preferences at the current path
    const prefsAtPath = preferencesByPath[currentPath] || [];
    for (const pref of prefsAtPath) {
      // Skip if this is a path node, not a leaf
      if (pref.isPathNode) continue;

      // Add the preference value
      node.values.push({
        value: pref.value,
        confidence: pref.confidence || 1,
        source: pref.source || PreferenceSource.EXPLICIT,
        timestamp: pref.updatedAt || new Date(),
        expiresAt: pref.expiresAt,
        metadata: pref.metadata,
      });
    }

    // Find all child paths
    const childPaths = Object.keys(preferencesByPath)
      .filter(path => path.startsWith(currentPath ? `${currentPath}.` : '') && path !== currentPath)
      .map(path => {
        const suffix = currentPath ? path.slice(currentPath.length + 1) : path;
        return suffix.split('.')[0];
      })
      .filter((value, index, self) => self.indexOf(value) === index); // Unique values

    // Create child nodes
    for (const childKey of childPaths) {
      const childPath = currentPath ? `${currentPath}.${childKey}` : childKey;
      const childNode: PreferenceNode = {
        id: uuidv4(),
        path: childPath.split('.'),
        key: childKey,
        values: [],
        children: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Recursively build the child node
      this.buildTreeRecursive(childNode, preferencesByPath, childPath);

      // Add the child node
      node.children.push(childNode);
    }
  }

  /**
   * Get a specific preference value
   * @param userId User ID
   * @param path Path to the preference
   * @param options Query options
   * @returns Preference value or null if not found
   */
  async getPreference(
    userId: string,
    path: string[],
    options?: PreferenceQueryOptions,
  ): Promise<PreferenceValue | null> {
    try {
      // Get the full preference tree
      const tree = await this.getPreferenceTree(userId);

      // Find the node at the specified path
      const node = this.findNodeAtPath(tree.root, path);
      if (!node) {
        return null;
      }

      // Get the effective value based on options
      return this.getEffectiveValue(node, options);
    } catch (error) {
      this.logger.error(`Error getting preference for user ${userId} at path ${path.join('.')}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get multiple preferences at once
   * @param userId User ID
   * @param paths Paths to the preferences
   * @param options Query options
   * @returns Map of paths to preference values
   */
  async getPreferences(
    userId: string,
    paths: string[][],
    options?: PreferenceQueryOptions,
  ): Promise<Map<string, PreferenceValue | null>> {
    try {
      // Get the full preference tree
      const tree = await this.getPreferenceTree(userId);

      // Get each preference
      const result = new Map<string, PreferenceValue | null>();
      for (const path of paths) {
        const node = this.findNodeAtPath(tree.root, path);
        const value = node ? this.getEffectiveValue(node, options) : null;
        result.set(path.join('.'), value);
      }

      return result;
    } catch (error) {
      this.logger.error(`Error getting multiple preferences for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Set a preference value
   * @param userId User ID
   * @param path Path to the preference
   * @param value Preference value
   * @returns Updated preference tree
   */
  async setPreference(
    userId: string,
    path: string[],
    value: PreferenceValue,
  ): Promise<PreferenceTree> {
    try {
      // Get the current tree
      const tree = await this.getPreferenceTree(userId);

      // Create the update operation
      const operation: PreferenceUpdateOperation = {
        type: 'update',
        path,
        value,
      };

      // Apply the update
      return this.updatePreferenceTree(userId, tree, [operation]);
    } catch (error) {
      this.logger.error(`Error setting preference for user ${userId} at path ${path.join('.')}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update multiple preferences at once
   * @param userId User ID
   * @param operations Update operations
   * @returns Updated preference tree
   */
  async updatePreferences(
    userId: string,
    operations: PreferenceUpdateOperation[],
  ): Promise<PreferenceTree> {
    try {
      // Get the current tree
      const tree = await this.getPreferenceTree(userId);

      // Apply the updates
      return this.updatePreferenceTree(userId, tree, operations);
    } catch (error) {
      this.logger.error(`Error updating preferences for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Remove a preference
   * @param userId User ID
   * @param path Path to the preference
   * @returns Updated preference tree
   */
  async removePreference(
    userId: string,
    path: string[],
  ): Promise<PreferenceTree> {
    try {
      // Get the current tree
      const tree = await this.getPreferenceTree(userId);

      // Create the update operation
      const operation: PreferenceUpdateOperation = {
        type: 'remove',
        path,
      };

      // Apply the update
      return this.updatePreferenceTree(userId, tree, [operation]);
    } catch (error) {
      this.logger.error(`Error removing preference for user ${userId} at path ${path.join('.')}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Find a node at the specified path
   * @param root Root node
   * @param path Path to find
   * @returns Node at the path or null if not found
   */
  private findNodeAtPath(root: PreferenceNode, path: string[]): PreferenceNode | null {
    if (path.length === 0) {
      return root;
    }

    let currentNode = root;
    for (let i = 0; i < path.length; i++) {
      const segment = path[i];
      const childNode = currentNode.children.find(child => child.key === segment);
      if (!childNode) {
        return null;
      }
      currentNode = childNode;
    }

    return currentNode;
  }

  /**
   * Get the effective value for a node based on query options
   * @param node Preference node
   * @param options Query options
   * @returns Effective preference value or null if none found
   */
  private getEffectiveValue(node: PreferenceNode, options?: PreferenceQueryOptions): PreferenceValue | null {
    if (!node.values || node.values.length === 0) {
      return null;
    }

    // Filter values based on options
    let validValues = [...node.values];

    // Filter by source
    if (options?.includeSources) {
      validValues = validValues.filter(v => options.includeSources.includes(v.source));
    }
    if (options?.excludeSources) {
      validValues = validValues.filter(v => !options.excludeSources.includes(v.source));
    }

    // Filter by confidence
    if (options?.minConfidence !== undefined) {
      validValues = validValues.filter(v => v.confidence >= options.minConfidence);
    }

    // Filter by expiration
    if (!options?.includeExpired) {
      const now = new Date();
      validValues = validValues.filter(v => !v.expiresAt || v.expiresAt > now);
    }

    if (validValues.length === 0) {
      return null;
    }

    // Resolve conflicts based on strategy
    const strategy = options?.resolveConflicts || this.defaultConflictStrategy;
    return this.resolveConflict(validValues, strategy);
  }

  /**
   * Resolve conflicts between multiple preference values
   * @param values Preference values
   * @param strategy Conflict resolution strategy
   * @returns Resolved preference value
   */
  private resolveConflict(values: PreferenceValue[], strategy: ConflictResolutionStrategy): PreferenceValue {
    if (values.length === 1) {
      return values[0];
    }

    switch (strategy) {
      case ConflictResolutionStrategy.HIGHEST_CONFIDENCE:
        return values.reduce((prev, current) =>
          current.confidence > prev.confidence ? current : prev
        );

      case ConflictResolutionStrategy.MOST_RECENT:
        return values.reduce((prev, current) =>
          current.timestamp > prev.timestamp ? current : prev
        );

      case ConflictResolutionStrategy.EXPLICIT_OVER_IMPLICIT:
        const explicitValues = values.filter(v => v.source === PreferenceSource.EXPLICIT);
        if (explicitValues.length > 0) {
          return this.resolveConflict(explicitValues, ConflictResolutionStrategy.HIGHEST_CONFIDENCE);
        }
        return this.resolveConflict(values, ConflictResolutionStrategy.HIGHEST_CONFIDENCE);

      case ConflictResolutionStrategy.COMBINE:
        // For numeric values, calculate weighted average based on confidence
        if (typeof values[0].value === 'number') {
          const totalConfidence = values.reduce((sum, v) => sum + v.confidence, 0);
          const weightedSum = values.reduce((sum, v) =>
            sum + (v.value as number) * v.confidence, 0
          );
          const averageValue = weightedSum / totalConfidence;

          return {
            value: averageValue,
            confidence: Math.min(1, totalConfidence / values.length),
            source: PreferenceSource.IMPLICIT,
            timestamp: new Date(),
            metadata: { combinedFrom: values.length },
          };
        }

        // For non-numeric values, fall back to highest confidence
        return this.resolveConflict(values, ConflictResolutionStrategy.HIGHEST_CONFIDENCE);

      default:
        return this.resolveConflict(values, ConflictResolutionStrategy.HIGHEST_CONFIDENCE);
    }
  }

  /**
   * Update a preference tree with multiple operations
   * @param userId User ID
   * @param tree Current preference tree
   * @param operations Update operations
   * @returns Updated preference tree
   */
  private async updatePreferenceTree(
    userId: string,
    tree: PreferenceTree,
    operations: PreferenceUpdateOperation[],
  ): Promise<PreferenceTree> {
    // Create a deep copy of the tree to avoid modifying the original
    const updatedTree: PreferenceTree = JSON.parse(JSON.stringify(tree));
    updatedTree.version += 1;
    updatedTree.lastFullUpdate = new Date();

    // Apply each operation
    for (const operation of operations) {
      switch (operation.type) {
        case 'add':
          this.addPreferenceNode(updatedTree.root, operation);
          break;
        case 'update':
          this.updatePreferenceNode(updatedTree.root, operation);
          break;
        case 'remove':
          this.removePreferenceNode(updatedTree.root, operation);
          break;
      }
    }

    // Save the updated tree to the database
    await this.savePreferenceTree(userId, updatedTree, operations);

    // Update the cache
    if (this.cacheEnabled) {
      this.preferenceCache.set(userId, {
        tree: updatedTree,
        expiresAt: Date.now() + this.cacheExpirationMs,
      });
    }

    // Emit events for each operation
    for (const operation of operations) {
      this.emitPreferenceEvent(userId, operation);
    }

    return updatedTree;
  }

  /**
   * Add a preference node
   * @param root Root node
   * @param operation Add operation
   */
  private addPreferenceNode(root: PreferenceNode, operation: PreferenceUpdateOperation): void {
    if (!operation.key || !operation.value) {
      throw new Error('Key and value are required for add operations');
    }

    const path = operation.path;
    const key = operation.key;
    const value = operation.value;

    // Find or create the parent node
    let parentNode = root;
    let currentPath: string[] = [];

    for (const segment of path) {
      currentPath.push(segment);
      let childNode = parentNode.children.find(child => child.key === segment);

      if (!childNode) {
        // Create the node if it doesn't exist
        childNode = {
          id: uuidv4(),
          path: [...currentPath],
          key: segment,
          values: [],
          children: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        parentNode.children.push(childNode);
      }

      parentNode = childNode;
    }

    // Add the new node as a child of the parent
    const newNode: PreferenceNode = {
      id: uuidv4(),
      path: [...path, key],
      key,
      values: [value],
      children: [],
      metadata: operation.metadata,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    parentNode.children.push(newNode);
  }

  /**
   * Update a preference node
   * @param root Root node
   * @param operation Update operation
   */
  private updatePreferenceNode(root: PreferenceNode, operation: PreferenceUpdateOperation): void {
    if (!operation.value) {
      throw new Error('Value is required for update operations');
    }

    const node = this.findNodeAtPath(root, operation.path);
    if (!node) {
      // If the node doesn't exist, create it
      this.addPreferenceNode(root, {
        ...operation,
        type: 'add',
        key: operation.path[operation.path.length - 1],
        path: operation.path.slice(0, -1),
      });
      return;
    }

    // Update the node's values
    const value = operation.value;
    const existingValueIndex = node.values.findIndex(v =>
      v.source === value.source &&
      typeof v.value === typeof value.value
    );

    if (existingValueIndex >= 0) {
      // Update existing value
      node.values[existingValueIndex] = value;
    } else {
      // Add new value
      node.values.push(value);
    }

    // Update metadata if provided
    if (operation.metadata) {
      node.metadata = {
        ...node.metadata,
        ...operation.metadata,
      };
    }

    node.updatedAt = new Date();
  }

  /**
   * Remove a preference node
   * @param root Root node
   * @param operation Remove operation
   */
  private removePreferenceNode(root: PreferenceNode, operation: PreferenceUpdateOperation): void {
    const path = operation.path;

    if (path.length === 0) {
      // Can't remove the root node
      throw new Error('Cannot remove the root node');
    }

    // Find the parent node
    const parentPath = path.slice(0, -1);
    const parentNode = this.findNodeAtPath(root, parentPath);

    if (!parentNode) {
      // Parent node doesn't exist, nothing to remove
      return;
    }

    // Find the node to remove
    const nodeKey = path[path.length - 1];
    const nodeIndex = parentNode.children.findIndex(child => child.key === nodeKey);

    if (nodeIndex >= 0) {
      // Remove the node
      parentNode.children.splice(nodeIndex, 1);
      parentNode.updatedAt = new Date();
    }
  }

  /**
   * Save a preference tree to the database
   * @param userId User ID
   * @param tree Preference tree
   * @param operations Operations that were applied
   */
  private async savePreferenceTree(
    userId: string,
    tree: PreferenceTree,
    operations: PreferenceUpdateOperation[],
  ): Promise<void> {
    try {
      // In a real implementation, this would save the tree to the database
      // For now, we'll just log the operations
      this.logger.log(`Saving preference tree for user ${userId}, version ${tree.version}`);

      // Create a snapshot of the tree
      const snapshot: PreferenceSnapshot = {
        id: uuidv4(),
        userId,
        tree,
        createdAt: new Date(),
        reason: `Applied ${operations.length} operations`,
      };

      // In a real implementation, we would save the snapshot to the database
      this.logger.debug(`Created preference snapshot ${snapshot.id}`);

      // In a real implementation, we would also update the flat preferences in the database
      // This would involve converting the tree back to flat preferences

      // For now, we'll just simulate the database operation
      await new Promise(resolve => setTimeout(resolve, 10));
    } catch (error) {
      this.logger.error(`Error saving preference tree for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Emit a preference event
   * @param userId User ID
   * @param operation Operation that was applied
   */
  private emitPreferenceEvent(userId: string, operation: PreferenceUpdateOperation): void {
    try {
      const eventType = operation.type === 'add' ? 'created' :
                        operation.type === 'update' ? 'updated' : 'removed';

      const event: PreferenceEvent = {
        id: uuidv4(),
        userId,
        type: eventType,
        path: operation.path,
        key: operation.key || operation.path[operation.path.length - 1],
        newValue: operation.value,
        source: operation.value?.source || PreferenceSource.EXPLICIT,
        timestamp: new Date(),
        metadata: operation.metadata,
      };

      this.eventEmitter.emit(`preference.${eventType}`, event);
      this.logger.debug(`Emitted preference.${eventType} event for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error emitting preference event for user ${userId}: ${error.message}`);
    }
  }

  /**
   * Create a snapshot of a user's preferences
   * @param userId User ID
   * @param reason Reason for creating the snapshot
   * @returns Preference snapshot
   */
  async createPreferenceSnapshot(userId: string, reason: string): Promise<PreferenceSnapshot> {
    try {
      const tree = await this.getPreferenceTree(userId);

      const snapshot: PreferenceSnapshot = {
        id: uuidv4(),
        userId,
        tree,
        createdAt: new Date(),
        reason,
      };

      // In a real implementation, we would save the snapshot to the database
      this.logger.log(`Created preference snapshot ${snapshot.id} for user ${userId}: ${reason}`);

      return snapshot;
    } catch (error) {
      this.logger.error(`Error creating preference snapshot for user ${userId}: ${error.message}`);
      throw error;
    }
  }
}