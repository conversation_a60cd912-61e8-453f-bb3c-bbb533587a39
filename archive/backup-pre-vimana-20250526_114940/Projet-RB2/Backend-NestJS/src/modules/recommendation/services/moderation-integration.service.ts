import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { v4 as uuidv4 } from 'uuid';
import { AnalyticsIntegrationService } from './analytics-integration.service';

/**
 * Service d'intégration avec le service de modération
 * Permet de vérifier que les recommandations respectent les règles de modération
 */
@Injectable()
export class ModerationIntegrationService {
  private readonly logger = new Logger(ModerationIntegrationService.name);
  private readonly moderationServiceUrl: string;
  private readonly apiKey: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly analyticsService: AnalyticsIntegrationService,
  ) {
    this.moderationServiceUrl = this.configService.get<string>('MODERATION_SERVICE_URL', 'http://localhost:3002');
    this.apiKey = this.configService.get<string>('MODERATION_SERVICE_API_KEY', 'default-api-key');
  }

  /**
   * Filtre les recommandations selon les règles de modération
   * @param recommendations Liste des recommandations à filtrer
   * @param type Type de recommandation
   * @returns Liste des recommandations filtrées
   */
  async filterRecommendations(recommendations: any[], type: RecommendationType): Promise<any[]> {
    try {
      this.logger.log(`Filtrage de ${recommendations.length} recommandations de type ${type}`);

      // Si le service de modération n'est pas disponible, on retourne les recommandations telles quelles
      if (!this.isModerationServiceAvailable()) {
        this.logger.warn('Service de modération non disponible, aucun filtrage appliqué');
        return recommendations;
      }

      // Récupérer les IDs des recommandations
      const recommendationIds = recommendations.map(rec => rec.id);

      // Vérifier le statut de modération des recommandations
      const moderationStatuses = await this.getModerationStatuses(recommendationIds, type);

      // Filtrer les recommandations selon leur statut de modération
      const filteredRecommendations = recommendations.filter(rec => {
        const status = moderationStatuses[rec.id];
        return status === 'APPROVED' || status === 'PENDING' || !status;
      });

      this.logger.log(`${filteredRecommendations.length} recommandations après filtrage de modération`);
      return filteredRecommendations;
    } catch (error) {
      this.logger.error(`Erreur lors du filtrage des recommandations: ${error.message}`);
      // En cas d'erreur, on retourne les recommandations originales
      return recommendations;
    }
  }

  /**
   * Vérifie si une recommandation spécifique respecte les règles de modération
   * @param recommendationId ID de la recommandation à vérifier
   * @param type Type de recommandation
   * @returns true si la recommandation est approuvée ou en attente, false sinon
   */
  async isRecommendationAllowed(recommendationId: string, type: RecommendationType): Promise<boolean> {
    try {
      // Si le service de modération n'est pas disponible, on considère la recommandation comme autorisée
      if (!this.isModerationServiceAvailable()) {
        return true;
      }

      // Vérifier le statut de modération de la recommandation
      const moderationStatuses = await this.getModerationStatuses([recommendationId], type);
      const status = moderationStatuses[recommendationId];

      return status === 'APPROVED' || status === 'PENDING' || !status;
    } catch (error) {
      this.logger.error(`Erreur lors de la vérification de la recommandation ${recommendationId}: ${error.message}`);
      // En cas d'erreur, on considère la recommandation comme autorisée
      return true;
    }
  }

  /**
   * Enregistre un feedback de modération pour une recommandation
   * @param recommendationId ID de la recommandation
   * @param type Type de recommandation
   * @param userId ID de l'utilisateur qui a signalé la recommandation
   * @param reason Raison du signalement
   * @returns Résultat de l'opération
   */
  async reportRecommendation(
    recommendationId: string,
    type: RecommendationType,
    userId: string,
    reason: string,
  ): Promise<any> {
    try {
      this.logger.log(`Signalement de la recommandation ${recommendationId} par l'utilisateur ${userId}`);

      // Si le service de modération n'est pas disponible, on enregistre le signalement en local
      if (!this.isModerationServiceAvailable()) {
        return this.storeReportLocally(recommendationId, type, userId, reason);
      }

      // Envoyer le signalement au service de modération
      const { data } = await firstValueFrom(
        this.httpService.post(
          `${this.moderationServiceUrl}/api/moderation/report`,
          {
            contentId: recommendationId,
            contentType: this.mapRecommendationTypeToContentType(type),
            userId,
            reason,
          },
          {
            headers: {
              'Authorization': `Bearer ${this.apiKey}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      // Suivre l'événement dans le service d'analytics
      try {
        await this.analyticsService.trackRecommendationFeedback(
          userId,
          recommendationId,
          type,
          'REPORT',
          {
            reason,
            moderationResponse: data,
          },
        );
      } catch (analyticsError) {
        // Ne pas bloquer le processus si l'enregistrement dans analytics échoue
        this.logger.warn(`Erreur lors de l'enregistrement du signalement dans analytics: ${analyticsError.message}`);
      }

      return data;
    } catch (error) {
      this.logger.error(`Erreur lors du signalement de la recommandation: ${error.message}`);

      // En cas d'erreur avec le service de modération, on enregistre le signalement en local
      return this.storeReportLocally(recommendationId, type, userId, reason);
    }
  }

  /**
   * Vérifie si le service de modération est disponible
   * @returns true si le service est disponible, false sinon
   */
  private isModerationServiceAvailable(): boolean {
    // Cette méthode pourrait être améliorée pour vérifier réellement la disponibilité du service
    // Pour l'instant, on vérifie simplement si l'URL et l'API key sont définies
    return !!this.moderationServiceUrl && !!this.apiKey && this.moderationServiceUrl !== 'http://localhost:3002';
  }

  /**
   * Récupère les statuts de modération pour une liste de recommandations
   * @param recommendationIds Liste des IDs de recommandations
   * @param type Type de recommandation
   * @returns Mapping des IDs de recommandations vers leur statut de modération
   */
  private async getModerationStatuses(
    recommendationIds: string[],
    type: RecommendationType,
  ): Promise<Record<string, string>> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.post(
          `${this.moderationServiceUrl}/api/moderation/status`,
          {
            contentIds: recommendationIds,
            contentType: this.mapRecommendationTypeToContentType(type),
          },
          {
            headers: {
              'Authorization': `Bearer ${this.apiKey}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      return data.statuses || {};
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des statuts de modération: ${error.message}`);
      return {};
    }
  }

  // Stockage temporaire des signalements en mémoire
  private localReports: Array<{
    id: string;
    userId: string;
    content: string;
    contentType: string;
    isApproved: boolean;
    violations: string[];
    score: Record<string, any>;
    timestamp: Date;
  }> = [];

  /**
   * Enregistre un signalement en local (fallback si le service de modération n'est pas disponible)
   * @param recommendationId ID de la recommandation
   * @param type Type de recommandation
   * @param userId ID de l'utilisateur qui a signalé la recommandation
   * @param reason Raison du signalement
   * @returns Résultat de l'opération
   */
  private async storeReportLocally(
    recommendationId: string,
    type: RecommendationType,
    userId: string,
    reason: string,
  ): Promise<any> {
    try {
      // Créer un ID unique pour le signalement
      const reportId = uuidv4();

      // Enregistrer le signalement en mémoire
      const report = {
        id: reportId,
        userId,
        content: recommendationId,
        contentType: this.mapRecommendationTypeToContentType(type),
        isApproved: false, // Par défaut, on considère le contenu comme non approuvé
        violations: [reason],
        score: {}, // Score vide par défaut
        timestamp: new Date(),
      };

      this.localReports.push(report);

      // Enregistrer également dans les logs pour persistance temporaire
      this.logger.debug(`Signalement enregistré localement: ${JSON.stringify(report)}`);

      // Suivre l'événement dans le service d'analytics
      try {
        await this.analyticsService.trackRecommendationFeedback(
          userId,
          recommendationId,
          type,
          'REPORT',
          {
            reason,
            localReport: true,
          },
        );
      } catch (analyticsError) {
        // Ne pas bloquer le processus si l'enregistrement dans analytics échoue
        this.logger.warn(`Erreur lors de l'enregistrement du signalement local dans analytics: ${analyticsError.message}`);
      }

      return {
        success: true,
        message: 'Signalement enregistré localement',
        data: report,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement local du signalement: ${error.message}`);
      throw new HttpException('Erreur lors du signalement de la recommandation', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Convertit un type de recommandation en type de contenu pour le service de modération
   * @param type Type de recommandation
   * @returns Type de contenu correspondant
   */
  private mapRecommendationTypeToContentType(type: RecommendationType): string {
    switch (type) {
      case RecommendationType.RETREAT:
        return 'RETREAT';
      case RecommendationType.COURSE:
        return 'COURSE';
      case RecommendationType.VIDEO:
        return 'VIDEO';
      case RecommendationType.ARTICLE:
        return 'ARTICLE';
      case RecommendationType.EVENT:
        return 'EVENT';
      default:
        return 'UNKNOWN';
    }
  }
}
