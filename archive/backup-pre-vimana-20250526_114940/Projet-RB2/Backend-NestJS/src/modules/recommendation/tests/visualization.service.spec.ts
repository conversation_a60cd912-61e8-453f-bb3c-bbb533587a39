import { Test, TestingModule } from '@nestjs/testing';
import { VisualizationService } from '../services/visualization.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';

// Mock pour PrismaService
const mockPrismaService = {
  userSimilarity: {
    findMany: jest.fn(),
  },
  user: {
    findMany: jest.fn(),
  },
  recommendation: {
    findMany: jest.fn(),
    groupBy: jest.fn(),
  },
  userInteraction: {
    findMany: jest.fn(),
    groupBy: jest.fn(),
  },
  course: {
    findMany: jest.fn(),
  },
  retreat: {
    findMany: jest.fn(),
  },
  partner: {
    findMany: jest.fn(),
  },
};

describe('VisualizationService', () => {
  let service: VisualizationService;
  let prismaService: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VisualizationService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<VisualizationService>(VisualizationService);
    prismaService = module.get<PrismaService>(PrismaService);

    // Réinitialiser les mocks avant chaque test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateUserSimilarityGraph', () => {
    it('should generate a user similarity graph', async () => {
      // Configurer les mocks
      mockPrismaService.userSimilarity.findMany.mockResolvedValue([
        { userId: 'user1', similarUserId: 'user2', similarity: 0.8 },
        { userId: 'user1', similarUserId: 'user3', similarity: 0.7 },
        { userId: 'user2', similarUserId: 'user3', similarity: 0.6 },
      ]);

      mockPrismaService.user.findMany.mockResolvedValue([
        { 
          id: 'user1', 
          email: '<EMAIL>',
          profile: { firstName: 'John', lastName: 'Doe', avatarUrl: 'avatar1.jpg' }
        },
        { 
          id: 'user2', 
          email: '<EMAIL>',
          profile: { firstName: 'Jane', lastName: 'Smith', avatarUrl: 'avatar2.jpg' }
        },
        { 
          id: 'user3', 
          email: '<EMAIL>',
          profile: { firstName: 'Bob', lastName: 'Johnson', avatarUrl: 'avatar3.jpg' }
        },
      ]);

      // Appeler la méthode à tester
      const result = await service.generateUserSimilarityGraph(3, 0.5);

      // Vérifier les résultats
      expect(result).toBeDefined();
      expect(result.nodes).toHaveLength(3);
      expect(result.links).toHaveLength(3);

      // Vérifier les nœuds
      expect(result.nodes[0].id).toBe('user1');
      expect(result.nodes[0].label).toBe('John Doe');
      expect(result.nodes[0].avatar).toBe('avatar1.jpg');

      expect(result.nodes[1].id).toBe('user2');
      expect(result.nodes[1].label).toBe('Jane Smith');
      expect(result.nodes[1].avatar).toBe('avatar2.jpg');

      expect(result.nodes[2].id).toBe('user3');
      expect(result.nodes[2].label).toBe('Bob Johnson');
      expect(result.nodes[2].avatar).toBe('avatar3.jpg');

      // Vérifier les liens
      expect(result.links[0].source).toBe('user1');
      expect(result.links[0].target).toBe('user2');
      expect(result.links[0].value).toBe(0.8);

      expect(result.links[1].source).toBe('user1');
      expect(result.links[1].target).toBe('user3');
      expect(result.links[1].value).toBe(0.7);

      expect(result.links[2].source).toBe('user2');
      expect(result.links[2].target).toBe('user3');
      expect(result.links[2].value).toBe(0.6);

      // Vérifier que les méthodes du PrismaService ont été appelées avec les bons paramètres
      expect(mockPrismaService.userSimilarity.findMany).toHaveBeenCalledWith({
        where: {
          similarity: {
            gte: 0.5,
          },
        },
        orderBy: {
          similarity: 'desc',
        },
        take: 15, // 3 * 5
      });
    });
  });

  describe('generateUserRecommendationGraph', () => {
    it('should generate a user recommendation graph', async () => {
      const userId = 'user-123';
      
      // Configurer les mocks
      mockPrismaService.recommendation.findMany.mockResolvedValue([
        { 
          id: 'rec1', 
          userId, 
          itemId: 'course1', 
          type: RecommendationType.COURSE,
          score: 0.9,
          createdAt: new Date('2023-01-01')
        },
        { 
          id: 'rec2', 
          userId, 
          itemId: 'retreat1', 
          type: RecommendationType.RETREAT,
          score: 0.8,
          createdAt: new Date('2023-01-02')
        },
      ]);

      mockPrismaService.userInteraction.findMany.mockResolvedValue([
        { 
          userId, 
          itemId: 'course1', 
          itemType: RecommendationType.COURSE,
          interactionType: 'VIEW',
          createdAt: new Date('2023-01-03')
        },
        { 
          userId, 
          itemId: 'course1', 
          itemType: RecommendationType.COURSE,
          interactionType: 'LIKE',
          createdAt: new Date('2023-01-04')
        },
        { 
          userId, 
          itemId: 'retreat1', 
          itemType: RecommendationType.RETREAT,
          interactionType: 'VIEW',
          createdAt: new Date('2023-01-05')
        },
      ]);

      mockPrismaService.course.findMany.mockResolvedValue([
        { 
          id: 'course1', 
          title: 'Yoga Course', 
          category: 'yoga'
        },
      ]);

      mockPrismaService.retreat.findMany.mockResolvedValue([
        { 
          id: 'retreat1', 
          title: 'Meditation Retreat', 
          metadata: { category: 'meditation' }
        },
      ]);

      // Appeler la méthode à tester
      const result = await service.generateUserRecommendationGraph(userId, 10);

      // Vérifier les résultats
      expect(result).toBeDefined();
      expect(result.nodes).toHaveLength(5); // 1 user + 2 recommendations + 2 items
      expect(result.links).toHaveLength(7); // 2 user->rec + 2 rec->item + 3 user->item (interactions)

      // Vérifier le nœud utilisateur
      const userNode = result.nodes.find(node => node.id === userId);
      expect(userNode).toBeDefined();
      expect(userNode.type).toBe('user');

      // Vérifier les nœuds de recommandation
      const recNodes = result.nodes.filter(node => node.type === 'recommendation');
      expect(recNodes).toHaveLength(2);
      expect(recNodes[0].id).toBe('rec1');
      expect(recNodes[0].itemId).toBe('course1');
      expect(recNodes[0].recommendationType).toBe(RecommendationType.COURSE);
      expect(recNodes[0].score).toBe(0.9);

      // Vérifier les nœuds d'items
      const itemNodes = result.nodes.filter(node => node.type === 'item');
      expect(itemNodes).toHaveLength(2);
      expect(itemNodes[0].id).toBe('course1');
      expect(itemNodes[0].label).toBe('Yoga Course');
      expect(itemNodes[0].itemType).toBe(RecommendationType.COURSE);
      expect(itemNodes[0].category).toBe('yoga');

      // Vérifier les liens
      const userToRecLinks = result.links.filter(link => link.source === userId && link.type === 'receives');
      expect(userToRecLinks).toHaveLength(2);

      const recToItemLinks = result.links.filter(link => link.type === 'recommends');
      expect(recToItemLinks).toHaveLength(2);
      expect(recToItemLinks[0].source).toBe('rec1');
      expect(recToItemLinks[0].target).toBe('course1');
      expect(recToItemLinks[0].score).toBe(0.9);

      const userToItemLinks = result.links.filter(link => link.source === userId && ['view', 'like'].includes(link.type));
      expect(userToItemLinks).toHaveLength(3);
      expect(userToItemLinks[0].target).toBe('course1');
      expect(userToItemLinks[0].type).toBe('view');
      expect(userToItemLinks[1].target).toBe('course1');
      expect(userToItemLinks[1].type).toBe('like');
      expect(userToItemLinks[2].target).toBe('retreat1');
      expect(userToItemLinks[2].type).toBe('view');

      // Vérifier que les méthodes du PrismaService ont été appelées avec les bons paramètres
      expect(mockPrismaService.recommendation.findMany).toHaveBeenCalledWith({
        where: {
          userId,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 10,
      });

      expect(mockPrismaService.userInteraction.findMany).toHaveBeenCalledWith({
        where: {
          userId,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 20, // 10 * 2
      });
    });
  });

  describe('generateRecommendationHeatmap', () => {
    it('should generate a recommendation heatmap', async () => {
      // Configurer les mocks
      mockPrismaService.recommendation.groupBy.mockResolvedValue([
        { 
          type: RecommendationType.COURSE, 
          metadata: { strategy: 'HYBRID' }, 
          _count: { id: 50 }
        },
        { 
          type: RecommendationType.COURSE, 
          metadata: { strategy: 'CONTENT_BASED' }, 
          _count: { id: 30 }
        },
        { 
          type: RecommendationType.RETREAT, 
          metadata: { strategy: 'HYBRID' }, 
          _count: { id: 40 }
        },
      ]);

      mockPrismaService.userInteraction.groupBy.mockResolvedValue([
        { 
          itemType: RecommendationType.COURSE, 
          interactionType: 'VIEW', 
          metadata: { recommendationStrategy: 'HYBRID' }, 
          _count: { id: 200 }
        },
        { 
          itemType: RecommendationType.COURSE, 
          interactionType: 'CLICK', 
          metadata: { recommendationStrategy: 'HYBRID' }, 
          _count: { id: 100 }
        },
        { 
          itemType: RecommendationType.RETREAT, 
          interactionType: 'VIEW', 
          metadata: { recommendationStrategy: 'HYBRID' }, 
          _count: { id: 150 }
        },
      ]);

      // Appeler la méthode à tester
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-31');
      const result = await service.generateRecommendationHeatmap(startDate, endDate);

      // Vérifier les résultats
      expect(result).toBeDefined();
      expect(result.period.start).toEqual(startDate);
      expect(result.period.end).toEqual(endDate);

      // Vérifier la carte de chaleur des recommandations
      expect(result.recommendations[RecommendationType.COURSE]['HYBRID']).toBe(50);
      expect(result.recommendations[RecommendationType.COURSE]['CONTENT_BASED']).toBe(30);
      expect(result.recommendations[RecommendationType.RETREAT]['HYBRID']).toBe(40);

      // Vérifier la carte de chaleur des interactions
      expect(result.interactions[RecommendationType.COURSE]['HYBRID']['VIEW']).toBe(200);
      expect(result.interactions[RecommendationType.COURSE]['HYBRID']['CLICK']).toBe(100);
      expect(result.interactions[RecommendationType.RETREAT]['HYBRID']['VIEW']).toBe(150);

      // Vérifier que les méthodes du PrismaService ont été appelées avec les bons paramètres
      expect(mockPrismaService.recommendation.groupBy).toHaveBeenCalledWith({
        by: ['type', 'metadata'],
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _count: {
          id: true,
        },
      });

      expect(mockPrismaService.userInteraction.groupBy).toHaveBeenCalledWith({
        by: ['itemType', 'interactionType', 'metadata'],
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _count: {
          id: true,
        },
      });
    });
  });
});
