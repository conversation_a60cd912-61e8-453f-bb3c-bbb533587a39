/**
 * Tests d'intégration pour le contrôleur de recommandation
 */

import * as request from 'supertest';
import { Test } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { RecommendationModule } from '../../recommendation.module.refactored';
import { RecommendationType } from '../../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../../enums/recommendation-strategy.enum';
import { HybridMethod } from '../../enums/hybrid-method.enum';
import { AgentRbClient } from '../../clients/agent-rb-client';
import { MockAgentRbClient } from './agent-rb-client.mock';
import { PrismaService } from '../../../../prisma/prisma.service';
import { app, prisma, testUserToken, testAdminToken } from './setup';

describe('RecommendationController (Integration)', () => {
  let httpServer: any;

  beforeAll(() => {
    httpServer = app.getHttpServer();
  });

  describe('GET /api/v1/recommendations', () => {
    it('should return personalized recommendations', async () => {
      // Act
      const response = await request(httpServer)
        .get('/api/v1/recommendations')
        .set('Authorization', `Bearer ${testUserToken}`)
        .expect(200);

      // Assert
      expect(response.body).toBeDefined();
      expect(response.body.items).toBeDefined();
      expect(Array.isArray(response.body.items)).toBe(true);
      expect(response.body.meta).toBeDefined();
      expect(response.body.meta.total).toBeDefined();
      expect(response.body.meta.page).toBeDefined();
      expect(response.body.meta.limit).toBeDefined();
      expect(response.body.meta.totalPages).toBeDefined();
    });

    it('should filter recommendations by type', async () => {
      // Act
      const response = await request(httpServer)
        .get('/api/v1/recommendations?type=RETREAT')
        .set('Authorization', `Bearer ${testUserToken}`)
        .expect(200);

      // Assert
      expect(response.body.items).toBeDefined();
      expect(Array.isArray(response.body.items)).toBe(true);
      
      // Vérifier que tous les éléments sont du type RETREAT
      response.body.items.forEach((item: any) => {
        expect(item.type).toBe('RETREAT');
      });
    });

    it('should apply pagination', async () => {
      // Act
      const response = await request(httpServer)
        .get('/api/v1/recommendations?limit=2&page=1')
        .set('Authorization', `Bearer ${testUserToken}`)
        .expect(200);

      // Assert
      expect(response.body.items).toBeDefined();
      expect(Array.isArray(response.body.items)).toBe(true);
      expect(response.body.items.length).toBeLessThanOrEqual(2);
      expect(response.body.meta.limit).toBe(2);
      expect(response.body.meta.page).toBe(1);
    });

    it('should use different recommendation strategies', async () => {
      // Act - Content-based
      const contentBasedResponse = await request(httpServer)
        .get('/api/v1/recommendations?strategy=CONTENT_BASED')
        .set('Authorization', `Bearer ${testUserToken}`)
        .expect(200);

      // Act - Collaborative
      const collaborativeResponse = await request(httpServer)
        .get('/api/v1/recommendations?strategy=COLLABORATIVE')
        .set('Authorization', `Bearer ${testUserToken}`)
        .expect(200);

      // Act - Hybrid
      const hybridResponse = await request(httpServer)
        .get('/api/v1/recommendations?strategy=HYBRID&hybridMethod=WEIGHTED')
        .set('Authorization', `Bearer ${testUserToken}`)
        .expect(200);

      // Assert
      expect(contentBasedResponse.body.items).toBeDefined();
      expect(collaborativeResponse.body.items).toBeDefined();
      expect(hybridResponse.body.items).toBeDefined();
    });

    it('should return 401 when not authenticated', async () => {
      // Act & Assert
      await request(httpServer)
        .get('/api/v1/recommendations')
        .expect(401);
    });
  });

  describe('GET /api/v1/recommendations/trending', () => {
    it('should return trending recommendations', async () => {
      // Act
      const response = await request(httpServer)
        .get('/api/v1/recommendations/trending')
        .set('Authorization', `Bearer ${testUserToken}`)
        .expect(200);

      // Assert
      expect(response.body).toBeDefined();
      expect(response.body.items).toBeDefined();
      expect(Array.isArray(response.body.items)).toBe(true);
      expect(response.body.meta).toBeDefined();
    });

    it('should filter trending recommendations by type', async () => {
      // Act
      const response = await request(httpServer)
        .get('/api/v1/recommendations/trending?type=PARTNER')
        .set('Authorization', `Bearer ${testUserToken}`)
        .expect(200);

      // Assert
      expect(response.body.items).toBeDefined();
      
      // Vérifier que tous les éléments sont du type PARTNER
      response.body.items.forEach((item: any) => {
        expect(item.type).toBe('PARTNER');
      });
    });

    it('should return 401 when not authenticated', async () => {
      // Act & Assert
      await request(httpServer)
        .get('/api/v1/recommendations/trending')
        .expect(401);
    });
  });

  describe('GET /api/v1/recommendations/similar/:type/:itemId', () => {
    it('should return similar items', async () => {
      // Act
      const response = await request(httpServer)
        .get('/api/v1/recommendations/similar/RETREAT/retreat-1')
        .set('Authorization', `Bearer ${testUserToken}`)
        .expect(200);

      // Assert
      expect(response.body).toBeDefined();
      expect(response.body.items).toBeDefined();
      expect(Array.isArray(response.body.items)).toBe(true);
      expect(response.body.meta).toBeDefined();
    });

    it('should return 404 when item does not exist', async () => {
      // Act & Assert
      await request(httpServer)
        .get('/api/v1/recommendations/similar/RETREAT/nonexistent')
        .set('Authorization', `Bearer ${testUserToken}`)
        .expect(404);
    });

    it('should return 401 when not authenticated', async () => {
      // Act & Assert
      await request(httpServer)
        .get('/api/v1/recommendations/similar/RETREAT/retreat-1')
        .expect(401);
    });
  });

  describe('POST /api/v1/recommendations/interactions', () => {
    it('should record an interaction', async () => {
      // Arrange
      const interactionData = {
        itemId: 'retreat-1',
        type: RecommendationType.RETREAT,
        interactionType: 'VIEW',
        metadata: {
          duration: 120,
        },
      };

      // Act
      const response = await request(httpServer)
        .post('/api/v1/recommendations/interactions')
        .set('Authorization', `Bearer ${testUserToken}`)
        .send(interactionData)
        .expect(201);

      // Assert
      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
    });

    it('should return 400 when interaction data is invalid', async () => {
      // Arrange
      const invalidInteractionData = {
        // Missing required fields
        metadata: {
          duration: 120,
        },
      };

      // Act & Assert
      await request(httpServer)
        .post('/api/v1/recommendations/interactions')
        .set('Authorization', `Bearer ${testUserToken}`)
        .send(invalidInteractionData)
        .expect(400);
    });

    it('should return 401 when not authenticated', async () => {
      // Arrange
      const interactionData = {
        itemId: 'retreat-1',
        type: RecommendationType.RETREAT,
        interactionType: 'VIEW',
      };

      // Act & Assert
      await request(httpServer)
        .post('/api/v1/recommendations/interactions')
        .send(interactionData)
        .expect(401);
    });
  });

  describe('POST /api/v1/recommendations/preferences', () => {
    it('should update preferences', async () => {
      // Arrange
      const preferencesData = {
        preferredCategories: ['Yoga', 'Meditation'],
        preferredLevels: ['BEGINNER', 'INTERMEDIATE'],
        preferredLocations: ['France', 'Italy'],
      };

      // Act
      const response = await request(httpServer)
        .post('/api/v1/recommendations/preferences')
        .set('Authorization', `Bearer ${testUserToken}`)
        .send(preferencesData)
        .expect(200);

      // Assert
      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.preferences).toBeDefined();
      expect(response.body.preferences.preferredCategories).toEqual(preferencesData.preferredCategories);
      expect(response.body.preferences.preferredLevels).toEqual(preferencesData.preferredLevels);
      expect(response.body.preferences.preferredLocations).toEqual(preferencesData.preferredLocations);
    });

    it('should return 401 when not authenticated', async () => {
      // Arrange
      const preferencesData = {
        preferredCategories: ['Yoga', 'Meditation'],
      };

      // Act & Assert
      await request(httpServer)
        .post('/api/v1/recommendations/preferences')
        .send(preferencesData)
        .expect(401);
    });
  });
});
