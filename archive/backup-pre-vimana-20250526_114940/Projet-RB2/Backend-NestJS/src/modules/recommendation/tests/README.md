# Tests du Système de Recommandation

Ce dossier contient les tests pour le système de recommandation, y compris les tests unitaires et d'intégration.

## Structure des Tests

```
tests/
├── integration/           # Tests d'intégration
│   ├── jest.config.js     # Configuration Jest pour les tests d'intégration
│   ├── setup.ts           # Configuration pour les tests d'intégration
│   ├── mocks.ts           # Données mockées pour les tests
│   ├── agent-rb-client.mock.ts  # Mock du client Agent-RB
│   ├── recommendation.controller.integration.spec.ts  # Tests d'intégration pour le contrôleur
│   └── agent-rb-integration.service.integration.spec.ts  # Tests d'intégration pour le service d'intégration
└── unit/                  # Tests unitaires
    ├── controllers/       # Tests unitaires pour les contrôleurs
    ├── services/          # Tests unitaires pour les services
    └── clients/           # Tests unitaires pour les clients
```

## Tests d'Intégration

Les tests d'intégration vérifient que les différents composants du système de recommandation fonctionnent correctement ensemble et avec les systèmes externes comme Agent-RB.

### Prérequis

- Node.js 14+
- npm ou yarn
- Base de données PostgreSQL (pour les tests qui utilisent Prisma)

### Configuration

1. Créez un fichier `.env.test` à la racine du projet avec les variables d'environnement suivantes :

```
JWT_SECRET=test-secret
AGENT_RB_SERVICE_URL=http://localhost:5000
AGENT_RB_TIMEOUT_MS=5000
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/retreatandbe_test
```

2. Assurez-vous que la base de données de test existe et est accessible.

### Exécution des Tests d'Intégration

Pour exécuter tous les tests d'intégration du système de recommandation :

```bash
./scripts/run-recommendation-integration-tests.sh
```

Pour exécuter un test spécifique :

```bash
npx jest src/modules/recommendation/tests/integration/recommendation.controller.integration.spec.ts --config=src/modules/recommendation/tests/integration/jest.config.js
```

### Mocks

Les tests d'intégration utilisent des mocks pour simuler les dépendances externes comme Agent-RB. Les données mockées sont définies dans `mocks.ts` et le client Agent-RB mocké est défini dans `agent-rb-client.mock.ts`.

## Tests d'Intégration Agent-RB

Les tests d'intégration pour l'intégration entre Agent-RB et le système de recommandation se trouvent dans le dossier `Agent-RB/tests/integration/`.

### Exécution des Tests d'Intégration Agent-RB

Pour exécuter les tests d'intégration d'Agent-RB :

```bash
cd Agent-RB
./scripts/run-recommendation-integration-tests.sh
```

## Couverture de Code

Les tests génèrent des rapports de couverture de code qui sont disponibles dans le dossier `coverage/`. Pour visualiser le rapport de couverture, ouvrez `coverage/lcov-report/index.html` dans un navigateur.

## Bonnes Pratiques

### Tests d'Intégration

1. **Isolation** : Chaque test doit être indépendant des autres tests.
2. **Données de Test** : Utilisez des données de test spécifiques et évitez de dépendre de données existantes.
3. **Nettoyage** : Nettoyez les données de test après chaque test.
4. **Mocks** : Utilisez des mocks pour les dépendances externes.
5. **Assertions** : Vérifiez non seulement les résultats, mais aussi les effets secondaires (appels de méthodes, etc.).

### Tests Unitaires

1. **Isolation** : Testez une seule unité à la fois.
2. **Mocks** : Utilisez des mocks pour toutes les dépendances.
3. **Couverture** : Visez une couverture de code élevée (> 80%).
4. **Cas de Test** : Testez les cas normaux, les cas limites et les cas d'erreur.
5. **Lisibilité** : Utilisez des noms de test descriptifs et des commentaires si nécessaire.

## Dépannage

### Les Tests Échouent avec une Erreur de Connexion à la Base de Données

1. Vérifiez que la base de données de test existe et est accessible.
2. Vérifiez que les informations de connexion dans `.env.test` sont correctes.
3. Vérifiez que le schéma de la base de données est à jour avec `npx prisma db push --schema=./prisma/schema.prisma --preview-feature`.

### Les Tests Échouent avec une Erreur de Timeout

1. Augmentez la valeur de `testTimeout` dans `jest.config.js`.
2. Vérifiez que les services externes sont accessibles et répondent rapidement.

### Les Tests Échouent avec une Erreur d'Authentification

1. Vérifiez que `JWT_SECRET` est défini dans `.env.test`.
2. Vérifiez que les tokens JWT sont générés correctement dans `setup.ts`.

## Ressources

- [Documentation Jest](https://jestjs.io/docs/getting-started)
- [Documentation NestJS sur les Tests](https://docs.nestjs.com/fundamentals/testing)
- [Documentation Prisma sur les Tests](https://www.prisma.io/docs/guides/testing)
