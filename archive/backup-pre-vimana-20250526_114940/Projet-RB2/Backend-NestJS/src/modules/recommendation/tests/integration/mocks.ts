/**
 * Mocks pour les tests d'intégration du système de recommandation
 */

import { RecommendationType } from '../../enums/recommendation-type.enum';

/**
 * Données de retraites mockées
 */
export const mockRetreats = [
  {
    id: 'retreat-1',
    title: 'Retraite de Yoga dans les Alpes',
    description: 'Une semaine de détente et de reconnexion avec la nature dans les magnifiques Alpes françaises.',
    category: 'Yoga',
    level: 'INTERMEDIATE',
    location: 'Chamonix, France',
    duration: 7,
    tags: ['yoga', 'meditation', 'mountains'],
    price: 1200,
    currency: 'EUR',
    startDate: '2023-07-15',
    endDate: '2023-07-22',
    reviewCount: 42,
    averageRating: 4.7,
    available: true,
    remainingSpots: 5,
    imageUrl: 'https://example.com/images/retreat-1.jpg',
  },
  {
    id: 'retreat-2',
    title: 'Retraite de Méditation en Toscane',
    description: 'Découvrez la paix intérieure dans un cadre idyllique au cœur de la campagne toscane.',
    category: 'Meditation',
    level: 'BEGINNER',
    location: 'Florence, Italy',
    duration: 5,
    tags: ['meditation', 'mindfulness', 'countryside'],
    price: 950,
    currency: 'EUR',
    startDate: '2023-08-10',
    endDate: '2023-08-15',
    reviewCount: 28,
    averageRating: 4.9,
    available: true,
    remainingSpots: 3,
    imageUrl: 'https://example.com/images/retreat-2.jpg',
  },
  {
    id: 'retreat-3',
    title: 'Retraite Bien-être au Portugal',
    description: 'Combinez yoga, méditation et surf sur les magnifiques plages du Portugal.',
    category: 'Wellness',
    level: 'ALL',
    location: 'Lagos, Portugal',
    duration: 10,
    tags: ['yoga', 'surf', 'beach', 'wellness'],
    price: 1500,
    currency: 'EUR',
    startDate: '2023-09-05',
    endDate: '2023-09-15',
    reviewCount: 35,
    averageRating: 4.6,
    available: true,
    remainingSpots: 8,
    imageUrl: 'https://example.com/images/retreat-3.jpg',
  },
];

/**
 * Données de partenaires mockées
 */
export const mockPartners = [
  {
    id: 'partner-1',
    name: 'Sophie Martin',
    title: 'Instructrice de Yoga',
    description: 'Instructrice de yoga certifiée avec plus de 10 ans d\'expérience dans l\'enseignement du Hatha et Vinyasa Yoga.',
    specialties: ['Hatha Yoga', 'Vinyasa Yoga', 'Yoga Thérapeutique'],
    location: 'Paris, France',
    experience_years: 10,
    reviewCount: 89,
    averageRating: 4.8,
    available: true,
    imageUrl: 'https://example.com/images/partner-1.jpg',
  },
  {
    id: 'partner-2',
    name: 'Jean Dupont',
    title: 'Coach de Méditation',
    description: 'Coach de méditation spécialisé dans la pleine conscience et la méditation guidée pour débutants et intermédiaires.',
    specialties: ['Méditation Pleine Conscience', 'Méditation Guidée', 'Respiration'],
    location: 'Lyon, France',
    experience_years: 8,
    reviewCount: 64,
    averageRating: 4.7,
    available: true,
    imageUrl: 'https://example.com/images/partner-2.jpg',
  },
  {
    id: 'partner-3',
    name: 'Maria Rossi',
    title: 'Thérapeute Holistique',
    description: 'Thérapeute holistique combinant diverses approches pour un bien-être complet: yoga, méditation, nutrition et massages.',
    specialties: ['Thérapie Holistique', 'Nutrition', 'Massages', 'Yoga'],
    location: 'Milan, Italy',
    experience_years: 15,
    reviewCount: 112,
    averageRating: 4.9,
    available: true,
    imageUrl: 'https://example.com/images/partner-3.jpg',
  },
];

/**
 * Données de cours mockées
 */
export const mockCourses = [
  {
    id: 'course-1',
    title: 'Introduction au Yoga',
    description: 'Un cours complet pour débutants couvrant les bases du yoga, de la respiration et de la méditation.',
    category: 'Yoga',
    level: 'BEGINNER',
    duration: 4, // semaines
    tags: ['yoga', 'beginner', 'breathing'],
    price: 99,
    currency: 'EUR',
    reviewCount: 156,
    averageRating: 4.6,
    available: true,
    imageUrl: 'https://example.com/images/course-1.jpg',
  },
  {
    id: 'course-2',
    title: 'Méditation Avancée',
    description: 'Techniques de méditation avancées pour approfondir votre pratique et atteindre un état de conscience supérieur.',
    category: 'Meditation',
    level: 'ADVANCED',
    duration: 6, // semaines
    tags: ['meditation', 'advanced', 'mindfulness'],
    price: 149,
    currency: 'EUR',
    reviewCount: 78,
    averageRating: 4.8,
    available: true,
    imageUrl: 'https://example.com/images/course-2.jpg',
  },
  {
    id: 'course-3',
    title: 'Nutrition et Bien-être',
    description: 'Apprenez à équilibrer votre alimentation pour améliorer votre santé physique et mentale.',
    category: 'Wellness',
    level: 'INTERMEDIATE',
    duration: 8, // semaines
    tags: ['nutrition', 'wellness', 'health'],
    price: 129,
    currency: 'EUR',
    reviewCount: 92,
    averageRating: 4.5,
    available: true,
    imageUrl: 'https://example.com/images/course-3.jpg',
  },
];

/**
 * Récupère les données mockées en fonction du type
 * @param type Type d'élément
 * @returns Données mockées
 */
export function getMockDataByType(type: RecommendationType) {
  switch (type) {
    case RecommendationType.RETREAT:
      return mockRetreats;
    case RecommendationType.PARTNER:
      return mockPartners;
    case RecommendationType.COURSE:
      return mockCourses;
    default:
      return [];
  }
}

/**
 * Récupère un élément mocké par son ID et son type
 * @param type Type d'élément
 * @param id ID de l'élément
 * @returns Élément mocké ou undefined
 */
export function getMockItemById(type: RecommendationType, id: string) {
  const items = getMockDataByType(type);
  return items.find(item => item.id === id);
}

/**
 * Génère des recommandations mockées
 * @param type Type d'élément
 * @param count Nombre de recommandations
 * @returns Recommandations mockées
 */
export function generateMockRecommendations(type: RecommendationType, count: number = 5) {
  const items = getMockDataByType(type);
  const recommendations = items.slice(0, count).map((item, index) => ({
    id: item.id,
    type,
    title: item.title || item.name,
    description: item.description,
    score: 1 - (index * 0.1), // Score décroissant
    sources: ['mock'],
    reasons: ['Recommandation de test'],
    imageUrl: item.imageUrl,
    url: `https://retreatandbe.com/${type.toLowerCase()}s/${item.id}`,
    metadata: {
      ...item,
    },
  }));
  
  return {
    items: recommendations,
    total: items.length,
    page: 1,
    limit: count,
  };
}
