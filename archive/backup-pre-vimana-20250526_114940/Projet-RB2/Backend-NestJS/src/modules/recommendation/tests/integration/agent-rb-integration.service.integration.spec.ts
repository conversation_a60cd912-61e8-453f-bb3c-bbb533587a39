/**
 * Tests d'intégration pour le service d'intégration Agent-RB
 */

import { Test } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AgentRbIntegrationService } from '../../services/agent-rb-integration.service';
import { AgentRbClient } from '../../clients/agent-rb-client';
import { MockAgentRbClient } from './agent-rb-client.mock';
import { RecommendationType } from '../../enums/recommendation-type.enum';
import { mockRetreats, mockPartners, mockCourses } from './mocks';

describe('AgentRbIntegrationService (Integration)', () => {
  let agentRbIntegrationService: AgentRbIntegrationService;
  let agentRbClient: MockAgentRbClient;

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
      ],
      providers: [
        AgentRbIntegrationService,
        {
          provide: AgentRbClient,
          useClass: MockAgentRbClient,
        },
      ],
    }).compile();

    agentRbIntegrationService = moduleRef.get<AgentRbIntegrationService>(AgentRbIntegrationService);
    agentRbClient = moduleRef.get<AgentRbClient>(AgentRbClient) as unknown as MockAgentRbClient;
  });

  describe('enrichRecommendation', () => {
    it('should enrich a retreat recommendation', async () => {
      // Arrange
      const recommendation = {
        id: 'retreat-1',
        score: 0.95,
        sources: ['test'],
        reasons: ['Test reason'],
      };

      // Act
      const enrichedRecommendation = await agentRbIntegrationService.enrichRecommendation(
        recommendation,
        RecommendationType.RETREAT,
        true,
      );

      // Assert
      expect(enrichedRecommendation).toBeDefined();
      expect(enrichedRecommendation.id).toBe('retreat-1');
      expect(enrichedRecommendation.type).toBe(RecommendationType.RETREAT);
      expect(enrichedRecommendation.title).toBe(mockRetreats[0].title);
      expect(enrichedRecommendation.description).toBe(mockRetreats[0].description);
      expect(enrichedRecommendation.score).toBe(0.95);
      expect(enrichedRecommendation.sources).toEqual(['test']);
      expect(enrichedRecommendation.reasons).toEqual(['Test reason']);
      expect(enrichedRecommendation.imageUrl).toBe(mockRetreats[0].imageUrl);
      expect(enrichedRecommendation.url).toBe(`https://retreatandbe.com/retreats/${recommendation.id}`);
      expect(enrichedRecommendation.metadata).toBeDefined();
      expect(enrichedRecommendation.metadata.category).toBe(mockRetreats[0].category);
      expect(enrichedRecommendation.metadata.level).toBe(mockRetreats[0].level);
      expect(enrichedRecommendation.metadata.location).toBe(mockRetreats[0].location);
      expect(enrichedRecommendation.metadata.duration).toBe(mockRetreats[0].duration);
      expect(enrichedRecommendation.metadata.tags).toEqual(mockRetreats[0].tags);
      expect(enrichedRecommendation.metadata.price).toBe(mockRetreats[0].price);
      expect(enrichedRecommendation.metadata.currency).toBe(mockRetreats[0].currency);
    });

    it('should enrich a partner recommendation', async () => {
      // Arrange
      const recommendation = {
        id: 'partner-1',
        score: 0.9,
        sources: ['test'],
        reasons: ['Test reason'],
      };

      // Act
      const enrichedRecommendation = await agentRbIntegrationService.enrichRecommendation(
        recommendation,
        RecommendationType.PARTNER,
        true,
      );

      // Assert
      expect(enrichedRecommendation).toBeDefined();
      expect(enrichedRecommendation.id).toBe('partner-1');
      expect(enrichedRecommendation.type).toBe(RecommendationType.PARTNER);
      expect(enrichedRecommendation.title).toBe(mockPartners[0].name);
      expect(enrichedRecommendation.description).toBe(mockPartners[0].description);
      expect(enrichedRecommendation.score).toBe(0.9);
      expect(enrichedRecommendation.sources).toEqual(['test']);
      expect(enrichedRecommendation.reasons).toEqual(['Test reason']);
      expect(enrichedRecommendation.imageUrl).toBe(mockPartners[0].imageUrl);
      expect(enrichedRecommendation.url).toBe(`https://retreatandbe.com/partners/${recommendation.id}`);
      expect(enrichedRecommendation.metadata).toBeDefined();
      expect(enrichedRecommendation.metadata.tags).toEqual(mockPartners[0].specialties);
      expect(enrichedRecommendation.metadata.location).toBe(mockPartners[0].location);
    });

    it('should enrich a course recommendation', async () => {
      // Arrange
      const recommendation = {
        id: 'course-1',
        score: 0.85,
        sources: ['test'],
        reasons: ['Test reason'],
      };

      // Act
      const enrichedRecommendation = await agentRbIntegrationService.enrichRecommendation(
        recommendation,
        RecommendationType.COURSE,
        true,
      );

      // Assert
      expect(enrichedRecommendation).toBeDefined();
      expect(enrichedRecommendation.id).toBe('course-1');
      expect(enrichedRecommendation.type).toBe(RecommendationType.COURSE);
      expect(enrichedRecommendation.title).toBe(mockCourses[0].title);
      expect(enrichedRecommendation.description).toBe(mockCourses[0].description);
      expect(enrichedRecommendation.score).toBe(0.85);
      expect(enrichedRecommendation.sources).toEqual(['test']);
      expect(enrichedRecommendation.reasons).toEqual(['Test reason']);
      expect(enrichedRecommendation.imageUrl).toBe(mockCourses[0].imageUrl);
      expect(enrichedRecommendation.url).toBe(`https://retreatandbe.com/courses/${recommendation.id}`);
      expect(enrichedRecommendation.metadata).toBeDefined();
      expect(enrichedRecommendation.metadata.level).toBe(mockCourses[0].level);
      expect(enrichedRecommendation.metadata.duration).toBe(mockCourses[0].duration);
    });

    it('should handle non-existent items', async () => {
      // Arrange
      const recommendation = {
        id: 'nonexistent',
        score: 0.8,
        sources: ['test'],
        reasons: ['Test reason'],
      };

      // Act & Assert
      await expect(
        agentRbIntegrationService.enrichRecommendation(
          recommendation,
          RecommendationType.RETREAT,
          true,
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should not include metadata when includeMetadata is false', async () => {
      // Arrange
      const recommendation = {
        id: 'retreat-1',
        score: 0.95,
        sources: ['test'],
        reasons: ['Test reason'],
      };

      // Act
      const enrichedRecommendation = await agentRbIntegrationService.enrichRecommendation(
        recommendation,
        RecommendationType.RETREAT,
        false,
      );

      // Assert
      expect(enrichedRecommendation).toBeDefined();
      expect(enrichedRecommendation.metadata).toBeUndefined();
    });
  });

  describe('enrichRecommendations', () => {
    it('should enrich multiple recommendations', async () => {
      // Arrange
      const recommendations = [
        {
          id: 'retreat-1',
          score: 0.95,
          sources: ['test'],
          reasons: ['Test reason 1'],
        },
        {
          id: 'retreat-2',
          score: 0.9,
          sources: ['test'],
          reasons: ['Test reason 2'],
        },
      ];

      // Act
      const enrichedRecommendations = await agentRbIntegrationService.enrichRecommendations(
        recommendations,
        RecommendationType.RETREAT,
        true,
      );

      // Assert
      expect(enrichedRecommendations).toBeDefined();
      expect(enrichedRecommendations.length).toBe(2);
      expect(enrichedRecommendations[0].id).toBe('retreat-1');
      expect(enrichedRecommendations[1].id).toBe('retreat-2');
    });

    it('should handle empty recommendations array', async () => {
      // Act
      const enrichedRecommendations = await agentRbIntegrationService.enrichRecommendations(
        [],
        RecommendationType.RETREAT,
        true,
      );

      // Assert
      expect(enrichedRecommendations).toBeDefined();
      expect(enrichedRecommendations.length).toBe(0);
    });

    it('should continue processing when one recommendation fails', async () => {
      // Arrange
      const recommendations = [
        {
          id: 'retreat-1',
          score: 0.95,
          sources: ['test'],
          reasons: ['Test reason 1'],
        },
        {
          id: 'nonexistent',
          score: 0.9,
          sources: ['test'],
          reasons: ['Test reason 2'],
        },
        {
          id: 'retreat-2',
          score: 0.85,
          sources: ['test'],
          reasons: ['Test reason 3'],
        },
      ];

      // Act
      const enrichedRecommendations = await agentRbIntegrationService.enrichRecommendations(
        recommendations,
        RecommendationType.RETREAT,
        true,
      );

      // Assert
      expect(enrichedRecommendations).toBeDefined();
      expect(enrichedRecommendations.length).toBe(2); // Le deuxième élément est filtré car il n'existe pas
      expect(enrichedRecommendations[0].id).toBe('retreat-1');
      expect(enrichedRecommendations[1].id).toBe('retreat-2');
    });
  });
});
