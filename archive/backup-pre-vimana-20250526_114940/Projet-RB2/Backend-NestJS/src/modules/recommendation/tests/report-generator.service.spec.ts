import { Test, TestingModule } from '@nestjs/testing';
import { ReportGeneratorService } from '../services/report-generator.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { AnalyticsService } from '../services/analytics.service';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';

// Mock pour les services
const mockPrismaService = {
  user: {
    findUnique: jest.fn(),
  },
};

const mockAnalyticsService = {
  getGlobalMetrics: jest.fn(),
  getUserMetrics: jest.fn(),
};

const mockConfigService = {
  get: jest.fn(),
};

// Mock pour fs et path
jest.mock('fs', () => ({
  existsSync: jest.fn(),
  mkdirSync: jest.fn(),
  createWriteStream: jest.fn(() => ({
    on: jest.fn().mockImplementation(function(event, callback) {
      if (event === 'finish') {
        callback();
      }
      return this;
    }),
  })),
}));

jest.mock('path', () => ({
  join: jest.fn((dir, file) => `${dir}/${file}`),
}));

// Mock pour PDFDocument
jest.mock('pdfkit', () => {
  return jest.fn().mockImplementation(() => ({
    pipe: jest.fn().mockReturnThis(),
    fontSize: jest.fn().mockReturnThis(),
    text: jest.fn().mockReturnThis(),
    moveDown: jest.fn().mockReturnThis(),
    moveTo: jest.fn().mockReturnThis(),
    lineTo: jest.fn().mockReturnThis(),
    stroke: jest.fn().mockReturnThis(),
    end: jest.fn(),
    y: 100,
  }));
});

describe('ReportGeneratorService', () => {
  let service: ReportGeneratorService;
  let prismaService: PrismaService;
  let analyticsService: AnalyticsService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReportGeneratorService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: AnalyticsService,
          useValue: mockAnalyticsService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<ReportGeneratorService>(ReportGeneratorService);
    prismaService = module.get<PrismaService>(PrismaService);
    analyticsService = module.get<AnalyticsService>(AnalyticsService);
    configService = module.get<ConfigService>(ConfigService);

    // Réinitialiser les mocks avant chaque test
    jest.clearAllMocks();
    
    // Configurer les mocks par défaut
    mockConfigService.get.mockReturnValue('test-reports');
    (fs.existsSync as jest.Mock).mockReturnValue(false);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateGlobalReport', () => {
    it('should generate a global report', async () => {
      // Configurer les mocks
      const mockMetrics = {
        period: {
          start: new Date('2023-01-01'),
          end: new Date('2023-01-31'),
        },
        totalRecommendations: 100,
        totalInteractions: 500,
        interactionCounts: {
          VIEW: 300,
          CLICK: 150,
          LIKE: 50,
          BOOKMARK: 30,
          ENROLL: 20,
          PURCHASE: 10,
        },
        rates: {
          clickThroughRate: 50,
          conversionRate: 10,
          engagementRate: 26.67,
        },
        byType: [
          {
            type: 'COURSE',
            totalRecommendations: 50,
            totalInteractions: 250,
            interactionCounts: {
              VIEW: 150,
              CLICK: 75,
            },
            rates: {
              clickThroughRate: 50,
              conversionRate: 0,
              engagementRate: 0,
            },
          },
        ],
        byStrategy: [
          {
            strategy: 'HYBRID',
            totalRecommendations: 30,
            totalInteractions: 150,
            interactionCounts: {
              VIEW: 90,
              CLICK: 45,
            },
            rates: {
              clickThroughRate: 50,
              conversionRate: 0,
              engagementRate: 0,
            },
          },
        ],
        daily: [
          {
            date: new Date('2023-01-01'),
            totalRecommendations: 20,
            totalInteractions: 100,
            interactionCounts: {},
          },
          {
            date: new Date('2023-01-02'),
            totalRecommendations: 30,
            totalInteractions: 150,
            interactionCounts: {},
          },
        ],
      };

      mockAnalyticsService.getGlobalMetrics.mockResolvedValue(mockMetrics);

      // Appeler la méthode à tester
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-31');
      const result = await service.generateGlobalReport(startDate, endDate);

      // Vérifier les résultats
      expect(result).toBeDefined();
      expect(result).toBe('test-reports/global_report_2023-01-01_to_2023-01-31.pdf');

      // Vérifier que les méthodes ont été appelées avec les bons paramètres
      expect(mockAnalyticsService.getGlobalMetrics).toHaveBeenCalledWith(startDate, endDate);
      expect(fs.mkdirSync).toHaveBeenCalledWith('test-reports', { recursive: true });
      expect(fs.createWriteStream).toHaveBeenCalledWith('test-reports/global_report_2023-01-01_to_2023-01-31.pdf');
    });
  });

  describe('generateUserReport', () => {
    it('should generate a user report', async () => {
      const userId = 'user-123';
      
      // Configurer les mocks
      const mockUser = {
        email: '<EMAIL>',
        profile: {
          firstName: 'John',
          lastName: 'Doe',
        },
      };

      const mockMetrics = {
        userId,
        period: {
          start: new Date('2023-01-01'),
          end: new Date('2023-01-31'),
        },
        totalRecommendations: 50,
        totalInteractions: 200,
        interactionCounts: {
          VIEW: 120,
          CLICK: 60,
          LIKE: 20,
        },
        rates: {
          clickThroughRate: 50,
          conversionRate: 0,
          engagementRate: 16.67,
        },
        categoryInteractions: [
          {
            category: 'yoga',
            count: 30,
            isPreferred: true,
          },
          {
            category: 'meditation',
            count: 20,
            isPreferred: true,
          },
          {
            category: 'fitness',
            count: 10,
            isPreferred: false,
          },
        ],
        preferences: {
          recommendationStrategy: 'HYBRID',
          preferredCategories: ['yoga', 'meditation'],
          preferredTags: ['wellness', 'mindfulness'],
        },
      };

      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockAnalyticsService.getUserMetrics.mockResolvedValue(mockMetrics);

      // Appeler la méthode à tester
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-31');
      const result = await service.generateUserReport(userId, startDate, endDate);

      // Vérifier les résultats
      expect(result).toBeDefined();
      expect(result).toBe('test-reports/user_report_john_doe_2023-01-01_to_2023-01-31.pdf');

      // Vérifier que les méthodes ont été appelées avec les bons paramètres
      expect(mockPrismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
        select: {
          email: true,
          profile: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
      });
      
      expect(mockAnalyticsService.getUserMetrics).toHaveBeenCalledWith(userId, startDate, endDate);
      expect(fs.createWriteStream).toHaveBeenCalledWith('test-reports/user_report_john_doe_2023-01-01_to_2023-01-31.pdf');
    });
  });
});
