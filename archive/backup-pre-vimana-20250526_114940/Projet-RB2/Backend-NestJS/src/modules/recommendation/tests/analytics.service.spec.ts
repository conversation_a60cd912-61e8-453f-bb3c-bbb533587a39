import { Test, TestingModule } from '@nestjs/testing';
import { AnalyticsService } from '../services/analytics.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';

// Mock pour PrismaService
const mockPrismaService = {
  recommendation: {
    count: jest.fn(),
    groupBy: jest.fn(),
  },
  userInteraction: {
    count: jest.fn(),
    groupBy: jest.fn(),
  },
  userPreference: {
    findUnique: jest.fn(),
  },
  analyticsReport: {
    create: jest.fn(),
  },
};

describe('AnalyticsService', () => {
  let service: AnalyticsService;
  let prismaService: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AnalyticsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<AnalyticsService>(AnalyticsService);
    prismaService = module.get<PrismaService>(PrismaService);

    // Réinitialiser les mocks avant chaque test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getGlobalMetrics', () => {
    it('should return global metrics', async () => {
      // Configurer les mocks
      mockPrismaService.recommendation.count.mockResolvedValue(100);
      mockPrismaService.userInteraction.count.mockResolvedValue(500);
      mockPrismaService.userInteraction.groupBy.mockResolvedValueOnce([
        { interactionType: 'VIEW', _count: { interactionType: 300 } },
        { interactionType: 'CLICK', _count: { interactionType: 150 } },
        { interactionType: 'LIKE', _count: { interactionType: 50 } },
        { interactionType: 'BOOKMARK', _count: { interactionType: 30 } },
        { interactionType: 'ENROLL', _count: { interactionType: 20 } },
        { interactionType: 'PURCHASE', _count: { interactionType: 10 } },
      ]);

      // Mock pour les métriques par type
      mockPrismaService.recommendation.count.mockResolvedValueOnce(50); // COURSE
      mockPrismaService.userInteraction.count.mockResolvedValueOnce(250); // COURSE
      mockPrismaService.userInteraction.groupBy.mockResolvedValueOnce([
        { interactionType: 'VIEW', _count: { interactionType: 150 } },
        { interactionType: 'CLICK', _count: { interactionType: 75 } },
      ]);

      mockPrismaService.recommendation.count.mockResolvedValueOnce(50); // RETREAT
      mockPrismaService.userInteraction.count.mockResolvedValueOnce(250); // RETREAT
      mockPrismaService.userInteraction.groupBy.mockResolvedValueOnce([
        { interactionType: 'VIEW', _count: { interactionType: 150 } },
        { interactionType: 'CLICK', _count: { interactionType: 75 } },
      ]);

      // Mock pour les métriques par stratégie
      mockPrismaService.recommendation.count.mockResolvedValueOnce(30); // HYBRID
      mockPrismaService.userInteraction.count.mockResolvedValueOnce(150); // HYBRID
      mockPrismaService.userInteraction.groupBy.mockResolvedValueOnce([
        { interactionType: 'VIEW', _count: { interactionType: 90 } },
        { interactionType: 'CLICK', _count: { interactionType: 45 } },
      ]);

      mockPrismaService.recommendation.count.mockResolvedValueOnce(30); // CONTENT_BASED
      mockPrismaService.userInteraction.count.mockResolvedValueOnce(150); // CONTENT_BASED
      mockPrismaService.userInteraction.groupBy.mockResolvedValueOnce([
        { interactionType: 'VIEW', _count: { interactionType: 90 } },
        { interactionType: 'CLICK', _count: { interactionType: 45 } },
      ]);

      mockPrismaService.recommendation.count.mockResolvedValueOnce(40); // MATRIX_FACTORIZATION
      mockPrismaService.userInteraction.count.mockResolvedValueOnce(200); // MATRIX_FACTORIZATION
      mockPrismaService.userInteraction.groupBy.mockResolvedValueOnce([
        { interactionType: 'VIEW', _count: { interactionType: 120 } },
        { interactionType: 'CLICK', _count: { interactionType: 60 } },
      ]);

      // Mock pour les métriques quotidiennes
      mockPrismaService.userInteraction.groupBy.mockResolvedValueOnce([
        { createdAt: new Date('2023-01-01'), _count: { interactionType: 100 } },
        { createdAt: new Date('2023-01-02'), _count: { interactionType: 150 } },
      ]);

      mockPrismaService.recommendation.groupBy.mockResolvedValueOnce([
        { createdAt: new Date('2023-01-01'), _count: { id: 20 } },
        { createdAt: new Date('2023-01-02'), _count: { id: 30 } },
      ]);

      // Appeler la méthode à tester
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-31');
      const result = await service.getGlobalMetrics(startDate, endDate);

      // Vérifier les résultats
      expect(result).toBeDefined();
      expect(result.totalRecommendations).toBe(100);
      expect(result.totalInteractions).toBe(500);
      expect(result.interactionCounts).toEqual({
        VIEW: 300,
        CLICK: 150,
        LIKE: 50,
        BOOKMARK: 30,
        ENROLL: 20,
        PURCHASE: 10,
      });

      // Vérifier les taux de conversion
      expect(result.rates.clickThroughRate).toBeCloseTo(50, 1); // 150/300 * 100
      expect(result.rates.conversionRate).toBeCloseTo(10, 1); // (20+10)/300 * 100
      expect(result.rates.engagementRate).toBeCloseTo(26.67, 1); // (50+30)/300 * 100

      // Vérifier les métriques par type
      expect(result.byType).toHaveLength(Object.keys(RecommendationType).length);
      
      // Vérifier les métriques par stratégie
      expect(result.byStrategy).toHaveLength(Object.keys(RecommendationStrategy).length);
      
      // Vérifier les métriques quotidiennes
      expect(result.daily).toHaveLength(2);
      expect(result.daily[0].date).toEqual(new Date('2023-01-01'));
      expect(result.daily[0].totalInteractions).toBe(100);
      expect(result.daily[0].totalRecommendations).toBe(20);
      expect(result.daily[1].date).toEqual(new Date('2023-01-02'));
      expect(result.daily[1].totalInteractions).toBe(150);
      expect(result.daily[1].totalRecommendations).toBe(30);

      // Vérifier que les méthodes du PrismaService ont été appelées avec les bons paramètres
      expect(mockPrismaService.recommendation.count).toHaveBeenCalledWith({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      expect(mockPrismaService.userInteraction.count).toHaveBeenCalledWith({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });
    });
  });

  describe('getUserMetrics', () => {
    it('should return user metrics', async () => {
      const userId = 'user-123';
      
      // Configurer les mocks
      mockPrismaService.recommendation.count.mockResolvedValue(50);
      mockPrismaService.userInteraction.count.mockResolvedValue(200);
      mockPrismaService.userInteraction.groupBy.mockResolvedValueOnce([
        { interactionType: 'VIEW', _count: { interactionType: 120 } },
        { interactionType: 'CLICK', _count: { interactionType: 60 } },
        { interactionType: 'LIKE', _count: { interactionType: 20 } },
      ]);

      mockPrismaService.userPreference.findUnique.mockResolvedValue({
        preferences: {
          recommendationStrategy: RecommendationStrategy.HYBRID,
          preferredCategories: ['yoga', 'meditation'],
          preferredTags: ['wellness', 'mindfulness'],
        },
      });

      mockPrismaService.userInteraction.groupBy.mockResolvedValueOnce([
        { metadata: { category: 'yoga' }, _count: { interactionType: 30 } },
        { metadata: { category: 'meditation' }, _count: { interactionType: 20 } },
        { metadata: { category: 'fitness' }, _count: { interactionType: 10 } },
      ]);

      // Appeler la méthode à tester
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-31');
      const result = await service.getUserMetrics(userId, startDate, endDate);

      // Vérifier les résultats
      expect(result).toBeDefined();
      expect(result.userId).toBe(userId);
      expect(result.totalRecommendations).toBe(50);
      expect(result.totalInteractions).toBe(200);
      expect(result.interactionCounts).toEqual({
        VIEW: 120,
        CLICK: 60,
        LIKE: 20,
      });

      // Vérifier les taux de conversion
      expect(result.rates.clickThroughRate).toBeCloseTo(50, 1); // 60/120 * 100
      expect(result.rates.conversionRate).toBeCloseTo(0, 1); // Pas d'ENROLL ou PURCHASE
      expect(result.rates.engagementRate).toBeCloseTo(16.67, 1); // 20/120 * 100

      // Vérifier les interactions par catégorie
      expect(result.categoryInteractions).toHaveLength(3);
      expect(result.categoryInteractions[0].category).toBe('yoga');
      expect(result.categoryInteractions[0].count).toBe(30);
      expect(result.categoryInteractions[0].isPreferred).toBe(true);
      expect(result.categoryInteractions[1].category).toBe('meditation');
      expect(result.categoryInteractions[1].count).toBe(20);
      expect(result.categoryInteractions[1].isPreferred).toBe(true);
      expect(result.categoryInteractions[2].category).toBe('fitness');
      expect(result.categoryInteractions[2].count).toBe(10);
      expect(result.categoryInteractions[2].isPreferred).toBe(false);

      // Vérifier les préférences
      expect(result.preferences).toEqual({
        recommendationStrategy: RecommendationStrategy.HYBRID,
        preferredCategories: ['yoga', 'meditation'],
        preferredTags: ['wellness', 'mindfulness'],
      });

      // Vérifier que les méthodes du PrismaService ont été appelées avec les bons paramètres
      expect(mockPrismaService.recommendation.count).toHaveBeenCalledWith({
        where: {
          userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      expect(mockPrismaService.userInteraction.count).toHaveBeenCalledWith({
        where: {
          userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      expect(mockPrismaService.userPreference.findUnique).toHaveBeenCalledWith({
        where: { userId },
        select: { preferences: true },
      });
    });
  });
});
