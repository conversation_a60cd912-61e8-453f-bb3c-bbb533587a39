/**
 * Méthodes de factorisation matricielle pour les recommandations
 */
export enum FactorizationMethod {
  /**
   * Singular Value Decomposition - décompose la matrice utilisateur-item
   * en trois matrices (U, Sigma, V) pour capturer les facteurs latents
   */
  SVD = 'SVD',
  
  /**
   * Alternating Least Squares - méthode itérative qui optimise
   * alternativement les facteurs utilisateur et item
   */
  ALS = 'ALS',
  
  /**
   * Non-negative Matrix Factorization - factorisation avec contrainte
   * de non-négativité, utile pour l'interprétabilité
   */
  NMF = 'NMF',
  
  /**
   * Bayesian Personalized Ranking - optimise le classement relatif
   * des items plutôt que les scores absolus
   */
  BPR = 'BPR',
}
