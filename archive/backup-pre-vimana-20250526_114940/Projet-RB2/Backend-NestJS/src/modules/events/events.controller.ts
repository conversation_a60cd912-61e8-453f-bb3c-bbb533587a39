import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  UseGuards,
  Patch,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { EventsService } from './events.service';
import { CreateEventDto } from './dto/create-event.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@ApiTags('events')
@Controller('events')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class EventsController {
  constructor(private readonly eventsService: EventsService) {}

  @Post()
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Créer un nouvel événement' })
  @ApiResponse({ status: 201, description: 'L\'événement a été créé avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  create(@Body() createEventDto: CreateEventDto) {
    return this.eventsService.create(createEventDto);
  }

  @Get()
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer tous les événements' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'eventType', required: false, type: String, description: 'Type d\'événement' })
  @ApiResponse({ status: 200, description: 'Liste des événements récupérée avec succès.' })
  findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('eventType') eventType?: string,
  ) {
    return this.eventsService.findAll(page, limit, eventType);
  }

  @Get(':id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer un événement par son ID' })
  @ApiResponse({ status: 200, description: 'L\'événement a été récupéré avec succès.' })
  @ApiResponse({ status: 404, description: 'Événement non trouvé.' })
  findOne(@Param('id') id: string) {
    return this.eventsService.findOne(id);
  }

  @Get('user/:userId')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer les événements d\'un utilisateur' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiResponse({ status: 200, description: 'Liste des événements récupérée avec succès.' })
  findByUserId(
    @Param('userId') userId: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.eventsService.findByUserId(parseInt(userId), page, limit);
  }

  @Patch(':id/status')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Mettre à jour le statut d\'un événement' })
  @ApiResponse({ status: 200, description: 'Le statut de l\'événement a été mis à jour avec succès.' })
  @ApiResponse({ status: 404, description: 'Événement non trouvé.' })
  updateStatus(
    @Param('id') id: string,
    @Body('status') status: string,
  ) {
    return this.eventsService.updateStatus(id, status);
  }

  @Delete(':id')
  @Roles('ADMIN')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Supprimer un événement' })
  @ApiResponse({ status: 204, description: 'L\'événement a été supprimé avec succès.' })
  @ApiResponse({ status: 404, description: 'Événement non trouvé.' })
  remove(@Param('id') id: string) {
    return this.eventsService.remove(id);
  }
}
