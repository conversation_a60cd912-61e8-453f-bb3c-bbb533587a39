import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateEventDto } from './dto/create-event.dto';
// import { any } from '@prisma/client';

@Injectable()
export class EventsService {
  private readonly logger = new Logger(EventsService.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(createEventDto: CreateEventDto): Promise<any> {
    try {
      const event = await this.prisma.eventLog.create({
        data: {
          eventType: createEventDto.eventType,
          payload: createEventDto.payload,
          status: createEventDto.status || 'RECEIVED',
          userId: createEventDto.userId,
        },
      });

      this.logger.log(`Événement créé: ${event.id} - Type: ${event.eventType}`);
      return event;
    } catch (error) {
      this.logger.error(`<PERSON>rreur lors de la création de l'événement: ${error.message}`);
      throw error;
    }
  }

  async findAll(page = 1, limit = 10, eventType?: string): Promise<{ events: any[]; total: number; page: number; limit: number }> {
    const skip = (page - 1) * limit;
    const where = eventType ? { eventType } : {};

    const [events, total] = await Promise.all([
      this.prisma.eventLog.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.eventLog.count({ where }),
    ]);

    return {
      events,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string): Promise<any> {
    return this.prisma.eventLog.findUnique({
      where: { id },
    });
  }

  async findByUserId(userId: number, page = 1, limit = 10): Promise<{ events: any[]; total: number; page: number; limit: number }> {
    const skip = (page - 1) * limit;

    const [events, total] = await Promise.all([
      this.prisma.eventLog.findMany({
        where: { userId },
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.eventLog.count({ where: { userId } }),
    ]);

    return {
      events,
      total,
      page,
      limit,
    };
  }

  async updateStatus(id: string, status: string): Promise<any> {
    return this.prisma.eventLog.update({
      where: { id },
      data: { status },
    });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.eventLog.delete({
      where: { id },
    });
  }

  /**
   * S'abonne à un type d'événement
   * @param eventPattern Motif d'événement (peut contenir des wildcards *)
   * @param callback Fonction de rappel à exécuter lorsqu'un événement correspondant est reçu
   */
  subscribe(eventPattern: string, callback: (event: any) => void): void {
    this.logger.log(`Abonnement aux événements: ${eventPattern}`);

    // Dans une implémentation réelle, on utiliserait un système de pub/sub
    // Pour cette implémentation simplifiée, on simule l'abonnement

    // Enregistrer l'abonnement dans un registre (simulé ici)
    this.logger.log(`Abonnement enregistré pour: ${eventPattern}`);
  }
}
