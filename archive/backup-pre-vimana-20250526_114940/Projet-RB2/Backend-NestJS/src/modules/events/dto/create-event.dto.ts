import { IsString, IsOptional, IsNumber, IsObject, IsIn } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateEventDto {
  @ApiProperty({
    description: 'Type d\'événement',
    example: 'USER_CREATED',
  })
  @IsString()
  eventType: string;

  @ApiProperty({
    description: 'Données de l\'événement',
    example: { userId: '123', action: 'create' },
  })
  @IsObject()
  payload: any;

  @ApiPropertyOptional({
    description: 'Statut de l\'événement',
    example: 'RECEIVED',
    enum: ['RECEIVED', 'PROCESSING', 'COMPLETED', 'FAILED'],
    default: 'RECEIVED',
  })
  @IsOptional()
  @IsString()
  @IsIn(['RECEIVED', 'PROCESSING', 'COMPLETED', 'FAILED'])
  status?: string;

  @ApiPropertyOptional({
    description: 'ID de l\'utilisateur associé à l\'événement',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  userId?: number;
}
