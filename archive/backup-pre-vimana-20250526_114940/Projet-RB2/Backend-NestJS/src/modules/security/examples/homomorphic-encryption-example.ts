/**
 * Exemple d'utilisation du service de chiffrement homomorphique
 * 
 * Ce fichier montre comment utiliser le HomomorphicEncryptionService
 * pour effectuer des calculs sur des données chiffrées sans les déchiffrer.
 */

import { HomomorphicEncryptionService } from '../services/homomorphic-encryption.service';
import { ConfigService } from '@nestjs/config';

async function runHomomorphicExample() {
  console.log('Démarrage de l\'exemple de chiffrement homomorphique...');
  
  // Créer une instance du service de configuration
  const configService = new ConfigService({
    HOMOMORPHIC_ENCRYPTION_ENABLED: true,
    HOMOMORPHIC_ENCRYPTION_SCHEME: 'BFV',
    HOMOMORPHIC_ENCRYPTION_SECURITY_LEVEL: 128,
    HOMOMORPHIC_ENCRYPTION_POLY_MODULUS_DEGREE: 4096,
    HOMOMORPHIC_ENCRYPTION_PLAINTEXT_MODULUS: 1024,
    HOMOMORPHIC_ENCRYPTION_COEFF_MODULUS_BITS: [60, 40, 40, 60]
  });
  
  // Créer une instance du service de chiffrement homomorphique
  const homomorphicService = new HomomorphicEncryptionService(configService);
  
  // Initialiser le service
  await homomorphicService.onModuleInit();
  
  if (!homomorphicService.isEnabled()) {
    console.error('Le chiffrement homomorphique n\'est pas activé');
    return;
  }
  
  console.log('Service de chiffrement homomorphique initialisé');
  
  try {
    // Exemple 1: Chiffrement et déchiffrement
    console.log('\nExemple 1: Chiffrement et déchiffrement');
    const originalValue = 42;
    console.log(`Valeur originale: ${originalValue}`);
    
    const encrypted = await homomorphicService.encrypt(originalValue);
    console.log(`Valeur chiffrée: ${encrypted.toString('hex').substring(0, 50)}...`);
    
    const decrypted = await homomorphicService.decrypt(encrypted);
    console.log(`Valeur déchiffrée: ${decrypted}`);
    
    // Exemple 2: Addition homomorphique
    console.log('\nExemple 2: Addition homomorphique');
    const value1 = 15;
    const value2 = 27;
    console.log(`Valeurs: ${value1} et ${value2}`);
    
    const encrypted1 = await homomorphicService.encrypt(value1);
    const encrypted2 = await homomorphicService.encrypt(value2);
    
    const encryptedSum = await homomorphicService.add(encrypted1, encrypted2);
    console.log(`Somme chiffrée: ${encryptedSum.toString('hex').substring(0, 50)}...`);
    
    const decryptedSum = await homomorphicService.decrypt(encryptedSum);
    console.log(`Somme déchiffrée: ${decryptedSum}`);
    console.log(`Vérification: ${value1} + ${value2} = ${value1 + value2}`);
    
    // Exemple 3: Multiplication homomorphique
    console.log('\nExemple 3: Multiplication homomorphique');
    const value3 = 5;
    const value4 = 7;
    console.log(`Valeurs: ${value3} et ${value4}`);
    
    const encrypted3 = await homomorphicService.encrypt(value3);
    const encrypted4 = await homomorphicService.encrypt(value4);
    
    const encryptedProduct = await homomorphicService.multiply(encrypted3, encrypted4);
    console.log(`Produit chiffré: ${encryptedProduct.toString('hex').substring(0, 50)}...`);
    
    const decryptedProduct = await homomorphicService.decrypt(encryptedProduct);
    console.log(`Produit déchiffré: ${decryptedProduct}`);
    console.log(`Vérification: ${value3} * ${value4} = ${value3 * value4}`);
    
    // Exemple 4: Calcul de moyenne homomorphique
    console.log('\nExemple 4: Calcul de moyenne homomorphique');
    const values = [10, 20, 30, 40, 50];
    console.log(`Valeurs: ${values.join(', ')}`);
    
    const encryptedValues = await Promise.all(values.map(v => homomorphicService.encrypt(v)));
    console.log(`Valeurs chiffrées: ${encryptedValues.length} éléments`);
    
    const encryptedAverage = await homomorphicService.average(encryptedValues);
    console.log(`Moyenne chiffrée: ${encryptedAverage.toString('hex').substring(0, 50)}...`);
    
    const decryptedAverage = await homomorphicService.decrypt(encryptedAverage);
    console.log(`Moyenne déchiffrée: ${decryptedAverage}`);
    
    const expectedAverage = values.reduce((sum, val) => sum + val, 0) / values.length;
    console.log(`Vérification: Moyenne de [${values.join(', ')}] = ${expectedAverage}`);
    
    // Exemple 5: Génération de clés
    console.log('\nExemple 5: Génération de clés');
    const keyPair = await homomorphicService.generateKeyPair();
    console.log(`Clé publique: ${keyPair.publicKey.substring(0, 50)}...`);
    console.log(`Clé privée: ${keyPair.privateKey.substring(0, 50)}...`);
    
    console.log('\nExemple terminé avec succès');
  } catch (error) {
    console.error('Erreur lors de l\'exécution de l\'exemple:', error);
  }
}

// Exécuter l'exemple si ce fichier est appelé directement
if (require.main === module) {
  runHomomorphicExample().catch(console.error);
}

export { runHomomorphicExample };
