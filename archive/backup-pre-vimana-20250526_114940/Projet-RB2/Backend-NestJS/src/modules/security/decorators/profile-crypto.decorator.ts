import { CryptoProfilingService } from '../services/crypto-profiling.service';

/**
 * Décorateur pour profiler les méthodes cryptographiques
 * @param operationType Type d'opération (ex: 'encrypt', 'decrypt', 'homomorphic.add')
 * @param serviceName Nom du service (ex: 'HomomorphicEncryptionService', 'QuantumResistantService')
 * @returns Décorateur de méthode
 */
export function ProfileCrypto(operationType: string, serviceName: string) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    // Stocker la méthode originale
    const originalMethod = descriptor.value;

    // Remplacer la méthode par une version profilée
    descriptor.value = async function (...args: any[]) {
      // Obtenir le service de profilage
      const profilingService = this.profilingService as CryptoProfilingService;
      
      // Si le service de profilage n'est pas disponible, exécuter la méthode originale
      if (!profilingService) {
        return originalMethod.apply(this, args);
      }
      
      // Appliquer le profilage
      return profilingService.profileMethod(
        this,
        propertyKey,
        { value: originalMethod },
        operationType,
        serviceName,
      ).value.apply(this, args);
    };

    return descriptor;
  };
}
