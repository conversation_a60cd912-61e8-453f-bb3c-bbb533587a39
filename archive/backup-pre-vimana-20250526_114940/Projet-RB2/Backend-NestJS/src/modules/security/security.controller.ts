import { Controller, Get, Post, Body, UseGuards, Req, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SecurityService } from './security.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Public } from '../auth/decorators/public.decorator';

@ApiTags('security')
@Controller('security')
export class SecurityController {
  constructor(private readonly securityService: SecurityService) {}

  @Get('health')
  @Public()
  @ApiOperation({ summary: 'Get security service health status' })
  @ApiResponse({ status: 200, description: 'Returns the health status of the security service' })
  async getHealthStatus() {
    return this.securityService.getHealthStatus?.() || { status: 'ok', timestamp: new Date() };
  }

  @Get('dashboard')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get security dashboard data' })
  @ApiResponse({ status: 200, description: 'Returns security dashboard data' })
  async getSecurityDashboard(@Query('timeframe') timeframe: 'day' | 'week' | 'month' = 'day') {
    return this.securityService.getSecurityDashboardData?.(timeframe) || { timeframe, stats: {} };
  }

  @Get('events')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get security events' })
  @ApiResponse({ status: 200, description: 'Returns security events' })
  async getSecurityEvents(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('type') type?: string,
    @Query('severity') severity?: string,
  ) {
    return this.securityService.getSecurityEvents?.(page, limit, type, severity) || { page, limit, type, severity, events: [] };
  }

  @Get('alerts')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get security alerts' })
  @ApiResponse({ status: 200, description: 'Returns security alerts' })
  async getSecurityAlerts(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('status') status?: 'OPEN' | 'ACKNOWLEDGED' | 'RESOLVED',
    @Query('priority') priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL',
  ) {
    return this.securityService.getSecurityAlerts?.(page, limit, status, priority) || { page, limit, status, priority, alerts: [] };
  }

  @Post('alerts/:id/acknowledge')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Acknowledge a security alert' })
  @ApiResponse({ status: 200, description: 'Alert acknowledged successfully' })
  async acknowledgeAlert(@Param('id') id: string, @Req() req: any) {
    return await this.securityService.acknowledgeAlert?.(id, req.user.id) || { alertId: id, userId: req.user.id, status: 'ACKNOWLEDGED' };
  }

  @Post('alerts/:id/resolve')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Resolve a security alert' })
  @ApiResponse({ status: 200, description: 'Alert resolved successfully' })
  async resolveAlert(
    @Param('id') id: string,
    @Body() data: { resolution: string },
    @Req() req: any,
  ) {
    return this.securityService.resolveAlert?.(id, data.resolution, req.user.id) || { alertId: id, resolution: data.resolution, userId: req.user.id, status: 'RESOLVED' };
  }

  @Get('training/simulations')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get security training simulations' })
  @ApiResponse({ status: 200, description: 'Returns security training simulations' })
  async getTrainingSimulations(@Req() req: any) {
    return await this.securityService.getTrainingSimulations?.(req.user.id) || { userId: req.user.id, simulations: [] };
  }

  @Post('training/simulations/:id/start')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Start a security training simulation' })
  @ApiResponse({ status: 200, description: 'Simulation started successfully' })
  async startTrainingSimulation(@Param('id') id: string, @Req() req: any) {
    return this.securityService.startTrainingSimulation?.(req.user.id, id) || { userId: req.user.id, simulationId: id, started: true };
  }

  @Post('training/simulations/:id/complete')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Complete a security training simulation' })
  @ApiResponse({ status: 200, description: 'Simulation completed successfully' })
  async completeTrainingSimulation(
    @Param('id') id: string,
    @Body() data: { response: string },
    @Req() req: any,
  ) {
    return this.securityService.completeTrainingSimulation?.(req.user.id, id, data.response) || { userId: req.user.id, simulationId: id, result: data.response, completed: true };
  }

  @Get('training/stats')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user security training stats' })
  @ApiResponse({ status: 200, description: 'Returns user security training stats' })
  async getUserTrainingStats(@Req() req: any) {
    return this.securityService.getUserSecurityStats?.(req.user.id) || { userId: req.user.id, stats: {} };
  }
}
