import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { VaultService } from './vault.service';

/**
 * Interface pour les métadonnées des clés de chiffrement
 */
export interface KeyMetadata {
  id: string;
  algorithm: string;
  createdAt: Date;
  expiresAt: Date;
  version: number;
  purpose: 'encryption' | 'signing' | 'hmac' | 'general';
  status: 'active' | 'rotating' | 'deprecated' | 'revoked';
  rotationPolicy?: {
    interval: number; // en millisecondes
    autoRotate: boolean;
  };
}

/**
 * Interface pour une clé de chiffrement avec ses métadonnées
 */
export interface EncryptionKey {
  key: Buffer;
  metadata: KeyMetadata;
}

/**
 * Service de gestion des clés de chiffrement
 * Ce service gère le cycle de vie des clés cryptographiques utilisées dans l'application
 */
@Injectable()
export class KeyManagementService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(KeyManagementService.name);
  private readonly keyCache: Map<string, EncryptionKey> = new Map();
  private readonly KEY_PATH_PREFIX = 'encryption-keys';
  private readonly DEFAULT_KEY_ROTATION_INTERVAL = 7 * 24 * 60 * 60 * 1000; // 7 jours
  private readonly DEFAULT_KEY_EXPIRY = 30 * 24 * 60 * 60 * 1000; // 30 jours
  private readonly DEFAULT_ALGORITHM = 'aes-256-gcm';
  private keyRotationInterval: NodeJS.Timeout | null = null;
  private readonly keyStorage: Map<string, { value: string, metadata: KeyMetadata }> = new Map();

  // Options de configuration du service
  private options: {
    keySize: number;
    algorithm: string;
    rotationInterval: number;
    useVault: boolean;
  };

  constructor(
    private readonly configService: ConfigService,
    private readonly vaultService: VaultService,
  ) {
    // Initialiser les options à partir de la configuration
    this.options = {
      keySize: this.configService.get<number>('encryption.keyManagement.keySize', 32),
      algorithm: this.configService.get<string>('encryption.keyManagement.algorithm', 'aes-256-gcm'),
      rotationInterval: this.configService.get<number>('encryption.keyManagement.rotationInterval', 30 * 24 * 60 * 60 * 1000), // 30 jours par défaut
      useVault: this.configService.get<boolean>('encryption.keyManagement.useVault', false),
    };
  }

  /**
   * Initialisation du service lors du démarrage de l'application
   */
  async onModuleInit() {
    try {
      await this.initializeKeys();
      this.logger.log('Key Management Service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Key Management Service', error);
      throw error;
    }
  }

  /**
   * Initialise les clés de chiffrement
   * Récupère les clés existantes ou en crée de nouvelles si nécessaire
   */
  private async initializeKeys(): Promise<void> {
    try {
      // Récupérer les métadonnées des clés
      const keyMetadata = await this.getKeyMetadata();

      // Si aucune clé n'existe, en créer de nouvelles
      if (!keyMetadata || Object.keys(keyMetadata).length === 0) {
        this.logger.log('No encryption keys found, creating new keys');
        await this.createInitialKeys();
      } else {
        // Charger les clés actives
        await this.loadActiveKeys(keyMetadata);
      }

      // Planifier la rotation automatique des clés
      this.scheduleKeyRotation();
    } catch (error) {
      this.logger.error('Failed to initialize keys', error);
      throw error;
    }
  }

  /**
   * Crée les clés initiales pour différents usages
   */
  private async createInitialKeys(): Promise<void> {
    try {
      // Créer une clé pour le chiffrement général
      await this.createKey('general', this.DEFAULT_ALGORITHM, 'encryption');

      // Créer une clé pour la signature
      await this.createKey('signing', 'hmac-sha256', 'signing');

      // Créer une clé pour les services financiers
      await this.createKey('financial', this.DEFAULT_ALGORITHM, 'encryption');

      // Créer une clé pour le stockage décentralisé
      await this.createKey('storage', this.DEFAULT_ALGORITHM, 'encryption');

      // Créer une clé pour la messagerie
      await this.createKey('messaging', this.DEFAULT_ALGORITHM, 'encryption');
    } catch (error) {
      this.logger.error('Failed to create initial keys', error);
      throw error;
    }
  }

  /**
   * Récupère les métadonnées des clés
   */
  private async getKeyMetadata(): Promise<Record<string, KeyMetadata>> {
    try {
      // Essayer d'abord de récupérer les métadonnées depuis Vault
      if (this.vaultService.isInitialized()) {
        const metadata = await this.vaultService.readSecret('keys/metadata');
        if (metadata) {
          return metadata as Record<string, KeyMetadata>;
        }
      }

      // Fallback vers le stockage en mémoire
      const metadataEntries = Array.from(this.keyStorage.entries())
        .filter(([key]) => key.startsWith(`${this.KEY_PATH_PREFIX}/metadata`))
        .map(([_, value]) => value);

      if (metadataEntries.length === 0) {
        // Retourner un objet vide typé
        return Object.create(null) as Record<string, KeyMetadata>;
      }

      return metadataEntries[0].value as unknown as Record<string, KeyMetadata>;
    } catch (error) {
      this.logger.error('Failed to get key metadata', error);
      return Object.create(null) as Record<string, KeyMetadata>;
    }
  }

  /**
   * Charge les clés actives en mémoire
   */
  private async loadActiveKeys(keyMetadata: Record<string, KeyMetadata>): Promise<void> {
    try {
      for (const [keyId, metadata] of Object.entries(keyMetadata)) {
        if (metadata.status === 'active' || metadata.status === 'rotating') {
          const keyData = await this.getKeyFromStorage(keyId);
          if (keyData) {
            const key = Buffer.from(keyData, 'base64');
            this.keyCache.set(keyId, { key, metadata });
            this.logger.debug(`Loaded key: ${keyId}`);
          }
        }
      }
      this.logger.log(`Loaded ${this.keyCache.size} active encryption keys`);
    } catch (error) {
      this.logger.error('Failed to load active keys', error);
      throw error;
    }
  }

  /**
   * Récupère les métadonnées de toutes les clés (exposé publiquement)
   */
  public async fetchKeyMetadata(): Promise<Record<string, KeyMetadata>> {
    return await this.getKeyMetadata();
  }

  /**
   * Récupère une clé depuis le stockage
   */
  private async getKeyFromStorage(keyId: string): Promise<string | null> {
    // Essayer d'abord de récupérer la clé depuis Vault
    if (this.vaultService.isInitialized()) {
      try {
        const keyData = await this.vaultService.readSecret(`keys/values/${keyId}`);
        if (keyData && keyData.value) {
          return keyData.value;
        }
      } catch (error) {
        this.logger.error(`Failed to get key from Vault: ${keyId}, falling back to in-memory storage`, error);
      }
    }

    // Fallback vers le stockage en mémoire
    const keyEntry = this.keyStorage.get(`${this.KEY_PATH_PREFIX}/${keyId}`);
    return keyEntry ? keyEntry.value : null;
  }

  /**
   * Sauvegarde une clé dans le stockage
   */
  private async saveKeyToStorage(keyId: string, value: string, metadata?: any): Promise<void> {
    // Essayer d'abord de sauvegarder la clé dans Vault
    if (this.vaultService.isInitialized()) {
      try {
        await this.vaultService.writeSecret(`keys/values/${keyId}`, { value, metadata: metadata || {} });
        return;
      } catch (error) {
        this.logger.error(`Failed to save key to Vault: ${keyId}, falling back to in-memory storage`, error);
      }
    }

    // Fallback vers le stockage en mémoire
    this.keyStorage.set(`${this.KEY_PATH_PREFIX}/${keyId}`, {
      value,
      metadata: metadata || {}
    });
  }

  /**
   * Sauvegarde les métadonnées des clés
   */
  private async saveKeyMetadata(metadata: Record<string, KeyMetadata>): Promise<void> {
    // Essayer d'abord de sauvegarder les métadonnées dans Vault
    if (this.vaultService.isInitialized()) {
      try {
        await this.vaultService.writeSecret('keys/metadata', metadata);
        return;
      } catch (error) {
        this.logger.error('Failed to save key metadata to Vault, falling back to in-memory storage', error);
      }
    }

    // Fallback vers le stockage en mémoire
    this.keyStorage.set(`${this.KEY_PATH_PREFIX}/metadata`, {
      value: JSON.stringify(metadata),
      metadata: {
        id: 'metadata',
        algorithm: 'none',
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 an
        version: 1,
        purpose: 'general',
        status: 'active',
        rotationPolicy: {
          interval: 0,
          autoRotate: false
        }
      }
    });
  }

  /**
   * Planifie la rotation automatique des clés
   */
  private scheduleKeyRotation(): void {
    // Annuler l'intervalle existant s'il y en a un
    if (this.keyRotationInterval) {
      clearInterval(this.keyRotationInterval);
    }

    // Vérifier les clés toutes les heures
    this.keyRotationInterval = setInterval(async () => {
      try {
        await this.checkAndRotateKeys();
      } catch (error) {
        this.logger.error('Error during scheduled key rotation', error);
      }
    }, 60 * 60 * 1000); // 1 heure

    this.logger.log('Key rotation scheduler started');
  }

  /**
   * Vérifie si des clés doivent être rotées et les rotate si nécessaire
   */
  private async checkAndRotateKeys(): Promise<void> {
    const now = new Date();
    const keysToRotate: string[] = [];

    // Identifier les clés qui doivent être rotées
    for (const [keyId, { metadata }] of this.keyCache.entries()) {
      if (metadata.status === 'active' && metadata.expiresAt <= now) {
        keysToRotate.push(keyId);
      }
    }

    // Rotater les clés identifiées
    for (const keyId of keysToRotate) {
      try {
        await this.rotateKey(keyId);
        this.logger.log(`Rotated key: ${keyId}`);
      } catch (error) {
        this.logger.error(`Failed to rotate key: ${keyId}`, error);
      }
    }
  }

  /**
   * Crée une nouvelle clé de chiffrement
   */
  public async createKey(
    name: string,
    algorithm: string,
    purpose: 'encryption' | 'signing' | 'hmac' | 'general',
    options?: {
      rotationInterval?: number;
      autoRotate?: boolean;
    },
  ): Promise<string> {
    try {
      // Générer un ID unique pour la clé
      const keyId = `${name}-${Date.now()}-${crypto.randomBytes(4).toString('hex')}`;

      // Générer une clé cryptographique aléatoire
      let key: Buffer;

      // Utiliser les options de configuration du service si disponibles
      const useVault = this.options?.useVault !== undefined ? this.options.useVault : this.vaultService.isInitialized();

      // Essayer d'utiliser Vault pour générer la clé si disponible
      if (useVault && this.vaultService.isInitialized()) {
        try {
          const keyBase64 = await this.vaultService.generateKey(algorithm);
          key = Buffer.from(keyBase64, 'base64');
        } catch (error) {
          this.logger.warn(`Failed to generate key using Vault, falling back to local generation: ${error.message}`);
          const keyLength = this.getKeyLength(algorithm);
          key = crypto.randomBytes(keyLength);
        }
      } else {
        const keyLength = this.getKeyLength(algorithm);
        key = crypto.randomBytes(keyLength);
      }

      // Créer les métadonnées de la clé
      const now = new Date();
      const rotationInterval = options?.rotationInterval || this.DEFAULT_KEY_ROTATION_INTERVAL;
      const metadata: KeyMetadata = {
        id: keyId,
        algorithm,
        createdAt: now,
        expiresAt: new Date(now.getTime() + this.DEFAULT_KEY_EXPIRY),
        version: 1,
        purpose,
        status: 'active',
        rotationPolicy: {
          interval: rotationInterval,
          autoRotate: options?.autoRotate !== undefined ? options.autoRotate : true,
        },
      };

      // Stocker la clé
      await this.saveKeyToStorage(keyId, key.toString('base64'));

      // Mettre à jour les métadonnées
      const keyMetadata = await this.getKeyMetadata() || {};
      keyMetadata[keyId] = metadata;
      await this.saveKeyMetadata(keyMetadata);

      // Mettre en cache la clé
      this.keyCache.set(keyId, { key, metadata });

      this.logger.log(`Created new key: ${keyId}`);
      return keyId;
    } catch (error) {
      this.logger.error('Failed to create key', error);
      throw error;
    }
  }

  /**
   * Récupère une clé de chiffrement par son ID
   */
  public async getKey(keyId: string): Promise<EncryptionKey> {
    // Vérifier si la clé est dans le cache
    const cachedKey = this.keyCache.get(keyId);
    if (cachedKey) {
      return cachedKey;
    }

    // Si la clé n'est pas dans le cache, la récupérer depuis le stockage
    try {
      const keyValue = await this.getKeyFromStorage(keyId);
      if (!keyValue) {
        throw new Error(`Key not found: ${keyId}`);
      }

      const key = Buffer.from(keyValue, 'base64');
      const keyMetadata = await this.getKeyMetadata();
      const metadata = keyMetadata[keyId];

      if (!metadata) {
        throw new Error(`Key metadata not found: ${keyId}`);
      }

      // Mettre en cache la clé
      const encryptionKey: EncryptionKey = { key, metadata };
      this.keyCache.set(keyId, encryptionKey);

      return encryptionKey;
    } catch (error) {
      this.logger.error(`Failed to retrieve key: ${keyId}`, error);
      throw error;
    }
  }

  /**
   * Récupère une clé active pour un usage spécifique
   */
  public async getActiveKey(purpose: 'encryption' | 'signing' | 'hmac' | 'general', name?: string): Promise<EncryptionKey> {
    // Chercher une clé active pour l'usage spécifié
    for (const [keyId, encryptionKey] of this.keyCache.entries()) {
      const { metadata } = encryptionKey;
      if (
        metadata.status === 'active' &&
        metadata.purpose === purpose &&
        (!name || keyId.startsWith(name))
      ) {
        return encryptionKey;
      }
    }

    // Si aucune clé active n'est trouvée, en créer une nouvelle
    if (name) {
      const keyId = await this.createKey(name, this.getDefaultAlgorithm(purpose), purpose);
      return this.getKey(keyId);
    }

    throw new Error(`No active key found for purpose: ${purpose}`);
  }

  /**
   * Effectue la rotation d'une clé
   */
  public async rotateKey(keyId: string): Promise<string> {
    try {
      // Récupérer la clé existante
      const existingKey = await this.getKey(keyId);
      const { metadata } = existingKey;

      // Marquer l'ancienne clé comme en rotation
      metadata.status = 'rotating';

      // Créer une nouvelle clé avec les mêmes paramètres
      const nameParts = keyId.split('-');
      const name = nameParts[0];

      const newKeyId = await this.createKey(
        name,
        metadata.algorithm,
        metadata.purpose,
        {
          rotationInterval: metadata.rotationPolicy?.interval,
          autoRotate: metadata.rotationPolicy?.autoRotate,
        },
      );

      // Mettre à jour les métadonnées
      const keyMetadata = await this.getKeyMetadata();
      keyMetadata[keyId] = metadata;
      await this.saveKeyMetadata(keyMetadata);

      // Mettre à jour le cache
      this.keyCache.set(keyId, { ...existingKey, metadata });

      this.logger.log(`Rotated key: ${keyId} -> ${newKeyId}`);
      return newKeyId;
    } catch (error) {
      this.logger.error(`Failed to rotate key: ${keyId}`, error);
      throw error;
    }
  }

  /**
   * Révoque une clé
   */
  public async revokeKey(keyId: string): Promise<void> {
    try {
      // Récupérer les métadonnées
      const keyMetadata = await this.getKeyMetadata();
      const metadata = keyMetadata[keyId];

      if (!metadata) {
        throw new Error(`Key metadata not found: ${keyId}`);
      }

      // Marquer la clé comme révoquée
      metadata.status = 'revoked';
      keyMetadata[keyId] = metadata;

      // Mettre à jour les métadonnées
      await this.saveKeyMetadata(keyMetadata);

      // Supprimer du cache
      this.keyCache.delete(keyId);

      this.logger.log(`Revoked key: ${keyId}`);
    } catch (error) {
      this.logger.error(`Failed to revoke key: ${keyId}`, error);
      throw error;
    }
  }

  /**
   * Détermine la longueur de clé appropriée pour un algorithme donné
   */
  private getKeyLength(algorithm: string): number {
    // Utiliser la taille de clé configurée si disponible
    if (this.options?.keySize) {
      return this.options.keySize;
    }

    // Sinon, déterminer la taille en fonction de l'algorithme
    switch (algorithm) {
      case 'aes-128-gcm':
        return 16; // 128 bits
      case 'aes-192-gcm':
        return 24; // 192 bits
      case 'aes-256-gcm':
      case 'aes-256-cbc':
        return 32; // 256 bits
      case 'chacha20-poly1305':
        return 32; // 256 bits
      case 'hmac-sha256':
        return 32; // 256 bits
      case 'hmac-sha512':
        return 64; // 512 bits
      default:
        return 32; // Par défaut 256 bits
    }
  }

  /**
   * Retourne l'algorithme par défaut pour un usage donné
   */
  private getDefaultAlgorithm(purpose: 'encryption' | 'signing' | 'hmac' | 'general'): string {
    // Utiliser l'algorithme configuré si disponible
    if (this.options?.algorithm) {
      return this.options.algorithm;
    }

    // Sinon, déterminer l'algorithme en fonction de l'usage
    switch (purpose) {
      case 'encryption':
        return 'aes-256-gcm';
      case 'signing':
        return 'hmac-sha256';
      case 'hmac':
        return 'hmac-sha256';
      case 'general':
        return 'aes-256-gcm';
      default:
        return 'aes-256-gcm';
    }
  }

  /**
   * Récupère une clé par son ID
   * @param keyId ID de la clé à récupérer
   * @returns La clé et ses métadonnées
   */
  public async getKeyById(keyId: string): Promise<{ key: Buffer; keyId: string }> {
    try {
      const encryptionKey = await this.getKey(keyId);
      return {
        key: encryptionKey.key,
        keyId: encryptionKey.metadata.id
      };
    } catch (error) {
      this.logger.error(`Failed to get key by ID: ${keyId}`, error);
      throw error;
    }
  }

  /**
   * Liste toutes les clés actives
   */
  public async listActiveKeys(): Promise<KeyMetadata[]> {
    const activeKeys: KeyMetadata[] = [];

    for (const { metadata } of this.keyCache.values()) {
      if (metadata.status === 'active' || metadata.status === 'rotating') {
        activeKeys.push(metadata);
      }
    }

    return activeKeys;
  }

  /**
   * Nettoie les ressources lors de l'arrêt de l'application
   */
  public async onModuleDestroy() {
    if (this.keyRotationInterval) {
      clearInterval(this.keyRotationInterval);
    }
    this.keyCache.clear();
    this.logger.log('Key Management Service shut down');
  }
}
