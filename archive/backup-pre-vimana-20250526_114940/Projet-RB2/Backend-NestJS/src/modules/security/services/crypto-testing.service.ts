import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { KeyManagementService } from './key-management.service';
import { EndToEndEncryptionService } from './end-to-end-encryption.service';
import { HomomorphicEncryptionService } from './homomorphic-encryption.service';
import { QuantumResistantService } from './quantum-resistant.service';
import { CryptoLoggingService } from './crypto-logging.service';
import { CryptoPerformanceService } from './crypto-performance.service';
import { AdvancedHomomorphicService } from './advanced-homomorphic.service';

/**
 * Interface pour les résultats de test
 */
export interface TestResult {
  service: string;
  test: string;
  success: boolean;
  executionTimeMs: number;
  error?: string;
  details?: Record<string, any>;
}

/**
 * Interface pour le rapport de test
 */
export interface TestReport {
  timestamp: Date;
  totalTests: number;
  successfulTests: number;
  failedTests: number;
  executionTimeMs: number;
  results: TestResult[];
}

/**
 * Service de test automatisé pour les services de chiffrement
 * Ce service fournit des méthodes pour tester automatiquement les services de chiffrement
 * et générer des rapports de test
 */
@Injectable()
export class CryptoTestingService {
  private readonly logger = new Logger(CryptoTestingService.name);
  private readonly enabled: boolean;
  private readonly autoTestIntervalMs: number;
  private readonly testDataSize: number;
  private readonly testTimeout: number;
  private autoTestInterval: NodeJS.Timeout | null = null;

  constructor(
    private readonly configService: ConfigService,
    private readonly keyManagementService: KeyManagementService,
    private readonly e2eEncryptionService: EndToEndEncryptionService,
    private readonly homomorphicEncryptionService: HomomorphicEncryptionService,
    private readonly quantumResistantService: QuantumResistantService,
    private readonly advancedHomomorphicService: AdvancedHomomorphicService,
    private readonly cryptoLoggingService: CryptoLoggingService,
    private readonly cryptoPerformanceService: CryptoPerformanceService,
  ) {
    this.enabled = this.configService.get<boolean>('encryption.testing.enabled', true);
    this.autoTestIntervalMs = this.configService.get<number>(
      'encryption.testing.autoTestIntervalMs',
      3600000 // 1 heure
    );
    this.testDataSize = this.configService.get<number>('encryption.testing.testDataSize', 100);
    this.testTimeout = this.configService.get<number>('encryption.testing.testTimeout', 30000); // 30 secondes

    // Démarrer les tests automatiques si activés
    if (this.enabled && this.autoTestIntervalMs > 0) {
      this.startAutoTests();
    }
  }

  /**
   * Démarre les tests automatiques
   */
  private startAutoTests(): void {
    if (this.autoTestInterval) {
      clearInterval(this.autoTestInterval);
    }

    this.autoTestInterval = setInterval(async () => {
      try {
        const report = await this.runAllTests();
        this.logger.log(
          `Auto test completed: ${report.successfulTests}/${report.totalTests} tests passed`
        );
        
        // Journaliser les tests échoués
        if (report.failedTests > 0) {
          const failedTests = report.results.filter(result => !result.success);
          for (const test of failedTests) {
            this.logger.error(
              `Test failed: ${test.service}.${test.test} - ${test.error}`
            );
          }
        }
      } catch (error) {
        this.logger.error('Auto test failed', error);
      }
    }, this.autoTestIntervalMs);

    this.logger.log(`Auto tests started with interval: ${this.autoTestIntervalMs}ms`);
  }

  /**
   * Arrête les tests automatiques
   */
  private stopAutoTests(): void {
    if (this.autoTestInterval) {
      clearInterval(this.autoTestInterval);
      this.autoTestInterval = null;
      this.logger.log('Auto tests stopped');
    }
  }

  /**
   * Exécute tous les tests
   * @returns Rapport de test
   */
  async runAllTests(): Promise<TestReport> {
    const startTime = Date.now();
    const results: TestResult[] = [];

    try {
      // Tester le service de gestion des clés
      results.push(...await this.testKeyManagementService());
      
      // Tester le service de chiffrement de bout en bout
      results.push(...await this.testEndToEndEncryptionService());
      
      // Tester le service de chiffrement homomorphique
      if (this.homomorphicEncryptionService.isEnabled()) {
        results.push(...await this.testHomomorphicEncryptionService());
        results.push(...await this.testAdvancedHomomorphicService());
      }
      
      // Tester le service de chiffrement résistant aux ordinateurs quantiques
      if (this.quantumResistantService.isEnabled()) {
        results.push(...await this.testQuantumResistantService());
      }
      
      // Calculer les statistiques
      const totalTests = results.length;
      const successfulTests = results.filter(result => result.success).length;
      const failedTests = totalTests - successfulTests;
      const executionTimeMs = Date.now() - startTime;
      
      return {
        timestamp: new Date(),
        totalTests,
        successfulTests,
        failedTests,
        executionTimeMs,
        results,
      };
    } catch (error) {
      this.logger.error('Failed to run all tests', error);
      throw error;
    }
  }

  /**
   * Teste le service de gestion des clés
   * @returns Résultats de test
   */
  private async testKeyManagementService(): Promise<TestResult[]> {
    const results: TestResult[] = [];
    
    // Test 1: Création de clé
    try {
      const startTime = Date.now();
      const keyId = await this.keyManagementService.createKey(
        'test',
        'aes-256-gcm',
        'encryption'
      );
      const executionTimeMs = Date.now() - startTime;
      
      results.push({
        service: 'KeyManagementService',
        test: 'createKey',
        success: !!keyId,
        executionTimeMs,
        details: { keyId },
      });
      
      // Test 2: Récupération de clé
      try {
        const startTime = Date.now();
        const key = await this.keyManagementService.getKey(keyId);
        const executionTimeMs = Date.now() - startTime;
        
        results.push({
          service: 'KeyManagementService',
          test: 'getKey',
          success: !!key && !!key.key && !!key.metadata,
          executionTimeMs,
          details: { keyId, metadata: key.metadata },
        });
      } catch (error) {
        results.push({
          service: 'KeyManagementService',
          test: 'getKey',
          success: false,
          executionTimeMs: Date.now() - startTime,
          error: error.message,
        });
      }
      
      // Test 3: Rotation de clé
      try {
        const startTime = Date.now();
        const newKeyId = await this.keyManagementService.rotateKey(keyId);
        const executionTimeMs = Date.now() - startTime;
        
        results.push({
          service: 'KeyManagementService',
          test: 'rotateKey',
          success: !!newKeyId && newKeyId !== keyId,
          executionTimeMs,
          details: { oldKeyId: keyId, newKeyId },
        });
      } catch (error) {
        results.push({
          service: 'KeyManagementService',
          test: 'rotateKey',
          success: false,
          executionTimeMs: Date.now() - startTime,
          error: error.message,
        });
      }
    } catch (error) {
      results.push({
        service: 'KeyManagementService',
        test: 'createKey',
        success: false,
        executionTimeMs: 0,
        error: error.message,
      });
    }
    
    return results;
  }

  /**
   * Teste le service de chiffrement de bout en bout
   * @returns Résultats de test
   */
  private async testEndToEndEncryptionService(): Promise<TestResult[]> {
    const results: TestResult[] = [];
    
    // Test 1: Génération de paire de clés
    let keyPair: { publicKey: string; privateKey: string };
    try {
      const startTime = Date.now();
      keyPair = await this.e2eEncryptionService.generateUserKeyPair();
      const executionTimeMs = Date.now() - startTime;
      
      results.push({
        service: 'EndToEndEncryptionService',
        test: 'generateUserKeyPair',
        success: !!keyPair && !!keyPair.publicKey && !!keyPair.privateKey,
        executionTimeMs,
      });
      
      // Test 2: Chiffrement de message
      try {
        const testMessage = 'This is a test message for E2E encryption';
        const startTime = Date.now();
        const encryptedMessage = await this.e2eEncryptionService.encryptMessage(
          testMessage,
          keyPair.publicKey
        );
        const executionTimeMs = Date.now() - startTime;
        
        results.push({
          service: 'EndToEndEncryptionService',
          test: 'encryptMessage',
          success: !!encryptedMessage,
          executionTimeMs,
        });
        
        // Test 3: Déchiffrement de message
        try {
          const startTime = Date.now();
          const decryptedMessage = await this.e2eEncryptionService.decryptMessage(
            encryptedMessage,
            keyPair.privateKey
          );
          const executionTimeMs = Date.now() - startTime;
          
          results.push({
            service: 'EndToEndEncryptionService',
            test: 'decryptMessage',
            success: decryptedMessage === testMessage,
            executionTimeMs,
            details: { original: testMessage, decrypted: decryptedMessage },
          });
        } catch (error) {
          results.push({
            service: 'EndToEndEncryptionService',
            test: 'decryptMessage',
            success: false,
            executionTimeMs: Date.now() - startTime,
            error: error.message,
          });
        }
      } catch (error) {
        results.push({
          service: 'EndToEndEncryptionService',
          test: 'encryptMessage',
          success: false,
          executionTimeMs: 0,
          error: error.message,
        });
      }
    } catch (error) {
      results.push({
        service: 'EndToEndEncryptionService',
        test: 'generateUserKeyPair',
        success: false,
        executionTimeMs: 0,
        error: error.message,
      });
    }
    
    return results;
  }

  /**
   * Teste le service de chiffrement homomorphique
   * @returns Résultats de test
   */
  private async testHomomorphicEncryptionService(): Promise<TestResult[]> {
    const results: TestResult[] = [];
    
    // Test 1: Chiffrement de données
    let encryptedData: Buffer;
    try {
      const testData = 'This is a test for homomorphic encryption';
      const startTime = Date.now();
      encryptedData = await this.homomorphicEncryptionService.encrypt(testData);
      const executionTimeMs = Date.now() - startTime;
      
      results.push({
        service: 'HomomorphicEncryptionService',
        test: 'encrypt',
        success: !!encryptedData,
        executionTimeMs,
      });
      
      // Test 2: Déchiffrement de données
      try {
        const startTime = Date.now();
        const decryptedData = await this.homomorphicEncryptionService.decrypt(encryptedData);
        const executionTimeMs = Date.now() - startTime;
        
        results.push({
          service: 'HomomorphicEncryptionService',
          test: 'decrypt',
          success: decryptedData.toString() === testData,
          executionTimeMs,
          details: { original: testData, decrypted: decryptedData.toString() },
        });
      } catch (error) {
        results.push({
          service: 'HomomorphicEncryptionService',
          test: 'decrypt',
          success: false,
          executionTimeMs: Date.now() - startTime,
          error: error.message,
        });
      }
      
      // Test 3: Addition homomorphique
      try {
        const value1 = 5;
        const value2 = 10;
        const encrypted1 = await this.homomorphicEncryptionService.encrypt(value1.toString());
        const encrypted2 = await this.homomorphicEncryptionService.encrypt(value2.toString());
        
        const startTime = Date.now();
        const encryptedSum = await this.homomorphicEncryptionService.add(encrypted1, encrypted2);
        const executionTimeMs = Date.now() - startTime;
        
        const decryptedSum = await this.homomorphicEncryptionService.decrypt(encryptedSum);
        const sumValue = parseInt(decryptedSum.toString(), 10);
        
        results.push({
          service: 'HomomorphicEncryptionService',
          test: 'add',
          success: sumValue === value1 + value2,
          executionTimeMs,
          details: { value1, value2, sum: sumValue, expected: value1 + value2 },
        });
      } catch (error) {
        results.push({
          service: 'HomomorphicEncryptionService',
          test: 'add',
          success: false,
          executionTimeMs: 0,
          error: error.message,
        });
      }
    } catch (error) {
      results.push({
        service: 'HomomorphicEncryptionService',
        test: 'encrypt',
        success: false,
        executionTimeMs: 0,
        error: error.message,
      });
    }
    
    return results;
  }

  /**
   * Teste le service de chiffrement homomorphique avancé
   * @returns Résultats de test
   */
  private async testAdvancedHomomorphicService(): Promise<TestResult[]> {
    const results: TestResult[] = [];
    
    // Test 1: Chiffrement d'un ensemble de données
    let encryptedDataset: Buffer[];
    try {
      const testData = [1, 2, 3, 4, 5];
      const startTime = Date.now();
      encryptedDataset = await this.advancedHomomorphicService.encryptDataset(testData);
      const executionTimeMs = Date.now() - startTime;
      
      results.push({
        service: 'AdvancedHomomorphicService',
        test: 'encryptDataset',
        success: !!encryptedDataset && encryptedDataset.length === testData.length,
        executionTimeMs,
        details: { dataSize: testData.length },
      });
      
      // Test 2: Opération d'addition
      try {
        const startTime = Date.now();
        const additionResult = await this.advancedHomomorphicService.performOperation({
          type: 'add',
          operands: encryptedDataset,
        });
        const executionTimeMs = Date.now() - startTime;
        
        const decryptedResult = await this.homomorphicEncryptionService.decrypt(additionResult.result);
        const sumValue = parseInt(decryptedResult.toString(), 10);
        const expectedSum = testData.reduce((sum, value) => sum + value, 0);
        
        results.push({
          service: 'AdvancedHomomorphicService',
          test: 'performOperation-add',
          success: sumValue === expectedSum,
          executionTimeMs,
          details: {
            operationType: 'add',
            result: sumValue,
            expected: expectedSum,
            executionTimeMs: additionResult.metadata.executionTimeMs,
          },
        });
      } catch (error) {
        results.push({
          service: 'AdvancedHomomorphicService',
          test: 'performOperation-add',
          success: false,
          executionTimeMs: Date.now() - startTime,
          error: error.message,
        });
      }
      
      // Test 3: Opération de moyenne
      try {
        const startTime = Date.now();
        const averageResult = await this.advancedHomomorphicService.performOperation({
          type: 'average',
          operands: encryptedDataset,
        });
        const executionTimeMs = Date.now() - startTime;
        
        const decryptedResult = await this.homomorphicEncryptionService.decrypt(averageResult.result);
        const avgValue = parseInt(decryptedResult.toString(), 10);
        const expectedAvg = Math.floor(testData.reduce((sum, value) => sum + value, 0) / testData.length);
        
        results.push({
          service: 'AdvancedHomomorphicService',
          test: 'performOperation-average',
          success: avgValue === expectedAvg,
          executionTimeMs,
          details: {
            operationType: 'average',
            result: avgValue,
            expected: expectedAvg,
            executionTimeMs: averageResult.metadata.executionTimeMs,
          },
        });
      } catch (error) {
        results.push({
          service: 'AdvancedHomomorphicService',
          test: 'performOperation-average',
          success: false,
          executionTimeMs: Date.now() - startTime,
          error: error.message,
        });
      }
    } catch (error) {
      results.push({
        service: 'AdvancedHomomorphicService',
        test: 'encryptDataset',
        success: false,
        executionTimeMs: 0,
        error: error.message,
      });
    }
    
    return results;
  }

  /**
   * Teste le service de chiffrement résistant aux ordinateurs quantiques
   * @returns Résultats de test
   */
  private async testQuantumResistantService(): Promise<TestResult[]> {
    const results: TestResult[] = [];
    
    // Test 1: Génération de clés
    try {
      const startTime = Date.now();
      await this.quantumResistantService.generateKeys();
      const executionTimeMs = Date.now() - startTime;
      
      results.push({
        service: 'QuantumResistantService',
        test: 'generateKeys',
        success: true,
        executionTimeMs,
      });
      
      // Test 2: Chiffrement de données
      try {
        const testData = 'This is a test for quantum-resistant encryption';
        const startTime = Date.now();
        const encryptedData = await this.quantumResistantService.encrypt(testData);
        const executionTimeMs = Date.now() - startTime;
        
        results.push({
          service: 'QuantumResistantService',
          test: 'encrypt',
          success: !!encryptedData,
          executionTimeMs,
        });
        
        // Test 3: Déchiffrement de données
        try {
          const startTime = Date.now();
          const decryptedData = await this.quantumResistantService.decrypt(encryptedData);
          const executionTimeMs = Date.now() - startTime;
          
          results.push({
            service: 'QuantumResistantService',
            test: 'decrypt',
            success: decryptedData.toString() === testData,
            executionTimeMs,
            details: { original: testData, decrypted: decryptedData.toString() },
          });
        } catch (error) {
          results.push({
            service: 'QuantumResistantService',
            test: 'decrypt',
            success: false,
            executionTimeMs: Date.now() - startTime,
            error: error.message,
          });
        }
      } catch (error) {
        results.push({
          service: 'QuantumResistantService',
          test: 'encrypt',
          success: false,
          executionTimeMs: 0,
          error: error.message,
        });
      }
    } catch (error) {
      results.push({
        service: 'QuantumResistantService',
        test: 'generateKeys',
        success: false,
        executionTimeMs: 0,
        error: error.message,
      });
    }
    
    return results;
  }

  /**
   * Exécute des tests de performance
   * @param iterations Nombre d'itérations
   * @param dataSize Taille des données en octets
   * @returns Résultats de test
   */
  async runPerformanceTests(
    iterations: number = 100,
    dataSize: number = 1024
  ): Promise<TestReport> {
    const startTime = Date.now();
    const results: TestResult[] = [];
    
    try {
      // Générer des données de test
      const testData = crypto.randomBytes(dataSize).toString('base64');
      
      // Tester les performances du service de chiffrement de bout en bout
      results.push(...await this.testE2EPerformance(testData, iterations));
      
      // Tester les performances du service de chiffrement homomorphique
      if (this.homomorphicEncryptionService.isEnabled()) {
        results.push(...await this.testHomomorphicPerformance(testData, iterations));
      }
      
      // Tester les performances du service de chiffrement résistant aux ordinateurs quantiques
      if (this.quantumResistantService.isEnabled()) {
        results.push(...await this.testQuantumResistantPerformance(testData, iterations));
      }
      
      // Calculer les statistiques
      const totalTests = results.length;
      const successfulTests = results.filter(result => result.success).length;
      const failedTests = totalTests - successfulTests;
      const executionTimeMs = Date.now() - startTime;
      
      return {
        timestamp: new Date(),
        totalTests,
        successfulTests,
        failedTests,
        executionTimeMs,
        results,
      };
    } catch (error) {
      this.logger.error('Failed to run performance tests', error);
      throw error;
    }
  }

  /**
   * Teste les performances du service de chiffrement de bout en bout
   * @param testData Données de test
   * @param iterations Nombre d'itérations
   * @returns Résultats de test
   */
  private async testE2EPerformance(
    testData: string,
    iterations: number
  ): Promise<TestResult[]> {
    const results: TestResult[] = [];
    
    try {
      // Générer une paire de clés
      const keyPair = await this.e2eEncryptionService.generateUserKeyPair();
      
      // Test de chiffrement
      const encryptStartTime = Date.now();
      const encryptOperations = Array(iterations).fill(0).map(() => 
        () => this.e2eEncryptionService.encryptMessage(testData, keyPair.publicKey)
      );
      
      const encryptedMessages = await this.cryptoPerformanceService.executeBatch(encryptOperations);
      const encryptExecutionTimeMs = Date.now() - encryptStartTime;
      
      results.push({
        service: 'EndToEndEncryptionService',
        test: 'encrypt-performance',
        success: encryptedMessages.length === iterations,
        executionTimeMs: encryptExecutionTimeMs,
        details: {
          iterations,
          dataSize: testData.length,
          averageTimeMs: encryptExecutionTimeMs / iterations,
        },
      });
      
      // Test de déchiffrement
      const decryptStartTime = Date.now();
      const decryptOperations = encryptedMessages.map(encryptedMessage => 
        () => this.e2eEncryptionService.decryptMessage(encryptedMessage, keyPair.privateKey)
      );
      
      const decryptedMessages = await this.cryptoPerformanceService.executeBatch(decryptOperations);
      const decryptExecutionTimeMs = Date.now() - decryptStartTime;
      
      results.push({
        service: 'EndToEndEncryptionService',
        test: 'decrypt-performance',
        success: decryptedMessages.length === iterations && decryptedMessages.every(msg => msg === testData),
        executionTimeMs: decryptExecutionTimeMs,
        details: {
          iterations,
          dataSize: testData.length,
          averageTimeMs: decryptExecutionTimeMs / iterations,
        },
      });
    } catch (error) {
      results.push({
        service: 'EndToEndEncryptionService',
        test: 'performance',
        success: false,
        executionTimeMs: 0,
        error: error.message,
      });
    }
    
    return results;
  }

  /**
   * Teste les performances du service de chiffrement homomorphique
   * @param testData Données de test
   * @param iterations Nombre d'itérations
   * @returns Résultats de test
   */
  private async testHomomorphicPerformance(
    testData: string,
    iterations: number
  ): Promise<TestResult[]> {
    const results: TestResult[] = [];
    
    try {
      // Test de chiffrement
      const encryptStartTime = Date.now();
      const encryptOperations = Array(iterations).fill(0).map(() => 
        () => this.homomorphicEncryptionService.encrypt(testData)
      );
      
      const encryptedData = await this.cryptoPerformanceService.executeBatch(encryptOperations);
      const encryptExecutionTimeMs = Date.now() - encryptStartTime;
      
      results.push({
        service: 'HomomorphicEncryptionService',
        test: 'encrypt-performance',
        success: encryptedData.length === iterations,
        executionTimeMs: encryptExecutionTimeMs,
        details: {
          iterations,
          dataSize: testData.length,
          averageTimeMs: encryptExecutionTimeMs / iterations,
        },
      });
      
      // Test d'addition homomorphique
      if (encryptedData.length >= 2) {
        const addStartTime = Date.now();
        const addOperations = [];
        
        for (let i = 0; i < Math.floor(iterations / 2); i++) {
          addOperations.push(() => 
            this.homomorphicEncryptionService.add(encryptedData[i * 2], encryptedData[i * 2 + 1])
          );
        }
        
        const addResults = await this.cryptoPerformanceService.executeBatch(addOperations);
        const addExecutionTimeMs = Date.now() - addStartTime;
        
        results.push({
          service: 'HomomorphicEncryptionService',
          test: 'add-performance',
          success: addResults.length === Math.floor(iterations / 2),
          executionTimeMs: addExecutionTimeMs,
          details: {
            iterations: Math.floor(iterations / 2),
            averageTimeMs: addExecutionTimeMs / Math.floor(iterations / 2),
          },
        });
      }
    } catch (error) {
      results.push({
        service: 'HomomorphicEncryptionService',
        test: 'performance',
        success: false,
        executionTimeMs: 0,
        error: error.message,
      });
    }
    
    return results;
  }

  /**
   * Teste les performances du service de chiffrement résistant aux ordinateurs quantiques
   * @param testData Données de test
   * @param iterations Nombre d'itérations
   * @returns Résultats de test
   */
  private async testQuantumResistantPerformance(
    testData: string,
    iterations: number
  ): Promise<TestResult[]> {
    const results: TestResult[] = [];
    
    try {
      // Générer des clés
      await this.quantumResistantService.generateKeys();
      
      // Test de chiffrement
      const encryptStartTime = Date.now();
      const encryptOperations = Array(iterations).fill(0).map(() => 
        () => this.quantumResistantService.encrypt(testData)
      );
      
      const encryptedData = await this.cryptoPerformanceService.executeBatch(encryptOperations);
      const encryptExecutionTimeMs = Date.now() - encryptStartTime;
      
      results.push({
        service: 'QuantumResistantService',
        test: 'encrypt-performance',
        success: encryptedData.length === iterations,
        executionTimeMs: encryptExecutionTimeMs,
        details: {
          iterations,
          dataSize: testData.length,
          averageTimeMs: encryptExecutionTimeMs / iterations,
        },
      });
      
      // Test de déchiffrement
      const decryptStartTime = Date.now();
      const decryptOperations = encryptedData.map(data => 
        () => this.quantumResistantService.decrypt(data)
      );
      
      const decryptedData = await this.cryptoPerformanceService.executeBatch(decryptOperations);
      const decryptExecutionTimeMs = Date.now() - decryptStartTime;
      
      results.push({
        service: 'QuantumResistantService',
        test: 'decrypt-performance',
        success: decryptedData.length === iterations,
        executionTimeMs: decryptExecutionTimeMs,
        details: {
          iterations,
          dataSize: testData.length,
          averageTimeMs: decryptExecutionTimeMs / iterations,
        },
      });
    } catch (error) {
      results.push({
        service: 'QuantumResistantService',
        test: 'performance',
        success: false,
        executionTimeMs: 0,
        error: error.message,
      });
    }
    
    return results;
  }

  /**
   * Nettoie les ressources lors de l'arrêt de l'application
   */
  public onModuleDestroy(): void {
    this.stopAutoTests();
  }
}
