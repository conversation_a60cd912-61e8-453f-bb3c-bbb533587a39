import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';

@Injectable()
export class ContentSecurityService {
  async scanForVulnerabilities(quick?: boolean): Promise<any[]> {
    // Stub temporaire pour lever l'erreur
    return [];
  }
  private readonly logger = new Logger(ContentSecurityService.name);
  private cspDirectives: Record<string, string[]>;

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
  ) {
    this.initializeCSP();
  }

  private initializeCSP(): void {
    // Default CSP directives
    this.cspDirectives = {
      'default-src': ["'self'"],
      'script-src': ["'self'"],
      'style-src': ["'self'"],
      'img-src': ["'self'", 'data:'],
      'font-src': ["'self'"],
      'connect-src': ["'self'"],
      'media-src': ["'self'"],
      'object-src': ["'none'"],
      'frame-src': ["'self'"],
      'base-uri': ["'self'"],
      'form-action': ["'self'"],
      'frame-ancestors': ["'self'"],
      'upgrade-insecure-requests': [],
    };

    // Load custom CSP from config if available
    const customCSP = this.configService.get<Record<string, string[]>>('security.csp');
    if (customCSP) {
      this.cspDirectives = { ...this.cspDirectives, ...customCSP };
    }
  }

  getCSPHeader(): string {
    const directives = Object.entries(this.cspDirectives)
      .map(([directive, sources]) => {
        if (sources.length === 0) {
          return directive;
        }
        return `${directive} ${sources.join(' ')}`;
      })
      .join('; ');

    return directives;
  }

  addCSPDirective(directive: string, source: string): void {
    if (!this.cspDirectives[directive]) {
      this.cspDirectives[directive] = [];
    }

    if (!this.cspDirectives[directive].includes(source)) {
      this.cspDirectives[directive].push(source);
      this.logger.log(`Added CSP directive: ${directive} ${source}`);
    }
  }

  removeCSPDirective(directive: string, source: string): void {
    if (this.cspDirectives[directive]) {
      const index = this.cspDirectives[directive].indexOf(source);
      if (index !== -1) {
        this.cspDirectives[directive].splice(index, 1);
        this.logger.log(`Removed CSP directive: ${directive} ${source}`);
      }
    }
  }

  async logCSPViolation(report: any): Promise<void> {
    try {
      const violationData = {
        blockedURI: report['blocked-uri'] || report.blockedURI,
        documentURI: report['document-uri'] || report.documentURI,
        violatedDirective: report['violated-directive'] || report.violatedDirective,
        effectiveDirective: report['effective-directive'] || report.effectiveDirective,
        originalPolicy: report['original-policy'] || report.originalPolicy,
        disposition: report.disposition,
        referrer: report.referrer,
        timestamp: new Date(),
      };

      await this.prisma.securityEvent.create({
        data: {
          type: 'CSP_VIOLATION',
          source: 'content-security',
          details: JSON.stringify(violationData),
          severity: 'WARNING',
          timestamp: new Date(),
        },
      });

      this.logger.warn(`CSP violation: ${violationData.violatedDirective} at ${violationData.blockedURI}`);
    } catch (error) {
      this.logger.error(`Failed to log CSP violation: ${error.message}`, error.stack);
    }
  }
}
