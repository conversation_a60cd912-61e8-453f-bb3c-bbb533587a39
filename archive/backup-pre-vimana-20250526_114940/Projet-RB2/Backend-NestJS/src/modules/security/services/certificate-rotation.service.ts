import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as fs from 'fs/promises';
import * as fsSync from 'fs';
import * as path from 'path';
import * as child_process from 'child_process';
import { promisify } from 'util';
import { CertificateManagementService } from './certificate-management.service';

const exec = promisify(child_process.exec);

/**
 * Interface pour les résultats de validation de certificat
 */
export interface CertificateValidationResult {
  valid: boolean;
  details: {
    expiresIn?: number; // Jours avant expiration
    expiresAt?: Date; // Date d'expiration
    issuer?: string; // Émetteur du certificat
    subject?: string; // Sujet du certificat
    serialNumber?: string; // Numéro de série
    error?: string; // Message d'erreur en cas d'échec
  };
}

/**
 * Service de rotation automatique des certificats mTLS
 * Ce service gère la rotation automatique des certificats mTLS pour sécuriser les communications inter-services
 */
@Injectable()
export class CertificateRotationService implements OnModuleInit {
  private readonly logger = new Logger(CertificateRotationService.name);
  private readonly certPath: string;
  private readonly keyPath: string;
  private readonly caPath: string;
  private readonly certsDir: string;
  private readonly enableAutoRotation: boolean;
  private readonly rotationThresholdDays: number;
  private readonly backupDir: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly certificateManagementService: CertificateManagementService
  ) {
    this.certsDir = this.configService.get<string>('CERTS_DIR', './certs');
    this.certPath = this.configService.get<string>('MTLS_CERT_PATH', path.join(this.certsDir, 'server.crt'));
    this.keyPath = this.configService.get<string>('MTLS_KEY_PATH', path.join(this.certsDir, 'server.key'));
    this.caPath = this.configService.get<string>('MTLS_CA_PATH', path.join(this.certsDir, 'ca.crt'));
    this.enableAutoRotation = this.configService.get<boolean>('ENABLE_CERT_AUTO_ROTATION', true);
    this.rotationThresholdDays = this.configService.get<number>('CERT_ROTATION_THRESHOLD_DAYS', 30);
    this.backupDir = path.join(this.certsDir, 'backup');
  }

  /**
   * Initialisation du service
   */
  async onModuleInit() {
    try {
      // Créer le répertoire de backup s'il n'existe pas
      if (!fsSync.existsSync(this.backupDir)) {
        await fs.mkdir(this.backupDir, { recursive: true });
        this.logger.log(`Created certificate backup directory: ${this.backupDir}`);
      }

      // Vérifier les certificats au démarrage
      const validationResult = await this.validateCertificate(this.certPath);
      
      if (!validationResult.valid) {
        this.logger.warn(`Certificate validation failed: ${validationResult.details.error}`);
        
        if (this.enableAutoRotation) {
          this.logger.log('Attempting to generate new certificates...');
          await this.rotateCertificates();
        }
      } else {
        this.logger.log(`Certificate is valid. Expires in ${validationResult.details.expiresIn} days.`);
        
        // Si le certificat expire bientôt et que la rotation automatique est activée, le renouveler
        if (validationResult.details.expiresIn <= this.rotationThresholdDays && this.enableAutoRotation) {
          this.logger.log(`Certificate expires in ${validationResult.details.expiresIn} days. Rotating...`);
          await this.rotateCertificates();
        }
      }
    } catch (error) {
      this.logger.error('Failed to initialize certificate rotation service:', error);
    }
  }

  /**
   * Vérifie si les certificats existent
   */
  async checkCertificates(): Promise<boolean> {
    try {
      await fs.access(this.certPath);
      await fs.access(this.keyPath);
      await fs.access(this.caPath);
      return true;
    } catch (error) {
      this.logger.error(`Certificates not found: ${error.message}`);
      return false;
    }
  }

  /**
   * Valide un certificat et retourne des informations détaillées
   * @param certPath Chemin du certificat à valider
   */
  async validateCertificate(certPath: string): Promise<CertificateValidationResult> {
    try {
      // Vérifier si le certificat existe
      try {
        await fs.access(certPath);
      } catch (error) {
        return {
          valid: false,
          details: {
            error: `Certificate file not found: ${certPath}`
          }
        };
      }

      // Vérifier la validité du certificat avec OpenSSL
      const { stdout } = await exec(`openssl x509 -in "${certPath}" -text -noout`);
      
      // Extraire les informations du certificat
      const issuerMatch = stdout.match(/Issuer: (.+)/);
      const subjectMatch = stdout.match(/Subject: (.+)/);
      const serialNumberMatch = stdout.match(/Serial Number: (.+)/);
      const validityMatch = stdout.match(/Not Before: (.+)\s+Not After : (.+)/);
      
      if (!validityMatch) {
        return {
          valid: false,
          details: {
            error: 'Could not extract validity information from certificate'
          }
        };
      }
      
      const notAfterStr = validityMatch[2].trim();
      const expiresAt = new Date(notAfterStr);
      const now = new Date();
      
      // Calculer le nombre de jours avant expiration
      const expiresIn = Math.floor((expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      // Vérifier si le certificat est expiré
      if (expiresIn < 0) {
        return {
          valid: false,
          details: {
            expiresIn,
            expiresAt,
            issuer: issuerMatch ? issuerMatch[1] : undefined,
            subject: subjectMatch ? subjectMatch[1] : undefined,
            serialNumber: serialNumberMatch ? serialNumberMatch[1] : undefined,
            error: 'Certificate has expired'
          }
        };
      }
      
      // Vérifier la signature du certificat avec la CA
      try {
        await exec(`openssl verify -CAfile "${this.caPath}" "${certPath}"`);
      } catch (error) {
        return {
          valid: false,
          details: {
            expiresIn,
            expiresAt,
            issuer: issuerMatch ? issuerMatch[1] : undefined,
            subject: subjectMatch ? subjectMatch[1] : undefined,
            serialNumber: serialNumberMatch ? serialNumberMatch[1] : undefined,
            error: 'Certificate signature verification failed'
          }
        };
      }
      
      return {
        valid: true,
        details: {
          expiresIn,
          expiresAt,
          issuer: issuerMatch ? issuerMatch[1] : undefined,
          subject: subjectMatch ? subjectMatch[1] : undefined,
          serialNumber: serialNumberMatch ? serialNumberMatch[1] : undefined
        }
      };
    } catch (error) {
      this.logger.error(`Failed to validate certificate: ${error.message}`);
      return {
        valid: false,
        details: {
          error: `Validation error: ${error.message}`
        }
      };
    }
  }

  /**
   * Vérifie si un certificat doit être renouvelé
   * @param certPath Chemin du certificat à vérifier
   */
  async shouldRotateCertificate(certPath: string): Promise<boolean> {
    try {
      // Vérifier si le certificat existe
      try {
        await fs.access(certPath);
      } catch (error) {
        return true; // Le certificat n'existe pas, il faut le générer
      }
      
      // Valider le certificat
      const validationResult = await this.validateCertificate(certPath);
      
      if (!validationResult.valid) {
        return true; // Le certificat n'est pas valide, il faut le renouveler
      }
      
      // Vérifier si le certificat expire bientôt
      return validationResult.details.expiresIn <= this.rotationThresholdDays;
    } catch (error) {
      this.logger.error(`Error checking certificate rotation: ${error.message}`);
      return true; // En cas d'erreur, considérer que le certificat doit être renouvelé
    }
  }

  /**
   * Sauvegarde les certificats existants
   */
  private async backupCertificates(): Promise<string> {
    try {
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const backupDirWithTimestamp = path.join(this.backupDir, timestamp);
      
      await fs.mkdir(backupDirWithTimestamp, { recursive: true });
      
      // Vérifier si les certificats existent avant de les sauvegarder
      const certificatesExist = await this.checkCertificates();
      
      if (certificatesExist) {
        await fs.copyFile(this.certPath, path.join(backupDirWithTimestamp, path.basename(this.certPath)));
        await fs.copyFile(this.keyPath, path.join(backupDirWithTimestamp, path.basename(this.keyPath)));
        await fs.copyFile(this.caPath, path.join(backupDirWithTimestamp, path.basename(this.caPath)));
        
        this.logger.log(`Certificates backed up to ${backupDirWithTimestamp}`);
      } else {
        this.logger.log('No certificates to backup');
      }
      
      return backupDirWithTimestamp;
    } catch (error) {
      this.logger.error(`Failed to backup certificates: ${error.message}`);
      throw error;
    }
  }

  /**
   * Effectue la rotation des certificats
   */
  async rotateCertificates(): Promise<boolean> {
    try {
      this.logger.log('Starting certificate rotation');
      
      // Sauvegarder les certificats existants
      await this.backupCertificates();
      
      // Extraire le nom du service à partir du chemin du certificat
      const certName = path.basename(this.certPath, '.crt');
      
      // Générer de nouveaux certificats
      const { certPath, keyPath, caCertPath } = await this.certificateManagementService.generateServiceCertificates(certName);
      
      this.logger.log(`Certificate rotation completed successfully. New certificate: ${certPath}`);
      
      // Vérifier les nouveaux certificats
      const validationResult = await this.validateCertificate(certPath);
      
      if (!validationResult.valid) {
        this.logger.error(`New certificate validation failed: ${validationResult.details.error}`);
        return false;
      }
      
      this.logger.log(`New certificate is valid. Expires at ${validationResult.details.expiresAt}`);
      return true;
    } catch (error) {
      this.logger.error(`Certificate rotation failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Planifie la rotation automatique des certificats
   * Cette méthode est exécutée tous les jours à minuit
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async scheduledCertificateRotation(): Promise<void> {
    if (!this.enableAutoRotation) {
      this.logger.debug('Automatic certificate rotation is disabled');
      return;
    }
    
    try {
      const shouldRotate = await this.shouldRotateCertificate(this.certPath);
      
      if (shouldRotate) {
        this.logger.log('Scheduled certificate rotation triggered');
        await this.rotateCertificates();
      } else {
        this.logger.debug('Certificates do not need rotation yet');
      }
    } catch (error) {
      this.logger.error(`Scheduled certificate rotation failed: ${error.message}`);
    }
  }

  /**
   * Vérifie l'état de santé des certificats
   * @returns Résultat de la validation des certificats
   */
  async checkCertificateHealth(): Promise<CertificateValidationResult> {
    try {
      const certificatesExist = await this.checkCertificates();
      
      if (!certificatesExist) {
        return {
          valid: false,
          details: {
            error: 'Certificates not found'
          }
        };
      }
      
      return await this.validateCertificate(this.certPath);
    } catch (error) {
      this.logger.error(`Certificate health check failed: ${error.message}`);
      return {
        valid: false,
        details: {
          error: `Health check error: ${error.message}`
        }
      };
    }
  }

  /**
   * Restaure des certificats à partir d'une sauvegarde
   * @param backupDir Répertoire de sauvegarde
   */
  async restoreCertificatesFromBackup(backupDir: string): Promise<boolean> {
    try {
      this.logger.log(`Restoring certificates from backup: ${backupDir}`);
      
      // Vérifier si le répertoire de sauvegarde existe
      try {
        await fs.access(backupDir);
      } catch (error) {
        this.logger.error(`Backup directory not found: ${backupDir}`);
        return false;
      }
      
      // Récupérer les noms de fichiers des certificats actuels
      const certFileName = path.basename(this.certPath);
      const keyFileName = path.basename(this.keyPath);
      const caFileName = path.basename(this.caPath);
      
      // Vérifier si les fichiers de sauvegarde existent
      const backupCertPath = path.join(backupDir, certFileName);
      const backupKeyPath = path.join(backupDir, keyFileName);
      const backupCaPath = path.join(backupDir, caFileName);
      
      try {
        await fs.access(backupCertPath);
        await fs.access(backupKeyPath);
        await fs.access(backupCaPath);
      } catch (error) {
        this.logger.error(`Backup files not found in ${backupDir}`);
        return false;
      }
      
      // Sauvegarder les certificats actuels avant de les remplacer
      await this.backupCertificates();
      
      // Restaurer les certificats depuis la sauvegarde
      await fs.copyFile(backupCertPath, this.certPath);
      await fs.copyFile(backupKeyPath, this.keyPath);
      await fs.copyFile(backupCaPath, this.caPath);
      
      this.logger.log(`Certificates restored from ${backupDir}`);
      
      // Vérifier les certificats restaurés
      const validationResult = await this.validateCertificate(this.certPath);
      
      if (!validationResult.valid) {
        this.logger.error(`Restored certificate validation failed: ${validationResult.details.error}`);
        return false;
      }
      
      this.logger.log(`Restored certificate is valid. Expires at ${validationResult.details.expiresAt}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to restore certificates: ${error.message}`);
      return false;
    }
  }

  /**
   * Liste les sauvegardes de certificats disponibles
   */
  async listCertificateBackups(): Promise<string[]> {
    try {
      const backups = await fs.readdir(this.backupDir);
      return backups.sort().reverse(); // Trier par ordre chronologique inversé (plus récent en premier)
    } catch (error) {
      this.logger.error(`Failed to list certificate backups: ${error.message}`);
      return [];
    }
  }
}
