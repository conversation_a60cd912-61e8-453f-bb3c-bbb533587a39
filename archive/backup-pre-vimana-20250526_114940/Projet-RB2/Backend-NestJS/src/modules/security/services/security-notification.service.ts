import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventsService } from '../../events/events.service';
import { SecurityEventService } from './security-event.service';
import { SecurityEventSeverity } from '../dto/create-security-event.dto';

/**
 * Interface pour les notifications de sécurité
 */
export interface SecurityNotification {
  id: string;
  userId: string;
  title: string;
  message: string;
  severity: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';
  type: string;
  read: boolean;
  createdAt: Date;
  readAt?: Date;
  actionRequired: boolean;
  actionUrl?: string;
  actionText?: string;
  eventId?: string;
}

/**
 * Service pour gérer les notifications de sécurité
 */
@Injectable()
export class SecurityNotificationService {
  private readonly logger = new Logger(SecurityNotificationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventsService: EventsService,
    private readonly securityEventService: SecurityEventService,
  ) {
    // S'abonner aux événements de sécurité pour créer des notifications
    this.subscribeToSecurityEvents();
  }

  /**
   * S'abonne aux événements de sécurité pour créer des notifications
   */
  private subscribeToSecurityEvents(): void {
    this.eventsService.subscribe('security.*', async (event) => {
      try {
        // Vérifier si l'événement nécessite une notification
        if (this.shouldCreateNotification(event)) {
          await this.createNotificationFromEvent(event);
        }
      } catch (error) {
        this.logger.error(`Error processing security event: ${error.message}`, error.stack);
      }
    });
  }

  /**
   * Détermine si un événement de sécurité doit générer une notification
   * @param event Événement de sécurité
   * @returns true si l'événement doit générer une notification
   */
  private shouldCreateNotification(event: any): boolean {
    // Liste des types d'événements qui nécessitent une notification
    const notifiableEvents = [
      'FAILED_LOGIN',
      'PASSWORD_CHANGED',
      'ACCOUNT_LOCKED',
      'SUSPICIOUS_ACTIVITY',
      'MALWARE_DETECTED',
      'SECURITY_SCAN_COMPLETED',
      'VULNERABILITY_DETECTED',
      'TRAINING_SIMULATION_PASSED',
      'TRAINING_SIMULATION_FAILED',
    ];

    // Vérifier si le type d'événement est dans la liste
    return notifiableEvents.includes(event.eventType);
  }

  /**
   * Crée une notification à partir d'un événement de sécurité
   * @param event Événement de sécurité
   */
  private async createNotificationFromEvent(event: any): Promise<void> {
    try {
      // Extraire les données de l'événement
      const { eventType, severity, details, userId } = event;

      // Si l'événement n'a pas d'utilisateur associé, ne pas créer de notification
      if (!userId) {
        return;
      }

      // Déterminer le titre et le message en fonction du type d'événement
      const { title, message, actionRequired, actionUrl, actionText } = this.getNotificationContent(eventType, details);

      // Créer la notification
      await this.createNotification({
        userId,
        title,
        message,
        severity: this.mapSeverity(severity),
        type: eventType,
        actionRequired,
        actionUrl,
        actionText,
        eventId: event.id,
      });
    } catch (error) {
      this.logger.error(`Error creating notification from event: ${error.message}`, error.stack);
    }
  }

  /**
   * Mappe la sévérité d'un événement de sécurité à la sévérité d'une notification
   * @param severity Sévérité de l'événement
   * @returns Sévérité de la notification
   */
  private mapSeverity(severity: SecurityEventSeverity): 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL' {
    switch (severity) {
      case SecurityEventSeverity.INFO:
        return 'INFO';
      case SecurityEventSeverity.WARNING:
        return 'WARNING';
      case SecurityEventSeverity.ERROR:
        return 'ERROR';
      case SecurityEventSeverity.CRITICAL:
        return 'CRITICAL';
      default:
        return 'INFO';
    }
  }

  /**
   * Détermine le contenu de la notification en fonction du type d'événement
   * @param eventType Type d'événement
   * @param details Détails de l'événement
   * @returns Contenu de la notification
   */
  private getNotificationContent(eventType: string, details: any): {
    title: string;
    message: string;
    actionRequired: boolean;
    actionUrl?: string;
    actionText?: string;
  } {
    switch (eventType) {
      case 'FAILED_LOGIN':
        return {
          title: 'Tentative de connexion échouée',
          message: `Une tentative de connexion à votre compte a échoué depuis l'adresse IP ${details.ip || 'inconnue'}.`,
          actionRequired: false,
        };
      case 'PASSWORD_CHANGED':
        return {
          title: 'Mot de passe modifié',
          message: 'Votre mot de passe a été modifié avec succès.',
          actionRequired: false,
        };
      case 'ACCOUNT_LOCKED':
        return {
          title: 'Compte verrouillé',
          message: 'Votre compte a été verrouillé suite à plusieurs tentatives de connexion échouées.',
          actionRequired: true,
          actionUrl: '/account/unlock',
          actionText: 'Déverrouiller le compte',
        };
      case 'SUSPICIOUS_ACTIVITY':
        return {
          title: 'Activité suspecte détectée',
          message: `Une activité suspecte a été détectée sur votre compte: ${details.description || 'activité non spécifiée'}.`,
          actionRequired: true,
          actionUrl: '/security/activity',
          actionText: 'Vérifier l\'activité',
        };
      case 'MALWARE_DETECTED':
        return {
          title: 'Malware détecté',
          message: `Un malware a été détecté dans le fichier "${details.filename || 'non spécifié'}".`,
          actionRequired: false,
        };
      case 'SECURITY_SCAN_COMPLETED':
        return {
          title: 'Scan de sécurité terminé',
          message: `Le scan de sécurité est terminé. ${details.vulnerabilityCount || 0} vulnérabilités détectées.`,
          actionRequired: details.vulnerabilityCount > 0,
          actionUrl: '/security/vulnerabilities',
          actionText: 'Voir les vulnérabilités',
        };
      case 'VULNERABILITY_DETECTED':
        return {
          title: 'Vulnérabilité détectée',
          message: `Une vulnérabilité a été détectée: ${details.description || 'non spécifiée'}.`,
          actionRequired: true,
          actionUrl: '/security/vulnerabilities',
          actionText: 'Voir les détails',
        };
      case 'TRAINING_SIMULATION_PASSED':
        return {
          title: 'Simulation de sécurité réussie',
          message: `Félicitations ! Vous avez réussi la simulation de sécurité "${details.simulationType || 'non spécifiée'}" avec un score de ${details.score || 0}%.`,
          actionRequired: false,
          actionUrl: '/security/training',
          actionText: 'Voir toutes les formations',
        };
      case 'TRAINING_SIMULATION_FAILED':
        return {
          title: 'Simulation de sécurité échouée',
          message: `Vous n'avez pas réussi la simulation de sécurité "${details.simulationType || 'non spécifiée'}". Votre score est de ${details.score || 0}%.`,
          actionRequired: true,
          actionUrl: '/security/training',
          actionText: 'Réessayer',
        };
      default:
        return {
          title: 'Notification de sécurité',
          message: 'Une notification de sécurité a été générée pour votre compte.',
          actionRequired: false,
        };
    }
  }

  /**
   * Crée une notification de sécurité
   * @param data Données de la notification
   * @returns La notification créée
   */
  async createNotification(data: {
    userId: string;
    title: string;
    message: string;
    severity: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';
    type: string;
    actionRequired: boolean;
    actionUrl?: string;
    actionText?: string;
    eventId?: string;
  }): Promise<SecurityNotification> {
    try {
      const notification = await this.prisma.securityNotification.create({
        data: {
          userId: data.userId,
          title: data.title,
          message: data.message,
          severity: data.severity,
          type: data.type,
          read: false,
          actionRequired: data.actionRequired,
          actionUrl: data.actionUrl,
          actionText: data.actionText,
          eventId: data.eventId,
        },
      });

      // Émettre un événement pour la notification
      await this.eventsService.create({
        eventType: 'SECURITY_NOTIFICATION_CREATED',
        payload: {
          notificationId: notification.id,
          userId: notification.userId,
          title: notification.title,
          severity: notification.severity,
        },
        status: 'COMPLETED',
      });

      return notification;
    } catch (error) {
      this.logger.error(`Error creating security notification: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Récupère les notifications de sécurité d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param options Options de pagination et de filtrage
   * @returns Liste des notifications
   */
  async getUserNotifications(
    userId: string,
    options: {
      page?: number;
      limit?: number;
      unreadOnly?: boolean;
      severity?: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';
    } = {},
  ): Promise<{ notifications: SecurityNotification[]; total: number; unreadCount: number }> {
    try {
      const { page = 1, limit = 10, unreadOnly = false, severity } = options;
      const skip = (page - 1) * limit;

      // Construire la clause where
      const where: any = { userId };
      if (unreadOnly) {
        where.read = false;
      }
      if (severity) {
        where.severity = severity;
      }

      // Récupérer les notifications
      const [notifications, total, unreadCount] = await Promise.all([
        this.prisma.securityNotification.findMany({
          where,
          skip,
          take: limit,
          orderBy: {
            createdAt: 'desc',
          },
        }),
        this.prisma.securityNotification.count({ where }),
        this.prisma.securityNotification.count({
          where: {
            userId,
            read: false,
          },
        }),
      ]);

      return { notifications, total, unreadCount };
    } catch (error) {
      this.logger.error(`Error getting user notifications: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Marque une notification comme lue
   * @param notificationId ID de la notification
   * @param userId ID de l'utilisateur
   * @returns La notification mise à jour
   */
  async markNotificationAsRead(notificationId: string, userId: string): Promise<SecurityNotification> {
    try {
      return await this.prisma.securityNotification.update({
        where: {
          id: notificationId,
          userId,
        },
        data: {
          read: true,
          readAt: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(`Error marking notification as read: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Marque toutes les notifications d'un utilisateur comme lues
   * @param userId ID de l'utilisateur
   * @returns Nombre de notifications mises à jour
   */
  async markAllNotificationsAsRead(userId: string): Promise<number> {
    try {
      const result = await this.prisma.securityNotification.updateMany({
        where: {
          userId,
          read: false,
        },
        data: {
          read: true,
          readAt: new Date(),
        },
      });

      return result.count;
    } catch (error) {
      this.logger.error(`Error marking all notifications as read: ${error.message}`, error.stack);
      throw error;
    }
  }
}
