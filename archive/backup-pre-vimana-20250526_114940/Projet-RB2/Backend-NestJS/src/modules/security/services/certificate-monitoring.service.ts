import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as fs from 'fs/promises';
import * as fsSync from 'fs';
import * as path from 'path';
import * as child_process from 'child_process';
import { promisify } from 'util';
import { CertificateRotationService, CertificateValidationResult } from './certificate-rotation.service';
import { CertificateRevocationService } from './certificate-revocation.service';

const exec = promisify(child_process.exec);

/**
 * Interface pour les métriques de certificat
 */
export interface CertificateMetrics {
  totalCertificates: number;
  validCertificates: number;
  expiringCertificates: number;
  expiredCertificates: number;
  revokedCertificates: number;
  averageExpiryDays: number;
  certificatesByService: Record<string, number>;
  rotationHistory: Array<{
    timestamp: Date;
    service: string;
    success: boolean;
  }>;
  lastUpdated: Date;
}

/**
 * Interface pour les informations détaillées d'un certificat
 */
export interface CertificateInfo {
  service: string;
  path: string;
  serialNumber: string;
  subject: string;
  issuer: string;
  validFrom: Date;
  validTo: Date;
  expiresIn: number;
  isValid: boolean;
  isRevoked: boolean;
  lastChecked: Date;
}

/**
 * Service de surveillance des certificats
 * Ce service surveille l'état des certificats et fournit des métriques et des alertes
 */
@Injectable()
export class CertificateMonitoringService implements OnModuleInit {
  private readonly logger = new Logger(CertificateMonitoringService.name);
  private readonly certsDir: string;
  private readonly metricsPath: string;
  private readonly alertThresholdDays: number;
  private readonly scanIntervalHours: number;
  private metrics: CertificateMetrics;
  private certificates: Map<string, CertificateInfo> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly certificateRotationService: CertificateRotationService,
    private readonly certificateRevocationService: CertificateRevocationService
  ) {
    this.certsDir = this.configService.get<string>('CERTS_DIR', './certs');
    this.metricsPath = path.join(this.certsDir, 'metrics.json');
    this.alertThresholdDays = this.configService.get<number>('CERT_ALERT_THRESHOLD_DAYS', 14);
    this.scanIntervalHours = this.configService.get<number>('CERT_SCAN_INTERVAL_HOURS', 6);
    
    // Initialiser les métriques
    this.metrics = {
      totalCertificates: 0,
      validCertificates: 0,
      expiringCertificates: 0,
      expiredCertificates: 0,
      revokedCertificates: 0,
      averageExpiryDays: 0,
      certificatesByService: {},
      rotationHistory: [],
      lastUpdated: new Date()
    };
  }

  /**
   * Initialisation du service
   */
  async onModuleInit() {
    try {
      // Charger les métriques existantes si disponibles
      await this.loadMetrics();
      
      // Effectuer un scan initial des certificats
      await this.scanCertificates();
    } catch (error) {
      this.logger.error('Failed to initialize certificate monitoring service:', error);
    }
  }

  /**
   * Charge les métriques depuis le fichier
   */
  private async loadMetrics(): Promise<void> {
    try {
      if (fsSync.existsSync(this.metricsPath)) {
        const metricsData = await fs.readFile(this.metricsPath, 'utf-8');
        const loadedMetrics = JSON.parse(metricsData);
        
        // Convertir les chaînes de date en objets Date
        loadedMetrics.lastUpdated = new Date(loadedMetrics.lastUpdated);
        loadedMetrics.rotationHistory = loadedMetrics.rotationHistory.map(entry => ({
          ...entry,
          timestamp: new Date(entry.timestamp)
        }));
        
        this.metrics = loadedMetrics;
        this.logger.log('Loaded certificate metrics from file');
      }
    } catch (error) {
      this.logger.error('Failed to load certificate metrics:', error);
    }
  }

  /**
   * Sauvegarde les métriques dans le fichier
   */
  private async saveMetrics(): Promise<void> {
    try {
      await fs.writeFile(this.metricsPath, JSON.stringify(this.metrics, null, 2));
      this.logger.debug('Saved certificate metrics to file');
    } catch (error) {
      this.logger.error('Failed to save certificate metrics:', error);
    }
  }

  /**
   * Scanne tous les certificats dans le répertoire des certificats
   */
  async scanCertificates(): Promise<void> {
    try {
      this.logger.log('Scanning certificates...');
      
      // Réinitialiser les compteurs
      this.metrics.totalCertificates = 0;
      this.metrics.validCertificates = 0;
      this.metrics.expiringCertificates = 0;
      this.metrics.expiredCertificates = 0;
      this.metrics.revokedCertificates = 0;
      this.metrics.certificatesByService = {};
      this.certificates.clear();
      
      let totalExpiryDays = 0;
      
      // Lister tous les fichiers dans le répertoire des certificats
      const files = await fs.readdir(this.certsDir);
      
      // Filtrer les fichiers de certificat (.crt)
      const certFiles = files.filter(file => file.endsWith('.crt') && file !== 'ca.crt');
      
      // Analyser chaque certificat
      for (const certFile of certFiles) {
        const certPath = path.join(this.certsDir, certFile);
        const serviceName = path.basename(certFile, '.crt');
        
        try {
          // Valider le certificat
          const validationResult = await this.certificateRotationService.validateCertificate(certPath);
          
          // Vérifier si le certificat est révoqué
          const isRevoked = await this.certificateRevocationService.isCertificateRevoked(certPath);
          
          // Extraire les informations du certificat
          const certInfo = await this.extractCertificateInfo(certPath, validationResult, isRevoked);
          
          if (certInfo) {
            this.certificates.set(serviceName, certInfo);
            
            // Mettre à jour les métriques
            this.metrics.totalCertificates++;
            
            if (certInfo.isValid && !certInfo.isRevoked) {
              this.metrics.validCertificates++;
            }
            
            if (certInfo.expiresIn <= this.alertThresholdDays && certInfo.expiresIn > 0) {
              this.metrics.expiringCertificates++;
            }
            
            if (certInfo.expiresIn <= 0) {
              this.metrics.expiredCertificates++;
            }
            
            if (certInfo.isRevoked) {
              this.metrics.revokedCertificates++;
            }
            
            // Mettre à jour les certificats par service
            if (!this.metrics.certificatesByService[serviceName]) {
              this.metrics.certificatesByService[serviceName] = 0;
            }
            this.metrics.certificatesByService[serviceName]++;
            
            // Ajouter à la somme des jours d'expiration pour calculer la moyenne
            if (certInfo.expiresIn > 0) {
              totalExpiryDays += certInfo.expiresIn;
            }
          }
        } catch (error) {
          this.logger.error(`Failed to analyze certificate ${certFile}:`, error);
        }
      }
      
      // Calculer la moyenne des jours d'expiration
      const validCertsCount = this.metrics.validCertificates - this.metrics.expiredCertificates;
      this.metrics.averageExpiryDays = validCertsCount > 0 ? Math.round(totalExpiryDays / validCertsCount) : 0;
      
      // Mettre à jour la date de dernière mise à jour
      this.metrics.lastUpdated = new Date();
      
      // Sauvegarder les métriques
      await this.saveMetrics();
      
      this.logger.log(`Certificate scan completed. Found ${this.metrics.totalCertificates} certificates.`);
    } catch (error) {
      this.logger.error('Failed to scan certificates:', error);
    }
  }

  /**
   * Extrait les informations détaillées d'un certificat
   * @param certPath Chemin du certificat
   * @param validationResult Résultat de la validation du certificat
   * @param isRevoked Indique si le certificat est révoqué
   * @returns Informations détaillées du certificat
   */
  private async extractCertificateInfo(
    certPath: string,
    validationResult: CertificateValidationResult,
    isRevoked: boolean
  ): Promise<CertificateInfo | null> {
    try {
      // Extraire les informations du certificat avec OpenSSL
      const { stdout } = await exec(`openssl x509 -in "${certPath}" -noout -text -serial`);
      
      // Extraire le numéro de série
      const serialMatch = stdout.match(/serial=([0-9A-Fa-f]+)/);
      const serialNumber = serialMatch ? serialMatch[1] : 'unknown';
      
      // Extraire le sujet
      const subjectMatch = stdout.match(/Subject: (.+)/);
      const subject = subjectMatch ? subjectMatch[1] : 'unknown';
      
      // Extraire l'émetteur
      const issuerMatch = stdout.match(/Issuer: (.+)/);
      const issuer = issuerMatch ? issuerMatch[1] : 'unknown';
      
      // Extraire les dates de validité
      const validityMatch = stdout.match(/Not Before: (.+)\s+Not After : (.+)/);
      const validFrom = validityMatch ? new Date(validityMatch[1]) : new Date();
      const validTo = validityMatch ? new Date(validityMatch[2]) : new Date();
      
      // Calculer le nombre de jours avant expiration
      const now = new Date();
      const expiresIn = Math.floor((validTo.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      return {
        service: path.basename(certPath, '.crt'),
        path: certPath,
        serialNumber,
        subject,
        issuer,
        validFrom,
        validTo,
        expiresIn,
        isValid: validationResult.valid,
        isRevoked,
        lastChecked: new Date()
      };
    } catch (error) {
      this.logger.error(`Failed to extract certificate info for ${certPath}:`, error);
      return null;
    }
  }

  /**
   * Enregistre une rotation de certificat dans l'historique
   * @param service Nom du service
   * @param success Indique si la rotation a réussi
   */
  async recordCertificateRotation(service: string, success: boolean): Promise<void> {
    try {
      // Ajouter l'entrée à l'historique de rotation
      this.metrics.rotationHistory.push({
        timestamp: new Date(),
        service,
        success
      });
      
      // Limiter la taille de l'historique à 100 entrées
      if (this.metrics.rotationHistory.length > 100) {
        this.metrics.rotationHistory = this.metrics.rotationHistory.slice(-100);
      }
      
      // Sauvegarder les métriques
      await this.saveMetrics();
      
      // Mettre à jour les informations des certificats
      await this.scanCertificates();
    } catch (error) {
      this.logger.error(`Failed to record certificate rotation for ${service}:`, error);
    }
  }

  /**
   * Obtient les métriques des certificats
   * @returns Métriques des certificats
   */
  getMetrics(): CertificateMetrics {
    return this.metrics;
  }

  /**
   * Obtient les informations détaillées de tous les certificats
   * @returns Liste des informations détaillées des certificats
   */
  getAllCertificatesInfo(): CertificateInfo[] {
    return Array.from(this.certificates.values());
  }

  /**
   * Obtient les informations détaillées d'un certificat
   * @param service Nom du service
   * @returns Informations détaillées du certificat ou null si le certificat n'existe pas
   */
  getCertificateInfo(service: string): CertificateInfo | null {
    return this.certificates.get(service) || null;
  }

  /**
   * Obtient la liste des certificats expirant bientôt
   * @param thresholdDays Nombre de jours avant expiration (par défaut: valeur configurée)
   * @returns Liste des certificats expirant bientôt
   */
  getExpiringCertificates(thresholdDays?: number): CertificateInfo[] {
    const threshold = thresholdDays || this.alertThresholdDays;
    return Array.from(this.certificates.values()).filter(
      cert => cert.expiresIn <= threshold && cert.expiresIn > 0 && !cert.isRevoked
    );
  }

  /**
   * Obtient la liste des certificats expirés
   * @returns Liste des certificats expirés
   */
  getExpiredCertificates(): CertificateInfo[] {
    return Array.from(this.certificates.values()).filter(
      cert => cert.expiresIn <= 0 && !cert.isRevoked
    );
  }

  /**
   * Obtient la liste des certificats révoqués
   * @returns Liste des certificats révoqués
   */
  getRevokedCertificates(): CertificateInfo[] {
    return Array.from(this.certificates.values()).filter(
      cert => cert.isRevoked
    );
  }

  /**
   * Scanne périodiquement les certificats
   * Cette méthode est exécutée selon l'intervalle configuré
   */
  @Cron(CronExpression.EVERY_HOUR)
  async scheduledCertificateScan(): Promise<void> {
    try {
      // Vérifier si c'est le moment de scanner les certificats
      const now = new Date();
      const hoursSinceLastUpdate = (now.getTime() - this.metrics.lastUpdated.getTime()) / (1000 * 60 * 60);
      
      if (hoursSinceLastUpdate >= this.scanIntervalHours) {
        this.logger.log('Scheduled certificate scan triggered');
        await this.scanCertificates();
      }
    } catch (error) {
      this.logger.error('Scheduled certificate scan failed:', error);
    }
  }
}
