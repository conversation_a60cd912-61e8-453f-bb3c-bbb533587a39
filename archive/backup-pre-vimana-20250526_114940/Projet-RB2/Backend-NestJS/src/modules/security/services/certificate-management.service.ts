import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import * as child_process from 'child_process';
import { promisify } from 'util';

const exec = promisify(child_process.exec);

/**
 * Interface pour les options de génération de certificat
 */
export interface CertificateOptions {
  commonName: string;
  organization: string;
  organizationalUnit: string;
  country: string;
  state: string;
  locality: string;
  emailAddress: string;
  validityDays: number;
  keySize: number;
  isCA: boolean;
}

/**
 * Service de gestion des certificats
 * Ce service fournit des méthodes pour générer et gérer des certificats X.509
 * pour l'authentification mTLS
 */
@Injectable()
export class CertificateManagementService {
  private readonly logger = new Logger(CertificateManagementService.name);
  private readonly certsDir: string;

  constructor(private readonly configService: ConfigService) {
    this.certsDir = this.configService.get<string>('CERTS_DIR', './certs');
    this.ensureCertsDirectory();
  }

  /**
   * S'assure que le répertoire des certificats existe
   */
  private ensureCertsDirectory(): void {
    try {
      if (!fs.existsSync(this.certsDir)) {
        fs.mkdirSync(this.certsDir, { recursive: true });
        this.logger.log(`Created certificates directory: ${this.certsDir}`);
      }
    } catch (error) {
      this.logger.error('Failed to create certificates directory:', error);
      throw error;
    }
  }

  /**
   * Génère une autorité de certification (CA)
   * @param options Options de génération de certificat
   * @returns Chemins des fichiers de certificat et de clé
   */
  async generateCA(options: Partial<CertificateOptions> = {}): Promise<{
    certPath: string;
    keyPath: string;
  }> {
    try {
      const defaultOptions: CertificateOptions = {
        commonName: 'Retreat And Be CA',
        organization: 'Retreat And Be',
        organizationalUnit: 'Security',
        country: 'FR',
        state: 'Ile-de-France',
        locality: 'Paris',
        emailAddress: '<EMAIL>',
        validityDays: 3650, // 10 ans
        keySize: 4096,
        isCA: true,
      };

      const caOptions = { ...defaultOptions, ...options };
      const caKeyPath = path.join(this.certsDir, 'ca.key');
      const caCertPath = path.join(this.certsDir, 'ca.crt');

      // Générer la clé privée
      await exec(
        `openssl genrsa -out "${caKeyPath}" ${caOptions.keySize}`
      );

      // Générer le certificat auto-signé
      const subj = this.formatSubject(caOptions);
      await exec(
        `openssl req -x509 -new -nodes -key "${caKeyPath}" -sha256 -days ${caOptions.validityDays} -out "${caCertPath}" -subj "${subj}"`
      );

      this.logger.log(`Generated CA certificate: ${caCertPath}`);
      return { certPath: caCertPath, keyPath: caKeyPath };
    } catch (error) {
      this.logger.error('Failed to generate CA:', error);
      throw error;
    }
  }

  /**
   * Génère un certificat client ou serveur signé par la CA
   * @param name Nom du certificat
   * @param options Options de génération de certificat
   * @returns Chemins des fichiers de certificat et de clé
   */
  async generateCertificate(
    name: string,
    options: Partial<CertificateOptions> = {}
  ): Promise<{
    certPath: string;
    keyPath: string;
    csrPath: string;
  }> {
    try {
      const defaultOptions: CertificateOptions = {
        commonName: `${name}.retreatandbe.com`,
        organization: 'Retreat And Be',
        organizationalUnit: 'Services',
        country: 'FR',
        state: 'Ile-de-France',
        locality: 'Paris',
        emailAddress: `${name}@retreatandbe.com`,
        validityDays: 365, // 1 an
        keySize: 2048,
        isCA: false,
      };

      const certOptions = { ...defaultOptions, ...options };
      const keyPath = path.join(this.certsDir, `${name}.key`);
      const csrPath = path.join(this.certsDir, `${name}.csr`);
      const certPath = path.join(this.certsDir, `${name}.crt`);
      const caKeyPath = path.join(this.certsDir, 'ca.key');
      const caCertPath = path.join(this.certsDir, 'ca.crt');

      // Vérifier si la CA existe
      if (!fs.existsSync(caKeyPath) || !fs.existsSync(caCertPath)) {
        await this.generateCA();
      }

      // Générer la clé privée
      await exec(
        `openssl genrsa -out "${keyPath}" ${certOptions.keySize}`
      );

      // Générer la demande de signature de certificat (CSR)
      const subj = this.formatSubject(certOptions);
      await exec(
        `openssl req -new -key "${keyPath}" -out "${csrPath}" -subj "${subj}"`
      );

      // Signer le certificat avec la CA
      await exec(
        `openssl x509 -req -in "${csrPath}" -CA "${caCertPath}" -CAkey "${caKeyPath}" -CAcreateserial -out "${certPath}" -days ${certOptions.validityDays} -sha256`
      );

      this.logger.log(`Generated certificate for ${name}: ${certPath}`);
      return { certPath, keyPath, csrPath };
    } catch (error) {
      this.logger.error(`Failed to generate certificate for ${name}:`, error);
      throw error;
    }
  }

  /**
   * Révoque un certificat
   * @param certPath Chemin du certificat à révoquer
   * @returns Chemin du fichier CRL
   */
  async revokeCertificate(certPath: string): Promise<string> {
    try {
      const caCertPath = path.join(this.certsDir, 'ca.crt');
      const caKeyPath = path.join(this.certsDir, 'ca.key');
      const crlPath = path.join(this.certsDir, 'ca.crl');
      const indexPath = path.join(this.certsDir, 'index.txt');
      const crlnumberPath = path.join(this.certsDir, 'crlnumber');

      // Créer les fichiers nécessaires s'ils n'existent pas
      if (!fs.existsSync(indexPath)) {
        fs.writeFileSync(indexPath, '');
      }

      if (!fs.existsSync(crlnumberPath)) {
        fs.writeFileSync(crlnumberPath, '01\n');
      }

      // Révoquer le certificat
      await exec(
        `openssl ca -config openssl.cnf -revoke "${certPath}" -keyfile "${caKeyPath}" -cert "${caCertPath}"`
      );

      // Générer la CRL
      await exec(
        `openssl ca -config openssl.cnf -gencrl -keyfile "${caKeyPath}" -cert "${caCertPath}" -out "${crlPath}"`
      );

      this.logger.log(`Revoked certificate: ${certPath}`);
      return crlPath;
    } catch (error) {
      this.logger.error(`Failed to revoke certificate ${certPath}:`, error);
      throw error;
    }
  }

  /**
   * Vérifie si un certificat est valide
   * @param certPath Chemin du certificat à vérifier
   * @returns true si le certificat est valide
   */
  async verifyCertificate(certPath: string): Promise<boolean> {
    try {
      const caCertPath = path.join(this.certsDir, 'ca.crt');
      const { stdout } = await exec(
        `openssl verify -CAfile "${caCertPath}" "${certPath}"`
      );
      
      return stdout.includes('OK');
    } catch (error) {
      this.logger.error(`Failed to verify certificate ${certPath}:`, error);
      return false;
    }
  }

  /**
   * Formate le sujet du certificat au format OpenSSL
   * @param options Options de génération de certificat
   * @returns Sujet formaté
   */
  private formatSubject(options: CertificateOptions): string {
    return `/C=${options.country}/ST=${options.state}/L=${options.locality}/O=${options.organization}/OU=${options.organizationalUnit}/CN=${options.commonName}/emailAddress=${options.emailAddress}`;
  }

  /**
   * Génère un bundle de certificats pour un service
   * @param serviceName Nom du service
   * @returns Chemins des fichiers générés
   */
  async generateServiceCertificates(
    serviceName: string
  ): Promise<{
    certPath: string;
    keyPath: string;
    caCertPath: string;
  }> {
    try {
      // Générer le certificat du service
      const { certPath, keyPath } = await this.generateCertificate(serviceName, {
        commonName: `${serviceName}.service.retreatandbe.internal`,
        organizationalUnit: 'Microservices',
      });

      const caCertPath = path.join(this.certsDir, 'ca.crt');

      return { certPath, keyPath, caCertPath };
    } catch (error) {
      this.logger.error(`Failed to generate certificates for service ${serviceName}:`, error);
      throw error;
    }
  }
}
