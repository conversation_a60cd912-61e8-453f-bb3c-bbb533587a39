import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { VaultService } from './vault.service';
import { KeyManagementService } from './key-management.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as crypto from 'crypto';

/**
 * Types de secrets gérés par le service
 */
export enum SecretType {
  API_KEY = 'api_key',
  DATABASE = 'database',
  OAUTH = 'oauth',
  JWT = 'jwt',
  ENCRYPTION = 'encryption',
  CERTIFICATE = 'certificate',
  GENERAL = 'general',
}

/**
 * Interface pour les métadonnées des secrets
 */
export interface SecretMetadata {
  id: string;
  name: string;
  type: SecretType;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  expiresAt?: Date;
  rotationPolicy?: {
    interval: number; // en millisecondes
    autoRotate: boolean;
    lastRotation?: Date;
  };
  tags?: string[];
  owner?: string;
  application?: string;
  environment?: string;
  version: number;
}

/**
 * Interface pour un secret avec ses métadonnées
 */
export interface Secret {
  value: string;
  metadata: SecretMetadata;
}

/**
 * Options de configuration du gestionnaire de secrets
 */
export interface SecretManagerOptions {
  useVault: boolean;
  defaultExpirationDays: number;
  autoRotationEnabled: boolean;
  defaultRotationInterval: number; // en millisecondes
  encryptSecrets: boolean;
}

/**
 * Service de gestion centralisée des secrets
 * Ce service fournit une interface unifiée pour gérer les secrets de l'application
 * Il peut utiliser HashiCorp Vault ou un stockage en mémoire selon la configuration
 */
@Injectable()
export class SecretManagerService implements OnModuleInit {
  private readonly logger = new Logger(SecretManagerService.name);
  private readonly options: SecretManagerOptions;
  private readonly secretsCache: Map<string, Secret> = new Map();
  private readonly secretsMetadataCache: Map<string, SecretMetadata> = new Map();
  private rotationTimers: Map<string, NodeJS.Timeout> = new Map();
  private initialized = false;

  constructor(
    private readonly configService: ConfigService,
    private readonly vaultService: VaultService,
    private readonly keyManagementService: KeyManagementService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.options = {
      useVault: this.configService.get<boolean>('secrets.useVault', false),
      defaultExpirationDays: this.configService.get<number>('secrets.defaultExpirationDays', 90),
      autoRotationEnabled: this.configService.get<boolean>('secrets.autoRotationEnabled', true),
      defaultRotationInterval: this.configService.get<number>('secrets.defaultRotationInterval', 30 * 24 * 60 * 60 * 1000), // 30 jours par défaut
      encryptSecrets: this.configService.get<boolean>('secrets.encryptSecrets', true),
    };
  }

  /**
   * Initialisation du service lors du démarrage de l'application
   */
  async onModuleInit() {
    try {
      await this.initialize();
      this.initialized = true;
      this.logger.log('Secret manager service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize secret manager service', error);
    }
  }

  /**
   * Initialise le service de gestion des secrets
   */
  private async initialize(): Promise<void> {
    // Charger les métadonnées des secrets
    await this.loadSecretsMetadata();

    // Configurer la rotation automatique des secrets
    if (this.options.autoRotationEnabled) {
      this.setupAutoRotation();
    }
  }

  /**
   * Charge les métadonnées des secrets depuis le stockage
   */
  private async loadSecretsMetadata(): Promise<void> {
    try {
      let metadata: Record<string, SecretMetadata> = {};

      // Essayer de charger les métadonnées depuis Vault si activé
      if (this.options.useVault && this.vaultService.isInitialized()) {
        const vaultMetadata = await this.vaultService.readSecret('secrets/metadata');
        if (vaultMetadata) {
          metadata = vaultMetadata as Record<string, SecretMetadata>;
        }
      }

      // Mettre en cache les métadonnées
      for (const [id, secretMetadata] of Object.entries(metadata)) {
        this.secretsMetadataCache.set(id, secretMetadata);
      }

      this.logger.debug(`Loaded metadata for ${this.secretsMetadataCache.size} secrets`);
    } catch (error) {
      this.logger.error('Failed to load secrets metadata', error);
      throw error;
    }
  }

  /**
   * Configure la rotation automatique des secrets
   */
  private setupAutoRotation(): void {
    // Parcourir tous les secrets avec une politique de rotation automatique
    for (const [id, metadata] of this.secretsMetadataCache.entries()) {
      if (metadata.rotationPolicy?.autoRotate) {
        this.scheduleRotation(id, metadata);
      }
    }
  }

  /**
   * Planifie la rotation d'un secret
   * @param secretId ID du secret
   * @param metadata Métadonnées du secret
   */
  private scheduleRotation(secretId: string, metadata: SecretMetadata): void {
    // Annuler le timer existant si présent
    if (this.rotationTimers.has(secretId)) {
      clearTimeout(this.rotationTimers.get(secretId));
      this.rotationTimers.delete(secretId);
    }

    // Calculer le délai avant la prochaine rotation
    const rotationInterval = metadata.rotationPolicy?.interval || this.options.defaultRotationInterval;
    const lastRotation = metadata.rotationPolicy?.lastRotation || metadata.createdAt;
    const nextRotation = new Date(lastRotation.getTime() + rotationInterval);
    const now = new Date();
    let delay = nextRotation.getTime() - now.getTime();

    // Si la rotation est déjà en retard, planifier pour bientôt
    if (delay < 0) {
      delay = 1000 * 60; // 1 minute
    }

    // Planifier la rotation
    const timer = setTimeout(async () => {
      try {
        await this.rotateSecret(secretId);
        // Replanifier après la rotation
        const updatedMetadata = this.secretsMetadataCache.get(secretId);
        if (updatedMetadata && updatedMetadata.rotationPolicy?.autoRotate) {
          this.scheduleRotation(secretId, updatedMetadata);
        }
      } catch (error) {
        this.logger.error(`Failed to auto-rotate secret: ${secretId}`, error);
      }
    }, delay);

    this.rotationTimers.set(secretId, timer);
    this.logger.debug(`Scheduled rotation for secret ${secretId} in ${Math.round(delay / (1000 * 60 * 60 * 24))} days`);
  }

  /**
   * Crée un nouveau secret
   * @param name Nom du secret
   * @param value Valeur du secret
   * @param type Type du secret
   * @param options Options supplémentaires
   * @returns ID du secret créé
   */
  async createSecret(
    name: string,
    value: string,
    type: SecretType = SecretType.GENERAL,
    options?: {
      description?: string;
      expiresInDays?: number;
      rotationInterval?: number;
      autoRotate?: boolean;
      tags?: string[];
      owner?: string;
      application?: string;
      environment?: string;
    },
  ): Promise<string> {
    try {
      // Générer un ID unique pour le secret
      const secretId = `${type}-${name}-${Date.now()}-${crypto.randomBytes(4).toString('hex')}`;

      // Créer les métadonnées du secret
      const now = new Date();
      const metadata: SecretMetadata = {
        id: secretId,
        name,
        type,
        description: options?.description,
        createdAt: now,
        updatedAt: now,
        expiresAt: options?.expiresInDays
          ? new Date(now.getTime() + options.expiresInDays * 24 * 60 * 60 * 1000)
          : undefined,
        rotationPolicy: {
          interval: options?.rotationInterval || this.options.defaultRotationInterval,
          autoRotate: options?.autoRotate !== undefined ? options.autoRotate : this.options.autoRotationEnabled,
          lastRotation: now,
        },
        tags: options?.tags || [],
        owner: options?.owner,
        application: options?.application,
        environment: options?.environment || this.configService.get<string>('NODE_ENV', 'development'),
        version: 1,
      };

      // Chiffrer la valeur du secret si nécessaire
      let secretValue = value;
      if (this.options.encryptSecrets) {
        secretValue = await this.encryptSecretValue(value, type);
      }

      // Stocker le secret
      await this.storeSecret(secretId, secretValue, metadata);

      // Mettre en cache les métadonnées
      this.secretsMetadataCache.set(secretId, metadata);

      // Configurer la rotation automatique si nécessaire
      if (metadata.rotationPolicy?.autoRotate) {
        this.scheduleRotation(secretId, metadata);
      }

      // Émettre un événement de création de secret
      this.eventEmitter.emit('secret.created', {
        secretId,
        type,
        name,
      });

      this.logger.log(`Created new secret: ${secretId} (${type}/${name})`);
      return secretId;
    } catch (error) {
      this.logger.error(`Failed to create secret: ${name} (${type})`, error);
      throw error;
    }
  }

  /**
   * Récupère un secret par son ID
   * @param secretId ID du secret
   * @returns Secret avec ses métadonnées
   */
  async getSecret(secretId: string): Promise<Secret> {
    try {
      // Vérifier si le secret est en cache
      const cachedSecret = this.secretsCache.get(secretId);
      if (cachedSecret) {
        return cachedSecret;
      }

      // Récupérer les métadonnées du secret
      const metadata = this.secretsMetadataCache.get(secretId);
      if (!metadata) {
        throw new Error(`Secret not found: ${secretId}`);
      }

      // Vérifier si le secret a expiré
      if (metadata.expiresAt && metadata.expiresAt < new Date()) {
        throw new Error(`Secret has expired: ${secretId}`);
      }

      // Récupérer la valeur du secret
      const secretValue = await this.retrieveSecretValue(secretId);
      if (!secretValue) {
        throw new Error(`Secret value not found: ${secretId}`);
      }

      // Déchiffrer la valeur du secret si nécessaire
      let value = secretValue;
      if (this.options.encryptSecrets) {
        value = await this.decryptSecretValue(secretValue, metadata.type);
      }

      // Mettre en cache le secret
      const secret: Secret = { value, metadata };
      this.secretsCache.set(secretId, secret);

      // Émettre un événement d'accès au secret
      this.eventEmitter.emit('secret.accessed', {
        secretId,
        type: metadata.type,
        name: metadata.name,
      });

      return secret;
    } catch (error) {
      this.logger.error(`Failed to get secret: ${secretId}`, error);
      throw error;
    }
  }

  /**
   * Récupère un secret par son nom et son type
   * @param name Nom du secret
   * @param type Type du secret
   * @returns Secret avec ses métadonnées
   */
  async getSecretByName(name: string, type: SecretType = SecretType.GENERAL): Promise<Secret> {
    try {
      // Rechercher le secret par nom et type
      for (const [id, metadata] of this.secretsMetadataCache.entries()) {
        if (metadata.name === name && metadata.type === type) {
          return this.getSecret(id);
        }
      }

      throw new Error(`Secret not found: ${type}/${name}`);
    } catch (error) {
      this.logger.error(`Failed to get secret by name: ${type}/${name}`, error);
      throw error;
    }
  }

  /**
   * Met à jour la valeur d'un secret
   * @param secretId ID du secret
   * @param newValue Nouvelle valeur du secret
   * @returns Métadonnées mises à jour
   */
  async updateSecret(secretId: string, newValue: string): Promise<SecretMetadata> {
    try {
      // Récupérer les métadonnées du secret
      const metadata = this.secretsMetadataCache.get(secretId);
      if (!metadata) {
        throw new Error(`Secret not found: ${secretId}`);
      }

      // Mettre à jour les métadonnées
      const updatedMetadata: SecretMetadata = {
        ...metadata,
        updatedAt: new Date(),
        version: metadata.version + 1,
      };

      // Chiffrer la nouvelle valeur si nécessaire
      let secretValue = newValue;
      if (this.options.encryptSecrets) {
        secretValue = await this.encryptSecretValue(newValue, metadata.type);
      }

      // Stocker le secret mis à jour
      await this.storeSecret(secretId, secretValue, updatedMetadata);

      // Mettre à jour le cache
      this.secretsMetadataCache.set(secretId, updatedMetadata);
      this.secretsCache.delete(secretId); // Invalider le cache de valeur

      // Émettre un événement de mise à jour
      this.eventEmitter.emit('secret.updated', {
        secretId,
        type: metadata.type,
        name: metadata.name,
      });

      this.logger.log(`Updated secret: ${secretId} (${metadata.type}/${metadata.name})`);
      return updatedMetadata;
    } catch (error) {
      this.logger.error(`Failed to update secret: ${secretId}`, error);
      throw error;
    }
  }

  /**
   * Effectue la rotation d'un secret
   * @param secretId ID du secret
   * @returns ID du nouveau secret
   */
  async rotateSecret(secretId: string): Promise<string> {
    try {
      // Récupérer les métadonnées du secret
      const metadata = this.secretsMetadataCache.get(secretId);
      if (!metadata) {
        throw new Error(`Secret not found: ${secretId}`);
      }

      // Récupérer la valeur actuelle du secret
      const currentSecret = await this.getSecret(secretId);

      // Générer une nouvelle valeur selon le type de secret
      const newValue = await this.generateSecretValue(metadata.type, currentSecret.value);

      // Créer un nouveau secret avec les mêmes métadonnées
      const newSecretId = await this.createSecret(metadata.name, newValue, metadata.type, {
        description: metadata.description,
        expiresInDays: metadata.expiresAt
          ? Math.max(0, Math.round((metadata.expiresAt.getTime() - Date.now()) / (24 * 60 * 60 * 1000)))
          : undefined,
        rotationInterval: metadata.rotationPolicy?.interval,
        autoRotate: metadata.rotationPolicy?.autoRotate,
        tags: metadata.tags,
        owner: metadata.owner,
        application: metadata.application,
        environment: metadata.environment,
      });

      // Marquer l'ancien secret comme remplacé
      await this.markSecretAsReplaced(secretId, newSecretId);

      // Émettre un événement de rotation
      this.eventEmitter.emit('secret.rotated', {
        oldSecretId: secretId,
        newSecretId,
        type: metadata.type,
        name: metadata.name,
      });

      this.logger.log(`Rotated secret: ${secretId} -> ${newSecretId} (${metadata.type}/${metadata.name})`);
      return newSecretId;
    } catch (error) {
      this.logger.error(`Failed to rotate secret: ${secretId}`, error);
      throw error;
    }
  }

  /**
   * Supprime un secret
   * @param secretId ID du secret
   */
  async deleteSecret(secretId: string): Promise<void> {
    try {
      // Récupérer les métadonnées du secret
      const metadata = this.secretsMetadataCache.get(secretId);
      if (!metadata) {
        throw new Error(`Secret not found: ${secretId}`);
      }

      // Supprimer le secret du stockage
      if (this.options.useVault && this.vaultService.isInitialized()) {
        await this.vaultService.deleteSecret(`secrets/values/${secretId}`);
      }

      // Supprimer les métadonnées
      this.secretsMetadataCache.delete(secretId);
      this.secretsCache.delete(secretId);

      // Annuler le timer de rotation si présent
      if (this.rotationTimers.has(secretId)) {
        clearTimeout(this.rotationTimers.get(secretId));
        this.rotationTimers.delete(secretId);
      }

      // Mettre à jour les métadonnées globales
      await this.saveSecretsMetadata();

      // Émettre un événement de suppression
      this.eventEmitter.emit('secret.deleted', {
        secretId,
        type: metadata.type,
        name: metadata.name,
      });

      this.logger.log(`Deleted secret: ${secretId} (${metadata.type}/${metadata.name})`);
    } catch (error) {
      this.logger.error(`Failed to delete secret: ${secretId}`, error);
      throw error;
    }
  }

  /**
   * Liste tous les secrets
   * @param filters Filtres optionnels
   * @returns Liste des métadonnées des secrets
   */
  async listSecrets(filters?: {
    type?: SecretType;
    application?: string;
    environment?: string;
    tags?: string[];
  }): Promise<SecretMetadata[]> {
    try {
      let secrets = Array.from(this.secretsMetadataCache.values());

      // Appliquer les filtres
      if (filters) {
        if (filters.type) {
          secrets = secrets.filter(s => s.type === filters.type);
        }
        if (filters.application) {
          secrets = secrets.filter(s => s.application === filters.application);
        }
        if (filters.environment) {
          secrets = secrets.filter(s => s.environment === filters.environment);
        }
        if (filters.tags && filters.tags.length > 0) {
          secrets = secrets.filter(s => filters.tags.some(tag => s.tags.includes(tag)));
        }
      }

      return secrets;
    } catch (error) {
      this.logger.error('Failed to list secrets', error);
      throw error;
    }
  }

  /**
   * Stocke un secret dans le stockage approprié
   * @param secretId ID du secret
   * @param value Valeur du secret
   * @param metadata Métadonnées du secret
   */
  private async storeSecret(secretId: string, value: string, metadata: SecretMetadata): Promise<void> {
    try {
      // Stocker dans Vault si activé
      if (this.options.useVault && this.vaultService.isInitialized()) {
        await this.vaultService.writeSecret(`secrets/values/${secretId}`, { value });
      } else {
        // Fallback vers le stockage en mémoire (pour le développement uniquement)
        this.secretsCache.set(secretId, { value, metadata });
      }

      // Mettre à jour les métadonnées globales
      await this.saveSecretsMetadata();
    } catch (error) {
      this.logger.error(`Failed to store secret: ${secretId}`, error);
      throw error;
    }
  }

  /**
   * Récupère la valeur d'un secret depuis le stockage
   * @param secretId ID du secret
   * @returns Valeur du secret
   */
  private async retrieveSecretValue(secretId: string): Promise<string> {
    try {
      // Récupérer depuis Vault si activé
      if (this.options.useVault && this.vaultService.isInitialized()) {
        const secretData = await this.vaultService.readSecret(`secrets/values/${secretId}`);
        return secretData?.value;
      } else {
        // Fallback vers le stockage en mémoire
        const cachedSecret = this.secretsCache.get(secretId);
        return cachedSecret?.value;
      }
    } catch (error) {
      this.logger.error(`Failed to retrieve secret value: ${secretId}`, error);
      throw error;
    }
  }

  /**
   * Sauvegarde les métadonnées des secrets
   */
  private async saveSecretsMetadata(): Promise<void> {
    try {
      const metadata = Object.fromEntries(this.secretsMetadataCache.entries());

      // Sauvegarder dans Vault si activé
      if (this.options.useVault && this.vaultService.isInitialized()) {
        await this.vaultService.writeSecret('secrets/metadata', metadata);
      }
    } catch (error) {
      this.logger.error('Failed to save secrets metadata', error);
      throw error;
    }
  }

  /**
   * Marque un secret comme remplacé par un nouveau
   * @param oldSecretId ID de l'ancien secret
   * @param newSecretId ID du nouveau secret
   */
  private async markSecretAsReplaced(oldSecretId: string, newSecretId: string): Promise<void> {
    try {
      const metadata = this.secretsMetadataCache.get(oldSecretId);
      if (!metadata) {
        return;
      }

      // Mettre à jour les métadonnées
      const updatedMetadata: SecretMetadata = {
        ...metadata,
        updatedAt: new Date(),
        tags: [...(metadata.tags || []), 'replaced'],
      };

      // Mettre à jour le cache
      this.secretsMetadataCache.set(oldSecretId, updatedMetadata);
      this.secretsCache.delete(oldSecretId); // Invalider le cache de valeur

      // Mettre à jour les métadonnées globales
      await this.saveSecretsMetadata();
    } catch (error) {
      this.logger.error(`Failed to mark secret as replaced: ${oldSecretId}`, error);
      throw error;
    }
  }

  /**
   * Chiffre la valeur d'un secret
   * @param value Valeur à chiffrer
   * @param type Type du secret
   * @returns Valeur chiffrée
   */
  private async encryptSecretValue(value: string, type: SecretType): Promise<string> {
    try {
      // Récupérer une clé active pour le chiffrement
      const { key } = await this.keyManagementService.getActiveKey('encryption', 'secrets');

      // Générer un vecteur d'initialisation aléatoire
      const iv = crypto.randomBytes(16);

      // Chiffrer la valeur
      const cipher = crypto.createCipheriv('aes-256-gcm', key, iv);
      let encrypted = cipher.update(value, 'utf8', 'base64');
      encrypted += cipher.final('base64');

      // Récupérer le tag d'authentification
      const authTag = cipher.getAuthTag();

      // Combiner les éléments pour le stockage
      return JSON.stringify({
        encrypted,
        iv: iv.toString('base64'),
        authTag: authTag.toString('base64'),
      });
    } catch (error) {
      this.logger.error('Failed to encrypt secret value', error);
      throw error;
    }
  }

  /**
   * Déchiffre la valeur d'un secret
   * @param encryptedValue Valeur chiffrée
   * @param type Type du secret
   * @returns Valeur déchiffrée
   */
  private async decryptSecretValue(encryptedValue: string, type: SecretType): Promise<string> {
    try {
      // Analyser la valeur chiffrée
      const { encrypted, iv, authTag } = JSON.parse(encryptedValue);

      // Récupérer une clé active pour le déchiffrement
      const { key } = await this.keyManagementService.getActiveKey('encryption', 'secrets');

      // Déchiffrer la valeur
      const decipher = crypto.createDecipheriv('aes-256-gcm', key, Buffer.from(iv, 'base64'));
      decipher.setAuthTag(Buffer.from(authTag, 'base64'));
      let decrypted = decipher.update(encrypted, 'base64', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      this.logger.error('Failed to decrypt secret value', error);
      throw error;
    }
  }

  /**
   * Génère une nouvelle valeur de secret en fonction du type
   * @param type Type du secret
   * @param currentValue Valeur actuelle (pour certains types)
   * @returns Nouvelle valeur générée
   */
  private async generateSecretValue(type: SecretType, currentValue?: string): Promise<string> {
    switch (type) {
      case SecretType.API_KEY:
        return this.generateApiKey();
      case SecretType.JWT:
        return this.generateJwtSecret();
      case SecretType.ENCRYPTION:
        return this.generateEncryptionKey();
      default:
        // Pour les autres types, générer une chaîne aléatoire
        return crypto.randomBytes(32).toString('base64');
    }
  }

  /**
   * Génère une nouvelle clé d'API
   * @returns Clé d'API générée
   */
  private generateApiKey(): string {
    // Format: prefix_base64
    const prefix = 'rb2_';
    const randomBytes = crypto.randomBytes(24);
    return `${prefix}${randomBytes.toString('base64').replace(/[+/=]/g, '')}`;
  }

  /**
   * Génère un nouveau secret JWT
   * @returns Secret JWT généré
   */
  private generateJwtSecret(): string {
    return crypto.randomBytes(64).toString('hex');
  }

  /**
   * Génère une nouvelle clé de chiffrement
   * @returns Clé de chiffrement générée
   */
  private generateEncryptionKey(): string {
    return crypto.randomBytes(32).toString('base64');
  }
}
