import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { SecurityEventService } from './security-event.service';
import { SecurityEventSeverity } from '../dto/create-security-event.dto';
import { SecurityScanType } from '../interfaces/security-scan-type.enum';
import { SecurityVulnerabilityLevel } from '../interfaces/security-vulnerability-level.enum';
import { SecurityService } from '../security.service';
import * as fs from 'fs';
import * as path from 'path';
import * as util from 'util';
import * as child_process from 'child_process';

const exec = util.promisify(child_process.exec);

/**
 * Service pour les audits de sécurité automatisés
 * Exécute des audits de sécurité périodiques et génère des rapports
 */
@Injectable()
export class AutomatedSecurityAuditService {
  private readonly logger = new Logger(AutomatedSecurityAuditService.name);
  private readonly auditEnabled: boolean;
  private readonly auditReportPath: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly securityEventService: SecurityEventService,
    private readonly securityService: SecurityService,
  ) {
    this.auditEnabled = this.configService.get<boolean>('SECURITY_AUDIT_ENABLED', true);
    this.auditReportPath = this.configService.get<string>('SECURITY_AUDIT_REPORT_PATH', './audit-reports');

    // Créer le répertoire des rapports s'il n'existe pas
    if (!fs.existsSync(this.auditReportPath)) {
      fs.mkdirSync(this.auditReportPath, { recursive: true });
    }
  }

  /**
   * Exécuter un audit de sécurité complet
   * Exécuté tous les dimanches à 2h du matin
   */
  @Cron(CronExpression.EVERY_WEEK)
  async runWeeklySecurityAudit() {
    if (!this.auditEnabled) {
      this.logger.log('Security audit is disabled');
      return;
    }

    this.logger.log('Starting weekly security audit');

    try {
      // Exécuter un scan de sécurité complet
      const scanResult = await this.securityService.scanForVulnerabilities(SecurityScanType.FULL);

      // Générer un rapport d'audit
      const reportPath = await this.generateAuditReport(scanResult);

      // Enregistrer l'événement d'audit
      await this.securityEventService.logSecurityEvent({
        eventType: 'SECURITY_AUDIT_COMPLETED',
        severity: SecurityEventSeverity.INFO,
        source: 'AUTOMATED_AUDIT',
        details: {
          scanResult: {
            vulnerabilityCount: scanResult.vulnerabilityCount,
            highSeverityCount: scanResult.highSeverityCount,
            mediumSeverityCount: scanResult.mediumSeverityCount,
            lowSeverityCount: scanResult.lowSeverityCount,
          },
          reportPath,
        },
      });

      this.logger.log(`Weekly security audit completed. Report saved to ${reportPath}`);
    } catch (error) {
      this.logger.error(`Error during weekly security audit: ${error.message}`, error.stack);

      // Enregistrer l'événement d'erreur
      await this.securityEventService.logSecurityEvent({
        eventType: 'SECURITY_AUDIT_FAILED',
        severity: SecurityEventSeverity.ERROR,
        source: 'AUTOMATED_AUDIT',
        details: {
          error: error.message,
        },
      });
    }
  }

  /**
   * Exécuter un audit de sécurité rapide
   * Exécuté tous les jours à 3h du matin
   */
  @Cron(CronExpression.EVERY_DAY_AT_3AM)
  async runDailySecurityAudit() {
    if (!this.auditEnabled) {
      this.logger.log('Security audit is disabled');
      return;
    }

    this.logger.log('Starting daily security audit');

    try {
      // Exécuter un scan de sécurité rapide
      const scanResult = await this.securityService.scanForVulnerabilities(SecurityScanType.QUICK);

      // Enregistrer l'événement d'audit
      await this.securityEventService.logSecurityEvent({
        eventType: 'DAILY_SECURITY_AUDIT_COMPLETED',
        severity: SecurityEventSeverity.INFO,
        source: 'AUTOMATED_AUDIT',
        details: {
          scanResult: {
            vulnerabilityCount: scanResult.vulnerabilityCount,
            highSeverityCount: scanResult.highSeverityCount,
            mediumSeverityCount: scanResult.mediumSeverityCount,
            lowSeverityCount: scanResult.lowSeverityCount,
          },
        },
      });

      // Si des vulnérabilités critiques sont détectées, générer un rapport
      if (scanResult.highSeverityCount > 0) {
        const reportPath = await this.generateAuditReport(scanResult);

        this.logger.warn(`Daily security audit detected ${scanResult.highSeverityCount} high severity vulnerabilities. Report saved to ${reportPath}`);
      } else {
        this.logger.log('Daily security audit completed. No high severity vulnerabilities detected.');
      }
    } catch (error) {
      this.logger.error(`Error during daily security audit: ${error.message}`, error.stack);

      // Enregistrer l'événement d'erreur
      await this.securityEventService.logSecurityEvent({
        eventType: 'DAILY_SECURITY_AUDIT_FAILED',
        severity: SecurityEventSeverity.ERROR,
        source: 'AUTOMATED_AUDIT',
        details: {
          error: error.message,
        },
      });
    }
  }

  /**
   * Exécuter un audit des dépendances
   * Exécuté tous les lundis à 4h du matin
   */
  @Cron('0 4 * * 1') // Tous les lundis à 4h du matin
  async runDependencyAudit() {
    if (!this.auditEnabled) {
      this.logger.log('Dependency audit is disabled');
      return;
    }

    this.logger.log('Starting dependency security audit');

    try {
      // Exécuter npm audit
      const { stdout, stderr } = await exec('npm audit --json');

      // Analyser les résultats
      const auditResults = JSON.parse(stdout);

      // Générer un rapport
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const reportPath = path.join(this.auditReportPath, `dependency-audit-${timestamp}.json`);

      fs.writeFileSync(reportPath, JSON.stringify(auditResults, null, 2));

      // Enregistrer l'événement d'audit
      await this.securityEventService.logSecurityEvent({
        eventType: 'DEPENDENCY_AUDIT_COMPLETED',
        severity: 'INFO' as any,
        source: 'AUTOMATED_AUDIT',
        details: {
          vulnerabilities: auditResults.metadata?.vulnerabilities,
          reportPath,
        },
      });

      // Si des vulnérabilités critiques sont détectées, enregistrer un événement d'alerte
      if (auditResults.metadata?.vulnerabilities?.critical > 0 || auditResults.metadata?.vulnerabilities?.high > 0) {
        await this.securityEventService.logSecurityEvent({
          eventType: 'CRITICAL_DEPENDENCIES_DETECTED',
          severity: 'HIGH' as any,
          source: 'AUTOMATED_AUDIT',
          details: {
            critical: auditResults.metadata?.vulnerabilities?.critical,
            high: auditResults.metadata?.vulnerabilities?.high,
            reportPath,
          },
        });

        this.logger.warn(`Dependency audit detected ${auditResults.metadata?.vulnerabilities?.critical} critical and ${auditResults.metadata?.vulnerabilities?.high} high severity vulnerabilities`);
      } else {
        this.logger.log('Dependency audit completed. No critical vulnerabilities detected.');
      }
    } catch (error) {
      this.logger.error(`Error during dependency audit: ${error.message}`, error.stack);

      // Enregistrer l'événement d'erreur
      await this.securityEventService.logSecurityEvent({
        eventType: 'DEPENDENCY_AUDIT_FAILED',
        severity: 'ERROR' as any,
        source: 'AUTOMATED_AUDIT',
        details: {
          error: error.message,
        },
      });
    }
  }

  /**
   * Générer un rapport d'audit de sécurité
   * @param scanResult Résultat du scan de sécurité
   */
  private async generateAuditReport(scanResult: any): Promise<string> {
    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const reportPath = path.join(this.auditReportPath, `security-audit-${timestamp}.json`);

    // Créer le rapport
    const report = {
      timestamp: new Date().toISOString(),
      scanType: scanResult.type,
      summary: {
        vulnerabilityCount: scanResult.vulnerabilityCount,
        highSeverityCount: scanResult.highSeverityCount,
        mediumSeverityCount: scanResult.mediumSeverityCount,
        lowSeverityCount: scanResult.lowSeverityCount,
      },
      vulnerabilities: scanResult.vulnerabilities.map(v => ({
        id: v.id,
        name: v.name,
        description: v.description,
        level: v.level,
        location: v.location,
        recommendation: v.recommendation,
      })),
    };

    // Écrire le rapport dans un fichier
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    return reportPath;
  }
}
