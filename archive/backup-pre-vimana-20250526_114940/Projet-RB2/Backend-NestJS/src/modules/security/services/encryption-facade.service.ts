import { Injectable, Logger } from '@nestjs/common';
import { EncryptionFactoryService } from './encryption-factory.service';
import { EncryptionPolicyService, EncryptionType, SensitiveDataType, SensitivityLevel } from './encryption-policy.service';
import { IEncryptionService } from '../interfaces/encryption-service.interface';
import { IHomomorphicEncryptionService } from '../interfaces/homomorphic-encryption-service.interface';
import { IQuantumResistantService } from '../interfaces/quantum-resistant-service.interface';
import { ISensitiveDataEncryptionService } from '../interfaces/sensitive-data-encryption-service.interface';

/**
 * Service de façade pour simplifier l'utilisation des services de chiffrement
 * Ce service fournit une interface unifiée pour tous les services de chiffrement
 */
@Injectable()
export class EncryptionFacadeService {
  private readonly logger = new Logger(EncryptionFacadeService.name);

  constructor(
    private readonly encryptionFactory: EncryptionFactoryService,
    private readonly policyService: EncryptionPolicyService,
  ) {}

  /**
   * Chiffre des données en utilisant le service approprié en fonction du contexte
   * @param data Données à chiffrer
   * @param context Contexte de chiffrement
   * @returns Données chiffrées
   */
  async encrypt(
    data: string | Buffer,
    context: {
      dataType?: SensitiveDataType;
      sensitivityLevel?: SensitivityLevel;
      policyName?: string;
      requireHomomorphic?: boolean;
      requireQuantumResistant?: boolean;
    } = {},
  ): Promise<Buffer> {
    const service = this.encryptionFactory.createEncryptionService(context);
    return service.encrypt(data);
  }

  /**
   * Déchiffre des données en utilisant le service approprié
   * @param encryptedData Données chiffrées
   * @param context Contexte de déchiffrement
   * @returns Données déchiffrées
   */
  async decrypt(
    encryptedData: Buffer,
    context: {
      dataType?: SensitiveDataType;
      sensitivityLevel?: SensitivityLevel;
      policyName?: string;
    } = {},
  ): Promise<Buffer> {
    const service = this.encryptionFactory.createEncryptionService(context);
    return service.decrypt(encryptedData);
  }

  /**
   * Chiffre une valeur numérique pour le traitement homomorphique
   * @param value Valeur à chiffrer
   * @returns Valeur chiffrée
   */
  async encryptForHomomorphicProcessing(value: number): Promise<Buffer> {
    const service = this.encryptionFactory.createHomomorphicEncryptionService();
    if (!service) {
      throw new Error('Homomorphic encryption service is not available');
    }
    return service.encryptNumber(value);
  }

  /**
   * Déchiffre une valeur numérique traitée de manière homomorphique
   * @param encryptedData Données chiffrées
   * @returns Valeur déchiffrée
   */
  async decryptHomomorphicResult(encryptedData: Buffer): Promise<number> {
    const service = this.encryptionFactory.createHomomorphicEncryptionService();
    if (!service) {
      throw new Error('Homomorphic encryption service is not available');
    }
    return service.decryptNumber(encryptedData);
  }

  /**
   * Effectue une opération homomorphique sur des données chiffrées
   * @param operation Opération à effectuer ('add', 'subtract', 'multiply', 'divide', 'average', 'variance')
   * @param operands Opérandes chiffrés
   * @returns Résultat chiffré
   */
  async performHomomorphicOperation(
    operation: 'add' | 'subtract' | 'multiply' | 'divide' | 'average' | 'variance',
    operands: Buffer[] | { a: Buffer; b: Buffer | number },
  ): Promise<Buffer> {
    const service = this.encryptionFactory.createHomomorphicEncryptionService();
    if (!service) {
      throw new Error('Homomorphic encryption service is not available');
    }

    switch (operation) {
      case 'add':
        if (!Array.isArray(operands) && 'a' in operands && 'b' in operands) {
          return service.add(operands.a, operands.b as Buffer);
        }
        throw new Error('Invalid operands for add operation');

      case 'subtract':
        if (!Array.isArray(operands) && 'a' in operands && 'b' in operands) {
          return service.subtract(operands.a, operands.b as Buffer);
        }
        throw new Error('Invalid operands for subtract operation');

      case 'multiply':
        if (!Array.isArray(operands) && 'a' in operands && 'b' in operands) {
          return service.multiply(operands.a, operands.b as Buffer);
        }
        throw new Error('Invalid operands for multiply operation');

      case 'divide':
        if (!Array.isArray(operands) && 'a' in operands && 'b' in operands) {
          return service.divide(operands.a, operands.b);
        }
        throw new Error('Invalid operands for divide operation');

      case 'average':
        if (Array.isArray(operands)) {
          return service.average(operands);
        }
        throw new Error('Invalid operands for average operation');

      case 'variance':
        if (Array.isArray(operands)) {
          return service.variance(operands);
        }
        throw new Error('Invalid operands for variance operation');

      default:
        throw new Error(`Unsupported homomorphic operation: ${operation}`);
    }
  }

  /**
   * Chiffre des données pour stockage en base de données
   * @param value Valeur à chiffrer
   * @param context Contexte de chiffrement
   * @returns Valeur chiffrée
   */
  async encryptForStorage(
    value: string | Buffer,
    context: {
      dataType?: SensitiveDataType;
      sensitivityLevel?: SensitivityLevel;
      policyName?: string;
      tableName?: string;
      columnName?: string;
    } = {},
  ): Promise<string> {
    const service = this.encryptionFactory.createSensitiveDataEncryptionService();
    return service.encryptForStorage(value, {
      tableName: context.tableName,
      columnName: context.columnName,
      dataType: context.dataType,
      sensitivityLevel: context.sensitivityLevel,
    });
  }

  /**
   * Déchiffre des données stockées en base de données
   * @param encryptedValue Valeur chiffrée
   * @param context Contexte de déchiffrement
   * @returns Valeur déchiffrée
   */
  async decryptFromStorage(
    encryptedValue: string,
    context: {
      dataType?: SensitiveDataType;
      sensitivityLevel?: SensitivityLevel;
      policyName?: string;
      tableName?: string;
      columnName?: string;
    } = {},
  ): Promise<string> {
    const service = this.encryptionFactory.createSensitiveDataEncryptionService();
    return service.decryptFromStorage(encryptedValue, {
      tableName: context.tableName,
      columnName: context.columnName,
      dataType: context.dataType,
      sensitivityLevel: context.sensitivityLevel,
    });
  }

  /**
   * Chiffre un objet entier pour stockage
   * @param obj Objet à chiffrer
   * @param fieldsToEncrypt Champs à chiffrer
   * @param context Contexte de chiffrement
   * @returns Objet avec les champs spécifiés chiffrés
   */
  async encryptObject<T>(
    obj: T,
    fieldsToEncrypt: string[],
    context: {
      dataType?: SensitiveDataType;
      sensitivityLevel?: SensitivityLevel;
      policyName?: string;
      tableName?: string;
    } = {},
  ): Promise<T> {
    const service = this.encryptionFactory.createSensitiveDataEncryptionService();
    return service.encryptObject(obj, fieldsToEncrypt, {
      tableName: context.tableName,
      dataType: context.dataType,
      sensitivityLevel: context.sensitivityLevel,
    });
  }

  /**
   * Déchiffre un objet entier
   * @param obj Objet à déchiffrer
   * @param fieldsToDecrypt Champs à déchiffrer
   * @param context Contexte de déchiffrement
   * @returns Objet avec les champs spécifiés déchiffrés
   */
  async decryptObject<T>(
    obj: T,
    fieldsToDecrypt: string[],
    context: {
      dataType?: SensitiveDataType;
      sensitivityLevel?: SensitivityLevel;
      policyName?: string;
      tableName?: string;
    } = {},
  ): Promise<T> {
    const service = this.encryptionFactory.createSensitiveDataEncryptionService();
    return service.decryptObject(obj, fieldsToDecrypt, {
      tableName: context.tableName,
      dataType: context.dataType,
      sensitivityLevel: context.sensitivityLevel,
    });
  }

  /**
   * Vérifie si un type de chiffrement est disponible
   * @param encryptionType Type de chiffrement à vérifier
   * @returns true si le type de chiffrement est disponible
   */
  isEncryptionTypeAvailable(encryptionType: EncryptionType): boolean {
    return this.encryptionFactory.isEncryptionTypeAvailable(encryptionType);
  }

  /**
   * Vérifie si le chiffrement homomorphique est disponible
   * @returns true si le chiffrement homomorphique est disponible
   */
  isHomomorphicAvailable(): boolean {
    return this.isEncryptionTypeAvailable(EncryptionType.HOMOMORPHIC);
  }

  /**
   * Vérifie si le chiffrement post-quantique est disponible
   * @returns true si le chiffrement post-quantique est disponible
   */
  isQuantumResistantAvailable(): boolean {
    return this.isEncryptionTypeAvailable(EncryptionType.QUANTUM_RESISTANT);
  }

  /**
   * Retourne les métadonnées d'un service de chiffrement
   * @param encryptionType Type de chiffrement
   * @returns Métadonnées du service
   */
  getEncryptionServiceMetadata(encryptionType: EncryptionType): Record<string, any> {
    let service: IEncryptionService;

    switch (encryptionType) {
      case EncryptionType.HOMOMORPHIC:
        service = this.encryptionFactory.createHomomorphicEncryptionService();
        break;
      case EncryptionType.QUANTUM_RESISTANT:
        service = this.encryptionFactory.createQuantumResistantService();
        break;
      case EncryptionType.STANDARD:
      default:
        service = this.encryptionFactory.createEncryptionService({});
        break;
    }

    if (!service) {
      return { available: false };
    }

    return {
      available: service.isEnabled(),
      type: service.getEncryptionType(),
      ...service.getMetadata(),
    };
  }
}
