import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';

/**
 * Interface pour les métriques de sécurité
 */
export interface SecurityMetrics {
  totalEvents: number;
  totalAlerts: number;
  topEventSources: Array<{ source: string; count: number }>;
  eventsBySeverity: {
    info: number;
    warning: number;
    error: number;
    critical: number;
  };
  timeframe: 'day' | 'week' | 'month';
}

/**
 * Interface pour les menaces actives
 */
export interface ActiveThreat {
  id: string;
  type: string;
  source: string;
  severity: string;
  detectedAt: Date;
  details: Record<string, any>;
  status: string;
  affectedAssets?: string[];
  mitigationSteps?: string[];
}

/**
 * Service de surveillance de sécurité
 * Ce service est responsable de la surveillance des événements de sécurité,
 * de la détection des menaces et de la génération d'alertes.
 */
@Injectable()
export class SecurityMonitoringService {
  private readonly logger = new Logger(SecurityMonitoringService.name);
  private _alertThresholds: Record<string, number> = {
    failedLogins: 5,
    rateLimitExceeded: 10,
    suspiciousActivities: 3,
    malwareDetections: 1,
    dataBreachAttempts: 1,
    unauthorizedAccess: 2,
    apiAbuse: 8,
  };

  // Getter pour alertThresholds
  get alertThresholds(): Record<string, number> {
    return this._alertThresholds;
  }

  // Menaces actives en cache
  private activeThreatsCache: Map<string, ActiveThreat> = new Map();

  // Dernière mise à jour des menaces actives
  private lastThreatUpdate: Date = new Date();

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initializeThresholds();
    this.subscribeToSecurityEvents();
  }

  private initializeThresholds(): void {
    const configThresholds = this.configService.get<Record<string, number>>('security.alertThresholds');
    if (configThresholds) {
      this._alertThresholds = { ...this._alertThresholds, ...configThresholds };
    }
  }

  /**
   * S'abonne aux événements de sécurité pour détecter les menaces
   */
  private subscribeToSecurityEvents(): void {
    // S'abonner aux événements de sécurité
    this.eventEmitter.on('security.event.logged', async (event: any) => {
      try {
        // Analyser l'événement pour détecter les menaces potentielles
        await this.analyzeSecurityEvent(event);
      } catch (error) {
        this.logger.error(`Failed to analyze security event: ${error.message}`, error.stack);
      }
    });

    // S'abonner aux alertes de sécurité
    this.eventEmitter.on('security.alert.created', async (alert: any) => {
      try {
        // Ajouter l'alerte aux menaces actives si elle est critique
        if (alert.priority === 'HIGH' || alert.priority === 'CRITICAL') {
          await this.addActiveThreat({
            id: alert.id,
            type: alert.type,
            source: 'SECURITY_ALERT',
            severity: alert.priority,
            detectedAt: new Date(alert.createdAt),
            details: typeof alert.details === 'string' ? JSON.parse(alert.details) : alert.details,
            status: 'ACTIVE',
          });
        }
      } catch (error) {
        this.logger.error(`Failed to process security alert: ${error.message}`, error.stack);
      }
    });

    // S'abonner aux incidents de sécurité
    this.eventEmitter.on('security.incident.created', async (incident: any) => {
      try {
        // Ajouter l'incident aux menaces actives
        await this.addActiveThreat({
          id: incident.incidentId,
          type: incident.type,
          source: 'SECURITY_INCIDENT',
          severity: incident.severity,
          detectedAt: new Date(incident.timestamp),
          details: {
            title: incident.title,
            description: 'Security incident detected',
          },
          status: 'ACTIVE',
        });
      } catch (error) {
        this.logger.error(`Failed to process security incident: ${error.message}`, error.stack);
      }
    });

    // S'abonner aux changements de statut des incidents
    this.eventEmitter.on('security.incident.status_changed', async (event: any) => {
      try {
        // Si l'incident est résolu ou fermé, supprimer la menace active
        if (event.newStatus === 'RESOLVED' || event.newStatus === 'CLOSED') {
          this.activeThreatsCache.delete(event.incidentId);
        }
      } catch (error) {
        this.logger.error(`Failed to process incident status change: ${error.message}`, error.stack);
      }
    });
  }

  async logSecurityEvent(
    type: string,
    source: string,
    details: Record<string, any>,
    severity: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL' = 'INFO',
    userId?: string,
  ): Promise<void> {
    try {
      const event = await this.prisma.securityEvent.create({
        data: {
          type,
          source,
          details: JSON.stringify(details),
          severity,
          userId,
          timestamp: new Date(),
        },
      });

      this.logger.log(`Security event logged: ${type} from ${source} with severity ${severity}`);
      this.eventEmitter.emit('security.event.logged', event);

      // Check if this event should trigger an alert
      await this.checkForAlerts(type, userId);
    } catch (error) {
      this.logger.error(`Failed to log security event: ${error.message}`, error.stack);
    }
  }

  private async checkForAlerts(eventType: string, userId?: string): Promise<void> {
    try {
      // Check for threshold-based alerts
      if (eventType === 'FAILED_LOGIN' && userId) {
        const count = await this.prisma.securityEvent.count({
          where: {
            type: 'FAILED_LOGIN',
            userId,
            timestamp: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
            },
          },
        });

        if (count >= this.alertThresholds.failedLogins) {
          await this.createAlert('EXCESSIVE_FAILED_LOGINS', 'HIGH', {
            userId,
            count,
            threshold: this.alertThresholds.failedLogins,
          });
        }
      }

      // Check for rate limit alerts
      if (eventType === 'RATE_LIMIT_EXCEEDED') {
        const count = await this.prisma.securityEvent.count({
          where: {
            type: 'RATE_LIMIT_EXCEEDED',
            timestamp: {
              gte: new Date(Date.now() - 60 * 60 * 1000), // Last hour
            },
          },
        });

        if (count >= this.alertThresholds.rateLimitExceeded) {
          await this.createAlert('EXCESSIVE_RATE_LIMITING', 'MEDIUM', {
            count,
            threshold: this.alertThresholds.rateLimitExceeded,
          });
        }
      }
    } catch (error) {
      this.logger.error(`Failed to check for alerts: ${error.message}`, error.stack);
    }
  }

  private async createAlert(
    type: string,
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL',
    details: Record<string, any>,
  ): Promise<void> {
    try {
      const alert = await this.prisma.securityAlert.create({
        data: {
          type,
          priority,
          details: JSON.stringify(details),
          status: 'OPEN',
          createdAt: new Date(),
        },
      });

      this.logger.warn(`Security alert created: ${type} with priority ${priority}`);
      this.eventEmitter.emit('security.alert.created', alert);
    } catch (error) {
      this.logger.error(`Failed to create security alert: ${error.message}`, error.stack);
    }
  }

  /**
   * Analyse un événement de sécurité pour détecter les menaces potentielles
   * @param event Événement de sécurité
   */
  private async analyzeSecurityEvent(event: any): Promise<void> {
    try {
      // Vérifier si l'événement est critique
      if (event.severity === 'CRITICAL') {
        // Ajouter directement aux menaces actives
        await this.addActiveThreat({
          id: event.id,
          type: event.type,
          source: event.source,
          severity: event.severity,
          detectedAt: new Date(event.timestamp),
          details: typeof event.details === 'string' ? JSON.parse(event.details) : event.details,
          status: 'ACTIVE',
        });
        return;
      }

      // Vérifier les types d'événements spécifiques qui constituent des menaces
      const criticalEventTypes = [
        'UNAUTHORIZED_ACCESS',
        'DATA_BREACH',
        'MALWARE_DETECTED',
        'RANSOMWARE_DETECTED',
        'SYSTEM_COMPROMISE',
        'NETWORK_INTRUSION',
        'ACCOUNT_COMPROMISE',
      ];

      if (criticalEventTypes.includes(event.type)) {
        await this.addActiveThreat({
          id: event.id,
          type: event.type,
          source: event.source,
          severity: event.severity,
          detectedAt: new Date(event.timestamp),
          details: typeof event.details === 'string' ? JSON.parse(event.details) : event.details,
          status: 'ACTIVE',
        });
      }
    } catch (error) {
      this.logger.error(`Failed to analyze security event: ${error.message}`, error.stack);
    }
  }

  /**
   * Ajoute une menace active au cache
   * @param threat Menace active
   */
  private async addActiveThreat(threat: ActiveThreat): Promise<void> {
    try {
      // Ajouter la menace au cache
      this.activeThreatsCache.set(threat.id, threat);
      this.lastThreatUpdate = new Date();

      // Émettre un événement de menace détectée
      this.eventEmitter.emit('security.threat.detected', {
        threatId: threat.id,
        type: threat.type,
        source: threat.source,
        severity: threat.severity,
        timestamp: new Date(),
      });

      this.logger.warn(
        `Active threat detected: ${threat.type} from ${threat.source} with severity ${threat.severity}`,
      );
    } catch (error) {
      this.logger.error(`Failed to add active threat: ${error.message}`, error.stack);
    }
  }

  /**
   * Récupère toutes les menaces actives
   * @returns Liste des menaces actives
   */
  async getActiveThreats(): Promise<ActiveThreat[]> {
    try {
      // Retourner les menaces actives du cache
      return Array.from(this.activeThreatsCache.values());
    } catch (error) {
      this.logger.error(`Failed to get active threats: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Récupère une menace active par son ID
   * @param id ID de la menace
   * @returns Menace active
   */
  async getActiveThreatById(id: string): Promise<ActiveThreat | null> {
    try {
      // Récupérer la menace du cache
      return this.activeThreatsCache.get(id) || null;
    } catch (error) {
      this.logger.error(`Failed to get active threat by ID: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Met à jour le statut d'une menace active
   * @param id ID de la menace
   * @param status Nouveau statut
   * @param mitigationSteps Étapes de mitigation
   * @returns Menace mise à jour
   */
  async updateActiveThreatStatus(
    id: string,
    status: string,
    mitigationSteps?: string[],
  ): Promise<ActiveThreat | null> {
    try {
      // Récupérer la menace du cache
      const threat = this.activeThreatsCache.get(id);

      if (!threat) {
        return null;
      }

      // Mettre à jour le statut
      threat.status = status;

      // Ajouter les étapes de mitigation si fournies
      if (mitigationSteps) {
        threat.mitigationSteps = mitigationSteps;
      }

      // Mettre à jour le cache
      this.activeThreatsCache.set(id, threat);
      this.lastThreatUpdate = new Date();

      // Émettre un événement de mise à jour de menace
      this.eventEmitter.emit('security.threat.updated', {
        threatId: id,
        status,
        timestamp: new Date(),
      });

      this.logger.log(`Active threat status updated: ${id} -> ${status}`);
      return threat;
    } catch (error) {
      this.logger.error(`Failed to update active threat status: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Supprime une menace active
   * @param id ID de la menace
   * @returns true si la suppression a réussi
   */
  async removeActiveThreat(id: string): Promise<boolean> {
    try {
      // Vérifier si la menace existe
      if (!this.activeThreatsCache.has(id)) {
        return false;
      }

      // Supprimer la menace du cache
      this.activeThreatsCache.delete(id);
      this.lastThreatUpdate = new Date();

      // Émettre un événement de suppression de menace
      this.eventEmitter.emit('security.threat.removed', {
        threatId: id,
        timestamp: new Date(),
      });

      this.logger.log(`Active threat removed: ${id}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to remove active threat: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Récupère les métriques de sécurité
   * @param timeframe Période de temps
   * @returns Métriques de sécurité
   */
  async getSecurityMetrics(timeframe: 'day' | 'week' | 'month' = 'day'): Promise<SecurityMetrics> {
    return this.getSecurityDashboardData(timeframe) as Promise<SecurityMetrics>;
  }

  async getSecurityDashboardData(timeframe: 'day' | 'week' | 'month' = 'day'): Promise<any> {
    try {
      const timeframeMap = {
        day: 24 * 60 * 60 * 1000,
        week: 7 * 24 * 60 * 60 * 1000,
        month: 30 * 24 * 60 * 60 * 1000,
      };

      const startDate = new Date(Date.now() - timeframeMap[timeframe]);

      const [events, alerts, topSources, severityCounts] = await Promise.all([
        this.prisma.securityEvent.count({
          where: {
            timestamp: {
              gte: startDate,
            },
          },
        }),
        this.prisma.securityAlert.count({
          where: {
            createdAt: {
              gte: startDate,
            },
          },
        }),
        this.prisma.securityEvent.groupBy({
          by: ['source'],
          _count: {
            id: true,
          },
          where: {
            timestamp: {
              gte: startDate,
            },
          },
          orderBy: {
            _count: {
              id: 'desc',
            },
          },
          take: 5,
        }),
        this.prisma.securityEvent.groupBy({
          by: ['severity'],
          _count: {
            id: true,
          },
          where: {
            timestamp: {
              gte: startDate,
            },
          },
        }),
      ]);

      return {
        totalEvents: events,
        totalAlerts: alerts,
        topEventSources: topSources.map(item => ({
          source: item.source,
          count: item._count.id,
        })),
        eventsBySeverity: severityCounts.reduce(
          (acc, item) => {
            acc[item.severity.toLowerCase()] = item._count.id;
            return acc;
          },
          { info: 0, warning: 0, error: 0, critical: 0 },
        ),
        timeframe,
      };
    } catch (error) {
      this.logger.error(`Failed to get dashboard data: ${error.message}`, error.stack);
      throw error;
    }
  }
}
