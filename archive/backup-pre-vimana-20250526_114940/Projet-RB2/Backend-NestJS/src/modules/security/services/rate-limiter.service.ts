import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';

@Injectable()
export class RateLimiterService {
  private readonly logger = new Logger(RateLimiterService.name);
  private readonly limits: Map<string, { count: number; resetAt: number }> = new Map();
  private readonly defaultLimit = 100; // Default requests per window
  private readonly defaultWindow = 60 * 1000; // Default window of 1 minute

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
  ) {}

  async isRateLimited(
    key: string,
    limit?: number,
    windowMs?: number,
  ): Promise<{ limited: boolean; remaining: number; resetAt: number }> {
    const actualLimit = limit || this.defaultLimit;
    const actualWindow = windowMs || this.defaultWindow;
    const now = Date.now();

    // Get current rate limit data for this key
    let limitData = this.limits.get(key);

    // If no data exists or the window has expired, create new data
    if (!limitData || now > limitData.resetAt) {
      limitData = {
        count: 0,
        resetAt: now + actualWindow,
      };
    }

    // Increment the count
    limitData.count++;

    // Check if the limit has been exceeded
    const limited = limitData.count > actualLimit;

    // Store the updated data
    this.limits.set(key, limitData);

    // Log rate limit events
    if (limited) {
      this.logger.warn(`Rate limit exceeded for key: ${key}`);
      await this.logRateLimitEvent(key, limitData.count, limitData.resetAt);
    }

    return {
      limited,
      remaining: Math.max(0, actualLimit - limitData.count),
      resetAt: limitData.resetAt,
    };
  }

  private async logRateLimitEvent(key: string, count: number, resetAt: number): Promise<void> {
    try {
      await this.prisma.securityEvent.create({
        data: {
          type: 'RATE_LIMIT_EXCEEDED',
          source: 'rate-limiter',
          details: JSON.stringify({
            key,
            count,
            resetAt: new Date(resetAt).toISOString(),
          }),
          severity: 'WARNING',
          timestamp: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(`Failed to log rate limit event: ${error.message}`, error.stack);
    }
  }

  clearExpiredLimits(): void {
    const now = Date.now();
    for (const [key, data] of this.limits.entries()) {
      if (now > data.resetAt) {
        this.limits.delete(key);
      }
    }
  }
}
