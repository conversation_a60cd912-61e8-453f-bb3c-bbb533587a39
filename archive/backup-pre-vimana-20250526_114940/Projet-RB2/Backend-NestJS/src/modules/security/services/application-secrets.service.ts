import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { SecretManagerService, SecretType } from './secret-manager.service';
import { KeyManagementService } from './key-management.service';
import { PrismaService } from '../../../prisma/prisma.service';
import * as crypto from 'crypto';

/**
 * Interface pour les métadonnées d'un secret d'application
 */
export interface ApplicationSecretMetadata {
  application: string;
  environment: string;
  description?: string;
  owner?: string;
  createdAt: Date;
  updatedAt: Date;
  version: number;
  rotationPolicy?: {
    interval: number; // en millisecondes
    autoRotate: boolean;
    lastRotation?: Date;
  };
}

/**
 * Interface pour un secret d'application
 */
export interface ApplicationSecret {
  name: string;
  value: string;
  metadata: ApplicationSecretMetadata;
}

/**
 * Service de gestion des secrets d'application
 * Ce service permet de gérer les secrets utilisés par les différentes applications
 * et microservices de la plateforme
 */
@Injectable()
export class ApplicationSecretsService implements OnModuleInit {
  private readonly logger = new Logger(ApplicationSecretsService.name);
  private readonly secretsCache: Map<string, ApplicationSecret> = new Map();
  private initialized = false;

  constructor(
    private readonly configService: ConfigService,
    private readonly secretManagerService: SecretManagerService,
    private readonly keyManagementService: KeyManagementService,
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Initialisation du service lors du démarrage de l'application
   */
  async onModuleInit() {
    try {
      await this.initialize();
      this.initialized = true;
      this.logger.log('Application secrets service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize application secrets service', error);
    }
  }

  /**
   * Initialise le service de gestion des secrets d'application
   */
  private async initialize(): Promise<void> {
    // Précharger les secrets courants en cache
    await this.preloadSecrets();

    // Configurer les écouteurs d'événements
    this.setupEventListeners();
  }

  /**
   * Précharge les secrets en cache
   */
  private async preloadSecrets(): Promise<void> {
    try {
      // Récupérer tous les secrets d'application
      const secrets = await this.secretManagerService.listSecrets({
        type: SecretType.GENERAL,
      });

      // Filtrer les secrets d'application
      const applicationSecrets = secrets.filter(secret => 
        secret.application && 
        !secret.tags?.includes('replaced')
      );

      // Mettre en cache les secrets
      for (const secretMetadata of applicationSecrets) {
        try {
          const secret = await this.secretManagerService.getSecret(secretMetadata.id);
          const appSecret: ApplicationSecret = {
            name: secretMetadata.name,
            value: secret.value,
            metadata: {
              application: secretMetadata.application,
              environment: secretMetadata.environment || 'development',
              description: secretMetadata.description,
              owner: secretMetadata.owner,
              createdAt: secretMetadata.createdAt,
              updatedAt: secretMetadata.updatedAt,
              version: secretMetadata.version,
              rotationPolicy: secretMetadata.rotationPolicy,
            },
          };

          // Clé de cache: application:environment:name
          const cacheKey = this.getCacheKey(
            appSecret.metadata.application,
            appSecret.metadata.environment,
            appSecret.name,
          );

          this.secretsCache.set(cacheKey, appSecret);
        } catch (error) {
          this.logger.error(`Failed to load secret ${secretMetadata.id}`, error);
        }
      }

      this.logger.debug(`Preloaded ${this.secretsCache.size} application secrets`);
    } catch (error) {
      this.logger.error('Failed to preload secrets', error);
      throw error;
    }
  }

  /**
   * Configure les écouteurs d'événements
   */
  private setupEventListeners(): void {
    // Écouter les événements de rotation des secrets
    this.eventEmitter.on('secret.rotated', async (event: any) => {
      try {
        // Invalider le cache pour le secret concerné
        for (const [key, value] of this.secretsCache.entries()) {
          if (key.includes(event.name)) {
            this.secretsCache.delete(key);
            this.logger.debug(`Invalidated cache for rotated secret: ${event.name}`);
            break;
          }
        }
      } catch (error) {
        this.logger.error(`Failed to handle secret rotation event: ${error.message}`, error);
      }
    });

    // Écouter les événements de mise à jour des secrets
    this.eventEmitter.on('secret.updated', async (event: any) => {
      try {
        // Invalider le cache pour le secret concerné
        for (const [key, value] of this.secretsCache.entries()) {
          if (key.includes(event.name)) {
            this.secretsCache.delete(key);
            this.logger.debug(`Invalidated cache for updated secret: ${event.name}`);
            break;
          }
        }
      } catch (error) {
        this.logger.error(`Failed to handle secret update event: ${error.message}`, error);
      }
    });
  }

  /**
   * Crée un nouveau secret d'application
   * @param name Nom du secret
   * @param value Valeur du secret
   * @param application Nom de l'application
   * @param options Options supplémentaires
   * @returns ID du secret créé
   */
  async createSecret(
    name: string,
    value: string,
    application: string,
    options?: {
      environment?: string;
      description?: string;
      owner?: string;
      expiresInDays?: number;
      rotationInterval?: number;
      autoRotate?: boolean;
    },
  ): Promise<string> {
    try {
      // Vérifier si le secret existe déjà
      const environment = options?.environment || this.configService.get<string>('NODE_ENV', 'development');
      const existingSecret = await this.getSecret(name, application, environment);

      if (existingSecret) {
        throw new Error(`Secret already exists: ${application}:${environment}:${name}`);
      }

      // Créer le secret
      const secretId = await this.secretManagerService.createSecret(
        name,
        value,
        SecretType.GENERAL,
        {
          description: options?.description,
          expiresInDays: options?.expiresInDays,
          rotationInterval: options?.rotationInterval,
          autoRotate: options?.autoRotate,
          tags: ['application'],
          owner: options?.owner,
          application,
          environment,
        },
      );

      // Invalider le cache
      const cacheKey = this.getCacheKey(application, environment, name);
      this.secretsCache.delete(cacheKey);

      // Émettre un événement de création de secret d'application
      this.eventEmitter.emit('application.secret.created', {
        secretId,
        name,
        application,
        environment,
      });

      return secretId;
    } catch (error) {
      this.logger.error(`Failed to create application secret: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Récupère un secret d'application
   * @param name Nom du secret
   * @param application Nom de l'application
   * @param environment Environnement
   * @returns Secret d'application
   */
  async getSecret(
    name: string,
    application: string,
    environment: string = this.configService.get<string>('NODE_ENV', 'development'),
  ): Promise<ApplicationSecret | null> {
    try {
      // Vérifier si le secret est en cache
      const cacheKey = this.getCacheKey(application, environment, name);
      const cachedSecret = this.secretsCache.get(cacheKey);

      if (cachedSecret) {
        return cachedSecret;
      }

      // Récupérer le secret depuis le gestionnaire de secrets
      try {
        const secret = await this.secretManagerService.getSecretByName(
          name,
          SecretType.GENERAL,
        );

        // Vérifier que le secret appartient à l'application et à l'environnement demandés
        if (
          secret.metadata.application !== application ||
          secret.metadata.environment !== environment
        ) {
          return null;
        }

        // Créer l'objet de secret d'application
        const appSecret: ApplicationSecret = {
          name: secret.metadata.name,
          value: secret.value,
          metadata: {
            application: secret.metadata.application,
            environment: secret.metadata.environment,
            description: secret.metadata.description,
            owner: secret.metadata.owner,
            createdAt: secret.metadata.createdAt,
            updatedAt: secret.metadata.updatedAt,
            version: secret.metadata.version,
            rotationPolicy: secret.metadata.rotationPolicy,
          },
        };

        // Mettre en cache
        this.secretsCache.set(cacheKey, appSecret);

        return appSecret;
      } catch (error) {
        // Si le secret n'est pas trouvé, retourner null
        if (error.message.includes('not found')) {
          return null;
        }
        throw error;
      }
    } catch (error) {
      this.logger.error(`Failed to get application secret: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Met à jour un secret d'application
   * @param name Nom du secret
   * @param newValue Nouvelle valeur du secret
   * @param application Nom de l'application
   * @param environment Environnement
   * @returns ID du secret mis à jour
   */
  async updateSecret(
    name: string,
    newValue: string,
    application: string,
    environment: string = this.configService.get<string>('NODE_ENV', 'development'),
  ): Promise<string> {
    try {
      // Récupérer le secret existant
      const existingSecret = await this.getSecret(name, application, environment);

      if (!existingSecret) {
        throw new Error(`Secret not found: ${application}:${environment}:${name}`);
      }

      // Récupérer l'ID du secret
      const secret = await this.secretManagerService.getSecretByName(
        name,
        SecretType.GENERAL,
      );

      // Mettre à jour le secret
      await this.secretManagerService.updateSecret(secret.metadata.id, newValue);

      // Invalider le cache
      const cacheKey = this.getCacheKey(application, environment, name);
      this.secretsCache.delete(cacheKey);

      // Émettre un événement de mise à jour de secret d'application
      this.eventEmitter.emit('application.secret.updated', {
        secretId: secret.metadata.id,
        name,
        application,
        environment,
      });

      return secret.metadata.id;
    } catch (error) {
      this.logger.error(`Failed to update application secret: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Effectue la rotation d'un secret d'application
   * @param name Nom du secret
   * @param application Nom de l'application
   * @param environment Environnement
   * @returns ID du nouveau secret
   */
  async rotateSecret(
    name: string,
    application: string,
    environment: string = this.configService.get<string>('NODE_ENV', 'development'),
  ): Promise<string> {
    try {
      // Récupérer le secret existant
      const existingSecret = await this.getSecret(name, application, environment);

      if (!existingSecret) {
        throw new Error(`Secret not found: ${application}:${environment}:${name}`);
      }

      // Récupérer l'ID du secret
      const secret = await this.secretManagerService.getSecretByName(
        name,
        SecretType.GENERAL,
      );

      // Effectuer la rotation du secret
      const newSecretId = await this.secretManagerService.rotateSecret(secret.metadata.id);

      // Invalider le cache
      const cacheKey = this.getCacheKey(application, environment, name);
      this.secretsCache.delete(cacheKey);

      // Émettre un événement de rotation de secret d'application
      this.eventEmitter.emit('application.secret.rotated', {
        oldSecretId: secret.metadata.id,
        newSecretId,
        name,
        application,
        environment,
      });

      return newSecretId;
    } catch (error) {
      this.logger.error(`Failed to rotate application secret: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Supprime un secret d'application
   * @param name Nom du secret
   * @param application Nom de l'application
   * @param environment Environnement
   */
  async deleteSecret(
    name: string,
    application: string,
    environment: string = this.configService.get<string>('NODE_ENV', 'development'),
  ): Promise<void> {
    try {
      // Récupérer le secret existant
      const existingSecret = await this.getSecret(name, application, environment);

      if (!existingSecret) {
        throw new Error(`Secret not found: ${application}:${environment}:${name}`);
      }

      // Récupérer l'ID du secret
      const secret = await this.secretManagerService.getSecretByName(
        name,
        SecretType.GENERAL,
      );

      // Supprimer le secret
      await this.secretManagerService.deleteSecret(secret.metadata.id);

      // Invalider le cache
      const cacheKey = this.getCacheKey(application, environment, name);
      this.secretsCache.delete(cacheKey);

      // Émettre un événement de suppression de secret d'application
      this.eventEmitter.emit('application.secret.deleted', {
        secretId: secret.metadata.id,
        name,
        application,
        environment,
      });
    } catch (error) {
      this.logger.error(`Failed to delete application secret: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Liste tous les secrets d'une application
   * @param application Nom de l'application
   * @param environment Environnement
   * @returns Liste des secrets de l'application
   */
  async listSecrets(
    application: string,
    environment: string = this.configService.get<string>('NODE_ENV', 'development'),
  ): Promise<ApplicationSecret[]> {
    try {
      // Récupérer tous les secrets de l'application
      const secrets = await this.secretManagerService.listSecrets({
        type: SecretType.GENERAL,
        application,
        environment,
      });

      // Filtrer les secrets qui n'ont pas été remplacés
      const activeSecrets = secrets.filter(secret => !secret.tags?.includes('replaced'));

      // Récupérer les valeurs des secrets
      const applicationSecrets: ApplicationSecret[] = [];

      for (const secretMetadata of activeSecrets) {
        try {
          // Vérifier si le secret est en cache
          const cacheKey = this.getCacheKey(application, environment, secretMetadata.name);
          const cachedSecret = this.secretsCache.get(cacheKey);

          if (cachedSecret) {
            applicationSecrets.push(cachedSecret);
          } else {
            // Récupérer le secret
            const secret = await this.secretManagerService.getSecret(secretMetadata.id);

            // Créer l'objet de secret d'application
            const appSecret: ApplicationSecret = {
              name: secretMetadata.name,
              value: secret.value,
              metadata: {
                application: secretMetadata.application,
                environment: secretMetadata.environment,
                description: secretMetadata.description,
                owner: secretMetadata.owner,
                createdAt: secretMetadata.createdAt,
                updatedAt: secretMetadata.updatedAt,
                version: secretMetadata.version,
                rotationPolicy: secretMetadata.rotationPolicy,
              },
            };

            // Mettre en cache
            this.secretsCache.set(cacheKey, appSecret);

            applicationSecrets.push(appSecret);
          }
        } catch (error) {
          this.logger.error(`Failed to load secret ${secretMetadata.id}`, error);
        }
      }

      return applicationSecrets;
    } catch (error) {
      this.logger.error(`Failed to list application secrets: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Liste toutes les applications qui ont des secrets
   * @returns Liste des applications
   */
  async listApplications(): Promise<string[]> {
    try {
      // Récupérer tous les secrets d'application
      const secrets = await this.secretManagerService.listSecrets({
        type: SecretType.GENERAL,
      });

      // Extraire les noms d'applications uniques
      const applications = new Set<string>();

      for (const secret of secrets) {
        if (secret.application) {
          applications.add(secret.application);
        }
      }

      return Array.from(applications);
    } catch (error) {
      this.logger.error(`Failed to list applications: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Génère une clé de cache pour un secret
   * @param application Nom de l'application
   * @param environment Environnement
   * @param name Nom du secret
   * @returns Clé de cache
   */
  private getCacheKey(application: string, environment: string, name: string): string {
    return `${application}:${environment}:${name}`;
  }
}
