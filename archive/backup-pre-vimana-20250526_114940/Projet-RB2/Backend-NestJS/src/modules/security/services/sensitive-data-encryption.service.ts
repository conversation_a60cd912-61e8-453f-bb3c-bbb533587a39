import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { KeyManagementService } from './key-management.service';
import { ExtendedCipher, ExtendedDecipher } from '../interfaces/extended-crypto.interfaces';

/**
 * Interface pour les options de configuration du chiffrement des données sensibles
 */
export interface SensitiveDataEncryptionOptions {
  enabled: boolean;
  algorithm: string;
  sensitiveFields: string[];
  sensitiveTypes: ('financial' | 'personal' | 'health' | 'authentication' | 'location')[];
}

/**
 * Interface pour les données chiffrées
 */
export interface EncryptedData {
  data: string;
  iv: string;
  authTag: string;
  keyId: string;
  algorithm: string;
  version: number;
  metadata?: {
    encryptedFields: string[];
    encryptedAt: Date;
    sensitiveTypes: string[];
  };
}

/**
 * Service de chiffrement des données sensibles au repos
 * Ce service fournit des méthodes pour identifier et chiffrer les données sensibles
 * stockées dans la base de données ou dans d'autres systèmes de stockage
 */
@Injectable()
export class SensitiveDataEncryptionService {
  private readonly logger = new Logger(SensitiveDataEncryptionService.name);
  private readonly options: SensitiveDataEncryptionOptions;

  // Définition des champs sensibles par défaut
  private readonly DEFAULT_SENSITIVE_FIELDS = [
    'password', 'creditCard', 'cardNumber', 'cvv', 'ssn', 'socialSecurityNumber',
    'passport', 'drivingLicense', 'bankAccount', 'iban', 'taxId', 'healthId',
    'medicalRecord', 'biometricData', 'fingerprint', 'dna', 'retinaScan',
    'privateKey', 'secret', 'token', 'apiKey', 'accessKey', 'secretKey',
    'authToken', 'refreshToken', 'pin', 'passcode'
  ];

  // Définition des types de données sensibles par défaut
  private readonly DEFAULT_SENSITIVE_TYPES = [
    'financial', 'personal', 'health', 'authentication', 'location'
  ];

  // Expressions régulières pour détecter les données sensibles
  private readonly SENSITIVE_DATA_PATTERNS = {
    creditCard: /\b(?:\d[ -]*?){13,16}\b/,
    ssn: /\b\d{3}[-\s]?\d{2}[-\s]?\d{4}\b/,
    email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/,
    phoneNumber: /\b(?:\+\d{1,3}[-\s]?)?\(?\d{3}\)?[-\s]?\d{3}[-\s]?\d{4}\b/,
    ipAddress: /\b(?:\d{1,3}\.){3}\d{1,3}\b/,
    password: /\b(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}\b/
  };

  constructor(
    private readonly configService: ConfigService,
    private readonly keyManagementService: KeyManagementService,
  ) {
    this.options = {
      enabled: this.configService.get<boolean>('encryption.sensitiveData.enabled', true),
      algorithm: this.configService.get<string>('encryption.sensitiveData.algorithm', 'aes-256-gcm'),
      sensitiveFields: this.configService.get<string[]>(
        'encryption.sensitiveData.sensitiveFields',
        this.DEFAULT_SENSITIVE_FIELDS
      ),
      sensitiveTypes: this.configService.get<string[]>(
        'encryption.sensitiveData.sensitiveTypes',
        this.DEFAULT_SENSITIVE_TYPES
      ) as ('financial' | 'personal' | 'health' | 'authentication' | 'location')[],
    };

    this.logger.log(`Sensitive data encryption service initialized with algorithm: ${this.options.algorithm}`);
    this.logger.debug(`Monitoring ${this.options.sensitiveFields.length} sensitive fields`);
  }

  /**
   * Vérifie si le service est activé
   * @returns true si le service est activé
   */
  isEnabled(): boolean {
    return this.options.enabled;
  }

  /**
   * Chiffre des données sensibles
   * @param data Données à chiffrer
   * @param type Type de données sensibles
   * @returns Données chiffrées
   */
  async encryptSensitiveData(data: string | object, type?: string): Promise<EncryptedData> {
    try {
      if (!this.options.enabled) {
        throw new Error('Sensitive data encryption is disabled');
      }

      // Convertir les objets en JSON
      const dataString = typeof data === 'string' ? data : JSON.stringify(data);

      // Récupérer une clé active pour le chiffrement
      const { key, metadata: keyMetadata } = await this.keyManagementService.getActiveKey('encryption', type || 'general');

      // Générer un vecteur d'initialisation aléatoire
      const iv = crypto.randomBytes(12);

      // Chiffrer les données
      const cipher = crypto.createCipheriv(this.options.algorithm, key, iv) as ExtendedCipher;

      let encryptedData = cipher.update(dataString, 'utf8', 'base64');
      encryptedData += cipher.final('base64');

      // Récupérer le tag d'authentification
      const authTag = cipher.getAuthTag();

      return {
        data: encryptedData,
        iv: iv.toString('base64'),
        authTag: authTag.toString('base64'),
        keyId: keyMetadata.id,
        algorithm: this.options.algorithm,
        version: keyMetadata.version,
        metadata: {
          encryptedFields: typeof data === 'object' ? this.identifySensitiveFields(data) : ['all'],
          encryptedAt: new Date(),
          sensitiveTypes: [type || 'general']
        }
      };
    } catch (error) {
      this.logger.error('Failed to encrypt sensitive data:', error);
      throw error;
    }
  }

  /**
   * Déchiffre des données sensibles
   * @param encryptedData Données chiffrées
   * @returns Données déchiffrées
   */
  async decryptSensitiveData(encryptedData: EncryptedData): Promise<string> {
    try {
      if (!this.options.enabled) {
        throw new Error('Sensitive data encryption is disabled');
      }

      // Récupérer la clé utilisée pour le chiffrement
      const { key } = await this.keyManagementService.getKey(encryptedData.keyId);

      // Déchiffrer les données
      const iv = Buffer.from(encryptedData.iv, 'base64');
      const authTag = Buffer.from(encryptedData.authTag, 'base64');

      const decipher = crypto.createDecipheriv(encryptedData.algorithm, key, iv) as ExtendedDecipher;
      decipher.setAuthTag(authTag);

      let decrypted = decipher.update(encryptedData.data, 'base64', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      this.logger.error('Failed to decrypt sensitive data:', error);
      throw error;
    }
  }

  /**
   * Identifie les champs sensibles dans un objet
   * @param data Objet à analyser
   * @returns Liste des champs sensibles trouvés
   */
  identifySensitiveFields(data: object): string[] {
    const sensitiveFields: string[] = [];

    // Fonction récursive pour parcourir l'objet
    const traverse = (obj: any, path: string = '') => {
      if (!obj || typeof obj !== 'object') {
        return;
      }

      for (const [key, value] of Object.entries(obj)) {
        const currentPath = path ? `${path}.${key}` : key;

        // Vérifier si le champ est sensible
        if (this.isSensitiveField(key, value)) {
          sensitiveFields.push(currentPath);
        }

        // Parcourir récursivement les objets imbriqués
        if (value && typeof value === 'object' && !Buffer.isBuffer(value)) {
          traverse(value, currentPath);
        }
      }
    };

    traverse(data);
    return sensitiveFields;
  }

  /**
   * Vérifie si un champ est sensible
   * @param fieldName Nom du champ
   * @param value Valeur du champ
   * @returns true si le champ est sensible
   */
  isSensitiveField(fieldName: string, value: any): boolean {
    // Vérifier si le nom du champ est dans la liste des champs sensibles
    const isNameSensitive = this.options.sensitiveFields.some(field =>
      fieldName.toLowerCase().includes(field.toLowerCase())
    );

    if (isNameSensitive) {
      return true;
    }

    // Vérifier si la valeur correspond à un pattern de données sensibles
    if (typeof value === 'string') {
      return Object.values(this.SENSITIVE_DATA_PATTERNS).some(pattern =>
        pattern.test(value)
      );
    }

    return false;
  }

  /**
   * Chiffre les champs sensibles d'un objet
   * @param data Objet à chiffrer
   * @returns Objet avec les champs sensibles chiffrés
   */
  async encryptSensitiveFields(data: object): Promise<object> {
    try {
      if (!this.options.enabled) {
        return data;
      }

      // Copier l'objet pour ne pas modifier l'original
      const result = JSON.parse(JSON.stringify(data));

      // Identifier les champs sensibles
      const sensitiveFields = this.identifySensitiveFields(data);

      if (sensitiveFields.length === 0) {
        return result;
      }

      // Fonction récursive pour chiffrer les champs sensibles
      const encryptFields = async (obj: any, path: string = '') => {
        if (!obj || typeof obj !== 'object') {
          return;
        }

        for (const [key, value] of Object.entries(obj)) {
          const currentPath = path ? `${path}.${key}` : key;

          // Chiffrer le champ s'il est sensible
          if (sensitiveFields.includes(currentPath)) {
            if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
              const encryptedData = await this.encryptSensitiveData(String(value));
              obj[key] = {
                __encrypted: true,
                data: encryptedData.data,
                iv: encryptedData.iv,
                authTag: encryptedData.authTag,
                keyId: encryptedData.keyId,
                algorithm: encryptedData.algorithm,
                version: encryptedData.version
              };
            }
          } else if (value && typeof value === 'object' && !Buffer.isBuffer(value)) {
            // Parcourir récursivement les objets imbriqués
            await encryptFields(value, currentPath);
          }
        }
      };

      await encryptFields(result);
      return result;
    } catch (error) {
      this.logger.error('Failed to encrypt sensitive fields:', error);
      return data;
    }
  }

  /**
   * Déchiffre les champs sensibles d'un objet
   * @param data Objet avec des champs chiffrés
   * @returns Objet avec les champs déchiffrés
   */
  async decryptSensitiveFields(data: object): Promise<object> {
    try {
      if (!this.options.enabled) {
        return data;
      }

      // Copier l'objet pour ne pas modifier l'original
      const result = JSON.parse(JSON.stringify(data));

      // Fonction récursive pour déchiffrer les champs
      const decryptFields = async (obj: any) => {
        if (!obj || typeof obj !== 'object') {
          return;
        }

        for (const [key, value] of Object.entries(obj)) {
          if (value && typeof value === 'object') {
            // Vérifier si l'objet a la propriété __encrypted
            const encryptedObj = value as any;
            if (encryptedObj.__encrypted === true) {
              // Déchiffrer le champ
              const encryptedData: EncryptedData = {
                data: encryptedObj.data,
                iv: encryptedObj.iv,
                authTag: encryptedObj.authTag,
                keyId: encryptedObj.keyId,
                algorithm: encryptedObj.algorithm,
                version: encryptedObj.version
              };

              obj[key] = await this.decryptSensitiveData(encryptedData);
            } else if (!Buffer.isBuffer(value)) {
              // Parcourir récursivement les objets imbriqués
              await decryptFields(value);
            }
          }
        }
      };

      await decryptFields(result);
      return result;
    } catch (error) {
      this.logger.error('Failed to decrypt sensitive fields:', error);
      return data;
    }
  }

  /**
   * Chiffre un fichier contenant des données sensibles
   * @param fileData Données du fichier
   * @param fileType Type de fichier
   * @returns Fichier chiffré
   */
  async encryptSensitiveFile(fileData: Buffer, fileType: string): Promise<{
    encryptedData: Buffer;
    metadata: {
      iv: string;
      authTag: string;
      keyId: string;
      algorithm: string;
      version: number;
      fileType: string;
      encryptedAt: Date;
    };
  }> {
    try {
      if (!this.options.enabled) {
        throw new Error('Sensitive data encryption is disabled');
      }

      // Récupérer une clé active pour le chiffrement
      const { key, metadata: keyMetadata } = await this.keyManagementService.getActiveKey('encryption', 'file');

      // Générer un vecteur d'initialisation aléatoire
      const iv = crypto.randomBytes(12);

      // Chiffrer le fichier
      const cipher = crypto.createCipheriv(this.options.algorithm, key, iv) as ExtendedCipher;

      const encryptedData = Buffer.concat([
        cipher.update(fileData),
        cipher.final()
      ]);

      // Récupérer le tag d'authentification
      const authTag = cipher.getAuthTag();

      return {
        encryptedData,
        metadata: {
          iv: iv.toString('base64'),
          authTag: authTag.toString('base64'),
          keyId: keyMetadata.id,
          algorithm: this.options.algorithm,
          version: keyMetadata.version,
          fileType,
          encryptedAt: new Date()
        }
      };
    } catch (error) {
      this.logger.error('Failed to encrypt sensitive file:', error);
      throw error;
    }
  }

  /**
   * Déchiffre un fichier contenant des données sensibles
   * @param encryptedData Données chiffrées
   * @param metadata Métadonnées du fichier chiffré
   * @returns Fichier déchiffré
   */
  async decryptSensitiveFile(
    encryptedData: Buffer,
    metadata: {
      iv: string;
      authTag: string;
      keyId: string;
      algorithm: string;
    }
  ): Promise<Buffer> {
    try {
      if (!this.options.enabled) {
        throw new Error('Sensitive data encryption is disabled');
      }

      // Récupérer la clé utilisée pour le chiffrement
      const { key } = await this.keyManagementService.getKey(metadata.keyId);

      // Déchiffrer le fichier
      const iv = Buffer.from(metadata.iv, 'base64');
      const authTag = Buffer.from(metadata.authTag, 'base64');

      const decipher = crypto.createDecipheriv(metadata.algorithm, key, iv) as ExtendedDecipher;
      decipher.setAuthTag(authTag);

      return Buffer.concat([
        decipher.update(encryptedData),
        decipher.final()
      ]);
    } catch (error) {
      this.logger.error('Failed to decrypt sensitive file:', error);
      throw error;
    }
  }

  /**
   * Vérifie si un type de données est considéré comme sensible
   * @param type Type de données
   * @returns true si le type est sensible
   */
  isSensitiveType(type: string): boolean {
    return this.options.sensitiveTypes.includes(type as any);
  }

  /**
   * Obtient la liste des champs sensibles configurés
   * @returns Liste des champs sensibles
   */
  getSensitiveFields(): string[] {
    return [...this.options.sensitiveFields];
  }

  /**
   * Obtient la liste des types de données sensibles configurés
   * @returns Liste des types de données sensibles
   */
  getSensitiveTypes(): string[] {
    return [...this.options.sensitiveTypes];
  }

  /**
   * Ajoute un champ sensible à la configuration
   * @param fieldName Nom du champ sensible
   */
  addSensitiveField(fieldName: string): void {
    if (!this.options.sensitiveFields.includes(fieldName)) {
      this.options.sensitiveFields.push(fieldName);
      this.logger.log(`Added sensitive field: ${fieldName}`);
    }
  }

  /**
   * Ajoute un pattern de détection de données sensibles
   * @param name Nom du pattern
   * @param pattern Expression régulière
   */
  addSensitivePattern(name: string, pattern: RegExp): void {
    (this.SENSITIVE_DATA_PATTERNS as any)[name] = pattern;
    this.logger.log(`Added sensitive data pattern: ${name}`);
  }
}
