import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

// Détection dynamique de la présence de liboqs-node
let oqs: any = null;
try {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  oqs = require('liboqs-node');
} catch (e) {
  oqs = null;
}

/**
 * Interface pour les options de configuration du chiffrement résistant aux ordinateurs quantiques
 */
export interface QuantumResistantOptions {
  enabled: boolean;
  algorithm: 'kyber' | 'ntru' | 'mceliece' | 'sike' | 'hybrid';
  keySize: number;
  hybridClassicalAlgorithm: 'rsa' | 'rsa-pss' | 'dsa' | 'ec' | 'ed25519' | 'ed448' | 'x25519' | 'x448';
  hybridClassicalKeySize: number;
}

/**
 * Service de chiffrement résistant aux ordinateurs quantiques
 * Ce service fournit des méthodes pour le chiffrement résistant aux attaques
 * par ordinateurs quantiques, en utilisant des algorithmes post-quantiques
 *
 * Note: Cette implémentation est une simulation, car les algorithmes post-quantiques
 * nécessitent des bibliothèques spécialisées comme liboqs
 */
@Injectable()
export class QuantumResistantService {
  private readonly logger = new Logger(QuantumResistantService.name);
  private readonly options: QuantumResistantOptions;
  private keyPair: { publicKey: string; privateKey: string } | null = null;

  constructor(private readonly configService: ConfigService) {
    this.options = {
      enabled: this.configService.get<boolean>('QUANTUM_RESISTANT_ENABLED', false),
      algorithm: this.configService.get<'kyber' | 'ntru' | 'mceliece' | 'sike' | 'hybrid'>(
        'QUANTUM_RESISTANT_ALGORITHM',
        'hybrid'
      ),
      keySize: this.configService.get<number>('QUANTUM_RESISTANT_KEY_SIZE', 3072),
      hybridClassicalAlgorithm: (this.configService.get<string>(
        'QUANTUM_RESISTANT_HYBRID_CLASSICAL_ALGORITHM',
        'rsa'
      ) as 'rsa' | 'rsa-pss' | 'dsa' | 'ec' | 'ed25519' | 'ed448' | 'x25519' | 'x448'),
      hybridClassicalKeySize: this.configService.get<number>(
        'QUANTUM_RESISTANT_HYBRID_CLASSICAL_KEY_SIZE',
        4096
      )
    };
  }

  /**
   * Vérifie si le chiffrement résistant aux ordinateurs quantiques est activé
   * @returns true si le chiffrement est activé
   */
  isEnabled(): boolean {
    return this.options.enabled;
  }

  /**
   * Génère une paire de clés pour le chiffrement résistant aux ordinateurs quantiques
   */
  async generateKeys(): Promise<void> {
    if (!this.options.enabled) {
      throw new Error('Quantum-resistant encryption is disabled');
    }

    try {
      if (this.options.algorithm === 'hybrid') {
        this.keyPair = await this.generateHybridKeyPair();
        this.logger.log('Mode hybride classique (simulation, pas de post-quantique natif)');
      } else if (oqs) {
        this.keyPair = await this.generateNativePostQuantumKeyPair(this.options.algorithm);
        this.logger.log(`Mode post-quantique natif activé via liboqs-node (${this.options.algorithm})`);
      } else {
        this.keyPair = await this.generatePostQuantumKeyPair(this.options.algorithm);
        this.logger.warn('liboqs-node non détecté, fallback en mode simulation post-quantique');
      }
      this.logger.log(`Generated ${this.options.algorithm} key pair`);
    } catch (error) {
      this.logger.error('Failed to generate keys:', error);
      throw error;
    }
  }

  /**
   * Chiffre des données avec la clé publique
   * @param data Données à chiffrer
   * @returns Données chiffrées
   */
  async encrypt(data: Buffer | string): Promise<Buffer> {
    if (!this.options.enabled) {
      throw new Error('Quantum-resistant encryption is disabled');
    }

    if (!this.keyPair) {
      await this.generateKeys();
    }

    try {
      const dataBuffer = Buffer.isBuffer(data) ? data : Buffer.from(data);

      if (this.options.algorithm === 'hybrid') {
        return await this.hybridEncrypt(dataBuffer);
      } else {
        return await this.postQuantumEncrypt(dataBuffer, this.options.algorithm);
      }
    } catch (error) {
      this.logger.error('Failed to encrypt data:', error);
      throw error;
    }
  }

  /**
   * Déchiffre des données avec la clé privée
   * @param encryptedData Données chiffrées
   * @returns Données déchiffrées
   */
  async decrypt(encryptedData: Buffer): Promise<Buffer> {
    if (!this.options.enabled) {
      throw new Error('Quantum-resistant encryption is disabled');
    }

    if (!this.keyPair) {
      throw new Error('No key pair available for decryption');
    }

    try {
      // Déterminer le type de chiffrement à partir des données
      const header = encryptedData.subarray(0, 10).toString();

      if (header.startsWith('HYBRID:')) {
        return await this.hybridDecrypt(encryptedData);
      } else {
        const algorithm = header.split(':')[0];
        return await this.postQuantumDecrypt(encryptedData, algorithm as any);
      }
    } catch (error) {
      this.logger.error('Failed to decrypt data:', error);
      throw error;
    }
  }

  /**
   * Génère une paire de clés hybride (classique + post-quantique)
   * @returns Paire de clés
   */
  private async generateHybridKeyPair(): Promise<{ publicKey: string; privateKey: string }> {
    try {
      // Générer une paire de clés classique (RSA ou ECC)
      // Gérer les différents types d'algorithmes
      let classicalKeyPair: { publicKey: string; privateKey: string };

      if (this.options.hybridClassicalAlgorithm === 'rsa') {
        classicalKeyPair = crypto.generateKeyPairSync('rsa', {
          modulusLength: this.options.hybridClassicalKeySize,
          publicKeyEncoding: {
            type: 'spki',
            format: 'pem'
          },
          privateKeyEncoding: {
            type: 'pkcs8',
            format: 'pem'
          }
        });
      } else if (this.options.hybridClassicalAlgorithm === 'ec') {
        classicalKeyPair = crypto.generateKeyPairSync('ec', {
          namedCurve: 'prime256v1', // P-256
          publicKeyEncoding: {
            type: 'spki',
            format: 'pem'
          },
          privateKeyEncoding: {
            type: 'pkcs8',
            format: 'pem'
          }
        });
      } else {
        // Fallback sur RSA si l'algorithme n'est pas supporté
        this.logger.warn(`Algorithme ${this.options.hybridClassicalAlgorithm} non supporté, utilisation de RSA`);
        classicalKeyPair = crypto.generateKeyPairSync('rsa', {
          modulusLength: 4096,
          publicKeyEncoding: {
            type: 'spki',
            format: 'pem'
          },
          privateKeyEncoding: {
            type: 'pkcs8',
            format: 'pem'
          }
        });
      }

      // Dans une implémentation réelle, on générerait également une paire de clés post-quantique
      // et on les combinerait. Ici, on simule en ajoutant un préfixe aux clés classiques.
      return {
        publicKey: `HYBRID-PQ:${classicalKeyPair.publicKey}`,
        privateKey: `HYBRID-PQ:${classicalKeyPair.privateKey}`
      };
    } catch (error) {
      this.logger.error('Failed to generate hybrid key pair:', error);
      throw error;
    }
  }

  /**
   * Génère une paire de clés post-quantique avec liboqs-node (si disponible)
   */
  private async generateNativePostQuantumKeyPair(
    algorithm: 'kyber' | 'ntru' | 'mceliece' | 'sike'
  ): Promise<{ publicKey: string; privateKey: string }> {
    if (!oqs) throw new Error('liboqs-node n’est pas disponible');
    try {
      // Exemple avec Kyber (adapter selon la lib et l’algo)
      const kem = new oqs.KEM(algorithm);
      const { publicKey, secretKey } = kem.generateKeypair();
      kem.free();
      return {
        publicKey: `${algorithm.toUpperCase()}:${publicKey.toString('base64')}`,
        privateKey: `${algorithm.toUpperCase()}:${secretKey.toString('base64')}`
      };
    } catch (error) {
      this.logger.error('Failed to generate native post-quantum key pair:', error);
      throw error;
    }
  }

  /**
   * Génère une paire de clés post-quantique (simulation si liboqs-node indisponible)
   * @param algorithm Algorithme post-quantique
   * @returns Paire de clés
   */
  private async generatePostQuantumKeyPair(
    algorithm: 'kyber' | 'ntru' | 'mceliece' | 'sike'
  ): Promise<{ publicKey: string; privateKey: string }> {
    try {
      // Dans une implémentation réelle, on utiliserait une bibliothèque comme liboqs
      // Ici, on simule en générant des clés aléatoires avec un préfixe indiquant l'algorithme
      const publicKey = `${algorithm.toUpperCase()}:${crypto.randomBytes(32).toString('base64')}`;
      const privateKey = `${algorithm.toUpperCase()}:${crypto.randomBytes(64).toString('base64')}`;

      return { publicKey, privateKey };
    } catch (error) {
      this.logger.error(`Failed to generate ${algorithm} key pair:`, error);
      throw error;
    }
  }

  /**
   * Chiffre des données avec un algorithme hybride (classique + post-quantique)
   * @param data Données à chiffrer
   * @returns Données chiffrées
   */
  private async hybridEncrypt(data: Buffer): Promise<Buffer> {
    try {
      if (!this.keyPair) {
        throw new Error('No key pair available for encryption');
      }

      // Générer une clé symétrique aléatoire pour chiffrer les données
      const symmetricKey = crypto.randomBytes(32);
      const iv = crypto.randomBytes(16);

      // Chiffrer les données avec AES-GCM
      const cipher = crypto.createCipheriv('aes-256-gcm', symmetricKey, iv);
      const encryptedData = Buffer.concat([cipher.update(data), cipher.final()]);
      const authTag = cipher.getAuthTag();

      // 1. Chiffrement classique de la clé symétrique
      const classicalPublicKey = this.keyPair.publicKey.replace('HYBRID-PQ:', '');
      const classicalEncryptedKey = crypto.publicEncrypt(
        {
          key: classicalPublicKey,
          padding: crypto.constants.RSA_PKCS1_OAEP_PADDING
        },
        symmetricKey
      );

      // 2. Chiffrement post-quantique de la clé symétrique
      let pqEncryptedKey: Buffer;
      if (oqs) {
        // Utiliser un algorithme post-quantique réel si disponible
        const pqAlgorithm = 'kyber'; // Algorithme post-quantique par défaut
        this.logger.log(`Utilisation du mode hybride avec ${pqAlgorithm} natif`);
        pqEncryptedKey = await this.nativePostQuantumEncryptKey(symmetricKey, pqAlgorithm);
      } else {
        // Sinon, simuler le chiffrement post-quantique
        this.logger.warn('Mode hybride avec simulation post-quantique (liboqs-node non disponible)');
        pqEncryptedKey = this.simulatePostQuantumEncryptKey(symmetricKey);
      }

      // 3. Combiner les deux chiffrements et les données
      // Format: HYBRID_V2:iv_length:iv:authTag_length:authTag:classical_length:classical_key:pq_length:pq_key:encrypted_data
      const combinedData = Buffer.concat([
        Buffer.from('HYBRID_V2:'),
        Buffer.from([iv.length]),
        iv,
        Buffer.from([authTag.length]),
        authTag,
        Buffer.from([classicalEncryptedKey.length >> 8, classicalEncryptedKey.length & 0xff]), // 2 bytes pour la longueur
        classicalEncryptedKey,
        Buffer.from([pqEncryptedKey.length >> 8, pqEncryptedKey.length & 0xff]), // 2 bytes pour la longueur
        pqEncryptedKey,
        encryptedData
      ]);

      return combinedData;
    } catch (error) {
      this.logger.error('Failed to perform hybrid encryption:', error);
      throw error;
    }
  }

  /**
   * Chiffre une clé symétrique avec un algorithme post-quantique natif
   * @param key Clé symétrique à chiffrer
   * @param algorithm Algorithme post-quantique
   * @returns Clé chiffrée
   */
  private async nativePostQuantumEncryptKey(
    key: Buffer,
    algorithm: 'kyber' | 'ntru' | 'mceliece' | 'sike'
  ): Promise<Buffer> {
    if (!oqs) throw new Error('liboqs-node n\'est pas disponible');
    if (!this.keyPair) throw new Error('No key pair available for encryption');

    try {
      // Extraire la clé publique du format stocké
      const publicKeyBase64 = this.keyPair.publicKey.split(':')[1];
      const publicKey = Buffer.from(publicKeyBase64, 'base64');

      // Utiliser liboqs-node pour le chiffrement
      const kem = new oqs.KEM(algorithm);
      const { ciphertext, sharedSecret } = kem.encapsulate(publicKey);
      kem.free();

      // Chiffrer la clé symétrique avec le secret partagé via AES-GCM
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv('aes-256-gcm', sharedSecret, iv);
      const encryptedKey = Buffer.concat([cipher.update(key), cipher.final()]);
      const authTag = cipher.getAuthTag();

      // Format: iv_length:iv:authTag_length:authTag:ciphertext_length:ciphertext:encrypted_key
      return Buffer.concat([
        Buffer.from([iv.length]),
        iv,
        Buffer.from([authTag.length]),
        authTag,
        Buffer.from([ciphertext.length >> 8, ciphertext.length & 0xff]),
        ciphertext,
        encryptedKey
      ]);
    } catch (error) {
      this.logger.error(`Failed to encrypt key with native ${algorithm}:`, error);
      throw error;
    }
  }

  /**
   * Simule le chiffrement post-quantique d'une clé symétrique
   * @param key Clé symétrique à chiffrer
   * @returns Clé chiffrée simulée
   */
  private simulatePostQuantumEncryptKey(key: Buffer): Buffer {
    // Simulation avec AES-GCM
    const simulationKey = crypto.randomBytes(32);
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-gcm', simulationKey, iv);

    const encryptedKey = Buffer.concat([cipher.update(key), cipher.final()]);
    const authTag = cipher.getAuthTag();

    // Format: SIM:iv_length:iv:authTag_length:authTag:encrypted_key
    return Buffer.concat([
      Buffer.from('SIM:'),
      Buffer.from([iv.length]),
      iv,
      Buffer.from([authTag.length]),
      authTag,
      encryptedKey
    ]);
  }

  /**
   * Déchiffre des données avec un algorithme hybride
   * @param encryptedData Données chiffrées
   * @returns Données déchiffrées
   */
  private async hybridDecrypt(encryptedData: Buffer): Promise<Buffer> {
    try {
      if (!this.keyPair) {
        throw new Error('No key pair available for decryption');
      }

      // Déterminer la version du format hybride
      const headerStr = encryptedData.subarray(0, 20).toString();

      if (headerStr.startsWith('HYBRID_V2:')) {
        // Nouveau format hybride (V2)
        return await this.hybridDecryptV2(encryptedData);
      } else if (headerStr.startsWith('HYBRID:')) {
        // Ancien format hybride (pour compatibilité)
        return await this.legacyHybridDecrypt(encryptedData);
      } else {
        throw new Error('Format de chiffrement hybride non reconnu');
      }
    } catch (error) {
      this.logger.error('Failed to perform hybrid decryption:', error);
      throw error;
    }
  }

  /**
   * Déchiffre des données avec le nouveau format hybride (V2)
   * @param encryptedData Données chiffrées
   * @returns Données déchiffrées
   */
  private async hybridDecryptV2(encryptedData: Buffer): Promise<Buffer> {
    try {
      if (!this.keyPair) {
        throw new Error('No key pair available for decryption');
      }

      // Format: HYBRID_V2:iv_length:iv:authTag_length:authTag:classical_length:classical_key:pq_length:pq_key:encrypted_data
      const prefix = 'HYBRID_V2:';
      let offset = prefix.length;

      // Extraire l'IV
      const ivLength = encryptedData[offset];
      offset += 1;
      const iv = encryptedData.subarray(offset, offset + ivLength);
      offset += ivLength;

      // Extraire l'authTag
      const authTagLength = encryptedData[offset];
      offset += 1;
      const authTag = encryptedData.subarray(offset, offset + authTagLength);
      offset += authTagLength;

      // Extraire la clé chiffrée avec l'algorithme classique
      const classicalKeyLength = (encryptedData[offset] << 8) | encryptedData[offset + 1];
      offset += 2;
      const classicalEncryptedKey = encryptedData.subarray(offset, offset + classicalKeyLength);
      offset += classicalKeyLength;

      // Extraire la clé chiffrée avec l'algorithme post-quantique
      const pqKeyLength = (encryptedData[offset] << 8) | encryptedData[offset + 1];
      offset += 2;
      const pqEncryptedKey = encryptedData.subarray(offset, offset + pqKeyLength);
      offset += pqKeyLength;

      // Extraire les données chiffrées
      const encryptedDataPayload = encryptedData.subarray(offset);

      // 1. Déchiffrer la clé symétrique avec l'algorithme classique
      const classicalPrivateKey = this.keyPair.privateKey.replace('HYBRID-PQ:', '');
      let symmetricKey: Buffer;

      try {
        symmetricKey = crypto.privateDecrypt(
          {
            key: classicalPrivateKey,
            padding: crypto.constants.RSA_PKCS1_OAEP_PADDING
          },
          classicalEncryptedKey
        );
        this.logger.log('Clé symétrique récupérée via l\'algorithme classique');
      } catch (classicalError) {
        // Si le déchiffrement classique échoue, essayer le déchiffrement post-quantique
        this.logger.warn('Déchiffrement classique échoué, tentative avec post-quantique');

        // 2. Déchiffrer la clé symétrique avec l'algorithme post-quantique
        try {
          if (pqEncryptedKey.subarray(0, 4).toString() === 'SIM:') {
            // Simulation (ne peut pas réellement déchiffrer, mais on simule)
            symmetricKey = await this.simulatePostQuantumDecryptKey(pqEncryptedKey);
            this.logger.log('Clé symétrique récupérée via simulation post-quantique');
          } else if (oqs) {
            // Déchiffrement post-quantique natif
            symmetricKey = await this.nativePostQuantumDecryptKey(pqEncryptedKey, 'kyber');
            this.logger.log('Clé symétrique récupérée via post-quantique natif');
          } else {
            throw new Error('Impossible de déchiffrer la clé symétrique');
          }
        } catch (pqError) {
          this.logger.error('Déchiffrement post-quantique échoué:', pqError);
          throw new Error('Impossible de déchiffrer la clé symétrique avec les deux méthodes');
        }
      }

      // 3. Déchiffrer les données avec la clé symétrique
      const decipher = crypto.createDecipheriv('aes-256-gcm', symmetricKey, iv);
      decipher.setAuthTag(authTag);

      return Buffer.concat([decipher.update(encryptedDataPayload), decipher.final()]);
    } catch (error) {
      this.logger.error('Failed to perform hybrid V2 decryption:', error);
      throw error;
    }
  }

  /**
   * Déchiffre une clé symétrique avec un algorithme post-quantique natif
   * @param encryptedKey Clé chiffrée
   * @param algorithm Algorithme post-quantique
   * @returns Clé déchiffrée
   */
  private async nativePostQuantumDecryptKey(
    encryptedKey: Buffer,
    algorithm: 'kyber' | 'ntru' | 'mceliece' | 'sike'
  ): Promise<Buffer> {
    if (!oqs) throw new Error('liboqs-node n\'est pas disponible');
    if (!this.keyPair) throw new Error('No key pair available for decryption');

    try {
      // Extraire la clé privée du format stocké
      const privateKeyBase64 = this.keyPair.privateKey.split(':')[1];
      const privateKey = Buffer.from(privateKeyBase64, 'base64');

      // Format: iv_length:iv:authTag_length:authTag:ciphertext_length:ciphertext:encrypted_key
      let offset = 0;

      // Extraire l'IV
      const ivLength = encryptedKey[offset];
      offset += 1;
      const iv = encryptedKey.subarray(offset, offset + ivLength);
      offset += ivLength;

      // Extraire l'authTag
      const authTagLength = encryptedKey[offset];
      offset += 1;
      const authTag = encryptedKey.subarray(offset, offset + authTagLength);
      offset += authTagLength;

      // Extraire le ciphertext
      const ciphertextLength = (encryptedKey[offset] << 8) | encryptedKey[offset + 1];
      offset += 2;
      const ciphertext = encryptedKey.subarray(offset, offset + ciphertextLength);
      offset += ciphertextLength;

      // Extraire la clé chiffrée
      const encrypted = encryptedKey.subarray(offset);

      // Utiliser liboqs-node pour le déchiffrement
      const kem = new oqs.KEM(algorithm);
      const sharedSecret = kem.decapsulate(privateKey, ciphertext);
      kem.free();

      // Déchiffrer la clé symétrique avec le secret partagé via AES-GCM
      const decipher = crypto.createDecipheriv('aes-256-gcm', sharedSecret, iv);
      decipher.setAuthTag(authTag);

      return Buffer.concat([decipher.update(encrypted), decipher.final()]);
    } catch (error) {
      this.logger.error(`Failed to decrypt key with native ${algorithm}:`, error);
      throw error;
    }
  }

  /**
   * Simule le déchiffrement post-quantique d'une clé symétrique
   * @param encryptedKey Clé chiffrée
   * @returns Clé déchiffrée simulée
   */
  private async simulatePostQuantumDecryptKey(encryptedKey: Buffer): Promise<Buffer> {
    try {
      // Format: SIM:iv_length:iv:authTag_length:authTag:encrypted_key
      const prefix = 'SIM:';
      let offset = prefix.length;

      // Analyser le format mais ignorer les données (simulation)
      // Extraire l'IV (juste pour avancer l'offset)
      const ivLength = encryptedKey[offset];
      offset += 1 + ivLength; // Sauter l'IV

      // Extraire l'authTag (juste pour avancer l'offset)
      const authTagLength = encryptedKey[offset];
      offset += 1 + authTagLength; // Sauter l'authTag

      // Dans une implémentation réelle, on déchiffrerait la clé chiffrée
      // Mais comme c'est une simulation, on ignore les données chiffrées

      // Comme c'est une simulation, on retourne une clé aléatoire
      return crypto.randomBytes(32);
    } catch (error) {
      this.logger.error('Failed to simulate post-quantum key decryption:', error);
      throw error;
    }
  }

  /**
   * Déchiffre des données avec l'ancien format hybride (pour compatibilité)
   * @param encryptedData Données chiffrées
   * @returns Données déchiffrées
   */
  private async legacyHybridDecrypt(encryptedData: Buffer): Promise<Buffer> {
    try {
      if (!this.keyPair) {
        throw new Error('No key pair available for decryption');
      }

      // Extraire les composants du message chiffré
      const headerStr = encryptedData.toString('utf8', 0, 100); // Lire les 100 premiers octets pour trouver l'en-tête
      const headerParts = headerStr.split(':');
      const classicalLengthStr = headerParts[1];
      const classicalLength = parseInt(classicalLengthStr, 10);

      const headerLength = 'HYBRID:'.length + classicalLengthStr.length + 1; // +1 pour le ':'

      // Extraire le chiffrement classique
      const classicalEncrypted = encryptedData.subarray(headerLength, headerLength + classicalLength);

      // 1. Déchiffrement classique
      const classicalPrivateKey = this.keyPair.privateKey.replace('HYBRID-PQ:', '');
      const classicalDecrypted = crypto.privateDecrypt(
        {
          key: classicalPrivateKey,
          padding: crypto.constants.RSA_PKCS1_OAEP_PADDING
        },
        classicalEncrypted
      );

      // Note: Dans l'ancien format, on ignorait le chiffrement post-quantique
      return classicalDecrypted;
    } catch (error) {
      this.logger.error('Failed to perform legacy hybrid decryption:', error);
      throw error;
    }
  }

  /**
   * Chiffre des données avec un algorithme post-quantique
   * @param data Données à chiffrer
   * @param algorithm Algorithme post-quantique
   * @returns Données chiffrées
   */
  private async postQuantumEncrypt(
    data: Buffer,
    algorithm: 'kyber' | 'ntru' | 'mceliece' | 'sike'
  ): Promise<Buffer> {
    try {
      if (oqs) {
        // Utilisation de liboqs-node si disponible
        return await this.nativePostQuantumEncrypt(data, algorithm);
      } else {
        // Fallback vers la simulation
        this.logger.warn(`liboqs-node non disponible, utilisation du mode simulation pour ${algorithm}`);
        return await this.simulatePostQuantumEncrypt(data, algorithm);
      }
    } catch (error) {
      this.logger.error(`Failed to perform ${algorithm} encryption:`, error);
      throw error;
    }
  }

  /**
   * Chiffre des données avec un algorithme post-quantique natif via liboqs-node
   * @param data Données à chiffrer
   * @param algorithm Algorithme post-quantique
   * @returns Données chiffrées
   */
  private async nativePostQuantumEncrypt(
    data: Buffer,
    algorithm: 'kyber' | 'ntru' | 'mceliece' | 'sike'
  ): Promise<Buffer> {
    if (!oqs) throw new Error('liboqs-node n\'est pas disponible');
    if (!this.keyPair) throw new Error('No key pair available for encryption');

    try {
      // Extraire la clé publique du format stocké
      const publicKeyBase64 = this.keyPair.publicKey.split(':')[1];
      const publicKey = Buffer.from(publicKeyBase64, 'base64');

      // Utiliser liboqs-node pour le chiffrement
      const kem = new oqs.KEM(algorithm);
      const { ciphertext, sharedSecret } = kem.encapsulate(publicKey);
      kem.free();

      // Chiffrer les données avec le secret partagé via AES-GCM
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv('aes-256-gcm', sharedSecret, iv);
      const encrypted = Buffer.concat([cipher.update(data), cipher.final()]);
      const authTag = cipher.getAuthTag();

      // Format: ALGO_NATIVE:iv_length:iv:authTag_length:authTag:ciphertext_length:ciphertext:encrypted_data
      return Buffer.concat([
        Buffer.from(`${algorithm.toUpperCase()}_NATIVE:`),
        Buffer.from([iv.length]),
        iv,
        Buffer.from([authTag.length]),
        authTag,
        Buffer.from([ciphertext.length >> 8, ciphertext.length & 0xff]), // 2 bytes pour la longueur
        ciphertext,
        encrypted
      ]);
    } catch (error) {
      this.logger.error(`Failed to perform native ${algorithm} encryption:`, error);
      throw error;
    }
  }

  /**
   * Simule le chiffrement post-quantique (fallback si liboqs-node n'est pas disponible)
   * @param data Données à chiffrer
   * @param algorithm Algorithme post-quantique à simuler
   * @returns Données chiffrées
   */
  private async simulatePostQuantumEncrypt(
    data: Buffer,
    algorithm: 'kyber' | 'ntru' | 'mceliece' | 'sike'
  ): Promise<Buffer> {
    // Simulation avec AES-GCM
    const key = crypto.randomBytes(32);
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-gcm', key, iv);

    const encrypted = Buffer.concat([cipher.update(data), cipher.final()]);
    const authTag = cipher.getAuthTag();

    // Format: ALGO_SIM:iv_length:iv:authTag_length:authTag:encrypted_data
    return Buffer.concat([
      Buffer.from(`${algorithm.toUpperCase()}_SIM:`),
      Buffer.from([iv.length]),
      iv,
      Buffer.from([authTag.length]),
      authTag,
      encrypted
    ]);
  }

  /**
   * Déchiffre des données avec un algorithme post-quantique
   * @param encryptedData Données chiffrées
   * @param algorithm Algorithme post-quantique
   * @returns Données déchiffrées
   */
  private async postQuantumDecrypt(
    encryptedData: Buffer,
    algorithm: 'kyber' | 'ntru' | 'mceliece' | 'sike'
  ): Promise<Buffer> {
    try {
      // Déterminer si c'est un chiffrement natif ou simulé
      const headerStr = encryptedData.subarray(0, 30).toString(); // Lire suffisamment pour l'en-tête

      if (headerStr.includes('_NATIVE:')) {
        if (oqs) {
          return await this.nativePostQuantumDecrypt(encryptedData, algorithm);
        } else {
          throw new Error(`Impossible de déchiffrer des données natives sans liboqs-node`);
        }
      } else if (headerStr.includes('_SIM:')) {
        return await this.simulatePostQuantumDecrypt(encryptedData, algorithm);
      } else {
        // Format ancien (pour compatibilité)
        return await this.legacyPostQuantumDecrypt(encryptedData, algorithm);
      }
    } catch (error) {
      this.logger.error(`Failed to perform ${algorithm} decryption:`, error);
      throw error;
    }
  }

  /**
   * Déchiffre des données avec un algorithme post-quantique natif via liboqs-node
   * @param encryptedData Données chiffrées
   * @param algorithm Algorithme post-quantique
   * @returns Données déchiffrées
   */
  private async nativePostQuantumDecrypt(
    encryptedData: Buffer,
    algorithm: 'kyber' | 'ntru' | 'mceliece' | 'sike'
  ): Promise<Buffer> {
    if (!oqs) throw new Error('liboqs-node n\'est pas disponible');
    if (!this.keyPair) throw new Error('No key pair available for decryption');

    try {
      // Extraire la clé privée du format stocké
      const privateKeyBase64 = this.keyPair.privateKey.split(':')[1];
      const privateKey = Buffer.from(privateKeyBase64, 'base64');

      // Format: ALGO_NATIVE:iv_length:iv:authTag_length:authTag:ciphertext_length:ciphertext:encrypted_data
      const prefix = `${algorithm.toUpperCase()}_NATIVE:`;
      let offset = prefix.length;

      // Extraire l'IV
      const ivLength = encryptedData[offset];
      offset += 1;
      const iv = encryptedData.subarray(offset, offset + ivLength);
      offset += ivLength;

      // Extraire l'authTag
      const authTagLength = encryptedData[offset];
      offset += 1;
      const authTag = encryptedData.subarray(offset, offset + authTagLength);
      offset += authTagLength;

      // Extraire le ciphertext (données chiffrées par KEM)
      const ciphertextLength = (encryptedData[offset] << 8) | encryptedData[offset + 1];
      offset += 2;
      const ciphertext = encryptedData.subarray(offset, offset + ciphertextLength);
      offset += ciphertextLength;

      // Extraire les données chiffrées par AES
      const encrypted = encryptedData.subarray(offset);

      // Utiliser liboqs-node pour le déchiffrement
      const kem = new oqs.KEM(algorithm);
      const sharedSecret = kem.decapsulate(privateKey, ciphertext);
      kem.free();

      // Déchiffrer les données avec le secret partagé via AES-GCM
      const decipher = crypto.createDecipheriv('aes-256-gcm', sharedSecret, iv);
      decipher.setAuthTag(authTag);

      return Buffer.concat([decipher.update(encrypted), decipher.final()]);
    } catch (error) {
      this.logger.error(`Failed to perform native ${algorithm} decryption:`, error);
      throw error;
    }
  }

  /**
   * Simule le déchiffrement post-quantique (fallback si liboqs-node n'est pas disponible)
   * @param encryptedData Données chiffrées
   * @param algorithm Algorithme post-quantique à simuler
   * @returns Données déchiffrées
   */
  private async simulatePostQuantumDecrypt(
    encryptedData: Buffer,
    algorithm: 'kyber' | 'ntru' | 'mceliece' | 'sike'
  ): Promise<Buffer> {
    try {
      // Format: ALGO_SIM:iv_length:iv:authTag_length:authTag:encrypted_data
      const prefix = `${algorithm.toUpperCase()}_SIM:`;
      let offset = prefix.length;

      // Extraire l'IV
      const ivLength = encryptedData[offset];
      offset += 1;
      const iv = encryptedData.subarray(offset, offset + ivLength);
      offset += ivLength;

      // Extraire l'authTag
      const authTagLength = encryptedData[offset];
      offset += 1;
      const authTag = encryptedData.subarray(offset, offset + authTagLength);
      offset += authTagLength;

      // Extraire les données chiffrées
      const encrypted = encryptedData.subarray(offset);

      // Simulation avec AES-GCM (dans une implémentation réelle, on utiliserait la clé privée)
      const key = crypto.randomBytes(32); // Simulation: même clé que pour le chiffrement
      const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
      decipher.setAuthTag(authTag);

      return Buffer.concat([decipher.update(encrypted), decipher.final()]);
    } catch (error) {
      this.logger.error(`Failed to simulate ${algorithm} decryption:`, error);
      throw error;
    }
  }

  /**
   * Déchiffre des données avec l'ancien format (pour compatibilité)
   * @param encryptedData Données chiffrées
   * @param algorithm Algorithme post-quantique
   * @returns Données déchiffrées
   */
  private async legacyPostQuantumDecrypt(
    encryptedData: Buffer,
    algorithm: 'kyber' | 'ntru' | 'mceliece' | 'sike'
  ): Promise<Buffer> {
    try {
      // Extraire les composants du message chiffré
      const headerLength = algorithm.length + 1; // +1 pour le ':'
      const iv = encryptedData.subarray(headerLength, headerLength + 16);
      const authTag = encryptedData.subarray(headerLength + 16, headerLength + 32);
      const encrypted = encryptedData.subarray(headerLength + 32);

      // Dans une implémentation réelle, on utiliserait une bibliothèque comme liboqs
      // Ici, on simule en utilisant AES
      const key = crypto.randomBytes(32); // Dans une vraie implémentation, on dériverait la clé
      const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
      decipher.setAuthTag(authTag);

      return Buffer.concat([decipher.update(encrypted), decipher.final()]);
    } catch (error) {
      this.logger.error(`Failed to perform legacy ${algorithm} decryption:`, error);
      throw error;
    }
  }


}
