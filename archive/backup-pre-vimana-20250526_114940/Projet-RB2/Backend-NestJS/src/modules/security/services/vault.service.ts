import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as vault from 'node-vault';

/**
 * Interface pour les options de configuration de Vault
 */
export interface VaultOptions {
  endpoint: string;
  token: string;
  namespace?: string;
  apiVersion?: string;
  keyPrefix: string;
  enabled: boolean;
  sslVerify: boolean;
  timeout: number;
  retryOptions?: {
    retries: number;
    factor: number;
    minTimeout: number;
    maxTimeout: number;
  };
}

/**
 * Service d'intégration avec HashiCorp Vault
 * Ce service gère l'interaction avec Vault pour le stockage sécurisé des clés et secrets
 */
@Injectable()
export class VaultService implements OnModuleInit {
  private readonly logger = new Logger(VaultService.name);
  private client: vault.client;
  private readonly options: VaultOptions;
  private initialized = false;

  constructor(private readonly configService: ConfigService) {
    this.options = {
      endpoint: this.configService.get<string>('VAULT_ENDPOINT', 'http://localhost:8200'),
      token: this.configService.get<string>('VAULT_TOKEN', ''),
      namespace: this.configService.get<string>('VAULT_NAMESPACE'),
      apiVersion: this.configService.get<string>('VAULT_API_VERSION', 'v1'),
      keyPrefix: this.configService.get<string>('VAULT_KEY_PREFIX', 'retreat-and-be'),
      enabled: this.configService.get<boolean>('VAULT_ENABLED', false),
      sslVerify: this.configService.get<boolean>('VAULT_SSL_VERIFY', true),
      timeout: this.configService.get<number>('VAULT_TIMEOUT', 5000),
      retryOptions: {
        retries: this.configService.get<number>('VAULT_RETRY_COUNT', 3),
        factor: this.configService.get<number>('VAULT_RETRY_FACTOR', 2),
        minTimeout: this.configService.get<number>('VAULT_RETRY_MIN_TIMEOUT', 1000),
        maxTimeout: this.configService.get<number>('VAULT_RETRY_MAX_TIMEOUT', 10000),
      },
    };
  }

  /**
   * Initialisation du service lors du démarrage de l'application
   */
  async onModuleInit() {
    if (!this.options.enabled) {
      this.logger.warn('Vault integration is disabled. Using in-memory storage for secrets.');
      return;
    }

    try {
      await this.initializeClient();
      this.initialized = true;
      this.logger.log('Vault service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Vault service', error);
      this.logger.warn('Falling back to in-memory storage for secrets');
    }
  }

  /**
   * Initialise le client Vault
   */
  private async initializeClient(): Promise<void> {
    try {
      const vaultOptions: vault.VaultOptions = {
        apiVersion: this.options.apiVersion,
        endpoint: this.options.endpoint,
        token: this.options.token,
        namespace: this.options.namespace,
        requestOptions: {
          timeout: this.options.timeout,
        },
      };

      this.client = vault(vaultOptions);

      // Vérifier la connexion à Vault
      const status = await this.client.status();
      this.logger.debug(`Vault status: ${JSON.stringify(status)}`);

      // Vérifier si le moteur KV v2 est activé
      await this.ensureKVEngineEnabled();
    } catch (error) {
      this.logger.error('Failed to initialize Vault client', error);
      throw error;
    }
  }

  /**
   * S'assure que le moteur KV v2 est activé
   */
  private async ensureKVEngineEnabled(): Promise<void> {
    try {
      // Vérifier si le moteur KV v2 est déjà activé
      const mounts = await this.client.mounts();
      const kvPath = 'secret/';

      if (!mounts[kvPath]) {
        // Activer le moteur KV v2
        await this.client.mount({
          mount_point: 'secret',
          type: 'kv',
          options: {
            version: '2',
          },
        });
        this.logger.log('KV v2 engine enabled at secret/');
      } else {
        this.logger.debug('KV engine already enabled');
      }
    } catch (error) {
      this.logger.error('Failed to ensure KV engine is enabled', error);
      throw error;
    }
  }

  /**
   * Vérifie si le service Vault est initialisé et disponible
   */
  public isInitialized(): boolean {
    return this.initialized && this.options.enabled;
  }

  /**
   * Stocke un secret dans Vault
   * @param path Chemin du secret
   * @param data Données du secret
   */
  public async writeSecret(path: string, data: Record<string, any>): Promise<void> {
    if (!this.isInitialized()) {
      throw new Error('Vault service is not initialized');
    }

    try {
      const fullPath = this.getFullPath(path);
      await this.client.write(`secret/data/${fullPath}`, { data });
      this.logger.debug(`Secret written to: secret/data/${fullPath}`);
    } catch (error) {
      this.logger.error(`Failed to write secret to path: ${path}`, error);
      throw error;
    }
  }

  /**
   * Récupère un secret depuis Vault
   * @param path Chemin du secret
   * @returns Données du secret
   */
  public async readSecret(path: string): Promise<Record<string, any> | null> {
    if (!this.isInitialized()) {
      throw new Error('Vault service is not initialized');
    }

    try {
      const fullPath = this.getFullPath(path);
      const result = await this.client.read(`secret/data/${fullPath}`);
      return result?.data?.data || null;
    } catch (error) {
      // Si le secret n'existe pas, retourner null
      if (error.response?.statusCode === 404) {
        return null;
      }
      this.logger.error(`Failed to read secret from path: ${path}`, error);
      throw error;
    }
  }

  /**
   * Supprime un secret de Vault
   * @param path Chemin du secret
   */
  public async deleteSecret(path: string): Promise<void> {
    if (!this.isInitialized()) {
      throw new Error('Vault service is not initialized');
    }

    try {
      const fullPath = this.getFullPath(path);
      await this.client.delete(`secret/data/${fullPath}`);
      this.logger.debug(`Secret deleted from: secret/data/${fullPath}`);
    } catch (error) {
      this.logger.error(`Failed to delete secret from path: ${path}`, error);
      throw error;
    }
  }

  /**
   * Liste les secrets dans un chemin donné
   * @param path Chemin à lister
   * @returns Liste des clés
   */
  public async listSecrets(path: string): Promise<string[]> {
    if (!this.isInitialized()) {
      throw new Error('Vault service is not initialized');
    }

    try {
      const fullPath = this.getFullPath(path);
      const result = await this.client.list(`secret/metadata/${fullPath}`);
      return result?.data?.keys || [];
    } catch (error) {
      // Si le chemin n'existe pas, retourner un tableau vide
      if (error.response?.statusCode === 404) {
        return [];
      }
      this.logger.error(`Failed to list secrets at path: ${path}`, error);
      throw error;
    }
  }

  /**
   * Génère un chemin complet avec le préfixe configuré
   * @param path Chemin relatif
   * @returns Chemin complet
   */
  private getFullPath(path: string): string {
    return `${this.options.keyPrefix}/${path}`.replace(/\/+/g, '/').replace(/^\/|\/$/g, '');
  }

  /**
   * Génère une clé cryptographique aléatoire via Vault
   * @param keyType Type de clé (aes256-gcm, hmac-sha256, etc.)
   * @returns Clé générée en base64
   */
  public async generateKey(keyType: string): Promise<string> {
    if (!this.isInitialized()) {
      throw new Error('Vault service is not initialized');
    }

    try {
      // Déterminer les paramètres en fonction du type de clé
      let params: Record<string, any> = {};
      
      switch (keyType) {
        case 'aes128-gcm':
          params = { type: 'aes128-gcm96' };
          break;
        case 'aes256-gcm':
          params = { type: 'aes256-gcm96' };
          break;
        case 'chacha20-poly1305':
          params = { type: 'chacha20-poly1305' };
          break;
        case 'hmac-sha256':
          params = { type: 'hmac', key_type: 'sha256' };
          break;
        case 'hmac-sha512':
          params = { type: 'hmac', key_type: 'sha512' };
          break;
        case 'rsa-2048':
          params = { type: 'rsa', bits: 2048 };
          break;
        case 'rsa-4096':
          params = { type: 'rsa', bits: 4096 };
          break;
        case 'ed25519':
          params = { type: 'ed25519' };
          break;
        default:
          params = { type: 'aes256-gcm96' };
      }

      // Utiliser l'API Transit de Vault pour générer une clé
      // Note: Cela nécessite que le moteur Transit soit activé
      const transitPath = 'transit';
      const keyName = `generated-${Date.now()}`;

      // Vérifier si le moteur Transit est activé
      await this.ensureTransitEngineEnabled();

      // Créer une clé dans Transit
      await this.client.write(`${transitPath}/keys/${keyName}`, params);

      // Exporter la clé (si possible)
      const exportResult = await this.client.write(`${transitPath}/export/encryption-key/${keyName}`, {});
      const keyData = exportResult?.data?.keys?.['1'] || null;

      // Supprimer la clé de Transit après l'avoir exportée
      await this.client.delete(`${transitPath}/keys/${keyName}`);

      if (!keyData) {
        throw new Error('Failed to export key from Vault Transit');
      }

      return keyData;
    } catch (error) {
      this.logger.error(`Failed to generate key of type: ${keyType}`, error);
      throw error;
    }
  }

  /**
   * S'assure que le moteur Transit est activé
   */
  private async ensureTransitEngineEnabled(): Promise<void> {
    try {
      // Vérifier si le moteur Transit est déjà activé
      const mounts = await this.client.mounts();
      const transitPath = 'transit/';

      if (!mounts[transitPath]) {
        // Activer le moteur Transit
        await this.client.mount({
          mount_point: 'transit',
          type: 'transit',
        });
        this.logger.log('Transit engine enabled at transit/');
      } else {
        this.logger.debug('Transit engine already enabled');
      }
    } catch (error) {
      this.logger.error('Failed to ensure Transit engine is enabled', error);
      throw error;
    }
  }
}
