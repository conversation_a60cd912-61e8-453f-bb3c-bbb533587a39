import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from '../../../prisma/prisma.service';
import { SecurityEventService } from './security-event.service';
import { ConfigService } from '@nestjs/config';
import * as path from 'path';
import * as fs from 'fs';
import * as nodemailer from 'nodemailer';

export enum ComplianceFramework {
  GDPR = 'GDPR',
  PCI_DSS = 'PCI_DSS',
  ISO_27001 = 'ISO_27001',
  HIPAA = 'HIPAA',
  SOC2 = 'SOC2',
}

export enum ComplianceStatus {
  COMPLIANT = 'COMPLIANT',
  PARTIALLY_COMPLIANT = 'PARTIALLY_COMPLIANT',
  NON_COMPLIANT = 'NON_COMPLIANT',
  NOT_APPLICABLE = 'NOT_APPLICABLE',
}

export interface ComplianceCheck {
  id: string;
  name: string;
  description: string;
  framework: ComplianceFramework;
  checkFunction: () => Promise<ComplianceCheckResult>;
  required: boolean;
}

export interface ComplianceCheckResult {
  checkId: string;
  status: ComplianceStatus;
  details?: string;
  timestamp: Date;
}

export interface ComplianceReport {
  id: string;
  generatedAt: Date;
  framework: ComplianceFramework;
  results: ComplianceCheckResult[];
  overallStatus: ComplianceStatus;
  summary: {
    compliant: number;
    partiallyCompliant: number;
    nonCompliant: number;
    total: number;
  };
}

export interface ComplianceDocumentation {
  id: string;
  title: string;
  description: string;
  framework: ComplianceFramework;
  createdAt: Date;
  content: string;
  attachments?: string[];
}

@Injectable()
export class ComplianceService {
  private readonly logger = new Logger(ComplianceService.name);
  private complianceChecks: Map<ComplianceFramework, ComplianceCheck[]> = new Map();
  private reportsDir: string;
  private docsDir: string;
  private emailTransporter: any;

  constructor(
    private readonly prisma: PrismaService,
    private readonly securityEventService: SecurityEventService,
    private readonly configService: ConfigService,
  ) {
    // Initialize directories for reports and documentation
    this.reportsDir = path.join(process.cwd(), 'compliance-reports');
    this.docsDir = path.join(process.cwd(), 'compliance-docs');
    
    // Create directories if they don't exist
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
    }
    
    if (!fs.existsSync(this.docsDir)) {
      fs.mkdirSync(this.docsDir, { recursive: true });
    }
    
    // Initialize email transporter for notifications
    this.initializeEmailTransporter();
    
    // Initialize compliance checks
    this.initializeComplianceChecks();
  }

  /**
   * Initialize email transporter for sending compliance reports
   */
  private initializeEmailTransporter() {
    try {
      this.emailTransporter = nodemailer.createTransport({
        host: this.configService.get<string>('EMAIL_HOST', 'smtp.example.com'),
        port: this.configService.get<number>('EMAIL_PORT', 587),
        secure: this.configService.get<boolean>('EMAIL_SECURE', false),
        auth: {
          user: this.configService.get<string>('EMAIL_USER', ''),
          pass: this.configService.get<string>('EMAIL_PASS', ''),
        },
      });
      this.logger.log('Email transporter initialized successfully');
    } catch (error) {
      this.logger.error(`Failed to initialize email transporter: ${error.message}`);
    }
  }

  /**
   * Initialize compliance checks for different frameworks
   */
  private initializeComplianceChecks() {
    // GDPR Compliance Checks
    const gdprChecks: ComplianceCheck[] = [
      {
        id: 'gdpr-data-protection',
        name: 'Protection des données personnelles',
        description: 'Vérifier que les données personnelles sont protégées par des mesures techniques appropriées',
        framework: ComplianceFramework.GDPR,
        checkFunction: this.checkDataProtection.bind(this),
        required: true,
      },
      {
        id: 'gdpr-consent',
        name: 'Consentement explicite',
        description: 'Vérifier que le consentement explicite est recueilli pour le traitement des données personnelles',
        framework: ComplianceFramework.GDPR,
        checkFunction: this.checkExplicitConsent.bind(this),
        required: true,
      },
      {
        id: 'gdpr-right-to-be-forgotten',
        name: 'Droit à l\'oubli',
        description: 'Vérifier que les mécanismes de suppression des données sont en place',
        framework: ComplianceFramework.GDPR,
        checkFunction: this.checkRightToBeForgotten.bind(this),
        required: true,
      },
      {
        id: 'gdpr-data-access-logging',
        name: 'Journalisation des accès aux données',
        description: 'Vérifier que tous les accès aux données personnelles sont journalisés',
        framework: ComplianceFramework.GDPR,
        checkFunction: this.checkDataAccessLogging.bind(this),
        required: true,
      },
    ];
    
    // PCI DSS Compliance Checks
    const pciDssChecks: ComplianceCheck[] = [
      {
        id: 'pci-encryption',
        name: 'Chiffrement des données de carte',
        description: 'Vérifier que les données de carte de paiement sont chiffrées',
        framework: ComplianceFramework.PCI_DSS,
        checkFunction: this.checkCardDataEncryption.bind(this),
        required: true,
      },
      {
        id: 'pci-access-control',
        name: 'Contrôle d\'accès',
        description: 'Vérifier que l\'accès aux données de carte est restreint',
        framework: ComplianceFramework.PCI_DSS,
        checkFunction: this.checkCardDataAccessControl.bind(this),
        required: true,
      },
      {
        id: 'pci-network-security',
        name: 'Sécurité réseau',
        description: 'Vérifier que le réseau est sécurisé',
        framework: ComplianceFramework.PCI_DSS,
        checkFunction: this.checkNetworkSecurity.bind(this),
        required: true,
      },
    ];
    
    // ISO 27001 Compliance Checks
    const iso27001Checks: ComplianceCheck[] = [
      {
        id: 'iso-security-policy',
        name: 'Politique de sécurité',
        description: 'Vérifier que la politique de sécurité est documentée et à jour',
        framework: ComplianceFramework.ISO_27001,
        checkFunction: this.checkSecurityPolicy.bind(this),
        required: true,
      },
      {
        id: 'iso-asset-management',
        name: 'Gestion des actifs',
        description: 'Vérifier que les actifs sont inventoriés',
        framework: ComplianceFramework.ISO_27001,
        checkFunction: this.checkAssetManagement.bind(this),
        required: true,
      },
      {
        id: 'iso-incident-management',
        name: 'Gestion des incidents',
        description: 'Vérifier que les incidents de sécurité sont gérés',
        framework: ComplianceFramework.ISO_27001,
        checkFunction: this.checkIncidentManagement.bind(this),
        required: true,
      },
    ];
    
    // Store all checks in the map
    this.complianceChecks.set(ComplianceFramework.GDPR, gdprChecks);
    this.complianceChecks.set(ComplianceFramework.PCI_DSS, pciDssChecks);
    this.complianceChecks.set(ComplianceFramework.ISO_27001, iso27001Checks);
    
    this.logger.log('Compliance checks initialized successfully');
  }

  /**
   * Run compliance checks for a specific framework
   * @param framework The compliance framework to check
   * @returns A compliance report
   */
  async runComplianceChecks(framework: ComplianceFramework): Promise<ComplianceReport> {
    this.logger.log(`Running compliance checks for ${framework}`);
    
    const checks = this.complianceChecks.get(framework) || [];
    const results: ComplianceCheckResult[] = [];
    
    for (const check of checks) {
      try {
        const result = await check.checkFunction();
        results.push(result);
      } catch (error) {
        this.logger.error(`Error running compliance check ${check.id}: ${error.message}`);
        results.push({
          checkId: check.id,
          status: ComplianceStatus.NON_COMPLIANT,
          details: `Error: ${error.message}`,
          timestamp: new Date(),
        });
      }
    }
    
    // Calculate overall status
    let overallStatus = ComplianceStatus.COMPLIANT;
    const requiredChecks = checks.filter(check => check.required);
    
    if (requiredChecks.length > 0) {
      const requiredResults = results.filter(result => 
        requiredChecks.some(check => check.id === result.checkId)
      );
      
      if (requiredResults.some(result => result.status === ComplianceStatus.NON_COMPLIANT)) {
        overallStatus = ComplianceStatus.NON_COMPLIANT;
      } else if (requiredResults.some(result => result.status === ComplianceStatus.PARTIALLY_COMPLIANT)) {
        overallStatus = ComplianceStatus.PARTIALLY_COMPLIANT;
      }
    }
    
    // Create summary
    const summary = {
      compliant: results.filter(result => result.status === ComplianceStatus.COMPLIANT).length,
      partiallyCompliant: results.filter(result => result.status === ComplianceStatus.PARTIALLY_COMPLIANT).length,
      nonCompliant: results.filter(result => result.status === ComplianceStatus.NON_COMPLIANT).length,
      total: results.length,
    };
    
    // Create report
    const report: ComplianceReport = {
      id: `${framework.toLowerCase()}-${Date.now()}`,
      generatedAt: new Date(),
      framework,
      results,
      overallStatus,
      summary,
    };
    
    // Save report to database
    await this.saveComplianceReport(report);
    
    // Log event
    await this.securityEventService.logSecurityEvent({
      eventType: 'COMPLIANCE_CHECK',
      severity: overallStatus === ComplianceStatus.COMPLIANT ? 'INFO' : 'WARNING',
      source: 'ComplianceService',
      details: {
        framework,
        overallStatus,
        summary,
      },
    });
    
    return report;
  }

  /**
   * Run compliance checks for all frameworks
   * @returns A map of compliance reports by framework
   */
  async runAllComplianceChecks(): Promise<Map<ComplianceFramework, ComplianceReport>> {
    this.logger.log('Running compliance checks for all frameworks');
    
    const reports = new Map<ComplianceFramework, ComplianceReport>();
    
    for (const framework of Object.values(ComplianceFramework)) {
      const report = await this.runComplianceChecks(framework);
      reports.set(framework, report);
    }
    
    return reports;
  }

  /**
   * Save a compliance report to the database and file system
   * @param report The compliance report to save
   */
  private async saveComplianceReport(report: ComplianceReport): Promise<void> {
    try {
      // Save to database (mock implementation - would use Prisma in real app)
      this.logger.log(`Saving compliance report ${report.id} to database`);
      
      // Save to file system
      const reportPath = path.join(this.reportsDir, `${report.id}.json`);
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      this.logger.log(`Saved compliance report to ${reportPath}`);
    } catch (error) {
      this.logger.error(`Failed to save compliance report: ${error.message}`);
    }
  }

  /**
   * Generate a compliance report in various formats
   * @param reportId The ID of the report to generate
   * @param format The format of the report (json, html, pdf)
   * @returns The path to the generated report
   */
  async generateComplianceReport(reportId: string, format: 'json' | 'html' | 'pdf' = 'json'): Promise<string> {
    try {
      const reportPath = path.join(this.reportsDir, `${reportId}.json`);
      
      if (!fs.existsSync(reportPath)) {
        throw new Error(`Report ${reportId} not found`);
      }
      
      const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
      
      if (format === 'json') {
        return reportPath;
      }
      
      if (format === 'html') {
        const htmlPath = path.join(this.reportsDir, `${reportId}.html`);
        const htmlContent = this.generateHtmlReport(report);
        fs.writeFileSync(htmlPath, htmlContent);
        return htmlPath;
      }
      
      if (format === 'pdf') {
        // In a real implementation, we would use a library like puppeteer to generate PDF
        const pdfPath = path.join(this.reportsDir, `${reportId}.pdf`);
        this.logger.log(`PDF generation would happen here in a real implementation`);
        // Mock PDF generation
        fs.writeFileSync(pdfPath, 'PDF content would be here');
        return pdfPath;
      }
      
      throw new Error(`Unsupported format: ${format}`);
    } catch (error) {
      this.logger.error(`Failed to generate compliance report: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate an HTML report from a compliance report
   * @param report The compliance report
   * @returns The HTML content
   */
  private generateHtmlReport(report: ComplianceReport): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Compliance Report - ${report.framework}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
            h1 { color: #333; }
            .summary { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
            .status-compliant { color: green; }
            .status-partially-compliant { color: orange; }
            .status-non-compliant { color: red; }
            table { width: 100%; border-collapse: collapse; }
            th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
            th { background-color: #f2f2f2; }
          </style>
        </head>
        <body>
          <h1>Compliance Report - ${report.framework}</h1>
          <p>Generated at: ${new Date(report.generatedAt).toLocaleString()}</p>
          
          <div class="summary">
            <h2>Summary</h2>
            <p>Overall Status: <span class="status-${report.overallStatus.toLowerCase()}">${report.overallStatus}</span></p>
            <p>Compliant: ${report.summary.compliant}/${report.summary.total}</p>
            <p>Partially Compliant: ${report.summary.partiallyCompliant}/${report.summary.total}</p>
            <p>Non-Compliant: ${report.summary.nonCompliant}/${report.summary.total}</p>
          </div>
          
          <h2>Results</h2>
          <table>
            <tr>
              <th>Check ID</th>
              <th>Status</th>
              <th>Details</th>
              <th>Timestamp</th>
            </tr>
            ${report.results.map(result => `
              <tr>
                <td>${result.checkId}</td>
                <td class="status-${result.status.toLowerCase()}">${result.status}</td>
                <td>${result.details || 'N/A'}</td>
                <td>${new Date(result.timestamp).toLocaleString()}</td>
              </tr>
            `).join('')}
          </table>
        </body>
      </html>
    `;
  }

  /**
   * Prepare documentation for external audits
   * @param framework The compliance framework
   * @param title The title of the documentation
   * @param description The description of the documentation
   * @returns The created documentation
   */
  async prepareAuditDocumentation(
    framework: ComplianceFramework,
    title: string,
    description: string,
  ): Promise<ComplianceDocumentation> {
    this.logger.log(`Preparing audit documentation for ${framework}: ${title}`);
    
    try {
      // Run compliance checks to get latest data
      const report = await this.runComplianceChecks(framework);
      
      // Generate documentation content
      const content = this.generateDocumentationContent(framework, report);
      
      // Create documentation object
      const documentation: ComplianceDocumentation = {
        id: `doc-${Date.now()}`,
        title,
        description,
        framework,
        createdAt: new Date(),
        content,
        attachments: [],
      };
      
      // Save documentation
      const docPath = path.join(this.docsDir, `${documentation.id}.md`);
      fs.writeFileSync(docPath, content);
      
      // Add report as attachment
      const reportPath = await this.generateComplianceReport(report.id, 'pdf');
      documentation.attachments = [reportPath];
      
      // Save documentation metadata
      const metadataPath = path.join(this.docsDir, `${documentation.id}.json`);
      fs.writeFileSync(metadataPath, JSON.stringify(documentation, null, 2));
      
      return documentation;
    } catch (error) {
      this.logger.error(`Failed to prepare audit documentation: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate documentation content for a compliance framework
   * @param framework The compliance framework
   * @param report The compliance report
   * @returns The documentation content
   */
  private generateDocumentationContent(framework: ComplianceFramework, report: ComplianceReport): string {
    const checks = this.complianceChecks.get(framework) || [];
    
    return `# Documentation d'audit pour ${framework}

## Vue d'ensemble

Cette documentation a été préparée pour faciliter les audits externes de conformité ${framework}. Elle contient des informations sur les contrôles de conformité, les résultats des vérifications automatisées, et les preuves de conformité.

## Statut de conformité

Statut global: **${report.overallStatus}**

- Contrôles conformes: ${report.summary.compliant}/${report.summary.total}
- Contrôles partiellement conformes: ${report.summary.partiallyCompliant}/${report.summary.total}
- Contrôles non conformes: ${report.summary.nonCompliant}/${report.summary.total}

## Contrôles de conformité

${checks.map(check => `
### ${check.name}

**ID:** ${check.id}
**Description:** ${check.description}
**Obligatoire:** ${check.required ? 'Oui' : 'Non'}

**Statut actuel:** ${report.results.find(r => r.checkId === check.id)?.status || 'Non vérifié'}

${report.results.find(r => r.checkId === check.id)?.details ? `**Détails:** ${report.results.find(r => r.checkId === check.id)?.details}` : ''}
`).join('\n')}

## Preuves de conformité

Les preuves suivantes sont disponibles pour démontrer la conformité:

1. Rapport de conformité complet (voir pièce jointe)
2. Journaux d'audit de sécurité
3. Documentation des politiques et procédures

## Contact

Pour toute question concernant cette documentation ou les contrôles de conformité, veuillez contacter l'équipe de sécurité à <EMAIL>.
`;
  }

  /**
   * Schedule periodic compliance checks
   * Runs weekly by default
   */
  @Cron(CronExpression.EVERY_WEEK)
  async scheduledComplianceCheck() {
    this.logger.log('Running scheduled compliance checks');
    
    try {
      const reports = await this.runAllComplianceChecks();
      
      // Send email notification with results
      await this.sendComplianceReportEmail(reports);
      
      this.logger.log('Scheduled compliance checks completed successfully');
    } catch (error) {
      this.logger.error(`Error running scheduled compliance checks: ${error.message}`);
    }
  }

  /**
   * Send an email with compliance report results
   * @param reports The compliance reports
   */
  private async sendComplianceReportEmail(reports: Map<ComplianceFramework, ComplianceReport>): Promise<void> {
    try {
      if (!this.emailTransporter) {
        this.logger.warn('Email transporter not initialized, skipping email notification');
        return;
      }
      
      const recipients = this.configService.get<string>('COMPLIANCE_REPORT_RECIPIENTS', '<EMAIL>');
      
      // Create email content
      let emailContent = '<h1>Rapport de conformité hebdomadaire</h1>';
      
      for (const [framework, report] of reports.entries()) {
        emailContent += `
          <h2>Framework: ${framework}</h2>
          <p>Statut global: <strong>${report.overallStatus}</strong></p>
          <ul>
            <li>Contrôles conformes: ${report.summary.compliant}/${report.summary.total}</li>
            <li>Contrôles partiellement conformes: ${report.summary.partiallyCompliant}/${report.summary.total}</li>
            <li>Contrôles non conformes: ${report.summary.nonCompliant}/${report.summary.total}</li>
          </ul>
        `;
        
        if (report.summary.nonCompliant > 0) {
          emailContent += '<h3>Contrôles non conformes:</h3><ul>';
          
          for (const result of report.results) {
            if (result.status === ComplianceStatus.NON_COMPLIANT) {
              emailContent += `<li>${result.checkId}: ${result.details || 'Aucun détail'}</li>`;
            }
          }
          
          emailContent += '</ul>';
        }
      }
      
      // Send email
      await this.emailTransporter.sendMail({
        from: this.configService.get<string>('EMAIL_FROM', '<EMAIL>'),
        to: recipients,
        subject: 'Rapport de conformité hebdomadaire',
        html: emailContent,
      });
      
      this.logger.log(`Compliance report email sent to ${recipients}`);
    } catch (error) {
      this.logger.error(`Failed to send compliance report email: ${error.message}`);
    }
  }

  // Compliance check implementations
  
  // GDPR Checks
  private async checkDataProtection(): Promise<ComplianceCheckResult> {
    // Implementation would check encryption settings, access controls, etc.
    return {
      checkId: 'gdpr-data-protection',
      status: ComplianceStatus.COMPLIANT,
      details: 'Les données personnelles sont chiffrées au repos et en transit',
      timestamp: new Date(),
    };
  }
  
  private async checkExplicitConsent(): Promise<ComplianceCheckResult> {
    // Implementation would check consent mechanisms
    return {
      checkId: 'gdpr-consent',
      status: ComplianceStatus.COMPLIANT,
      details: 'Le consentement explicite est recueilli pour toutes les données personnelles',
      timestamp: new Date(),
    };
  }
  
  private async checkRightToBeForgotten(): Promise<ComplianceCheckResult> {
    // Implementation would check data deletion mechanisms
    return {
      checkId: 'gdpr-right-to-be-forgotten',
      status: ComplianceStatus.COMPLIANT,
      details: 'Les mécanismes de suppression des données sont en place',
      timestamp: new Date(),
    };
  }
  
  private async checkDataAccessLogging(): Promise<ComplianceCheckResult> {
    // Implementation would check data access logging
    return {
      checkId: 'gdpr-data-access-logging',
      status: ComplianceStatus.COMPLIANT,
      details: 'Tous les accès aux données personnelles sont journalisés',
      timestamp: new Date(),
    };
  }
  
  // PCI DSS Checks
  private async checkCardDataEncryption(): Promise<ComplianceCheckResult> {
    // Implementation would check card data encryption
    return {
      checkId: 'pci-encryption',
      status: ComplianceStatus.COMPLIANT,
      details: 'Les données de carte de paiement sont chiffrées',
      timestamp: new Date(),
    };
  }
  
  private async checkCardDataAccessControl(): Promise<ComplianceCheckResult> {
    // Implementation would check card data access control
    return {
      checkId: 'pci-access-control',
      status: ComplianceStatus.COMPLIANT,
      details: 'L\'accès aux données de carte est restreint',
      timestamp: new Date(),
    };
  }
  
  private async checkNetworkSecurity(): Promise<ComplianceCheckResult> {
    // Implementation would check network security
    return {
      checkId: 'pci-network-security',
      status: ComplianceStatus.COMPLIANT,
      details: 'Le réseau est sécurisé',
      timestamp: new Date(),
    };
  }
  
  // ISO 27001 Checks
  private async checkSecurityPolicy(): Promise<ComplianceCheckResult> {
    // Implementation would check security policy
    return {
      checkId: 'iso-security-policy',
      status: ComplianceStatus.COMPLIANT,
      details: 'La politique de sécurité est documentée et à jour',
      timestamp: new Date(),
    };
  }
  
  private async checkAssetManagement(): Promise<ComplianceCheckResult> {
    // Implementation would check asset management
    return {
      checkId: 'iso-asset-management',
      status: ComplianceStatus.COMPLIANT,
      details: 'Les actifs sont inventoriés',
      timestamp: new Date(),
    };
  }
  
  private async checkIncidentManagement(): Promise<ComplianceCheckResult> {
    // Implementation would check incident management
    return {
      checkId: 'iso-incident-management',
      status: ComplianceStatus.COMPLIANT,
      details: 'Les incidents de sécurité sont gérés',
      timestamp: new Date(),
    };
  }
}
