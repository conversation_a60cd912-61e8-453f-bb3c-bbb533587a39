import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';
import { KeyManagementService } from './key-management.service';

/**
 * Interface pour les événements cryptographiques
 */
export interface CryptoEvent {
  timestamp: Date;
  operation: string;
  service: string;
  success: boolean;
  keyId?: string;
  algorithm?: string;
  executionTimeMs?: number;
  errorMessage?: string;
  userId?: string;
  ipAddress?: string;
  metadata?: Record<string, any>;
}

/**
 * Niveaux de sensibilité des journaux
 */
export enum LogSensitivityLevel {
  PUBLIC = 'public',     // Informations non sensibles
  INTERNAL = 'internal', // Informations internes (algorithmes, temps d'exécution)
  SENSITIVE = 'sensitive', // Informations sensibles (identifiants de clés)
  CRITICAL = 'critical', // Informations critiques (erreurs détaillées)
}

/**
 * Service de journalisation sécurisée pour les opérations cryptographiques
 * Ce service fournit des méthodes pour journaliser les opérations cryptographiques
 * de manière sécurisée, avec différents niveaux de sensibilité et de détail
 */
@Injectable()
export class CryptoLoggingService {
  private readonly logger = new Logger(CryptoLoggingService.name);
  private readonly enabled: boolean;
  private readonly logLevel: LogSensitivityLevel;
  private readonly logToFile: boolean;
  private readonly logFilePath: string;
  private readonly logRotationSizeBytes: number;
  private readonly signLogs: boolean;
  private readonly encryptSensitiveLogs: boolean;
  private readonly retentionDays: number;
  private readonly anonymizeUserIds: boolean;

  constructor(
    private readonly configService: ConfigService,
    private readonly keyManagementService: KeyManagementService,
  ) {
    this.enabled = this.configService.get<boolean>('encryption.logging.enabled', true);
    this.logLevel = this.configService.get<LogSensitivityLevel>(
      'encryption.logging.level',
      LogSensitivityLevel.INTERNAL
    );
    this.logToFile = this.configService.get<boolean>('encryption.logging.logToFile', true);
    this.logFilePath = this.configService.get<string>(
      'encryption.logging.filePath',
      './logs/crypto-operations.log'
    );
    this.logRotationSizeBytes = this.configService.get<number>(
      'encryption.logging.rotationSizeBytes',
      10 * 1024 * 1024 // 10 MB
    );
    this.signLogs = this.configService.get<boolean>('encryption.logging.signLogs', true);
    this.encryptSensitiveLogs = this.configService.get<boolean>(
      'encryption.logging.encryptSensitiveLogs',
      true
    );
    this.retentionDays = this.configService.get<number>('encryption.logging.retentionDays', 90);
    this.anonymizeUserIds = this.configService.get<boolean>(
      'encryption.logging.anonymizeUserIds',
      true
    );

    // Créer le répertoire de logs s'il n'existe pas
    if (this.logToFile) {
      this.ensureLogDirectory();
    }
  }

  /**
   * Journalise un événement cryptographique
   * @param event Événement à journaliser
   */
  async logCryptoEvent(event: CryptoEvent): Promise<void> {
    if (!this.enabled) {
      return;
    }

    try {
      // Anonymiser les identifiants utilisateur si configuré
      if (this.anonymizeUserIds && event.userId) {
        event.userId = this.anonymizeUserId(event.userId);
      }

      // Filtrer les informations sensibles selon le niveau de journalisation
      const filteredEvent = this.filterSensitiveInfo(event);

      // Journaliser l'événement dans les logs NestJS
      this.logToNestLogger(filteredEvent);

      // Journaliser l'événement dans un fichier si configuré
      if (this.logToFile) {
        await this.logToFileSystem(event);
      }
    } catch (error) {
      // Utiliser le logger NestJS standard pour éviter une boucle infinie
      this.logger.error('Failed to log crypto event', error);
    }
  }

  /**
   * Journalise une opération de chiffrement
   * @param service Service qui effectue l'opération
   * @param algorithm Algorithme utilisé
   * @param keyId Identifiant de la clé utilisée
   * @param executionTimeMs Temps d'exécution en millisecondes
   * @param userId Identifiant de l'utilisateur (optionnel)
   * @param ipAddress Adresse IP (optionnel)
   * @param metadata Métadonnées supplémentaires (optionnel)
   */
  async logEncryption(
    service: string,
    algorithm: string,
    keyId: string,
    executionTimeMs: number,
    userId?: string,
    ipAddress?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logCryptoEvent({
      timestamp: new Date(),
      operation: 'encrypt',
      service,
      success: true,
      keyId,
      algorithm,
      executionTimeMs,
      userId,
      ipAddress,
      metadata,
    });
  }

  /**
   * Journalise une opération de déchiffrement
   * @param service Service qui effectue l'opération
   * @param algorithm Algorithme utilisé
   * @param keyId Identifiant de la clé utilisée
   * @param executionTimeMs Temps d'exécution en millisecondes
   * @param userId Identifiant de l'utilisateur (optionnel)
   * @param ipAddress Adresse IP (optionnel)
   * @param metadata Métadonnées supplémentaires (optionnel)
   */
  async logDecryption(
    service: string,
    algorithm: string,
    keyId: string,
    executionTimeMs: number,
    userId?: string,
    ipAddress?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logCryptoEvent({
      timestamp: new Date(),
      operation: 'decrypt',
      service,
      success: true,
      keyId,
      algorithm,
      executionTimeMs,
      userId,
      ipAddress,
      metadata,
    });
  }

  /**
   * Journalise une opération de rotation de clé
   * @param service Service qui effectue l'opération
   * @param oldKeyId Identifiant de l'ancienne clé
   * @param newKeyId Identifiant de la nouvelle clé
   * @param executionTimeMs Temps d'exécution en millisecondes
   * @param userId Identifiant de l'utilisateur (optionnel)
   * @param ipAddress Adresse IP (optionnel)
   */
  async logKeyRotation(
    service: string,
    oldKeyId: string,
    newKeyId: string,
    executionTimeMs: number,
    userId?: string,
    ipAddress?: string
  ): Promise<void> {
    await this.logCryptoEvent({
      timestamp: new Date(),
      operation: 'key_rotation',
      service,
      success: true,
      keyId: oldKeyId,
      executionTimeMs,
      userId,
      ipAddress,
      metadata: { newKeyId },
    });
  }

  /**
   * Journalise une erreur cryptographique
   * @param service Service qui a rencontré l'erreur
   * @param operation Opération qui a échoué
   * @param errorMessage Message d'erreur
   * @param keyId Identifiant de la clé utilisée (optionnel)
   * @param algorithm Algorithme utilisé (optionnel)
   * @param userId Identifiant de l'utilisateur (optionnel)
   * @param ipAddress Adresse IP (optionnel)
   */
  async logError(
    service: string,
    operation: string,
    errorMessage: string,
    keyId?: string,
    algorithm?: string,
    userId?: string,
    ipAddress?: string
  ): Promise<void> {
    await this.logCryptoEvent({
      timestamp: new Date(),
      operation,
      service,
      success: false,
      keyId,
      algorithm,
      errorMessage,
      userId,
      ipAddress,
    });
  }

  /**
   * Filtre les informations sensibles selon le niveau de journalisation
   * @param event Événement à filtrer
   * @returns Événement filtré
   */
  private filterSensitiveInfo(event: CryptoEvent): Partial<CryptoEvent> {
    const filteredEvent: Partial<CryptoEvent> = {
      timestamp: event.timestamp,
      operation: event.operation,
      service: event.service,
      success: event.success,
    };

    // Ajouter les informations selon le niveau de journalisation
    if (this.logLevel !== LogSensitivityLevel.PUBLIC) {
      filteredEvent.algorithm = event.algorithm;
      filteredEvent.executionTimeMs = event.executionTimeMs;
    }

    if (this.logLevel === LogSensitivityLevel.SENSITIVE || this.logLevel === LogSensitivityLevel.CRITICAL) {
      filteredEvent.keyId = event.keyId;
      filteredEvent.userId = event.userId;
      filteredEvent.ipAddress = event.ipAddress;
    }

    if (this.logLevel === LogSensitivityLevel.CRITICAL) {
      filteredEvent.errorMessage = event.errorMessage;
      filteredEvent.metadata = event.metadata;
    }

    return filteredEvent;
  }

  /**
   * Journalise un événement dans les logs NestJS
   * @param event Événement à journaliser
   */
  private logToNestLogger(event: Partial<CryptoEvent>): void {
    const logMessage = `[${event.service}] ${event.operation} - ${event.success ? 'SUCCESS' : 'FAILURE'}`;
    
    if (event.success) {
      this.logger.log(logMessage);
    } else {
      this.logger.error(`${logMessage}: ${event.errorMessage}`);
    }
  }

  /**
   * Journalise un événement dans un fichier
   * @param event Événement à journaliser
   */
  private async logToFileSystem(event: CryptoEvent): Promise<void> {
    try {
      // Vérifier si la rotation des logs est nécessaire
      await this.checkLogRotation();

      // Préparer l'entrée de log
      let logEntry: string;
      
      if (this.encryptSensitiveLogs && (
        this.logLevel === LogSensitivityLevel.SENSITIVE || 
        this.logLevel === LogSensitivityLevel.CRITICAL
      )) {
        // Chiffrer les logs sensibles
        logEntry = await this.encryptLogEntry(event);
      } else {
        // Formater l'entrée de log en JSON
        logEntry = JSON.stringify(this.filterSensitiveInfo(event));
      }

      // Signer l'entrée de log si configuré
      if (this.signLogs) {
        logEntry = await this.signLogEntry(logEntry);
      }

      // Écrire l'entrée de log dans le fichier
      fs.appendFileSync(this.logFilePath, logEntry + '\n');
    } catch (error) {
      this.logger.error('Failed to log to file system', error);
    }
  }

  /**
   * Vérifie si la rotation des logs est nécessaire
   */
  private async checkLogRotation(): Promise<void> {
    try {
      if (!fs.existsSync(this.logFilePath)) {
        return;
      }

      const stats = fs.statSync(this.logFilePath);
      if (stats.size >= this.logRotationSizeBytes) {
        const timestamp = new Date().toISOString().replace(/:/g, '-');
        const rotatedLogPath = `${this.logFilePath}.${timestamp}`;
        
        fs.renameSync(this.logFilePath, rotatedLogPath);
        this.logger.log(`Rotated log file to: ${rotatedLogPath}`);
        
        // Supprimer les anciens logs selon la politique de rétention
        await this.cleanupOldLogs();
      }
    } catch (error) {
      this.logger.error('Failed to check log rotation', error);
    }
  }

  /**
   * Supprime les anciens logs selon la politique de rétention
   */
  private async cleanupOldLogs(): Promise<void> {
    try {
      const logDir = path.dirname(this.logFilePath);
      const logBaseName = path.basename(this.logFilePath);
      
      const files = fs.readdirSync(logDir);
      const now = new Date();
      
      for (const file of files) {
        if (file.startsWith(logBaseName) && file !== logBaseName) {
          const filePath = path.join(logDir, file);
          const stats = fs.statSync(filePath);
          
          const fileAge = (now.getTime() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);
          if (fileAge > this.retentionDays) {
            fs.unlinkSync(filePath);
            this.logger.log(`Deleted old log file: ${filePath}`);
          }
        }
      }
    } catch (error) {
      this.logger.error('Failed to cleanup old logs', error);
    }
  }

  /**
   * Chiffre une entrée de log
   * @param event Événement à chiffrer
   * @returns Entrée de log chiffrée
   */
  private async encryptLogEntry(event: CryptoEvent): Promise<string> {
    try {
      // Récupérer une clé active pour le chiffrement
      const { key, metadata } = await this.keyManagementService.getActiveKey('encryption', 'logging');
      
      // Générer un vecteur d'initialisation aléatoire
      const iv = crypto.randomBytes(12);
      
      // Chiffrer l'entrée de log
      const cipher = crypto.createCipheriv('aes-256-gcm', key, iv);
      const jsonData = JSON.stringify(event);
      
      let encryptedData = cipher.update(jsonData, 'utf8', 'base64');
      encryptedData += cipher.final('base64');
      
      // Récupérer le tag d'authentification
      const authTag = cipher.getAuthTag();
      
      // Formater l'entrée de log chiffrée
      return JSON.stringify({
        encrypted: true,
        keyId: metadata.id,
        iv: iv.toString('base64'),
        authTag: authTag.toString('base64'),
        data: encryptedData,
        timestamp: event.timestamp,
      });
    } catch (error) {
      this.logger.error('Failed to encrypt log entry', error);
      // En cas d'erreur, retourner l'entrée non chiffrée
      return JSON.stringify(this.filterSensitiveInfo(event));
    }
  }

  /**
   * Signe une entrée de log
   * @param logEntry Entrée de log à signer
   * @returns Entrée de log signée
   */
  private async signLogEntry(logEntry: string): Promise<string> {
    try {
      // Récupérer une clé active pour la signature
      const { key, metadata } = await this.keyManagementService.getActiveKey('signing', 'logging');
      
      // Calculer la signature HMAC
      const hmac = crypto.createHmac('sha256', key);
      hmac.update(logEntry);
      const signature = hmac.digest('base64');
      
      // Ajouter la signature à l'entrée de log
      const signedEntry = JSON.parse(logEntry);
      signedEntry.signature = signature;
      signedEntry.signatureKeyId = metadata.id;
      
      return JSON.stringify(signedEntry);
    } catch (error) {
      this.logger.error('Failed to sign log entry', error);
      // En cas d'erreur, retourner l'entrée non signée
      return logEntry;
    }
  }

  /**
   * Anonymise un identifiant utilisateur
   * @param userId Identifiant utilisateur à anonymiser
   * @returns Identifiant utilisateur anonymisé
   */
  private anonymizeUserId(userId: string): string {
    // Utiliser un hachage pour anonymiser l'identifiant
    const hash = crypto.createHash('sha256');
    hash.update(userId);
    return hash.digest('hex').substring(0, 16);
  }

  /**
   * S'assure que le répertoire de logs existe
   */
  private ensureLogDirectory(): void {
    try {
      const logDir = path.dirname(this.logFilePath);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
        this.logger.log(`Created log directory: ${logDir}`);
      }
    } catch (error) {
      this.logger.error('Failed to create log directory', error);
    }
  }

  /**
   * Vérifie l'intégrité des logs
   * @returns Résultat de la vérification
   */
  async verifyLogIntegrity(): Promise<{
    verified: boolean;
    totalEntries: number;
    validEntries: number;
    invalidEntries: number;
    errors: string[];
  }> {
    if (!this.signLogs || !this.logToFile || !fs.existsSync(this.logFilePath)) {
      return {
        verified: false,
        totalEntries: 0,
        validEntries: 0,
        invalidEntries: 0,
        errors: ['Log verification is not available'],
      };
    }

    try {
      const logContent = fs.readFileSync(this.logFilePath, 'utf8');
      const logLines = logContent.split('\n').filter(line => line.trim().length > 0);
      
      let totalEntries = 0;
      let validEntries = 0;
      let invalidEntries = 0;
      const errors: string[] = [];
      
      for (const line of logLines) {
        totalEntries++;
        
        try {
          const entry = JSON.parse(line);
          
          if (!entry.signature || !entry.signatureKeyId) {
            invalidEntries++;
            errors.push(`Entry at line ${totalEntries} is not signed`);
            continue;
          }
          
          // Récupérer la clé de signature
          const { key } = await this.keyManagementService.getKey(entry.signatureKeyId);
          
          // Extraire la signature
          const signature = entry.signature;
          delete entry.signature;
          delete entry.signatureKeyId;
          
          // Recalculer la signature
          const hmac = crypto.createHmac('sha256', key);
          hmac.update(JSON.stringify(entry));
          const expectedSignature = hmac.digest('base64');
          
          if (signature === expectedSignature) {
            validEntries++;
          } else {
            invalidEntries++;
            errors.push(`Invalid signature at line ${totalEntries}`);
          }
        } catch (error) {
          invalidEntries++;
          errors.push(`Error processing line ${totalEntries}: ${error.message}`);
        }
      }
      
      return {
        verified: invalidEntries === 0,
        totalEntries,
        validEntries,
        invalidEntries,
        errors,
      };
    } catch (error) {
      this.logger.error('Failed to verify log integrity', error);
      return {
        verified: false,
        totalEntries: 0,
        validEntries: 0,
        invalidEntries: 0,
        errors: [`Failed to verify log integrity: ${error.message}`],
      };
    }
  }
}
