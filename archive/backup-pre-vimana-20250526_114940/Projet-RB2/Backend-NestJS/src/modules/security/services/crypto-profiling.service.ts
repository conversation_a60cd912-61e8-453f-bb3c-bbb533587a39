import { Injectable, Logger } from '@nestjs/common';
import { performance } from 'perf_hooks';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CryptoPerformanceService } from './crypto-performance.service';

/**
 * Interface pour les métriques de performance
 */
export interface PerformanceMetrics {
  operationId: string;
  operationType: string;
  serviceName: string;
  durationMs: number;
  inputSize: number;
  outputSize: number;
  memoryUsageBefore: NodeJS.MemoryUsage;
  memoryUsageAfter: NodeJS.MemoryUsage;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * Service de profilage des opérations cryptographiques
 * Ce service mesure les performances des différentes opérations cryptographiques
 * et identifie les goulots d'étranglement.
 */
@Injectable()
export class CryptoProfilingService {
  private readonly logger = new Logger(CryptoProfilingService.name);
  private readonly metrics: Map<string, PerformanceMetrics[]> = new Map();
  private readonly thresholds: Map<string, number> = new Map();
  private _samplingRate: number = 0.1; // 10% des opérations sont profilées par défaut

  // Getter pour samplingRate
  get samplingRate(): number {
    return this._samplingRate;
  }

  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly performanceService: CryptoPerformanceService,
  ) {
    // Initialiser les seuils de performance par défaut (en ms)
    this.thresholds.set('encrypt', 50);
    this.thresholds.set('decrypt', 50);
    this.thresholds.set('homomorphic.add', 100);
    this.thresholds.set('homomorphic.multiply', 200);
    this.thresholds.set('homomorphic.divide', 300);
    this.thresholds.set('homomorphic.average', 400);
    this.thresholds.set('homomorphic.variance', 500);
    this.thresholds.set('quantum.encrypt', 100);
    this.thresholds.set('quantum.decrypt', 100);
    this.thresholds.set('quantum.hybrid', 150);
    this.thresholds.set('key.generate', 500);
    this.thresholds.set('key.rotate', 1000);
  }

  /**
   * Définit le taux d'échantillonnage pour le profilage
   * @param rate Taux d'échantillonnage (0.0 - 1.0)
   */
  setSamplingRate(rate: number): void {
    if (rate < 0 || rate > 1) {
      throw new Error('Sampling rate must be between 0 and 1');
    }
    this._samplingRate = rate;
    this.logger.log(`Sampling rate set to ${rate * 100}%`);
  }

  /**
   * Définit le seuil de performance pour une opération
   * @param operationType Type d'opération
   * @param thresholdMs Seuil en millisecondes
   */
  setThreshold(operationType: string, thresholdMs: number): void {
    this.thresholds.set(operationType, thresholdMs);
    this.logger.log(`Threshold for ${operationType} set to ${thresholdMs}ms`);
  }

  /**
   * Décore une fonction avec du profilage
   * @param target Objet cible
   * @param propertyKey Nom de la méthode
   * @param descriptor Descripteur de la méthode
   * @param operationType Type d'opération
   * @param serviceName Nom du service
   * @returns Descripteur modifié
   */
  profileMethod(
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
    operationType: string,
    serviceName: string,
  ): PropertyDescriptor {
    const originalMethod = descriptor.value;
    const profilingService = this;

    descriptor.value = async function (...args: any[]) {
      // Décider si on profile cette opération en fonction du taux d'échantillonnage
      if (Math.random() > profilingService.samplingRate) {
        return originalMethod.apply(this, args);
      }

      const operationId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      const startTime = performance.now();
      const memoryBefore = process.memoryUsage();

      let inputSize = 0;
      try {
        // Estimer la taille des entrées
        for (const arg of args) {
          if (arg instanceof Buffer) {
            inputSize += arg.length;
          } else if (typeof arg === 'string') {
            inputSize += arg.length;
          } else if (Array.isArray(arg)) {
            inputSize += JSON.stringify(arg).length;
          } else if (typeof arg === 'object' && arg !== null) {
            inputSize += JSON.stringify(arg).length;
          }
        }
      } catch (error) {
        // Ignorer les erreurs lors de l'estimation de la taille
      }

      try {
        const result = await originalMethod.apply(this, args);

        const endTime = performance.now();
        const duration = endTime - startTime;
        const memoryAfter = process.memoryUsage();

        let outputSize = 0;
        try {
          // Estimer la taille de la sortie
          if (result instanceof Buffer) {
            outputSize = result.length;
          } else if (typeof result === 'string') {
            outputSize = result.length;
          } else if (Array.isArray(result)) {
            outputSize = JSON.stringify(result).length;
          } else if (typeof result === 'object' && result !== null) {
            outputSize = JSON.stringify(result).length;
          }
        } catch (error) {
          // Ignorer les erreurs lors de l'estimation de la taille
        }

        // Créer les métriques de performance
        const metrics: PerformanceMetrics = {
          operationId,
          operationType,
          serviceName,
          durationMs: duration,
          inputSize,
          outputSize,
          memoryUsageBefore: memoryBefore,
          memoryUsageAfter: memoryAfter,
          timestamp: new Date(),
          metadata: {
            methodName: propertyKey,
            argsTypes: args.map(arg => typeof arg),
          },
        };

        // Stocker les métriques
        profilingService.storeMetrics(metrics);

        // Vérifier si la durée dépasse le seuil
        const threshold = profilingService.thresholds.get(operationType) || 100;
        if (duration > threshold) {
          profilingService.logger.warn(
            `Performance threshold exceeded for ${operationType}: ${duration.toFixed(2)}ms (threshold: ${threshold}ms)`,
          );
          profilingService.eventEmitter.emit('crypto.performance.threshold.exceeded', {
            ...metrics,
            threshold,
          });
        }

        // Envoyer les métriques au service de performance
        profilingService.performanceService.recordMetrics(metrics);

        return result;
      } catch (error) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        const memoryAfter = process.memoryUsage();

        // Créer les métriques de performance pour l'erreur
        const metrics: PerformanceMetrics = {
          operationId,
          operationType,
          serviceName,
          durationMs: duration,
          inputSize,
          outputSize: 0,
          memoryUsageBefore: memoryBefore,
          memoryUsageAfter: memoryAfter,
          timestamp: new Date(),
          metadata: {
            methodName: propertyKey,
            argsTypes: args.map(arg => typeof arg),
            error: error.message,
            stack: error.stack,
          },
        };

        // Stocker les métriques
        profilingService.storeMetrics(metrics);

        // Envoyer les métriques au service de performance
        profilingService.performanceService.recordMetrics(metrics);

        // Réémettre l'erreur
        throw error;
      }
    };

    return descriptor;
  }

  /**
   * Stocke les métriques de performance
   * @param metrics Métriques de performance
   */
  private storeMetrics(metrics: PerformanceMetrics): void {
    const { operationType } = metrics;
    if (!this.metrics.has(operationType)) {
      this.metrics.set(operationType, []);
    }
    const operationMetrics = this.metrics.get(operationType);
    operationMetrics.push(metrics);

    // Limiter le nombre de métriques stockées (garder les 1000 dernières)
    if (operationMetrics.length > 1000) {
      operationMetrics.shift();
    }
  }

  /**
   * Retourne les métriques de performance pour un type d'opération
   * @param operationType Type d'opération
   * @returns Métriques de performance
   */
  getMetrics(operationType?: string): PerformanceMetrics[] {
    if (operationType) {
      return this.metrics.get(operationType) || [];
    }

    // Retourner toutes les métriques
    const allMetrics: PerformanceMetrics[] = [];
    for (const metrics of this.metrics.values()) {
      allMetrics.push(...metrics);
    }
    return allMetrics;
  }

  /**
   * Retourne les statistiques de performance pour un type d'opération
   * @param operationType Type d'opération
   * @returns Statistiques de performance
   */
  getStatistics(operationType?: string): Record<string, any> {
    const metrics = this.getMetrics(operationType);
    if (metrics.length === 0) {
      return {
        count: 0,
        avgDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        p95Duration: 0,
        p99Duration: 0,
        avgInputSize: 0,
        avgOutputSize: 0,
        avgMemoryDelta: 0,
      };
    }

    // Calculer les statistiques
    const durations = metrics.map(m => m.durationMs).sort((a, b) => a - b);
    const inputSizes = metrics.map(m => m.inputSize);
    const outputSizes = metrics.map(m => m.outputSize);
    const memoryDeltas = metrics.map(
      m => m.memoryUsageAfter.heapUsed - m.memoryUsageBefore.heapUsed,
    );

    const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
    const minDuration = durations[0];
    const maxDuration = durations[durations.length - 1];
    const p95Index = Math.floor(durations.length * 0.95);
    const p99Index = Math.floor(durations.length * 0.99);
    const p95Duration = durations[p95Index];
    const p99Duration = durations[p99Index];

    const avgInputSize = inputSizes.reduce((a, b) => a + b, 0) / inputSizes.length;
    const avgOutputSize = outputSizes.reduce((a, b) => a + b, 0) / outputSizes.length;
    const avgMemoryDelta = memoryDeltas.reduce((a, b) => a + b, 0) / memoryDeltas.length;

    return {
      count: metrics.length,
      avgDuration,
      minDuration,
      maxDuration,
      p95Duration,
      p99Duration,
      avgInputSize,
      avgOutputSize,
      avgMemoryDelta,
    };
  }

  /**
   * Efface toutes les métriques de performance
   */
  clearMetrics(): void {
    this.metrics.clear();
    this.logger.log('All metrics cleared');
  }

  /**
   * Génère un rapport de performance
   * @returns Rapport de performance
   */
  generateReport(): Record<string, any> {
    const report: Record<string, any> = {
      timestamp: new Date(),
      summary: {},
      operationTypes: {},
    };

    // Statistiques globales
    const allMetrics = this.getMetrics();
    report.summary = {
      totalOperations: allMetrics.length,
      uniqueOperationTypes: this.metrics.size,
      ...this.getStatistics(),
    };

    // Statistiques par type d'opération
    for (const [operationType, metrics] of this.metrics.entries()) {
      report.operationTypes[operationType] = {
        count: metrics.length,
        ...this.getStatistics(operationType),
        threshold: this.thresholds.get(operationType) || 100,
        thresholdExceededCount: metrics.filter(
          m => m.durationMs > (this.thresholds.get(operationType) || 100),
        ).length,
      };
    }

    return report;
  }

  /**
   * Exporte les métriques au format JSON
   * @returns Métriques au format JSON
   */
  exportMetricsJson(): string {
    return JSON.stringify(this.getMetrics(), null, 2);
  }

  /**
   * Exporte les métriques au format CSV
   * @returns Métriques au format CSV
   */
  exportMetricsCsv(): string {
    const metrics = this.getMetrics();
    if (metrics.length === 0) {
      return '';
    }

    // En-têtes CSV
    const headers = [
      'operationId',
      'operationType',
      'serviceName',
      'durationMs',
      'inputSize',
      'outputSize',
      'heapUsedBefore',
      'heapUsedAfter',
      'timestamp',
    ].join(',');

    // Lignes CSV
    const rows = metrics.map(m => {
      return [
        m.operationId,
        m.operationType,
        m.serviceName,
        m.durationMs,
        m.inputSize,
        m.outputSize,
        m.memoryUsageBefore.heapUsed,
        m.memoryUsageAfter.heapUsed,
        m.timestamp.toISOString(),
      ].join(',');
    });

    return [headers, ...rows].join('\n');
  }
}
