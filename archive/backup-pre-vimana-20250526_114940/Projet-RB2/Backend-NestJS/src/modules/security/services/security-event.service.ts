import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventsService } from '../../events/events.service';
import { CreateSecurityEventDto } from '../dto/create-security-event.dto';

@Injectable()
export class SecurityEventService {
  private readonly logger = new Logger(SecurityEventService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventsService: EventsService,
  ) {}

  /**
   * Journalise un événement de sécurité
   * @param eventData Données de l'événement de sécurité
   * @returns L'événement de sécurité créé
   */
  async logSecurityEvent(eventData: any) {
    try {
      // Créer un DTO à partir des données d'événement
      const createSecurityEventDto: CreateSecurityEventDto = {
        eventType: eventData.eventType,
        source: eventData.source,
        severity: eventData.severity,
        details: eventData.details || {},
        timestamp: eventData.timestamp || new Date().toISOString()
      };

      // Appeler la méthode createSecurityEvent avec le DTO
      return this.createSecurityEvent(createSecurityEventDto);
    } catch (error) {
      this.logger.error(`Erreur lors de la journalisation de l'événement de sécurité: ${error.message}`);
      throw error;
    }
  }

  /**
   * Crée un événement de sécurité
   * @param createSecurityEventDto DTO pour la création d'un événement de sécurité
   * @returns L'événement de sécurité créé
   */
  async createSecurityEvent(createSecurityEventDto: CreateSecurityEventDto) {
    try {
      // Créer l'événement de sécurité dans la base de données
      const securityEvent = await this.prisma.securityEvent.create({
        data: {
          type: createSecurityEventDto.eventType,
          severity: createSecurityEventDto.severity,
          source: createSecurityEventDto.source,
          details: createSecurityEventDto.details || {},
          timestamp: createSecurityEventDto.timestamp ? new Date(createSecurityEventDto.timestamp) : new Date(),
        },
      });

      // Créer un événement système pour la journalisation
      await this.eventsService.create({
        eventType: `SECURITY_${createSecurityEventDto.eventType}`,
        payload: {
          securityEventId: securityEvent.id,
          severity: createSecurityEventDto.severity,
          source: createSecurityEventDto.source,
          details: createSecurityEventDto.details,
        },
        status: 'COMPLETED',
      });

      this.logger.log(`Événement de sécurité créé: ${securityEvent.id}`);
      return securityEvent;
    } catch (error) {
      this.logger.error(`Erreur lors de la création de l'événement de sécurité: ${error.message}`);
      throw error;
    }
  }

  async getRecentEvents(limit = 10) {
    return this.prisma.securityEvent.findMany({
      take: limit,
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async getEventsByType(type: string, limit = 10) {
    return this.prisma.securityEvent.findMany({
      where: {
        type,
      },
      take: limit,
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async getEventsBySeverity(severity: string, limit = 10) {
    return this.prisma.securityEvent.findMany({
      where: {
        severity,
      },
      take: limit,
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async getEventsBySource(source: string, limit = 10) {
    return this.prisma.securityEvent.findMany({
      where: {
        source,
      },
      take: limit,
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async getEventsByTimeRange(startDate: Date, endDate: Date) {
    return this.prisma.securityEvent.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async getEventById(id: string) {
    return this.prisma.securityEvent.findUnique({
      where: { id },
    });
  }
}
