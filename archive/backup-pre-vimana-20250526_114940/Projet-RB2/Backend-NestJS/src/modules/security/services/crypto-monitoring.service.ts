import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { KeyManagementService } from './key-management.service';
import { CryptoLoggingService } from './crypto-logging.service';

/**
 * Interface pour les métriques de chiffrement
 */
export interface CryptoMetrics {
  operations: {
    total: number;
    successful: number;
    failed: number;
    byType: Record<string, number>;
    byService: Record<string, number>;
  };
  performance: {
    averageExecutionTime: number;
    p95ExecutionTime: number;
    p99ExecutionTime: number;
  };
  keys: {
    total: number;
    active: number;
    rotating: number;
    expired: number;
    revoked: number;
    byAlgorithm: Record<string, number>;
  };
  anomalies: {
    count: number;
    types: Record<string, number>;
  };
  lastUpdated: Date;
}

/**
 * Interface pour un événement cryptographique
 */
export interface CryptoEvent {
  timestamp: Date;
  operation: string;
  service: string;
  algorithm?: string;
  keyId?: string;
  executionTimeMs?: number;
  success: boolean;
  errorMessage?: string;
  userId?: string;
  ipAddress?: string;
}

/**
 * Interface pour une alerte cryptographique
 */
export interface CryptoAlert {
  id: string;
  timestamp: Date;
  type: 'performance' | 'security' | 'anomaly' | 'key_expiration';
  severity: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  details: Record<string, any>;
  acknowledged: boolean;
  resolvedAt?: Date;
}

/**
 * Service de monitoring cryptographique
 * Ce service surveille les opérations cryptographiques, collecte des métriques
 * et génère des alertes en cas d'anomalies
 */
@Injectable()
export class CryptoMonitoringService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(CryptoMonitoringService.name);
  private metrics: CryptoMetrics;
  private alerts: CryptoAlert[] = [];
  private events: CryptoEvent[] = [];
  private readonly maxEventsToStore: number;
  private readonly alertThresholds: {
    failureRate: number;
    executionTime: number;
    anomalyDetection: {
      enabled: boolean;
      sensitivityLevel: number;
      minSampleSize: number;
    };
  };
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly keyManagementService: KeyManagementService,
    private readonly cryptoLoggingService: CryptoLoggingService,
  ) {
    // Initialiser les métriques
    this.metrics = {
      operations: {
        total: 0,
        successful: 0,
        failed: 0,
        byType: {},
        byService: {},
      },
      performance: {
        averageExecutionTime: 0,
        p95ExecutionTime: 0,
        p99ExecutionTime: 0,
      },
      keys: {
        total: 0,
        active: 0,
        rotating: 0,
        expired: 0,
        revoked: 0,
        byAlgorithm: {},
      },
      anomalies: {
        count: 0,
        types: {},
      },
      lastUpdated: new Date(),
    };

    // Configurer les paramètres
    this.maxEventsToStore = this.configService.get<number>('crypto.monitoring.maxEventsToStore', 10000);
    this.alertThresholds = {
      failureRate: this.configService.get<number>('crypto.monitoring.alertThresholds.failureRate', 0.05), // 5%
      executionTime: this.configService.get<number>('crypto.monitoring.alertThresholds.executionTime', 500), // 500ms
      anomalyDetection: {
        enabled: this.configService.get<boolean>('crypto.monitoring.anomalyDetection.enabled', true),
        sensitivityLevel: this.configService.get<number>('crypto.monitoring.anomalyDetection.sensitivityLevel', 2.0),
        minSampleSize: this.configService.get<number>('crypto.monitoring.anomalyDetection.minSampleSize', 30),
      },
    };
  }

  /**
   * Initialisation du service
   */
  async onModuleInit() {
    try {
      this.logger.log('Initializing crypto monitoring service...');

      // S'abonner aux événements cryptographiques
      this.subscribeToEvents();

      // Démarrer la collecte périodique de métriques
      this.startMetricsCollection();

      this.logger.log('Crypto monitoring service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize crypto monitoring service:', error);
    }
  }

  /**
   * Nettoyage lors de l'arrêt du service
   */
  onModuleDestroy() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
    this.logger.log('Crypto monitoring service stopped');
  }

  /**
   * S'abonne aux événements cryptographiques
   */
  private subscribeToEvents() {
    // S'abonner aux événements de chiffrement
    this.eventEmitter.on('crypto.operation', (event: CryptoEvent) => {
      this.recordEvent(event);
    });

    // S'abonner aux événements de rotation de clés
    this.eventEmitter.on('crypto.key.rotation', (event: any) => {
      this.recordKeyRotation(event);
    });

    // S'abonner aux événements d'erreur cryptographique
    this.eventEmitter.on('crypto.error', (event: CryptoEvent) => {
      this.recordEvent(event);
      this.analyzeError(event);
    });
  }

  /**
   * Démarre la collecte périodique de métriques
   */
  private startMetricsCollection() {
    // Collecter les métriques toutes les minutes
    this.monitoringInterval = setInterval(() => {
      this.updateMetrics();
    }, 60000); // 1 minute
  }

  /**
   * Enregistre un événement cryptographique
   * @param event Événement à enregistrer
   */
  private recordEvent(event: CryptoEvent) {
    // Ajouter l'événement à la liste des événements
    this.events.push(event);

    // Limiter le nombre d'événements stockés
    if (this.events.length > this.maxEventsToStore) {
      this.events.shift(); // Supprimer l'événement le plus ancien
    }

    // Mettre à jour les métriques en temps réel
    this.updateRealTimeMetrics(event);
  }

  /**
   * Met à jour les métriques en temps réel
   * @param event Événement cryptographique
   */
  private updateRealTimeMetrics(event: CryptoEvent) {
    // Incrémenter le compteur total d'opérations
    this.metrics.operations.total++;

    // Incrémenter le compteur de succès ou d'échec
    if (event.success) {
      this.metrics.operations.successful++;
    } else {
      this.metrics.operations.failed++;
    }

    // Mettre à jour les compteurs par type d'opération
    if (!this.metrics.operations.byType[event.operation]) {
      this.metrics.operations.byType[event.operation] = 0;
    }
    this.metrics.operations.byType[event.operation]++;

    // Mettre à jour les compteurs par service
    if (!this.metrics.operations.byService[event.service]) {
      this.metrics.operations.byService[event.service] = 0;
    }
    this.metrics.operations.byService[event.service]++;

    // Mettre à jour les métriques de performance si disponibles
    if (event.executionTimeMs) {
      // Calculer la nouvelle moyenne d'exécution
      const totalTime = this.metrics.performance.averageExecutionTime * (this.metrics.operations.total - 1);
      this.metrics.performance.averageExecutionTime = (totalTime + event.executionTimeMs) / this.metrics.operations.total;

      // Vérifier si le temps d'exécution dépasse le seuil
      if (event.executionTimeMs > this.alertThresholds.executionTime) {
        this.createPerformanceAlert(event);
      }
    }

    // Mettre à jour la date de dernière mise à jour
    this.metrics.lastUpdated = new Date();
  }

  /**
   * Enregistre une rotation de clé
   * @param event Événement de rotation de clé
   */
  private recordKeyRotation(event: any) {
    // Enregistrer l'événement pour analyse ultérieure
    this.logger.debug('Key rotation event recorded', event);
  }

  /**
   * Analyse une erreur cryptographique
   * @param event Événement d'erreur
   */
  private analyzeError(event: CryptoEvent) {
    // Calculer le taux d'échec actuel
    const failureRate = this.metrics.operations.failed / this.metrics.operations.total;

    // Vérifier si le taux d'échec dépasse le seuil
    if (failureRate > this.alertThresholds.failureRate) {
      this.createSecurityAlert({
        type: 'security',
        severity: 'warning',
        message: `High crypto operation failure rate: ${(failureRate * 100).toFixed(2)}%`,
        details: {
          failureRate,
          threshold: this.alertThresholds.failureRate,
          recentErrors: this.getRecentErrors(5),
        },
      });
    }

    // Analyser le type d'erreur pour des alertes spécifiques
    if (event.errorMessage?.includes('key expired')) {
      this.createSecurityAlert({
        type: 'key_expiration',
        severity: 'error',
        message: `Expired key used: ${event.keyId}`,
        details: {
          keyId: event.keyId,
          operation: event.operation,
          service: event.service,
        },
      });
    } else if (event.errorMessage?.includes('invalid algorithm')) {
      this.createSecurityAlert({
        type: 'security',
        severity: 'critical',
        message: `Invalid cryptographic algorithm used: ${event.algorithm}`,
        details: {
          algorithm: event.algorithm,
          operation: event.operation,
          service: event.service,
        },
      });
    }
  }

  /**
   * Récupère les erreurs récentes
   * @param count Nombre d'erreurs à récupérer
   * @returns Liste des erreurs récentes
   */
  private getRecentErrors(count: number): CryptoEvent[] {
    return this.events
      .filter(event => !event.success)
      .slice(-count);
  }

  /**
   * Crée une alerte de performance
   * @param event Événement déclencheur
   */
  private createPerformanceAlert(event: CryptoEvent) {
    this.createAlert({
      type: 'performance',
      severity: 'warning',
      message: `Slow crypto operation: ${event.operation} (${event.executionTimeMs}ms)`,
      details: {
        operation: event.operation,
        service: event.service,
        executionTimeMs: event.executionTimeMs,
        threshold: this.alertThresholds.executionTime,
      },
    });
  }

  /**
   * Crée une alerte de sécurité
   * @param params Paramètres de l'alerte
   */
  private createSecurityAlert(params: {
    type: 'security' | 'anomaly' | 'key_expiration';
    severity: 'info' | 'warning' | 'error' | 'critical';
    message: string;
    details: Record<string, any>;
  }) {
    this.createAlert({
      type: params.type,
      severity: params.severity,
      message: params.message,
      details: params.details,
    });
  }

  /**
   * Crée une alerte
   * @param params Paramètres de l'alerte
   */
  private createAlert(params: {
    type: 'performance' | 'security' | 'anomaly' | 'key_expiration';
    severity: 'info' | 'warning' | 'error' | 'critical';
    message: string;
    details: Record<string, any>;
  }) {
    const alert: CryptoAlert = {
      id: `crypto-alert-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      timestamp: new Date(),
      type: params.type,
      severity: params.severity,
      message: params.message,
      details: params.details,
      acknowledged: false,
    };

    // Ajouter l'alerte à la liste des alertes
    this.alerts.push(alert);

    // Émettre un événement d'alerte
    this.eventEmitter.emit('crypto.alert', alert);

    // Journaliser l'alerte
    this.logAlert(alert);

    // Incrémenter le compteur d'anomalies
    this.metrics.anomalies.count++;
    if (!this.metrics.anomalies.types[params.type]) {
      this.metrics.anomalies.types[params.type] = 0;
    }
    this.metrics.anomalies.types[params.type]++;
  }

  /**
   * Journalise une alerte
   * @param alert Alerte à journaliser
   */
  private logAlert(alert: CryptoAlert) {
    switch (alert.severity) {
      case 'critical':
        this.logger.error(`CRITICAL CRYPTO ALERT: ${alert.message}`, alert.details);
        break;
      case 'error':
        this.logger.error(`Crypto Alert: ${alert.message}`, alert.details);
        break;
      case 'warning':
        this.logger.warn(`Crypto Warning: ${alert.message}`, alert.details);
        break;
      case 'info':
        this.logger.log(`Crypto Info: ${alert.message}`, alert.details);
        break;
    }
  }

  /**
   * Met à jour les métriques complètes
   * Cette méthode est exécutée périodiquement
   */
  private async updateMetrics() {
    try {
      // Mettre à jour les métriques des clés
      await this.updateKeyMetrics();

      // Calculer les percentiles de temps d'exécution
      this.updatePerformanceMetrics();

      // Détecter les anomalies
      if (this.alertThresholds.anomalyDetection.enabled) {
        this.detectAnomalies();
      }

      // Mettre à jour la date de dernière mise à jour
      this.metrics.lastUpdated = new Date();

      // Émettre un événement de mise à jour des métriques
      this.eventEmitter.emit('crypto.metrics.updated', this.getMetrics());
    } catch (error) {
      this.logger.error('Failed to update crypto metrics:', error);
    }
  }

  /**
   * Met à jour les métriques des clés
   */
  private async updateKeyMetrics() {
    try {
      // Réinitialiser les compteurs
      this.metrics.keys = {
        total: 0,
        active: 0,
        rotating: 0,
        expired: 0,
        revoked: 0,
        byAlgorithm: {},
      };

      // Récupérer toutes les clés
      const keys = await this.getAllKeys();

      // Mettre à jour les compteurs
      this.metrics.keys.total = keys.length;

      for (const key of keys) {
        // Compter par statut
        switch (key.metadata.status) {
          case 'active':
            this.metrics.keys.active++;
            break;
          case 'rotating':
            this.metrics.keys.rotating++;
            break;
          case 'deprecated':
            // Vérifier si la clé est expirée
            if (key.metadata.expiresAt < new Date()) {
              this.metrics.keys.expired++;
            }
            break;
          case 'revoked':
            this.metrics.keys.revoked++;
            break;
        }

        // Compter par algorithme
        if (!this.metrics.keys.byAlgorithm[key.metadata.algorithm]) {
          this.metrics.keys.byAlgorithm[key.metadata.algorithm] = 0;
        }
        this.metrics.keys.byAlgorithm[key.metadata.algorithm]++;
      }
    } catch (error) {
      this.logger.error('Failed to update key metrics:', error);
    }
  }

  /**
   * Récupère toutes les clés
   * @returns Liste des clés
   */
  private async getAllKeys(): Promise<any[]> {
    try {
      // Cette méthode devrait être implémentée pour récupérer toutes les clés
      // depuis le service de gestion des clés
      // Pour l'instant, nous retournons un tableau vide
      return [];
    } catch (error) {
      this.logger.error('Failed to get all keys:', error);
      return [];
    }
  }

  /**
   * Met à jour les métriques de performance
   */
  private updatePerformanceMetrics() {
    try {
      // Récupérer les temps d'exécution des événements réussis
      const executionTimes = this.events
        .filter(event => event.success && event.executionTimeMs !== undefined)
        .map(event => event.executionTimeMs as number)
        .sort((a, b) => a - b);

      if (executionTimes.length > 0) {
        // Calculer les percentiles
        const p95Index = Math.floor(executionTimes.length * 0.95);
        const p99Index = Math.floor(executionTimes.length * 0.99);

        this.metrics.performance.p95ExecutionTime = executionTimes[p95Index] || 0;
        this.metrics.performance.p99ExecutionTime = executionTimes[p99Index] || 0;
      }
    } catch (error) {
      this.logger.error('Failed to update performance metrics:', error);
    }
  }

  /**
   * Détecte les anomalies dans les opérations cryptographiques
   */
  private detectAnomalies() {
    try {
      // Implémenter la détection d'anomalies
      // Cette méthode pourrait utiliser des algorithmes statistiques pour détecter
      // des comportements anormaux dans les opérations cryptographiques
    } catch (error) {
      this.logger.error('Failed to detect anomalies:', error);
    }
  }

  /**
   * Récupère les métriques actuelles
   * @returns Métriques cryptographiques
   */
  public getMetrics(): CryptoMetrics {
    return { ...this.metrics };
  }

  /**
   * Récupère les alertes actives
   * @param includeAcknowledged Inclure les alertes acquittées
   * @returns Liste des alertes
   */
  public getAlerts(includeAcknowledged: boolean = false): CryptoAlert[] {
    if (includeAcknowledged) {
      return [...this.alerts];
    }
    return this.alerts.filter(alert => !alert.acknowledged);
  }

  /**
   * Acquitte une alerte
   * @param alertId Identifiant de l'alerte
   * @returns Succès de l'opération
   */
  public acknowledgeAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      return true;
    }
    return false;
  }

  /**
   * Résout une alerte
   * @param alertId Identifiant de l'alerte
   * @returns Succès de l'opération
   */
  public resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      alert.resolvedAt = new Date();
      return true;
    }
    return false;
  }

  /**
   * Nettoie les alertes résolues anciennes
   * Cette méthode est exécutée quotidiennement
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  private cleanupResolvedAlerts() {
    try {
      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      // Supprimer les alertes résolues de plus de 30 jours
      this.alerts = this.alerts.filter(alert => {
        if (alert.resolvedAt && alert.resolvedAt < thirtyDaysAgo) {
          return false;
        }
        return true;
      });

      this.logger.debug(`Cleaned up resolved alerts. Remaining alerts: ${this.alerts.length}`);
    } catch (error) {
      this.logger.error('Failed to clean up resolved alerts:', error);
    }
  }

  /**
   * Génère un rapport de sécurité cryptographique
   * Cette méthode est exécutée hebdomadairement
   */
  @Cron(CronExpression.EVERY_WEEK)
  private async generateSecurityReport() {
    try {
      const report = {
        timestamp: new Date(),
        metrics: this.getMetrics(),
        alerts: {
          total: this.alerts.length,
          active: this.alerts.filter(a => !a.acknowledged).length,
          byType: {} as Record<string, number>,
          bySeverity: {} as Record<string, number>,
        },
        recommendations: [] as string[],
      };

      // Compter les alertes par type et sévérité
      for (const alert of this.alerts) {
        if (!report.alerts.byType[alert.type]) {
          report.alerts.byType[alert.type] = 0;
        }
        report.alerts.byType[alert.type]++;

        if (!report.alerts.bySeverity[alert.severity]) {
          report.alerts.bySeverity[alert.severity] = 0;
        }
        report.alerts.bySeverity[alert.severity]++;
      }

      // Générer des recommandations
      if (this.metrics.operations.failed > 0) {
        const failureRate = this.metrics.operations.failed / this.metrics.operations.total;
        if (failureRate > 0.01) { // Plus de 1% d'échecs
          report.recommendations.push(`Investigate high failure rate (${(failureRate * 100).toFixed(2)}%) in crypto operations.`);
        }
      }

      if (this.metrics.keys.expired > 0) {
        report.recommendations.push(`Remove or rotate ${this.metrics.keys.expired} expired encryption keys.`);
      }

      if (this.metrics.performance.p95ExecutionTime > this.alertThresholds.executionTime) {
        report.recommendations.push(`Optimize crypto operations. 95th percentile execution time (${this.metrics.performance.p95ExecutionTime}ms) exceeds threshold.`);
      }

      // Journaliser le rapport
      this.logger.log('Weekly crypto security report generated', report);

      // Émettre un événement de rapport
      this.eventEmitter.emit('crypto.report.generated', report);
    } catch (error) {
      this.logger.error('Failed to generate security report:', error);
    }
  }
}
