import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const statAsync = promisify(fs.stat);

@Injectable()
export class FileSecurityService {
  async scanForVulnerabilities(): Promise<any[]> {
    // Stub temporaire pour lever l'erreur
    return [];
  }
  private readonly logger = new Logger(FileSecurityService.name);
  private readonly allowedMimeTypes: string[];
  private readonly maxFileSize: number;
  private readonly uploadDir: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
  ) {
    this.allowedMimeTypes = this.configService.get<string[]>('app.allowedMimeTypes') || [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
    ];
    this.maxFileSize = this.configService.get<number>('app.maxUploadSize') || 10 * 1024 * 1024; // 10MB default
    this.uploadDir = this.configService.get<string>('app.uploadDir') || 'uploads';

    // Ensure upload directory exists
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
    }
  }

  async validateFile(file: Express.Multer.File): Promise<{ valid: boolean; reason?: string }> {
    // Check file size
    if (file.size > this.maxFileSize) {
      return {
        valid: false,
        reason: `File size exceeds the maximum allowed size of ${this.maxFileSize / 1024 / 1024}MB`,
      };
    }

    // Check MIME type
    if (!this.allowedMimeTypes.includes(file.mimetype)) {
      return {
        valid: false,
        reason: `File type ${file.mimetype} is not allowed. Allowed types: ${this.allowedMimeTypes.join(', ')}`,
      };
    }

    // Validate file content (basic check)
    try {
      const isValidContent = await this.validateFileContent(file);
      if (!isValidContent) {
        return {
          valid: false,
          reason: 'File content validation failed. The file may be corrupted or contain malicious code.',
        };
      }
    } catch (error) {
      this.logger.error(`File content validation error: ${error.message}`, error.stack);
      return {
        valid: false,
        reason: 'Error during file content validation',
      };
    }

    return { valid: true };
  }

  private async validateFileContent(file: Express.Multer.File): Promise<boolean> {
    // Basic content validation based on file type
    switch (file.mimetype) {
      case 'image/jpeg':
      case 'image/png':
      case 'image/gif':
        return this.validateImageFile(file);
      case 'application/pdf':
        return this.validatePdfFile(file);
      default:
        return true; // Skip validation for other types
    }
  }

  private async validateImageFile(file: Express.Multer.File): Promise<boolean> {
    // Check for common image file signatures
    const buffer = file.buffer || (await readFileAsync(file.path));
    const fileSignature = buffer.toString('hex', 0, 8).toUpperCase();

    // JPEG signature: FF D8 FF
    if (file.mimetype === 'image/jpeg' && !fileSignature.startsWith('FFD8FF')) {
      return false;
    }

    // PNG signature: 89 50 4E 47 0D 0A 1A 0A
    if (file.mimetype === 'image/png' && fileSignature !== '89504E470D0A1A0A') {
      return false;
    }

    // GIF signature: 47 49 46 38
    if (file.mimetype === 'image/gif' && !fileSignature.startsWith('47494638')) {
      return false;
    }

    return true;
  }

  private async validatePdfFile(file: Express.Multer.File): Promise<boolean> {
    // Check for PDF signature: %PDF-
    const buffer = file.buffer || (await readFileAsync(file.path));
    const fileSignature = buffer.toString('ascii', 0, 5);
    return fileSignature === '%PDF-';
  }

  async scanFileForMalware(file: Express.Multer.File): Promise<{ clean: boolean; reason?: string }> {
    // This is a placeholder for actual malware scanning
    // In a real implementation, you would integrate with a malware scanning service
    this.logger.log(`Scanning file ${file.originalname} for malware`);

    // Log the scan event
    await this.logFileSecurityEvent('FILE_SCAN', {
      filename: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
    });

    // Simulate scanning
    return { clean: true };
  }

  async saveSecureFile(file: Express.Multer.File, userId: string): Promise<string> {
    try {
      // Generate a secure filename
      const fileExtension = path.extname(file.originalname);
      const secureFilename = `${crypto.randomUUID()}${fileExtension}`;
      const filePath = path.join(this.uploadDir, secureFilename);

      // Save the file
      await writeFileAsync(filePath, file.buffer || (await readFileAsync(file.path)));

      // Calculate file hash for integrity verification
      const fileHash = crypto
        .createHash('sha256')
        .update(file.buffer || (await readFileAsync(filePath)))
        .digest('hex');

      // Log file upload
      await this.logFileSecurityEvent('FILE_UPLOAD', {
        filename: file.originalname,
        secureFilename,
        mimetype: file.mimetype,
        size: file.size,
        hash: fileHash,
        userId,
      });

      // Create file record in database
      await this.prisma.file.create({
        data: {
          originalName: file.originalname,
          storedName: secureFilename,
          path: filePath,
          mimeType: file.mimetype,
          size: file.size,
          hash: fileHash,
          uploadedBy: userId,
          uploadedAt: new Date(),
        },
      });

      return secureFilename;
    } catch (error) {
      this.logger.error(`Error saving secure file: ${error.message}`, error.stack);
      throw error;
    }
  }

  async verifyFileIntegrity(filename: string): Promise<boolean> {
    try {
      // Get file record from database
      const fileRecord = await this.prisma.file.findUnique({
        where: { storedName: filename },
      });

      if (!fileRecord) {
        this.logger.warn(`File record not found for ${filename}`);
        return false;
      }

      // Read the file
      const filePath = path.join(this.uploadDir, filename);
      const fileBuffer = await readFileAsync(filePath);

      // Calculate current hash
      const currentHash = crypto.createHash('sha256').update(fileBuffer).digest('hex');

      // Compare with stored hash
      const isIntact = currentHash === fileRecord.hash;

      if (!isIntact) {
        this.logger.warn(`File integrity check failed for ${filename}`);
        await this.logFileSecurityEvent('FILE_INTEGRITY_FAILURE', {
          filename,
          storedHash: fileRecord.hash,
          currentHash,
        });
      }

      return isIntact;
    } catch (error) {
      this.logger.error(`Error verifying file integrity: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Récupère les informations d'un fichier à partir de son nom sécurisé
   * @param filename Nom sécurisé du fichier
   * @returns Informations sur le fichier ou null si le fichier n'existe pas
   */
  async getFileRecord(filename: string): Promise<any> {
    try {
      return await this.prisma.file.findUnique({
        where: { storedName: filename },
      });
    } catch (error) {
      this.logger.error(`Error getting file record: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Récupère le nombre de fichiers scannés
   * @returns Nombre de fichiers scannés
   */
  async getScannedFilesCount(): Promise<number> {
    try {
      return await this.prisma.file.count();
    } catch (error) {
      this.logger.error(`Error getting scanned files count: ${error.message}`, error.stack);
      return 0;
    }
  }

  /**
   * Récupère le nombre de menaces détectées
   * @returns Nombre de menaces détectées
   */
  async getDetectedThreatsCount(): Promise<number> {
    try {
      return await this.prisma.securityEvent.count({
        where: {
          type: 'MALWARE_DETECTED',
          source: 'FILE_SECURITY',
        },
      });
    } catch (error) {
      this.logger.error(`Error getting detected threats count: ${error.message}`, error.stack);
      return 0;
    }
  }

  private async logFileSecurityEvent(
    eventType: string,
    details: Record<string, any>,
  ): Promise<void> {
    try {
      await this.prisma.securityEvent.create({
        data: {
          type: eventType,
          source: 'file-security',
          details: JSON.stringify(details),
          severity: 'INFO',
          timestamp: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(`Failed to log file security event: ${error.message}`, error.stack);
    }
  }
}
