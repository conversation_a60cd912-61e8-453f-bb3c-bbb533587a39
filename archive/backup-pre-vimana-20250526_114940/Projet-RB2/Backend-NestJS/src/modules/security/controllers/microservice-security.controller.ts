import { Controller, Get, Post, HttpStatus, HttpException, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { MicroserviceSecurityService } from '../services/microservice-security.service';
import { Roles } from '../../../common/decorators/roles.decorator';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';

@ApiTags('microservice-security')
@Controller('api/security/microservice')
@UseGuards(JwtAuthGuard, RolesGuard)
export class MicroserviceSecurityController {
  constructor(private readonly microserviceSecurityService: MicroserviceSecurityService) {}

  /**
   * Vérifie l'état de santé du service de sécurité des microservices
   */
  @Get('health')
  @ApiOperation({ summary: 'Vérifie l\'état de santé du service de sécurité des microservices' })
  @ApiResponse({ status: 200, description: 'État de santé du service' })
  async checkHealth() {
    try {
      const healthStatus = await this.microserviceSecurityService.healthCheck();
      return {
        statusCode: HttpStatus.OK,
        data: healthStatus,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to check microservice security health',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Force la rotation des clés de service
   */
  @Post('keys/rotate')
  @Roles('admin')
  @ApiOperation({ summary: 'Force la rotation des clés de service' })
  @ApiResponse({ status: 200, description: 'Clés rotées avec succès' })
  async rotateKeys() {
    try {
      const newVersion = await this.microserviceSecurityService.forceKeyRotation();
      return {
        statusCode: HttpStatus.OK,
        message: 'Service keys rotated successfully',
        data: { newVersion },
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to rotate service keys',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupère la clé publique du service
   */
  @Get('keys/public')
  @ApiOperation({ summary: 'Récupère la clé publique du service' })
  @ApiResponse({ status: 200, description: 'Clé publique du service' })
  getPublicKey() {
    try {
      const publicKeyInfo = this.microserviceSecurityService.getPublicKey();
      
      if (!publicKeyInfo) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: 'Public key not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }
      
      return {
        statusCode: HttpStatus.OK,
        data: publicKeyInfo,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to get public key',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Valide les certificats du service
   */
  @Post('certificates/validate')
  @Roles('admin')
  @ApiOperation({ summary: 'Valide les certificats du service' })
  @ApiResponse({ status: 200, description: 'Résultat de la validation des certificats' })
  async validateCertificates() {
    try {
      const certPath = this.microserviceSecurityService['options'].certPath;
      const validationResult = await this.microserviceSecurityService.validateCertificate(certPath);
      
      return {
        statusCode: HttpStatus.OK,
        data: validationResult,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to validate certificates',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
