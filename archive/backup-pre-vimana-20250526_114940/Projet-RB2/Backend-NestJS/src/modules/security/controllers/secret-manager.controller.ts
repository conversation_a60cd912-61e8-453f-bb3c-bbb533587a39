import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../../common/decorators/roles.decorator';
import { SecretManagerService, SecretType, SecretMetadata } from '../services/secret-manager.service';

@ApiTags('secret-management')
@Controller('api/security/secrets')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class SecretManagerController {
  private readonly logger = new Logger(SecretManagerController.name);

  constructor(private readonly secretManagerService: SecretManagerService) {}

  @Post()
  @Roles('admin')
  @ApiOperation({ summary: 'Crée un nouveau secret' })
  @ApiResponse({ status: 201, description: 'Secret créé avec succès' })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['name', 'value'],
      properties: {
        name: { type: 'string', example: 'api-key-service-a' },
        value: { type: 'string', example: 'secret-value' },
        type: { 
          type: 'string', 
          enum: Object.values(SecretType),
          example: SecretType.API_KEY 
        },
        description: { type: 'string', example: 'API key for Service A' },
        expiresInDays: { type: 'number', example: 90 },
        rotationInterval: { type: 'number', example: 2592000000 }, // 30 jours en ms
        autoRotate: { type: 'boolean', example: true },
        tags: { type: 'array', items: { type: 'string' }, example: ['api', 'service-a'] },
        owner: { type: 'string', example: 'team-backend' },
        application: { type: 'string', example: 'service-a' },
        environment: { type: 'string', example: 'production' },
      },
    },
  })
  async createSecret(
    @Body() body: {
      name: string;
      value: string;
      type?: SecretType;
      description?: string;
      expiresInDays?: number;
      rotationInterval?: number;
      autoRotate?: boolean;
      tags?: string[];
      owner?: string;
      application?: string;
      environment?: string;
    },
  ) {
    try {
      const secretId = await this.secretManagerService.createSecret(
        body.name,
        body.value,
        body.type || SecretType.GENERAL,
        {
          description: body.description,
          expiresInDays: body.expiresInDays,
          rotationInterval: body.rotationInterval,
          autoRotate: body.autoRotate,
          tags: body.tags,
          owner: body.owner,
          application: body.application,
          environment: body.environment,
        },
      );

      return {
        statusCode: HttpStatus.CREATED,
        message: 'Secret created successfully',
        data: { secretId },
      };
    } catch (error) {
      this.logger.error(`Failed to create secret: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to create secret',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  @Roles('admin')
  @ApiOperation({ summary: 'Liste tous les secrets' })
  @ApiResponse({ status: 200, description: 'Liste des secrets récupérée avec succès' })
  @ApiQuery({ name: 'type', required: false, enum: Object.values(SecretType) })
  @ApiQuery({ name: 'application', required: false })
  @ApiQuery({ name: 'environment', required: false })
  @ApiQuery({ name: 'tags', required: false, isArray: true, type: 'string' })
  async listSecrets(
    @Query('type') type?: SecretType,
    @Query('application') application?: string,
    @Query('environment') environment?: string,
    @Query('tags') tags?: string | string[],
  ) {
    try {
      // Convertir les tags en tableau si nécessaire
      const tagsArray = tags ? (Array.isArray(tags) ? tags : [tags]) : undefined;

      const secrets = await this.secretManagerService.listSecrets({
        type,
        application,
        environment,
        tags: tagsArray,
      });

      // Filtrer les informations sensibles
      const filteredSecrets = secrets.map(secret => this.filterSensitiveInfo(secret));

      return {
        statusCode: HttpStatus.OK,
        data: filteredSecrets,
      };
    } catch (error) {
      this.logger.error(`Failed to list secrets: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to list secrets',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Récupère un secret par son ID' })
  @ApiResponse({ status: 200, description: 'Secret récupéré avec succès' })
  @ApiParam({ name: 'id', description: 'ID du secret' })
  async getSecret(@Param('id') secretId: string) {
    try {
      const secret = await this.secretManagerService.getSecret(secretId);

      return {
        statusCode: HttpStatus.OK,
        data: {
          id: secret.metadata.id,
          name: secret.metadata.name,
          type: secret.metadata.type,
          value: secret.value,
          metadata: this.filterSensitiveInfo(secret.metadata),
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get secret: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to get secret',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('by-name/:name')
  @Roles('admin')
  @ApiOperation({ summary: 'Récupère un secret par son nom' })
  @ApiResponse({ status: 200, description: 'Secret récupéré avec succès' })
  @ApiParam({ name: 'name', description: 'Nom du secret' })
  @ApiQuery({ name: 'type', required: false, enum: Object.values(SecretType) })
  async getSecretByName(
    @Param('name') name: string,
    @Query('type') type?: SecretType,
  ) {
    try {
      const secret = await this.secretManagerService.getSecretByName(name, type);

      return {
        statusCode: HttpStatus.OK,
        data: {
          id: secret.metadata.id,
          name: secret.metadata.name,
          type: secret.metadata.type,
          value: secret.value,
          metadata: this.filterSensitiveInfo(secret.metadata),
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get secret by name: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to get secret by name',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Met à jour un secret' })
  @ApiResponse({ status: 200, description: 'Secret mis à jour avec succès' })
  @ApiParam({ name: 'id', description: 'ID du secret' })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['value'],
      properties: {
        value: { type: 'string', example: 'new-secret-value' },
      },
    },
  })
  async updateSecret(
    @Param('id') secretId: string,
    @Body() body: { value: string },
  ) {
    try {
      const updatedMetadata = await this.secretManagerService.updateSecret(secretId, body.value);

      return {
        statusCode: HttpStatus.OK,
        message: 'Secret updated successfully',
        data: {
          id: updatedMetadata.id,
          metadata: this.filterSensitiveInfo(updatedMetadata),
        },
      };
    } catch (error) {
      this.logger.error(`Failed to update secret: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to update secret',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':id/rotate')
  @Roles('admin')
  @ApiOperation({ summary: 'Effectue la rotation d\'un secret' })
  @ApiResponse({ status: 200, description: 'Rotation du secret effectuée avec succès' })
  @ApiParam({ name: 'id', description: 'ID du secret' })
  async rotateSecret(@Param('id') secretId: string) {
    try {
      const newSecretId = await this.secretManagerService.rotateSecret(secretId);

      return {
        statusCode: HttpStatus.OK,
        message: 'Secret rotated successfully',
        data: {
          oldSecretId: secretId,
          newSecretId,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to rotate secret: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to rotate secret',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Supprime un secret' })
  @ApiResponse({ status: 200, description: 'Secret supprimé avec succès' })
  @ApiParam({ name: 'id', description: 'ID du secret' })
  async deleteSecret(@Param('id') secretId: string) {
    try {
      await this.secretManagerService.deleteSecret(secretId);

      return {
        statusCode: HttpStatus.OK,
        message: 'Secret deleted successfully',
      };
    } catch (error) {
      this.logger.error(`Failed to delete secret: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to delete secret',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Filtre les informations sensibles des métadonnées d'un secret
   * @param metadata Métadonnées du secret
   * @returns Métadonnées filtrées
   */
  private filterSensitiveInfo(metadata: SecretMetadata): Partial<SecretMetadata> {
    const { id, name, type, description, createdAt, updatedAt, expiresAt, tags, owner, application, environment, version } = metadata;
    return {
      id,
      name,
      type,
      description,
      createdAt,
      updatedAt,
      expiresAt,
      tags,
      owner,
      application,
      environment,
      version,
    };
  }
}
