import { Controller, Get, Post, Param, Body, HttpStatus, HttpException, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CertificateManagementService } from '../services/certificate-management.service';
import { CertificateRotationService, CertificateValidationResult } from '../services/certificate-rotation.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';

/**
 * Contrôleur pour la gestion des certificats
 * Ce contrôleur expose des endpoints pour gérer les certificats mTLS
 */
@ApiTags('security/certificates')
@Controller('security/certificates')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class CertificateController {
  constructor(
    private readonly certificateManagementService: CertificateManagementService,
    private readonly certificateRotationService: CertificateRotationService,
  ) {}

  /**
   * Génère un certificat pour un service
   */
  @Post('generate/:serviceName')
  @Roles('admin')
  @ApiOperation({ summary: 'Génère un certificat pour un service' })
  @ApiResponse({ status: 201, description: 'Certificat généré avec succès' })
  async generateCertificate(@Param('serviceName') serviceName: string) {
    try {
      const result = await this.certificateManagementService.generateServiceCertificates(serviceName);
      return {
        statusCode: HttpStatus.CREATED,
        message: `Certificate generated for service ${serviceName}`,
        data: result,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to generate certificate',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Vérifie l'état de santé des certificats
   */
  @Get('health')
  @Roles('admin')
  @ApiOperation({ summary: 'Vérifie l\'état de santé des certificats' })
  @ApiResponse({ status: 200, description: 'État de santé des certificats' })
  async checkCertificateHealth(): Promise<{
    statusCode: number;
    message: string;
    data: CertificateValidationResult;
  }> {
    try {
      const result = await this.certificateRotationService.checkCertificateHealth();
      return {
        statusCode: HttpStatus.OK,
        message: result.valid ? 'Certificates are valid' : 'Certificates are invalid',
        data: result,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to check certificate health',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Effectue la rotation des certificats
   */
  @Post('rotate')
  @Roles('admin')
  @ApiOperation({ summary: 'Effectue la rotation des certificats' })
  @ApiResponse({ status: 200, description: 'Rotation des certificats effectuée avec succès' })
  async rotateCertificates() {
    try {
      const result = await this.certificateRotationService.rotateCertificates();
      return {
        statusCode: HttpStatus.OK,
        message: result ? 'Certificate rotation completed successfully' : 'Certificate rotation failed',
        success: result,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to rotate certificates',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Liste les sauvegardes de certificats disponibles
   */
  @Get('backups')
  @Roles('admin')
  @ApiOperation({ summary: 'Liste les sauvegardes de certificats disponibles' })
  @ApiResponse({ status: 200, description: 'Liste des sauvegardes de certificats' })
  async listCertificateBackups() {
    try {
      const backups = await this.certificateRotationService.listCertificateBackups();
      return {
        statusCode: HttpStatus.OK,
        message: 'Certificate backups retrieved successfully',
        data: backups,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to list certificate backups',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Restaure des certificats à partir d'une sauvegarde
   */
  @Post('restore/:backupId')
  @Roles('admin')
  @ApiOperation({ summary: 'Restaure des certificats à partir d\'une sauvegarde' })
  @ApiResponse({ status: 200, description: 'Certificats restaurés avec succès' })
  async restoreCertificatesFromBackup(@Param('backupId') backupId: string) {
    try {
      const backups = await this.certificateRotationService.listCertificateBackups();
      
      if (!backups.includes(backupId)) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: 'Backup not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }
      
      const backupDir = `certs/backup/${backupId}`;
      const result = await this.certificateRotationService.restoreCertificatesFromBackup(backupDir);
      
      return {
        statusCode: HttpStatus.OK,
        message: result ? 'Certificates restored successfully' : 'Certificate restoration failed',
        success: result,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to restore certificates',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
