import {
  Controller,
  Post,
  Get,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Body,
  Param,
  Req,
  Res,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { FileSecurityService } from '../services/file-security.service';
import { SecurityEventService } from '../services/security-event.service';
import { SecurityEventSeverity } from '../dto/create-security-event.dto';
import { Request, Response } from 'express';

@ApiTags('file-security')
@Controller('security/files')
export class FileSecurityController {
  constructor(
    private readonly fileSecurityService: FileSecurityService,
    private readonly securityEventService: SecurityEventService,
  ) {}

  @Post('upload')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Upload a file securely' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({ status: 201, description: 'File uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Invalid file' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(@UploadedFile() file: Express.Multer.File, @Req() req: Request) {
    try {
      // Validate file
      const validationResult = await this.fileSecurityService.validateFile(file);
      if (!validationResult.valid) {
        // Log security event for invalid file
        await this.securityEventService.logSecurityEvent({
          eventType: 'INVALID_FILE_UPLOAD',
          severity: SecurityEventSeverity.WARNING,
          source: 'FILE_SECURITY',
          details: {
            filename: file.originalname,
            reason: validationResult.reason,
            userId: req.user['id'],
          },
        });

        throw new HttpException(validationResult.reason, HttpStatus.BAD_REQUEST);
      }

      // Scan file for malware
      const scanResult = await this.fileSecurityService.scanFileForMalware(file);
      if (!scanResult.clean) {
        // Log security event for malware detection
        await this.securityEventService.logSecurityEvent({
          eventType: 'MALWARE_DETECTED',
          severity: SecurityEventSeverity.ERROR,
          source: 'FILE_SECURITY',
          details: {
            filename: file.originalname,
            reason: scanResult.reason,
            userId: req.user['id'],
          },
        });

        throw new HttpException(
          'File contains malware or suspicious content',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Save file securely
      const secureFilename = await this.fileSecurityService.saveSecureFile(file, req.user['id']);

      return {
        filename: secureFilename,
        originalName: file.originalname,
        mimeType: file.mimetype,
        size: file.size,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to upload file: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':filename')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a secure file' })
  @ApiResponse({ status: 200, description: 'Returns the file' })
  @ApiResponse({ status: 404, description: 'File not found' })
  async getFile(@Param('filename') filename: string, @Res() res: Response) {
    try {
      // Verify file integrity
      const isIntact = await this.fileSecurityService.verifyFileIntegrity(filename);
      if (!isIntact) {
        throw new HttpException('File integrity check failed', HttpStatus.BAD_REQUEST);
      }

      // Get file record from database
      const fileRecord = await this.fileSecurityService.getFileRecord(filename);
      if (!fileRecord) {
        throw new HttpException('File not found', HttpStatus.NOT_FOUND);
      }

      // Set appropriate headers
      res.setHeader('Content-Type', fileRecord.mimeType);
      res.setHeader('Content-Disposition', `attachment; filename="${fileRecord.originalName}"`);

      // Send file
      return res.sendFile(fileRecord.path, { root: './' });
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to get file: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('validate')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Validate a file without uploading it' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({ status: 200, description: 'File validation result' })
  @UseInterceptors(FileInterceptor('file'))
  async validateFile(@UploadedFile() file: Express.Multer.File) {
    try {
      // Validate file
      const validationResult = await this.fileSecurityService.validateFile(file);

      // If valid, scan for malware
      if (validationResult.valid) {
        const scanResult = await this.fileSecurityService.scanFileForMalware(file);
        return {
          valid: validationResult.valid && scanResult.clean,
          reason: scanResult.clean ? null : scanResult.reason,
        };
      }

      return validationResult;
    } catch (error) {
      throw new HttpException(
        `Failed to validate file: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('scan/status')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get file scanning status' })
  @ApiResponse({ status: 200, description: 'Returns file scanning status' })
  async getScanningStatus() {
    try {
      // Get scanning status
      return {
        status: 'operational',
        lastScanTime: new Date(),
        scannedFilesCount: await this.fileSecurityService.getScannedFilesCount(),
        detectedThreatsCount: await this.fileSecurityService.getDetectedThreatsCount(),
      };
    } catch (error) {
      throw new HttpException(
        `Failed to get scanning status: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
