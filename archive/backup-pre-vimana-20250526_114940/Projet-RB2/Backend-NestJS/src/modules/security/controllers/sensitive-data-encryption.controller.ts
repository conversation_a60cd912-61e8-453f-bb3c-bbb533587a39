import { Controller, Post, Get, Body, HttpStatus, HttpException, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { SensitiveDataEncryptionService } from '../services/sensitive-data-encryption.service';
import { Roles } from '../../../common/decorators/roles.decorator';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';

@ApiTags('sensitive-data-encryption')
@Controller('api/security/sensitive-data')
@UseGuards(JwtAuthGuard, RolesGuard)
export class SensitiveDataEncryptionController {
  constructor(private readonly sensitiveDataEncryptionService: SensitiveDataEncryptionService) {}

  /**
   * Chiffre des données sensibles
   */
  @Post('encrypt')
  @ApiOperation({ summary: 'Chiffre des données sensibles' })
  @ApiResponse({ status: 200, description: 'Données chiffrées avec succès' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        data: { type: 'object', description: 'Données à chiffrer' },
        type: { type: 'string', description: 'Type de données sensibles' },
      },
      required: ['data'],
    },
  })
  async encryptData(@Body() body: { data: any; type?: string }) {
    try {
      const encryptedData = await this.sensitiveDataEncryptionService.encryptSensitiveData(
        body.data,
        body.type,
      );
      return {
        statusCode: HttpStatus.OK,
        message: 'Data encrypted successfully',
        data: encryptedData,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to encrypt data',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Déchiffre des données sensibles
   */
  @Post('decrypt')
  @ApiOperation({ summary: 'Déchiffre des données sensibles' })
  @ApiResponse({ status: 200, description: 'Données déchiffrées avec succès' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        encryptedData: {
          type: 'object',
          properties: {
            data: { type: 'string' },
            iv: { type: 'string' },
            authTag: { type: 'string' },
            keyId: { type: 'string' },
            algorithm: { type: 'string' },
            version: { type: 'number' },
          },
          required: ['data', 'iv', 'authTag', 'keyId', 'algorithm', 'version'],
        },
      },
      required: ['encryptedData'],
    },
  })
  async decryptData(@Body() body: { encryptedData: any }) {
    try {
      const decryptedData = await this.sensitiveDataEncryptionService.decryptSensitiveData(
        body.encryptedData,
      );
      return {
        statusCode: HttpStatus.OK,
        message: 'Data decrypted successfully',
        data: decryptedData,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to decrypt data',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Chiffre les champs sensibles d'un objet
   */
  @Post('encrypt-fields')
  @ApiOperation({ summary: 'Chiffre les champs sensibles d\'un objet' })
  @ApiResponse({ status: 200, description: 'Champs sensibles chiffrés avec succès' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        data: { type: 'object', description: 'Objet contenant des champs sensibles' },
      },
      required: ['data'],
    },
  })
  async encryptFields(@Body() body: { data: object }) {
    try {
      const encryptedObject = await this.sensitiveDataEncryptionService.encryptSensitiveFields(
        body.data,
      );
      return {
        statusCode: HttpStatus.OK,
        message: 'Sensitive fields encrypted successfully',
        data: encryptedObject,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to encrypt sensitive fields',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Déchiffre les champs sensibles d'un objet
   */
  @Post('decrypt-fields')
  @ApiOperation({ summary: 'Déchiffre les champs sensibles d\'un objet' })
  @ApiResponse({ status: 200, description: 'Champs sensibles déchiffrés avec succès' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        data: { type: 'object', description: 'Objet contenant des champs chiffrés' },
      },
      required: ['data'],
    },
  })
  async decryptFields(@Body() body: { data: object }) {
    try {
      const decryptedObject = await this.sensitiveDataEncryptionService.decryptSensitiveFields(
        body.data,
      );
      return {
        statusCode: HttpStatus.OK,
        message: 'Sensitive fields decrypted successfully',
        data: decryptedObject,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to decrypt sensitive fields',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Identifie les champs sensibles dans un objet
   */
  @Post('identify-fields')
  @ApiOperation({ summary: 'Identifie les champs sensibles dans un objet' })
  @ApiResponse({ status: 200, description: 'Champs sensibles identifiés avec succès' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        data: { type: 'object', description: 'Objet à analyser' },
      },
      required: ['data'],
    },
  })
  async identifySensitiveFields(@Body() body: { data: object }) {
    try {
      const sensitiveFields = this.sensitiveDataEncryptionService.identifySensitiveFields(
        body.data,
      );
      return {
        statusCode: HttpStatus.OK,
        message: 'Sensitive fields identified successfully',
        data: {
          sensitiveFields,
          count: sensitiveFields.length,
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to identify sensitive fields',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Obtient la liste des champs sensibles configurés
   */
  @Get('sensitive-fields')
  @ApiOperation({ summary: 'Obtient la liste des champs sensibles configurés' })
  @ApiResponse({ status: 200, description: 'Liste des champs sensibles' })
  getSensitiveFields() {
    try {
      const sensitiveFields = this.sensitiveDataEncryptionService.getSensitiveFields();
      return {
        statusCode: HttpStatus.OK,
        data: sensitiveFields,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to get sensitive fields',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Obtient la liste des types de données sensibles configurés
   */
  @Get('sensitive-types')
  @ApiOperation({ summary: 'Obtient la liste des types de données sensibles configurés' })
  @ApiResponse({ status: 200, description: 'Liste des types de données sensibles' })
  getSensitiveTypes() {
    try {
      const sensitiveTypes = this.sensitiveDataEncryptionService.getSensitiveTypes();
      return {
        statusCode: HttpStatus.OK,
        data: sensitiveTypes,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to get sensitive types',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Ajoute un champ sensible à la configuration
   */
  @Post('add-sensitive-field')
  @Roles('admin')
  @ApiOperation({ summary: 'Ajoute un champ sensible à la configuration' })
  @ApiResponse({ status: 200, description: 'Champ sensible ajouté avec succès' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        fieldName: { type: 'string', description: 'Nom du champ sensible' },
      },
      required: ['fieldName'],
    },
  })
  addSensitiveField(@Body() body: { fieldName: string }) {
    try {
      this.sensitiveDataEncryptionService.addSensitiveField(body.fieldName);
      return {
        statusCode: HttpStatus.OK,
        message: `Sensitive field '${body.fieldName}' added successfully`,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to add sensitive field',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
