import { Controller, Get, Post, Param, Body, UseGuards, Logger, Query, ParseBoolPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CryptoMonitoringService, CryptoMetrics, CryptoAlert } from '../services/crypto-monitoring.service';

@ApiTags('crypto-monitoring')
@Controller('security/crypto-monitoring')
@UseGuards(JwtAuthGuard, RolesGuard)
export class CryptoMonitoringController {
  private readonly logger = new Logger(CryptoMonitoringController.name);

  constructor(private readonly cryptoMonitoringService: CryptoMonitoringService) {}

  @Get('metrics')
  @Roles('admin', 'security-officer')
  @ApiOperation({ summary: 'Get crypto metrics' })
  @ApiResponse({ status: 200, description: 'Return crypto metrics' })
  getMetrics(): CryptoMetrics {
    return this.cryptoMonitoringService.getMetrics();
  }

  @Get('alerts')
  @Roles('admin', 'security-officer')
  @ApiOperation({ summary: 'Get crypto alerts' })
  @ApiResponse({ status: 200, description: 'Return crypto alerts' })
  @ApiQuery({ name: 'includeAcknowledged', required: false, type: Boolean })
  getAlerts(@Query('includeAcknowledged', new ParseBoolPipe({ optional: true })) includeAcknowledged?: boolean): CryptoAlert[] {
    return this.cryptoMonitoringService.getAlerts(includeAcknowledged);
  }

  @Post('alerts/:id/acknowledge')
  @Roles('admin', 'security-officer')
  @ApiOperation({ summary: 'Acknowledge a crypto alert' })
  @ApiResponse({ status: 200, description: 'Alert acknowledged' })
  @ApiResponse({ status: 404, description: 'Alert not found' })
  @ApiParam({ name: 'id', description: 'Alert ID' })
  acknowledgeAlert(@Param('id') alertId: string): { success: boolean; message: string } {
    const success = this.cryptoMonitoringService.acknowledgeAlert(alertId);
    if (success) {
      return { success: true, message: 'Alert acknowledged successfully' };
    }
    return { success: false, message: 'Alert not found' };
  }

  @Post('alerts/:id/resolve')
  @Roles('admin', 'security-officer')
  @ApiOperation({ summary: 'Resolve a crypto alert' })
  @ApiResponse({ status: 200, description: 'Alert resolved' })
  @ApiResponse({ status: 404, description: 'Alert not found' })
  @ApiParam({ name: 'id', description: 'Alert ID' })
  resolveAlert(@Param('id') alertId: string): { success: boolean; message: string } {
    const success = this.cryptoMonitoringService.resolveAlert(alertId);
    if (success) {
      return { success: true, message: 'Alert resolved successfully' };
    }
    return { success: false, message: 'Alert not found' };
  }
}
