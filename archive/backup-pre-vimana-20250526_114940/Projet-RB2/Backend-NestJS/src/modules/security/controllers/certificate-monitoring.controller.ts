import { Controller, Get, Post, Param, Query, Body, HttpStatus, HttpException, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { CertificateMonitoringService, CertificateMetrics, CertificateInfo } from '../services/certificate-monitoring.service';
import { CertificateRevocationService, RevokedCertificate } from '../services/certificate-revocation.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';

/**
 * DTO pour la révocation de certificat
 */
class RevokeCertificateDto {
  reason: string;
}

/**
 * Contrôleur pour la surveillance et la révocation des certificats
 * Ce contrôleur expose des endpoints pour surveiller et révoquer les certificats mTLS
 */
@ApiTags('security/certificates/monitoring')
@Controller('security/certificates/monitoring')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class CertificateMonitoringController {
  constructor(
    private readonly certificateMonitoringService: CertificateMonitoringService,
    private readonly certificateRevocationService: CertificateRevocationService,
  ) {}

  /**
   * Obtient les métriques des certificats
   */
  @Get('metrics')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Obtient les métriques des certificats' })
  @ApiResponse({ status: 200, description: 'Métriques des certificats' })
  async getMetrics(): Promise<{
    statusCode: number;
    message: string;
    data: CertificateMetrics;
  }> {
    try {
      const metrics = this.certificateMonitoringService.getMetrics();
      return {
        statusCode: HttpStatus.OK,
        message: 'Certificate metrics retrieved successfully',
        data: metrics,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to retrieve certificate metrics',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Obtient les informations détaillées de tous les certificats
   */
  @Get('certificates')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Obtient les informations détaillées de tous les certificats' })
  @ApiResponse({ status: 200, description: 'Informations détaillées des certificats' })
  async getAllCertificatesInfo(): Promise<{
    statusCode: number;
    message: string;
    data: CertificateInfo[];
  }> {
    try {
      const certificates = this.certificateMonitoringService.getAllCertificatesInfo();
      return {
        statusCode: HttpStatus.OK,
        message: 'Certificate information retrieved successfully',
        data: certificates,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to retrieve certificate information',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Obtient les informations détaillées d'un certificat
   */
  @Get('certificates/:service')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Obtient les informations détaillées d\'un certificat' })
  @ApiResponse({ status: 200, description: 'Informations détaillées du certificat' })
  @ApiResponse({ status: 404, description: 'Certificat non trouvé' })
  async getCertificateInfo(@Param('service') service: string): Promise<{
    statusCode: number;
    message: string;
    data: CertificateInfo;
  }> {
    try {
      const certificate = this.certificateMonitoringService.getCertificateInfo(service);
      
      if (!certificate) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: `Certificate for service ${service} not found`,
          },
          HttpStatus.NOT_FOUND,
        );
      }
      
      return {
        statusCode: HttpStatus.OK,
        message: 'Certificate information retrieved successfully',
        data: certificate,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to retrieve certificate information',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Obtient la liste des certificats expirant bientôt
   */
  @Get('expiring')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Obtient la liste des certificats expirant bientôt' })
  @ApiResponse({ status: 200, description: 'Liste des certificats expirant bientôt' })
  @ApiQuery({ name: 'thresholdDays', required: false, type: Number, description: 'Nombre de jours avant expiration' })
  async getExpiringCertificates(@Query('thresholdDays') thresholdDays?: number): Promise<{
    statusCode: number;
    message: string;
    data: CertificateInfo[];
  }> {
    try {
      const certificates = this.certificateMonitoringService.getExpiringCertificates(thresholdDays);
      return {
        statusCode: HttpStatus.OK,
        message: 'Expiring certificates retrieved successfully',
        data: certificates,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to retrieve expiring certificates',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Obtient la liste des certificats expirés
   */
  @Get('expired')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Obtient la liste des certificats expirés' })
  @ApiResponse({ status: 200, description: 'Liste des certificats expirés' })
  async getExpiredCertificates(): Promise<{
    statusCode: number;
    message: string;
    data: CertificateInfo[];
  }> {
    try {
      const certificates = this.certificateMonitoringService.getExpiredCertificates();
      return {
        statusCode: HttpStatus.OK,
        message: 'Expired certificates retrieved successfully',
        data: certificates,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to retrieve expired certificates',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Obtient la liste des certificats révoqués
   */
  @Get('revoked')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Obtient la liste des certificats révoqués' })
  @ApiResponse({ status: 200, description: 'Liste des certificats révoqués' })
  async getRevokedCertificates(): Promise<{
    statusCode: number;
    message: string;
    data: CertificateInfo[];
  }> {
    try {
      const certificates = this.certificateMonitoringService.getRevokedCertificates();
      return {
        statusCode: HttpStatus.OK,
        message: 'Revoked certificates retrieved successfully',
        data: certificates,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to retrieve revoked certificates',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Révoque un certificat
   */
  @Post('revoke/:service')
  @Roles('admin')
  @ApiOperation({ summary: 'Révoque un certificat' })
  @ApiResponse({ status: 200, description: 'Certificat révoqué avec succès' })
  @ApiResponse({ status: 404, description: 'Certificat non trouvé' })
  async revokeCertificate(
    @Param('service') service: string,
    @Body() revokeCertificateDto: RevokeCertificateDto,
  ): Promise<{
    statusCode: number;
    message: string;
    success: boolean;
  }> {
    try {
      const certificate = this.certificateMonitoringService.getCertificateInfo(service);
      
      if (!certificate) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: `Certificate for service ${service} not found`,
          },
          HttpStatus.NOT_FOUND,
        );
      }
      
      const result = await this.certificateRevocationService.revokeCertificate(
        certificate.path,
        revokeCertificateDto.reason || 'unspecified',
      );
      
      // Mettre à jour les métriques après la révocation
      await this.certificateMonitoringService.scanCertificates();
      
      return {
        statusCode: HttpStatus.OK,
        message: result ? `Certificate for service ${service} revoked successfully` : `Failed to revoke certificate for service ${service}`,
        success: result,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to revoke certificate',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Obtient la liste de révocation de certificats (CRL)
   */
  @Get('crl')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Obtient la liste de révocation de certificats (CRL)' })
  @ApiResponse({ status: 200, description: 'Liste de révocation de certificats' })
  async getCRL(): Promise<{
    statusCode: number;
    message: string;
    data: RevokedCertificate[];
  }> {
    try {
      const revokedCertificates = this.certificateRevocationService.getAllRevokedCertificates();
      return {
        statusCode: HttpStatus.OK,
        message: 'Certificate revocation list retrieved successfully',
        data: revokedCertificates,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to retrieve certificate revocation list',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Génère une nouvelle liste de révocation de certificats (CRL)
   */
  @Post('crl/generate')
  @Roles('admin')
  @ApiOperation({ summary: 'Génère une nouvelle liste de révocation de certificats (CRL)' })
  @ApiResponse({ status: 200, description: 'Liste de révocation de certificats générée avec succès' })
  async generateCRL(): Promise<{
    statusCode: number;
    message: string;
    data: {
      crlPath: string;
    };
  }> {
    try {
      const crlPath = await this.certificateRevocationService.generateCRL();
      return {
        statusCode: HttpStatus.OK,
        message: 'Certificate revocation list generated successfully',
        data: {
          crlPath,
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to generate certificate revocation list',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Déclenche un scan des certificats
   */
  @Post('scan')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Déclenche un scan des certificats' })
  @ApiResponse({ status: 200, description: 'Scan des certificats déclenché avec succès' })
  async triggerCertificateScan(): Promise<{
    statusCode: number;
    message: string;
  }> {
    try {
      await this.certificateMonitoringService.scanCertificates();
      return {
        statusCode: HttpStatus.OK,
        message: 'Certificate scan triggered successfully',
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to trigger certificate scan',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
