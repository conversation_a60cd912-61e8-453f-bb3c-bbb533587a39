import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { QuantumResistantService } from '../services/quantum-resistant.service';

describe('QuantumResistantService', () => {
  let service: QuantumResistantService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuantumResistantService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue: any) => {
              const config = {
                'QUANTUM_RESISTANT_ENABLED': true,
                'QUANTUM_RESISTANT_ALGORITHM': 'hybrid',
                'QUANTUM_RESISTANT_KEY_SIZE': 3072,
                'QUANTUM_RESISTANT_HYBRID_CLASSICAL_ALGORITHM': 'rsa',
                'QUANTUM_RESISTANT_HYBRID_CLASSICAL_KEY_SIZE': 2048,
              };
              return config[key] !== undefined ? config[key] : defaultValue;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<QuantumResistantService>(QuantumResistantService);
    configService = module.get<ConfigService>(ConfigService);
    
    // Générer les clés avant les tests
    await service.generateKeys();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should encrypt and decrypt data correctly with hybrid mode', async () => {
    const testData = 'Test data for encryption';
    const encryptedData = await service.encrypt(testData);
    
    expect(encryptedData).toBeDefined();
    expect(encryptedData instanceof Buffer).toBeTruthy();
    
    const decryptedData = await service.decrypt(encryptedData);
    expect(decryptedData.toString()).toEqual(testData);
  });

  it('should handle different data types for encryption', async () => {
    // Test avec une chaîne
    const stringData = 'String test data';
    const encryptedString = await service.encrypt(stringData);
    const decryptedString = await service.decrypt(encryptedString);
    expect(decryptedString.toString()).toEqual(stringData);
    
    // Test avec un Buffer
    const bufferData = Buffer.from('Buffer test data');
    const encryptedBuffer = await service.encrypt(bufferData);
    const decryptedBuffer = await service.decrypt(encryptedBuffer);
    expect(decryptedBuffer.toString()).toEqual(bufferData.toString());
  });

  it('should throw an error when trying to decrypt with no key pair', async () => {
    // Simuler l'absence de clé
    jest.spyOn(service as any, 'keyPair', 'get').mockReturnValue(null);
    
    const testData = 'Test data';
    const encryptedData = Buffer.from('dummy encrypted data');
    
    await expect(service.decrypt(encryptedData)).rejects.toThrow('No key pair available for decryption');
  });

  it('should throw an error when quantum-resistant encryption is disabled', async () => {
    // Simuler la désactivation du chiffrement
    jest.spyOn(service, 'isEnabled').mockReturnValue(false);
    
    const testData = 'Test data';
    
    await expect(service.encrypt(testData)).rejects.toThrow('Quantum-resistant encryption is disabled');
    await expect(service.decrypt(Buffer.from('dummy'))).rejects.toThrow('Quantum-resistant encryption is disabled');
  });
});
