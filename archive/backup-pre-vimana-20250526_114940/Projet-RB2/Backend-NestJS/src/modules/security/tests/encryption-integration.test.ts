import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { EncryptionFacadeService } from '../services/encryption-facade.service';
import { EncryptionFactoryService } from '../services/encryption-factory.service';
import { EncryptionPolicyService, EncryptionType, SensitiveDataType, SensitivityLevel } from '../services/encryption-policy.service';
import { HomomorphicEncryptionService } from '../services/homomorphic-encryption.service';
import { QuantumResistantService } from '../services/quantum-resistant.service';
import { SensitiveDataEncryptionService } from '../services/sensitive-data-encryption.service';
import { KeyManagementService } from '../services/key-management.service';
import { VaultService } from '../services/vault.service';

describe('Encryption Integration Tests', () => {
  let facadeService: EncryptionFacadeService;
  let factoryService: EncryptionFactoryService;
  let policyService: EncryptionPolicyService;
  let homomorphicService: HomomorphicEncryptionService;
  let quantumResistantService: QuantumResistantService;
  let sensitiveDataService: SensitiveDataEncryptionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EncryptionFacadeService,
        EncryptionFactoryService,
        EncryptionPolicyService,
        HomomorphicEncryptionService,
        QuantumResistantService,
        SensitiveDataEncryptionService,
        KeyManagementService,
        VaultService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue: any) => {
              const config = {
                'HOMOMORPHIC_ENCRYPTION_ENABLED': true,
                'HOMOMORPHIC_ENCRYPTION_SCHEME': 'CKKS',
                'HOMOMORPHIC_ENCRYPTION_POLY_MODULUS_DEGREE': 8192,
                'HOMOMORPHIC_ENCRYPTION_SECURITY_LEVEL': 128,
                'QUANTUM_RESISTANT_ENABLED': true,
                'QUANTUM_RESISTANT_ALGORITHM': 'hybrid',
                'QUANTUM_RESISTANT_KEY_SIZE': 3072,
                'QUANTUM_RESISTANT_HYBRID_CLASSICAL_ALGORITHM': 'rsa',
                'QUANTUM_RESISTANT_HYBRID_CLASSICAL_KEY_SIZE': 2048,
                'SENSITIVE_DATA_ENCRYPTION_ENABLED': true,
                'SENSITIVE_DATA_ENCRYPTION_ALGORITHM': 'AES-256-GCM',
                'SENSITIVE_DATA_ENCRYPTION_KEY_SIZE': 256,
                'VAULT_ENABLED': false,
              };
              return config[key] !== undefined ? config[key] : defaultValue;
            }),
          },
        },
      ],
    }).compile();

    facadeService = module.get<EncryptionFacadeService>(EncryptionFacadeService);
    factoryService = module.get<EncryptionFactoryService>(EncryptionFactoryService);
    policyService = module.get<EncryptionPolicyService>(EncryptionPolicyService);
    homomorphicService = module.get<HomomorphicEncryptionService>(HomomorphicEncryptionService);
    quantumResistantService = module.get<QuantumResistantService>(QuantumResistantService);
    sensitiveDataService = module.get<SensitiveDataEncryptionService>(SensitiveDataEncryptionService);

    // Initialize services
    await homomorphicService.initialize();
    await quantumResistantService.generateKeys();
  });

  it('should be defined', () => {
    expect(facadeService).toBeDefined();
    expect(factoryService).toBeDefined();
    expect(policyService).toBeDefined();
    expect(homomorphicService).toBeDefined();
    expect(quantumResistantService).toBeDefined();
    expect(sensitiveDataService).toBeDefined();
  });

  describe('Encryption Factory Service', () => {
    it('should create appropriate encryption service based on context', () => {
      // Financial data should use quantum-resistant encryption
      const financialService = factoryService.createEncryptionService({
        dataType: SensitiveDataType.FINANCIAL,
      });
      expect(financialService.getEncryptionType()).toEqual('QUANTUM_RESISTANT');

      // Analytics data should use homomorphic encryption
      const analyticsService = factoryService.createEncryptionService({
        dataType: SensitiveDataType.ANALYTICS,
      });
      expect(analyticsService.getEncryptionType()).toEqual('HOMOMORPHIC');

      // Personal data should use standard encryption
      const personalService = factoryService.createEncryptionService({
        dataType: SensitiveDataType.PERSONAL,
      });
      expect(personalService.getEncryptionType()).toEqual('STANDARD');
    });

    it('should respect explicit requirements over policy', () => {
      // Even though personal data uses standard encryption by policy,
      // explicit requirement for homomorphic should override it
      const service = factoryService.createEncryptionService({
        dataType: SensitiveDataType.PERSONAL,
        requireHomomorphic: true,
      });
      expect(service.getEncryptionType()).toEqual('HOMOMORPHIC');
    });
  });

  describe('Encryption Facade Service', () => {
    it('should encrypt and decrypt data correctly with different services', async () => {
      const testData = 'Test data for encryption';

      // Encrypt with standard encryption (personal data)
      const encryptedStandard = await facadeService.encrypt(testData, {
        dataType: SensitiveDataType.PERSONAL,
      });
      expect(encryptedStandard).toBeDefined();
      expect(encryptedStandard instanceof Buffer).toBeTruthy();

      // Decrypt with standard encryption
      const decryptedStandard = await facadeService.decrypt(encryptedStandard, {
        dataType: SensitiveDataType.PERSONAL,
      });
      expect(decryptedStandard.toString()).toEqual(testData);

      // Encrypt with quantum-resistant encryption (financial data)
      const encryptedQuantum = await facadeService.encrypt(testData, {
        dataType: SensitiveDataType.FINANCIAL,
      });
      expect(encryptedQuantum).toBeDefined();
      expect(encryptedQuantum instanceof Buffer).toBeTruthy();

      // Decrypt with quantum-resistant encryption
      const decryptedQuantum = await facadeService.decrypt(encryptedQuantum, {
        dataType: SensitiveDataType.FINANCIAL,
      });
      expect(decryptedQuantum.toString()).toEqual(testData);
    });

    it('should perform homomorphic operations correctly', async () => {
      // Only run if homomorphic encryption is available
      if (!facadeService.isHomomorphicAvailable()) {
        console.log('Homomorphic encryption not available, skipping test');
        return;
      }

      const a = 10;
      const b = 5;

      // Encrypt values for homomorphic processing
      const encryptedA = await facadeService.encryptForHomomorphicProcessing(a);
      const encryptedB = await facadeService.encryptForHomomorphicProcessing(b);

      // Perform addition
      const encryptedSum = await facadeService.performHomomorphicOperation('add', {
        a: encryptedA,
        b: encryptedB,
      });
      const sum = await facadeService.decryptHomomorphicResult(encryptedSum);
      expect(Math.abs(sum - (a + b))).toBeLessThan(0.001);

      // Perform subtraction
      const encryptedDiff = await facadeService.performHomomorphicOperation('subtract', {
        a: encryptedA,
        b: encryptedB,
      });
      const diff = await facadeService.decryptHomomorphicResult(encryptedDiff);
      expect(Math.abs(diff - (a - b))).toBeLessThan(0.001);

      // Perform multiplication
      const encryptedProduct = await facadeService.performHomomorphicOperation('multiply', {
        a: encryptedA,
        b: encryptedB,
      });
      const product = await facadeService.decryptHomomorphicResult(encryptedProduct);
      expect(Math.abs(product - (a * b))).toBeLessThan(0.001);

      // Perform division
      const encryptedQuotient = await facadeService.performHomomorphicOperation('divide', {
        a: encryptedA,
        b: b, // Using scalar for division
      });
      const quotient = await facadeService.decryptHomomorphicResult(encryptedQuotient);
      expect(Math.abs(quotient - (a / b))).toBeLessThan(0.001);
    });

    it('should encrypt and decrypt objects correctly', async () => {
      const testObject = {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        creditCard: '1234-5678-9012-3456',
        address: '123 Main St',
      };

      const fieldsToEncrypt = ['name', 'email', 'creditCard'];

      // Encrypt object
      const encryptedObject = await facadeService.encryptObject(
        testObject,
        fieldsToEncrypt,
        { dataType: SensitiveDataType.PERSONAL },
      );

      // Check that specified fields are encrypted
      expect(encryptedObject.id).toEqual(testObject.id); // Not encrypted
      expect(encryptedObject.address).toEqual(testObject.address); // Not encrypted
      expect(encryptedObject.name).not.toEqual(testObject.name); // Encrypted
      expect(encryptedObject.email).not.toEqual(testObject.email); // Encrypted
      expect(encryptedObject.creditCard).not.toEqual(testObject.creditCard); // Encrypted

      // Decrypt object
      const decryptedObject = await facadeService.decryptObject(
        encryptedObject,
        fieldsToEncrypt,
        { dataType: SensitiveDataType.PERSONAL },
      );

      // Check that decrypted object matches original
      expect(decryptedObject).toEqual(testObject);
    });
  });

  describe('Policy Service', () => {
    it('should return correct policy for data type', () => {
      const financialPolicy = policyService.getPolicyForDataType(SensitiveDataType.FINANCIAL);
      expect(financialPolicy.encryptionType).toEqual(EncryptionType.HYBRID);
      expect(financialPolicy.requireQuantumResistant).toBeTruthy();

      const analyticsPolicy = policyService.getPolicyForDataType(SensitiveDataType.ANALYTICS);
      expect(analyticsPolicy.encryptionType).toEqual(EncryptionType.HOMOMORPHIC);
      expect(analyticsPolicy.allowHomomorphic).toBeTruthy();

      const personalPolicy = policyService.getPolicyForDataType(SensitiveDataType.PERSONAL);
      expect(personalPolicy.encryptionType).toEqual(EncryptionType.STANDARD);
    });

    it('should return correct policy for sensitivity level', () => {
      const lowPolicy = policyService.getPolicyForSensitivityLevel(SensitivityLevel.LOW);
      expect(lowPolicy.encryptionType).toEqual(EncryptionType.STANDARD);

      const highPolicy = policyService.getPolicyForSensitivityLevel(SensitivityLevel.HIGH);
      expect(highPolicy.encryptionType).toEqual(EncryptionType.HYBRID);
      expect(highPolicy.requireQuantumResistant).toBeTruthy();

      const criticalPolicy = policyService.getPolicyForSensitivityLevel(SensitivityLevel.CRITICAL);
      expect(criticalPolicy.encryptionType).toEqual(EncryptionType.QUANTUM_RESISTANT);
      expect(criticalPolicy.requireQuantumResistant).toBeTruthy();
    });

    it('should correctly determine if homomorphic encryption is allowed', () => {
      expect(policyService.isHomomorphicAllowed({ dataType: SensitiveDataType.ANALYTICS })).toBeTruthy();
      expect(policyService.isHomomorphicAllowed({ dataType: SensitiveDataType.FINANCIAL })).toBeTruthy();
      expect(policyService.isHomomorphicAllowed({ dataType: SensitiveDataType.PERSONAL })).toBeFalsy();
    });

    it('should correctly determine if quantum-resistant encryption is required', () => {
      expect(policyService.isQuantumResistantRequired({ dataType: SensitiveDataType.FINANCIAL })).toBeTruthy();
      expect(policyService.isQuantumResistantRequired({ dataType: SensitiveDataType.AUTHENTICATION })).toBeTruthy();
      expect(policyService.isQuantumResistantRequired({ dataType: SensitiveDataType.ANALYTICS })).toBeFalsy();
      expect(policyService.isQuantumResistantRequired({ dataType: SensitiveDataType.PERSONAL })).toBeFalsy();
    });
  });
});
