import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { AnomalyDetectionService } from './services/anomaly-detection.service';
import { RateLimiterService } from './services/rate-limiter.service';
import { ContentSecurityService } from './services/content-security.service';
import { SecurityMonitoringService } from './services/security-monitoring.service';
import { SecurityEventService } from './services/security-event.service';
import { FileSecurityService } from './services/file-security.service';
import { SecurityEventSeverity } from './dto/create-security-event.dto';
import { SecurityScanResult } from './interfaces/security-scan-result.interface';
import { SecurityVulnerability } from './interfaces/security-vulnerability.interface';
import { SecurityScanType } from './interfaces/security-scan-type.enum';
import { SecurityVulnerabilityLevel } from './interfaces/security-vulnerability-level.enum';

@Injectable()
export class SecurityService {
  private readonly logger = new Logger(SecurityService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly anomalyDetectionService: AnomalyDetectionService,
    private readonly rateLimiterService: RateLimiterService,
    private readonly contentSecurityService: ContentSecurityService,
    private readonly securityMonitoringService: SecurityMonitoringService,
    private readonly securityEventService: SecurityEventService,
    private readonly fileSecurityService: FileSecurityService,
  ) {}

  // Health check
  async getHealthStatus() {
    return { status: 'ok', timestamp: new Date() };
  }

  // Dashboard
  async getSecurityDashboardData(timeframe: 'day' | 'week' | 'month' = 'day') {
    try {
      return await this.securityMonitoringService.getSecurityDashboardData(timeframe);
    } catch (error) {
      this.logger.error(`Error getting security dashboard data: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Events
  async getSecurityEvents(page = 1, limit = 10, type?: string, severity?: string) {
    try {
      const skip = (page - 1) * limit;

      // Build the where clause based on filters
      const where: any = {};
      if (type) where.type = type;
      if (severity) where.severity = severity;

      // Get events from database
      const [events, total] = await Promise.all([
        this.prisma.securityEvent.findMany({
          skip,
          take: limit,
          where,
          orderBy: {
            timestamp: 'desc',
          },
        }),
        this.prisma.securityEvent.count({ where }),
      ]);

      return {
        events,
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error(`Error getting security events: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Alerts
  async getSecurityAlerts(
    page = 1,
    limit = 10,
    status?: 'OPEN' | 'ACKNOWLEDGED' | 'RESOLVED',
    priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  ) {
    try {
      const skip = (page - 1) * limit;

      // Build the where clause based on filters
      const where: any = {};
      if (status) where.status = status;
      if (priority) where.priority = priority;

      // Get alerts from database
      const [alerts, total] = await Promise.all([
        this.prisma.securityAlert.findMany({
          skip,
          take: limit,
          where,
          orderBy: {
            createdAt: 'desc',
          },
          include: {
            acknowledgedBy: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
            resolvedBy: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        }),
        this.prisma.securityAlert.count({ where }),
      ]);

      return {
        alerts,
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error(`Error getting security alerts: ${error.message}`, error.stack);
      throw error;
    }
  }

  async acknowledgeAlert(alertId: string, userId: string) {
    try {
      // Update the alert status
      const alert = await this.prisma.securityAlert.update({
        where: { id: alertId },
        data: {
          status: 'ACKNOWLEDGED',
          acknowledgedAt: new Date(),
          acknowledgedById: userId,
        },
        include: {
          acknowledgedBy: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      // Log the event
      await this.securityEventService.logSecurityEvent({
        eventType: 'ALERT_ACKNOWLEDGED',
        severity: SecurityEventSeverity.INFO,
        source: 'SECURITY_SERVICE',
        details: {
          alertId,
          userId,
          alertType: alert.type,
        },
      });

      return alert;
    } catch (error) {
      this.logger.error(`Error acknowledging alert: ${error.message}`, error.stack);
      throw error;
    }
  }

  async resolveAlert(alertId: string, resolution: string, userId: string) {
    try {
      // Update the alert status
      const alert = await this.prisma.securityAlert.update({
        where: { id: alertId },
        data: {
          status: 'RESOLVED',
          resolvedAt: new Date(),
          resolvedById: userId,
          resolution,
        },
        include: {
          resolvedBy: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      // Log the event
      await this.securityEventService.logSecurityEvent({
        eventType: 'ALERT_RESOLVED',
        severity: SecurityEventSeverity.INFO,
        source: 'SECURITY_SERVICE',
        details: {
          alertId,
          userId,
          alertType: alert.type,
          resolution,
        },
      });

      return alert;
    } catch (error) {
      this.logger.error(`Error resolving alert: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Training simulations
  async getTrainingSimulations(userId: string) {
    try {
      // Get all available simulations
      const allSimulations = await this.prisma.securityTrainingSimulation.findMany({
        where: {
          isActive: true,
        },
        orderBy: {
          difficulty: 'asc',
        },
      });

      // Get user's completed simulations
      const userCompletions = await this.prisma.securityTrainingCompletion.findMany({
        where: {
          userId,
        },
        include: {
          simulation: true,
        },
      });

      // Map user progress to simulations
      const simulations = allSimulations.map(simulation => {
        const completion = userCompletions.find(c => c.simulationId === simulation.id);

        return {
          ...simulation,
          completed: !!completion,
          completedAt: completion?.completedAt || null,
          score: completion?.score || null,
          attempts: completion?.attempts || 0,
        };
      });

      return { userId, simulations };
    } catch (error) {
      this.logger.error(`Error getting training simulations: ${error.message}`, error.stack);
      throw error;
    }
  }

  async startTrainingSimulation(userId: string, simulationId: string) {
    try {
      // Check if simulation exists
      const simulation = await this.prisma.securityTrainingSimulation.findUnique({
        where: { id: simulationId },
      });

      if (!simulation) {
        throw new Error(`Simulation with ID ${simulationId} not found`);
      }

      if (!simulation.isActive) {
        throw new Error(`Simulation with ID ${simulationId} is not active`);
      }

      // Create or update user progress
      const userProgress = await this.prisma.securityTrainingProgress.upsert({
        where: {
          userId_simulationId: {
            userId,
            simulationId,
          },
        },
        update: {
          startedAt: new Date(),
          attempts: {
            increment: 1,
          },
        },
        create: {
          userId,
          simulationId,
          startedAt: new Date(),
          attempts: 1,
        },
      });

      // Log the event
      await this.securityEventService.logSecurityEvent({
        eventType: 'TRAINING_SIMULATION_STARTED',
        severity: SecurityEventSeverity.INFO,
        source: 'SECURITY_TRAINING',
        details: {
          userId,
          simulationId,
          simulationType: simulation.type,
        },
      });

      return {
        userId,
        simulationId,
        started: true,
        simulation,
        progress: userProgress,
      };
    } catch (error) {
      this.logger.error(`Error starting training simulation: ${error.message}`, error.stack);
      throw error;
    }
  }

  async completeTrainingSimulation(userId: string, simulationId: string, response: any) {
    try {
      // Check if simulation exists
      const simulation = await this.prisma.securityTrainingSimulation.findUnique({
        where: { id: simulationId },
      });

      if (!simulation) {
        throw new Error(`Simulation with ID ${simulationId} not found`);
      }

      // Get user progress
      const progress = await this.prisma.securityTrainingProgress.findUnique({
        where: {
          userId_simulationId: {
            userId,
            simulationId,
          },
        },
      });

      if (!progress) {
        throw new Error(`User has not started simulation with ID ${simulationId}`);
      }

      // Calculate score based on response and simulation type
      const score = this.calculateSimulationScore(simulation, response);
      const passed = score >= simulation.passingScore;

      // Update user progress
      await this.prisma.securityTrainingProgress.update({
        where: {
          userId_simulationId: {
            userId,
            simulationId,
          },
        },
        data: {
          completedAt: new Date(),
          score,
          passed,
        },
      });

      // If passed, create or update completion record
      if (passed) {
        await this.prisma.securityTrainingCompletion.upsert({
          where: {
            userId_simulationId: {
              userId,
              simulationId,
            },
          },
          update: {
            completedAt: new Date(),
            score,
            attempts: progress.attempts,
          },
          create: {
            userId,
            simulationId,
            completedAt: new Date(),
            score,
            attempts: progress.attempts,
          },
        });
      }

      // Log the event
      await this.securityEventService.logSecurityEvent({
        eventType: passed ? 'TRAINING_SIMULATION_PASSED' : 'TRAINING_SIMULATION_FAILED',
        severity: SecurityEventSeverity.INFO,
        source: 'SECURITY_TRAINING',
        details: {
          userId,
          simulationId,
          simulationType: simulation.type,
          score,
          passed,
        },
      });

      return {
        userId,
        simulationId,
        completed: true,
        score,
        passed,
        feedback: this.getSimulationFeedback(simulation, score, passed),
      };
    } catch (error) {
      this.logger.error(`Error completing training simulation: ${error.message}`, error.stack);
      throw error;
    }
  }

  private calculateSimulationScore(simulation: any, response: any): number {
    // This is a simplified implementation
    // In a real application, this would be more complex based on simulation type
    switch (simulation.type) {
      case 'PHISHING':
        return this.calculatePhishingScore(simulation, response);
      case 'PASSWORD':
        return this.calculatePasswordScore(simulation, response);
      case 'DATA_HANDLING':
        return this.calculateDataHandlingScore(simulation, response);
      default:
        return 0;
    }
  }

  private calculatePhishingScore(simulation: any, response: any): number {
    // Example implementation for phishing simulation
    const correctAnswer = simulation.correctAnswer;
    const userAnswer = response.answer;

    // Basic scoring: 100 if correct, 0 if incorrect
    return correctAnswer === userAnswer ? 100 : 0;
  }

  private calculatePasswordScore(simulation: any, response: any): number {
    // Example implementation for password simulation
    const password = response.password;

    // Score based on password strength
    let score = 0;

    // Length check
    if (password.length >= 12) score += 25;
    else if (password.length >= 8) score += 15;

    // Complexity checks
    if (/[A-Z]/.test(password)) score += 25; // Uppercase
    if (/[a-z]/.test(password)) score += 15; // Lowercase
    if (/[0-9]/.test(password)) score += 20; // Numbers
    if (/[^A-Za-z0-9]/.test(password)) score += 15; // Special characters

    return Math.min(100, score);
  }

  private calculateDataHandlingScore(simulation: any, response: any): number {
    // Example implementation for data handling simulation
    const correctAnswers = simulation.correctAnswers;
    const userAnswers = response.answers;

    // Calculate percentage of correct answers
    let correctCount = 0;
    for (const key in correctAnswers) {
      if (userAnswers[key] === correctAnswers[key]) {
        correctCount++;
      }
    }

    return Math.round((correctCount / Object.keys(correctAnswers).length) * 100);
  }

  private getSimulationFeedback(simulation: any, score: number, passed: boolean): string {
    // Generate feedback based on simulation type and score
    if (passed) {
      return `Félicitations ! Vous avez réussi la simulation "${simulation.title}" avec un score de ${score}%.`;
    } else {
      return `Vous n'avez pas réussi la simulation "${simulation.title}". Votre score est de ${score}%. Le score minimum requis est de ${simulation.passingScore}%. Veuillez réessayer.`;
    }
  }

  // User security stats
  async getUserSecurityStats(userId: string) {
    try {
      // Get user's training completions
      const completions = await this.prisma.securityTrainingCompletion.findMany({
        where: {
          userId,
        },
        include: {
          simulation: true,
        },
      });

      // Get user's security events
      const securityEvents = await this.prisma.securityEvent.findMany({
        where: {
          userId,
        },
        orderBy: {
          timestamp: 'desc',
        },
        take: 10,
      });

      // Calculate stats
      const totalSimulations = await this.prisma.securityTrainingSimulation.count({
        where: {
          isActive: true,
        },
      });

      const completedSimulations = completions.length;
      const completionRate = totalSimulations > 0 ? (completedSimulations / totalSimulations) * 100 : 0;

      const averageScore = completions.length > 0
        ? completions.reduce((sum, completion) => sum + completion.score, 0) / completions.length
        : 0;

      // Get security risk score
      const riskScore = await this.calculateUserRiskScore(userId);

      return {
        userId,
        stats: {
          completedSimulations,
          totalSimulations,
          completionRate,
          averageScore,
          riskScore,
          securityEvents,
          lastTrainingDate: completions.length > 0
            ? Math.max(...completions.map(c => c.completedAt.getTime()))
            : null,
        },
      };
    } catch (error) {
      this.logger.error(`Error getting user security stats: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async calculateUserRiskScore(userId: string): Promise<number> {
    // This is a simplified implementation
    // In a real application, this would be more complex

    // Get user's security events in the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [
      failedLogins,
      suspiciousActivities,
      completedTrainings,
      passwordChanges,
    ] = await Promise.all([
      // Failed login attempts
      this.prisma.securityEvent.count({
        where: {
          userId,
          type: 'FAILED_LOGIN',
          timestamp: {
            gte: thirtyDaysAgo,
          },
        },
      }),
      // Suspicious activities
      this.prisma.securityEvent.count({
        where: {
          userId,
          severity: {
            in: ['WARNING', 'ERROR', 'CRITICAL'],
          },
          timestamp: {
            gte: thirtyDaysAgo,
          },
        },
      }),
      // Completed security trainings
      this.prisma.securityTrainingCompletion.count({
        where: {
          userId,
          completedAt: {
            gte: thirtyDaysAgo,
          },
        },
      }),
      // Password changes
      this.prisma.securityEvent.count({
        where: {
          userId,
          type: 'PASSWORD_CHANGED',
          timestamp: {
            gte: thirtyDaysAgo,
          },
        },
      }),
    ]);

    // Calculate risk score (lower is better)
    let riskScore = 50; // Base score

    // Increase risk for negative factors
    riskScore += failedLogins * 5;
    riskScore += suspiciousActivities * 10;

    // Decrease risk for positive factors
    riskScore -= completedTrainings * 15;
    riskScore -= passwordChanges * 10;

    // Ensure score is between 0 and 100
    return Math.max(0, Math.min(100, riskScore));
  }

  async scanForVulnerabilities(type: SecurityScanType): Promise<SecurityScanResult> {
    this.logger.log(`Démarrage d'un scan de sécurité de type: ${type}`);

    const startTime = Date.now();
    let vulnerabilities: SecurityVulnerability[] = [];

    try {
      switch (type) {
        case SecurityScanType.FULL:
          vulnerabilities = await this.runFullScan();
          break;
        case SecurityScanType.QUICK:
          vulnerabilities = await this.runQuickScan();
          break;
        case SecurityScanType.CONTENT:
          vulnerabilities = await this.contentSecurityService.scanForVulnerabilities();
          break;
        case SecurityScanType.FILE:
          vulnerabilities = await this.fileSecurityService.scanForVulnerabilities();
          break;
        default:
          throw new Error(`Type de scan non pris en charge: ${type}`);
      }

      // Enregistrer les résultats du scan
      const scanResult: SecurityScanResult = {
        type,
        startTime: new Date(startTime),
        endTime: new Date(),
        duration: Date.now() - startTime,
        vulnerabilities,
        vulnerabilityCount: vulnerabilities.length,
        highSeverityCount: vulnerabilities.filter(v => v.level === SecurityVulnerabilityLevel.HIGH).length,
        mediumSeverityCount: vulnerabilities.filter(v => v.level === SecurityVulnerabilityLevel.MEDIUM).length,
        lowSeverityCount: vulnerabilities.filter(v => v.level === SecurityVulnerabilityLevel.LOW).length,
        status: 'COMPLETED',
      };

      // Enregistrer le scan dans la base de données
      await this.prisma.securityScan.create({
        data: {
          type,
          startTime: new Date(startTime),
          endTime: new Date(),
          duration: scanResult.duration,
          vulnerabilityCount: scanResult.vulnerabilityCount,
          highSeverityCount: scanResult.highSeverityCount,
          mediumSeverityCount: scanResult.mediumSeverityCount,
          lowSeverityCount: scanResult.lowSeverityCount,
          status: scanResult.status,
          result: scanResult,
        },
      });

      // Enregistrer un événement de sécurité pour chaque vulnérabilité critique
      for (const vulnerability of vulnerabilities.filter(v => v.level === SecurityVulnerabilityLevel.HIGH)) {
        await this.securityEventService.logSecurityEvent({
          eventType: 'VULNERABILITY_DETECTED',
          severity: SecurityEventSeverity.ERROR,
          source: 'SECURITY_SCAN',
          details: {
            description: vulnerability.description,
            vulnerability
          }
        });
      }

      this.logger.log(`Scan de sécurité terminé. ${vulnerabilities.length} vulnérabilités détectées.`);
      return scanResult;
    } catch (error) {
      this.logger.error(`Erreur lors du scan de sécurité: ${error.message}`);

      // Enregistrer le scan échoué dans la base de données
      await this.prisma.securityScan.create({
        data: {
          type,
          startTime: new Date(startTime),
          endTime: new Date(),
          duration: Date.now() - startTime,
          status: 'FAILED',
          error: error.message,
        },
      });

      // Enregistrer un événement de sécurité pour l'échec du scan
      await this.securityEventService.logSecurityEvent({
        eventType: 'SECURITY_SCAN_FAILED',
        severity: SecurityEventSeverity.WARNING,
        source: 'SECURITY_SCAN',
        details: {
          description: `Le scan de sécurité de type ${type} a échoué: ${error.message}`,
          error: error.message,
          scanType: type
        }
      });

      throw error;
    }
  }

  private async runFullScan(): Promise<SecurityVulnerability[]> {
    this.logger.log('Exécution d\'un scan de sécurité complet');

    // Exécuter tous les types de scan
    const [contentVulnerabilities, fileVulnerabilities] = await Promise.all([
      this.contentSecurityService.scanForVulnerabilities(),
      this.fileSecurityService.scanForVulnerabilities(),
    ]);

    return [...contentVulnerabilities, ...fileVulnerabilities];
  }

  private async runQuickScan(): Promise<SecurityVulnerability[]> {
    this.logger.log('Exécution d\'un scan de sécurité rapide');

    // Exécuter un scan limité
    const contentVulnerabilities = await this.contentSecurityService.scanForVulnerabilities(true);

    return contentVulnerabilities;
  }

  async getSecurityMetrics() {
    const [
      blockedIPs,
      securityEvents,
      latestScans,
      activeThreats,
    ] = await Promise.all([
      this.anomalyDetectionService.getBlockedIPs(),
      this.securityEventService.getRecentEvents(),
      this.getLatestScans(),
      this.securityMonitoringService.getActiveThreats(),
    ]);

    return {
      blockedIPs: blockedIPs.length,
      securityEvents: securityEvents.length,
      latestScans,
      activeThreats: activeThreats.length,
      threatLevel: this.calculateThreatLevel(blockedIPs.length, securityEvents.length, activeThreats.length),
    };
  }

  private async getLatestScans() {
    return this.prisma.securityScan.findMany({
      take: 5,
      orderBy: {
        startTime: 'desc',
      },
    });
  }

  private calculateThreatLevel(blockedIPs: number, securityEvents: number, activeThreats: number): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    const score = blockedIPs * 0.2 + securityEvents * 0.3 + activeThreats * 0.5;

    if (score < 5) return 'LOW';
    if (score < 10) return 'MEDIUM';
    if (score < 20) return 'HIGH';
    return 'CRITICAL';
  }
}
