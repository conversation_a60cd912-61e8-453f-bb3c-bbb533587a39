import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { PrismaModule } from '../../prisma/prisma.module';
import { KeyManagementService } from './services/key-management.service';
import { VaultService } from './services/vault.service';
import { EndToEndEncryptionService } from './services/end-to-end-encryption.service';
import { HomomorphicEncryptionService } from './services/homomorphic-encryption.service';
import { QuantumResistantService } from './services/quantum-resistant.service';
import { MicroserviceSecurityService } from './services/microservice-security.service';
import { CertificateManagementService } from './services/certificate-management.service';
import { CertificateRotationService } from './services/certificate-rotation.service';
import { CertificateRevocationService } from './services/certificate-revocation.service';
import { CertificateMonitoringService } from './services/certificate-monitoring.service';
import { AdvancedHomomorphicService } from './services/advanced-homomorphic.service';
import { CryptoLoggingService } from './services/crypto-logging.service';
import { CryptoPerformanceService } from './services/crypto-performance.service';
import { CryptoTestingService } from './services/crypto-testing.service';
import { CryptoMonitoringService } from './services/crypto-monitoring.service';
import { CryptoProfilingService } from './services/crypto-profiling.service';
import { CryptoCacheService } from './services/crypto-cache.service';
import { CryptoOptimizationService } from './services/crypto-optimization.service';
import { SensitiveDataEncryptionService } from './services/sensitive-data-encryption.service';
import { EncryptionAuditService } from './services/encryption-audit.service';
import { TokenizationService } from './services/tokenization.service';
import { EncryptionPolicyService } from './services/encryption-policy.service';
import { EncryptionFactoryService } from './services/encryption-factory.service';
import { EncryptionFacadeService } from './services/encryption-facade.service';
import { DefaultEncryptionService } from './services/default-encryption.service';
import { SecretManagerService } from './services/secret-manager.service';
import { ApplicationSecretsService } from './services/application-secrets.service';
import { EncryptionAuditController } from './controllers/encryption-audit.controller';
import { TokenizationController } from './controllers/tokenization.controller';
import { CryptoMonitoringController } from './controllers/crypto-monitoring.controller';
import { CryptoOptimizationController } from './controllers/crypto-optimization.controller';
import { EncryptionPolicyController } from './controllers/encryption-policy.controller';
import { SecretManagerController } from './controllers/secret-manager.controller';
import { ApplicationSecretsController } from './controllers/application-secrets.controller';
import { IEncryptionService } from './interfaces/encryption-service.interface';
import { IHomomorphicEncryptionService } from './interfaces/homomorphic-encryption-service.interface';
import { IQuantumResistantService } from './interfaces/quantum-resistant-service.interface';
import { ISensitiveDataEncryptionService } from './interfaces/sensitive-data-encryption-service.interface';

/**
 * Module pour les services de chiffrement
 * Ce module fournit des services pour différentes méthodes de chiffrement :
 * - Gestion des clés (KeyManagementService)
 * - Intégration avec HashiCorp Vault (VaultService)
 * - Chiffrement de bout en bout (EndToEndEncryptionService)
 * - Chiffrement homomorphique (HomomorphicEncryptionService) - Permet d'effectuer des calculs sur des données chiffrées sans les déchiffrer
 * - Chiffrement homomorphique avancé (AdvancedHomomorphicService)
 * - Chiffrement résistant aux ordinateurs quantiques (QuantumResistantService)
 * - Sécurité des communications entre microservices (MicroserviceSecurityService)
 * - Gestion des certificats pour mTLS (CertificateManagementService)
 * - Rotation automatique des certificats (CertificateRotationService)
 * - Gestion des listes de révocation de certificats (CertificateRevocationService)
 * - Surveillance des certificats (CertificateMonitoringService)
 * - Journalisation sécurisée des opérations cryptographiques (CryptoLoggingService)
 * - Optimisation des performances cryptographiques (CryptoPerformanceService)
 * - Tests automatisés des services de chiffrement (CryptoTestingService)
 * - Surveillance et alertes des opérations cryptographiques (CryptoMonitoringService)
 * - Tokenisation des données sensibles (TokenizationService) - Remplace les données sensibles par des tokens non sensibles
 */
@Module({
  imports: [ConfigModule, ScheduleModule.forRoot(), EventEmitterModule.forRoot(), PrismaModule],
  controllers: [
    EncryptionAuditController,
    TokenizationController,
    CryptoMonitoringController,
    CryptoOptimizationController,
    EncryptionPolicyController,
    SecretManagerController,
    ApplicationSecretsController
  ],
  providers: [
    VaultService,
    KeyManagementService,
    EndToEndEncryptionService,
    HomomorphicEncryptionService,
    QuantumResistantService,
    MicroserviceSecurityService,
    CertificateManagementService,
    CertificateRotationService,
    CertificateRevocationService,
    CertificateMonitoringService,
    AdvancedHomomorphicService,
    CryptoLoggingService,
    CryptoPerformanceService,
    CryptoTestingService,
    CryptoMonitoringService,
    CryptoProfilingService,
    CryptoCacheService,
    CryptoOptimizationService,
    SensitiveDataEncryptionService,
    EncryptionAuditService,
    TokenizationService,
    EncryptionPolicyService,
    EncryptionFactoryService,
    EncryptionFacadeService,
    DefaultEncryptionService,
    SecretManagerService,
    ApplicationSecretsService,
    // Fournir les services d'interface avec l'implémentation par défaut
    {
      provide: 'IEncryptionService',
      useClass: DefaultEncryptionService,
    },
    {
      provide: 'IHomomorphicEncryptionService',
      useClass: DefaultEncryptionService,
    },
    {
      provide: 'IQuantumResistantService',
      useClass: DefaultEncryptionService,
    },
    {
      provide: 'ISensitiveDataEncryptionService',
      useClass: DefaultEncryptionService,
    }
  ],
  exports: [
    VaultService,
    KeyManagementService,
    EndToEndEncryptionService,
    HomomorphicEncryptionService,
    QuantumResistantService,
    MicroserviceSecurityService,
    CertificateManagementService,
    CertificateRotationService,
    CertificateRevocationService,
    CertificateMonitoringService,
    AdvancedHomomorphicService,
    CryptoLoggingService,
    CryptoPerformanceService,
    CryptoTestingService,
    CryptoMonitoringService,
    CryptoProfilingService,
    CryptoCacheService,
    CryptoOptimizationService,
    SensitiveDataEncryptionService,
    EncryptionAuditService,
    TokenizationService,
    EncryptionPolicyService,
    EncryptionFactoryService,
    EncryptionFacadeService,
    DefaultEncryptionService,
    SecretManagerService,
    ApplicationSecretsService,
    // Exporter les services d'interface
    'IEncryptionService',
    'IHomomorphicEncryptionService',
    'IQuantumResistantService',
    'ISensitiveDataEncryptionService'
  ]
})
export class EncryptionModule {}
