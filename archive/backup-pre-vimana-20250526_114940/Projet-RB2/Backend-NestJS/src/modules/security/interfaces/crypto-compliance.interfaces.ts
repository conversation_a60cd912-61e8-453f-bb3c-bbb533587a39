/**
 * Interfaces pour le service de conformité cryptographique
 */

/**
 * Standards de conformité cryptographique
 */
export enum CryptoComplianceStandard {
  NIST_800_53 = 'NIST_800_53',
  NIST_800_57 = 'NIST_800_57',
  NIST_800_131A = 'NIST_800_131A',
  FIPS_140_2 = 'FIPS_140_2',
  FIPS_140_3 = 'FIPS_140_3',
  PCI_DSS = 'PCI_DSS',
  HIPAA = 'HIPAA',
  GDPR = 'GDPR',
  ISO_27001 = 'ISO_27001',
  SOC2 = 'SOC2',
  CCPA = 'CCPA'
}

/**
 * Niveaux de sévérité pour les problèmes de conformité
 */
export enum ComplianceSeverity {
  CRITICAL = 'CRITICAL',
  HIGH = 'HIGH',
  MEDIUM = 'MEDIUM',
  LOW = 'LOW',
  INFO = 'INFO'
}

/**
 * Statut de conformité
 */
export enum ComplianceStatus {
  COMPLIANT = 'COMPLIANT',
  PARTIALLY_COMPLIANT = 'PARTIALLY_COMPLIANT',
  NON_COMPLIANT = 'NON_COMPLIANT',
  UNKNOWN = 'UNKNOWN'
}

/**
 * Problème de conformité
 */
export interface ComplianceIssue {
  id?: string;
  standard: CryptoComplianceStandard;
  controlId: string;
  description: string;
  severity: ComplianceSeverity;
  affectedComponent: string;
  remediation: string;
  details?: Record<string, any>;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Contrôle de conformité
 */
export interface ComplianceControl {
  id: string;
  standard: CryptoComplianceStandard;
  category: string;
  title: string;
  description: string;
  requirements: string[];
  validationMethod: 'AUTOMATED' | 'MANUAL' | 'HYBRID';
  references?: string[];
}

/**
 * Résultat de vérification d'un contrôle
 */
export interface ControlCheckResult {
  controlId: string;
  status: ComplianceStatus;
  issues: ComplianceIssue[];
  details?: Record<string, any>;
  timestamp: Date;
}

/**
 * Options pour la vérification de conformité
 */
export interface ComplianceCheckOptions {
  standards?: CryptoComplianceStandard[];
  components?: string[];
  includeDetails?: boolean;
  checkDependencies?: boolean;
}

/**
 * Rapport de conformité
 */
export interface ComplianceReport {
  id?: string;
  timestamp: Date;
  standards: CryptoComplianceStandard[];
  overallStatus: ComplianceStatus;
  controlResults: ControlCheckResult[];
  summary: {
    totalControls: number;
    compliantControls: number;
    partiallyCompliantControls: number;
    nonCompliantControls: number;
    criticalIssues: number;
    highIssues: number;
    mediumIssues: number;
    lowIssues: number;
  };
  recommendations: string[];
  generatedBy: string;
  metadata?: Record<string, any>;
}

/**
 * Définition d'un standard de conformité
 */
export interface ComplianceStandardDefinition {
  id: CryptoComplianceStandard;
  name: string;
  description: string;
  version: string;
  website: string;
  controls: ComplianceControl[];
  categories: {
    id: string;
    name: string;
    description: string;
  }[];
}

/**
 * Mappage entre les standards de conformité
 */
export interface ComplianceMapping {
  sourceStandard: CryptoComplianceStandard;
  sourceControlId: string;
  targetStandard: CryptoComplianceStandard;
  targetControlId: string;
  mappingType: 'DIRECT' | 'PARTIAL' | 'RELATED';
  notes?: string;
}

/**
 * Historique de conformité
 */
export interface ComplianceHistory {
  id?: string;
  reportId: string;
  timestamp: Date;
  standards: CryptoComplianceStandard[];
  overallStatus: ComplianceStatus;
  summary: {
    totalControls: number;
    compliantControls: number;
    partiallyCompliantControls: number;
    nonCompliantControls: number;
    criticalIssues: number;
    highIssues: number;
    mediumIssues: number;
    lowIssues: number;
  };
}

/**
 * Plan de remédiation
 */
export interface RemediationPlan {
  id?: string;
  issueId: string;
  description: string;
  steps: {
    id: string;
    description: string;
    status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
    assignedTo?: string;
    dueDate?: Date;
    completedDate?: Date;
    notes?: string;
  }[];
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  priority: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  assignedTo?: string;
  dueDate?: Date;
  completedDate?: Date;
}
