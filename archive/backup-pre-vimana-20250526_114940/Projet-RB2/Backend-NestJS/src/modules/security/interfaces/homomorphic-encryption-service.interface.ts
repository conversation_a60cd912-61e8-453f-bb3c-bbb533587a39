import { IAdvancedEncryptionService } from './advanced-encryption-service.interface';

/**
 * Interface pour le service de chiffrement homomorphique
 * Étend l'interface de chiffrement avancé avec des méthodes spécifiques au chiffrement homomorphique
 */
export interface IHomomorphicEncryptionService extends IAdvancedEncryptionService {
  /**
   * Chiffre une valeur numérique
   * @param value Valeur à chiffrer
   * @returns Valeur chiffrée
   */
  encryptNumber(value: number): Promise<Buffer>;

  /**
   * Déchiffre une valeur numérique
   * @param encryptedData Données chiffrées
   * @returns Valeur déchiffrée
   */
  decryptNumber(encryptedData: Buffer): Promise<number>;

  /**
   * Chiffre un lot de valeurs numériques
   * @param values Valeurs à chiffrer
   * @returns Valeurs chiffrées
   */
  encryptBatch(values: number[]): Promise<Buffer>;

  /**
   * Déchiffre un lot de valeurs numériques
   * @param encryptedData Données chiffrées
   * @returns Valeurs déchiffrées
   */
  decryptBatch(encryptedData: Buffer): Promise<number[]>;

  /**
   * Effectue une addition homomorphique
   * @param a Premier opérande
   * @param b Deuxième opérande
   * @returns Résultat de l'addition
   */
  add(a: Buffer, b: Buffer): Promise<Buffer>;

  /**
   * Effectue une soustraction homomorphique
   * @param a Premier opérande
   * @param b Deuxième opérande
   * @returns Résultat de la soustraction
   */
  subtract(a: Buffer, b: Buffer): Promise<Buffer>;

  /**
   * Effectue une multiplication homomorphique
   * @param a Premier opérande
   * @param b Deuxième opérande
   * @returns Résultat de la multiplication
   */
  multiply(a: Buffer, b: Buffer): Promise<Buffer>;

  /**
   * Effectue une division homomorphique
   * @param a Numérateur
   * @param b Dénominateur (Buffer ou nombre)
   * @returns Résultat de la division
   */
  divide(a: Buffer, b: Buffer | number): Promise<Buffer>;

  /**
   * Calcule la moyenne homomorphique
   * @param values Valeurs chiffrées
   * @returns Moyenne chiffrée
   */
  average(values: Buffer[]): Promise<Buffer>;

  /**
   * Calcule la variance homomorphique
   * @param values Valeurs chiffrées
   * @returns Variance chiffrée
   */
  variance(values: Buffer[]): Promise<Buffer>;

  /**
   * Retourne le schéma de chiffrement homomorphique utilisé
   * @returns Schéma de chiffrement (ex: "BFV", "CKKS")
   */
  getScheme(): string;
}
