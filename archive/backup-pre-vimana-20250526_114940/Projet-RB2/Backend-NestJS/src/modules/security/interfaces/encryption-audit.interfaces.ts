/**
 * Interfaces pour le service d'audit des implémentations de chiffrement
 */

/**
 * Niveaux de sévérité pour les vulnérabilités
 */
export enum VulnerabilitySeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

/**
 * Types de vulnérabilités cryptographiques
 */
export enum CryptoVulnerabilityType {
  WEAK_ALGORITHM = 'WEAK_ALGORITHM',
  INSUFFICIENT_KEY_SIZE = 'INSUFFICIENT_KEY_SIZE',
  INSECURE_MODE = 'INSECURE_MODE',
  STATIC_IV = 'STATIC_IV',
  MISSING_AUTH_TAG = 'MISSING_AUTH_TAG',
  KEY_REUSE = 'KEY_REUSE',
  MISSING_KEY_ROTATION = 'MISSING_KEY_ROTATION',
  INSECURE_RANDOM = 'INSECURE_RANDOM',
  PADDING_ORACLE = 'PADDING_ORACLE',
  TIMING_ATTACK = 'TIMING_ATTACK',
  SIDE_CHANNEL = 'SIDE_CHANNEL',
  QUANTUM_VULNERABLE = 'QUANTUM_VULNERABLE',
  IMPLEMENTATION_FLAW = 'IMPLEMENTATION_FLAW',
  CONFIGURATION_ISSUE = 'CONFIGURATION_ISSUE',
  LIBRARY_VULNERABILITY = 'LIBRARY_VULNERABILITY'
}

/**
 * Interface pour une vulnérabilité cryptographique
 */
export interface CryptoVulnerability {
  id: string;
  type: CryptoVulnerabilityType;
  severity: VulnerabilitySeverity;
  description: string;
  service: string;
  affectedComponent: string;
  remediation: string;
  cve?: string;
  references?: string[];
  detectedAt: Date;
}

/**
 * Interface pour les résultats d'audit d'un service de chiffrement
 */
export interface EncryptionServiceAuditResult {
  serviceId: string;
  serviceName: string;
  serviceType: string;
  timestamp: Date;
  status: 'PASS' | 'FAIL' | 'WARNING';
  vulnerabilities: CryptoVulnerability[];
  recommendations: string[];
  configuration: Record<string, any>;
  complianceStatus: {
    [standard: string]: boolean;
  };
  performanceMetrics?: {
    encryptionTimeMs?: number;
    decryptionTimeMs?: number;
    keyGenerationTimeMs?: number;
  };
}

/**
 * Interface pour le rapport d'audit complet
 */
export interface EncryptionAuditReport {
  id: string;
  timestamp: Date;
  summary: {
    totalServices: number;
    passedServices: number;
    failedServices: number;
    warningServices: number;
    criticalVulnerabilities: number;
    highVulnerabilities: number;
    mediumVulnerabilities: number;
    lowVulnerabilities: number;
  };
  serviceResults: EncryptionServiceAuditResult[];
  overallStatus: 'PASS' | 'FAIL' | 'WARNING';
  recommendations: string[];
}

/**
 * Interface pour les options d'audit
 */
export interface EncryptionAuditOptions {
  includePerformanceTests?: boolean;
  complianceStandards?: string[];
  auditDepth?: 'BASIC' | 'STANDARD' | 'DEEP';
  includeDependencies?: boolean;
  includeLibraryVulnerabilities?: boolean;
  includeQuantumReadiness?: boolean;
}
