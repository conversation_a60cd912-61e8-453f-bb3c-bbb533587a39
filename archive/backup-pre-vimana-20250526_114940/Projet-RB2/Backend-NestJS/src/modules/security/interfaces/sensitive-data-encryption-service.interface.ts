import { IEncryptionService } from './encryption-service.interface';

/**
 * Interface pour le service de chiffrement des données sensibles
 * Étend l'interface de base avec des méthodes spécifiques au chiffrement des données sensibles
 */
export interface ISensitiveDataEncryptionService extends IEncryptionService {
  /**
   * Chiffre une valeur pour stockage en base de données
   * @param value Valeur à chiffrer
   * @param context Contexte de chiffrement (ex: nom de la table, nom de la colonne)
   * @returns Valeur chiffrée
   */
  encryptForStorage(value: string | Buffer, context: Record<string, any>): Promise<string>;

  /**
   * Déchiffre une valeur stockée en base de données
   * @param encryptedValue Valeur chiffrée
   * @param context Contexte de déchiffrement (ex: nom de la table, nom de la colonne)
   * @returns Valeur déchiffrée
   */
  decryptFromStorage(encryptedValue: string, context: Record<string, any>): Promise<string>;

  /**
   * Chiffre un objet entier
   * @param obj Objet à chiffrer
   * @param fieldsToEncrypt Champs à chiffrer
   * @param context Contexte de chiffrement
   * @returns Objet avec les champs spécifiés chiffrés
   */
  encryptObject<T>(obj: T, fieldsToEncrypt: string[], context?: Record<string, any>): Promise<T>;

  /**
   * Déchiffre un objet entier
   * @param obj Objet à déchiffrer
   * @param fieldsToDecrypt Champs à déchiffrer
   * @param context Contexte de déchiffrement
   * @returns Objet avec les champs spécifiés déchiffrés
   */
  decryptObject<T>(obj: T, fieldsToDecrypt: string[], context?: Record<string, any>): Promise<T>;

  /**
   * Retourne l'algorithme de chiffrement utilisé
   * @returns Algorithme de chiffrement (ex: "AES-GCM", "ChaCha20-Poly1305")
   */
  getAlgorithm(): string;

  /**
   * Vérifie si le chiffrement déterministe est activé
   * @returns true si le chiffrement déterministe est activé
   */
  isDeterministicEnabled(): boolean;
}
