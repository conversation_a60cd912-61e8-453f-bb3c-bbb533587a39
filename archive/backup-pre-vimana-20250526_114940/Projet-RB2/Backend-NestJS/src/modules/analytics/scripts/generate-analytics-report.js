#!/usr/bin/env node

/**
 * Script to generate an analytics report for creators
 *
 * Usage:
 *   node generate-analytics-report.js --creatorId <creatorId> [--startDate YYYY-MM-DD] [--endDate YYYY-MM-DD] [--format json|csv|html|pdf] [--output filename] [--compare] [--interactive]
 *
 * Options:
 *   --creatorId   ID of the creator (required)
 *   --startDate   Start date for the report (default: 30 days ago)
 *   --endDate     End date for the report (default: today)
 *   --format      Output format: json, csv, html, or pdf (default: html)
 *   --output      Output file (default: creator-analytics-report-{creatorId}.{format})
 *   --metrics     Comma-separated list of metrics to include (default: all)
 *   --compare     Compare with previous period
 *   --interactive Generate an interactive HTML report with charts
 *   --theme       Report theme: light, dark, or branded (default: light)
 *   --benchmark   Include industry benchmarks for comparison
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { ChartJSNodeCanvas } = require('chartjs-node-canvas');
const PDFDocument = require('pdfkit');
const { format, subDays, differenceInDays, parseISO } = require('date-fns');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  creatorId: null,
  startDate: new Date(new Date().setDate(new Date().getDate() - 30)),
  endDate: new Date(),
  format: 'html',
  output: null,
  metrics: null,
  compare: false,
  interactive: false,
  theme: 'light',
  benchmark: false,
};

for (let i = 0; i < args.length; i++) {
  if (args[i] === '--creatorId' && i + 1 < args.length) {
    options.creatorId = args[i + 1];
    i++;
  } else if (args[i] === '--startDate' && i + 1 < args.length) {
    options.startDate = new Date(args[i + 1]);
    i++;
  } else if (args[i] === '--endDate' && i + 1 < args.length) {
    options.endDate = new Date(args[i + 1]);
    i++;
  } else if (args[i] === '--format' && i + 1 < args.length) {
    options.format = args[i + 1];
    i++;
  } else if (args[i] === '--output' && i + 1 < args.length) {
    options.output = args[i + 1];
    i++;
  } else if (args[i] === '--metrics' && i + 1 < args.length) {
    options.metrics = args[i + 1].split(',');
    i++;
  } else if (args[i] === '--compare') {
    options.compare = true;
  } else if (args[i] === '--interactive') {
    options.interactive = true;
  } else if (args[i] === '--theme' && i + 1 < args.length) {
    options.theme = args[i + 1];
    i++;
  } else if (args[i] === '--benchmark') {
    options.benchmark = true;
  }
}

// Validate required options
if (!options.creatorId) {
  console.error('Error: --creatorId is required');
  process.exit(1);
}

// Set default output filename if not provided
if (!options.output) {
  const dateStr = format(new Date(), 'yyyyMMdd');
  options.output = `creator-analytics-report-${options.creatorId}-${dateStr}.${options.format}`;
}

// Force interactive mode for HTML format
if (options.format === 'html') {
  options.interactive = true;
}

// API base URL
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';

// Configuration pour la génération de graphiques
const chartJSNodeCanvas = new ChartJSNodeCanvas({
  width: 800,
  height: 400,
  backgroundColour: options.theme === 'dark' ? '#2c3e50' : 'white'
});

// Thèmes de couleurs pour les graphiques
const chartColors = {
  light: {
    primary: '#3498db',
    secondary: '#2ecc71',
    tertiary: '#e74c3c',
    quaternary: '#f39c12',
    background: 'white',
    text: '#333333',
    grid: '#dddddd'
  },
  dark: {
    primary: '#3498db',
    secondary: '#2ecc71',
    tertiary: '#e74c3c',
    quaternary: '#f39c12',
    background: '#2c3e50',
    text: '#ecf0f1',
    grid: '#34495e'
  },
  branded: {
    primary: '#8e44ad',
    secondary: '#27ae60',
    tertiary: '#d35400',
    quaternary: '#2c3e50',
    background: '#ecf0f1',
    text: '#2c3e50',
    grid: '#bdc3c7'
  }
};

async function generateReport() {
  console.log('Generating creator analytics report...');
  console.log(`Creator ID: ${options.creatorId}`);
  console.log(`Start date: ${options.startDate.toISOString().split('T')[0]}`);
  console.log(`End date: ${options.endDate.toISOString().split('T')[0]}`);
  console.log(`Format: ${options.format}${options.interactive ? ' (interactive)' : ''}`);

  if (options.compare) {
    console.log('Comparing with previous period');
  }

  if (options.benchmark) {
    console.log('Including industry benchmarks');
  }

  try {
    // Fetch data from the API
    const data = await fetchAnalyticsData();

    // Fetch comparison data if requested
    if (options.compare) {
      const periodLength = differenceInDays(options.endDate, options.startDate);
      const previousStartDate = subDays(options.startDate, periodLength);
      const previousEndDate = subDays(options.startDate, 1);

      console.log(`Comparison period: ${format(previousStartDate, 'yyyy-MM-dd')} to ${format(previousEndDate, 'yyyy-MM-dd')}`);

      data.comparisonData = await fetchAnalyticsData({
        startDate: previousStartDate,
        endDate: previousEndDate
      });
    }

    // Fetch benchmark data if requested
    if (options.benchmark) {
      data.benchmarkData = await fetchBenchmarkData();
    }

    // Generate report in the requested format
    let reportContent;
    switch (options.format) {
      case 'json':
        reportContent = JSON.stringify(data, null, 2);
        break;
      case 'csv':
        reportContent = generateCsvReport(data);
        break;
      case 'html':
        reportContent = await generateHtmlReport(data);
        break;
      case 'pdf':
        await generatePdfReport(data);
        reportContent = null; // PDF is written directly to file
        break;
      default:
        console.error(`Unsupported format: ${options.format}`);
        process.exit(1);
    }

    // Write report to file if not PDF (PDF is written directly)
    if (reportContent) {
      fs.writeFileSync(options.output, reportContent);
      console.log(`Report written to ${options.output}`);
    }

  } catch (error) {
    console.error('Error generating report:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the report generation
generateReport();
