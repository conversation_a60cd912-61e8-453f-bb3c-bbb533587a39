import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { EngagementService } from './engagement.service';
import { AudienceService } from './audience.service';
import { RevenueService } from './revenue.service';
import { MetricType } from '@prisma/client';

interface BenchmarkResult {
  value: number;
  percentile: number;
  categoryAverage: number;
}

interface CategoryStats {
  min: number;
  max: number;
  avg: number;
  median: number;
  p25: number;
  p75: number;
  p90: number;
  p95: number;
  p99: number;
  count: number;
}

@Injectable()
export class BenchmarkService {
  private readonly logger = new Logger(BenchmarkService.name);
  private readonly categoryCache: Map<string, Map<string, CategoryStats>> = new Map();
  private readonly cacheTTL: number;
  private lastCacheUpdate: Date = new Date(0);

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly engagementService: EngagementService,
    private readonly audienceService: AudienceService,
    private readonly revenueService: RevenueService,
  ) {
    this.cacheTTL = this.configService.get<number>('BENCHMARK_CACHE_TTL', 3600000); // 1 heure par défaut
  }

  /**
   * Récupère les benchmarks d'engagement pour un créateur
   * @param creatorId ID du créateur
   * @param category Catégorie pour le benchmark (ex: "yoga", "fitness", "all")
   * @returns Benchmarks d'engagement
   */
  async getEngagementBenchmarks(creatorId: string, category: string): Promise<{
    views: BenchmarkResult;
    engagementRate: BenchmarkResult;
  }> {
    this.logger.debug(`Getting engagement benchmarks for creator ${creatorId} in category ${category}`);

    // Récupérer les métriques d'engagement du créateur
    const creatorMetrics = await this.engagementService.getCreatorEngagementSummary(creatorId);

    // Récupérer les statistiques de la catégorie
    const categoryStats = await this.getCategoryStats(category);

    // Calculer les benchmarks pour les vues
    const viewsValue = creatorMetrics.totalViews;
    const viewsStats = categoryStats.get(MetricType.VIEWS);
    const viewsBenchmark = this.calculateBenchmark(viewsValue, viewsStats);

    // Calculer les benchmarks pour le taux d'engagement
    const engagementRateValue = creatorMetrics.engagementRate;
    const engagementRateStats = categoryStats.get(MetricType.ENGAGEMENT_RATE);
    const engagementRateBenchmark = this.calculateBenchmark(engagementRateValue, engagementRateStats);

    return {
      views: viewsBenchmark,
      engagementRate: engagementRateBenchmark,
    };
  }

  /**
   * Récupère les benchmarks d'audience pour un créateur
   * @param creatorId ID du créateur
   * @param category Catégorie pour le benchmark
   * @returns Benchmarks d'audience
   */
  async getAudienceBenchmarks(creatorId: string, category: string): Promise<{
    followers: BenchmarkResult;
    growth: BenchmarkResult;
  }> {
    this.logger.debug(`Getting audience benchmarks for creator ${creatorId} in category ${category}`);

    // Récupérer les métriques d'audience du créateur
    const creatorMetrics = await this.audienceService.getCreatorAudienceSummary(creatorId);

    // Récupérer les statistiques de la catégorie
    const categoryStats = await this.getCategoryStats(category);

    // Calculer les benchmarks pour les followers
    const followersValue = creatorMetrics.totalFollowers;
    const followersStats = categoryStats.get(MetricType.FOLLOWERS);
    const followersBenchmark = this.calculateBenchmark(followersValue, followersStats);

    // Calculer les benchmarks pour la croissance
    const growthValue = creatorMetrics.growthRate;
    const growthStats = categoryStats.get(MetricType.GROWTH_RATE);
    const growthBenchmark = this.calculateBenchmark(growthValue, growthStats);

    return {
      followers: followersBenchmark,
      growth: growthBenchmark,
    };
  }

  /**
   * Récupère les benchmarks de revenus pour un créateur
   * @param creatorId ID du créateur
   * @param category Catégorie pour le benchmark
   * @returns Benchmarks de revenus
   */
  async getRevenueBenchmarks(creatorId: string, category: string): Promise<{
    amount: BenchmarkResult;
  }> {
    this.logger.debug(`Getting revenue benchmarks for creator ${creatorId} in category ${category}`);

    // Récupérer les métriques de revenus du créateur
    const creatorMetrics = await this.revenueService.getCreatorRevenueSummary(creatorId);

    // Récupérer les statistiques de la catégorie
    const categoryStats = await this.getCategoryStats(category);

    // Calculer les benchmarks pour les revenus
    const revenueValue = creatorMetrics.totalRevenue;
    const revenueStats = categoryStats.get(MetricType.REVENUE);
    const revenueBenchmark = this.calculateBenchmark(revenueValue, revenueStats);

    return {
      amount: revenueBenchmark,
    };
  }

  /**
   * Récupère les statistiques pour une catégorie
   * @param category Catégorie
   * @returns Statistiques par type de métrique
   */
  private async getCategoryStats(category: string): Promise<Map<string, CategoryStats>> {
    // Vérifier si les statistiques sont en cache et si le cache est valide
    const now = new Date();
    if (
      this.categoryCache.has(category) &&
      now.getTime() - this.lastCacheUpdate.getTime() < this.cacheTTL
    ) {
      return this.categoryCache.get(category);
    }

    this.logger.debug(`Computing category stats for ${category}`);

    // Récupérer les statistiques depuis la base de données
    const stats = new Map<string, CategoryStats>();

    // Récupérer les créateurs de la catégorie
    const creators = await this.getCreatorsInCategory(category);
    const creatorIds = creators.map(c => c.id);

    if (creatorIds.length === 0) {
      this.logger.warn(`No creators found in category ${category}`);
      return stats;
    }

    // Récupérer les métriques pour chaque type
    await Promise.all([
      this.computeMetricStats(MetricType.VIEWS, creatorIds, stats),
      this.computeMetricStats(MetricType.ENGAGEMENT_RATE, creatorIds, stats),
      this.computeMetricStats(MetricType.FOLLOWERS, creatorIds, stats),
      this.computeMetricStats(MetricType.GROWTH_RATE, creatorIds, stats),
      this.computeMetricStats(MetricType.REVENUE, creatorIds, stats),
    ]);

    // Mettre en cache les statistiques
    this.categoryCache.set(category, stats);
    this.lastCacheUpdate = now;

    return stats;
  }

  /**
   * Récupère les créateurs dans une catégorie
   * @param category Catégorie
   * @returns Liste des créateurs
   */
  private async getCreatorsInCategory(category: string): Promise<any[]> {
    if (category === 'all') {
      // Récupérer tous les créateurs
      return this.prisma.user.findMany({
        where: {
          role: 'CREATOR',
        },
        select: {
          id: true,
          username: true,
          categories: true,
        },
      });
    } else {
      // Récupérer les créateurs de la catégorie spécifiée
      return this.prisma.user.findMany({
        where: {
          role: 'CREATOR',
          categories: {
            has: category,
          },
        },
        select: {
          id: true,
          username: true,
          categories: true,
        },
      });
    }
  }

  /**
   * Calcule les statistiques pour un type de métrique
   * @param metricType Type de métrique
   * @param creatorIds IDs des créateurs
   * @param stats Map des statistiques à mettre à jour
   */
  private async computeMetricStats(
    metricType: MetricType,
    creatorIds: string[],
    stats: Map<string, CategoryStats>,
  ): Promise<void> {
    // Récupérer les valeurs pour tous les créateurs
    const values: number[] = [];

    for (const creatorId of creatorIds) {
      let value: number;

      switch (metricType) {
        case MetricType.VIEWS:
          const engagementSummary = await this.engagementService.getCreatorEngagementSummary(creatorId);
          value = engagementSummary.totalViews;
          break;
        case MetricType.ENGAGEMENT_RATE:
          const engagementRateSummary = await this.engagementService.getCreatorEngagementSummary(creatorId);
          value = engagementRateSummary.engagementRate;
          break;
        case MetricType.FOLLOWERS:
          const audienceSummary = await this.audienceService.getCreatorAudienceSummary(creatorId);
          value = audienceSummary.totalFollowers;
          break;
        case MetricType.GROWTH_RATE:
          const growthSummary = await this.audienceService.getCreatorAudienceSummary(creatorId);
          value = growthSummary.growthRate;
          break;
        case MetricType.REVENUE:
          const revenueSummary = await this.revenueService.getCreatorRevenueSummary(creatorId);
          value = revenueSummary.totalRevenue;
          break;
        default:
          value = 0;
      }

      values.push(value);
    }

    // Calculer les statistiques
    const categoryStats = this.calculateStats(values);
    stats.set(metricType, categoryStats);
  }

  /**
   * Calcule les statistiques à partir d'un tableau de valeurs
   * @param values Tableau de valeurs
   * @returns Statistiques calculées
   */
  private calculateStats(values: number[]): CategoryStats {
    if (values.length === 0) {
      return {
        min: 0,
        max: 0,
        avg: 0,
        median: 0,
        p25: 0,
        p75: 0,
        p90: 0,
        p95: 0,
        p99: 0,
        count: 0,
      };
    }

    // Trier les valeurs
    const sortedValues = [...values].sort((a, b) => a - b);
    const count = sortedValues.length;

    // Calculer les statistiques de base
    const min = sortedValues[0];
    const max = sortedValues[count - 1];
    const avg = sortedValues.reduce((sum, val) => sum + val, 0) / count;

    // Calculer les percentiles
    const median = this.getPercentile(sortedValues, 50);
    const p25 = this.getPercentile(sortedValues, 25);
    const p75 = this.getPercentile(sortedValues, 75);
    const p90 = this.getPercentile(sortedValues, 90);
    const p95 = this.getPercentile(sortedValues, 95);
    const p99 = this.getPercentile(sortedValues, 99);

    return {
      min,
      max,
      avg,
      median,
      p25,
      p75,
      p90,
      p95,
      p99,
      count,
    };
  }

  /**
   * Calcule un percentile spécifique
   * @param sortedValues Valeurs triées
   * @param percentile Percentile à calculer (0-100)
   * @returns Valeur du percentile
   */
  private getPercentile(sortedValues: number[], percentile: number): number {
    const index = (percentile / 100) * (sortedValues.length - 1);
    const lower = Math.floor(index);
    const upper = Math.ceil(index);
    const weight = index % 1;

    if (upper === lower) {
      return sortedValues[index];
    }

    return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
  }

  /**
   * Calcule le benchmark pour une valeur
   * @param value Valeur à comparer
   * @param stats Statistiques de la catégorie
   * @returns Résultat du benchmark
   */
  private calculateBenchmark(value: number, stats: CategoryStats): BenchmarkResult {
    if (!stats) {
      return {
        value,
        percentile: 50,
        categoryAverage: value,
      };
    }

    // Calculer le percentile de la valeur
    const percentile = this.calculatePercentile(value, stats);

    return {
      value,
      percentile,
      categoryAverage: stats.avg,
    };
  }

  /**
   * Calcule le percentile d'une valeur dans les statistiques
   * @param value Valeur
   * @param stats Statistiques
   * @returns Percentile (0-100)
   */
  private calculatePercentile(value: number, stats: CategoryStats): number {
    if (value <= stats.min) {
      return 0;
    }
    if (value >= stats.max) {
      return 100;
    }

    // Interpolation linéaire entre les percentiles connus
    if (value <= stats.p25) {
      return this.interpolate(value, stats.min, stats.p25, 0, 25);
    } else if (value <= stats.median) {
      return this.interpolate(value, stats.p25, stats.median, 25, 50);
    } else if (value <= stats.p75) {
      return this.interpolate(value, stats.median, stats.p75, 50, 75);
    } else if (value <= stats.p90) {
      return this.interpolate(value, stats.p75, stats.p90, 75, 90);
    } else if (value <= stats.p95) {
      return this.interpolate(value, stats.p90, stats.p95, 90, 95);
    } else if (value <= stats.p99) {
      return this.interpolate(value, stats.p95, stats.p99, 95, 99);
    } else {
      return this.interpolate(value, stats.p99, stats.max, 99, 100);
    }
  }

  /**
   * Interpolation linéaire
   * @param value Valeur à interpoler
   * @param min Valeur minimale
   * @param max Valeur maximale
   * @param minPercentile Percentile minimal
   * @param maxPercentile Percentile maximal
   * @returns Valeur interpolée
   */
  private interpolate(
    value: number,
    min: number,
    max: number,
    minPercentile: number,
    maxPercentile: number,
  ): number {
    if (max === min) {
      return minPercentile;
    }
    return minPercentile + ((value - min) / (max - min)) * (maxPercentile - minPercentile);
  }
}
