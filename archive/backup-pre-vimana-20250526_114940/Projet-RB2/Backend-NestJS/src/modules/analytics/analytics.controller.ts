import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AnalyticsService } from './analytics.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ContentType } from '@prisma/client';

@ApiTags('analytics')
@Controller('analytics')
export class AnalyticsController {
  private readonly logger = new Logger(AnalyticsController.name);

  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('metrics/:creatorId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les métriques pour un créateur' })
  @ApiResponse({
    status: 200,
    description: 'Les métriques du créateur',
  })
  async getCreatorMetrics(
    @Param('creatorId') creatorId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('contentType') contentType?: ContentType,
  ) {
    this.logger.debug(`Getting metrics for creator: ${creatorId}`);
    return this.analyticsService.getCreatorMetrics(creatorId, {
      startDate,
      endDate,
      contentType,
    });
  }

  @Get('metrics/:creatorId/content/:contentId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les métriques pour un contenu spécifique' })
  @ApiResponse({
    status: 200,
    description: 'Les métriques du contenu',
  })
  async getContentMetrics(
    @Param('creatorId') creatorId: string,
    @Param('contentId') contentId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.debug(`Getting metrics for content: ${contentId}`);
    return this.analyticsService.getContentMetrics(creatorId, contentId, {
      startDate,
      endDate,
    });
  }

  @Get('metrics/:creatorId/summary')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir un résumé des métriques pour un créateur' })
  @ApiResponse({
    status: 200,
    description: 'Le résumé des métriques du créateur',
  })
  async getCreatorSummary(@Param('creatorId') creatorId: string) {
    this.logger.debug(`Getting summary for creator: ${creatorId}`);
    return this.analyticsService.getCreatorSummary(creatorId);
  }

  @Get('forecasting/:creatorId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les prévisions pour un créateur' })
  @ApiResponse({
    status: 200,
    description: 'Les prévisions du créateur',
  })
  async getCreatorForecasts(@Param('creatorId') creatorId: string) {
    this.logger.debug(`Getting forecasts for creator: ${creatorId}`);
    return this.analyticsService.getCreatorForecasts(creatorId);
  }

  @Get('benchmarks/:creatorId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les benchmarks pour un créateur' })
  @ApiResponse({
    status: 200,
    description: 'Les benchmarks du créateur',
  })
  async getCreatorBenchmarks(
    @Param('creatorId') creatorId: string,
    @Query('category') category: string = 'all',
  ) {
    this.logger.debug(`Getting benchmarks for creator: ${creatorId} in category: ${category}`);
    return this.analyticsService.getCreatorBenchmarks(creatorId, category);
  }

  @Get('dashboards/:creatorId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les tableaux de bord d\'un créateur' })
  @ApiResponse({
    status: 200,
    description: 'Les tableaux de bord du créateur',
  })
  async getCreatorDashboards(@Param('creatorId') creatorId: string) {
    this.logger.debug(`Getting dashboards for creator: ${creatorId}`);
    return this.analyticsService.getCreatorDashboards(creatorId);
  }

  @Get('dashboards/detail/:dashboardId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir un tableau de bord spécifique' })
  @ApiResponse({
    status: 200,
    description: 'Le tableau de bord',
  })
  async getCreatorDashboard(@Param('dashboardId') dashboardId: string) {
    this.logger.debug(`Getting dashboard: ${dashboardId}`);
    return this.analyticsService.getCreatorDashboard(dashboardId);
  }

  @Post('dashboards/:creatorId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Créer un nouveau tableau de bord' })
  @ApiResponse({
    status: 201,
    description: 'Le tableau de bord a été créé avec succès',
  })
  async createCreatorDashboard(
    @Param('creatorId') creatorId: string,
    @Body() dashboardData: any,
  ) {
    this.logger.debug(`Creating dashboard for creator: ${creatorId}`);
    return this.analyticsService.createCreatorDashboard(creatorId, dashboardData);
  }

  @Put('dashboards/:dashboardId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour un tableau de bord' })
  @ApiResponse({
    status: 200,
    description: 'Le tableau de bord a été mis à jour avec succès',
  })
  async updateCreatorDashboard(
    @Param('dashboardId') dashboardId: string,
    @Body() dashboardData: any,
  ) {
    this.logger.debug(`Updating dashboard: ${dashboardId}`);
    return this.analyticsService.updateCreatorDashboard(dashboardId, dashboardData);
  }

  @Delete('dashboards/:dashboardId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Supprimer un tableau de bord' })
  @ApiResponse({
    status: 200,
    description: 'Le tableau de bord a été supprimé avec succès',
  })
  async deleteCreatorDashboard(@Param('dashboardId') dashboardId: string) {
    this.logger.debug(`Deleting dashboard: ${dashboardId}`);
    return this.analyticsService.deleteCreatorDashboard(dashboardId);
  }
}
