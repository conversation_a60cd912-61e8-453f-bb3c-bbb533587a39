import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsString, IsOptional, IsUUID, IsNotEmpty, IsBoolean, IsNumber, Min, IsDate, IsObject, ValidateNested, IsArray } from 'class-validator';
import { Type } from 'class-transformer';
import { ContentType, WidgetType } from '@prisma/client';

export class MetricsFiltersDto {
  @ApiPropertyOptional({ 
    description: 'Date de début pour les métriques (format: YYYY-MM-DD)',
    example: '2023-01-01'
  })
  @IsOptional()
  @IsString()
  startDate?: string;

  @ApiPropertyOptional({ 
    description: 'Date de fin pour les métriques (format: YYYY-MM-DD)',
    example: '2023-12-31'
  })
  @IsOptional()
  @IsString()
  endDate?: string;

  @ApiPropertyOptional({ 
    description: 'Type de contenu',
    enum: ContentType
  })
  @IsOptional()
  @IsEnum(ContentType)
  contentType?: ContentType;
}

export class ContentMetricsFiltersDto {
  @ApiPropertyOptional({ 
    description: 'Date de début pour les métriques (format: YYYY-MM-DD)',
    example: '2023-01-01'
  })
  @IsOptional()
  @IsString()
  startDate?: string;

  @ApiPropertyOptional({ 
    description: 'Date de fin pour les métriques (format: YYYY-MM-DD)',
    example: '2023-12-31'
  })
  @IsOptional()
  @IsString()
  endDate?: string;
}

export class PositionDto {
  @ApiProperty({ description: 'Position X dans la grille', example: 0 })
  @IsNumber()
  x: number;

  @ApiProperty({ description: 'Position Y dans la grille', example: 0 })
  @IsNumber()
  y: number;

  @ApiProperty({ description: 'Largeur du widget', example: 6 })
  @IsNumber()
  @Min(1)
  width: number;

  @ApiProperty({ description: 'Hauteur du widget', example: 4 })
  @IsNumber()
  @Min(1)
  height: number;
}

export class WidgetDto {
  @ApiProperty({ 
    description: 'Type de widget',
    enum: WidgetType,
    example: WidgetType.LINE_CHART
  })
  @IsEnum(WidgetType)
  type: WidgetType;

  @ApiProperty({ description: 'Titre du widget', example: 'Évolution des vues' })
  @IsString()
  title: string;

  @ApiProperty({ 
    description: 'Configuration du widget',
    example: { 
      metric: 'VIEWS', 
      timeRange: 'LAST_30_DAYS',
      showLegend: true
    }
  })
  @IsObject()
  config: Record<string, any>;

  @ApiProperty({ description: 'Position du widget dans le tableau de bord' })
  @ValidateNested()
  @Type(() => PositionDto)
  position: PositionDto;
}

export class CreateDashboardDto {
  @ApiProperty({ description: 'Nom du tableau de bord', example: 'Tableau de bord principal' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Description du tableau de bord', example: 'Tableau de bord pour suivre les performances globales' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    description: 'Configuration de la mise en page',
    example: { 
      columns: 12, 
      rowHeight: 30,
      compactType: 'vertical'
    }
  })
  @IsObject()
  layout: Record<string, any>;

  @ApiPropertyOptional({ 
    description: 'Widgets du tableau de bord',
    type: [WidgetDto]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WidgetDto)
  widgets?: WidgetDto[];

  @ApiPropertyOptional({ 
    description: 'Indique si c\'est le tableau de bord par défaut',
    default: false
  })
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;
}

export class UpdateDashboardDto {
  @ApiPropertyOptional({ description: 'Nom du tableau de bord' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Description du tableau de bord' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Configuration de la mise en page' })
  @IsOptional()
  @IsObject()
  layout?: Record<string, any>;

  @ApiPropertyOptional({ 
    description: 'Indique si c\'est le tableau de bord par défaut'
  })
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;
}

export class AddWidgetDto {
  @ApiProperty({ 
    description: 'Type de widget',
    enum: WidgetType,
    example: WidgetType.BAR_CHART
  })
  @IsEnum(WidgetType)
  type: WidgetType;

  @ApiProperty({ description: 'Titre du widget', example: 'Répartition des revenus' })
  @IsString()
  title: string;

  @ApiProperty({ 
    description: 'Configuration du widget',
    example: { 
      metric: 'REVENUE', 
      groupBy: 'SOURCE',
      showValues: true
    }
  })
  @IsObject()
  config: Record<string, any>;

  @ApiProperty({ description: 'Position du widget dans le tableau de bord' })
  @ValidateNested()
  @Type(() => PositionDto)
  position: PositionDto;
}

export class UpdateWidgetDto {
  @ApiPropertyOptional({ 
    description: 'Type de widget',
    enum: WidgetType
  })
  @IsOptional()
  @IsEnum(WidgetType)
  type?: WidgetType;

  @ApiPropertyOptional({ description: 'Titre du widget' })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({ description: 'Configuration du widget' })
  @IsOptional()
  @IsObject()
  config?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Position du widget dans le tableau de bord' })
  @IsOptional()
  @ValidateNested()
  @Type(() => PositionDto)
  position?: PositionDto;
}
