import { Injectable, NotFoundException, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { firstValueFrom } from 'rxjs';
import { CreatePaymentIntentDto } from './dto/create-payment-intent.dto';
import { ConfirmPaymentDto } from './dto/confirm-payment.dto';
import { AddPaymentMethodDto } from './dto/add-payment-method.dto';
import { RequestRefundDto } from './dto/request-refund.dto';
import { ApplyPromoCodeDto } from './dto/apply-promo-code.dto';

@Injectable()
export class PaymentsService {
  private readonly financialServiceUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly prismaService: PrismaService,
  ) {
    this.financialServiceUrl = this.configService.get<string>('FINANCIAL_SERVICE_URL') || 'http://localhost:3004/api';
  }

  /**
   * Create a payment intent for a booking
   * @param createPaymentIntentDto - DTO with booking ID
   * @param userId - ID of the current user
   * @returns Payment intent with client secret
   */
  async createPaymentIntent(createPaymentIntentDto: CreatePaymentIntentDto, userId: string) {
    const { bookingId } = createPaymentIntentDto;

    // Verify booking exists and belongs to the user
    const booking = await this.prismaService.booking.findUnique({
      where: { id: bookingId },
      include: { retreat: true },
    });

    if (!booking) {
      throw new NotFoundException('Booking not found');
    }

    if (booking.userId !== userId) {
      throw new BadRequestException('This booking does not belong to you');
    }

    if (booking.status === 'CONFIRMED') {
      throw new BadRequestException('This booking is already paid for');
    }

    try {
      // Call Financial Management microservice to create payment intent
      const response = await firstValueFrom(
        this.httpService.post(`${this.financialServiceUrl}/payments/intent`, {
          amount: booking.totalPrice,
          currency: 'EUR',
          bookingId,
          userId,
          metadata: {
            bookingId,
            retreatId: booking.retreatId,
            retreatTitle: booking.retreat.title,
          },
        }),
      );

      return response.data;
    } catch (error) {
      console.error('Error creating payment intent:', error.response?.data || error.message);
      throw new InternalServerErrorException('Failed to create payment intent');
    }
  }

  /**
   * Confirm a payment
   * @param confirmPaymentDto - DTO with payment intent ID and payment method ID
   * @param userId - ID of the current user
   * @returns Confirmed payment
   */
  async confirmPayment(confirmPaymentDto: ConfirmPaymentDto, userId: string) {
    const { paymentIntentId, paymentMethodId } = confirmPaymentDto;

    try {
      // Call Financial Management microservice to confirm payment
      const response = await firstValueFrom(
        this.httpService.post(`${this.financialServiceUrl}/payments/confirm`, {
          paymentIntentId,
          paymentMethodId,
          userId,
        }),
      );

      // Update booking status if payment is successful
      const paymentData = response.data;
      if (paymentData.status === 'succeeded' && paymentData.metadata?.bookingId) {
        await this.prismaService.booking.update({
          where: { id: paymentData.metadata.bookingId },
          data: { status: 'CONFIRMED' },
        });
      }

      return paymentData;
    } catch (error) {
      console.error('Error confirming payment:', error.response?.data || error.message);
      throw new InternalServerErrorException('Failed to confirm payment');
    }
  }

  /**
   * Get payment methods for a user
   * @param userId - ID of the current user
   * @returns List of payment methods
   */
  async getPaymentMethods(userId: string) {
    try {
      // Call Financial Management microservice to get payment methods
      const response = await firstValueFrom(
        this.httpService.get(`${this.financialServiceUrl}/payments/methods/${userId}`),
      );

      return response.data;
    } catch (error) {
      console.error('Error getting payment methods:', error.response?.data || error.message);
      throw new InternalServerErrorException('Failed to get payment methods');
    }
  }

  /**
   * Add a payment method for a user
   * @param addPaymentMethodDto - DTO with payment method ID
   * @param userId - ID of the current user
   * @returns Added payment method
   */
  async addPaymentMethod(addPaymentMethodDto: AddPaymentMethodDto, userId: string) {
    const { paymentMethodId } = addPaymentMethodDto;

    try {
      // Call Financial Management microservice to add payment method
      const response = await firstValueFrom(
        this.httpService.post(`${this.financialServiceUrl}/payments/methods`, {
          paymentMethodId,
          userId,
        }),
      );

      return response.data;
    } catch (error) {
      console.error('Error adding payment method:', error.response?.data || error.message);
      throw new InternalServerErrorException('Failed to add payment method');
    }
  }

  /**
   * Delete a payment method
   * @param paymentMethodId - ID of the payment method to delete
   * @param userId - ID of the current user
   * @returns Success status
   */
  async deletePaymentMethod(paymentMethodId: string, userId: string) {
    try {
      // Call Financial Management microservice to delete payment method
      const response = await firstValueFrom(
        this.httpService.delete(`${this.financialServiceUrl}/payments/methods/${paymentMethodId}`, {
          data: { userId },
        }),
      );

      return response.data;
    } catch (error) {
      console.error('Error deleting payment method:', error.response?.data || error.message);
      throw new InternalServerErrorException('Failed to delete payment method');
    }
  }

  /**
   * Get payment history for a user
   * @param userId - ID of the current user
   * @param page - Page number
   * @param limit - Number of items per page
   * @returns Paginated list of payments
   */
  async getPaymentHistory(userId: string, page = 1, limit = 10) {
    try {
      // Call Financial Management microservice to get payment history
      const response = await firstValueFrom(
        this.httpService.get(`${this.financialServiceUrl}/payments/history/${userId}`, {
          params: { page, limit },
        }),
      );

      return response.data;
    } catch (error) {
      console.error('Error getting payment history:', error.response?.data || error.message);
      throw new InternalServerErrorException('Failed to get payment history');
    }
  }

  /**
   * Request a refund for a payment
   * @param requestRefundDto - DTO with payment ID, amount, and reason
   * @param userId - ID of the current user
   * @returns Refund details
   */
  async requestRefund(requestRefundDto: RequestRefundDto, userId: string) {
    const { paymentId, amount, reason } = requestRefundDto;

    try {
      // Call Financial Management microservice to request refund
      const response = await firstValueFrom(
        this.httpService.post(`${this.financialServiceUrl}/refunds`, {
          paymentId,
          amount,
          reason,
          userId,
        }),
      );

      // Update booking status if refund is successful
      const refundData = response.data;
      if (refundData.status === 'succeeded' && refundData.metadata?.bookingId) {
        await this.prismaService.booking.update({
          where: { id: refundData.metadata.bookingId },
          data: { status: 'CANCELLED' },
        });
      }

      return refundData;
    } catch (error) {
      console.error('Error requesting refund:', error.response?.data || error.message);
      throw new InternalServerErrorException('Failed to request refund');
    }
  }

  /**
   * Apply a promo code to a booking
   * @param applyPromoCodeDto - DTO with booking ID and promo code
   * @param userId - ID of the current user
   * @returns Updated booking with applied discount
   */
  async applyPromoCode(applyPromoCodeDto: ApplyPromoCodeDto, userId: string) {
    const { bookingId, promoCode } = applyPromoCodeDto;

    // Verify booking exists and belongs to the user
    const booking = await this.prismaService.booking.findUnique({
      where: { id: bookingId },
      include: { retreat: true },
    });

    if (!booking) {
      throw new NotFoundException('Booking not found');
    }

    if (booking.userId !== userId) {
      throw new BadRequestException('This booking does not belong to you');
    }

    try {
      // Call Financial Management microservice to validate and apply promo code
      const response = await firstValueFrom(
        this.httpService.post(`${this.financialServiceUrl}/promo-codes/apply`, {
          promoCode,
          bookingId,
          userId,
          originalAmount: booking.totalPrice,
        }),
      );

      const promoCodeData = response.data;

      // Update booking with discount
      const updatedBooking = await this.prismaService.booking.update({
        where: { id: bookingId },
        data: {
          totalPrice: promoCodeData.discountedAmount,
          discountAmount: promoCodeData.discountAmount,
          promoCode: promoCode,
        },
      });

      return updatedBooking;
    } catch (error) {
      console.error('Error applying promo code:', error.response?.data || error.message);
      throw new InternalServerErrorException('Failed to apply promo code');
    }
  }
}
