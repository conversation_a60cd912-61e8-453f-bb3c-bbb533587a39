import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUUID } from 'class-validator';

export class ApplyPromoCodeDto {
  @ApiProperty({
    description: 'ID of the booking to apply the promo code to',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  bookingId: string;

  @ApiProperty({
    description: 'Promo code to apply',
    example: 'SUMMER2023',
  })
  @IsString()
  @IsNotEmpty()
  promoCode: string;
}
