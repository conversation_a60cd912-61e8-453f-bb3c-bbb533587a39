import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsNumber, IsOptional, Min } from 'class-validator';

export class RequestRefundDto {
  @ApiProperty({
    description: 'ID of the payment to refund',
    example: 'py_3NqLkSJHR94LkQpZ1gEDz7Xm',
  })
  @IsString()
  @IsNotEmpty()
  paymentId: string;

  @ApiPropertyOptional({
    description: 'Amount to refund (in cents). If not provided, full amount will be refunded.',
    example: 10000,
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  amount?: number;

  @ApiPropertyOptional({
    description: 'Reason for the refund',
    example: 'Customer requested cancellation',
  })
  @IsString()
  @IsOptional()
  reason?: string;
}
