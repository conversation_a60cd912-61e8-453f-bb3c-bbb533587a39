import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { PaymentsService } from './payments.service';
import { CreatePaymentIntentDto } from './dto/create-payment-intent.dto';
import { ConfirmPaymentDto } from './dto/confirm-payment.dto';
import { AddPaymentMethodDto } from './dto/add-payment-method.dto';
import { RequestRefundDto } from './dto/request-refund.dto';
import { ApplyPromoCodeDto } from './dto/apply-promo-code.dto';

@ApiTags('payments')
@Controller('payments')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post('intent')
  @ApiOperation({ summary: 'Create a payment intent for a booking' })
  @ApiResponse({ status: 201, description: 'Payment intent created successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid request data.' })
  @ApiResponse({ status: 404, description: 'Booking not found.' })
  async createPaymentIntent(
    @Body() createPaymentIntentDto: CreatePaymentIntentDto,
    @CurrentUser() user: any,
  ) {
    return this.paymentsService.createPaymentIntent(createPaymentIntentDto, user.id);
  }

  @Post('confirm')
  @ApiOperation({ summary: 'Confirm a payment' })
  @ApiResponse({ status: 200, description: 'Payment confirmed successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid request data.' })
  async confirmPayment(
    @Body() confirmPaymentDto: ConfirmPaymentDto,
    @CurrentUser() user: any,
  ) {
    return this.paymentsService.confirmPayment(confirmPaymentDto, user.id);
  }

  @Get('methods')
  @ApiOperation({ summary: 'Get payment methods for the current user' })
  @ApiResponse({ status: 200, description: 'Payment methods retrieved successfully.' })
  async getPaymentMethods(@CurrentUser() user: any) {
    return this.paymentsService.getPaymentMethods(user.id);
  }

  @Post('methods')
  @ApiOperation({ summary: 'Add a payment method for the current user' })
  @ApiResponse({ status: 201, description: 'Payment method added successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid request data.' })
  async addPaymentMethod(
    @Body() addPaymentMethodDto: AddPaymentMethodDto,
    @CurrentUser() user: any,
  ) {
    return this.paymentsService.addPaymentMethod(addPaymentMethodDto, user.id);
  }

  @Delete('methods/:id')
  @ApiOperation({ summary: 'Delete a payment method' })
  @ApiResponse({ status: 200, description: 'Payment method deleted successfully.' })
  @ApiResponse({ status: 404, description: 'Payment method not found.' })
  @ApiParam({ name: 'id', description: 'Payment method ID' })
  async deletePaymentMethod(
    @Param('id') paymentMethodId: string,
    @CurrentUser() user: any,
  ) {
    return this.paymentsService.deletePaymentMethod(paymentMethodId, user.id);
  }

  @Get('history')
  @ApiOperation({ summary: 'Get payment history for the current user' })
  @ApiResponse({ status: 200, description: 'Payment history retrieved successfully.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page' })
  async getPaymentHistory(
    @CurrentUser() user: any,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.paymentsService.getPaymentHistory(user.id, page, limit);
  }

  @Post('refunds')
  @ApiOperation({ summary: 'Request a refund for a payment' })
  @ApiResponse({ status: 201, description: 'Refund requested successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid request data.' })
  async requestRefund(
    @Body() requestRefundDto: RequestRefundDto,
    @CurrentUser() user: any,
  ) {
    return this.paymentsService.requestRefund(requestRefundDto, user.id);
  }

  @Post('promo-codes/apply')
  @ApiOperation({ summary: 'Apply a promo code to a booking' })
  @ApiResponse({ status: 200, description: 'Promo code applied successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid promo code or booking.' })
  @ApiResponse({ status: 404, description: 'Booking not found.' })
  @HttpCode(HttpStatus.OK)
  async applyPromoCode(
    @Body() applyPromoCodeDto: ApplyPromoCodeDto,
    @CurrentUser() user: any,
  ) {
    return this.paymentsService.applyPromoCode(applyPromoCodeDto, user.id);
  }

  @Get('available-methods')
  @ApiOperation({ summary: 'Get available payment methods for a country' })
  @ApiResponse({ status: 200, description: 'Available payment methods retrieved successfully.' })
  @ApiQuery({ name: 'country', required: true, type: String, description: 'ISO country code' })
  async getAvailablePaymentMethods(@Query('country') countryCode: string) {
    // This endpoint doesn't need user authentication
    // It returns the available payment methods for a specific country
    const response = await this.paymentsService['httpService'].get(
      `${this.paymentsService['financialServiceUrl']}/payments/available-methods`,
      { params: { country: countryCode } },
    ).toPromise();
    
    return response.data;
  }
}
