import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsArray,
  IsUrl,
  ValidateNested,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PartnerType, PartnerCategory } from '../../../prisma/prisma-types';

export class ContactInfoDto {
  @ApiProperty({ description: 'Primary contact name', example: '<PERSON>' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Primary contact email', example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: 'Primary contact phone', example: '+33612345678' })
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiPropertyOptional({ description: 'Primary contact position', example: 'CEO' })
  @IsString()
  @IsOptional()
  position?: string;
}

export class AddressDto {
  @ApiProperty({ description: 'Street address', example: '123 Main St' })
  @IsString()
  @IsNotEmpty()
  street: string;

  @ApiProperty({ description: 'City', example: 'Paris' })
  @IsString()
  @IsNotEmpty()
  city: string;

  @ApiProperty({ description: 'State/Province', example: 'Île-de-France' })
  @IsString()
  @IsNotEmpty()
  state: string;

  @ApiProperty({ description: 'Postal code', example: '75001' })
  @IsString()
  @IsNotEmpty()
  postalCode: string;

  @ApiProperty({ description: 'Country', example: 'France' })
  @IsString()
  @IsNotEmpty()
  country: string;
}

export class CreatePartnerDto {
  @ApiProperty({ description: 'User ID of the partner', example: '123e4567-e89b-12d3-a456-426614174000' })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({ description: 'Company name', example: 'Wellness Retreat Center' })
  @IsString()
  @IsNotEmpty()
  companyName: string;

  @ApiProperty({
    description: 'Partner type',
    enum: PartnerType,
    example: PartnerType.PREMIUM_CERTIFIED,
  })
  @IsEnum(PartnerType)
  @IsNotEmpty()
  type: PartnerType;

  @ApiProperty({
    description: 'Partner category',
    enum: PartnerCategory,
    example: PartnerCategory.WELLNESS,
  })
  @IsEnum(PartnerCategory)
  @IsNotEmpty()
  category: PartnerCategory;

  @ApiProperty({
    description: 'Description of the partner services',
    example: 'We provide premium wellness retreats in the heart of nature.',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiPropertyOptional({ description: 'Logo URL', example: 'https://example.com/logo.png' })
  @IsUrl()
  @IsOptional()
  logo?: string;

  @ApiPropertyOptional({ description: 'Website URL', example: 'https://example.com' })
  @IsUrl()
  @IsOptional()
  website?: string;

  @ApiProperty({
    description: 'Partner specializations',
    example: ['Yoga', 'Meditation', 'Wellness'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  specializations: string[];

  @ApiProperty({
    description: 'Languages spoken',
    example: ['French', 'English', 'Spanish'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  languages: string[];

  @ApiProperty({ description: 'Contact information' })
  @ValidateNested()
  @Type(() => ContactInfoDto)
  @IsObject()
  contactInfo: ContactInfoDto;

  @ApiProperty({ description: 'Address information' })
  @ValidateNested()
  @Type(() => AddressDto)
  @IsObject()
  address: AddressDto;

  @ApiPropertyOptional({
    description: 'Coverage areas (JSON)',
    example: { regions: ['Paris', 'Lyon', 'Marseille'] },
  })
  @IsObject()
  @IsOptional()
  coverageAreas?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Insurance information (JSON)',
    example: { provider: 'Insurance Co', policyNumber: '12345', coverage: '1000000' },
  })
  @IsObject()
  @IsOptional()
  insurance?: Record<string, any>;
}
