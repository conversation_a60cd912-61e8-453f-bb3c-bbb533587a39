import { Is<PERSON><PERSON>, <PERSON>NotEmpty, IsUUID, IsOptional, IsObject, IsEnum, IsNumber, Min, IsArray } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PayoutMethod } from '../enums/payout-method.enum';

export class CreatePayoutDto {
  @ApiProperty({
    description: 'ID de l\'affilié',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  affiliateId: string;

  @ApiProperty({
    description: 'Montant du paiement',
    example: 99.99,
  })
  @IsNumber()
  @Min(0)
  amount: number;

  @ApiProperty({
    description: 'Méthode de paiement',
    enum: PayoutMethod,
    example: PayoutMethod.BANK_TRANSFER,
  })
  @IsEnum(PayoutMethod)
  @IsNotEmpty()
  method: PayoutMethod;

  @ApiPropertyOptional({
    description: 'IDs des commissions',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID(undefined, { each: true })
  @IsOptional()
  commissionIds?: string[];

  @ApiPropertyOptional({
    description: 'Description',
    example: 'Paiement mensuel des commissions',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Métadonnées',
    example: { period: 'June 2023', paymentDetails: { accountNumber: '*********' } },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
