import { Is<PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsO<PERSON>, IsNumber, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateCommissionDto {
  @ApiProperty({
    description: 'ID de l\'affilié',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  affiliateId: string;

  @ApiProperty({
    description: 'Montant de la commission',
    example: 9.99,
  })
  @IsNumber()
  @Min(0)
  amount: number;

  @ApiPropertyOptional({
    description: 'ID de la conversion',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  conversionId?: string;

  @ApiPropertyOptional({
    description: 'Description',
    example: 'Commission pour l\'achat d\'un produit',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'M<PERSON><PERSON>onn<PERSON>',
    example: { product: 'product-1', commissionRate: 10 },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
