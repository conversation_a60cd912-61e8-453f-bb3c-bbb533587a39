import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  ParseUUIDPipe,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { AffiliateService } from '../services/affiliate.service';
import { ReferralService } from '../services/referral.service';
import { CommissionService } from '../services/commission.service';
import { PayoutService } from '../services/payout.service';
import { CreateAffiliateDto } from '../dto/create-affiliate.dto';
import { UpdateAffiliateDto } from '../dto/update-affiliate.dto';
import { CreateReferralDto } from '../dto/create-referral.dto';
import { CreateReferralByCodeDto } from '../dto/create-referral-by-code.dto';
import { CreatePayoutDto } from '../dto/create-payout.dto';
import { JwtAuthGuard } from '../../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../../common/guards/roles.guard';
import { Roles } from '../../../common/decorators/roles.decorator';
import { AffiliateStatus } from '../enums/affiliate-status.enum';
import { AffiliateLevel } from '../enums/affiliate-level.enum';

@ApiTags('Affiliates')
@Controller('affiliates')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class AffiliateController {
  constructor(
    private readonly affiliateService: AffiliateService,
    private readonly referralService: ReferralService,
    private readonly commissionService: CommissionService,
    private readonly payoutService: PayoutService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Créer un nouvel affilié' })
  @ApiResponse({ status: 201, description: 'Affilié créé avec succès' })
  @ApiResponse({ status: 400, description: 'Données invalides' })
  @ApiBody({ type: CreateAffiliateDto })
  async create(@Body() createAffiliateDto: CreateAffiliateDto) {
    return await this.affiliateService.create(createAffiliateDto);
  }

  @Get()
  @ApiOperation({ summary: 'Récupérer tous les affiliés' })
  @ApiResponse({ status: 200, description: 'Liste des affiliés récupérée avec succès' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'status', required: false, enum: AffiliateStatus, description: 'Statut des affiliés' })
  @Roles('admin')
  async findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('status') status?: AffiliateStatus,
  ) {
    return await this.affiliateService.findAll(page, limit, status);
  }

  @Get('stats')
  @ApiOperation({ summary: 'Récupérer les statistiques des affiliés' })
  @ApiResponse({ status: 200, description: 'Statistiques récupérées avec succès' })
  @Roles('admin')
  async getStats() {
    return await this.affiliateService.getAffiliateStats();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Récupérer un affilié par son ID' })
  @ApiResponse({ status: 200, description: 'Affilié récupéré avec succès' })
  @ApiResponse({ status: 404, description: 'Affilié non trouvé' })
  @ApiParam({ name: 'id', description: 'ID de l\'affilié' })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    return await this.affiliateService.findOne(id);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Récupérer un affilié par l\'ID de l\'utilisateur' })
  @ApiResponse({ status: 200, description: 'Affilié récupéré avec succès' })
  @ApiResponse({ status: 404, description: 'Affilié non trouvé' })
  @ApiParam({ name: 'userId', description: 'ID de l\'utilisateur' })
  async findByUserId(@Param('userId', ParseUUIDPipe) userId: string) {
    return await this.affiliateService.findByUserId(userId);
  }

  @Get('code/:referralCode')
  @ApiOperation({ summary: 'Récupérer un affilié par son code de référence' })
  @ApiResponse({ status: 200, description: 'Affilié récupéré avec succès' })
  @ApiResponse({ status: 404, description: 'Affilié non trouvé' })
  @ApiParam({ name: 'referralCode', description: 'Code de référence' })
  async findByReferralCode(@Param('referralCode') referralCode: string) {
    return await this.affiliateService.findByReferralCode(referralCode);
  }

  @Get(':id/dashboard')
  @ApiOperation({ summary: 'Récupérer le tableau de bord d\'un affilié' })
  @ApiResponse({ status: 200, description: 'Tableau de bord récupéré avec succès' })
  @ApiResponse({ status: 404, description: 'Affilié non trouvé' })
  @ApiParam({ name: 'id', description: 'ID de l\'affilié' })
  async getDashboard(@Param('id', ParseUUIDPipe) id: string) {
    return await this.affiliateService.getDashboard(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Mettre à jour un affilié' })
  @ApiResponse({ status: 200, description: 'Affilié mis à jour avec succès' })
  @ApiResponse({ status: 404, description: 'Affilié non trouvé' })
  @ApiParam({ name: 'id', description: 'ID de l\'affilié' })
  @ApiBody({ type: UpdateAffiliateDto })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateAffiliateDto: UpdateAffiliateDto,
  ) {
    return await this.affiliateService.update(id, updateAffiliateDto);
  }

  @Patch(':id/approve')
  @ApiOperation({ summary: 'Approuver un affilié' })
  @ApiResponse({ status: 200, description: 'Affilié approuvé avec succès' })
  @ApiResponse({ status: 404, description: 'Affilié non trouvé' })
  @ApiParam({ name: 'id', description: 'ID de l\'affilié' })
  @Roles('admin')
  async approve(@Param('id', ParseUUIDPipe) id: string) {
    return await this.affiliateService.approve(id);
  }

  @Patch(':id/reject')
  @ApiOperation({ summary: 'Rejeter un affilié' })
  @ApiResponse({ status: 200, description: 'Affilié rejeté avec succès' })
  @ApiResponse({ status: 404, description: 'Affilié non trouvé' })
  @ApiParam({ name: 'id', description: 'ID de l\'affilié' })
  @ApiQuery({ name: 'reason', required: false, type: String, description: 'Raison du rejet' })
  @Roles('admin')
  async reject(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('reason') reason?: string,
  ) {
    return await this.affiliateService.reject(id, reason);
  }

  @Patch(':id/suspend')
  @ApiOperation({ summary: 'Suspendre un affilié' })
  @ApiResponse({ status: 200, description: 'Affilié suspendu avec succès' })
  @ApiResponse({ status: 404, description: 'Affilié non trouvé' })
  @ApiParam({ name: 'id', description: 'ID de l\'affilié' })
  @ApiQuery({ name: 'reason', required: false, type: String, description: 'Raison de la suspension' })
  @Roles('admin')
  async suspend(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('reason') reason?: string,
  ) {
    return await this.affiliateService.suspend(id, reason);
  }

  @Patch(':id/reactivate')
  @ApiOperation({ summary: 'Réactiver un affilié' })
  @ApiResponse({ status: 200, description: 'Affilié réactivé avec succès' })
  @ApiResponse({ status: 404, description: 'Affilié non trouvé' })
  @ApiParam({ name: 'id', description: 'ID de l\'affilié' })
  @Roles('admin')
  async reactivate(@Param('id', ParseUUIDPipe) id: string) {
    return await this.affiliateService.reactivate(id);
  }

  @Patch(':id/level')
  @ApiOperation({ summary: 'Mettre à jour le niveau d\'un affilié' })
  @ApiResponse({ status: 200, description: 'Niveau d\'affilié mis à jour avec succès' })
  @ApiResponse({ status: 404, description: 'Affilié non trouvé' })
  @ApiParam({ name: 'id', description: 'ID de l\'affilié' })
  @ApiQuery({ name: 'level', required: true, enum: AffiliateLevel, description: 'Niveau de l\'affilié' })
  @ApiQuery({ name: 'commissionRate', required: false, type: Number, description: 'Taux de commission' })
  @Roles('admin')
  async updateLevel(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('level') level: AffiliateLevel,
    @Query('commissionRate') commissionRate?: number,
  ) {
    return await this.affiliateService.updateLevel(id, level, commissionRate);
  }

  @Post('referrals')
  @ApiOperation({ summary: 'Créer une nouvelle référence' })
  @ApiResponse({ status: 201, description: 'Référence créée avec succès' })
  @ApiResponse({ status: 400, description: 'Données invalides' })
  @ApiBody({ type: CreateReferralDto })
  async createReferral(@Body() createReferralDto: CreateReferralDto) {
    return await this.referralService.create(
      createReferralDto.affiliateId,
      createReferralDto.referredUserId,
      createReferralDto.type,
      createReferralDto.metadata
    );
  }

  @Post('referrals/by-code')
  @ApiOperation({ summary: 'Créer une nouvelle référence par code' })
  @ApiResponse({ status: 201, description: 'Référence créée avec succès' })
  @ApiResponse({ status: 400, description: 'Données invalides' })
  @ApiBody({ type: CreateReferralByCodeDto })
  @HttpCode(HttpStatus.CREATED)
  async createReferralByCode(@Body() createReferralByCodeDto: CreateReferralByCodeDto) {
    return await this.referralService.createByCode(createReferralByCodeDto);
  }

  @Get(':id/referrals')
  @ApiOperation({ summary: 'Récupérer les références d\'un affilié' })
  @ApiResponse({ status: 200, description: 'Références récupérées avec succès' })
  @ApiResponse({ status: 404, description: 'Affilié non trouvé' })
  @ApiParam({ name: 'id', description: 'ID de l\'affilié' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  async getReferrals(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return await this.referralService.findByAffiliateId(id, page, limit);
  }

  @Get(':id/commissions')
  @ApiOperation({ summary: 'Récupérer les commissions d\'un affilié' })
  @ApiResponse({ status: 200, description: 'Commissions récupérées avec succès' })
  @ApiResponse({ status: 404, description: 'Affilié non trouvé' })
  @ApiParam({ name: 'id', description: 'ID de l\'affilié' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  async getCommissions(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return await this.commissionService.findByAffiliateId(id, page, limit);
  }

  @Post('payouts')
  @ApiOperation({ summary: 'Créer un nouveau paiement' })
  @ApiResponse({ status: 201, description: 'Paiement créé avec succès' })
  @ApiResponse({ status: 400, description: 'Données invalides' })
  @ApiBody({ type: CreatePayoutDto })
  @Roles('admin')
  async createPayout(@Body() createPayoutDto: CreatePayoutDto) {
    return await this.payoutService.create(
      createPayoutDto.affiliateId,
      createPayoutDto.amount,
      createPayoutDto.method,
      createPayoutDto.commissionIds,
      createPayoutDto.description,
      createPayoutDto.metadata
    );
  }

  @Get(':id/payouts')
  @ApiOperation({ summary: 'Récupérer les paiements d\'un affilié' })
  @ApiResponse({ status: 200, description: 'Paiements récupérés avec succès' })
  @ApiResponse({ status: 404, description: 'Affilié non trouvé' })
  @ApiParam({ name: 'id', description: 'ID de l\'affilié' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  async getPayouts(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return await this.payoutService.findByAffiliateId(id, page, limit);
  }
}
