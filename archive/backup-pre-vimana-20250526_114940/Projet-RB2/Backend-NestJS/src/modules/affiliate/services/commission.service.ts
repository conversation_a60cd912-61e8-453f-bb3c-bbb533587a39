import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventsService } from '../../events/events.service';
import { CommissionStatus } from '../enums/commission-status.enum';

@Injectable()
export class CommissionService {
  private readonly logger = new Logger(CommissionService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventsService: EventsService,
  ) {}

  /**
   * Crée une nouvelle commission
   * @param affiliateId ID de l'affilié
   * @param amount Montant de la commission
   * @param conversionId ID de la conversion
   * @param description Description
   * @param metadata Métadonnées
   * @returns La commission créée
   */
  async create(
    affiliateId: string,
    amount: number,
    conversionId?: string,
    description?: string,
    metadata: Record<string, any> = {},
  ) {
    try {
      this.logger.log(`Création d'une nouvelle commission: affilié ${affiliateId}, montant ${amount}`);
      
      // Vérifier si l'affilié existe
      const affiliate = await this.prisma.affiliate.findUnique({
        where: { id: affiliateId },
      });
      
      if (!affiliate) {
        throw new NotFoundException(`Affilié avec l'ID ${affiliateId} non trouvé`);
      }
      
      // Créer la commission
      const commission = await this.prisma.commission.create({
        data: {
          affiliateId,
          amount,
          conversionId,
          description,
          status: CommissionStatus.PENDING,
          metadata,
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'COMMISSION_CREATED',
        payload: {
          commissionId: commission.id,
          affiliateId: commission.affiliateId,
          amount: commission.amount,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Commission créée avec succès: ${commission.id}`);
      return commission;
    } catch (error) {
      this.logger.error(`Erreur lors de la création de la commission: ${error.message}`);
      throw error;
    }
  }

  /**
   * Crée une commission à partir d'une conversion
   * @param conversionId ID de la conversion
   * @returns La commission créée
   */
  async createFromConversion(conversionId: string) {
    try {
      this.logger.log(`Création d'une commission à partir de la conversion ${conversionId}`);
      
      // Récupérer la conversion
      const conversion = await this.prisma.conversion.findUnique({
        where: { id: conversionId },
        include: {
          affiliate: true,
        },
      });
      
      if (!conversion) {
        throw new NotFoundException(`Conversion avec l'ID ${conversionId} non trouvée`);
      }
      
      // Calculer le montant de la commission
      const commissionRate = conversion.affiliate.commissionRate;
      const commissionAmount = (conversion.amount * commissionRate) / 100;
      
      // Créer la commission
      return this.create(
        conversion.affiliateId,
        commissionAmount,
        conversionId,
        `Commission pour la conversion ${conversionId}`,
        {
          conversionType: conversion.type,
          conversionAmount: conversion.amount,
          commissionRate,
        },
      );
    } catch (error) {
      this.logger.error(`Erreur lors de la création de la commission à partir de la conversion: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère toutes les commissions avec pagination
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @param status Statut des commissions
   * @returns Liste paginée des commissions
   */
  async findAll(page = 1, limit = 10, status?: CommissionStatus) {
    const skip = (page - 1) * limit;
    
    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    const [commissions, total] = await Promise.all([
      this.prisma.commission.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          affiliate: {
            select: {
              id: true,
              userId: true,
              referralCode: true,
              user: {
                select: {
                  id: true,
                  email: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          conversion: true,
          payout: true,
        },
      }),
      this.prisma.commission.count({ where }),
    ]);
    
    return {
      commissions,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère une commission par son ID
   * @param id ID de la commission
   * @returns La commission
   */
  async findOne(id: string) {
    const commission = await this.prisma.commission.findUnique({
      where: { id },
      include: {
        affiliate: {
          select: {
            id: true,
            userId: true,
            referralCode: true,
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        conversion: true,
        payout: true,
      },
    });
    
    if (!commission) {
      throw new NotFoundException(`Commission avec l'ID ${id} non trouvée`);
    }
    
    return commission;
  }

  /**
   * Récupère les commissions d'un affilié
   * @param affiliateId ID de l'affilié
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @param status Statut des commissions
   * @returns Liste paginée des commissions
   */
  async findByAffiliateId(affiliateId: string, page = 1, limit = 10, status?: CommissionStatus) {
    const skip = (page - 1) * limit;
    
    const where: any = {
      affiliateId,
    };
    
    if (status) {
      where.status = status;
    }
    
    const [commissions, total] = await Promise.all([
      this.prisma.commission.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          conversion: true,
          payout: true,
        },
      }),
      this.prisma.commission.count({ where }),
    ]);
    
    return {
      commissions,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère les commissions d'une conversion
   * @param conversionId ID de la conversion
   * @returns Liste des commissions
   */
  async findByConversionId(conversionId: string) {
    return this.prisma.commission.findMany({
      where: { conversionId },
      include: {
        payout: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  /**
   * Met à jour le statut d'une commission
   * @param id ID de la commission
   * @param status Nouveau statut
   * @returns La commission mise à jour
   */
  async updateStatus(id: string, status: CommissionStatus) {
    try {
      // Vérifier si la commission existe
      const commission = await this.findOne(id);
      
      // Mettre à jour le statut
      const updatedCommission = await this.prisma.commission.update({
        where: { id },
        data: { status },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'COMMISSION_STATUS_UPDATED',
        payload: {
          commissionId: updatedCommission.id,
          oldStatus: commission.status,
          newStatus: status,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Statut de la commission mis à jour avec succès: ${updatedCommission.id}, statut: ${status}`);
      return updatedCommission;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du statut de la commission: ${error.message}`);
      throw error;
    }
  }

  /**
   * Approuve une commission
   * @param id ID de la commission
   * @returns La commission approuvée
   */
  async approve(id: string) {
    try {
      // Vérifier si la commission existe
      const commission = await this.findOne(id);
      
      // Vérifier si la commission est déjà approuvée
      if (commission.status === CommissionStatus.APPROVED) {
        throw new BadRequestException(`La commission est déjà approuvée`);
      }
      
      // Mettre à jour le statut
      const updatedCommission = await this.prisma.commission.update({
        where: { id },
        data: {
          status: CommissionStatus.APPROVED,
          approvedAt: new Date(),
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'COMMISSION_APPROVED',
        payload: {
          commissionId: updatedCommission.id,
          affiliateId: updatedCommission.affiliateId,
          amount: updatedCommission.amount,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Commission approuvée avec succès: ${updatedCommission.id}`);
      return updatedCommission;
    } catch (error) {
      this.logger.error(`Erreur lors de l'approbation de la commission: ${error.message}`);
      throw error;
    }
  }

  /**
   * Rejette une commission
   * @param id ID de la commission
   * @param reason Raison du rejet
   * @returns La commission rejetée
   */
  async reject(id: string, reason?: string) {
    try {
      // Vérifier si la commission existe
      const commission = await this.findOne(id);
      
      // Vérifier si la commission est déjà rejetée
      if (commission.status === CommissionStatus.REJECTED) {
        throw new BadRequestException(`La commission est déjà rejetée`);
      }
      
      // Mettre à jour le statut
      const updatedCommission = await this.prisma.commission.update({
        where: { id },
        data: {
          status: CommissionStatus.REJECTED,
          rejectionReason: reason,
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'COMMISSION_REJECTED',
        payload: {
          commissionId: updatedCommission.id,
          affiliateId: updatedCommission.affiliateId,
          reason,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Commission rejetée avec succès: ${updatedCommission.id}`);
      return updatedCommission;
    } catch (error) {
      this.logger.error(`Erreur lors du rejet de la commission: ${error.message}`);
      throw error;
    }
  }

  /**
   * Approuve les commissions d'une conversion
   * @param conversionId ID de la conversion
   * @returns Liste des commissions approuvées
   */
  async approveByConversionId(conversionId: string) {
    try {
      // Récupérer les commissions de la conversion
      const commissions = await this.findByConversionId(conversionId);
      
      // Approuver chaque commission
      const approvedCommissions = await Promise.all(
        commissions.map((commission: any) => this.approve(commission.id)),
      );
      
      return approvedCommissions;
    } catch (error) {
      this.logger.error(`Erreur lors de l'approbation des commissions de la conversion: ${error.message}`);
      throw error;
    }
  }

  /**
   * Rejette les commissions d'une conversion
   * @param conversionId ID de la conversion
   * @param reason Raison du rejet
   * @returns Liste des commissions rejetées
   */
  async rejectByConversionId(conversionId: string, reason?: string) {
    try {
      // Récupérer les commissions de la conversion
      const commissions = await this.findByConversionId(conversionId);
      
      // Rejeter chaque commission
      const rejectedCommissions = await Promise.all(
        commissions.map((commission: any) => this.reject(commission.id, reason)),
      );
      
      return rejectedCommissions;
    } catch (error) {
      this.logger.error(`Erreur lors du rejet des commissions de la conversion: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère le solde disponible d'un affilié
   * @param affiliateId ID de l'affilié
   * @returns Solde disponible
   */
  async getAvailableBalance(affiliateId: string) {
    try {
      // Calculer le montant total des commissions approuvées
      const totalCommissions = await this.prisma.commission.aggregate({
        where: {
          affiliateId,
          status: CommissionStatus.APPROVED,
        },
        _sum: {
          amount: true,
        },
      });
      
      // Calculer le montant total des paiements
      const totalPayouts = await this.prisma.payout.aggregate({
        where: {
          affiliateId,
        },
        _sum: {
          amount: true,
        },
      });
      
      // Calculer le solde disponible
      const availableBalance = (totalCommissions._sum.amount || 0) - (totalPayouts._sum.amount || 0);
      
      return {
        availableBalance,
        totalCommissions: totalCommissions._sum.amount || 0,
        totalPayouts: totalPayouts._sum.amount || 0,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération du solde disponible: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les commissions disponibles pour le paiement
   * @param affiliateId ID de l'affilié
   * @returns Liste des commissions disponibles
   */
  async getAvailableCommissions(affiliateId: string) {
    return this.prisma.commission.findMany({
      where: {
        affiliateId,
        status: CommissionStatus.APPROVED,
        payoutId: null,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });
  }
}
