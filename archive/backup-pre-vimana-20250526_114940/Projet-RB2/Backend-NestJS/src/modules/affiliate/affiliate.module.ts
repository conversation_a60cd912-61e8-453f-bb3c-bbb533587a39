import { Module } from '@nestjs/common';
import { AffiliateService } from './services/affiliate.service';
import { AffiliateController } from './controllers/affiliate.controller';
import { PrismaModule } from '../../prisma/prisma.module';
import { ReferralService } from './services/referral.service';
import { CommissionService } from './services/commission.service';
import { ConversionService } from './services/conversion.service';
import { PayoutService } from './services/payout.service';
import { EventsModule } from '../events/events.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    PrismaModule,
    EventsModule,
    UsersModule,
  ],
  controllers: [AffiliateController],
  providers: [
    AffiliateService,
    ReferralService,
    CommissionService,
    ConversionService,
    PayoutService,
  ],
  exports: [
    AffiliateService,
    ReferralService,
    CommissionService,
  ],
})
export class AffiliateModule {}
