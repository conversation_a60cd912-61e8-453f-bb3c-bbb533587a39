import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { CreateAuditLogDto } from '../dto/create-audit-log.dto';

@Injectable()
export class AuditLogService {
  private readonly logger = new Logger(AuditLogService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Crée un log d'audit
   * @param createAuditLogDto Données du log d'audit
   * @returns Le log d'audit créé
   */
  async createLog(createAuditLogDto: CreateAuditLogDto) {
    try {
      const auditLog = await this.prisma.auditLog.create({
        data: {
          userId: createAuditLogDto.userId,
          action: createAuditLogDto.action,
          entity: createAuditLogDto.entity,
          entityId: createAuditLogDto.entityId,
          oldValues: createAuditLogDto.oldValues || {},
          newValues: createAuditLogDto.newValues || {},
          metadata: createAuditLogDto.metadata || {},
          ip: createAuditLogDto.ip,
          userAgent: createAuditLogDto.userAgent,
        },
      });

      this.logger.log(`Log d'audit créé: ${auditLog.id}`);
      return auditLog;
    } catch (error) {
      this.logger.error(`Erreur lors de la création du log d'audit: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère tous les logs d'audit avec pagination
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @returns Liste paginée des logs d'audit
   */
  async findAll(page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    const [auditLogs, total] = await Promise.all([
      this.prisma.auditLog.findMany({
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
            },
          },
        },
      }),
      this.prisma.auditLog.count(),
    ]);

    return {
      auditLogs,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère un log d'audit par son ID
   * @param id ID du log d'audit
   * @returns Le log d'audit
   */
  async findOne(id: string) {
    return this.prisma.auditLog.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
          },
        },
      },
    });
  }

  /**
   * Récupère les logs d'audit pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @returns Liste paginée des logs d'audit de l'utilisateur
   */
  async findByUser(userId: string, page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    const [auditLogs, total] = await Promise.all([
      this.prisma.auditLog.findMany({
        where: { userId },
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
            },
          },
        },
      }),
      this.prisma.auditLog.count({
        where: { userId },
      }),
    ]);

    return {
      auditLogs,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère les logs d'audit pour une entité
   * @param entity Type d'entité
   * @param entityId ID de l'entité
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @returns Liste paginée des logs d'audit pour l'entité
   */
  async findByEntity(entity: string, entityId: string, page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    const [auditLogs, total] = await Promise.all([
      this.prisma.auditLog.findMany({
        where: { entity, entityId },
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
            },
          },
        },
      }),
      this.prisma.auditLog.count({
        where: { entity, entityId },
      }),
    ]);

    return {
      auditLogs,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère les logs d'audit pour une action
   * @param action Action
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @returns Liste paginée des logs d'audit pour l'action
   */
  async findByAction(action: string, page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    const [auditLogs, total] = await Promise.all([
      this.prisma.auditLog.findMany({
        where: { action },
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
            },
          },
        },
      }),
      this.prisma.auditLog.count({
        where: { action },
      }),
    ]);

    return {
      auditLogs,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère les logs d'audit pour une période
   * @param startDate Date de début
   * @param endDate Date de fin
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @returns Liste paginée des logs d'audit pour la période
   */
  async findByDateRange(startDate: Date, endDate: Date, page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    const [auditLogs, total] = await Promise.all([
      this.prisma.auditLog.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
            },
          },
        },
      }),
      this.prisma.auditLog.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      }),
    ]);

    return {
      auditLogs,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }
}
