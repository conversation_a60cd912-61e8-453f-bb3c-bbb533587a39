import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { AuditService } from './audit.service';
import { ComplianceService } from './compliance.service';
import { ReportType } from '../enums/report-type.enum';
import { ReportFormat } from '../enums/report-format.enum';
import { CreateReportDto } from '../dto/create-report.dto';

@Injectable()
export class ReportingService {
  private readonly logger = new Logger(ReportingService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly auditService: AuditService,
    private readonly complianceService: ComplianceService,
  ) {}

  /**
   * Crée un rapport
   * @param createReportDto Données du rapport
   * @returns Le rapport créé
   */
  async createReport(createReportDto: CreateReportDto) {
    try {
      // Générer le contenu du rapport
      const content = await this.generateReportContent(
        createReportDto.type,
        createReportDto.startDate,
        createReportDto.endDate,
        createReportDto.format,
        createReportDto.options,
      );

      // Créer le rapport dans la base de données
      const report = await this.prisma.report.create({
        data: {
          type: createReportDto.type,
          format: createReportDto.format,
          startDate: createReportDto.startDate,
          endDate: createReportDto.endDate,
          content,
          generatedBy: createReportDto.generatedBy,
          options: createReportDto.options || {},
        },
      });

      this.logger.log(`Rapport créé: ${report.id}`);
      return report;
    } catch (error) {
      this.logger.error(`Erreur lors de la création du rapport: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère tous les rapports avec pagination
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @returns Liste paginée des rapports
   */
  async findAll(page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    const [reports, total] = await Promise.all([
      this.prisma.report.findMany({
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
            },
          },
        },
      }),
      this.prisma.report.count(),
    ]);

    return {
      reports,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère un rapport par son ID
   * @param id ID du rapport
   * @returns Le rapport
   */
  async findOne(id: string) {
    return this.prisma.report.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
          },
        },
      },
    });
  }

  /**
   * Récupère les rapports par type
   * @param type Type de rapport
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @returns Liste paginée des rapports par type
   */
  async findByType(type: ReportType, page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    const [reports, total] = await Promise.all([
      this.prisma.report.findMany({
        where: { type },
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
            },
          },
        },
      }),
      this.prisma.report.count({
        where: { type },
      }),
    ]);

    return {
      reports,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère les rapports pour une période
   * @param startDate Date de début
   * @param endDate Date de fin
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @returns Liste paginée des rapports pour la période
   */
  async findByDateRange(startDate: Date, endDate: Date, page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    const [reports, total] = await Promise.all([
      this.prisma.report.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
            },
          },
        },
      }),
      this.prisma.report.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      }),
    ]);

    return {
      reports,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Génère le contenu d'un rapport
   * @param type Type de rapport
   * @param startDate Date de début
   * @param endDate Date de fin
   * @param format Format du rapport
   * @param options Options du rapport
   * @returns Contenu du rapport
   */
  private async generateReportContent(
    type: ReportType,
    startDate: Date,
    endDate: Date,
    format: ReportFormat,
    options?: Record<string, any>,
  ) {
    switch (type) {
      case ReportType.AUDIT:
        return this.generateAuditReport(startDate, endDate, format, options);
      case ReportType.COMPLIANCE:
        return this.generateComplianceReport(startDate, endDate, format, options);
      case ReportType.SECURITY:
        return this.generateSecurityReport(startDate, endDate, format, options);
      case ReportType.USER_ACTIVITY:
        return this.generateUserActivityReport(startDate, endDate, format, options);
      default:
        throw new Error(`Type de rapport non pris en charge: ${type}`);
    }
  }

  /**
   * Génère un rapport d'audit
   * @param startDate Date de début
   * @param endDate Date de fin
   * @param format Format du rapport
   * @param options Options du rapport
   * @returns Contenu du rapport d'audit
   */
  private async generateAuditReport(
    startDate: Date,
    endDate: Date,
    format: ReportFormat,
    options?: Record<string, any>,
  ) {
    const auditReport = await this.auditService.generateReport({
      startDate,
      endDate,
      ...options,
      action: options?.action ?? '',
      resource: options?.resource ?? '',
    });

    // Formater le rapport selon le format demandé
    switch (format) {
      case ReportFormat.JSON:
        return JSON.stringify(auditReport, null, 2);
      case ReportFormat.CSV:
        return this.convertToCSV(auditReport);
      case ReportFormat.PDF:
        return this.convertToPDF(auditReport);
      case ReportFormat.HTML:
        return this.convertToHTML(auditReport);
      default:
        return JSON.stringify(auditReport, null, 2);
    }
  }

  /**
   * Génère un rapport de conformité
   * @param startDate Date de début
   * @param endDate Date de fin
   * @param format Format du rapport
   * @param options Options du rapport
   * @returns Contenu du rapport de conformité
   */
  private async generateComplianceReport(
    startDate: Date,
    endDate: Date,
    format: ReportFormat,
    options?: Record<string, any>,
  ) {
    // Récupérer les contrôles de conformité pour la période
    const { complianceChecks } = await this.complianceService.findByDateRange(
      startDate,
      endDate,
    );

    // Analyser les résultats
    const complianceReport = {
      period: {
        startDate,
        endDate,
      },
      summary: {
        totalChecks: complianceChecks.length,
        compliantChecks: complianceChecks.filter((check: any) => check.status === 'COMPLIANT').length,
        partiallyCompliantChecks: complianceChecks.filter((check: any) => check.status === 'PARTIALLY_COMPLIANT').length,
        nonCompliantChecks: complianceChecks.filter((check: any) => check.status === 'NON_COMPLIANT').length,
      },
      checksByType: this.groupComplianceChecksByType(complianceChecks),
      issues: this.extractComplianceIssues(complianceChecks),
      recommendations: this.generateComplianceRecommendations(complianceChecks),
      details: complianceChecks,
    };

    // Formater le rapport selon le format demandé
    switch (format) {
      case ReportFormat.JSON:
        return JSON.stringify(complianceReport, null, 2);
      case ReportFormat.CSV:
        return this.convertToCSV(complianceReport);
      case ReportFormat.PDF:
        return this.convertToPDF(complianceReport);
      case ReportFormat.HTML:
        return this.convertToHTML(complianceReport);
      default:
        return JSON.stringify(complianceReport, null, 2);
    }
  }

  /**
   * Génère un rapport de sécurité
   * @param startDate Date de début
   * @param endDate Date de fin
   * @param format Format du rapport
   * @param options Options du rapport
   * @returns Contenu du rapport de sécurité
   */
  private async generateSecurityReport(
    startDate: Date,
    endDate: Date,
    format: ReportFormat,
    options?: Record<string, any>,
  ) {
    // Récupérer les événements de sécurité pour la période
    const securityEvents = await this.prisma.securityEvent.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Récupérer les scans de sécurité pour la période
    const securityScans = await this.prisma.securityScan.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Récupérer les IPs bloquées pour la période
    const blockedIPs = await this.prisma.blockedIP.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Analyser les résultats
    const securityReport = {
      period: {
        startDate,
        endDate,
      },
      summary: {
        totalEvents: securityEvents.length,
        highSeverityEvents: securityEvents.filter((event: any) => event.severity === 'HIGH').length,
        mediumSeverityEvents: securityEvents.filter((event: any) => event.severity === 'MEDIUM').length,
        lowSeverityEvents: securityEvents.filter((event: any) => event.severity === 'LOW').length,
        totalScans: securityScans.length,
        totalBlockedIPs: blockedIPs.length,
      },
      eventsByType: this.groupSecurityEventsByType(securityEvents),
      scanResults: this.summarizeSecurityScans(securityScans),
      blockedIPs: this.summarizeBlockedIPs(blockedIPs),
      recommendations: this.generateSecurityRecommendations(securityEvents, securityScans),
      details: {
        events: securityEvents,
        scans: securityScans,
        blockedIPs,
      },
    };

    // Formater le rapport selon le format demandé
    switch (format) {
      case ReportFormat.JSON:
        return JSON.stringify(securityReport, null, 2);
      case ReportFormat.CSV:
        return this.convertToCSV(securityReport);
      case ReportFormat.PDF:
        return this.convertToPDF(securityReport);
      case ReportFormat.HTML:
        return this.convertToHTML(securityReport);
      default:
        return JSON.stringify(securityReport, null, 2);
    }
  }

  /**
   * Génère un rapport d'activité utilisateur
   * @param startDate Date de début
   * @param endDate Date de fin
   * @param format Format du rapport
   * @param options Options du rapport
   * @returns Contenu du rapport d'activité utilisateur
   */
  private async generateUserActivityReport(
    startDate: Date,
    endDate: Date,
    format: ReportFormat,
    options?: Record<string, any>,
  ) {
    // Récupérer les activités utilisateur pour la période
    const userActivities = await this.prisma.activity.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Analyser les résultats
    const userActivityReport = {
      period: {
        startDate,
        endDate,
      },
      summary: {
        totalActivities: userActivities.length,
        uniqueUsers: new Set(userActivities.map((activity: any) => activity.userId)).size,
        activitiesByDay: this.groupActivitiesByDay(userActivities),
      },
      activitiesByType: this.groupActivitiesByType(userActivities),
      mostActiveUsers: this.getMostActiveUsers(userActivities),
      details: userActivities,
    };

    // Formater le rapport selon le format demandé
    switch (format) {
      case ReportFormat.JSON:
        return JSON.stringify(userActivityReport, null, 2);
      case ReportFormat.CSV:
        return this.convertToCSV(userActivityReport);
      case ReportFormat.PDF:
        return this.convertToPDF(userActivityReport);
      case ReportFormat.HTML:
        return this.convertToHTML(userActivityReport);
      default:
        return JSON.stringify(userActivityReport, null, 2);
    }
  }

  /**
   * Groupe les contrôles de conformité par type
   * @param complianceChecks Contrôles de conformité
   * @returns Contrôles de conformité groupés par type
   */
  private groupComplianceChecksByType(complianceChecks: any[]) {
    const groups: Record<string, any[]> = {};
    
    complianceChecks.forEach(check => {
      if (!groups[check.type]) {
        groups[check.type] = [];
      }
      groups[check.type].push(check);
    });
    
    return Object.entries(groups).map(([type, checks]) => ({
      type,
      count: checks.length,
      compliantCount: checks.filter(check => check.status === 'COMPLIANT').length,
      partiallyCompliantCount: checks.filter(check => check.status === 'PARTIALLY_COMPLIANT').length,
      nonCompliantCount: checks.filter(check => check.status === 'NON_COMPLIANT').length,
    }));
  }

  /**
   * Extrait les problèmes de conformité
   * @param complianceChecks Contrôles de conformité
   * @returns Problèmes de conformité
   */
  private extractComplianceIssues(complianceChecks: any[]) {
    const issues: any[] = [];
    
    complianceChecks.forEach(check => {
      if (check.issues && check.issues.length > 0) {
        check.issues.forEach((issue: any) => {
          issues.push({
            ...issue,
            checkId: check.id,
            checkType: check.type,
            checkDate: check.createdAt,
          });
        });
      }
    });
    
    return issues as any[];
  }

  /**
   * Génère des recommandations de conformité
   * @param complianceChecks Contrôles de conformité
   * @returns Recommandations de conformité
   */
  private generateComplianceRecommendations(complianceChecks: any[]) {
    const recommendations = new Set<string>();
    
    complianceChecks.forEach(check => {
      if (check.issues && check.issues.length > 0) {
        check.issues.forEach((issue: any) => {
          if (issue.recommendation) {
            recommendations.add(issue.recommendation);
          }
        });
      }
    });
    
    return Array.from(recommendations);
  }

  /**
   * Groupe les événements de sécurité par type
   * @param securityEvents Événements de sécurité
   * @returns Événements de sécurité groupés par type
   */
  private groupSecurityEventsByType(securityEvents: any[]) {
    const groups: Record<string, any[]> = {};
    
    securityEvents.forEach(event => {
      if (!groups[event.type]) {
        groups[event.type] = [];
      }
      groups[event.type].push(event);
    });
    
    return Object.entries(groups).map(([type, events]) => ({
      type,
      count: events.length,
      highSeverityCount: events.filter(event => event.severity === 'HIGH').length,
      mediumSeverityCount: events.filter(event => event.severity === 'MEDIUM').length,
      lowSeverityCount: events.filter(event => event.severity === 'LOW').length,
    }));
  }

  /**
   * Résume les scans de sécurité
   * @param securityScans Scans de sécurité
   * @returns Résumé des scans de sécurité
   */
  private summarizeSecurityScans(securityScans: any[]) {
    return {
      totalScans: securityScans.length,
      completedScans: securityScans.filter(scan => scan.status === 'COMPLETED').length,
      failedScans: securityScans.filter(scan => scan.status === 'FAILED').length,
      totalVulnerabilities: securityScans.reduce((sum, scan) => sum + (scan.vulnerabilityCount || 0), 0),
      highSeverityVulnerabilities: securityScans.reduce((sum, scan) => sum + (scan.highSeverityCount || 0), 0),
      mediumSeverityVulnerabilities: securityScans.reduce((sum, scan) => sum + (scan.mediumSeverityCount || 0), 0),
      lowSeverityVulnerabilities: securityScans.reduce((sum, scan) => sum + (scan.lowSeverityCount || 0), 0),
      scansByType: this.groupScansByType(securityScans),
    };
  }

  /**
   * Groupe les scans de sécurité par type
   * @param securityScans Scans de sécurité
   * @returns Scans de sécurité groupés par type
   */
  private groupScansByType(securityScans: any[]) {
    const groups: Record<string, any[]> = {};
    
    securityScans.forEach(scan => {
      if (!groups[scan.type]) {
        groups[scan.type] = [];
      }
      groups[scan.type].push(scan);
    });
    
    return Object.entries(groups).map(([type, scans]) => ({
      type,
      count: scans.length,
      completedCount: scans.filter(scan => scan.status === 'COMPLETED').length,
      failedCount: scans.filter(scan => scan.status === 'FAILED').length,
      totalVulnerabilities: scans.reduce((sum, scan) => sum + (scan.vulnerabilityCount || 0), 0),
    }));
  }

  /**
   * Résume les IPs bloquées
   * @param blockedIPs IPs bloquées
   * @returns Résumé des IPs bloquées
   */
  private summarizeBlockedIPs(blockedIPs: any[]) {
    return {
      totalBlockedIPs: blockedIPs.length,
      activeBlockedIPs: blockedIPs.filter(ip => new Date(ip.expiresAt) > new Date()).length,
      expiredBlockedIPs: blockedIPs.filter(ip => new Date(ip.expiresAt) <= new Date()).length,
      reasonsForBlocking: this.groupBlockedIPsByReason(blockedIPs),
    };
  }

  /**
   * Groupe les IPs bloquées par raison
   * @param blockedIPs IPs bloquées
   * @returns IPs bloquées groupées par raison
   */
  private groupBlockedIPsByReason(blockedIPs: any[]) {
    const groups: Record<string, any[]> = {};
    
    blockedIPs.forEach(ip => {
      if (!groups[ip.reason]) {
        groups[ip.reason] = [];
      }
      groups[ip.reason].push(ip);
    });
    
    return Object.entries(groups).map(([reason, ips]) => ({
      reason,
      count: ips.length,
    }));
  }

  /**
   * Génère des recommandations de sécurité
   * @param securityEvents Événements de sécurité
   * @param securityScans Scans de sécurité
   * @returns Recommandations de sécurité
   */
  private generateSecurityRecommendations(securityEvents: any[], securityScans: any[]) {
    const recommendations = [
      'Mettre à jour régulièrement les dépendances pour corriger les vulnérabilités connues',
      'Configurer correctement les en-têtes de sécurité HTTP',
      'Mettre en place une politique de mots de passe forts',
      'Activer l\'authentification à deux facteurs pour tous les utilisateurs',
      'Surveiller régulièrement les journaux de sécurité',
    ];
    
    // Ajouter des recommandations spécifiques en fonction des événements de sécurité
    if (securityEvents.some(event => event.type === 'BRUTE_FORCE_ATTEMPT')) {
      recommendations.push('Renforcer la protection contre les attaques par force brute');
    }
    
    if (securityEvents.some(event => event.type === 'SUSPICIOUS_IP')) {
      recommendations.push('Mettre en place une liste d\'IPs autorisées pour les opérations sensibles');
    }
    
    // Ajouter des recommandations spécifiques en fonction des scans de sécurité
    const vulnerableScans = securityScans.filter(scan => scan.vulnerabilityCount > 0);
    if (vulnerableScans.length > 0) {
      recommendations.push('Corriger les vulnérabilités identifiées lors des scans de sécurité');
    }
    
    return recommendations;
  }

  /**
   * Groupe les activités par jour
   * @param activities Activités
   * @returns Activités groupées par jour
   */
  private groupActivitiesByDay(activities: any[]) {
    const groups: Record<string, number> = {};
    
    activities.forEach(activity => {
      const day = activity.createdAt.toISOString().split('T')[0];
      if (!groups[day]) {
        groups[day] = 0;
      }
      groups[day]++;
    });
    
    return Object.entries(groups).map(([day, count]) => ({
      day,
      count,
    }));
  }

  /**
   * Groupe les activités par type
   * @param activities Activités
   * @returns Activités groupées par type
   */
  private groupActivitiesByType(activities: any[]) {
    const groups: Record<string, any[]> = {};
    
    activities.forEach(activity => {
      if (!groups[activity.type]) {
        groups[activity.type] = [];
      }
      groups[activity.type].push(activity);
    });
    
    return Object.entries(groups).map(([type, activities]) => ({
      type,
      count: activities.length,
    }));
  }

  /**
   * Récupère les utilisateurs les plus actifs
   * @param activities Activités
   * @param limit Nombre d'utilisateurs à récupérer
   * @returns Utilisateurs les plus actifs
   */
  private getMostActiveUsers(activities: any[], limit = 10) {
    const userActivities: Record<string, { user: any; count: number }> = {};
    
    activities.forEach(activity => {
      if (!userActivities[activity.userId]) {
        userActivities[activity.userId] = {
          user: activity.user,
          count: 0,
        };
      }
      userActivities[activity.userId].count++;
    });
    
    return Object.values(userActivities)
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  /**
   * Convertit un objet en CSV
   * @param data Données à convertir
   * @returns Données au format CSV
   */
  private convertToCSV(data: any): string {
    // Dans une implémentation réelle, on utiliserait une bibliothèque comme json2csv
    return 'CSV data';
  }

  /**
   * Convertit un objet en PDF
   * @param data Données à convertir
   * @returns Données au format PDF
   */
  private convertToPDF(data: any): string {
    // Dans une implémentation réelle, on utiliserait une bibliothèque comme PDFKit
    return 'PDF data';
  }

  /**
   * Convertit un objet en HTML
   * @param data Données à convertir
   * @returns Données au format HTML
   */
  private convertToHTML(data: any): string {
    // Dans une implémentation réelle, on utiliserait un moteur de template comme Handlebars
    return `
      <html>
        <head>
          <title>Rapport</title>
          <style>
            body { font-family: Arial, sans-serif; }
            h1 { color: #333; }
            table { border-collapse: collapse; width: 100%; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
          </style>
        </head>
        <body>
          <h1>Rapport</h1>
          <pre>${JSON.stringify(data, null, 2)}</pre>
        </body>
      </html>
    `;
  }
}
