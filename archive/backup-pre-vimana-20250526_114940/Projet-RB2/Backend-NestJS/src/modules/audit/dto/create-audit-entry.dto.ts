import { IsString, IsNotEmpty, IsOptional, IsObject, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateAuditEntryDto {
  @ApiProperty({
    description: 'ID de l\'utilisateur qui a effectué l\'action',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Action effectuée',
    example: 'CREATE_USER',
  })
  @IsString()
  @IsNotEmpty()
  action: string;

  @ApiProperty({
    description: 'Ressource concernée',
    example: 'USER',
  })
  @IsString()
  @IsNotEmpty()
  resource: string;

  @ApiPropertyOptional({
    description: 'ID de la ressource concernée',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsOptional()
  resourceId?: string;

  @ApiPropertyOptional({
    description: 'Détails de l\'action',
    example: { name: '<PERSON>', email: '<EMAIL>' },
  })
  @IsObject()
  @IsOptional()
  details?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Adresse IP de l\'utilisateur',
    example: '***********',
  })
  @IsString()
  @IsOptional()
  ip?: string;

  @ApiPropertyOptional({
    description: 'User-Agent de l\'utilisateur',
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  })
  @IsString()
  @IsOptional()
  userAgent?: string;
}
