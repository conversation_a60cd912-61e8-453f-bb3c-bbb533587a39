import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { AuditService } from '../services/audit.service';
import { ComplianceService } from '../services/compliance.service';
import { CreateAuditEntryDto } from '../dto/create-audit-entry.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles, UserRole } from '../../auth/decorators/roles.decorator';
import { CurrentUser } from '../../../common/decorators';
import { ParseObjectIdPipe } from '../../../common/pipes/parse-object-id.pipe';
import { AuditOptions } from '../../../shared/interfaces';
import { Type } from 'class-transformer';
import { IsOptional, IsString, IsDate, IsInt, Min, IsEnum } from 'class-validator';

class AuditQueryDto implements AuditOptions {

  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number;


  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  limit?: number;


  @IsOptional()
  @IsString()
  sortBy?: string;


  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc';


  @IsOptional()
  @IsString()
  userId?: string;


  @IsOptional()
  @IsString()
  action: string = '';


  @IsOptional()
  @IsString()
  resource: string = '';


  @IsOptional()
  @IsDate()
  @Type(() => Date)
  startDate?: Date;


  @IsOptional()
  @IsDate()
  @Type(() => Date)
  endDate?: Date;
}

@ApiTags('audit')
@Controller('audit')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class AuditController {
  constructor(
    private readonly auditService: AuditService,
    private readonly complianceService: ComplianceService
  ) {}


  @Post('entries')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Créer une entrée d\'audit' })
  @ApiResponse({ status: 201, description: 'L\'entrée d\'audit a été créée avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  createAuditEntry(@Body() createAuditEntryDto: CreateAuditEntryDto) {
    return this.auditService.createAuditEntry(createAuditEntryDto);
  }

  @Get('entries')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Récupérer toutes les entrées d\'audit' })
  @ApiResponse({ status: 200, description: 'Liste des entrées d\'audit récupérée avec succès.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Champ de tri' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordre de tri' })
  @ApiQuery({ name: 'userId', required: false, type: String, description: 'ID de l\'utilisateur' })
  @ApiQuery({ name: 'action', required: false, type: String, description: 'Action' })
  @ApiQuery({ name: 'resource', required: false, type: String, description: 'Ressource' })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Date de début (ISO)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'Date de fin (ISO)' })
  findAllEntries(@Query() query: AuditQueryDto) {
    return this.auditService.findAll(query);
  }

  @Get('entries/:id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Récupérer une entrée d\'audit par son ID' })
  @ApiResponse({ status: 200, description: 'L\'entrée d\'audit a été récupérée avec succès.' })
  @ApiResponse({ status: 404, description: 'Entrée d\'audit non trouvée.' })
  findOneEntry(@Param('id', ParseObjectIdPipe) id: string) {
    return this.auditService.findOne(id);
  }

  @Get('entries/user/:userId')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Récupérer les entrées d\'audit pour un utilisateur' })
  @ApiResponse({ status: 200, description: 'Liste des entrées d\'audit récupérée avec succès.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Champ de tri' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordre de tri' })
  @ApiQuery({ name: 'action', required: false, type: String, description: 'Action' })
  @ApiQuery({ name: 'resource', required: false, type: String, description: 'Ressource' })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Date de début (ISO)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'Date de fin (ISO)' })
  findByUser(
    @Param('userId', ParseObjectIdPipe) userId: string,
    @Query() query: AuditQueryDto,
  ) {
    return this.auditService.findByUser(userId, query);
  }

  @Get('entries/action/:action')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Récupérer les entrées d\'audit pour une action' })
  @ApiResponse({ status: 200, description: 'Liste des entrées d\'audit récupérée avec succès.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Champ de tri' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordre de tri' })
  @ApiQuery({ name: 'userId', required: false, type: String, description: 'ID de l\'utilisateur' })
  @ApiQuery({ name: 'resource', required: false, type: String, description: 'Ressource' })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Date de début (ISO)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'Date de fin (ISO)' })
  findByAction(
    @Param('action') action: string,
    @Query() query: AuditQueryDto,
  ) {
    return this.auditService.findByAction(action, query);
  }

  @Get('entries/resource/:resource')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Récupérer les entrées d\'audit pour une ressource' })
  @ApiResponse({ status: 200, description: 'Liste des entrées d\'audit récupérée avec succès.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Champ de tri' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordre de tri' })
  @ApiQuery({ name: 'userId', required: false, type: String, description: 'ID de l\'utilisateur' })
  @ApiQuery({ name: 'action', required: false, type: String, description: 'Action' })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Date de début (ISO)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'Date de fin (ISO)' })
  findByResource(
    @Param('resource') resource: string,
    @Query() query: AuditQueryDto,
  ) {
    return this.auditService.findByResource(resource, query);
  }

  @Post('reports/generate')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Générer un rapport d\'audit' })
  @ApiResponse({ status: 200, description: 'Le rapport d\'audit a été généré avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  generateReport(
    @Body() options: AuditQueryDto,
    @CurrentUser('id') userId: string,
  ) {
    return this.auditService.generateReport({
      ...options,
      userId,
    });
  }

  @Post('compliance/gdpr')
  @Roles(UserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Exécuter un contrôle de conformité GDPR' })
  @ApiResponse({ status: 200, description: 'Le contrôle de conformité GDPR a été exécuté avec succès.' })
  runGdprComplianceCheck(@CurrentUser('id') userId: string) {
    return this.complianceService.runGdprComplianceCheck(userId);
  }
}
