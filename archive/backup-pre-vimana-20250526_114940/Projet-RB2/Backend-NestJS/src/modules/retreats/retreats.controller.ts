import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  UseGuards,
  Query,
  HttpStatus,
  HttpCode,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { FilesInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { RetreatsService } from './retreats.service';
import { CreateRetreatDto } from './dto/create-retreat.dto';
import { UpdateRetreatDto } from './dto/update-retreat.dto';
import { RetreatResponseDto } from './dto/retreat-response.dto';
import { UserRole } from '../auth/decorators/roles.decorator';

@ApiTags('retreats')
@Controller('retreats')
export class RetreatsController {
  constructor(private readonly retreatsService: RetreatsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new retreat' })
  @ApiResponse({ status: 201, description: 'Retreat created successfully', type: RetreatResponseDto })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.HOST, UserRole.ORGANIZER)
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createRetreatDto: CreateRetreatDto, @CurrentUser() user: any): Promise<RetreatResponseDto> {
    return this.retreatsService.create(createRetreatDto, user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all retreats' })
  @ApiResponse({ status: 200, description: 'Return all retreats', type: [RetreatResponseDto] })
  async findAll(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('search') search?: string,
    @Query('category') category?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('minPrice') minPrice?: number,
    @Query('maxPrice') maxPrice?: number,
    @Query('location') location?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'asc' | 'desc',
  ): Promise<{ data: RetreatResponseDto[]; total: number; page: number; limit: number }> {
    return this.retreatsService.findAll({
      page,
      limit,
      search,
      category,
      startDate,
      endDate,
      minPrice,
      maxPrice,
      location,
      sortBy,
      sortOrder,
    });
  }

  @Get('featured')
  @ApiOperation({ summary: 'Get featured retreats' })
  @ApiResponse({ status: 200, description: 'Return featured retreats', type: [RetreatResponseDto] })
  async getFeatured(): Promise<RetreatResponseDto[]> {
    return this.retreatsService.getFeatured();
  }

  @Get('upcoming')
  @ApiOperation({ summary: 'Get upcoming retreats' })
  @ApiResponse({ status: 200, description: 'Return upcoming retreats', type: [RetreatResponseDto] })
  async getUpcoming(): Promise<RetreatResponseDto[]> {
    return this.retreatsService.getUpcoming();
  }

  @Get('categories')
  @ApiOperation({ summary: 'Get retreat categories' })
  @ApiResponse({ status: 200, description: 'Return retreat categories' })
  async getCategories(): Promise<string[]> {
    return this.retreatsService.getCategories();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a retreat by id' })
  @ApiResponse({ status: 200, description: 'Return a retreat', type: RetreatResponseDto })
  @ApiResponse({ status: 404, description: 'Retreat not found' })
  async findOne(@Param('id') id: string): Promise<RetreatResponseDto> {
    return this.retreatsService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a retreat' })
  @ApiResponse({ status: 200, description: 'Retreat updated successfully', type: RetreatResponseDto })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Retreat not found' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.HOST, UserRole.ORGANIZER)
  async update(
    @Param('id') id: string,
    @Body() updateRetreatDto: UpdateRetreatDto,
    @CurrentUser() user: any,
  ): Promise<RetreatResponseDto> {
    return this.retreatsService.update(id, updateRetreatDto, user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a retreat' })
  @ApiResponse({ status: 204, description: 'Retreat deleted successfully' })
  @ApiResponse({ status: 404, description: 'Retreat not found' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.HOST, UserRole.ORGANIZER)
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string, @CurrentUser() user: any): Promise<void> {
    return this.retreatsService.remove(id, user.id);
  }

  @Post(':id/images')
  @ApiOperation({ summary: 'Upload retreat images' })
  @ApiResponse({ status: 200, description: 'Images uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Retreat not found' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.HOST, UserRole.ORGANIZER)
  @UseInterceptors(FilesInterceptor('files', 10))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
      },
    },
  })
  async uploadImages(
    @Param('id') id: string,
    @UploadedFiles() files: Array<Express.Multer.File>,
    @CurrentUser() user: any,
  ): Promise<{ urls: string[] }> {
    return this.retreatsService.uploadImages(id, files, user.id);
  }

  @Get(':id/reviews')
  @ApiOperation({ summary: 'Get retreat reviews' })
  @ApiResponse({ status: 200, description: 'Return retreat reviews' })
  @ApiResponse({ status: 404, description: 'Retreat not found' })
  async getReviews(@Param('id') id: string): Promise<any[]> {
    return this.retreatsService.getReviews(id);
  }
}
