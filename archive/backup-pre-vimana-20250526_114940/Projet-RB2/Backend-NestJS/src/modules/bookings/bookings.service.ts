import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateBookingDto } from './dto/create-booking.dto';
import { UpdateBookingDto } from './dto/update-booking.dto';
import { BookingResponseDto } from './dto/booking-response.dto';
import { UserRole } from '../auth/decorators/roles.decorator';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class BookingsService {
  private readonly logger = new Logger(BookingsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async create(createBookingDto: CreateBookingDto, userId: string): Promise<BookingResponseDto> {
    try {
      // Check if retreat exists and is published
      const retreat = await this.prisma.retreat.findUnique({
        where: {
          id: createBookingDto.retreatId,
          status: 'PUBLISHED',
        },
        include: {
          host: {
            select: {
              id: true,
            },
          },
        },
      });

      if (!retreat) {
        throw new NotFoundException(`Retreat with ID ${createBookingDto.retreatId} not found or not published`);
      }

      // Check if user is not booking their own retreat
      if (retreat.host.id === userId) {
        throw new BadRequestException('You cannot book your own retreat');
      }

      // Check if retreat has available capacity
      const bookingsCount = await this.prisma.booking.count({
        where: {
          retreatId: createBookingDto.retreatId,
          status: {
            in: ['CONFIRMED', 'PENDING'],
          },
        },
      });

      if (bookingsCount >= retreat.capacity) {
        throw new BadRequestException('This retreat is fully booked');
      }

      // Calculate total price
      const totalPrice = retreat.price * createBookingDto.participants;

      // Create booking
      const booking = await this.prisma.booking.create({
        data: {
          retreatId: createBookingDto.retreatId,
          userId,
          participants: createBookingDto.participants,
          totalPrice,
          specialRequests: createBookingDto.specialRequests,
          status: 'PENDING',
        },
        include: {
          retreat: {
            include: {
              host: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                  image: true,
                },
              },
            },
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              image: true,
            },
          },
        },
      });

      // Emit booking created event
      this.eventEmitter.emit('booking.created', booking);

      this.logger.log(`Booking created successfully: ${booking.id}`);
      return booking;
    } catch (error) {
      this.logger.error(`Error creating booking: ${error.message}`);
      throw error;
    }
  }

  async findAll(params: {
    page?: number;
    limit?: number;
    status?: string;
    retreatId?: string;
    userId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<{ data: BookingResponseDto[]; total: number; page: number; limit: number }> {
    const {
      page = 1,
      limit = 10,
      status,
      retreatId,
      userId,
      startDate,
      endDate,
    } = params;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (retreatId) {
      where.retreatId = retreatId;
    }

    if (userId) {
      where.userId = userId;
    }

    if (startDate || endDate) {
      where.retreat = {
        ...where.retreat,
      };

      if (startDate) {
        where.retreat.startDate = {
          ...where.retreat?.startDate,
          gte: new Date(startDate),
        };
      }

      if (endDate) {
        where.retreat.endDate = {
          ...where.retreat?.endDate,
          lte: new Date(endDate),
        };
      }
    }

    try {
      const [bookings, total] = await Promise.all([
        this.prisma.booking.findMany({
          where,
          skip,
          take: limit,
          orderBy: {
            createdAt: 'desc',
          },
          include: {
            retreat: {
              include: {
                host: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    image: true,
                  },
                },
              },
            },
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                image: true,
              },
            },
          },
        }),
        this.prisma.booking.count({ where }),
      ]);

      return {
        data: bookings,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Error finding bookings: ${error.message}`);
      throw error;
    }
  }

  async findUserBookings(
    userId: string,
    params: {
      page?: number;
      limit?: number;
      status?: string;
    },
  ): Promise<{ data: BookingResponseDto[]; total: number; page: number; limit: number }> {
    const { page = 1, limit = 10, status } = params;

    const where: any = {
      userId,
    };

    if (status) {
      where.status = status;
    }

    try {
      const [bookings, total] = await Promise.all([
        this.prisma.booking.findMany({
          where,
          skip: (page - 1) * limit,
          take: limit,
          orderBy: {
            createdAt: 'desc',
          },
          include: {
            retreat: {
              include: {
                host: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    image: true,
                  },
                },
              },
            },
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                image: true,
              },
            },
          },
        }),
        this.prisma.booking.count({ where }),
      ]);

      return {
        data: bookings,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Error finding user bookings: ${error.message}`);
      throw error;
    }
  }

  async findHostBookings(
    hostId: string,
    params: {
      page?: number;
      limit?: number;
      status?: string;
      retreatId?: string;
    },
  ): Promise<{ data: BookingResponseDto[]; total: number; page: number; limit: number }> {
    const { page = 1, limit = 10, status, retreatId } = params;

    const where: any = {
      retreat: {
        hostId,
      },
    };

    if (status) {
      where.status = status;
    }

    if (retreatId) {
      where.retreatId = retreatId;
    }

    try {
      const [bookings, total] = await Promise.all([
        this.prisma.booking.findMany({
          where,
          skip: (page - 1) * limit,
          take: limit,
          orderBy: {
            createdAt: 'desc',
          },
          include: {
            retreat: {
              include: {
                host: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    image: true,
                  },
                },
              },
            },
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                image: true,
              },
            },
          },
        }),
        this.prisma.booking.count({ where }),
      ]);

      return {
        data: bookings,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Error finding host bookings: ${error.message}`);
      throw error;
    }
  }

  async findOne(id: string, userId: string, userRole: UserRole): Promise<BookingResponseDto> {
    try {
      const booking = await this.prisma.booking.findUnique({
        where: { id },
        include: {
          retreat: {
            include: {
              host: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                  image: true,
                },
              },
            },
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              image: true,
            },
          },
        },
      });

      if (!booking) {
        throw new NotFoundException(`Booking with ID ${id} not found`);
      }

      // Check if user has permission to view this booking
      if (
        userRole !== UserRole.ADMIN &&
        booking.userId !== userId &&
        booking.retreat.host.id !== userId
      ) {
        throw new ForbiddenException('You do not have permission to view this booking');
      }

      return booking;
    } catch (error) {
      this.logger.error(`Error finding booking: ${error.message}`);
      throw error;
    }
  }

  async update(
    id: string,
    updateBookingDto: UpdateBookingDto,
    userId: string,
    userRole: UserRole,
  ): Promise<BookingResponseDto> {
    try {
      // Check if booking exists
      const booking = await this.prisma.booking.findUnique({
        where: { id },
        include: {
          retreat: {
            include: {
              host: {
                select: {
                  id: true,
                },
              },
            },
          },
        },
      });

      if (!booking) {
        throw new NotFoundException(`Booking with ID ${id} not found`);
      }

      // Check if user has permission to update this booking
      if (
        userRole !== UserRole.ADMIN &&
        booking.userId !== userId &&
        booking.retreat.host.id !== userId
      ) {
        throw new ForbiddenException('You do not have permission to update this booking');
      }

      // Only allow updating certain fields based on user role and booking status
      const data: any = {};

      // User can update special requests if booking is pending or confirmed
      if (
        booking.userId === userId &&
        ['PENDING', 'CONFIRMED'].includes(booking.status) &&
        updateBookingDto.specialRequests !== undefined
      ) {
        data.specialRequests = updateBookingDto.specialRequests;
      }

      // Host or admin can update status
      if (
        (booking.retreat.host.id === userId || userRole === UserRole.ADMIN) &&
        updateBookingDto.status
      ) {
        data.status = updateBookingDto.status;
      }

      // If no valid updates, throw error
      if (Object.keys(data).length === 0) {
        throw new BadRequestException('No valid updates provided');
      }

      // Update booking
      const updatedBooking = await this.prisma.booking.update({
        where: { id },
        data,
        include: {
          retreat: {
            include: {
              host: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                  image: true,
                },
              },
            },
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              image: true,
            },
          },
        },
      });

      // Emit booking updated event
      this.eventEmitter.emit('booking.updated', updatedBooking);

      this.logger.log(`Booking updated successfully: ${id}`);
      return updatedBooking;
    } catch (error) {
      this.logger.error(`Error updating booking: ${error.message}`);
      throw error;
    }
  }

  async remove(id: string, userId: string, userRole: UserRole): Promise<void> {
    try {
      // Check if booking exists
      const booking = await this.prisma.booking.findUnique({
        where: { id },
        include: {
          retreat: {
            include: {
              host: {
                select: {
                  id: true,
                },
              },
            },
          },
        },
      });

      if (!booking) {
        throw new NotFoundException(`Booking with ID ${id} not found`);
      }

      // Check if user has permission to delete this booking
      if (
        userRole !== UserRole.ADMIN &&
        booking.userId !== userId &&
        booking.retreat.host.id !== userId
      ) {
        throw new ForbiddenException('You do not have permission to delete this booking');
      }

      // Delete booking
      await this.prisma.booking.delete({
        where: { id },
      });

      // Emit booking deleted event
      this.eventEmitter.emit('booking.deleted', booking);

      this.logger.log(`Booking deleted successfully: ${id}`);
    } catch (error) {
      this.logger.error(`Error deleting booking: ${error.message}`);
      throw error;
    }
  }

  async cancelBooking(id: string, userId: string, userRole: UserRole): Promise<BookingResponseDto> {
    try {
      // Check if booking exists
      const booking = await this.prisma.booking.findUnique({
        where: { id },
        include: {
          retreat: {
            include: {
              host: {
                select: {
                  id: true,
                },
              },
            },
          },
        },
      });

      if (!booking) {
        throw new NotFoundException(`Booking with ID ${id} not found`);
      }

      // Check if user has permission to cancel this booking
      if (
        userRole !== UserRole.ADMIN &&
        booking.userId !== userId &&
        booking.retreat.host.id !== userId
      ) {
        throw new ForbiddenException('You do not have permission to cancel this booking');
      }

      // Check if booking can be cancelled
      if (!['PENDING', 'CONFIRMED'].includes(booking.status)) {
        throw new BadRequestException(`Booking with status ${booking.status} cannot be cancelled`);
      }

      // Update booking status to CANCELLED
      const cancelledBooking = await this.prisma.booking.update({
        where: { id },
        data: {
          status: 'CANCELLED',
          cancelledAt: new Date(),
        },
        include: {
          retreat: {
            include: {
              host: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                  image: true,
                },
              },
            },
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              image: true,
            },
          },
        },
      });

      // Emit booking cancelled event
      this.eventEmitter.emit('booking.cancelled', cancelledBooking);

      this.logger.log(`Booking cancelled successfully: ${id}`);
      return cancelledBooking;
    } catch (error) {
      this.logger.error(`Error cancelling booking: ${error.message}`);
      throw error;
    }
  }

  async confirmBooking(id: string, userId: string, userRole: UserRole): Promise<BookingResponseDto> {
    try {
      // Check if booking exists
      const booking = await this.prisma.booking.findUnique({
        where: { id },
        include: {
          retreat: {
            include: {
              host: {
                select: {
                  id: true,
                },
              },
            },
          },
        },
      });

      if (!booking) {
        throw new NotFoundException(`Booking with ID ${id} not found`);
      }

      // Only host or admin can confirm bookings
      if (
        userRole !== UserRole.ADMIN &&
        booking.retreat.host.id !== userId
      ) {
        throw new ForbiddenException('Only the host or an admin can confirm bookings');
      }

      // Check if booking can be confirmed
      if (booking.status !== 'PENDING') {
        throw new BadRequestException(`Booking with status ${booking.status} cannot be confirmed`);
      }

      // Update booking status to CONFIRMED
      const confirmedBooking = await this.prisma.booking.update({
        where: { id },
        data: {
          status: 'CONFIRMED',
          confirmedAt: new Date(),
        },
        include: {
          retreat: {
            include: {
              host: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                  image: true,
                },
              },
            },
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              image: true,
            },
          },
        },
      });

      // Emit booking confirmed event
      this.eventEmitter.emit('booking.confirmed', confirmedBooking);

      this.logger.log(`Booking confirmed successfully: ${id}`);
      return confirmedBooking;
    } catch (error) {
      this.logger.error(`Error confirming booking: ${error.message}`);
      throw error;
    }
  }
}
