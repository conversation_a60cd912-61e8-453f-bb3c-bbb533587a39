import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsPositive,
  IsOptional,
  Min,
  Max,
  IsEnum,
} from 'class-validator';

enum BookingStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED',
  REFUNDED = 'REFUNDED',
}

export class UpdateBookingDto {
  @ApiPropertyOptional({
    description: 'Number of participants',
    example: 2,
    minimum: 1,
    maximum: 20,
  })
  @IsNumber()
  @IsPositive()
  @Min(1)
  @Max(20)
  @IsOptional()
  participants?: number;

  @ApiPropertyOptional({
    description: 'Special requests or notes for the booking',
    example: 'I have dietary restrictions (vegan). Please also arrange airport pickup if possible.',
  })
  @IsString()
  @IsOptional()
  specialRequests?: string;

  @ApiPropertyOptional({
    description: 'Status of the booking',
    enum: BookingStatus,
    example: BookingStatus.CONFIRMED,
  })
  @IsEnum(BookingStatus)
  @IsOptional()
  status?: BookingStatus;
}
