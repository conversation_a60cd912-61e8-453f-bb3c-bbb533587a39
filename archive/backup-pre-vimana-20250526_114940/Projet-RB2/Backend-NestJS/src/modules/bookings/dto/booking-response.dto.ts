import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

class UserDto {
  @ApiProperty({ description: 'ID of the user', example: '123e4567-e89b-12d3-a456-426614174000' })
  id: string;

  @ApiProperty({ description: 'First name of the user', example: '<PERSON>' })
  firstName: string;

  @ApiProperty({ description: 'Last name of the user', example: 'Doe' })
  lastName: string;

  @ApiProperty({ description: 'Email of the user', example: '<EMAIL>' })
  email: string;

  @ApiPropertyOptional({ description: 'Profile picture of the user', example: '/uploads/users/123/profile.jpg' })
  image?: string;
}

class HostDto {
  @ApiProperty({ description: 'ID of the host', example: '123e4567-e89b-12d3-a456-426614174000' })
  id: string;

  @ApiProperty({ description: 'First name of the host', example: '<PERSON>' })
  firstName: string;

  @ApiProperty({ description: 'Last name of the host', example: '<PERSON>' })
  lastName: string;

  @ApiProperty({ description: 'Email of the host', example: '<EMAIL>' })
  email: string;

  @ApiPropertyOptional({ description: 'Profile picture of the host', example: '/uploads/users/456/profile.jpg' })
  image?: string;
}

class RetreatDto {
  @ApiProperty({ description: 'ID of the retreat', example: '123e4567-e89b-12d3-a456-426614174000' })
  id: string;

  @ApiProperty({ description: 'Title of the retreat', example: 'Yoga Retreat in Bali' })
  title: string;

  @ApiProperty({ description: 'Description of the retreat', example: 'A relaxing yoga retreat in the heart of Bali' })
  description: string;

  @ApiProperty({ description: 'Location of the retreat', example: 'Ubud, Bali, Indonesia' })
  location: string;

  @ApiProperty({ description: 'Start date of the retreat', example: '2023-06-15T00:00:00.000Z' })
  startDate: Date;

  @ApiProperty({ description: 'End date of the retreat', example: '2023-06-22T00:00:00.000Z' })
  endDate: Date;

  @ApiProperty({ description: 'Price of the retreat', example: 1500 })
  price: number;

  @ApiProperty({ description: 'Host of the retreat', type: HostDto })
  host: HostDto;

  @ApiProperty({
    description: 'Images of the retreat',
    type: [String],
    example: ['/uploads/retreats/1/image1.jpg', '/uploads/retreats/1/image2.jpg'],
  })
  images: string[];
}

export class BookingResponseDto {
  @ApiProperty({ description: 'ID of the booking', example: '123e4567-e89b-12d3-a456-426614174000' })
  id: string;

  @ApiProperty({ description: 'ID of the retreat', example: '123e4567-e89b-12d3-a456-426614174000' })
  retreatId: string;

  @ApiProperty({ description: 'ID of the user', example: '123e4567-e89b-12d3-a456-426614174000' })
  userId: string;

  @ApiProperty({ description: 'Number of participants', example: 2 })
  participants: number;

  @ApiProperty({ description: 'Total price of the booking', example: 3000 })
  totalPrice: number;

  @ApiProperty({
    description: 'Status of the booking',
    example: 'PENDING',
    enum: ['PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED', 'REFUNDED'],
  })
  status: string;

  @ApiPropertyOptional({
    description: 'Special requests or notes for the booking',
    example: 'I have dietary restrictions (vegan). Please also arrange airport pickup if possible.',
  })
  specialRequests?: string;

  @ApiProperty({ description: 'Creation date of the booking', example: '2023-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date of the booking', example: '2023-01-02T00:00:00.000Z' })
  updatedAt: Date;

  @ApiPropertyOptional({ description: 'Confirmation date of the booking', example: '2023-01-02T00:00:00.000Z' })
  confirmedAt?: Date;

  @ApiPropertyOptional({ description: 'Cancellation date of the booking', example: '2023-01-03T00:00:00.000Z' })
  cancelledAt?: Date;

  @ApiPropertyOptional({ description: 'Completion date of the booking', example: '2023-06-22T00:00:00.000Z' })
  completedAt?: Date;

  @ApiPropertyOptional({ description: 'Refund date of the booking', example: '2023-01-04T00:00:00.000Z' })
  refundedAt?: Date;

  @ApiProperty({ description: 'User who made the booking', type: UserDto })
  user: UserDto;

  @ApiProperty({ description: 'Retreat that was booked', type: RetreatDto })
  retreat: RetreatDto;
}
