import { IsString, <PERSON>NotEmpty, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>th, IsUrl } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { LEVELS } from '../../../shared/constants';

export class CreateCourseDto {
  @ApiProperty({
    description: 'Titre du cours',
    example: 'Introduction à la méditation',
    minLength: 3,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(100)
  title: string;

  @ApiProperty({
    description: 'Description du cours',
    example: 'Un cours complet pour apprendre les bases de la méditation',
    minLength: 10,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(10)
  description: string;

  @ApiPropertyOptional({
    description: 'Niveau du cours',
    example: 'BEGINNER',
    enum: Object.values(LEVELS),
  })
  @IsOptional()
  @IsString()
  level?: string;

  @ApiPropertyOptional({
    description: 'URL de l\'image de couverture',
    example: 'https://example.com/images/meditation.jpg',
  })
  @IsOptional()
  @IsString()
  @IsUrl()
  coverImage?: string;

  @ApiPropertyOptional({
    description: 'Catégorie du cours',
    example: 'Méditation',
  })
  @IsOptional()
  @IsString()
  category?: string;
}
