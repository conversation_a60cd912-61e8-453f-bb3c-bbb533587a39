import { IsArray, IsUUID, ArrayMinSize } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ReorderLessonsDto {
  @ApiProperty({
    description: 'Liste ordonnée des IDs de leçon',
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsUUID(4, { each: true })
  lessonIds: string[];
}
