import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { EnrollmentService } from '../services/enrollment.service';
import { CreateEnrollmentDto } from '../dto/create-enrollment.dto';
import { UpdateEnrollmentDto } from '../dto/update-enrollment.dto';
import { UpdateProgressDto } from '../dto/update-progress.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CurrentUser } from '../../../common/decorators';
import { PaginationPipe } from '../../../common/pipes/pagination.pipe';
import { PaginationOptions } from '../../../shared/interfaces';
import { ParseObjectIdPipe } from '../../../common/pipes/parse-object-id.pipe';
import { Cacheable } from '../../../common/decorators';

@ApiTags('enrollments')
@Controller('enrollments')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class EnrollmentController {
  constructor(private readonly enrollmentService: EnrollmentService) {}

  @Post()
  @ApiOperation({ summary: 'Créer une nouvelle inscription' })
  @ApiResponse({ status: 201, description: 'L\'inscription a été créée avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 404, description: 'Cours ou utilisateur non trouvé.' })
  @ApiResponse({ status: 409, description: 'L\'utilisateur est déjà inscrit à ce cours.' })
  create(@Body() createEnrollmentDto: CreateEnrollmentDto) {
    return this.enrollmentService.create(createEnrollmentDto);
  }

  @Post('enroll-me/:courseId')
  @ApiOperation({ summary: 'Inscrire l\'utilisateur connecté à un cours' })
  @ApiResponse({ status: 201, description: 'L\'inscription a été créée avec succès.' })
  @ApiResponse({ status: 404, description: 'Cours non trouvé.' })
  @ApiResponse({ status: 409, description: 'L\'utilisateur est déjà inscrit à ce cours.' })
  enrollMe(
    @CurrentUser('id') userId: string,
    @Param('courseId', ParseObjectIdPipe) courseId: string,
  ) {
    const createEnrollmentDto: CreateEnrollmentDto = {
      userId,
      courseId,
      progress: 0,
    };
    return this.enrollmentService.create(createEnrollmentDto);
  }

  @Get()
  @Roles('ADMIN')
  @Cacheable(60) // Cache pour 60 secondes
  @ApiOperation({ summary: 'Récupérer toutes les inscriptions' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Champ de tri' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordre de tri' })
  @ApiResponse({ status: 200, description: 'Liste des inscriptions récupérée avec succès.' })
  findAll(@Query(new PaginationPipe()) paginationOptions: PaginationOptions) {
    return this.enrollmentService.findAll(paginationOptions);
  }

  @Get('my-enrollments')
  @Cacheable(30) // Cache pour 30 secondes
  @ApiOperation({ summary: 'Récupérer les inscriptions de l\'utilisateur connecté' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Champ de tri' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordre de tri' })
  @ApiResponse({ status: 200, description: 'Liste des inscriptions récupérée avec succès.' })
  findMyEnrollments(
    @CurrentUser('id') userId: string,
    @Query(new PaginationPipe()) paginationOptions: PaginationOptions,
  ) {
    return this.enrollmentService.findByUser(userId, paginationOptions);
  }

  @Get('user/:userId')
  @Roles('ADMIN')
  @Cacheable(30) // Cache pour 30 secondes
  @ApiOperation({ summary: 'Récupérer les inscriptions d\'un utilisateur' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Champ de tri' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordre de tri' })
  @ApiResponse({ status: 200, description: 'Liste des inscriptions récupérée avec succès.' })
  @ApiResponse({ status: 404, description: 'Utilisateur non trouvé.' })
  findByUser(
    @Param('userId', ParseObjectIdPipe) userId: string,
    @Query(new PaginationPipe()) paginationOptions: PaginationOptions,
  ) {
    return this.enrollmentService.findByUser(userId, paginationOptions);
  }

  @Get('course/:courseId')
  @Roles('ADMIN')
  @Cacheable(30) // Cache pour 30 secondes
  @ApiOperation({ summary: 'Récupérer les inscriptions à un cours' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Champ de tri' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordre de tri' })
  @ApiResponse({ status: 200, description: 'Liste des inscriptions récupérée avec succès.' })
  @ApiResponse({ status: 404, description: 'Cours non trouvé.' })
  findByCourse(
    @Param('courseId', ParseObjectIdPipe) courseId: string,
    @Query(new PaginationPipe()) paginationOptions: PaginationOptions,
  ) {
    return this.enrollmentService.findByCourse(courseId, paginationOptions);
  }

  @Get('course/:courseId/stats')
  @Roles('ADMIN')
  @Cacheable(60) // Cache pour 60 secondes
  @ApiOperation({ summary: 'Récupérer les statistiques d\'inscription à un cours' })
  @ApiResponse({ status: 200, description: 'Statistiques récupérées avec succès.' })
  @ApiResponse({ status: 404, description: 'Cours non trouvé.' })
  getEnrollmentStats(@Param('courseId', ParseObjectIdPipe) courseId: string) {
    return this.enrollmentService.getEnrollmentStats(courseId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Récupérer une inscription par son ID' })
  @ApiResponse({ status: 200, description: 'L\'inscription a été récupérée avec succès.' })
  @ApiResponse({ status: 404, description: 'Inscription non trouvée.' })
  findOne(@Param('id', ParseObjectIdPipe) id: string) {
    return this.enrollmentService.findOne(id);
  }

  @Patch(':id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Mettre à jour une inscription' })
  @ApiResponse({ status: 200, description: 'L\'inscription a été mise à jour avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 404, description: 'Inscription non trouvée.' })
  update(
    @Param('id', ParseObjectIdPipe) id: string,
    @Body() updateEnrollmentDto: UpdateEnrollmentDto,
  ) {
    return this.enrollmentService.update(id, updateEnrollmentDto);
  }

  @Patch(':id/progress')
  @ApiOperation({ summary: 'Mettre à jour la progression d\'une inscription' })
  @ApiResponse({ status: 200, description: 'La progression a été mise à jour avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 404, description: 'Inscription non trouvée.' })
  @ApiResponse({ status: 409, description: 'La progression doit être comprise entre 0 et 100.' })
  updateProgress(
    @Param('id', ParseObjectIdPipe) id: string,
    @Body() updateProgressDto: UpdateProgressDto,
  ) {
    return this.enrollmentService.updateProgress(id, updateProgressDto.progress);
  }

  @Delete(':id')
  @Roles('ADMIN')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Supprimer une inscription' })
  @ApiResponse({ status: 204, description: 'L\'inscription a été supprimée avec succès.' })
  @ApiResponse({ status: 404, description: 'Inscription non trouvée.' })
  remove(@Param('id', ParseObjectIdPipe) id: string) {
    return this.enrollmentService.remove(id);
  }
}
