import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { CourseService } from '../services/course.service';
import { CreateCourseDto } from '../dto/create-course.dto';
import { UpdateCourseDto } from '../dto/update-course.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { Public } from '../../auth/decorators/public.decorator';
import { PaginationPipe } from '../../../common/pipes/pagination.pipe';
import { PaginationOptions } from '../../../shared/interfaces';
import { ParseObjectIdPipe } from '../../../common/pipes/parse-object-id.pipe';
import { Cacheable } from '../../../common/decorators';

@ApiTags('courses')
@Controller('courses')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class CourseController {
  constructor(private readonly courseService: CourseService) {}

  @Post()
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Créer un nouveau cours' })
  @ApiResponse({ status: 201, description: 'Le cours a été créé avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 409, description: 'Un cours avec ce titre existe déjà.' })
  create(@Body() createCourseDto: CreateCourseDto) {
    return this.courseService.create(createCourseDto);
  }

  @Get()
  @Public()
  @Cacheable(60) // Cache pour 60 secondes
  @ApiOperation({ summary: 'Récupérer tous les cours' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Champ de tri' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordre de tri' })
  @ApiResponse({ status: 200, description: 'Liste des cours récupérée avec succès.' })
  findAll(@Query(new PaginationPipe()) paginationOptions: PaginationOptions) {
    return this.courseService.findAll(paginationOptions);
  }

  @Get('search')
  @Public()
  @Cacheable(30) // Cache pour 30 secondes
  @ApiOperation({ summary: 'Rechercher des cours' })
  @ApiQuery({ name: 'query', required: true, type: String, description: 'Terme de recherche' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiResponse({ status: 200, description: 'Résultats de recherche récupérés avec succès.' })
  search(
    @Query('query') query: string,
    @Query(new PaginationPipe()) paginationOptions: PaginationOptions,
  ) {
    return this.courseService.search(query, paginationOptions);
  }

  @Get('category/:category')
  @Public()
  @Cacheable(60) // Cache pour 60 secondes
  @ApiOperation({ summary: 'Récupérer les cours par catégorie' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiResponse({ status: 200, description: 'Liste des cours récupérée avec succès.' })
  findByCategory(
    @Param('category') category: string,
    @Query(new PaginationPipe()) paginationOptions: PaginationOptions,
  ) {
    return this.courseService.findByCategory(category, paginationOptions);
  }

  @Get('level/:level')
  @Public()
  @Cacheable(60) // Cache pour 60 secondes
  @ApiOperation({ summary: 'Récupérer les cours par niveau' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiResponse({ status: 200, description: 'Liste des cours récupérée avec succès.' })
  findByLevel(
    @Param('level') level: string,
    @Query(new PaginationPipe()) paginationOptions: PaginationOptions,
  ) {
    return this.courseService.findByLevel(level, paginationOptions);
  }

  @Get(':id')
  @Public()
  @Cacheable(30) // Cache pour 30 secondes
  @ApiOperation({ summary: 'Récupérer un cours par son ID' })
  @ApiResponse({ status: 200, description: 'Le cours a été récupéré avec succès.' })
  @ApiResponse({ status: 404, description: 'Cours non trouvé.' })
  findOne(@Param('id', ParseObjectIdPipe) id: string) {
    return this.courseService.findOne(id);
  }

  @Patch(':id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Mettre à jour un cours' })
  @ApiResponse({ status: 200, description: 'Le cours a été mis à jour avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 404, description: 'Cours non trouvé.' })
  @ApiResponse({ status: 409, description: 'Un cours avec ce titre existe déjà.' })
  update(
    @Param('id', ParseObjectIdPipe) id: string,
    @Body() updateCourseDto: UpdateCourseDto,
  ) {
    return this.courseService.update(id, updateCourseDto);
  }

  @Delete(':id')
  @Roles('ADMIN')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Supprimer un cours' })
  @ApiResponse({ status: 204, description: 'Le cours a été supprimé avec succès.' })
  @ApiResponse({ status: 404, description: 'Cours non trouvé.' })
  remove(@Param('id', ParseObjectIdPipe) id: string) {
    return this.courseService.remove(id);
  }
}
