import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { LessonService } from '../services/lesson.service';
import { CreateLessonDto } from '../dto/create-lesson.dto';
import { UpdateLessonDto } from '../dto/update-lesson.dto';
import { ReorderLessonsDto } from '../dto/reorder-lessons.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { Public } from '../../auth/decorators/public.decorator';
import { PaginationPipe } from '../../../common/pipes/pagination.pipe';
import { PaginationOptions } from '../../../shared/interfaces';
import { ParseObjectIdPipe } from '../../../common/pipes/parse-object-id.pipe';
import { Cacheable } from '../../../common/decorators';

@ApiTags('lessons')
@Controller('courses/:courseId/lessons')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class LessonController {
  constructor(private readonly lessonService: LessonService) {}

  @Post()
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Créer une nouvelle leçon' })
  @ApiResponse({ status: 201, description: 'La leçon a été créée avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 404, description: 'Cours non trouvé.' })
  @ApiResponse({ status: 409, description: 'Une leçon avec cet ordre existe déjà pour ce cours.' })
  create(
    @Param('courseId', ParseObjectIdPipe) courseId: string,
    @Body() createLessonDto: CreateLessonDto,
  ) {
    // Assurer que le courseId du DTO correspond au courseId de l'URL
    createLessonDto.courseId = courseId;
    return this.lessonService.create(createLessonDto);
  }

  @Get()
  @Public()
  @Cacheable(60) // Cache pour 60 secondes
  @ApiOperation({ summary: 'Récupérer toutes les leçons d\'un cours' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Champ de tri' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordre de tri' })
  @ApiResponse({ status: 200, description: 'Liste des leçons récupérée avec succès.' })
  @ApiResponse({ status: 404, description: 'Cours non trouvé.' })
  findAll(
    @Param('courseId', ParseObjectIdPipe) courseId: string,
    @Query(new PaginationPipe()) paginationOptions: PaginationOptions,
  ) {
    return this.lessonService.findAll(courseId, paginationOptions);
  }

  @Get(':id')
  @Public()
  @Cacheable(30) // Cache pour 30 secondes
  @ApiOperation({ summary: 'Récupérer une leçon par son ID' })
  @ApiResponse({ status: 200, description: 'La leçon a été récupérée avec succès.' })
  @ApiResponse({ status: 404, description: 'Leçon non trouvée.' })
  findOne(@Param('id', ParseObjectIdPipe) id: string) {
    return this.lessonService.findOne(id);
  }

  @Patch(':id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Mettre à jour une leçon' })
  @ApiResponse({ status: 200, description: 'La leçon a été mise à jour avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 404, description: 'Leçon non trouvée.' })
  @ApiResponse({ status: 409, description: 'Une leçon avec cet ordre existe déjà pour ce cours.' })
  update(
    @Param('id', ParseObjectIdPipe) id: string,
    @Body() updateLessonDto: UpdateLessonDto,
  ) {
    return this.lessonService.update(id, updateLessonDto);
  }

  @Delete(':id')
  @Roles('ADMIN')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Supprimer une leçon' })
  @ApiResponse({ status: 204, description: 'La leçon a été supprimée avec succès.' })
  @ApiResponse({ status: 404, description: 'Leçon non trouvée.' })
  remove(@Param('id', ParseObjectIdPipe) id: string) {
    return this.lessonService.remove(id);
  }

  @Post('reorder')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Réorganiser les leçons d\'un cours' })
  @ApiResponse({ status: 200, description: 'Les leçons ont été réorganisées avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 404, description: 'Cours non trouvé.' })
  @ApiResponse({ status: 409, description: 'Tous les IDs de leçon du cours doivent être présents pour la réorganisation.' })
  reorderLessons(
    @Param('courseId', ParseObjectIdPipe) courseId: string,
    @Body() reorderLessonsDto: ReorderLessonsDto,
  ) {
    return this.lessonService.reorderLessons(courseId, reorderLessonsDto.lessonIds);
  }
}
