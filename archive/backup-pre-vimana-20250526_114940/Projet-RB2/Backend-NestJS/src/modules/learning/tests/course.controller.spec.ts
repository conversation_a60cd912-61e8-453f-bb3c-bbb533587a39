import { Test, TestingModule } from '@nestjs/testing';
import { CourseController } from '../controllers/course.controller';
import { CourseService } from '../services/course.service';
import { CreateCourseDto } from '../dto/create-course.dto';
import { UpdateCourseDto } from '../dto/update-course.dto';
import { PaginationOptions } from '../../../shared/interfaces';

// Mock du CourseService
const mockCourseService = {
  create: jest.fn(),
  findAll: jest.fn(),
  findOne: jest.fn(),
  update: jest.fn(),
  remove: jest.fn(),
  search: jest.fn(),
  findByCategory: jest.fn(),
  findByLevel: jest.fn(),
};

describe('CourseController', () => {
  let controller: CourseController;
  let service: CourseService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CourseController],
      providers: [
        {
          provide: CourseService,
          useValue: mockCourseService,
        },
      ],
    }).compile();

    controller = module.get<CourseController>(CourseController);
    service = module.get<CourseService>(CourseService);

    // Réinitialiser les mocks avant chaque test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a course', async () => {
      // Arrange
      const createCourseDto: CreateCourseDto = {
        title: 'Test Course',
        description: 'Test Description',
        level: 'BEGINNER',
        category: 'Test Category',
      };

      const expectedCourse = {
        id: 'test-id',
        ...createCourseDto,
        createdAt: new Date(),
        updatedAt: new Date(),
        coverImage: null,
      };

      mockCourseService.create.mockResolvedValue(expectedCourse);

      // Act
      const result = await controller.create(createCourseDto);

      // Assert
      expect(mockCourseService.create).toHaveBeenCalledWith(createCourseDto);
      expect(result).toEqual(expectedCourse);
    });
  });

  describe('findAll', () => {
    it('should return paginated courses', async () => {
      // Arrange
      const paginationOptions: PaginationOptions = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const expectedResult = {
        courses: [
          {
            id: 'test-id-1',
            title: 'Test Course 1',
            description: 'Test Description 1',
            level: 'BEGINNER',
            category: 'Test Category',
            createdAt: new Date(),
            updatedAt: new Date(),
            coverImage: null,
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
      };

      mockCourseService.findAll.mockResolvedValue(expectedResult);

      // Act
      const result = await controller.findAll(paginationOptions);

      // Assert
      expect(mockCourseService.findAll).toHaveBeenCalledWith(paginationOptions);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findOne', () => {
    it('should return a course by id', async () => {
      // Arrange
      const courseId = 'test-id';
      const expectedCourse = {
        id: courseId,
        title: 'Test Course',
        description: 'Test Description',
        level: 'BEGINNER',
        category: 'Test Category',
        createdAt: new Date(),
        updatedAt: new Date(),
        coverImage: null,
        lessons: [],
      };

      mockCourseService.findOne.mockResolvedValue(expectedCourse);

      // Act
      const result = await controller.findOne(courseId);

      // Assert
      expect(mockCourseService.findOne).toHaveBeenCalledWith(courseId);
      expect(result).toEqual(expectedCourse);
    });
  });

  describe('update', () => {
    it('should update a course', async () => {
      // Arrange
      const courseId = 'test-id';
      const updateCourseDto: UpdateCourseDto = {
        title: 'Updated Course',
        description: 'Updated Description',
      };

      const updatedCourse = {
        id: courseId,
        title: 'Updated Course',
        description: 'Updated Description',
        level: 'BEGINNER',
        category: 'Test Category',
        createdAt: new Date(),
        updatedAt: new Date(),
        coverImage: null,
      };

      mockCourseService.update.mockResolvedValue(updatedCourse);

      // Act
      const result = await controller.update(courseId, updateCourseDto);

      // Assert
      expect(mockCourseService.update).toHaveBeenCalledWith(courseId, updateCourseDto);
      expect(result).toEqual(updatedCourse);
    });
  });

  describe('remove', () => {
    it('should remove a course', async () => {
      // Arrange
      const courseId = 'test-id';
      mockCourseService.remove.mockResolvedValue(undefined);

      // Act
      const result = await controller.remove(courseId);

      // Assert
      expect(mockCourseService.remove).toHaveBeenCalledWith(courseId);
      expect(result).toBeUndefined();
    });
  });

  describe('search', () => {
    it('should search courses by query', async () => {
      // Arrange
      const query = 'test';
      const paginationOptions: PaginationOptions = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const expectedResult = {
        courses: [
          {
            id: 'test-id-1',
            title: 'Test Course 1',
            description: 'Test Description 1',
            level: 'BEGINNER',
            category: 'Test Category',
            createdAt: new Date(),
            updatedAt: new Date(),
            coverImage: null,
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
      };

      mockCourseService.search.mockResolvedValue(expectedResult);

      // Act
      const result = await controller.search(query, paginationOptions);

      // Assert
      expect(mockCourseService.search).toHaveBeenCalledWith(query, paginationOptions);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findByCategory', () => {
    it('should return courses by category', async () => {
      // Arrange
      const category = 'Test Category';
      const paginationOptions: PaginationOptions = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const expectedResult = {
        courses: [
          {
            id: 'test-id-1',
            title: 'Test Course 1',
            description: 'Test Description 1',
            level: 'BEGINNER',
            category: 'Test Category',
            createdAt: new Date(),
            updatedAt: new Date(),
            coverImage: null,
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
      };

      mockCourseService.findByCategory.mockResolvedValue(expectedResult);

      // Act
      const result = await controller.findByCategory(category, paginationOptions);

      // Assert
      expect(mockCourseService.findByCategory).toHaveBeenCalledWith(category, paginationOptions);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findByLevel', () => {
    it('should return courses by level', async () => {
      // Arrange
      const level = 'BEGINNER';
      const paginationOptions: PaginationOptions = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const expectedResult = {
        courses: [
          {
            id: 'test-id-1',
            title: 'Test Course 1',
            description: 'Test Description 1',
            level: 'BEGINNER',
            category: 'Test Category',
            createdAt: new Date(),
            updatedAt: new Date(),
            coverImage: null,
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
      };

      mockCourseService.findByLevel.mockResolvedValue(expectedResult);

      // Act
      const result = await controller.findByLevel(level, paginationOptions);

      // Assert
      expect(mockCourseService.findByLevel).toHaveBeenCalledWith(level, paginationOptions);
      expect(result).toEqual(expectedResult);
    });
  });
});
