import { IsEmail, IsString, IsOptional, Min<PERSON>ength, IsEnum, Matches } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateUserDto {
  @ApiProperty({
    description: 'Email de l\'utilisateur',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Veuillez fournir un email valide' })
  email: string;

  @ApiProperty({
    description: 'Mot de passe de l\'utilisateur',
    example: 'Password123!',
    minLength: 8,
  })
  @IsString()
  @MinLength(8, { message: 'Le mot de passe doit contenir au moins 8 caractères' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
    message: 'Le mot de passe doit contenir au moins une lettre majuscule, une lettre minuscule, un chiffre et un caractère spécial',
  })
  password: string;

  @ApiPropertyOptional({
    description: 'Nom de l\'utilisateur',
    example: '<PERSON>',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Rôle de l\'utilisateur',
    enum: ['USER', 'ADMIN', 'MODERATOR', 'PARTNER', 'HOST', 'ORGANIZER', 'PARTICIPANT'],
    default: 'USER',
  })
  @IsOptional()
  @IsEnum(['USER', 'ADMIN', 'MODERATOR', 'PARTNER', 'HOST', 'ORGANIZER', 'PARTICIPANT'], {
    message: 'Le rôle doit être l\'un des suivants: USER, ADMIN, MODERATOR, PARTNER, HOST, ORGANIZER, PARTICIPANT',
  })
  role?: string;
}
