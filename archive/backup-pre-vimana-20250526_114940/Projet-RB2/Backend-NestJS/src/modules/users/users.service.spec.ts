import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, ConflictException } from '@nestjs/common';
import { UsersService } from './users.service';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { testData } from '../../test/setup';
import * as bcrypt from 'bcrypt';

// Mock bcrypt
jest.mock('bcrypt');
const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('UsersService', () => {
  let service: UsersService;
  let prismaService: jest.Mocked<PrismaService>;

  const mockUser = {
    id: testData.user.id,
    email: testData.user.email,
    name: testData.user.name,
    password: testData.user.password,
    role: testData.user.role,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockPrismaService = {
    user: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    prismaService = module.get(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    const createUserDto: CreateUserDto = {
      email: '<EMAIL>',
      name: 'New User',
      password: 'password123',
    };

    it('should create a new user successfully', async () => {
      // Arrange
      const hashedPassword = 'hashedPassword123';
      const createdUser = {
        ...mockUser,
        email: createUserDto.email,
        name: createUserDto.name,
        password: hashedPassword,
      };

      prismaService.user.findUnique.mockResolvedValue(null); // Email not exists
      mockedBcrypt.hash.mockResolvedValue(hashedPassword as never);
      prismaService.user.create.mockResolvedValue(createdUser);

      // Act
      const result = await service.create(createUserDto);

      // Assert
      expect(result).toEqual(createdUser);
      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { email: createUserDto.email },
      });
      expect(bcrypt.hash).toHaveBeenCalledWith(createUserDto.password, 10);
      expect(prismaService.user.create).toHaveBeenCalledWith({
        data: {
          email: createUserDto.email,
          name: createUserDto.name,
          password: hashedPassword,
        },
      });
    });

    it('should throw ConflictException when email already exists', async () => {
      // Arrange
      prismaService.user.findUnique.mockResolvedValue(mockUser);

      // Act & Assert
      await expect(service.create(createUserDto)).rejects.toThrow(ConflictException);
      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { email: createUserDto.email },
      });
      expect(prismaService.user.create).not.toHaveBeenCalled();
    });

    it('should handle password hashing errors', async () => {
      // Arrange
      prismaService.user.findUnique.mockResolvedValue(null);
      mockedBcrypt.hash.mockRejectedValue(new Error('Hashing failed') as never);

      // Act & Assert
      await expect(service.create(createUserDto)).rejects.toThrow('Hashing failed');
    });
  });

  describe('findAll', () => {
    it('should return paginated users list', async () => {
      // Arrange
      const users = [mockUser, { ...mockUser, id: 'user-2', email: '<EMAIL>' }];
      const total = 2;

      prismaService.user.findMany.mockResolvedValue(users);
      prismaService.user.count.mockResolvedValue(total);

      // Act
      const result = await service.findAll({ page: 1, limit: 10 });

      // Assert
      expect(result).toEqual({
        data: users,
        total,
        page: 1,
        limit: 10,
        totalPages: 1,
      });
      expect(prismaService.user.findMany).toHaveBeenCalledWith({
        skip: 0,
        take: 10,
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
      });
    });

    it('should handle empty results', async () => {
      // Arrange
      prismaService.user.findMany.mockResolvedValue([]);
      prismaService.user.count.mockResolvedValue(0);

      // Act
      const result = await service.findAll({ page: 1, limit: 10 });

      // Assert
      expect(result).toEqual({
        data: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      });
    });

    it('should handle pagination correctly', async () => {
      // Arrange
      const users = [mockUser];
      const total = 25;

      prismaService.user.findMany.mockResolvedValue(users);
      prismaService.user.count.mockResolvedValue(total);

      // Act
      const result = await service.findAll({ page: 3, limit: 10 });

      // Assert
      expect(result.totalPages).toBe(3);
      expect(prismaService.user.findMany).toHaveBeenCalledWith({
        skip: 20, // (page - 1) * limit
        take: 10,
        select: expect.any(Object),
      });
    });
  });

  describe('findOne', () => {
    it('should return user when found', async () => {
      // Arrange
      prismaService.user.findUnique.mockResolvedValue(mockUser);

      // Act
      const result = await service.findOne(mockUser.id);

      // Assert
      expect(result).toEqual(mockUser);
      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: mockUser.id },
      });
    });

    it('should throw NotFoundException when user not found', async () => {
      // Arrange
      const nonExistentId = 'non-existent-id';
      prismaService.user.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(service.findOne(nonExistentId)).rejects.toThrow(NotFoundException);
      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: nonExistentId },
      });
    });
  });

  describe('findByEmail', () => {
    it('should return user when found by email', async () => {
      // Arrange
      prismaService.user.findUnique.mockResolvedValue(mockUser);

      // Act
      const result = await service.findByEmail(mockUser.email);

      // Assert
      expect(result).toEqual(mockUser);
      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { email: mockUser.email },
      });
    });

    it('should return null when user not found by email', async () => {
      // Arrange
      const nonExistentEmail = '<EMAIL>';
      prismaService.user.findUnique.mockResolvedValue(null);

      // Act
      const result = await service.findByEmail(nonExistentEmail);

      // Assert
      expect(result).toBeNull();
      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { email: nonExistentEmail },
      });
    });
  });

  describe('update', () => {
    const updateUserDto: UpdateUserDto = {
      name: 'Updated Name',
      email: '<EMAIL>',
    };

    it('should update user successfully', async () => {
      // Arrange
      const updatedUser = { ...mockUser, ...updateUserDto };
      
      prismaService.user.findUnique.mockResolvedValue(mockUser);
      prismaService.user.update.mockResolvedValue(updatedUser);

      // Act
      const result = await service.update(mockUser.id, updateUserDto);

      // Assert
      expect(result).toEqual(updatedUser);
      expect(prismaService.user.update).toHaveBeenCalledWith({
        where: { id: mockUser.id },
        data: updateUserDto,
      });
    });

    it('should throw NotFoundException when user not found', async () => {
      // Arrange
      const nonExistentId = 'non-existent-id';
      prismaService.user.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(service.update(nonExistentId, updateUserDto)).rejects.toThrow(
        NotFoundException,
      );
      expect(prismaService.user.update).not.toHaveBeenCalled();
    });

    it('should hash password when updating password', async () => {
      // Arrange
      const updateWithPassword: UpdateUserDto = {
        ...updateUserDto,
        password: 'newPassword123',
      };
      const hashedPassword = 'hashedNewPassword';
      const updatedUser = { ...mockUser, password: hashedPassword };

      prismaService.user.findUnique.mockResolvedValue(mockUser);
      mockedBcrypt.hash.mockResolvedValue(hashedPassword as never);
      prismaService.user.update.mockResolvedValue(updatedUser);

      // Act
      const result = await service.update(mockUser.id, updateWithPassword);

      // Assert
      expect(bcrypt.hash).toHaveBeenCalledWith('newPassword123', 10);
      expect(prismaService.user.update).toHaveBeenCalledWith({
        where: { id: mockUser.id },
        data: {
          ...updateWithPassword,
          password: hashedPassword,
        },
      });
    });
  });

  describe('remove', () => {
    it('should remove user successfully', async () => {
      // Arrange
      prismaService.user.findUnique.mockResolvedValue(mockUser);
      prismaService.user.delete.mockResolvedValue(mockUser);

      // Act
      const result = await service.remove(mockUser.id);

      // Assert
      expect(result).toEqual(mockUser);
      expect(prismaService.user.delete).toHaveBeenCalledWith({
        where: { id: mockUser.id },
      });
    });

    it('should throw NotFoundException when user not found', async () => {
      // Arrange
      const nonExistentId = 'non-existent-id';
      prismaService.user.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(service.remove(nonExistentId)).rejects.toThrow(NotFoundException);
      expect(prismaService.user.delete).not.toHaveBeenCalled();
    });
  });
});
