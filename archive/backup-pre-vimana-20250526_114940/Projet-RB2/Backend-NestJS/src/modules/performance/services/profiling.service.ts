import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ProfilingResult } from '../interfaces/profiling-result.interface';

@Injectable()
export class ProfilingService {
  private readonly logger = new Logger(ProfilingService.name);
  private readonly profilingResults: Map<string, ProfilingResult[]> = new Map();
  private readonly historyLimit = 20;

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Démarre une session de profilage
   * @param name Nom de la session
   * @param metadata Métadonnées
   * @returns ID de la session
   */
  startProfiling(name: string, metadata: Record<string, any> = {}) {
    try {
      const sessionId = `${name}-${Date.now()}`;

      // Créer une session de profilage
      const session = {
        id: sessionId,
        name,
        startTime: Date.now(),
        endTime: null,
        duration: null,
        metadata,
        measurements: [],
      };

      // Enregistrer la session
      this.prisma.profilingSession.create({
        data: {
          id: sessionId,
          name,
          startTime: new Date(session.startTime),
          metadata,
        },
      }).catch((error: Error) => {
        this.logger.error(`Erreur lors de l'enregistrement de la session de profilage: ${error.message}`);
      });

      this.logger.log(`Session de profilage démarrée: ${sessionId}`);
      return sessionId;
    } catch (error) {
      this.logger.error(`Erreur lors du démarrage de la session de profilage: ${error.message}`);
      throw error;
    }
  }

  /**
   * Termine une session de profilage
   * @param sessionId ID de la session
   * @param result Résultat du profilage
   * @returns Résultat du profilage
   */
  endProfiling(sessionId: string, result: Partial<ProfilingResult> = {}) {
    try {
      const endTime = Date.now();

      // Récupérer la session
      this.prisma.profilingSession.findUnique({
        where: { id: sessionId },
      }).then((session: any) => {
        if (!session) {
          this.logger.warn(`Session de profilage non trouvée: ${sessionId}`);
          return;
        }

        // Calculer la durée
        const duration = endTime - new Date(session.startTime).getTime();

        // Mettre à jour la session
        this.prisma.profilingSession.update({
          where: { id: sessionId },
          data: {
            endTime: new Date(endTime),
            duration,
            result: result as any,
          },
        }).catch((error: Error) => {
          this.logger.error(`Erreur lors de la mise à jour de la session de profilage: ${error.message}`);
        });

        // Ajouter le résultat à l'historique
        this.addProfilingResult(session.name, {
          id: sessionId,
          name: session.name,
          startTime: new Date(session.startTime).getTime(),
          endTime,
          duration,
          metadata: session.metadata as Record<string, any>,
          measurements: result.measurements || [],
          ...result,
        });

        this.logger.log(`Session de profilage terminée: ${sessionId}, durée: ${duration}ms`);
      }).catch((error: Error) => {
        this.logger.error(`Erreur lors de la récupération de la session de profilage: ${error.message}`);
      });

      return {
        sessionId,
        endTime,
        ...result,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la fin de la session de profilage: ${error.message}`);
      throw error;
    }
  }

  /**
   * Ajoute une mesure à une session de profilage
   * @param sessionId ID de la session
   * @param name Nom de la mesure
   * @param value Valeur de la mesure
   * @param unit Unité de la mesure
   * @param metadata Métadonnées
   * @returns La mesure ajoutée
   */
  addMeasurement(sessionId: string, name: string, value: number, unit: string, metadata: Record<string, any> = {}) {
    try {
      const measurement = {
        name,
        value,
        unit,
        timestamp: Date.now(),
        metadata,
      };

      // Enregistrer la mesure
      this.prisma.profilingMeasurement.create({
        data: {
          sessionId,
          name,
          value,
          unit,
          timestamp: new Date(measurement.timestamp),
          metadata,
        },
      }).catch((error: Error) => {
        this.logger.error(`Erreur lors de l'enregistrement de la mesure de profilage: ${error.message}`);
      });

      return measurement;
    } catch (error) {
      this.logger.error(`Erreur lors de l'ajout de la mesure de profilage: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les résultats de profilage
   * @param limit Nombre de résultats à récupérer
   * @returns Résultats de profilage
   */
  async getProfilingResults(limit = 10) {
    try {
      // Récupérer les sessions de profilage
      const sessions = await this.prisma.profilingSession.findMany({
        where: {
          endTime: {
            not: null,
          },
        },
        orderBy: {
          startTime: 'desc',
        },
        take: limit,
        include: {
          measurements: true,
        },
      });

      // Transformer les sessions en résultats
      return sessions.map((session: any) => ({
        id: session.id,
        name: session.name,
        startTime: new Date(session.startTime).getTime(),
        endTime: session.endTime ? new Date(session.endTime).getTime() : null,
        duration: session.duration,
        metadata: session.metadata as Record<string, any>,
        measurements: session.measurements.map((m: any) => ({
          name: m.name,
          value: m.value,
          unit: m.unit,
          timestamp: new Date(m.timestamp).getTime(),
          metadata: m.metadata as Record<string, any>,
        })),
        result: session.result as Record<string, any>,
      }));
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des résultats de profilage: ${error.message}`);

      // Retourner les résultats en cache
      return Array.from(this.profilingResults.values())
        .flat()
        .sort((a, b) => b.startTime - a.startTime)
        .slice(0, limit);
    }
  }

  /**
   * Récupère les résultats de profilage par nom
   * @param name Nom du profilage
   * @param limit Nombre de résultats à récupérer
   * @returns Résultats de profilage
   */
  async getProfilingResultsByName(name: string, limit = 10) {
    try {
      // Récupérer les sessions de profilage
      const sessions = await this.prisma.profilingSession.findMany({
        where: {
          name,
          endTime: {
            not: null,
          },
        },
        orderBy: {
          startTime: 'desc',
        },
        take: limit,
        include: {
          measurements: true,
        },
      });

      // Transformer les sessions en résultats
      return sessions.map((session: any) => ({
        id: session.id,
        name: session.name,
        startTime: new Date(session.startTime).getTime(),
        endTime: session.endTime ? new Date(session.endTime).getTime() : null,
        duration: session.duration,
        metadata: session.metadata as Record<string, any>,
        measurements: session.measurements.map((m: any) => ({
          name: m.name,
          value: m.value,
          unit: m.unit,
          timestamp: new Date(m.timestamp).getTime(),
          metadata: m.metadata as Record<string, any>,
        })),
        result: session.result as Record<string, any>,
      }));
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des résultats de profilage par nom: ${error.message}`);

      // Retourner les résultats en cache
      return (this.profilingResults.get(name) || [])
        .sort((a, b) => b.startTime - a.startTime)
        .slice(0, limit);
    }
  }

  /**
   * Récupère un résultat de profilage par ID
   * @param id ID du résultat
   * @returns Résultat de profilage
   */
  async getProfilingResultById(id: string) {
    try {
      // Récupérer la session de profilage
      const session = await this.prisma.profilingSession.findUnique({
        where: { id },
        include: {
          measurements: true,
        },
      });

      if (!session) {
        return null;
      }

      // Transformer la session en résultat
      return {
        id: session.id,
        name: session.name,
        startTime: new Date(session.startTime).getTime(),
        endTime: session.endTime ? new Date(session.endTime).getTime() : null,
        duration: session.duration,
        metadata: session.metadata as Record<string, any>,
        measurements: session.measurements.map((m: any) => ({
          name: m.name,
          value: m.value,
          unit: m.unit,
          timestamp: new Date(m.timestamp).getTime(),
          metadata: m.metadata as Record<string, any>,
        })),
        result: session.result as Record<string, any>,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération du résultat de profilage par ID: ${error.message}`);

      // Rechercher dans le cache
      for (const results of this.profilingResults.values()) {
        const result = results.find(r => r.id === id);
        if (result) {
          return result;
        }
      }

      return null;
    }
  }

  /**
   * Ajoute un résultat de profilage à l'historique
   * @param name Nom du profilage
   * @param result Résultat du profilage
   */
  private addProfilingResult(name: string, result: ProfilingResult) {
    // Récupérer l'historique
    const history = this.profilingResults.get(name) || [];

    // Ajouter le résultat
    history.push(result);

    // Limiter la taille de l'historique
    if (history.length > this.historyLimit) {
      history.shift();
    }

    // Mettre à jour l'historique
    this.profilingResults.set(name, history);
  }

  /**
   * Profil une fonction
   * @param name Nom du profilage
   * @param fn Fonction à profiler
   * @param metadata Métadonnées
   * @returns Résultat de la fonction
   */
  async profileFunction<T>(name: string, fn: () => Promise<T>, metadata: Record<string, any> = {}): Promise<T> {
    const sessionId = this.startProfiling(name, metadata);

    try {
      const startMemory = process.memoryUsage();
      const startTime = Date.now();

      // Exécuter la fonction
      const result = await fn();

      const endTime = Date.now();
      const endMemory = process.memoryUsage();

      // Calculer les métriques
      const duration = endTime - startTime;
      const memoryDiff = {
        rss: endMemory.rss - startMemory.rss,
        heapTotal: endMemory.heapTotal - startMemory.heapTotal,
        heapUsed: endMemory.heapUsed - startMemory.heapUsed,
        external: endMemory.external - startMemory.external,
      };

      // Terminer le profilage
      this.endProfiling(sessionId, {
        duration,
        memory: memoryDiff,
        measurements: [
          {
            name: 'duration',
            value: duration,
            unit: 'ms',
            timestamp: endTime,
            metadata: {},
          },
          {
            name: 'memory.rss',
            value: memoryDiff.rss,
            unit: 'bytes',
            timestamp: endTime,
            metadata: {},
          },
          {
            name: 'memory.heapUsed',
            value: memoryDiff.heapUsed,
            unit: 'bytes',
            timestamp: endTime,
            metadata: {},
          },
        ],
      });

      return result;
    } catch (error: any) {
      // Terminer le profilage en cas d'erreur
      this.endProfiling(sessionId, {
        error: error.message,
        stack: error.stack,
      });

      throw error;
    }
  }
}
