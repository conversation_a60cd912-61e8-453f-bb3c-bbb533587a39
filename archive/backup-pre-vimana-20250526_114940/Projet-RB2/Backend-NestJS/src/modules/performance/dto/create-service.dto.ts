import { IsString, <PERSON>NotEmpty, IsOptional, IsBoolean, IsUrl } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateServiceDto {
  @ApiProperty({
    description: 'Nom du service',
    example: 'Database Service',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'Description du service',
    example: 'Service de base de données PostgreSQL',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'URL de vérification de santé du service',
    example: 'http://localhost:5432/health',
  })
  @IsUrl()
  @IsOptional()
  healthCheckUrl?: string;

  @ApiPropertyOptional({
    description: 'Activer/désactiver la surveillance du service',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  enabled?: boolean;
}
