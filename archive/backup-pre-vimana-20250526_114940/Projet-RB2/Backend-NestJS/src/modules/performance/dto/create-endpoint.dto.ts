import { IsString, <PERSON>NotEmpty, IsOptional, IsBoolean, IsUrl } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateEndpointDto {
  @ApiProperty({
    description: 'URL de l\'endpoint',
    example: 'https://api.example.com/health',
  })
  @IsUrl()
  @IsNotEmpty()
  url: string;

  @ApiProperty({
    description: 'Nom de l\'endpoint',
    example: 'API Health Check',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'Description de l\'endpoint',
    example: 'Vérification de l\'état de santé de l\'API',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Activer/désactiver la surveillance de l\'endpoint',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  enabled?: boolean;
}
