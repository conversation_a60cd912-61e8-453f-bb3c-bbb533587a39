export enum PerformanceMetricType {
  // Métriques système
  CPU_USAGE = 'CPU_USAGE',
  MEMORY_USAGE = 'MEMORY_USAGE',
  DISK_USAGE = 'DISK_USAGE',
  
  // Métriques API
  API_REQUESTS_PER_MINUTE = 'API_REQUESTS_PER_MINUTE',
  API_AVERAGE_RESPONSE_TIME = 'API_AVERAGE_RESPONSE_TIME',
  API_ERROR_RATE = 'API_ERROR_RATE',
  
  // Métriques de base de données
  DB_AVERAGE_QUERY_TIME = 'DB_AVERAGE_QUERY_TIME',
  DB_QUERIES_PER_MINUTE = 'DB_QUERIES_PER_MINUTE',
  DB_CONNECTION_POOL_USAGE = 'DB_CONNECTION_POOL_USAGE',
  
  // Métriques de cache
  CACHE_HIT_RATE = 'CACHE_HIT_RATE',
  CACHE_USAGE = 'CACHE_USAGE',
}
