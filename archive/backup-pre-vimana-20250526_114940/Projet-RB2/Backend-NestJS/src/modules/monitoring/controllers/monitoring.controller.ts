import {
  Controller,
  Get,
  Query,
  UseGuards,
  HttpStatus,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { MetricsService } from '../services/metrics.service';
import { PerformanceMonitoringService } from '../services/performance-monitoring.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '@prisma/client';

@ApiTags('Monitoring')
@Controller('monitoring')
export class MonitoringController {
  private readonly logger = new Logger(MonitoringController.name);

  constructor(
    private readonly metricsService: MetricsService,
    private readonly performanceService: PerformanceMonitoringService,
  ) {}

  @Get('metrics')
  @Header('Content-Type', 'text/plain')
  @ApiOperation({ summary: 'Métriques Prometheus' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Métriques au format Prometheus',
  })
  async getMetrics(): Promise<string> {
    this.logger.log('Récupération des métriques Prometheus');
    return await this.metricsService.getMetrics();
  }

  @Get('health')
  @ApiOperation({ summary: 'État de santé du système' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'État de santé récupéré avec succès',
  })
  async getHealth() {
    this.logger.log('Vérification de l\'état de santé');
    return this.performanceService.getPerformanceSummary();
  }

  @Get('performance')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Métriques de performance actuelles' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Métriques de performance récupérées avec succès',
  })
  async getCurrentPerformance() {
    this.logger.log('Récupération des métriques de performance actuelles');
    return this.performanceService.getCurrentMetrics();
  }

  @Get('performance/history')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Historique des performances' })
  @ApiQuery({ name: 'minutes', required: false, description: 'Nombre de minutes d\'historique' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Historique des performances récupéré avec succès',
  })
  async getPerformanceHistory(@Query('minutes') minutes?: string) {
    const minutesNumber = minutes ? parseInt(minutes, 10) : 60;
    this.logger.log(`Récupération de l'historique des performances (${minutesNumber} minutes)`);
    return this.performanceService.getPerformanceHistory(minutesNumber);
  }

  @Get('alerts')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Alertes actives' })
  @ApiQuery({ name: 'severity', required: false, description: 'Filtrer par sévérité' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Alertes récupérées avec succès',
  })
  async getActiveAlerts(@Query('severity') severity?: string) {
    this.logger.log(`Récupération des alertes actives (severity: ${severity || 'all'})`);
    return this.performanceService.getActiveAlerts(severity);
  }

  @Get('report')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Rapport de performance détaillé' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Rapport de performance généré avec succès',
  })
  async getPerformanceReport() {
    this.logger.log('Génération du rapport de performance détaillé');
    return await this.performanceService.generatePerformanceReport();
  }

  @Get('system')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Métriques système' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Métriques système récupérées avec succès',
  })
  async getSystemMetrics() {
    this.logger.log('Récupération des métriques système');
    return await this.metricsService.getSystemMetrics();
  }

  @Get('application')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Métriques d\'application' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Métriques d\'application récupérées avec succès',
  })
  async getApplicationMetrics() {
    this.logger.log('Récupération des métriques d\'application');
    return await this.metricsService.getApplicationMetrics();
  }

  @Get('dashboard')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Tableau de bord de monitoring complet' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tableau de bord récupéré avec succès',
  })
  async getMonitoringDashboard() {
    this.logger.log('Récupération du tableau de bord de monitoring');
    
    const [
      performanceSummary,
      systemMetrics,
      applicationMetrics,
      activeAlerts,
      recentHistory,
    ] = await Promise.all([
      this.performanceService.getPerformanceSummary(),
      this.metricsService.getSystemMetrics(),
      this.metricsService.getApplicationMetrics(),
      this.performanceService.getActiveAlerts(),
      this.performanceService.getPerformanceHistory(30), // 30 dernières minutes
    ]);

    return {
      summary: performanceSummary,
      system: systemMetrics,
      application: applicationMetrics,
      alerts: {
        active: activeAlerts,
        critical: activeAlerts.filter(a => a.severity === 'critical'),
        warning: activeAlerts.filter(a => a.severity === 'medium' || a.severity === 'high'),
      },
      trends: {
        memory: recentHistory.map(h => ({
          timestamp: h.timestamp,
          value: h.memory?.heapUsedMB || 0,
        })),
        database: recentHistory.map(h => ({
          timestamp: h.timestamp,
          value: h.database?.responseTime || 0,
        })),
      },
      timestamp: new Date(),
    };
  }
}
