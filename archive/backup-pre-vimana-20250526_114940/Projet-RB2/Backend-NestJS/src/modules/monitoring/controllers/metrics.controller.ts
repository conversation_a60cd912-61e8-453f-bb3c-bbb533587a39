import { <PERSON>, Get, Header, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { PrometheusService } from '../services/prometheus.service';
import { Public } from '../../../common/decorators/public.decorator';

/**
 * Contrôleur pour exposer les métriques Prometheus
 */
@ApiTags('Monitoring')
@Controller('metrics')
export class MetricsController {
  private readonly logger = new Logger(MetricsController.name);

  constructor(private readonly prometheusService: PrometheusService) {}

  /**
   * Récupère les métriques au format Prometheus
   * @returns Métriques au format Prometheus
   */
  @Get()
  @Public()
  @Header('Content-Type', 'text/plain')
  @ApiOperation({
    summary: 'Get Prometheus metrics',
    description: 'Returns all metrics in Prometheus format',
  })
  @ApiResponse({
    status: 200,
    description: 'Metrics in Prometheus format',
  })
  async getMetrics(): Promise<string> {
    this.logger.debug('Getting metrics');
    return await this.prometheusService.getMetrics();
  }
}
