import { Test, TestingModule } from '@nestjs/testing';
import { MonitoringService } from './monitoring.service';
import { MetricsService } from './services/metrics.service';
import { PerformanceMonitoringService } from './services/performance-monitoring.service';
import { PrometheusService } from './services/prometheus.service';
import { AlertService } from './services/alert.service';

describe('MonitoringService', () => {
  let service: MonitoringService;
  let metricsService: jest.Mocked<MetricsService>;
  let performanceService: jest.Mocked<PerformanceMonitoringService>;
  let prometheusService: jest.Mocked<PrometheusService>;
  let alertService: jest.Mocked<AlertService>;

  beforeEach(async () => {
    const mockMetricsService = {
      getSystemMetrics: jest.fn(),
      getApplicationMetrics: jest.fn(),
      getMetrics: jest.fn(),
    } as any;

    const mockPerformanceService = {
      getPerformanceSummary: jest.fn().mockResolvedValue({}),
      getCurrentMetrics: jest.fn().mockReturnValue({}),
      getActiveAlerts: jest.fn().mockResolvedValue([]),
      generatePerformanceReport: jest.fn().mockResolvedValue({}),
    } as any;

    const mockPrometheusService = {
      incrementCounter: jest.fn(),
      observeHistogram: jest.fn(),
    };

    const mockAlertService = {
      sendAlert: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MonitoringService,
        {
          provide: MetricsService,
          useValue: mockMetricsService,
        },
        {
          provide: PerformanceMonitoringService,
          useValue: mockPerformanceService,
        },
        {
          provide: PrometheusService,
          useValue: mockPrometheusService,
        },
        {
          provide: AlertService,
          useValue: mockAlertService,
        },
      ],
    }).compile();

    service = module.get<MonitoringService>(MonitoringService);
    metricsService = module.get(MetricsService);
    performanceService = module.get(PerformanceMonitoringService);
    prometheusService = module.get(PrometheusService);
    alertService = module.get(AlertService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getMonitoringOverview', () => {
    it('should return monitoring overview', async () => {
      // Act
      const result = await service.getMonitoringOverview();

      // Assert
      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('performance');
      expect(result).toHaveProperty('system');
      expect(result).toHaveProperty('application');
      expect(result).toHaveProperty('alerts');
    });
  });

  describe('recordCustomMetric', () => {
    it('should record custom metric', () => {
      // Arrange
      const name = 'custom_metric';
      const value = 42;
      const labels = { type: 'test' };

      // Act
      service.recordCustomMetric(name, value, labels);

      // Assert
      expect(prometheusService.incrementCounter).toHaveBeenCalledWith(name, labels);
    });
  });

  describe('recordResponseTime', () => {
    it('should record response time', () => {
      // Arrange
      const operation = 'test_operation';
      const duration = 1500; // 1.5 seconds
      const labels = { endpoint: '/api/test' };

      // Act
      service.recordResponseTime(operation, duration, labels);

      // Assert
      expect(prometheusService.observeHistogram).toHaveBeenCalledWith(
        'test_operation_duration_seconds',
        1.5, // converted to seconds
        labels,
      );
    });
  });

  describe('triggerAlert', () => {
    it('should trigger alert', async () => {
      // Arrange
      const level = 'warning';
      const source = 'test';
      const title = 'Test Alert';
      const message = 'This is a test alert';
      const metadata = { value: 100 };

      // Act
      await service.triggerAlert(level, source, title, message, metadata);

      // Assert
      expect(alertService.sendAlert).toHaveBeenCalledWith({
        level,
        source,
        title,
        message,
        metadata,
      });
    });
  });

  describe('getHealthMetrics', () => {
    it('should return health metrics when system is healthy', async () => {
      // Arrange
      const mockCurrentMetrics = {
        memory: { heapUsed: 100000000, heapTotal: 200000000 },
        database: { responseTime: 50, isConnected: true },
      };
      const mockSystemMetrics = {
        memory: { heapUsed: 100000000, heapTotal: 200000000, external: 1000000, rss: 150000000 },
        cpu: { user: 1000000, system: 500000 },
        uptime: 3600,
        timestamp: new Date().toISOString(),
      };

      performanceService.getCurrentMetrics.mockReturnValue(mockCurrentMetrics);
      metricsService.getSystemMetrics.mockResolvedValue(mockSystemMetrics);

      // Act
      const result = await service.getHealthMetrics();

      // Assert
      expect(result).toHaveProperty('healthy', true);
      expect(result).toHaveProperty('metrics');
      expect(result.metrics.current).toBe(mockCurrentMetrics);
      expect(result.metrics.system).toBe(mockSystemMetrics);
    });

    it('should return unhealthy when system has issues', async () => {
      // Arrange
      const mockCurrentMetrics = {
        memory: { heapUsed: 180000000, heapTotal: 200000000 }, // 90% usage
        database: { responseTime: 50, isConnected: true },
      };

      performanceService.getCurrentMetrics.mockReturnValue(mockCurrentMetrics);
      metricsService.getSystemMetrics.mockResolvedValue({
        memory: { heapUsed: 100000000, heapTotal: 200000000, external: 1000000, rss: 150000000 },
        cpu: { user: 1000000, system: 500000 },
        uptime: 3600,
        timestamp: new Date().toISOString(),
      });

      // Act
      const result = await service.getHealthMetrics();

      // Assert
      expect(result).toHaveProperty('healthy', false);
    });
  });

  describe('generateMonitoringReport', () => {
    it('should generate complete monitoring report', async () => {
      // Arrange
      jest.spyOn(service, 'getMonitoringOverview').mockResolvedValue({} as any);

      // Act
      const result = await service.generateMonitoringReport();

      // Assert
      expect(result).toHaveProperty('overview');
      expect(result).toHaveProperty('performance');
      expect(result).toHaveProperty('metrics');
      expect(result).toHaveProperty('recommendations');
      expect(Array.isArray(result.recommendations)).toBe(true);
    });
  });

  describe('startMonitoring', () => {
    it('should start monitoring', async () => {
      // Act
      const result = await service.startMonitoring();

      // Assert
      expect(result).toHaveProperty('status', 'started');
      expect(result).toHaveProperty('services');
      expect(result.services.metrics).toBe('active');
      expect(result.services.performance).toBe('active');
      expect(result.services.alerts).toBe('active');
      expect(result.services.prometheus).toBe('active');
    });
  });

  describe('stopMonitoring', () => {
    it('should stop monitoring', async () => {
      // Act
      const result = await service.stopMonitoring();

      // Assert
      expect(result).toHaveProperty('status', 'stopped');
      expect(result).toHaveProperty('timestamp');
    });
  });
});
