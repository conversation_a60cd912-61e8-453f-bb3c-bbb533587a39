import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { PrismaModule } from '../../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module';

// Controllers
import { MetricsController } from './controllers/metrics.controller';
import { AlertsController } from './controllers/alerts.controller';
import { MonitoringController } from './controllers/monitoring.controller';

// Services
import { PrometheusService } from './services/prometheus.service';
import { AlertService } from './services/alert.service';
import { RecommendationMonitoringService } from './services/recommendation-monitoring.service';
import { MetricsService } from './services/metrics.service';
import { PerformanceMonitoringService } from './services/performance-monitoring.service';
import { MonitoringService } from './monitoring.service';

// Middleware
import { MetricsMiddleware } from './middleware/metrics.middleware';

// Configuration
import monitoringConfig from '../../config/monitoring.config';

/**
 * Module de monitoring
 */
@Module({
  imports: [
    PrismaModule,
    HttpModule,
    ScheduleModule.forRoot(),
    ConfigModule.forFeature(monitoringConfig),
    AuthModule,
  ],
  controllers: [
    MetricsController,
    AlertsController,
    MonitoringController,
  ],
  providers: [
    PrometheusService,
    AlertService,
    RecommendationMonitoringService,
    MetricsService,
    PerformanceMonitoringService,
    MonitoringService,
    MetricsMiddleware,
  ],
  exports: [
    PrometheusService,
    AlertService,
    RecommendationMonitoringService,
    MetricsService,
    PerformanceMonitoringService,
    MonitoringService,
  ],
})
export class MonitoringModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(MetricsMiddleware)
      .forRoutes('*'); // Appliquer à toutes les routes
  }
}
