import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { MetricsService } from './metrics.service';
import { PrismaService } from '../../../prisma/prisma.service';

describe('MetricsService', () => {
  let service: MetricsService;
  let prismaService: jest.Mocked<PrismaService>;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    const mockPrismaService = {
      $queryRaw: jest.fn(),
      user: {
        count: jest.fn(),
      },
      retreat: {
        count: jest.fn(),
      },
      booking: {
        count: jest.fn(),
      },
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MetricsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<MetricsService>(MetricsService);
    prismaService = module.get(PrismaService);
    configService = module.get(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('recordHttpRequest', () => {
    it('should record HTTP request metrics', () => {
      // Arrange
      const method = 'GET';
      const route = '/api/users';
      const statusCode = 200;
      const duration = 150;

      // Act
      service.recordHttpRequest(method, route, statusCode, duration);

      // Assert
      // Vérifier que la méthode ne lève pas d'erreur
      expect(service).toBeDefined();
    });
  });

  describe('recordRecommendationRequest', () => {
    it('should record recommendation request metrics', () => {
      // Arrange
      const type = 'user';
      const strategy = 'collaborative';
      const responseTime = 250;

      // Act
      service.recordRecommendationRequest(type, strategy, responseTime);

      // Assert
      expect(service).toBeDefined();
    });
  });

  describe('getSystemMetrics', () => {
    it('should return system metrics', async () => {
      // Act
      const result = await service.getSystemMetrics();

      // Assert
      expect(result).toHaveProperty('memory');
      expect(result).toHaveProperty('cpu');
      expect(result).toHaveProperty('uptime');
      expect(result).toHaveProperty('timestamp');
      expect(result.memory).toHaveProperty('heapUsed');
      expect(result.memory).toHaveProperty('heapTotal');
    });
  });

  describe('getApplicationMetrics', () => {
    it('should return application metrics', async () => {
      // Arrange
      prismaService.user.count.mockResolvedValue(100);
      prismaService.retreat.count.mockResolvedValue(50);
      prismaService.booking.count.mockResolvedValue(200);
      configService.get.mockImplementation((key, defaultValue) => {
        if (key === 'APP_VERSION') return '1.0.0';
        if (key === 'NODE_ENV') return 'test';
        return defaultValue;
      });

      // Act
      const result = await service.getApplicationMetrics();

      // Assert
      expect(result).toHaveProperty('database');
      expect(result).toHaveProperty('application');
      expect(result).toHaveProperty('timestamp');
      expect(result.database).toEqual({
        users: 100,
        retreats: 50,
        bookings: 200,
      });
      expect(result.application).toHaveProperty('version', '1.0.0');
      expect(result.application).toHaveProperty('environment', 'test');
    });

    it('should handle database errors gracefully', async () => {
      // Arrange
      prismaService.user.count.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.getApplicationMetrics()).rejects.toThrow('Database error');
    });
  });

  describe('getMetrics', () => {
    it('should return Prometheus metrics', async () => {
      // Act
      const result = await service.getMetrics();

      // Assert
      expect(typeof result).toBe('string');
      expect(result).toContain('# HELP');
      expect(result).toContain('# TYPE');
    });
  });

  describe('resetMetrics', () => {
    it('should reset all metrics', () => {
      // Act
      service.resetMetrics();

      // Assert
      expect(service).toBeDefined();
    });
  });

  describe('getRegister', () => {
    it('should return the Prometheus register', () => {
      // Act
      const register = service.getRegister();

      // Assert
      expect(register).toBeDefined();
      expect(typeof register.metrics).toBe('function');
    });
  });
});
