import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from '../../../prisma/prisma.service';
import * as promClient from 'prom-client';

@Injectable()
export class MetricsService {
  private readonly logger = new Logger(MetricsService.name);
  private readonly register = new promClient.Registry();

  // Métriques Prometheus
  private readonly httpRequestsTotal = new promClient.Counter({
    name: 'http_requests_total',
    help: 'Total number of HTTP requests',
    labelNames: ['method', 'route', 'status_code'],
    registers: [this.register],
  });

  private readonly httpRequestDuration = new promClient.Histogram({
    name: 'http_request_duration_seconds',
    help: 'Duration of HTTP requests in seconds',
    labelNames: ['method', 'route', 'status_code'],
    buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
    registers: [this.register],
  });

  private readonly databaseConnectionsActive = new promClient.Gauge({
    name: 'database_connections_active',
    help: 'Number of active database connections',
    registers: [this.register],
  });

  private readonly systemMemoryUsage = new promClient.Gauge({
    name: 'system_memory_usage_bytes',
    help: 'System memory usage in bytes',
    labelNames: ['type'],
    registers: [this.register],
  });

  private readonly systemCpuUsage = new promClient.Gauge({
    name: 'system_cpu_usage_percent',
    help: 'System CPU usage percentage',
    registers: [this.register],
  });

  private readonly recommendationRequestsTotal = new promClient.Counter({
    name: 'recommendation_requests_total',
    help: 'Total number of recommendation requests',
    labelNames: ['type', 'strategy'],
    registers: [this.register],
  });

  private readonly recommendationResponseTime = new promClient.Histogram({
    name: 'recommendation_response_time_seconds',
    help: 'Response time for recommendation requests',
    labelNames: ['type', 'strategy'],
    buckets: [0.1, 0.2, 0.5, 1, 2, 5],
    registers: [this.register],
  });

  constructor(
    private readonly configService: ConfigService,
    private readonly prismaService: PrismaService,
  ) {
    // Enregistrer les métriques par défaut de Node.js
    promClient.collectDefaultMetrics({ register: this.register });
  }

  /**
   * Obtenir toutes les métriques au format Prometheus
   */
  async getMetrics(): Promise<string> {
    return await this.register.metrics();
  }

  /**
   * Enregistrer une requête HTTP
   */
  recordHttpRequest(method: string, route: string, statusCode: number, duration: number) {
    this.httpRequestsTotal.inc({ method, route, status_code: statusCode.toString() });
    this.httpRequestDuration.observe(
      { method, route, status_code: statusCode.toString() },
      duration / 1000, // Convertir en secondes
    );
  }

  /**
   * Enregistrer une requête de recommandation
   */
  recordRecommendationRequest(type: string, strategy: string, responseTime: number) {
    this.recommendationRequestsTotal.inc({ type, strategy });
    this.recommendationResponseTime.observe({ type, strategy }, responseTime / 1000);
  }

  /**
   * Mettre à jour les métriques système
   */
  @Cron(CronExpression.EVERY_30_SECONDS)
  async updateSystemMetrics() {
    try {
      // Métriques mémoire
      const memoryUsage = process.memoryUsage();
      this.systemMemoryUsage.set({ type: 'heap_used' }, memoryUsage.heapUsed);
      this.systemMemoryUsage.set({ type: 'heap_total' }, memoryUsage.heapTotal);
      this.systemMemoryUsage.set({ type: 'external' }, memoryUsage.external);
      this.systemMemoryUsage.set({ type: 'rss' }, memoryUsage.rss);

      // Métriques CPU (approximation basée sur l'utilisation du processus)
      const cpuUsage = process.cpuUsage();
      const cpuPercent = (cpuUsage.user + cpuUsage.system) / 1000000; // Convertir en secondes
      this.systemCpuUsage.set(cpuPercent);

      // Métriques base de données (approximation)
      // Note: Prisma ne fournit pas directement le nombre de connexions actives
      // Ceci est une estimation basée sur l'état de la connexion
      try {
        await this.prismaService.$queryRaw`SELECT 1`;
        this.databaseConnectionsActive.set(1); // Au moins une connexion active
      } catch (error) {
        this.databaseConnectionsActive.set(0);
      }
    } catch (error) {
      this.logger.error('Erreur lors de la mise à jour des métriques système:', error);
    }
  }

  /**
   * Obtenir les métriques système actuelles
   */
  async getSystemMetrics() {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      memory: {
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        external: memoryUsage.external,
        rss: memoryUsage.rss,
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Obtenir les métriques de performance de l'application
   */
  async getApplicationMetrics() {
    try {
      // Statistiques de base de données
      const userCount = await this.prismaService.user.count();
      const retreatCount = await this.prismaService.retreat.count();
      const bookingCount = await this.prismaService.booking.count();

      return {
        database: {
          users: userCount,
          retreats: retreatCount,
          bookings: bookingCount,
        },
        application: {
          version: this.configService.get('APP_VERSION', '1.0.0'),
          environment: this.configService.get('NODE_ENV', 'development'),
          uptime: process.uptime(),
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Erreur lors de la récupération des métriques d\'application:', error);
      throw error;
    }
  }

  /**
   * Réinitialiser toutes les métriques
   */
  resetMetrics() {
    this.register.resetMetrics();
    this.logger.log('Métriques réinitialisées');
  }

  /**
   * Obtenir le registre Prometheus
   */
  getRegister() {
    return this.register;
  }
}
