import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { PrismaService } from '../../../prisma/prisma.service';

/**
 * Interface pour une alerte
 */
export interface Alert {
  level: 'info' | 'warning' | 'error' | 'critical';
  source: string;
  title: string;
  message: string;
  metadata?: Record<string, any>;
}

/**
 * Service pour l'envoi d'alertes
 */
@Injectable()
export class AlertService {
  private readonly logger = new Logger(AlertService.name);
  private readonly enabled: boolean;
  private readonly channels: {
    slack: {
      enabled: boolean;
      webhookUrl: string;
      channel: string;
    };
    email: {
      enabled: boolean;
      recipients: string[];
    };
    sms: {
      enabled: boolean;
      recipients: string[];
    };
    database: {
      enabled: boolean;
      retentionDays: number;
    };
  };
  private readonly throttling: {
    enabled: boolean;
    windowSeconds: number;
    maxAlertsPerWindow: number;
  };
  private readonly alertCounts: Map<string, { count: number; timestamp: number }> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly prisma: PrismaService,
  ) {
    this.enabled = this.configService.get<boolean>('monitoring.alerts.enabled', true);
    
    this.channels = {
      slack: {
        enabled: this.configService.get<boolean>('monitoring.alerts.channels.slack.enabled', false),
        webhookUrl: this.configService.get<string>('monitoring.alerts.channels.slack.webhookUrl', ''),
        channel: this.configService.get<string>('monitoring.alerts.channels.slack.channel', '#alerts'),
      },
      email: {
        enabled: this.configService.get<boolean>('monitoring.alerts.channels.email.enabled', false),
        recipients: this.configService.get<string[]>('monitoring.alerts.channels.email.recipients', []),
      },
      sms: {
        enabled: this.configService.get<boolean>('monitoring.alerts.channels.sms.enabled', false),
        recipients: this.configService.get<string[]>('monitoring.alerts.channels.sms.recipients', []),
      },
      database: {
        enabled: this.configService.get<boolean>('monitoring.alerts.channels.database.enabled', true),
        retentionDays: this.configService.get<number>('monitoring.alerts.channels.database.retentionDays', 30),
      },
    };
    
    this.throttling = {
      enabled: this.configService.get<boolean>('monitoring.alerts.throttling.enabled', true),
      windowSeconds: this.configService.get<number>('monitoring.alerts.throttling.windowSeconds', 300),
      maxAlertsPerWindow: this.configService.get<number>('monitoring.alerts.throttling.maxAlertsPerWindow', 5),
    };
    
    this.logger.log(`AlertService initialized with enabled=${this.enabled}`);
    
    if (this.enabled) {
      this.logger.log(`Channels: slack=${this.channels.slack.enabled}, email=${this.channels.email.enabled}, sms=${this.channels.sms.enabled}, database=${this.channels.database.enabled}`);
      this.logger.log(`Throttling: enabled=${this.throttling.enabled}, windowSeconds=${this.throttling.windowSeconds}, maxAlertsPerWindow=${this.throttling.maxAlertsPerWindow}`);
    }
  }

  /**
   * Envoie une alerte
   * @param alert Alerte à envoyer
   */
  async sendAlert(alert: Alert): Promise<void> {
    if (!this.enabled) return;
    
    // Générer une clé unique pour cette alerte
    const alertKey = this.generateAlertKey(alert);
    
    // Vérifier le throttling
    if (this.throttling.enabled && this.isThrottled(alertKey)) {
      this.logger.debug(`Alert throttled: ${alertKey}`);
      return;
    }
    
    this.logger.log(`Sending alert: ${alert.level} - ${alert.source} - ${alert.title}`);
    
    // Enregistrer l'alerte dans la base de données
    if (this.channels.database.enabled) {
      await this.saveAlertToDatabase(alert);
    }
    
    // Envoyer l'alerte via Slack
    if (this.channels.slack.enabled && this.channels.slack.webhookUrl) {
      await this.sendSlackAlert(alert);
    }
    
    // Envoyer l'alerte par email
    if (this.channels.email.enabled && this.channels.email.recipients.length > 0) {
      await this.sendEmailAlert(alert);
    }
    
    // Envoyer l'alerte par SMS
    if (this.channels.sms.enabled && this.channels.sms.recipients.length > 0) {
      await this.sendSmsAlert(alert);
    }
  }

  /**
   * Génère une clé unique pour une alerte
   * @param alert Alerte
   * @returns Clé unique
   */
  private generateAlertKey(alert: Alert): string {
    return `${alert.level}:${alert.source}:${alert.title}`;
  }

  /**
   * Vérifie si une alerte est throttled
   * @param alertKey Clé de l'alerte
   * @returns True si l'alerte est throttled
   */
  private isThrottled(alertKey: string): boolean {
    const now = Date.now();
    const alertCount = this.alertCounts.get(alertKey);
    
    // Si l'alerte n'a jamais été envoyée ou si la fenêtre est expirée
    if (!alertCount || now - alertCount.timestamp > this.throttling.windowSeconds * 1000) {
      this.alertCounts.set(alertKey, { count: 1, timestamp: now });
      return false;
    }
    
    // Incrémenter le compteur
    alertCount.count++;
    
    // Vérifier si le nombre maximum d'alertes est atteint
    if (alertCount.count > this.throttling.maxAlertsPerWindow) {
      return true;
    }
    
    return false;
  }

  /**
   * Enregistre une alerte dans la base de données
   * @param alert Alerte à enregistrer
   */
  private async saveAlertToDatabase(alert: Alert): Promise<void> {
    try {
      await this.prisma.alert.create({
        data: {
          level: alert.level,
          source: alert.source,
          title: alert.title,
          message: alert.message,
          metadata: alert.metadata || {},
        },
      });
      
      // Nettoyer les anciennes alertes
      const retentionDate = new Date();
      retentionDate.setDate(retentionDate.getDate() - this.channels.database.retentionDays);
      
      await this.prisma.alert.deleteMany({
        where: {
          createdAt: {
            lt: retentionDate,
          },
        },
      });
    } catch (error) {
      this.logger.error(`Error saving alert to database: ${error.message}`);
    }
  }

  /**
   * Envoie une alerte via Slack
   * @param alert Alerte à envoyer
   */
  private async sendSlackAlert(alert: Alert): Promise<void> {
    try {
      // Déterminer la couleur en fonction du niveau d'alerte
      let color = '#36a64f'; // vert pour info
      
      switch (alert.level) {
        case 'warning':
          color = '#ffcc00';
          break;
        case 'error':
          color = '#ff9900';
          break;
        case 'critical':
          color = '#ff0000';
          break;
      }
      
      // Construire le message Slack
      const slackMessage = {
        channel: this.channels.slack.channel,
        attachments: [
          {
            color,
            title: `[${alert.level.toUpperCase()}] ${alert.title}`,
            text: alert.message,
            fields: [
              {
                title: 'Source',
                value: alert.source,
                short: true,
              },
              {
                title: 'Timestamp',
                value: new Date().toISOString(),
                short: true,
              },
            ],
            footer: 'Retreat And Be Monitoring',
          },
        ],
      };
      
      // Ajouter les métadonnées si présentes
      if (alert.metadata) {
        slackMessage.attachments[0].fields.push({
          title: 'Metadata',
          value: '```' + JSON.stringify(alert.metadata, null, 2) + '```',
          short: false,
        });
      }
      
      // Envoyer le message
      await firstValueFrom(
        this.httpService.post(this.channels.slack.webhookUrl, slackMessage)
      );
    } catch (error) {
      this.logger.error(`Error sending Slack alert: ${error.message}`);
    }
  }

  /**
   * Envoie une alerte par email
   * @param alert Alerte à envoyer
   */
  private async sendEmailAlert(alert: Alert): Promise<void> {
    // Implémentation à venir
    this.logger.debug(`Email alert not implemented yet: ${alert.title}`);
  }

  /**
   * Envoie une alerte par SMS
   * @param alert Alerte à envoyer
   */
  private async sendSmsAlert(alert: Alert): Promise<void> {
    // Implémentation à venir
    this.logger.debug(`SMS alert not implemented yet: ${alert.title}`);
  }
}
