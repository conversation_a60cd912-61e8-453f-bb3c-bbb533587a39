import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrometheusService } from './prometheus.service';
import { AlertService } from './alert.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../../recommendation/enums/recommendation-type.enum';
import { RecommendationStrategy } from '../../recommendation/enums/recommendation-strategy.enum';

/**
 * Service de monitoring pour le système de recommandation
 * Surveille les performances et la qualité des recommandations
 */
@Injectable()
export class RecommendationMonitoringService {
  private readonly logger = new Logger(RecommendationMonitoringService.name);
  private readonly enabled: boolean;
  private readonly thresholds: {
    responseTime: number;
    errorRate: number;
    cacheHitRate: number;
    recommendationQuality: number;
    diversityScore: number;
    relevanceScore: number;
  };

  constructor(
    private readonly prometheusService: PrometheusService,
    private readonly alertService: AlertService,
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {
    this.enabled = this.configService.get<boolean>('monitoring.recommendation.enabled', true);

    this.thresholds = {
      responseTime: this.configService.get<number>('monitoring.recommendation.thresholds.responseTime', 500),
      errorRate: this.configService.get<number>('monitoring.recommendation.thresholds.errorRate', 5),
      cacheHitRate: this.configService.get<number>('monitoring.recommendation.thresholds.cacheHitRate', 80),
      recommendationQuality: this.configService.get<number>('monitoring.recommendation.thresholds.recommendationQuality', 0.6),
      diversityScore: this.configService.get<number>('monitoring.recommendation.thresholds.diversityScore', 0.4),
      relevanceScore: this.configService.get<number>('monitoring.recommendation.thresholds.relevanceScore', 0.7),
    };

    this.logger.log(`RecommendationMonitoringService initialized with enabled=${this.enabled}`);
    if (this.enabled) {
      this.logger.log(`Thresholds: responseTime=${this.thresholds.responseTime}ms, errorRate=${this.thresholds.errorRate}%, cacheHitRate=${this.thresholds.cacheHitRate}%, recommendationQuality=${this.thresholds.recommendationQuality}, diversityScore=${this.thresholds.diversityScore}, relevanceScore=${this.thresholds.relevanceScore}`);

      // Initialiser les métriques Prometheus
      this.initializeMetrics();
    }
  }

  /**
   * Initialise les métriques Prometheus
   */
  private initializeMetrics(): void {
    // Compteur pour les requêtes de recommandation
    this.prometheusService.registerCounter({
      name: 'recommendation_requests_total',
      help: 'Total number of recommendation requests',
      labelNames: ['type', 'strategy', 'endpoint'],
    });

    // Compteur pour les erreurs de recommandation
    this.prometheusService.registerCounter({
      name: 'recommendation_errors_total',
      help: 'Total number of recommendation errors',
      labelNames: ['type', 'strategy', 'endpoint', 'error_code'],
    });

    // Histogramme pour les temps de réponse
    this.prometheusService.registerHistogram({
      name: 'recommendation_response_time_seconds',
      help: 'Response time of recommendation requests in seconds',
      labelNames: ['type', 'strategy', 'endpoint'],
      buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10],
    });

    // Jauge pour le taux de hit du cache
    this.prometheusService.registerGauge({
      name: 'recommendation_cache_hit_rate',
      help: 'Cache hit rate for recommendation requests',
      labelNames: ['type', 'endpoint'],
    });

    // Jauge pour la qualité des recommandations
    this.prometheusService.registerGauge({
      name: 'recommendation_quality',
      help: 'Quality of recommendations based on user interactions',
      labelNames: ['type', 'strategy'],
    });

    // Compteur pour les interactions utilisateur
    this.prometheusService.registerCounter({
      name: 'recommendation_interactions_total',
      help: 'Total number of user interactions with recommendations',
      labelNames: ['type', 'interaction_type'],
    });

    // Jauge pour la diversité des recommandations
    this.prometheusService.registerGauge({
      name: 'recommendation_diversity_score',
      help: 'Diversity score of recommendations',
      labelNames: ['type', 'strategy'],
    });

    // Jauge pour la pertinence des recommandations
    this.prometheusService.registerGauge({
      name: 'recommendation_relevance_score',
      help: 'Relevance score of recommendations',
      labelNames: ['type', 'strategy'],
    });

    // Histogramme pour les temps d'entraînement des modèles
    this.prometheusService.registerHistogram({
      name: 'recommendation_model_training_time_seconds',
      help: 'Training time of recommendation models in seconds',
      labelNames: ['type', 'strategy'],
      buckets: [0.1, 0.5, 1, 5, 10, 30, 60, 300, 600],
    });

    // Compteur pour les recommandations générées par stratégie
    this.prometheusService.registerCounter({
      name: 'recommendation_generated_total',
      help: 'Total number of recommendations generated',
      labelNames: ['type', 'strategy'],
    });

    // Jauge pour la taille des modèles de recommandation
    this.prometheusService.registerGauge({
      name: 'recommendation_model_size',
      help: 'Size of recommendation models in memory',
      labelNames: ['type', 'strategy'],
    });
  }

  /**
   * Enregistre une requête de recommandation
   * @param type Type d'élément
   * @param strategy Stratégie de recommandation
   * @param endpoint Endpoint de l'API
   * @param responseTime Temps de réponse en millisecondes
   * @param error Erreur éventuelle
   */
  recordRecommendationRequest(
    type: RecommendationType,
    strategy: string,
    endpoint: string,
    responseTime: number,
    error?: { code: string; message: string },
  ): void {
    if (!this.enabled) return;

    // Incrémenter le compteur de requêtes
    this.prometheusService.incrementCounter('recommendation_requests_total', { type, strategy, endpoint });

    // Enregistrer le temps de réponse
    this.prometheusService.observeHistogram('recommendation_response_time_seconds', responseTime / 1000, { type, strategy, endpoint });

    // Si une erreur est survenue, incrémenter le compteur d'erreurs
    if (error) {
      this.prometheusService.incrementCounter('recommendation_errors_total', { type, strategy, endpoint, error_code: error.code });

      // Vérifier si le taux d'erreur dépasse le seuil
      this.checkErrorRate(type, strategy, endpoint);
    }

    // Vérifier si le temps de réponse dépasse le seuil
    if (responseTime > this.thresholds.responseTime) {
      this.alertService.sendAlert({
        level: 'warning',
        source: 'recommendation',
        title: 'Temps de réponse élevé',
        message: `Le temps de réponse pour ${endpoint} (${type}, ${strategy}) est de ${responseTime}ms, ce qui dépasse le seuil de ${this.thresholds.responseTime}ms.`,
        metadata: {
          type,
          strategy,
          endpoint,
          responseTime,
          threshold: this.thresholds.responseTime,
        },
      });
    }
  }

  /**
   * Enregistre un hit ou un miss du cache
   * @param type Type d'élément
   * @param endpoint Endpoint de l'API
   * @param hit Hit ou miss du cache
   */
  recordCacheHit(
    type: RecommendationType,
    endpoint: string,
    hit: boolean,
  ): void {
    if (!this.enabled) return;

    // Récupérer la valeur actuelle
    const currentValue = this.prometheusService.getGaugeValue('recommendation_cache_hit_rate', { type, endpoint }) || 0;
    const currentCount = this.prometheusService.getCounter('recommendation_requests_total', { type, endpoint }) || 1;

    // Calculer le nouveau taux de hit
    const newHitRate = ((currentValue * (currentCount - 1)) + (hit ? 100 : 0)) / currentCount;

    // Mettre à jour la jauge
    this.prometheusService.setGauge('recommendation_cache_hit_rate', newHitRate, { type, endpoint });

    // Vérifier si le taux de hit du cache est inférieur au seuil
    if (newHitRate < this.thresholds.cacheHitRate && currentCount > 100) {
      this.alertService.sendAlert({
        level: 'warning',
        source: 'recommendation',
        title: 'Taux de hit du cache faible',
        message: `Le taux de hit du cache pour ${endpoint} (${type}) est de ${newHitRate.toFixed(2)}%, ce qui est inférieur au seuil de ${this.thresholds.cacheHitRate}%.`,
        metadata: {
          type,
          endpoint,
          hitRate: newHitRate,
          threshold: this.thresholds.cacheHitRate,
        },
      });
    }
  }

  /**
   * Enregistre une interaction utilisateur avec une recommandation
   * @param type Type d'élément
   * @param interactionType Type d'interaction
   */
  recordInteraction(
    type: RecommendationType,
    interactionType: string,
  ): void {
    if (!this.enabled) return;

    // Incrémenter le compteur d'interactions
    this.prometheusService.incrementCounter('recommendation_interactions_total', { type, interaction_type: interactionType });
  }

  /**
   * Vérifie si le taux d'erreur dépasse le seuil
   * @param type Type d'élément
   * @param strategy Stratégie de recommandation
   * @param endpoint Endpoint de l'API
   */
  private checkErrorRate(
    type: RecommendationType,
    strategy: string,
    endpoint: string,
  ): void {
    const totalRequests = this.prometheusService.getCounter('recommendation_requests_total', { type, strategy, endpoint });
    const totalErrors = this.prometheusService.getCounter('recommendation_errors_total', { type, strategy, endpoint });

    if (totalRequests > 0) {
      const errorRate = (totalErrors / totalRequests) * 100;

      if (errorRate > this.thresholds.errorRate && totalRequests > 10) {
        this.alertService.sendAlert({
          level: 'error',
          source: 'recommendation',
          title: 'Taux d\'erreur élevé',
          message: `Le taux d\'erreur pour ${endpoint} (${type}, ${strategy}) est de ${errorRate.toFixed(2)}%, ce qui dépasse le seuil de ${this.thresholds.errorRate}%.`,
          metadata: {
            type,
            strategy,
            endpoint,
            errorRate,
            threshold: this.thresholds.errorRate,
          },
        });
      }
    }
  }

  /**
   * Calcule et enregistre la qualité des recommandations
   * Exécuté toutes les heures
   */
  @Cron(CronExpression.EVERY_HOUR)
  async calculateRecommendationQuality(): Promise<void> {
    if (!this.enabled) return;

    this.logger.log('Calculating recommendation quality...');

    try {
      // Pour chaque type d'élément et stratégie
      for (const type of Object.values(RecommendationType)) {
        // Utiliser les valeurs de l'enum RecommendationStrategy
        for (const strategy of Object.values(RecommendationStrategy)) {
          // Calculer la qualité des recommandations
          const quality = await this.calculateQualityForTypeAndStrategy(type, strategy);

          // Mettre à jour la jauge de qualité
          this.prometheusService.setGauge('recommendation_quality', quality, { type, strategy });

          // Calculer et mettre à jour la diversité
          const diversity = await this.calculateDiversityForTypeAndStrategy(type, strategy);
          this.prometheusService.setGauge('recommendation_diversity_score', diversity, { type, strategy });

          // Calculer et mettre à jour la pertinence
          const relevance = await this.calculateRelevanceForTypeAndStrategy(type, strategy);
          this.prometheusService.setGauge('recommendation_relevance_score', relevance, { type, strategy });

          // Vérifier si la qualité est inférieure au seuil
          if (quality < this.thresholds.recommendationQuality) {
            this.alertService.sendAlert({
              level: 'warning',
              source: 'recommendation',
              title: 'Qualité des recommandations faible',
              message: `La qualité des recommandations pour ${type} (${strategy}) est de ${quality.toFixed(2)}, ce qui est inférieur au seuil de ${this.thresholds.recommendationQuality}.`,
              metadata: {
                type,
                strategy,
                quality,
                threshold: this.thresholds.recommendationQuality,
              },
            });
          }

          // Vérifier si la diversité est inférieure au seuil
          if (diversity < this.thresholds.diversityScore) {
            this.alertService.sendAlert({
              level: 'warning',
              source: 'recommendation',
              title: 'Diversité des recommandations faible',
              message: `La diversité des recommandations pour ${type} (${strategy}) est de ${diversity.toFixed(2)}, ce qui est inférieur au seuil de ${this.thresholds.diversityScore}.`,
              metadata: {
                type,
                strategy,
                diversity,
                threshold: this.thresholds.diversityScore,
              },
            });
          }

          // Vérifier si la pertinence est inférieure au seuil
          if (relevance < this.thresholds.relevanceScore) {
            this.alertService.sendAlert({
              level: 'warning',
              source: 'recommendation',
              title: 'Pertinence des recommandations faible',
              message: `La pertinence des recommandations pour ${type} (${strategy}) est de ${relevance.toFixed(2)}, ce qui est inférieur au seuil de ${this.thresholds.relevanceScore}.`,
              metadata: {
                type,
                strategy,
                relevance,
                threshold: this.thresholds.relevanceScore,
              },
            });
          }
        }
      }

      this.logger.log('Recommendation quality calculation completed');
    } catch (error) {
      this.logger.error(`Error calculating recommendation quality: ${error.message}`);
    }
  }

  /**
   * Calcule la qualité des recommandations pour un type et une stratégie donnés
   * @param type Type d'élément
   * @param strategy Stratégie de recommandation
   * @returns Qualité des recommandations (entre 0 et 1)
   */
  private async calculateQualityForTypeAndStrategy(
    type: RecommendationType,
    strategy: string,
  ): Promise<number> {
    // Récupérer les interactions des 7 derniers jours
    const recentInteractions = await this.prisma.userInteraction.findMany({
      where: {
        itemType: type,
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 derniers jours
        },
        metadata: {
          path: ['sources'],
          array_contains: [strategy.toLowerCase()],
        },
      },
      select: {
        interactionType: true,
        metadata: true,
      },
    });

    if (recentInteractions.length === 0) {
      return 0;
    }

    // Calculer la qualité en fonction des types d'interaction
    let qualityScore = 0;
    let totalWeight = 0;

    for (const interaction of recentInteractions) {
      let weight = 0;

      switch (interaction.interactionType) {
        case 'VIEW':
          weight = 1;
          break;
        case 'LIKE':
          weight = 5;
          break;
        case 'ENROLL':
        case 'PURCHASE':
          weight = 10;
          break;
        case 'SHARE':
          weight = 3;
          break;
        case 'BOOKMARK':
          weight = 2;
          break;
        default:
          weight = 0;
      }

      qualityScore += weight;
      totalWeight += 10; // Poids maximum possible
    }

    return totalWeight > 0 ? qualityScore / totalWeight : 0;
  }

  /**
   * Calcule la diversité des recommandations pour un type et une stratégie donnés
   * @param type Type d'élément
   * @param strategy Stratégie de recommandation
   * @returns Score de diversité (entre 0 et 1)
   */
  private async calculateDiversityForTypeAndStrategy(
    type: RecommendationType,
    strategy: string,
  ): Promise<number> {
    try {
      // Récupérer les recommandations récentes
      const recentRecommendations = await this.prisma.recommendation.findMany({
        where: {
          type,
          metadata: {
            path: ['sources'],
            array_contains: [strategy.toLowerCase()],
          },
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 derniers jours
          },
        },
        select: {
          itemId: true,
          metadata: true,
        },
        take: 100, // Limiter à 100 recommandations pour l'analyse
      });

      if (recentRecommendations.length < 5) {
        return 0; // Pas assez de données pour calculer la diversité
      }

      // Calculer la diversité des catégories
      const categories = new Set();
      const tags = new Set();
      const locations = new Set();

      for (const recommendation of recentRecommendations) {
        const metadata = recommendation.metadata as any;

        if (metadata.category) {
          categories.add(metadata.category);
        }

        if (metadata.tags && Array.isArray(metadata.tags)) {
          metadata.tags.forEach((tag: string) => tags.add(tag));
        }

        if (metadata.location) {
          if (typeof metadata.location === 'string') {
            locations.add(metadata.location);
          } else if (metadata.location.country) {
            locations.add(metadata.location.country);
          }
        }
      }

      // Calculer le score de diversité
      const categoryDiversity = categories.size / Math.min(10, recentRecommendations.length);
      const tagDiversity = tags.size / Math.min(20, recentRecommendations.length * 3);
      const locationDiversity = locations.size / Math.min(5, recentRecommendations.length);

      // Combiner les scores avec des poids
      const diversityScore = (
        categoryDiversity * 0.4 +
        tagDiversity * 0.4 +
        locationDiversity * 0.2
      );

      return Math.min(1, diversityScore);
    } catch (error) {
      this.logger.error(`Error calculating diversity: ${error.message}`);
      return 0;
    }
  }

  /**
   * Calcule la pertinence des recommandations pour un type et une stratégie donnés
   * @param type Type d'élément
   * @param strategy Stratégie de recommandation
   * @returns Score de pertinence (entre 0 et 1)
   */
  private async calculateRelevanceForTypeAndStrategy(
    type: RecommendationType,
    strategy: string,
  ): Promise<number> {
    try {
      // Récupérer les interactions des 7 derniers jours
      const recentInteractions = await this.prisma.userInteraction.findMany({
        where: {
          itemType: type,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 derniers jours
          },
          metadata: {
            path: ['sources'],
            array_contains: [strategy.toLowerCase()],
          },
        },
        select: {
          userId: true,
          itemId: true,
          interactionType: true,
        },
      });

      if (recentInteractions.length === 0) {
        return 0;
      }

      // Récupérer les recommandations correspondantes
      const userItemPairs = recentInteractions.map((interaction: { userId: string; itemId: string }) => ({
        userId: interaction.userId,
        itemId: interaction.itemId,
      }));

      const recommendations = await this.prisma.recommendation.findMany({
        where: {
          OR: userItemPairs.map((pair: { userId: string; itemId: string }) => ({
            userId: pair.userId,
            itemId: pair.itemId,
          })),
          type,
        },
        select: {
          userId: true,
          itemId: true,
          score: true,
        },
      });

      if (recommendations.length === 0) {
        return 0;
      }

      // Créer une map des scores de recommandation
      const recommendationScores = new Map();
      for (const recommendation of recommendations) {
        const key = `${recommendation.userId}:${recommendation.itemId}`;
        recommendationScores.set(key, recommendation.score);
      }

      // Calculer la corrélation entre les scores de recommandation et les interactions
      let totalScore = 0;
      let count = 0;

      for (const interaction of recentInteractions) {
        const key = `${interaction.userId}:${interaction.itemId}`;
        const recommendationScore = recommendationScores.get(key);

        if (recommendationScore !== undefined) {
          let interactionWeight = 0;

          switch (interaction.interactionType) {
            case 'VIEW':
              interactionWeight = 0.2;
              break;
            case 'LIKE':
              interactionWeight = 0.8;
              break;
            case 'ENROLL':
            case 'PURCHASE':
              interactionWeight = 1.0;
              break;
            case 'SHARE':
              interactionWeight = 0.7;
              break;
            case 'BOOKMARK':
              interactionWeight = 0.6;
              break;
            default:
              interactionWeight = 0.1;
          }

          // Calculer la correspondance entre le score de recommandation et l'interaction
          const match = Math.min(1, recommendationScore * interactionWeight);
          totalScore += match;
          count++;
        }
      }

      return count > 0 ? totalScore / count : 0;
    } catch (error) {
      this.logger.error(`Error calculating relevance: ${error.message}`);
      return 0;
    }
  }
}
