import { Injectable, Logger } from '@nestjs/common';
import { MetricsService } from './services/metrics.service';
import { PerformanceMonitoringService } from './services/performance-monitoring.service';
import { PrometheusService } from './services/prometheus.service';
import { AlertService } from './services/alert.service';

/**
 * Service principal du module de monitoring
 * Orchestre tous les services de monitoring et fournit une interface unifiée
 */
@Injectable()
export class MonitoringService {
  private readonly logger = new Logger(MonitoringService.name);

  constructor(
    private readonly metricsService: MetricsService,
    private readonly performanceService: PerformanceMonitoringService,
    private readonly prometheusService: PrometheusService,
    private readonly alertService: AlertService,
  ) {}

  /**
   * Obtenir un aperçu complet du monitoring
   */
  async getMonitoringOverview() {
    try {
      this.logger.log('Génération de l\'aperçu de monitoring');

      const [
        performanceSummary,
        systemMetrics,
        applicationMetrics,
        activeAlerts,
      ] = await Promise.all([
        this.performanceService.getPerformanceSummary(),
        this.metricsService.getSystemMetrics(),
        this.metricsService.getApplicationMetrics(),
        this.performanceService.getActiveAlerts(),
      ]);

      return {
        status: this.determineOverallStatus(performanceSummary, activeAlerts),
        performance: performanceSummary,
        system: systemMetrics,
        application: applicationMetrics,
        alerts: {
          total: activeAlerts.length,
          critical: activeAlerts.filter(a => a.severity === 'critical').length,
          warning: activeAlerts.filter(a => a.severity === 'medium' || a.severity === 'high').length,
          recent: activeAlerts.slice(0, 5),
        },
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Erreur lors de la génération de l\'aperçu de monitoring:', error);
      throw error;
    }
  }

  /**
   * Enregistrer une métrique personnalisée
   */
  recordCustomMetric(name: string, value: number, labels?: Record<string, string>) {
    try {
      this.prometheusService.incrementCounter(name, labels);
      this.logger.debug(`Métrique personnalisée enregistrée: ${name} = ${value}`);
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de la métrique ${name}:`, error);
    }
  }

  /**
   * Enregistrer le temps de réponse d'une opération
   */
  recordResponseTime(operation: string, duration: number, labels?: Record<string, string>) {
    try {
      this.prometheusService.observeHistogram(`${operation}_duration_seconds`, duration / 1000, labels);
      this.logger.debug(`Temps de réponse enregistré: ${operation} = ${duration}ms`);
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement du temps de réponse pour ${operation}:`, error);
    }
  }

  /**
   * Déclencher une alerte personnalisée
   */
  async triggerAlert(
    level: 'info' | 'warning' | 'error' | 'critical',
    source: string,
    title: string,
    message: string,
    metadata?: any,
  ) {
    try {
      await this.alertService.sendAlert({
        level,
        source,
        title,
        message,
        metadata,
      });
      this.logger.log(`Alerte déclenchée: ${level} - ${title}`);
    } catch (error) {
      this.logger.error('Erreur lors du déclenchement de l\'alerte:', error);
    }
  }

  /**
   * Obtenir les métriques de santé du système
   */
  async getHealthMetrics() {
    try {
      const currentMetrics = this.performanceService.getCurrentMetrics();
      const systemMetrics = await this.metricsService.getSystemMetrics();

      return {
        healthy: currentMetrics ? this.isSystemHealthy(currentMetrics) : false,
        metrics: {
          current: currentMetrics,
          system: systemMetrics,
        },
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Erreur lors de la récupération des métriques de santé:', error);
      return {
        healthy: false,
        error: error.message,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Générer un rapport de monitoring complet
   */
  async generateMonitoringReport() {
    try {
      this.logger.log('Génération du rapport de monitoring complet');

      const [
        overview,
        performanceReport,
        prometheusMetrics,
      ] = await Promise.all([
        this.getMonitoringOverview(),
        this.performanceService.generatePerformanceReport(),
        this.metricsService.getMetrics(),
      ]);

      return {
        overview,
        performance: performanceReport,
        metrics: {
          prometheus: prometheusMetrics,
          summary: overview.system,
        },
        recommendations: this.generateMonitoringRecommendations(overview, performanceReport),
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Erreur lors de la génération du rapport de monitoring:', error);
      throw error;
    }
  }

  /**
   * Déterminer le statut global du système
   */
  private determineOverallStatus(performanceSummary: any, alerts: any[]): string {
    const criticalAlerts = alerts.filter(a => a.severity === 'critical');
    const warningAlerts = alerts.filter(a => a.severity === 'medium' || a.severity === 'high');

    if (criticalAlerts.length > 0) {
      return 'critical';
    }

    if (warningAlerts.length > 3) {
      return 'warning';
    }

    if (performanceSummary.status === 'critical') {
      return 'critical';
    }

    if (performanceSummary.status === 'warning') {
      return 'warning';
    }

    return 'healthy';
  }

  /**
   * Vérifier si le système est en bonne santé
   */
  private isSystemHealthy(metrics: any): boolean {
    if (!metrics) return false;

    // Vérifier la mémoire (< 80% de la heap)
    const memoryUsagePercent = (metrics.memory.heapUsed / metrics.memory.heapTotal) * 100;
    if (memoryUsagePercent > 80) return false;

    // Vérifier la base de données (< 1 seconde)
    if (metrics.database.responseTime > 1000) return false;

    // Vérifier la connectivité
    if (!metrics.database.isConnected) return false;

    return true;
  }

  /**
   * Générer des recommandations de monitoring
   */
  private generateMonitoringRecommendations(overview: any, performanceReport: any): string[] {
    const recommendations: string[] = [];

    // Recommandations basées sur les alertes
    if (overview.alerts?.critical > 0) {
      recommendations.push('Traiter immédiatement les alertes critiques');
    }

    if (overview.alerts?.warning > 5) {
      recommendations.push('Examiner et résoudre les alertes d\'avertissement');
    }

    // Recommandations basées sur les performances
    if (performanceReport.statistics?.memory?.trend === 'increasing') {
      recommendations.push('Surveiller la croissance de l\'utilisation mémoire');
    }

    if (performanceReport.statistics?.database?.averageResponseTime > 500) {
      recommendations.push('Optimiser les performances de la base de données');
    }

    // Recommandations générales
    if (recommendations.length === 0) {
      recommendations.push('Système en bonne santé - continuer la surveillance');
    }

    return recommendations;
  }

  /**
   * Démarrer la surveillance automatique
   */
  async startMonitoring() {
    this.logger.log('Démarrage de la surveillance automatique');

    // La surveillance est déjà démarrée via les services injectés
    // qui utilisent des tâches cron pour la collecte automatique

    return {
      status: 'started',
      services: {
        metrics: 'active',
        performance: 'active',
        alerts: 'active',
        prometheus: 'active',
      },
      timestamp: new Date(),
    };
  }

  /**
   * Arrêter la surveillance (pour les tests ou la maintenance)
   */
  async stopMonitoring() {
    this.logger.log('Arrêt de la surveillance');

    // Note: Dans une implémentation complète, on pourrait arrêter
    // les tâches cron et nettoyer les ressources

    return {
      status: 'stopped',
      timestamp: new Date(),
    };
  }
}
