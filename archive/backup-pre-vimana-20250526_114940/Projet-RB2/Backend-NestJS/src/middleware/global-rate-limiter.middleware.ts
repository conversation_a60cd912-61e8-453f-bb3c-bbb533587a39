import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
import { RateLimiterService } from '../modules/security/services/rate-limiter.service';
import { SecurityEventService } from '../modules/security/services/security-event.service';
import { SecurityEventSeverity } from '../modules/security/dto/create-security-event.dto';

/**
 * Middleware global de limitation de débit
 * Protège l'API contre les attaques par force brute et le scraping
 */
@Injectable()
export class GlobalRateLimiterMiddleware implements NestMiddleware {
  private readonly logger = new Logger(GlobalRateLimiterMiddleware.name);
  private readonly excludedPaths: string[] = [
    '/api/v1/health',
    '/api/docs',
    '/favicon.ico',
  ];

  constructor(
    private readonly configService: ConfigService,
    private readonly rateLimiterService: RateLimiterService,
    private readonly securityEventService: SecurityEventService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    // Ignorer les chemins exclus
    if (this.isPathExcluded(req.path)) {
      return next();
    }

    // Obtenir les paramètres de limitation de débit
    const rateLimit = this.configService.get('security.rateLimit');
    const windowMs = rateLimit.windowMs;
    const maxRequests = rateLimit.max;

    // Créer une clé unique basée sur l'IP et le chemin
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const path = req.path;
    const method = req.method;
    
    // Clé générale pour toutes les requêtes de cette IP
    const generalKey = `rate-limit:${ip}`;
    
    // Clé spécifique pour ce chemin et cette méthode
    const specificKey = `rate-limit:${ip}:${method}:${path}`;

    try {
      // Vérifier la limitation générale (toutes les requêtes)
      const generalResult = await this.rateLimiterService.isRateLimited(
        generalKey,
        maxRequests,
        windowMs,
      );

      // Vérifier la limitation spécifique (par chemin)
      // Utiliser une limite plus stricte pour les chemins sensibles
      const pathLimit = this.getPathSpecificLimit(path, maxRequests);
      const specificResult = await this.rateLimiterService.isRateLimited(
        specificKey,
        pathLimit,
        windowMs,
      );

      // Ajouter les en-têtes de limitation de débit
      res.setHeader('X-RateLimit-Limit', maxRequests.toString());
      res.setHeader('X-RateLimit-Remaining', generalResult.remaining.toString());
      res.setHeader('X-RateLimit-Reset', new Date(generalResult.resetAt).toISOString());

      // Si l'une des limites est dépassée, renvoyer une erreur 429
      if (generalResult.limited || specificResult.limited) {
        // Enregistrer l'événement de sécurité
        await this.securityEventService.logSecurityEvent({
          eventType: 'RATE_LIMIT_EXCEEDED',
          severity: SecurityEventSeverity.WARNING,
          source: 'RATE_LIMITER',
          details: {
            ip,
            path,
            method,
            generalRemaining: generalResult.remaining,
            specificRemaining: specificResult.remaining,
            resetAt: new Date(generalResult.resetAt).toISOString(),
          },
        });

        this.logger.warn(`Rate limit exceeded for ${ip} on ${method} ${path}`);
        
        res.status(429).json({
          statusCode: 429,
          message: 'Too Many Requests',
          error: 'Rate limit exceeded',
          retryAfter: Math.ceil((generalResult.resetAt - Date.now()) / 1000),
        });
        return;
      }

      next();
    } catch (error) {
      // En cas d'erreur, laisser passer la requête mais enregistrer l'erreur
      this.logger.error(`Error in rate limiter: ${error.message}`, error.stack);
      next();
    }
  }

  private isPathExcluded(path: string): boolean {
    return this.excludedPaths.some(excludedPath => path.startsWith(excludedPath));
  }

  private getPathSpecificLimit(path: string, defaultLimit: number): number {
    // Appliquer des limites plus strictes pour les chemins sensibles
    if (path.includes('/auth/login') || path.includes('/auth/register')) {
      return Math.floor(defaultLimit / 5); // 20% de la limite par défaut
    }
    
    if (path.includes('/users') || path.includes('/admin')) {
      return Math.floor(defaultLimit / 2); // 50% de la limite par défaut
    }
    
    return defaultLimit;
  }
}
