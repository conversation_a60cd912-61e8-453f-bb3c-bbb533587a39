import { Injectable, NestMiddleware, Logger, UnauthorizedException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

/**
 * Middleware pour la protection contre les attaques CSRF (Cross-Site Request Forgery)
 * Utilise le modèle Double Submit Cookie
 */
@Injectable()
export class CsrfMiddleware implements NestMiddleware {
  private readonly logger = new Logger(CsrfMiddleware.name);
  private readonly csrfCookieName: string;
  private readonly csrfHeaderName: string;
  private readonly csrfCookieOptions: any;
  private readonly excludedPaths: string[];
  private readonly excludedMethods: string[];

  constructor(private readonly configService: ConfigService) {
    this.csrfCookieName = this.configService.get<string>('CSRF_COOKIE_NAME', 'XSRF-TOKEN');
    this.csrfHeaderName = this.configService.get<string>('CSRF_HEADER_NAME', 'X-XSRF-TOKEN');
    this.csrfCookieOptions = {
      httpOnly: false, // Le cookie doit être accessible par JavaScript
      secure: this.configService.get<string>('NODE_ENV') === 'production',
      sameSite: this.configService.get<string>('NODE_ENV') === 'production' ? 'strict' : 'lax',
      path: '/',
      maxAge: 24 * 60 * 60 * 1000, // 24 heures
    };
    
    // Chemins exclus de la protection CSRF
    this.excludedPaths = [
      '/api/v1/auth/login',
      '/api/v1/auth/register',
      '/api/v1/auth/refresh-token',
      '/api/docs',
      '/api/v1/health',
    ];
    
    // Méthodes HTTP sûres (ne modifient pas l'état du serveur)
    this.excludedMethods = ['GET', 'HEAD', 'OPTIONS'];
  }

  /**
   * Middleware pour la protection CSRF
   * @param req Requête HTTP
   * @param res Réponse HTTP
   * @param next Fonction suivante
   */
  use(req: Request, res: Response, next: NextFunction) {
    // Ignorer les méthodes sûres
    if (this.excludedMethods.includes(req.method)) {
      // Pour les requêtes GET, générer un nouveau token CSRF
      if (req.method === 'GET') {
        this.setNewCsrfToken(res);
      }
      return next();
    }
    
    // Ignorer les chemins exclus
    if (this.isPathExcluded(req.path)) {
      return next();
    }
    
    // Vérifier le token CSRF
    const csrfCookie = req.cookies[this.csrfCookieName];
    const csrfHeader = req.headers[this.csrfHeaderName.toLowerCase()] as string;
    
    if (!csrfCookie || !csrfHeader || csrfCookie !== csrfHeader) {
      this.logger.warn(`CSRF token mismatch: cookie=${csrfCookie}, header=${csrfHeader}, path=${req.path}`);
      
      // Générer un nouveau token CSRF
      this.setNewCsrfToken(res);
      
      throw new UnauthorizedException('CSRF token validation failed');
    }
    
    // Générer un nouveau token CSRF après chaque requête réussie
    this.setNewCsrfToken(res);
    
    next();
  }

  /**
   * Vérifier si le chemin est exclu de la protection CSRF
   * @param path Chemin de la requête
   */
  private isPathExcluded(path: string): boolean {
    return this.excludedPaths.some(excludedPath => path.startsWith(excludedPath));
  }

  /**
   * Générer et définir un nouveau token CSRF
   * @param res Réponse HTTP
   */
  private setNewCsrfToken(res: Response): void {
    // Générer un token aléatoire
    const csrfToken = crypto.randomBytes(32).toString('hex');
    
    // Définir le cookie CSRF
    res.cookie(this.csrfCookieName, csrfToken, this.csrfCookieOptions);
    
    // Exposer le token dans l'en-tête pour les applications SPA
    res.setHeader(this.csrfHeaderName, csrfToken);
  }
}
