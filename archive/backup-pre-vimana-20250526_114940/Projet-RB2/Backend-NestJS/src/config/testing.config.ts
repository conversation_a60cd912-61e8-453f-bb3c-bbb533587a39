import { registerAs } from '@nestjs/config';

/**
 * Configuration pour les tests automatisés
 */
export default registerAs('testing', () => ({
  // Configuration générale
  enabled: process.env.TESTING_ENABLED === 'true' || true,
  
  // Tests cryptographiques
  crypto: {
    enabled: process.env.CRYPTO_TESTING_ENABLED === 'true' || true,
    autoTestIntervalMs: parseInt(process.env.CRYPTO_AUTO_TEST_INTERVAL_MS || '3600000', 10), // 1 heure
    testDataSize: parseInt(process.env.CRYPTO_TEST_DATA_SIZE || '100', 10),
    testTimeout: parseInt(process.env.CRYPTO_TEST_TIMEOUT || '30000', 10), // 30 secondes
  },
  
  // Tests de performance
  performance: {
    enabled: process.env.PERFORMANCE_TESTING_ENABLED === 'true' || true,
    iterations: parseInt(process.env.PERFORMANCE_TEST_ITERATIONS || '100', 10),
    dataSize: parseInt(process.env.PERFORMANCE_TEST_DATA_SIZE || '1024', 10),
  },
  
  // Tests de sécurité
  security: {
    enabled: process.env.SECURITY_TESTING_ENABLED === 'true' || true,
    scanInterval: parseInt(process.env.SECURITY_SCAN_INTERVAL || '86400000', 10), // 24 heures
    vulnerabilityScanEnabled: process.env.VULNERABILITY_SCAN_ENABLED === 'true' || true,
    penetrationTestEnabled: process.env.PENETRATION_TEST_ENABLED === 'true' || false,
  },
}));
