import { registerAs } from '@nestjs/config';

/**
 * Configuration pour les services de chiffrement
 */
export default registerAs('encryption', () => ({
  // Configuration générale
  enabled: process.env.ENCRYPTION_ENABLED === 'true',

  // Gestion des clés
  keyManagement: {
    keyRotationInterval: parseInt(process.env.KEY_ROTATION_INTERVAL || '604800000', 10), // 7 jours par défaut
    keyExpiry: parseInt(process.env.KEY_EXPIRY || '2592000000', 10), // 30 jours par défaut
  },

  // Chiffrement de bout en bout
  endToEnd: {
    enabled: process.env.E2E_ENCRYPTION_ENABLED === 'true',
    curve: process.env.E2E_ENCRYPTION_CURVE || 'prime256v1',
    algorithm: process.env.E2E_ENCRYPTION_ALGORITHM || 'aes-256-gcm',
  },

  // Chiffrement homomorphique
  homomorphic: {
    enabled: process.env.HOMOMORPHIC_ENCRYPTION_ENABLED === 'true',
    scheme: process.env.HOMOMORPHIC_ENCRYPTION_SCHEME || 'BFV',
    securityLevel: parseInt(process.env.HOMOMORPHIC_ENCRYPTION_SECURITY_LEVEL || '128', 10),
    polyModulusDegree: parseInt(process.env.HOMOMORPHIC_ENCRYPTION_POLY_MODULUS_DEGREE || '4096', 10),
    plaintextModulus: parseInt(process.env.HOMOMORPHIC_ENCRYPTION_PLAINTEXT_MODULUS || '1024', 10),
    coeffModulusBits: process.env.HOMOMORPHIC_ENCRYPTION_COEFF_MODULUS_BITS
      ? process.env.HOMOMORPHIC_ENCRYPTION_COEFF_MODULUS_BITS.split(',').map(bit => parseInt(bit, 10))
      : [60, 40, 40, 60],
  },

  // Chiffrement résistant aux ordinateurs quantiques
  quantumResistant: {
    enabled: process.env.QUANTUM_RESISTANT_ENABLED === 'true',
    algorithm: process.env.QUANTUM_RESISTANT_ALGORITHM || 'hybrid',
    keySize: parseInt(process.env.QUANTUM_RESISTANT_KEY_SIZE || '3072', 10),
    hybridClassicalAlgorithm: process.env.QUANTUM_RESISTANT_HYBRID_CLASSICAL_ALGORITHM || 'rsa',
    hybridClassicalKeySize: parseInt(process.env.QUANTUM_RESISTANT_HYBRID_CLASSICAL_KEY_SIZE || '4096', 10),
  },

  // Chiffrement pour les services financiers
  financial: {
    enabled: process.env.FINANCIAL_ENCRYPTION_ENABLED === 'true' || true,
    algorithm: process.env.FINANCIAL_ENCRYPTION_ALGORITHM || 'aes-256-gcm',
  },

  // Chiffrement pour le stockage décentralisé
  storage: {
    enabled: process.env.STORAGE_ENCRYPTION_ENABLED === 'true' || true,
    algorithm: process.env.STORAGE_ENCRYPTION_ALGORITHM || 'aes-256-gcm',
    rsaBits: parseInt(process.env.STORAGE_ENCRYPTION_RSA_BITS || '4096', 10),
  },

  // Chiffrement des données sensibles au repos
  sensitiveData: {
    enabled: process.env.SENSITIVE_DATA_ENCRYPTION_ENABLED === 'true' || true,
    algorithm: process.env.SENSITIVE_DATA_ENCRYPTION_ALGORITHM || 'aes-256-gcm',
    sensitiveFields: process.env.SENSITIVE_DATA_FIELDS ? process.env.SENSITIVE_DATA_FIELDS.split(',') : undefined,
    sensitiveTypes: process.env.SENSITIVE_DATA_TYPES ? process.env.SENSITIVE_DATA_TYPES.split(',') as any : undefined,
  },
}));
