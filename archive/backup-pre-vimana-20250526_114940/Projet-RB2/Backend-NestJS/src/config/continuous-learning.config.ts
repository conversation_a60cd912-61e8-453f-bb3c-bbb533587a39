import { registerAs } from '@nestjs/config';

/**
 * Configuration pour le module d'apprentissage continu
 */
export default registerAs('recommendation.continuousLearning', () => ({
  /**
   * Taux d'apprentissage pour les mises à jour incrémentielles
   * Valeurs plus élevées = adaptation plus rapide aux nouvelles interactions
   * Valeurs plus basses = stabilité accrue
   */
  learningRate: parseFloat(process.env.CONTINUOUS_LEARNING_RATE || '0.1'),

  /**
   * Facteur d'oubli pour les anciennes interactions
   * Valeurs plus élevées = les anciennes interactions restent importantes plus longtemps
   * Valeurs plus basses = les nouvelles interactions ont plus d'impact
   */
  forgettingFactor: parseFloat(process.env.CONTINUOUS_LEARNING_FORGETTING_FACTOR || '0.95'),

  /**
   * Seuil pour détecter les changements de comportement
   * Valeurs plus élevées = moins de détections de changements
   * Valeurs plus basses = plus de détections de changements
   */
  changeDetectionThreshold: parseFloat(process.env.CONTINUOUS_LEARNING_CHANGE_THRESHOLD || '0.3'),

  /**
   * Fenêtre temporelle pour l'analyse des interactions récentes (en heures)
   */
  recentInteractionsWindow: parseInt(process.env.CONTINUOUS_LEARNING_WINDOW_HOURS || '24', 10),

  /**
   * Nombre minimum d'interactions pour déclencher une mise à jour
   */
  minInteractionsForUpdate: parseInt(process.env.CONTINUOUS_LEARNING_MIN_INTERACTIONS || '5', 10),

  /**
   * Intervalle de mise à jour des modèles (en minutes)
   */
  modelUpdateInterval: parseInt(process.env.CONTINUOUS_LEARNING_UPDATE_INTERVAL || '15', 10),

  /**
   * Poids des différents types d'interactions
   * Valeurs plus élevées = plus d'impact sur les recommandations
   * Valeurs négatives = impact négatif (comme les dislikes)
   */
  interactionWeights: {
    VIEW: parseFloat(process.env.CONTINUOUS_LEARNING_WEIGHT_VIEW || '0.1'),
    LIKE: parseFloat(process.env.CONTINUOUS_LEARNING_WEIGHT_LIKE || '0.5'),
    DISLIKE: parseFloat(process.env.CONTINUOUS_LEARNING_WEIGHT_DISLIKE || '-0.5'),
    BOOKMARK: parseFloat(process.env.CONTINUOUS_LEARNING_WEIGHT_BOOKMARK || '0.7'),
    SHARE: parseFloat(process.env.CONTINUOUS_LEARNING_WEIGHT_SHARE || '0.8'),
    PURCHASE: parseFloat(process.env.CONTINUOUS_LEARNING_WEIGHT_PURCHASE || '1.0'),
    RECOMMENDATION_VIEW: parseFloat(process.env.CONTINUOUS_LEARNING_WEIGHT_REC_VIEW || '0.05'),
  },

  /**
   * Activer/désactiver la détection des comportements aberrants
   */
  enableOutlierDetection: process.env.CONTINUOUS_LEARNING_ENABLE_OUTLIER_DETECTION !== 'false',

  /**
   * Seuil pour la détection des comportements aberrants
   * Valeurs plus élevées = moins de détections d'anomalies
   * Valeurs plus basses = plus de détections d'anomalies
   */
  outlierDetectionThreshold: parseFloat(process.env.CONTINUOUS_LEARNING_OUTLIER_THRESHOLD || '2.5'),
}));
