import { registerAs } from '@nestjs/config';

export default registerAs('app', () => ({
  nodeEnv: process.env.NODE_ENV || 'development',
  name: process.env.APP_NAME || 'Retreat And Be API',
  port: parseInt(process.env.PORT ?? '3000', 10),
  apiPrefix: process.env.API_PREFIX || '/api/v1',
  fallbackLanguage: process.env.APP_FALLBACK_LANGUAGE || 'fr',
  headerLanguage: process.env.APP_HEADER_LANGUAGE || 'x-custom-lang',
  frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',
  uploadDir: process.env.UPLOAD_DIR || 'uploads',
  maxUploadSize: parseInt(process.env.MAX_UPLOAD_SIZE ?? String(10 * 1024 * 1024), 10), // 10MB
  allowedMimeTypes: (process.env.ALLOWED_MIME_TYPES || 'image/jpeg,image/png,image/gif,application/pdf').split(','),
}));
