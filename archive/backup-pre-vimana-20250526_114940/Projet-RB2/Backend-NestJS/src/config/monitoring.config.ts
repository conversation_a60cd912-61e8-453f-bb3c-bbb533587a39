import { registerAs } from '@nestjs/config';

/**
 * Configuration pour le monitoring
 */
export default registerAs('monitoring', () => ({
  /**
   * Configuration de Prometheus
   */
  prometheus: {
    /**
     * Activer Prometheus
     */
    enabled: process.env.MONITORING_PROMETHEUS_ENABLED !== 'false',
  },
  
  /**
   * Configuration des alertes
   */
  alerts: {
    /**
     * Activer les alertes
     */
    enabled: process.env.MONITORING_ALERTS_ENABLED !== 'false',
    
    /**
     * Canaux d'alerte
     */
    channels: {
      /**
       * Configuration de Slack
       */
      slack: {
        enabled: process.env.MONITORING_ALERTS_SLACK_ENABLED === 'true',
        webhookUrl: process.env.MONITORING_ALERTS_SLACK_WEBHOOK_URL || '',
        channel: process.env.MONITORING_ALERTS_SLACK_CHANNEL || '#alerts',
      },
      
      /**
       * Configuration des emails
       */
      email: {
        enabled: process.env.MONITORING_ALERTS_EMAIL_ENABLED === 'true',
        recipients: (process.env.MONITORING_ALERTS_EMAIL_RECIPIENTS || '').split(',').filter(Boolean),
      },
      
      /**
       * Configuration des SMS
       */
      sms: {
        enabled: process.env.MONITORING_ALERTS_SMS_ENABLED === 'true',
        recipients: (process.env.MONITORING_ALERTS_SMS_RECIPIENTS || '').split(',').filter(Boolean),
      },
      
      /**
       * Configuration de la base de données
       */
      database: {
        enabled: process.env.MONITORING_ALERTS_DATABASE_ENABLED !== 'false',
        retentionDays: parseInt(process.env.MONITORING_ALERTS_DATABASE_RETENTION_DAYS || '30', 10),
      },
    },
    
    /**
     * Configuration du throttling
     */
    throttling: {
      enabled: process.env.MONITORING_ALERTS_THROTTLING_ENABLED !== 'false',
      windowSeconds: parseInt(process.env.MONITORING_ALERTS_THROTTLING_WINDOW_SECONDS || '300', 10),
      maxAlertsPerWindow: parseInt(process.env.MONITORING_ALERTS_THROTTLING_MAX_ALERTS_PER_WINDOW || '5', 10),
    },
  },
  
  /**
   * Configuration du monitoring des recommandations
   */
  recommendation: {
    /**
     * Activer le monitoring des recommandations
     */
    enabled: process.env.MONITORING_RECOMMENDATION_ENABLED !== 'false',
    
    /**
     * Seuils d'alerte
     */
    thresholds: {
      /**
       * Seuil de temps de réponse (en ms)
       */
      responseTime: parseInt(process.env.MONITORING_RECOMMENDATION_THRESHOLD_RESPONSE_TIME || '500', 10),
      
      /**
       * Seuil de taux d'erreur (en %)
       */
      errorRate: parseInt(process.env.MONITORING_RECOMMENDATION_THRESHOLD_ERROR_RATE || '5', 10),
      
      /**
       * Seuil de taux de hit du cache (en %)
       */
      cacheHitRate: parseInt(process.env.MONITORING_RECOMMENDATION_THRESHOLD_CACHE_HIT_RATE || '80', 10),
      
      /**
       * Seuil de qualité des recommandations (entre 0 et 1)
       */
      recommendationQuality: parseFloat(process.env.MONITORING_RECOMMENDATION_THRESHOLD_QUALITY || '0.6'),
    },
  },
}));
