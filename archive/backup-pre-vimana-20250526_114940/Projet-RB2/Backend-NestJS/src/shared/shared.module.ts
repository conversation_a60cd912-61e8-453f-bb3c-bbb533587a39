import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { UtilsService } from './services/utils.service';
import { DateService } from './services/date.service';
// TODO: Implement these services
// import { StringService } from './services/string.service';
// import { FileService } from './services/file.service';
// import { LoggerService } from './services/logger.service';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    UtilsService,
    DateService,
    // TODO: Implement these services
    // StringService,
    // FileService,
    // LoggerService,
  ],
  exports: [
    UtilsService,
    DateService,
    // TODO: Implement these services
    // StringService,
    // FileService,
    // LoggerService,
  ],
})
export class SharedModule {}
