// Constantes pour les rôles utilisateur
export const USER_ROLES = {
  USER: 'USER',
  ADMIN: 'ADMIN',
  MODERATOR: 'MODERATOR',
  PARTNER: 'PARTNER',
  HOST: 'HOST',
  ORGANIZER: 'ORGANIZER',
  PARTICIPANT: 'PARTICIPANT',
};

// Constantes pour les statuts
export const STATUS = {
  DRAFT: 'DRAFT',
  PUBLISHED: 'PUBLISHED',
  ARCHIVED: 'ARCHIVED',
};

// Constantes pour les niveaux
export const LEVELS = {
  BEGINNER: 'BEGINNER',
  INTERMEDIATE: 'INTERMEDIATE',
  ADVANCED: 'ADVANCED',
  EXPERT: 'EXPERT',
};

// Constantes pour les statuts de réservation
export const BOOKING_STATUS = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  CANCELLED: 'CANCELLED',
  COMPLETED: 'COMPLETED',
  REFUNDED: 'REFUNDED',
};

// Constantes pour les statuts d'événement
export const EVENT_STATUS = {
  RECEIVED: 'RECEIVED',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
};

// Constantes pour les types d'événement
export const EVENT_TYPES = {
  USER_CREATED: 'USER_CREATED',
  USER_UPDATED: 'USER_UPDATED',
  USER_DELETED: 'USER_DELETED',
  USER_LOGIN: 'USER_LOGIN',
  USER_LOGOUT: 'USER_LOGOUT',
  COURSE_CREATED: 'COURSE_CREATED',
  COURSE_UPDATED: 'COURSE_UPDATED',
  COURSE_DELETED: 'COURSE_DELETED',
  LESSON_CREATED: 'LESSON_CREATED',
  LESSON_UPDATED: 'LESSON_UPDATED',
  LESSON_DELETED: 'LESSON_DELETED',
  ENROLLMENT_CREATED: 'ENROLLMENT_CREATED',
  ENROLLMENT_UPDATED: 'ENROLLMENT_UPDATED',
  ENROLLMENT_DELETED: 'ENROLLMENT_DELETED',
  GAME_SYSTEM_CREATED: 'GAME_SYSTEM_CREATED',
  GAME_SYSTEM_UPDATED: 'GAME_SYSTEM_UPDATED',
  GAME_SYSTEM_DELETED: 'GAME_SYSTEM_DELETED',
  GAME_LEVEL_CREATED: 'GAME_LEVEL_CREATED',
  GAME_LEVEL_UPDATED: 'GAME_LEVEL_UPDATED',
  GAME_LEVEL_DELETED: 'GAME_LEVEL_DELETED',
  QUEST_CREATED: 'QUEST_CREATED',
  QUEST_UPDATED: 'QUEST_UPDATED',
  QUEST_DELETED: 'QUEST_DELETED',
  PLAYER_PROGRESS_UPDATED: 'PLAYER_PROGRESS_UPDATED',
};

// Constantes pour les codes d'erreur
export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  CONFLICT_ERROR: 'CONFLICT_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  BAD_REQUEST_ERROR: 'BAD_REQUEST_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
};

// Constantes pour les en-têtes HTTP
export const HTTP_HEADERS = {
  AUTHORIZATION: 'Authorization',
  CONTENT_TYPE: 'Content-Type',
  ACCEPT: 'Accept',
  ACCEPT_LANGUAGE: 'Accept-Language',
  USER_AGENT: 'User-Agent',
  REFRESH_TOKEN: 'X-Refresh-Token',
};

// Constantes pour les types MIME
export const MIME_TYPES = {
  JSON: 'application/json',
  FORM: 'application/x-www-form-urlencoded',
  MULTIPART: 'multipart/form-data',
  TEXT: 'text/plain',
  HTML: 'text/html',
  XML: 'application/xml',
  PDF: 'application/pdf',
  JPEG: 'image/jpeg',
  PNG: 'image/png',
  GIF: 'image/gif',
};

// Constantes pour les routes API
export const API_ROUTES = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    REFRESH: '/auth/refresh',
    LOGOUT: '/auth/logout',
    TWO_FACTOR_GENERATE: '/auth/2fa/generate',
    TWO_FACTOR_ENABLE: '/auth/2fa/enable',
    TWO_FACTOR_DISABLE: '/auth/2fa/disable',
  },
  USERS: {
    BASE: '/users',
    BY_ID: '/users/:id',
    PROFILE: '/users/profile/me',
  },
  ACTIVITIES: {
    BASE: '/activities',
    BY_ID: '/activities/:id',
  },
  EVENTS: {
    BASE: '/events',
    BY_ID: '/events/:id',
    BY_USER: '/events/user/:userId',
    UPDATE_STATUS: '/events/:id/status',
  },
  COURSES: {
    BASE: '/courses',
    BY_ID: '/courses/:id',
    LESSONS: '/courses/:courseId/lessons',
    LESSON_BY_ID: '/courses/:courseId/lessons/:lessonId',
    ENROLLMENTS: '/courses/:courseId/enrollments',
  },
  GAME_SYSTEMS: {
    BASE: '/game-systems',
    BY_ID: '/game-systems/:id',
    LEVELS: '/game-systems/:systemId/levels',
    LEVEL_BY_ID: '/game-systems/:systemId/levels/:levelId',
    QUESTS: '/game-systems/:systemId/quests',
    QUEST_BY_ID: '/game-systems/:systemId/quests/:questId',
  },
  PLAYER_PROGRESS: {
    BASE: '/player-progress',
    BY_ID: '/player-progress/:id',
    BY_PLAYER: '/player-progress/player/:playerId',
  },
};
