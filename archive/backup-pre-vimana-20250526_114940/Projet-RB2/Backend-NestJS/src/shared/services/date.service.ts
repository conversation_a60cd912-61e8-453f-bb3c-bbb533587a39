import { Injectable } from '@nestjs/common';

@Injectable()
export class DateService {
  /**
   * Formate une date
   * @param date Date à formater
   * @param format Format de date
   * @param locale Locale
   * @returns Date formatée
   */
  formatDate(date: Date, format = 'dd/MM/yyyy', locale = 'fr-FR'): string {
    // Dans une implémentation réelle, on utiliserait une bibliothèque comme date-fns
    return new Intl.DateTimeFormat(locale).format(date);
  }

  /**
   * Formate une date et heure
   * @param date Date à formater
   * @param format Format de date et heure
   * @param locale Locale
   * @returns Date et heure formatées
   */
  formatDateTime(date: Date, format = 'dd/MM/yyyy HH:mm', locale = 'fr-FR'): string {
    // Dans une implémentation réelle, on utiliserait une bibliothèque comme date-fns
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  }

  /**
   * Calcule la différence entre deux dates en jours
   * @param date1 Première date
   * @param date2 Deuxième date
   * @returns Différence en jours
   */
  diffInDays(date1: Date, date2: Date): number {
    const diffTime = Math.abs(date2.getTime() - date1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Calcule la différence entre deux dates en heures
   * @param date1 Première date
   * @param date2 Deuxième date
   * @returns Différence en heures
   */
  diffInHours(date1: Date, date2: Date): number {
    const diffTime = Math.abs(date2.getTime() - date1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60));
  }

  /**
   * Calcule la différence entre deux dates en minutes
   * @param date1 Première date
   * @param date2 Deuxième date
   * @returns Différence en minutes
   */
  diffInMinutes(date1: Date, date2: Date): number {
    const diffTime = Math.abs(date2.getTime() - date1.getTime());
    return Math.ceil(diffTime / (1000 * 60));
  }

  /**
   * Calcule la différence entre deux dates en secondes
   * @param date1 Première date
   * @param date2 Deuxième date
   * @returns Différence en secondes
   */
  diffInSeconds(date1: Date, date2: Date): number {
    const diffTime = Math.abs(date2.getTime() - date1.getTime());
    return Math.ceil(diffTime / 1000);
  }

  /**
   * Ajoute des jours à une date
   * @param date Date de base
   * @param days Nombre de jours à ajouter
   * @returns Nouvelle date
   */
  addDays(date: Date, days: number): Date {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }

  /**
   * Ajoute des heures à une date
   * @param date Date de base
   * @param hours Nombre d'heures à ajouter
   * @returns Nouvelle date
   */
  addHours(date: Date, hours: number): Date {
    const result = new Date(date);
    result.setHours(result.getHours() + hours);
    return result;
  }

  /**
   * Ajoute des minutes à une date
   * @param date Date de base
   * @param minutes Nombre de minutes à ajouter
   * @returns Nouvelle date
   */
  addMinutes(date: Date, minutes: number): Date {
    const result = new Date(date);
    result.setMinutes(result.getMinutes() + minutes);
    return result;
  }

  /**
   * Ajoute des secondes à une date
   * @param date Date de base
   * @param seconds Nombre de secondes à ajouter
   * @returns Nouvelle date
   */
  addSeconds(date: Date, seconds: number): Date {
    const result = new Date(date);
    result.setSeconds(result.getSeconds() + seconds);
    return result;
  }

  /**
   * Vérifie si une date est aujourd'hui
   * @param date Date à vérifier
   * @returns true si la date est aujourd'hui
   */
  isToday(date: Date): boolean {
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  }

  /**
   * Vérifie si une date est dans le passé
   * @param date Date à vérifier
   * @returns true si la date est dans le passé
   */
  isPast(date: Date): boolean {
    return date.getTime() < new Date().getTime();
  }

  /**
   * Vérifie si une date est dans le futur
   * @param date Date à vérifier
   * @returns true si la date est dans le futur
   */
  isFuture(date: Date): boolean {
    return date.getTime() > new Date().getTime();
  }

  /**
   * Vérifie si une date est entre deux dates
   * @param date Date à vérifier
   * @param startDate Date de début
   * @param endDate Date de fin
   * @returns true si la date est entre les deux dates
   */
  isBetween(date: Date, startDate: Date, endDate: Date): boolean {
    return date.getTime() >= startDate.getTime() && date.getTime() <= endDate.getTime();
  }

  /**
   * Récupère le début du jour
   * @param date Date
   * @returns Date au début du jour
   */
  startOfDay(date: Date): Date {
    const result = new Date(date);
    result.setHours(0, 0, 0, 0);
    return result;
  }

  /**
   * Récupère la fin du jour
   * @param date Date
   * @returns Date à la fin du jour
   */
  endOfDay(date: Date): Date {
    const result = new Date(date);
    result.setHours(23, 59, 59, 999);
    return result;
  }

  /**
   * Récupère le début du mois
   * @param date Date
   * @returns Date au début du mois
   */
  startOfMonth(date: Date): Date {
    const result = new Date(date);
    result.setDate(1);
    result.setHours(0, 0, 0, 0);
    return result;
  }

  /**
   * Récupère la fin du mois
   * @param date Date
   * @returns Date à la fin du mois
   */
  endOfMonth(date: Date): Date {
    const result = new Date(date);
    result.setMonth(result.getMonth() + 1);
    result.setDate(0);
    result.setHours(23, 59, 59, 999);
    return result;
  }

  /**
   * Récupère le début de l'année
   * @param date Date
   * @returns Date au début de l'année
   */
  startOfYear(date: Date): Date {
    const result = new Date(date);
    result.setMonth(0, 1);
    result.setHours(0, 0, 0, 0);
    return result;
  }

  /**
   * Récupère la fin de l'année
   * @param date Date
   * @returns Date à la fin de l'année
   */
  endOfYear(date: Date): Date {
    const result = new Date(date);
    result.setMonth(11, 31);
    result.setHours(23, 59, 59, 999);
    return result;
  }

  /**
   * Formate une durée en texte
   * @param seconds Durée en secondes
   * @returns Durée formatée
   */
  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    const parts = [];
    if (hours > 0) parts.push(`${hours}h`);
    if (minutes > 0) parts.push(`${minutes}m`);
    if (remainingSeconds > 0 || parts.length === 0) parts.push(`${remainingSeconds}s`);
    
    return parts.join(' ');
  }

  /**
   * Formate une date relative
   * @param date Date
   * @returns Date relative
   */
  formatRelative(date: Date): string {
    const now = new Date();
    const diffInSeconds = this.diffInSeconds(date, now);
    
    if (diffInSeconds < 60) return 'à l\'instant';
    if (diffInSeconds < 3600) return `il y a ${Math.floor(diffInSeconds / 60)} minutes`;
    if (diffInSeconds < 86400) return `il y a ${Math.floor(diffInSeconds / 3600)} heures`;
    if (diffInSeconds < 2592000) return `il y a ${Math.floor(diffInSeconds / 86400)} jours`;
    if (diffInSeconds < 31536000) return `il y a ${Math.floor(diffInSeconds / 2592000)} mois`;
    return `il y a ${Math.floor(diffInSeconds / 31536000)} ans`;
  }
}
