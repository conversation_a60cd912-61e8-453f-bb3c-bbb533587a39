import { Injectable } from '@nestjs/common';

@Injectable()
export class UtilsService {
  /**
   * Génère un identifiant unique
   * @returns Identifiant unique
   */
  generateUniqueId(): string {
    return require('crypto').randomBytes(16).toString('hex');
  }

  /**
   * Génère un slug à partir d'une chaîne de caractères
   * @param text Texte à transformer en slug
   * @returns Slug
   */
  generateSlug(text: string): string {
    return text
      .toString()
      .toLowerCase()
      .trim()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]+/g, '')
      .replace(/\-\-+/g, '-')
      .replace(/^-+/, '')
      .replace(/-+$/, '');
  }

  /**
   * Génère un mot de passe aléatoire
   * @param length Longueur du mot de passe
   * @returns Mot de passe aléatoire
   */
  generateRandomPassword(length = 12): string {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+~`|}{[]:;?><,./-=';
    let password = '';
    for (let i = 0; i < length; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }

  /**
   * Mélange un tableau
   * @param array Tableau à mélanger
   * @returns Tableau mélangé
   */
  shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  /**
   * Pagine un tableau
   * @param array Tableau à paginer
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @returns Tableau paginé
   */
  paginateArray<T>(array: T[], page = 1, limit = 10): { data: T[]; total: number; page: number; limit: number; totalPages: number } {
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const data = array.slice(startIndex, endIndex);
    
    return {
      data,
      total: array.length,
      page,
      limit,
      totalPages: Math.ceil(array.length / limit),
    };
  }

  /**
   * Trie un tableau d'objets par une propriété
   * @param array Tableau à trier
   * @param key Propriété de tri
   * @param order Ordre de tri
   * @returns Tableau trié
   */
  sortArrayByKey<T>(array: T[], key: keyof T, order: 'asc' | 'desc' = 'asc'): T[] {
    return [...array].sort((a, b) => {
      if (a[key] < b[key]) return order === 'asc' ? -1 : 1;
      if (a[key] > b[key]) return order === 'asc' ? 1 : -1;
      return 0;
    });
  }

  /**
   * Filtre un tableau d'objets par une propriété
   * @param array Tableau à filtrer
   * @param key Propriété de filtre
   * @param value Valeur de filtre
   * @returns Tableau filtré
   */
  filterArrayByKey<T>(array: T[], key: keyof T, value: any): T[] {
    return array.filter(item => item[key] === value);
  }

  /**
   * Groupe un tableau d'objets par une propriété
   * @param array Tableau à grouper
   * @param key Propriété de groupement
   * @returns Tableau groupé
   */
  groupArrayByKey<T>(array: T[], key: keyof T): Record<string, T[]> {
    return array.reduce((groups, item) => {
      const groupKey = String(item[key]);
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(item);
      return groups;
    }, {} as Record<string, T[]>);
  }

  /**
   * Supprime les doublons d'un tableau
   * @param array Tableau avec doublons
   * @returns Tableau sans doublons
   */
  removeDuplicates<T>(array: T[]): T[] {
    return [...new Set(array)];
  }

  /**
   * Supprime les doublons d'un tableau d'objets par une propriété
   * @param array Tableau avec doublons
   * @param key Propriété de dédoublonnage
   * @returns Tableau sans doublons
   */
  removeDuplicatesByKey<T>(array: T[], key: keyof T): T[] {
    const seen = new Set();
    return array.filter(item => {
      const value = item[key];
      if (seen.has(value)) {
        return false;
      }
      seen.add(value);
      return true;
    });
  }

  /**
   * Vérifie si un objet est vide
   * @param obj Objet à vérifier
   * @returns true si l'objet est vide
   */
  isEmptyObject(obj: Record<string, any>): boolean {
    return Object.keys(obj).length === 0;
  }

  /**
   * Supprime les propriétés nulles ou undefined d'un objet
   * @param obj Objet à nettoyer
   * @returns Objet nettoyé
   */
  removeNullProperties<T extends Record<string, any>>(obj: T): Partial<T> {
    const result = { ...obj };
    Object.keys(result).forEach(key => {
      if (result[key] === null || result[key] === undefined) {
        delete result[key];
      }
    });
    return result;
  }

  /**
   * Convertit un objet en chaîne de requête
   * @param obj Objet à convertir
   * @returns Chaîne de requête
   */
  objectToQueryString(obj: Record<string, any>): string {
    return Object.keys(obj)
      .filter(key => obj[key] !== undefined && obj[key] !== null)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`)
      .join('&');
  }

  /**
   * Convertit une chaîne de requête en objet
   * @param queryString Chaîne de requête
   * @returns Objet
   */
  queryStringToObject(queryString: string): Record<string, string> {
    if (!queryString) return {};
    
    const params = new URLSearchParams(queryString);
    const result: Record<string, string> = {};
    
    params.forEach((value, key) => {
      result[key] = value;
    });
    
    return result;
  }

  /**
   * Formate un nombre avec séparateur de milliers
   * @param number Nombre à formater
   * @param separator Séparateur de milliers
   * @returns Nombre formaté
   */
  formatNumber(number: number, separator = ' '): string {
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, separator);
  }

  /**
   * Formate un prix
   * @param price Prix à formater
   * @param currency Devise
   * @param locale Locale
   * @returns Prix formaté
   */
  formatPrice(price: number, currency = 'EUR', locale = 'fr-FR'): string {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
    }).format(price);
  }

  /**
   * Tronque un texte
   * @param text Texte à tronquer
   * @param length Longueur maximale
   * @param suffix Suffixe
   * @returns Texte tronqué
   */
  truncateText(text: string, length = 100, suffix = '...'): string {
    if (text.length <= length) return text;
    return text.substring(0, length).trim() + suffix;
  }

  /**
   * Génère un code aléatoire
   * @param length Longueur du code
   * @param chars Caractères autorisés
   * @returns Code aléatoire
   */
  generateRandomCode(length = 6, chars = '0123456789'): string {
    let code = '';
    for (let i = 0; i < length; i++) {
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return code;
  }
}
