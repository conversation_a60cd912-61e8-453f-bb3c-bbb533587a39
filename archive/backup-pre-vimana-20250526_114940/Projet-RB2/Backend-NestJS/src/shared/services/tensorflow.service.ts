import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import * as tf from '@tensorflow/tfjs-node';

@Injectable()
export class TensorflowService implements OnModuleInit {
  private readonly logger = new Logger(TensorflowService.name);
  private model: tf.LayersModel;
  private isModelLoaded: boolean = false;
  private readonly gpuEnabled: boolean;

  constructor(private readonly configService: ConfigService) {
    this.gpuEnabled = this.configService.get<boolean>('TENSORFLOW_GPU_ENABLED', false);
  }

  async onModuleInit() {
    try {
      // Initialiser TensorFlow
      if (this.gpuEnabled) {
        this.logger.log('Initialisation de TensorFlow avec support GPU...');
        // Note: Le support GPU nécessite des configurations supplémentaires
      } else {
        this.logger.log('Initialisation de TensorFlow en mode CPU...');
      }
    } catch (error) {
      this.logger.error(`Erreur lors de l'initialisation de TensorFlow: ${error.message}`);
    }
  }

  /**
   * Vérifie si un modèle existe à l'emplacement spécifié
   */
  async modelExists(modelPath: string): Promise<boolean> {
    try {
      const modelJsonPath = path.join(modelPath, 'model.json');
      return fs.existsSync(modelJsonPath);
    } catch (error) {
      this.logger.error(`Erreur lors de la vérification de l'existence du modèle: ${error.message}`);
      return false;
    }
  }

  /**
   * Charge un modèle TensorFlow depuis le disque
   */
  async loadModel(modelPath: string): Promise<void> {
    try {
      // Créer le répertoire s'il n'existe pas
      if (!fs.existsSync(modelPath)) {
        fs.mkdirSync(modelPath, { recursive: true });
      }

      const modelJsonPath = `file://${path.join(modelPath, 'model.json')}`;
      this.model = await tf.loadLayersModel(modelJsonPath);
      this.isModelLoaded = true;
      this.logger.log(`Modèle chargé depuis ${modelJsonPath}`);
    } catch (error) {
      this.logger.error(`Erreur lors du chargement du modèle: ${error.message}`);
      throw error;
    }
  }

  /**
   * Sauvegarde le modèle TensorFlow sur le disque
   */
  async saveModel(modelPath: string): Promise<void> {
    try {
      if (!this.model) {
        throw new Error('Aucun modèle à sauvegarder');
      }

      // Créer le répertoire s'il n'existe pas
      if (!fs.existsSync(modelPath)) {
        fs.mkdirSync(modelPath, { recursive: true });
      }

      const modelSavePath = `file://${modelPath}`;
      await this.model.save(modelSavePath);
      this.logger.log(`Modèle sauvegardé à ${modelSavePath}`);
    } catch (error) {
      this.logger.error(`Erreur lors de la sauvegarde du modèle: ${error.message}`);
      throw error;
    }
  }

  /**
   * Crée un modèle de factorisation matricielle pour les recommandations
   */
  async createMatrixFactorizationModel(
    embeddingDimension: number,
    numUsers: number,
    numItems: number,
    learningRate: number,
  ): Promise<void> {
    try {
      // Définir les entrées du modèle
      const userInput = tf.input({ shape: [1], name: 'user_input', dtype: 'int32' });
      const itemInput = tf.input({ shape: [1], name: 'item_input', dtype: 'int32' });

      // Créer les couches d'embedding
      const userEmbedding = tf.layers.embedding({
        inputDim: numUsers + 1, // +1 pour l'index 0 (padding)
        outputDim: embeddingDimension,
        name: 'user_embedding',
      }).apply(userInput);

      const itemEmbedding = tf.layers.embedding({
        inputDim: numItems + 1, // +1 pour l'index 0 (padding)
        outputDim: embeddingDimension,
        name: 'item_embedding',
      }).apply(itemInput);

      // Aplatir les embeddings
      const userVector = tf.layers.flatten().apply(userEmbedding);
      const itemVector = tf.layers.flatten().apply(itemEmbedding);

      // Calculer le produit scalaire
      const dot = tf.layers.dot({ axes: 1 }).apply([userVector, itemVector]);

      // Ajouter une couche dense pour la sortie
      const output = tf.layers.dense({
        units: 1,
        activation: 'sigmoid',
        name: 'output',
      }).apply(dot);

      // Créer le modèle
      this.model = tf.model({
        inputs: [userInput, itemInput],
        outputs: output,
        name: 'recommendation_model',
      });

      // Compiler le modèle
      this.model.compile({
        optimizer: tf.train.adam(learningRate),
        loss: 'meanSquaredError',
        metrics: ['accuracy'],
      });

      // Résumé du modèle
      this.model.summary();
      this.isModelLoaded = true;
      this.logger.log('Modèle de factorisation matricielle créé avec succès');
    } catch (error) {
      this.logger.error(`Erreur lors de la création du modèle: ${error.message}`);
      throw error;
    }
  }

  /**
   * Entraîne le modèle avec les données fournies
   */
  async trainModel(
    userIndices: number[],
    itemIndices: number[],
    ratings: number[],
    batchSize: number,
    epochs: number,
  ): Promise<{ finalLoss: number; accuracy: number; duration: number }> {
    try {
      if (!this.model) {
        throw new Error('Aucun modèle à entraîner');
      }

      // Convertir les données en tenseurs
      const userTensor = tf.tensor2d(userIndices, [userIndices.length, 1], 'int32');
      const itemTensor = tf.tensor2d(itemIndices, [itemIndices.length, 1], 'int32');
      const ratingTensor = tf.tensor2d(ratings, [ratings.length, 1], 'float32');

      // Mesurer le temps d'entraînement
      const startTime = Date.now();

      // Entraîner le modèle
      const history = await this.model.fit(
        [userTensor, itemTensor],
        ratingTensor,
        {
          batchSize,
          epochs,
          validationSplit: 0.2,
          callbacks: {
            onEpochEnd: (epoch, logs) => {
              this.logger.log(`Epoch ${epoch + 1}/${epochs} - loss: ${logs.loss.toFixed(4)} - accuracy: ${logs.accuracy.toFixed(4)}`);
            },
          },
        },
      );

      // Calculer la durée
      const duration = Date.now() - startTime;

      // Libérer les tenseurs
      userTensor.dispose();
      itemTensor.dispose();
      ratingTensor.dispose();

      // Retourner les métriques finales
      return {
        finalLoss: history.history.loss[history.history.loss.length - 1],
        accuracy: history.history.accuracy[history.history.accuracy.length - 1],
        duration,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de l'entraînement du modèle: ${error.message}`);
      throw error;
    }
  }

  /**
   * Prédit les scores pour un utilisateur donné
   */
  async predictForUser(userIndex: number, numItems: number): Promise<number[]> {
    try {
      if (!this.model) {
        throw new Error('Aucun modèle pour la prédiction');
      }

      // Créer les tenseurs d'entrée
      const userTensor = tf.tensor2d(Array(numItems).fill(userIndex), [numItems, 1], 'int32');
      const itemTensor = tf.tensor2d(Array.from({ length: numItems }, (_, i) => i), [numItems, 1], 'int32');

      // Faire la prédiction
      const predictions = this.model.predict([userTensor, itemTensor]) as tf.Tensor;
      
      // Convertir en tableau JavaScript
      const result = await predictions.data();
      
      // Libérer les tenseurs
      userTensor.dispose();
      itemTensor.dispose();
      predictions.dispose();

      // Convertir en tableau de nombres
      return Array.from(result);
    } catch (error) {
      this.logger.error(`Erreur lors de la prédiction: ${error.message}`);
      throw error;
    }
  }

  /**
   * Prédit le score pour une paire utilisateur-élément spécifique
   */
  async predictScore(userIndex: number, itemIndex: number): Promise<number> {
    try {
      if (!this.model) {
        throw new Error('Aucun modèle pour la prédiction');
      }

      // Créer les tenseurs d'entrée
      const userTensor = tf.tensor2d([userIndex], [1, 1], 'int32');
      const itemTensor = tf.tensor2d([itemIndex], [1, 1], 'int32');

      // Faire la prédiction
      const prediction = this.model.predict([userTensor, itemTensor]) as tf.Tensor;
      
      // Convertir en nombre JavaScript
      const result = await prediction.data();
      
      // Libérer les tenseurs
      userTensor.dispose();
      itemTensor.dispose();
      prediction.dispose();

      return result[0];
    } catch (error) {
      this.logger.error(`Erreur lors de la prédiction de score: ${error.message}`);
      throw error;
    }
  }

  /**
   * Extrait les embeddings des utilisateurs du modèle
   */
  async getUserEmbeddings(): Promise<number[][]> {
    try {
      if (!this.model) {
        throw new Error('Aucun modèle pour extraire les embeddings');
      }

      // Récupérer la couche d'embedding des utilisateurs
      const userEmbeddingLayer = this.model.getLayer('user_embedding');
      const weights = userEmbeddingLayer.getWeights()[0];
      
      // Convertir en tableau JavaScript
      return await weights.array();
    } catch (error) {
      this.logger.error(`Erreur lors de l'extraction des embeddings utilisateurs: ${error.message}`);
      throw error;
    }
  }

  /**
   * Extrait les embeddings des éléments du modèle
   */
  async getItemEmbeddings(): Promise<number[][]> {
    try {
      if (!this.model) {
        throw new Error('Aucun modèle pour extraire les embeddings');
      }

      // Récupérer la couche d'embedding des éléments
      const itemEmbeddingLayer = this.model.getLayer('item_embedding');
      const weights = itemEmbeddingLayer.getWeights()[0];
      
      // Convertir en tableau JavaScript
      return await weights.array();
    } catch (error) {
      this.logger.error(`Erreur lors de l'extraction des embeddings d'éléments: ${error.message}`);
      throw error;
    }
  }
}
