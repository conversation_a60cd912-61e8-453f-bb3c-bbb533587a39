import { PaginationOptions, PaginatedResult } from '../interfaces';

/**
 * Crée un objet de pagination à partir des options fournies
 * @param options Options de pagination
 * @param defaultLimit Limite par défaut
 * @returns Options de pagination normalisées
 */
export function createPaginationOptions(
  options?: Partial<PaginationOptions>,
  defaultLimit = 10,
): PaginationOptions {
  const page = options?.page && options.page > 0 ? options.page : 1;
  const limit = options?.limit && options.limit > 0 ? options.limit : defaultLimit;
  const sortBy = options?.sortBy || 'createdAt';
  const sortOrder = options?.sortOrder || 'desc';

  return {
    page,
    limit,
    sortBy,
    sortOrder,
  };
}

/**
 * Crée un résultat paginé
 * @param items Éléments à paginer
 * @param total Nombre total d'éléments
 * @param options Options de pagination
 * @returns Résultat paginé
 */
export function createPaginatedResult<T>(
  items: T[],
  total: number,
  options: PaginationOptions,
): PaginatedResult<T> {
  const { page, limit } = options;
  const totalPages = Math.ceil(total / (limit ?? 1));

  return {
    items,
    total,
    page: page ?? 1,
    limit: limit ?? 10,
    totalPages,
  };

}

/**
 * Génère un slug à partir d'une chaîne de caractères
 * @param str Chaîne de caractères
 * @returns Slug
 */
export function slugify(str: string): string {
  return str
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

/**
 * Génère un identifiant unique
 * @returns Identifiant unique
 */
export function generateUniqueId(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

/**
 * Formate une date
 * @param date Date à formater
 * @param locale Locale à utiliser
 * @param options Options de formatage
 * @returns Date formatée
 */
export function formatDate(
  date: Date,
  locale = 'fr-FR',
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  },
): string {
  return new Intl.DateTimeFormat(locale, options).format(date);
}

/**
 * Tronque une chaîne de caractères
 * @param str Chaîne de caractères
 * @param length Longueur maximale
 * @param suffix Suffixe à ajouter si la chaîne est tronquée
 * @returns Chaîne tronquée
 */
export function truncate(str: string, length: number, suffix = '...'): string {
  if (str.length <= length) {
    return str;
  }
  return str.substring(0, length) + suffix;
}

/**
 * Sanitize une chaîne de caractères pour éviter les injections HTML
 * @param str Chaîne de caractères
 * @returns Chaîne sanitizée
 */
export function sanitizeHtml(str: string): string {
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

/**
 * Masque une partie d'une chaîne de caractères
 * @param str Chaîne de caractères
 * @param start Début de la partie visible
 * @param end Fin de la partie visible
 * @param mask Caractère de masquage
 * @returns Chaîne masquée
 */
export function maskString(str: string, start = 0, end = 0, mask = '*'): string {
  if (!str) return '';
  
  const firstPart = str.slice(0, start);
  const lastPart = str.slice(str.length - end);
  const maskedPart = mask.repeat(str.length - start - end);
  
  return firstPart + maskedPart + lastPart;
}

/**
 * Formate un nombre
 * @param num Nombre à formater
 * @param locale Locale à utiliser
 * @param options Options de formatage
 * @returns Nombre formaté
 */
export function formatNumber(
  num: number,
  locale = 'fr-FR',
  options: Intl.NumberFormatOptions = {
    style: 'decimal',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  },
): string {
  return new Intl.NumberFormat(locale, options).format(num);
}

/**
 * Formate un prix
 * @param price Prix à formater
 * @param currency Devise
 * @param locale Locale à utiliser
 * @returns Prix formaté
 */
export function formatPrice(
  price: number,
  currency = 'EUR',
  locale = 'fr-FR',
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(price);
}

/**
 * Vérifie si une chaîne de caractères est un email valide
 * @param email Email à vérifier
 * @returns true si l'email est valide, false sinon
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Vérifie si une chaîne de caractères est un numéro de téléphone valide
 * @param phone Numéro de téléphone à vérifier
 * @returns true si le numéro de téléphone est valide, false sinon
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^\+?[0-9]{10,15}$/;
  return phoneRegex.test(phone);
}

/**
 * Vérifie si une chaîne de caractères est une URL valide
 * @param url URL à vérifier
 * @returns true si l'URL est valide, false sinon
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * Génère un mot de passe aléatoire
 * @param length Longueur du mot de passe
 * @param includeUppercase Inclure des majuscules
 * @param includeNumbers Inclure des chiffres
 * @param includeSymbols Inclure des symboles
 * @returns Mot de passe aléatoire
 */
export function generateRandomPassword(
  length = 12,
  includeUppercase = true,
  includeNumbers = true,
  includeSymbols = true,
): string {
  let chars = 'abcdefghijklmnopqrstuvwxyz';
  
  if (includeUppercase) chars += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  if (includeNumbers) chars += '0123456789';
  if (includeSymbols) chars += '!@#$%^&*()_+~`|}{[]:;?><,./-=';
  
  let password = '';
  for (let i = 0; i < length; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return password;
}

/**
 * Convertit un objet en paramètres de requête
 * @param params Paramètres
 * @returns Chaîne de paramètres de requête
 */
export function objectToQueryString(params: Record<string, any>): string {
  return Object.keys(params)
    .filter(key => params[key] !== undefined && params[key] !== null)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');
}

/**
 * Convertit des paramètres de requête en objet
 * @param queryString Chaîne de paramètres de requête
 * @returns Objet de paramètres
 */
export function queryStringToObject(queryString: string): Record<string, string> {
  if (!queryString || queryString === '') return {};
  
  const params = new URLSearchParams(queryString.startsWith('?') ? queryString.substring(1) : queryString);
  const result: Record<string, string> = {};
  
  params.forEach((value, key) => {
    result[key] = value;
  });
  
  return result;
}

/**
 * Retarde l'exécution d'une fonction
 * @param ms Délai en millisecondes
 * @returns Promise
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Convertit un objet en chaîne CSV
 * @param data Données
 * @param headers En-têtes
 * @param delimiter Délimiteur
 * @returns Chaîne CSV
 */
export function objectToCsv(
  data: Record<string, any>[],
  headers?: string[],
  delimiter = ',',
): string {
  if (!data || data.length === 0) return '';
  
  const actualHeaders = headers || Object.keys(data[0]);
  const headerRow = actualHeaders.join(delimiter);
  
  const rows = data.map(obj => {
    return actualHeaders.map(header => {
      const cell = obj[header] === null || obj[header] === undefined ? '' : obj[header];
      return typeof cell === 'string' ? `"${cell.replace(/"/g, '""')}"` : cell;
    }).join(delimiter);
  });
  
  return [headerRow, ...rows].join('\n');
}

/**
 * Groupe un tableau d'objets par une propriété
 * @param array Tableau d'objets
 * @param key Propriété de regroupement
 * @returns Objets groupés
 */
export function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
  return array.reduce((result, item) => {
    const groupKey = String(item[key]);
    result[groupKey] = result[groupKey] || [];
    result[groupKey].push(item);
    return result;
  }, {} as Record<string, T[]>);
}

/**
 * Supprime les doublons d'un tableau
 * @param array Tableau
 * @param key Propriété de comparaison (pour les objets)
 * @returns Tableau sans doublons
 */
export function removeDuplicates<T>(array: T[], key?: keyof T): T[] {
  if (!key) {
    return [...new Set(array)];
  }
  
  const seen = new Set();
  return array.filter(item => {
    const value = item[key];
    if (seen.has(value)) {
      return false;
    }
    seen.add(value);
    return true;
  });
}

/**
 * Trie un tableau d'objets par une propriété
 * @param array Tableau d'objets
 * @param key Propriété de tri
 * @param order Ordre de tri
 * @returns Tableau trié
 */
export function sortBy<T>(array: T[], key: keyof T, order: 'asc' | 'desc' = 'asc'): T[] {
  return [...array].sort((a, b) => {
    const valueA = a[key];
    const valueB = b[key];
    
    if (valueA < valueB) return order === 'asc' ? -1 : 1;
    if (valueA > valueB) return order === 'asc' ? 1 : -1;
    return 0;
  });
}
