// Interface pour la pagination
export interface PaginatedResult<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Interface pour les options de pagination
export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Interface pour les réponses d'API
export interface ApiResponse<T> {
  statusCode: number;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
}

// Interface pour les erreurs d'API
export interface ApiError {
  statusCode: number;
  message: string;
  error: string;
  details?: any;
  timestamp: string;
  path: string;
}

// Interface pour les tokens JWT
export interface JwtPayload {
  sub: string;
  email: string;
  role: string;
  iat?: number;
  exp?: number;
}

// Interface pour les tokens d'authentification
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

// Interface pour les utilisateurs authentifiés
export interface AuthUser {
  id: string;
  email: string;
  role: string;
}

// Interface pour les résultats de recherche
export interface SearchResult<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  query: string;
}

// Interface pour les options de recherche
export interface SearchOptions extends PaginationOptions {
  query: string;
  fields?: string[];
}

// Interface pour les filtres
export interface FilterOptions {
  [key: string]: any;
}

// Interface pour les événements
export interface EventPayload {
  [key: string]: any;
}

// Interface pour les résultats de validation
export interface ValidationResult {
  isValid: boolean;
  errors?: ValidationError[];
}

// Interface pour les erreurs de validation
export interface ValidationError {
  field: string;
  message: string;
}

// Interface pour les résultats d'opération
export interface OperationResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

// Interface pour les options de cache
export interface CacheOptions {
  ttl?: number;
  key?: string;
}

// Interface pour les options de téléchargement de fichier
export interface FileUploadOptions {
  maxSize?: number;
  allowedMimeTypes?: string[];
  destination?: string;
}

// Interface pour les résultats de téléchargement de fichier
export interface FileUploadResult {
  filename: string;
  originalname: string;
  mimetype: string;
  size: number;
  path: string;
  url: string;
}

// Interface pour les options d'exportation
export interface ExportOptions {
  format: 'csv' | 'xlsx' | 'pdf';
  filename?: string;
  fields?: string[];
}

// Interface pour les options d'importation
export interface ImportOptions {
  format: 'csv' | 'xlsx';
  skipHeader?: boolean;
  mapping?: { [key: string]: string };
}

// Interface pour les résultats d'importation
export interface ImportResult {
  total: number;
  success: number;
  failed: number;
  errors?: { row: number; message: string }[];
}

// Interface pour les options de notification
export interface NotificationOptions {
  priority?: 'low' | 'normal' | 'high';
  expiration?: number;
  data?: any;
}

// Interface pour les notifications
export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: string;
  read: boolean;
  data?: any;
  createdAt: Date;
}

// Interface pour les options de rapport
export interface ReportOptions {
  startDate?: Date;
  endDate?: Date;
  groupBy?: string;
  filters?: FilterOptions;
}

// Interface pour les résultats de rapport
export interface ReportResult {
  data: any[];
  summary?: any;
  startDate?: Date;
  endDate?: Date;
  generatedAt: Date;
}

// Interface pour les options d'audit
export interface AuditOptions {
  userId?: string;
  action: string;
  resource: string;
  details?: any;
  // Pagination et filtres optionnels
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  startDate?: Date;
  endDate?: Date;
}

// Interface pour les entrées d'audit
export interface AuditEntry {
  id: string;
  userId?: string;
  action: string;
  resource: string;
  details?: any;
  ip?: string;
  userAgent?: string;
  createdAt: Date;
}
