import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import helmet from 'helmet';
// helmet v7+ utilise une exportation par défaut, donc l'import doit être import helmet from 'helmet';
import * as compression from 'compression';
import * as fs from 'fs';
import * as path from 'path';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from './prisma/prisma.service';
import { writeFileSync } from 'fs';
import { join } from 'path';

import { validateEnv } from './config/validate-env';

// Validation stricte de la configuration .env avec Zod
validateEnv(process.env);

async function bootstrap() {
  // Configuration HTTPS avec mTLS
  const httpsOptions = {
    key: fs.readFileSync(path.join(process.cwd(), process.env.MTLS_KEY_PATH || './certs/server.key')),
    cert: fs.readFileSync(path.join(process.cwd(), process.env.MTLS_CERT_PATH || './certs/server.crt')),
    ca: fs.readFileSync(path.join(process.cwd(), process.env.MTLS_CA_PATH || './certs/ca.crt')),
    requestCert: true,
    rejectUnauthorized: process.env.MTLS_REJECT_UNAUTHORIZED === 'true',
    minVersion: 'TLSv1.3',
  };

  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    httpsOptions,
  });

  const configService = app.get(ConfigService);
  const logger = new Logger('Bootstrap');

  // Get environment variables
  const port = configService.get<number>('app.port', 3443); // Port HTTPS par défaut
  const apiPrefix = configService.get<string>('app.apiPrefix', '/api/v1');
  const nodeEnv = configService.get<string>('app.nodeEnv', 'development');

  // Set global prefix
  app.setGlobalPrefix(apiPrefix);

  // Enable CORS
  app.enableCors(configService.get('security.cors'));

  // Use Helmet for security headers
  app.use(helmet(configService.get('security.helmet')));

  // Use compression
  app.use(compression());

  // Enable validation
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Enable Prisma shutdown hook
  const prismaService = app.get(PrismaService);
  await prismaService.enableShutdownHooks(app);

  // Setup Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Retreat And Be API (mTLS)')
    .setDescription('The Retreat And Be API documentation with mTLS')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('auth', 'Authentication endpoints')
    .addTag('users', 'User management endpoints')
    .addTag('encryption', 'Encryption management endpoints')
    .addTag('security', 'Security management endpoints')
    .build();

  const document = SwaggerModule.createDocument(app, config);

  // Save Swagger JSON file
  if (nodeEnv !== 'production') {
    writeFileSync(
      join(process.cwd(), 'swagger-mtls-spec.json'),
      JSON.stringify(document, null, 2),
    );
    logger.log('Swagger JSON file generated');
  }

  SwaggerModule.setup('api/docs', app, document);
  logger.log('Swagger documentation is available at: /api/docs');

  // Start the server
  await app.listen(port);
  logger.log(`Application is running in ${nodeEnv} mode with mTLS`);
  logger.log(`Server is listening on port ${port} (HTTPS)`);
  logger.log(`API is available at: ${apiPrefix}`);
}

bootstrap().catch((error) => {
  console.error('Application failed to start:', error);
  process.exit(1);
});
