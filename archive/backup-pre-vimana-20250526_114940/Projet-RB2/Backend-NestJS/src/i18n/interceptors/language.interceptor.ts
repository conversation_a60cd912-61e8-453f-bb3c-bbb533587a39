import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Inject,
  Optional,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { I18nService } from '../i18n.service';
import { Request } from 'express';

@Injectable()
export class LanguageInterceptor implements NestInterceptor {
  constructor(@Optional() private readonly i18nService?: I18nService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();

    if (this.i18nService) {
      const language = this.i18nService.getLanguage(request);

      // Set language in request object for later use
      request['language'] = language;
    } else {
      // Fallback to default language
      request['language'] = 'fr';
    }

    return next.handle();
  }
}
