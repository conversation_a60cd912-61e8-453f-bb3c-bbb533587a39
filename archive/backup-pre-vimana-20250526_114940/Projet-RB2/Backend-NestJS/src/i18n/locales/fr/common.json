{"welcome": "Bienvenue sur l'API Retreat And Be", "errors": {"not_found": "Ressource non trouvée", "unauthorized": "Non autorisé", "forbidden": "Accès interdit", "bad_request": "<PERSON><PERSON><PERSON><PERSON><PERSON> invalide", "internal_server": "<PERSON>rreur interne du serveur", "validation": "Erreur de validation", "conflict": "Conflit de ressources", "too_many_requests": "Trop de requêtes", "service_unavailable": "Service indisponible"}, "auth": {"login_success": "Connexion réussie", "login_failed": "Échec de la connexion", "logout_success": "Déconnexion réussie", "register_success": "Inscription réussie", "register_failed": "Échec de l'inscription", "email_already_exists": "Cet email est déjà utilisé", "invalid_credentials": "Identifiants invalides", "account_locked": "<PERSON><PERSON><PERSON>", "account_not_verified": "Compte non vérifié", "verification_email_sent": "Email de vérification envoyé", "password_reset_email_sent": "Email de réinitialisation de mot de passe envoyé", "password_reset_success": "Mot de passe réinitialisé avec succès", "password_reset_failed": "Échec de la réinitialisation du mot de passe", "token_expired": "Jeton expiré", "token_invalid": "<PERSON><PERSON> invalide", "mfa_required": "Authentification à deux facteurs requise", "mfa_setup_success": "Configuration de l'authentification à deux facteurs réussie", "mfa_verification_success": "Vérification de l'authentification à deux facteurs réussie", "mfa_verification_failed": "Échec de la vérification de l'authentification à deux facteurs"}, "users": {"created": "Utilisateur c<PERSON>é avec succès", "updated": "Utilisateur mis à jour avec succès", "deleted": "Utilisateur supprimé avec succès", "not_found": "Utilisateur non trouvé", "email_already_exists": "Cet email est déjà utilisé", "invalid_role": "<PERSON><PERSON><PERSON> invalide", "profile_updated": "Profil mis à jour avec succès", "password_changed": "Mot de passe changé avec succès", "password_invalid": "Mot de passe invalide", "account_deactivated": "Compte d<PERSON>", "account_reactivated": "Co<PERSON><PERSON> r<PERSON><PERSON>"}, "retreats": {"created": "Retraite créée avec succès", "updated": "Retraite mise à jour avec succès", "deleted": "Retraite supprimée avec succès", "not_found": "Retraite non trouvée", "booking_success": "Réservation réussie", "booking_failed": "Échec de la réservation", "booking_cancelled": "Réservation annulée", "booking_confirmed": "Réservation confirmée", "no_availability": "Aucune disponibilité pour cette date", "invalid_dates": "Dates invalides", "invalid_capacity": "Capacité invalide", "invalid_price": "Prix invalide"}, "professionals": {"created": "Professionnel créé avec succès", "updated": "Professionnel mis à jour avec succès", "deleted": "Professionnel supprimé avec succès", "not_found": "Professionnel non trouvé", "application_submitted": "Candidature soumise avec succès", "application_approved": "Candidature approuvée", "application_rejected": "Candidature rejetée", "profile_incomplete": "Profil incomplet", "availability_updated": "Disponibilité mise à jour", "service_added": "Service ajouté", "service_updated": "Service mis à jour", "service_deleted": "Service supprimé", "service_not_found": "Service non trouvé"}, "validation": {"required": "Ce champ est requis", "min_length": "Ce champ doit contenir au moins {min} caractères", "max_length": "Ce champ doit contenir au maximum {max} caractères", "email": "<PERSON><PERSON> invalide", "password_too_weak": "Mot de passe trop faible", "passwords_not_match": "Les mots de passe ne correspondent pas", "invalid_date": "Date invalide", "invalid_number": "Nombre invalide", "invalid_phone": "Numéro de téléphone invalide", "invalid_url": "URL invalide", "invalid_format": "Format invalide"}}