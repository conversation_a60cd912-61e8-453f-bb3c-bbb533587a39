import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO pour les métadonnées de pagination
 */
export class PaginationMetaDto {
  @ApiProperty({
    description: 'Nombre total d\'éléments',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Page actuelle',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Nombre d\'éléments par page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Nombre total de pages',
    example: 10,
  })
  totalPages: number;
}

/**
 * DTO générique pour les réponses paginées
 */
export class PaginatedResponseDto<T> {
  @ApiProperty({
    description: 'Liste des éléments pour la page actuelle',
    isArray: true,
  })
  items: T[];

  @ApiProperty({
    description: 'Métadonnées de pagination',
    type: PaginationMetaDto,
  })
  meta: PaginationMetaDto;
}
