import { PipeTransform, Injectable, ArgumentMetadata } from '@nestjs/common';
import { PaginationOptions } from '../../shared/interfaces';

@Injectable()
export class PaginationPipe implements PipeTransform<any, PaginationOptions> {
  private readonly defaultLimit: number;
  private readonly maxLimit: number;

  constructor() {
    this.defaultLimit = 10;
    this.maxLimit = 100;
  }

  transform(value: any, metadata: ArgumentMetadata): PaginationOptions {
    // Si la valeur est déjà un objet de pagination, la retourner
    if (value && typeof value === 'object' && 'page' in value) {
      return this.normalizePaginationOptions(value);
    }

    // Sinon, créer un objet de pagination par défaut
    return {
      page: 1,
      limit: this.defaultLimit,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    };
  }

  private normalizePaginationOptions(options: any): PaginationOptions {
    const page = options.page && Number.isInteger(Number(options.page)) && Number(options.page) > 0
      ? Number(options.page)
      : 1;

    let limit = options.limit && Number.isInteger(Number(options.limit)) && Number(options.limit) > 0
      ? Number(options.limit)
      : this.defaultLimit;

    // Limiter la taille de la page
    limit = Math.min(limit, this.maxLimit);

    const sortBy = options.sortBy || 'createdAt';
    const sortOrder = options.sortOrder === 'asc' ? 'asc' : 'desc';

    return {
      page,
      limit,
      sortBy,
      sortOrder,
    };
  }
}
