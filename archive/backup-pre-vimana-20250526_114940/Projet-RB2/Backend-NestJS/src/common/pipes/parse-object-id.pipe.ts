import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';
import { isUUID } from 'class-validator';

@Injectable()
export class ParseObjectIdPipe implements PipeTransform<string, string> {
  transform(value: string): string {
    // Vérifier si la valeur est un UUID valide
    if (!isUUID(value)) {
      throw new BadRequestException('Invalid ID format');
    }
    
    return value;
  }
}
