import { HttpException, HttpStatus } from '@nestjs/common';
import { ERROR_CODES } from '../../shared/constants';

/**
 * Exception de base pour l'application
 */
export class AppException extends HttpException {
  constructor(
    message: string,
    statusCode: HttpStatus,
    public readonly code: string,
    public readonly details?: any,
  ) {
    super(
      {
        statusCode,
        message,
        error: code,
        details,
      },
      statusCode,
    );
  }
}

/**
 * Exception pour les erreurs de validation
 */
export class ValidationException extends AppException {
  constructor(message = 'Validation failed', details?: any) {
    super(message, HttpStatus.BAD_REQUEST, ERROR_CODES.VALIDATION_ERROR, details);
  }
}

/**
 * Exception pour les erreurs d'authentification
 */
export class AuthenticationException extends AppException {
  constructor(message = 'Authentication failed') {
    super(message, HttpStatus.UNAUTHORIZED, ERROR_CODES.AUTHENTICATION_ERROR);
  }
}

/**
 * Exception pour les erreurs d'autorisation
 */
export class AuthorizationException extends AppException {
  constructor(message = 'Not authorized to perform this action') {
    super(message, HttpStatus.FORBIDDEN, ERROR_CODES.AUTHORIZATION_ERROR);
  }
}

/**
 * Exception pour les ressources non trouvées
 */
export class NotFoundException extends AppException {
  constructor(resource = 'Resource') {
    super(`${resource} not found`, HttpStatus.NOT_FOUND, ERROR_CODES.NOT_FOUND_ERROR);
  }
}

/**
 * Exception pour les conflits de ressources
 */
export class ConflictException extends AppException {
  constructor(message = 'Resource conflict') {
    super(message, HttpStatus.CONFLICT, ERROR_CODES.CONFLICT_ERROR);
  }
}

/**
 * Exception pour les limites de taux
 */
export class RateLimitException extends AppException {
  constructor(message = 'Rate limit exceeded') {
    super(message, HttpStatus.TOO_MANY_REQUESTS, ERROR_CODES.RATE_LIMIT_ERROR);
  }
}

/**
 * Exception pour les erreurs de base de données
 */
export class DatabaseException extends AppException {
  constructor(message = 'Database operation failed') {
    super(message, HttpStatus.INTERNAL_SERVER_ERROR, ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * Exception pour les erreurs de service externe
 */
export class ExternalServiceException extends AppException {
  constructor(service: string, message = 'Service unavailable') {
    super(
      `${service} error: ${message}`,
      HttpStatus.BAD_GATEWAY,
      ERROR_CODES.EXTERNAL_SERVICE_ERROR,
    );
  }
}

/**
 * Exception pour les requêtes mal formées
 */
export class BadRequestException extends AppException {
  constructor(message = 'Bad request') {
    super(message, HttpStatus.BAD_REQUEST, ERROR_CODES.BAD_REQUEST_ERROR);
  }
}

/**
 * Exception pour les délais d'attente
 */
export class TimeoutException extends AppException {
  constructor(message = 'Operation timed out') {
    super(message, HttpStatus.GATEWAY_TIMEOUT, ERROR_CODES.TIMEOUT_ERROR);
  }
}
