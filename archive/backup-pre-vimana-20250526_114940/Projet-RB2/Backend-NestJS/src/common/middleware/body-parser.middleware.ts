import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import * as bodyParser from 'body-parser';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JsonBodyParserMiddleware implements NestMiddleware {
  private readonly logger = new Logger(JsonBodyParserMiddleware.name);
  private readonly jsonParser: any;

  constructor(private readonly configService: ConfigService) {
    // Récupérer la limite de taille du corps de la requête
    const limit = this.configService.get<string>('app.bodyLimit', '10mb');
    
    // Créer le parser JSON
    this.jsonParser = bodyParser.json({
      limit,
      strict: true,
    });
    
    this.logger.log(`JSON body parser middleware initialized with limit: ${limit}`);
  }

  use(req: Request, res: Response, next: NextFunction) {
    this.jsonParser(req, res, next);
  }
}

@Injectable()
export class UrlEncodedBodyParserMiddleware implements NestMiddleware {
  private readonly logger = new Logger(UrlEncodedBodyParserMiddleware.name);
  private readonly urlEncodedParser: any;

  constructor(private readonly configService: ConfigService) {
    // Récupérer la limite de taille du corps de la requête
    const limit = this.configService.get<string>('app.bodyLimit', '10mb');
    
    // Créer le parser URL encoded
    this.urlEncodedParser = bodyParser.urlencoded({
      limit,
      extended: true,
    });
    
    this.logger.log(`URL encoded body parser middleware initialized with limit: ${limit}`);
  }

  use(req: Request, res: Response, next: NextFunction) {
    this.urlEncodedParser(req, res, next);
  }
}
