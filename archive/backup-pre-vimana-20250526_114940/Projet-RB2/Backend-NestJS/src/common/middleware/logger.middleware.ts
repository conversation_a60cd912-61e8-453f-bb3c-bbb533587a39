import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class LoggerMiddleware implements NestMiddleware {
  private readonly logger = new Logger('HTTP');

  use(req: Request, res: Response, next: NextFunction) {
    // Générer un ID de requête unique
    const requestId = uuidv4();
    // Correction TS7053 : cast temporaire en any
    (req as any)['requestId'] = requestId;
    
    // Ajouter l'ID de requête aux en-têtes de réponse
    res.setHeader('X-Request-ID', requestId);
    
    // Récupérer l'adresse IP du client
    const ip = this.getClientIp(req);
    
    // Récupérer l'agent utilisateur
    const userAgent = req.headers['user-agent'] || 'unknown';
    
    // Récupérer l'URL de référence
    const referer = req.headers.referer || 'unknown';
    
    // Récupérer la méthode HTTP et l'URL
    const { method, originalUrl } = req;
    
    // Récupérer l'ID utilisateur si disponible
    // Correction TS2339 : cast temporaire en any
    const userId = (req.user as any)?.id || 'anonymous';
    
    // Journaliser la requête entrante
    this.logger.log(
      `[${requestId}] ${method} ${originalUrl} - User: ${userId} - IP: ${ip} - UserAgent: ${userAgent} - Referer: ${referer}`,
    );
    
    // Récupérer l'heure de début de la requête
    const startTime = Date.now();
    
    // Intercepter la fin de la requête pour journaliser la réponse
    res.on('finish', () => {
      // Calculer la durée de la requête
      const duration = Date.now() - startTime;
      
      // Récupérer le code de statut de la réponse
      const { statusCode } = res;
      
      // Récupérer la taille de la réponse
      const contentLength = res.getHeader('content-length') || 0;
      
      // Journaliser la réponse
      if (statusCode >= 400) {
        this.logger.error(
          `[${requestId}] ${method} ${originalUrl} - ${statusCode} - ${duration}ms - ${contentLength} bytes`,
        );
      } else if (statusCode >= 300) {
        this.logger.warn(
          `[${requestId}] ${method} ${originalUrl} - ${statusCode} - ${duration}ms - ${contentLength} bytes`,
        );
      } else {
        this.logger.log(
          `[${requestId}] ${method} ${originalUrl} - ${statusCode} - ${duration}ms - ${contentLength} bytes`,
        );
      }
    });
    
    next();
  }

  private getClientIp(req: Request): string {
    return (
      req.headers['x-forwarded-for'] ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      'unknown'
    ).toString();
  }
}
