import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { Reflector } from '@nestjs/core';
import { EventsService } from '../../modules/events/events.service';

@Injectable()
export class AuditMiddleware implements NestMiddleware {
  private readonly logger = new Logger(AuditMiddleware.name);

  constructor(
    private readonly eventsService: EventsService,
    private readonly reflector: Reflector,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    // Récupérer l'heure de début de la requête
    const startTime = Date.now();
    
    // Récupérer la méthode HTTP et l'URL
    const { method, originalUrl, body, query, params } = req;
    
    // Récupérer l'adresse IP du client
    const ip = this.getClientIp(req);
    
    // Récupérer l'agent utilisateur
    const userAgent = req.headers['user-agent'] || 'unknown';
    
    // Récupérer l'ID utilisateur si disponible
    // Correction TS2339 : cast temporaire en any
    const userId = (req.user as any)?.id;
    
    // Intercepter la fin de la requête pour journaliser l'audit
    res.on('finish', async () => {
      try {
        // Récupérer le code de statut de la réponse
        const { statusCode } = res;
        
        // Calculer la durée de la requête
        const duration = Date.now() - startTime;
        
        // Déterminer le type d'événement en fonction de la méthode HTTP
        let eventType = 'API_REQUEST';
        if (method === 'POST') eventType = 'API_CREATE';
        if (method === 'PUT' || method === 'PATCH') eventType = 'API_UPDATE';
        if (method === 'DELETE') eventType = 'API_DELETE';
        
        // Créer l'événement d'audit
        await this.eventsService.create({
          eventType,
          payload: {
            method,
            url: originalUrl,
            statusCode,
            duration,
            ip,
            userAgent,
            body: this.sanitizeBody(body),
            query,
            params,
          },
          status: statusCode >= 400 ? 'FAILED' : 'COMPLETED',
          userId: userId ? parseInt(userId) : undefined,
        });
      } catch (error) {
        this.logger.error(`Failed to create audit event: ${error.message}`);
      }
    });
    
    next();
  }

  private getClientIp(req: Request): string {
    return (
      req.headers['x-forwarded-for'] ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      'unknown'
    ).toString();
  }

  private sanitizeBody(body: any): any {
    if (!body) return {};
    
    // Créer une copie du corps de la requête
    const sanitized = { ...body };
    
    // Supprimer les champs sensibles
    const sensitiveFields = ['password', 'passwordConfirmation', 'token', 'refreshToken', 'secret', 'apiKey'];
    
    sensitiveFields.forEach(field => {
      if (field in sanitized) {
        sanitized[field] = '***';
      }
    });
    
    return sanitized;
  }
}
