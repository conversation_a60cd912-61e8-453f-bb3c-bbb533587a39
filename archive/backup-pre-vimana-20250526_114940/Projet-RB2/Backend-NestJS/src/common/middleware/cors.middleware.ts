import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
import * as cors from 'cors';

@Injectable()
export class CorsMiddleware implements NestMiddleware {
  private readonly logger = new Logger(CorsMiddleware.name);
  private readonly corsHandler: any;

  constructor(private readonly configService: ConfigService) {
    // Récupérer la configuration CORS
    const corsConfig = this.configService.get('security.cors', {
      origin: ['http://localhost:3000'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'Accept-Language'],
      exposedHeaders: ['Content-Disposition'],
      maxAge: 3600,
    });
    
    // <PERSON><PERSON><PERSON> le handler CORS
    this.corsHandler = cors(corsConfig);
    
    this.logger.log(`CORS middleware initialized with origins: ${JSON.stringify(corsConfig.origin)}`);
  }

  use(req: Request, res: Response, next: NextFunction) {
    this.corsHandler(req, res, next);
  }
}
