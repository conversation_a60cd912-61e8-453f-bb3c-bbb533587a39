import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
// @ts-ignore
const compression = require('compression');

@Injectable()
export class CompressionMiddleware implements NestMiddleware {
  private readonly logger = new Logger(CompressionMiddleware.name);
  private readonly compressor: any;

  constructor() {
    this.compressor = compression({
      // Ne pas compresser les réponses de moins de 1KB
      threshold: 1024,
      // Niveau de compression (1 = rapide, 9 = meilleure compression)
      level: 6,
      // Types MIME à compresser
      filter: (req: Request, res: Response) => {
        if (req.headers['x-no-compression']) {
          return false;
        }
        
        // Compresser par défaut
        return compression.filter(req, res);
      },
    });
    
    this.logger.log('Compression middleware initialized');
  }

  use(req: Request, res: Response, next: NextFunction) {
    this.compressor(req, res, next);
  }
}
