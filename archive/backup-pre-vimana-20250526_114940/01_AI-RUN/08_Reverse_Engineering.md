# Reverse Engineering Existing Projects

## Context Awareness

**Previous Phases:**
- This phase can be initiated directly for existing projects
- Alternatively, it can follow any of the standard phases when integrating with an existing codebase

## Purpose

This prompt guides the AI through analyzing an existing project, reconstructing the conceptual foundation, and seamlessly integrating it into the Agentic Coding Framework workflow.

## Process Overview

1. **Project Analysis**
   - **Codebase Scanning:** Analyze the project structure, files, and dependencies
   - **Technology Stack Identification:** Determine languages, frameworks, libraries, and tools used
   - **Architecture Mapping:** Identify components, services, data models, and their relationships
   - **Feature Extraction:** Catalog existing functionality and user flows

2. **Documentation Reconstruction**
   - **Reverse PRD Generation:** Create a Product Requirements Document based on implemented features
   - **Core Concept Extraction:** Distill the project's central value proposition and target audience
   - **Market Context Inference:** Deduce the market positioning and competitive landscape
   - **Initial Idea Formulation:** Reconstruct the original project concept and objectives

3. **Framework Integration**
   - **Documentation Population:** Fill the standard framework documents (`idea_document.md`, `market_research.md`, etc.)
   - **Task Extraction:** Generate `tasks.json` with existing and pending tasks
   - **Technical Specs Creation:** Populate the `02_AI-DOCS/` and `03_SPECS/` directories

4. **Development Continuation**
   - **Gap Analysis:** Identify missing features or improvements needed
   - **Testing Strategy:** Develop tests for existing functionality
   - **Deployment Planning:** Create deployment pipeline if not present

## Implementation Guidelines

1. **Start with Structure Analysis:** Begin by mapping the project's directory structure and identifying key files
2. **Follow Technology Patterns:** Use identified frameworks to guide your understanding of the codebase
3. **Prioritize Documentation:** Focus on creating comprehensive documentation before suggesting changes
4. **Respect Existing Patterns:** Maintain consistency with the project's coding style and architecture

## Expected Outcomes

By following this reverse engineering approach, you will:

1. Generate a complete set of framework documents for an existing project
2. Provide a clear roadmap for continuing development within the framework
3. Enable seamless integration of existing work with the structured Agentic Coding workflow

## Transition to Next Phase

After completing the reverse engineering:

1. Update `project_session_state.json` with the reconstructed project information
2. Proceed to the appropriate next phase based on the project's current state (typically Task Management or Implementation)
3. If needed, refine the reconstructed documents with user feedback
