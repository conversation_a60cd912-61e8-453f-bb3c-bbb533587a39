import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';

/**
 * Types pour l'Agent Translation
 */
export interface TranslationRequest {
  id: string;
  text: string;
  sourceLanguage: string;
  targetLanguage: string;
  context?: string;
  domain?: string;
  tone?: 'formal' | 'informal' | 'technical' | 'marketing' | 'casual';
  culturalAdaptation?: boolean;
  preserveFormatting?: boolean;
  glossary?: Record<string, string>;
  createdAt: Date;
}

export interface TranslationResult {
  id: string;
  requestId: string;
  originalText: string;
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  confidence: number;
  culturalAdaptations: CulturalAdaptation[];
  qualityScore: number;
  alternatives: string[];
  metadata: TranslationMetadata;
  createdAt: Date;
}

export interface CulturalAdaptation {
  type: 'currency' | 'date' | 'address' | 'phone' | 'cultural_reference' | 'idiom';
  original: string;
  adapted: string;
  explanation: string;
}

export interface TranslationMetadata {
  model: string;
  processingTime: number;
  wordCount: number;
  characterCount: number;
  complexity: 'low' | 'medium' | 'high';
  domain: string;
}

export interface TranslationMemory {
  id: string;
  sourceText: string;
  targetText: string;
  sourceLanguage: string;
  targetLanguage: string;
  domain: string;
  quality: number;
  usage: number;
  lastUsed: Date;
  createdAt: Date;
}

export interface Glossary {
  id: string;
  name: string;
  description: string;
  sourceLanguage: string;
  targetLanguage: string;
  domain: string;
  terms: GlossaryTerm[];
  createdAt: Date;
  updatedAt: Date;
}

export interface GlossaryTerm {
  source: string;
  target: string;
  context?: string;
  notes?: string;
  approved: boolean;
}

/**
 * Agent Translation Principal
 * Responsable de la traduction et adaptation culturelle avec Ollama
 */
export class TranslationAgent extends EventEmitter {
  private logger: Logger;
  private isInitialized: boolean = false;
  private ollamaUrl: string;
  private defaultModel: string = 'llama2';

  // Langues supportées
  private readonly supportedLanguages = {
    'en': 'English',
    'fr': 'Français',
    'es': 'Español',
    'de': 'Deutsch',
    'it': 'Italiano',
    'pt': 'Português',
    'ru': 'Русский',
    'zh': '中文',
    'ja': '日本語',
    'ko': '한국어',
    'ar': 'العربية',
    'hi': 'हिन्दी'
  };

  // Domaines spécialisés
  private readonly domains = {
    'general': 'Général',
    'technical': 'Technique',
    'medical': 'Médical',
    'legal': 'Juridique',
    'marketing': 'Marketing',
    'finance': 'Finance',
    'education': 'Éducation',
    'tourism': 'Tourisme'
  };

  constructor(logger: Logger) {
    super();
    this.logger = logger;
    this.ollamaUrl = process.env.OLLAMA_URL || 'http://localhost:11434';
  }

  /**
   * Initialise l'agent de traduction
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initialisation de l\'Agent Translation...');

      // Vérification de la connexion Ollama
      await this.checkOllamaConnection();

      // Chargement du modèle par défaut
      await this.loadModel(this.defaultModel);

      this.isInitialized = true;
      this.logger.info('Agent Translation initialisé avec succès');
      this.emit('initialized');

    } catch (error) {
      this.logger.error('Erreur lors de l\'initialisation de l\'Agent Translation:', error);
      throw error;
    }
  }

  /**
   * Traduit un texte
   */
  async translateText(request: TranslationRequest): Promise<TranslationResult> {
    if (!this.isInitialized) {
      throw new Error('Agent Translation non initialisé');
    }

    this.logger.info(`Traduction: ${request.sourceLanguage} -> ${request.targetLanguage}`);

    try {
      const startTime = Date.now();

      // Préparation du prompt pour Ollama
      const prompt = this.buildTranslationPrompt(request);

      // Appel à Ollama pour la traduction
      const translatedText = await this.callOllama(prompt);

      // Post-traitement
      const processedText = await this.postProcessTranslation(translatedText, request);

      // Adaptation culturelle si demandée
      const culturalAdaptations = request.culturalAdaptation ? 
        await this.applyCulturalAdaptation(processedText, request.targetLanguage) : [];

      // Évaluation de la qualité
      const qualityScore = await this.evaluateTranslationQuality(
        request.text, 
        processedText, 
        request.sourceLanguage, 
        request.targetLanguage
      );

      // Génération d'alternatives
      const alternatives = await this.generateAlternatives(request, processedText);

      const result: TranslationResult = {
        id: uuidv4(),
        requestId: request.id,
        originalText: request.text,
        translatedText: processedText,
        sourceLanguage: request.sourceLanguage,
        targetLanguage: request.targetLanguage,
        confidence: this.calculateConfidence(qualityScore),
        culturalAdaptations,
        qualityScore,
        alternatives,
        metadata: {
          model: this.defaultModel,
          processingTime: Date.now() - startTime,
          wordCount: request.text.split(' ').length,
          characterCount: request.text.length,
          complexity: this.assessComplexity(request.text),
          domain: request.domain || 'general'
        },
        createdAt: new Date()
      };

      this.logger.info(`Traduction terminée: ${result.id}, qualité: ${qualityScore}`);
      this.emit('translationCompleted', result);

      return result;

    } catch (error) {
      this.logger.error('Erreur lors de la traduction:', error);
      throw error;
    }
  }

  /**
   * Traduit du contenu UI/UX
   */
  async translateUIContent(content: any, targetLanguage: string): Promise<any> {
    this.logger.info(`Traduction UI vers ${targetLanguage}`);

    try {
      const translatedContent = { ...content };

      // Traduction des éléments UI
      if (content.labels) {
        translatedContent.labels = await this.translateObject(content.labels, 'en', targetLanguage);
      }

      if (content.messages) {
        translatedContent.messages = await this.translateObject(content.messages, 'en', targetLanguage);
      }

      if (content.placeholders) {
        translatedContent.placeholders = await this.translateObject(content.placeholders, 'en', targetLanguage);
      }

      // Adaptation des formats
      translatedContent.formats = await this.adaptFormats(content.formats, targetLanguage);

      this.logger.info(`Contenu UI traduit vers ${targetLanguage}`);
      this.emit('uiContentTranslated', { targetLanguage, content: translatedContent });

      return translatedContent;

    } catch (error) {
      this.logger.error('Erreur lors de la traduction UI:', error);
      throw error;
    }
  }

  /**
   * Crée une mémoire de traduction
   */
  async createTranslationMemory(
    sourceText: string,
    targetText: string,
    sourceLanguage: string,
    targetLanguage: string,
    domain: string = 'general'
  ): Promise<TranslationMemory> {
    const memory: TranslationMemory = {
      id: uuidv4(),
      sourceText,
      targetText,
      sourceLanguage,
      targetLanguage,
      domain,
      quality: await this.evaluateTranslationQuality(sourceText, targetText, sourceLanguage, targetLanguage),
      usage: 1,
      lastUsed: new Date(),
      createdAt: new Date()
    };

    this.logger.info(`Mémoire de traduction créée: ${memory.id}`);
    this.emit('translationMemoryCreated', memory);

    return memory;
  }

  /**
   * Gère un glossaire de traduction
   */
  async createGlossary(
    name: string,
    sourceLanguage: string,
    targetLanguage: string,
    domain: string,
    terms: GlossaryTerm[]
  ): Promise<Glossary> {
    const glossary: Glossary = {
      id: uuidv4(),
      name,
      description: `Glossaire ${domain} ${sourceLanguage}-${targetLanguage}`,
      sourceLanguage,
      targetLanguage,
      domain,
      terms,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.logger.info(`Glossaire créé: ${glossary.id} (${terms.length} termes)`);
    this.emit('glossaryCreated', glossary);

    return glossary;
  }

  /**
   * Détecte automatiquement la langue
   */
  async detectLanguage(text: string): Promise<string> {
    try {
      const prompt = `Detect the language of this text and respond with only the ISO 639-1 language code (2 letters): "${text}"`;
      
      const response = await this.callOllama(prompt);
      const detectedLanguage = response.trim().toLowerCase();

      if (this.supportedLanguages[detectedLanguage]) {
        this.logger.info(`Langue détectée: ${detectedLanguage}`);
        return detectedLanguage;
      }

      // Fallback vers l'anglais si la détection échoue
      this.logger.warn(`Langue non reconnue: ${detectedLanguage}, fallback vers 'en'`);
      return 'en';

    } catch (error) {
      this.logger.error('Erreur lors de la détection de langue:', error);
      return 'en'; // Fallback
    }
  }

  /**
   * Vérifie la connexion Ollama
   */
  private async checkOllamaConnection(): Promise<void> {
    try {
      const response = await axios.get(`${this.ollamaUrl}/api/tags`);
      this.logger.info('Connexion Ollama établie');
    } catch (error) {
      throw new Error(`Impossible de se connecter à Ollama: ${error.message}`);
    }
  }

  /**
   * Charge un modèle Ollama
   */
  private async loadModel(model: string): Promise<void> {
    try {
      // Vérifier si le modèle est disponible
      const response = await axios.get(`${this.ollamaUrl}/api/tags`);
      const models = response.data.models || [];
      
      const modelExists = models.some((m: any) => m.name.includes(model));
      
      if (!modelExists) {
        this.logger.warn(`Modèle ${model} non trouvé, tentative de téléchargement...`);
        await this.pullModel(model);
      }

      this.logger.info(`Modèle ${model} prêt`);

    } catch (error) {
      this.logger.error(`Erreur lors du chargement du modèle ${model}:`, error);
      throw error;
    }
  }

  /**
   * Télécharge un modèle Ollama
   */
  private async pullModel(model: string): Promise<void> {
    try {
      await axios.post(`${this.ollamaUrl}/api/pull`, { name: model });
      this.logger.info(`Modèle ${model} téléchargé`);
    } catch (error) {
      throw new Error(`Impossible de télécharger le modèle ${model}: ${error.message}`);
    }
  }

  /**
   * Construit le prompt de traduction
   */
  private buildTranslationPrompt(request: TranslationRequest): string {
    const sourceLanguageName = this.supportedLanguages[request.sourceLanguage] || request.sourceLanguage;
    const targetLanguageName = this.supportedLanguages[request.targetLanguage] || request.targetLanguage;

    let prompt = `Translate the following text from ${sourceLanguageName} to ${targetLanguageName}:\n\n`;
    
    if (request.context) {
      prompt += `Context: ${request.context}\n\n`;
    }

    if (request.domain && request.domain !== 'general') {
      prompt += `Domain: ${this.domains[request.domain] || request.domain}\n\n`;
    }

    if (request.tone) {
      prompt += `Tone: ${request.tone}\n\n`;
    }

    prompt += `Text to translate: "${request.text}"\n\n`;
    prompt += `Provide only the translation without any additional text or explanation.`;

    return prompt;
  }

  /**
   * Appelle Ollama pour la génération
   */
  private async callOllama(prompt: string): Promise<string> {
    try {
      const response = await axios.post(`${this.ollamaUrl}/api/generate`, {
        model: this.defaultModel,
        prompt: prompt,
        stream: false,
        options: {
          temperature: 0.3, // Faible température pour plus de cohérence
          top_p: 0.9,
          top_k: 40
        }
      });

      return response.data.response.trim();

    } catch (error) {
      this.logger.error('Erreur lors de l\'appel Ollama:', error);
      throw new Error(`Erreur Ollama: ${error.message}`);
    }
  }

  /**
   * Post-traite la traduction
   */
  private async postProcessTranslation(text: string, request: TranslationRequest): Promise<string> {
    let processedText = text;

    // Application du glossaire si fourni
    if (request.glossary) {
      for (const [source, target] of Object.entries(request.glossary)) {
        const regex = new RegExp(`\\b${source}\\b`, 'gi');
        processedText = processedText.replace(regex, target);
      }
    }

    // Préservation du formatage si demandé
    if (request.preserveFormatting) {
      processedText = this.preserveFormatting(request.text, processedText);
    }

    return processedText;
  }

  /**
   * Applique l'adaptation culturelle
   */
  private async applyCulturalAdaptation(text: string, targetLanguage: string): Promise<CulturalAdaptation[]> {
    const adaptations: CulturalAdaptation[] = [];

    // Adaptation des devises
    const currencyAdaptations = await this.adaptCurrency(text, targetLanguage);
    adaptations.push(...currencyAdaptations);

    // Adaptation des dates
    const dateAdaptations = await this.adaptDates(text, targetLanguage);
    adaptations.push(...dateAdaptations);

    // Adaptation des références culturelles
    const culturalAdaptations = await this.adaptCulturalReferences(text, targetLanguage);
    adaptations.push(...culturalAdaptations);

    return adaptations;
  }

  /**
   * Évalue la qualité de la traduction
   */
  private async evaluateTranslationQuality(
    source: string,
    target: string,
    sourceLanguage: string,
    targetLanguage: string
  ): Promise<number> {
    // Simulation d'évaluation de qualité
    // Dans un vrai système, cela utiliserait des métriques comme BLEU, METEOR, etc.
    
    let score = 80; // Score de base

    // Facteurs d'évaluation
    const lengthRatio = target.length / source.length;
    if (lengthRatio < 0.5 || lengthRatio > 2.0) {
      score -= 10; // Pénalité pour ratio de longueur anormal
    }

    // Vérification de la cohérence terminologique
    if (this.hasTerminologyConsistency(source, target)) {
      score += 5;
    }

    // Vérification de la fluidité
    if (this.hasGoodFluency(target, targetLanguage)) {
      score += 10;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Calcule la confiance
   */
  private calculateConfidence(qualityScore: number): number {
    return qualityScore / 100;
  }

  /**
   * Évalue la complexité du texte
   */
  private assessComplexity(text: string): 'low' | 'medium' | 'high' {
    const wordCount = text.split(' ').length;
    const avgWordLength = text.replace(/\s/g, '').length / wordCount;
    
    if (wordCount < 50 && avgWordLength < 6) return 'low';
    if (wordCount < 200 && avgWordLength < 8) return 'medium';
    return 'high';
  }

  /**
   * Génère des alternatives de traduction
   */
  private async generateAlternatives(request: TranslationRequest, mainTranslation: string): Promise<string[]> {
    // Simulation de génération d'alternatives
    return [
      mainTranslation,
      // Ici on pourrait générer des alternatives avec des prompts différents
    ];
  }

  /**
   * Traduit un objet récursivement
   */
  private async translateObject(obj: any, sourceLanguage: string, targetLanguage: string): Promise<any> {
    const result: any = {};

    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'string') {
        const request: TranslationRequest = {
          id: uuidv4(),
          text: value,
          sourceLanguage,
          targetLanguage,
          createdAt: new Date()
        };
        const translation = await this.translateText(request);
        result[key] = translation.translatedText;
      } else if (typeof value === 'object' && value !== null) {
        result[key] = await this.translateObject(value, sourceLanguage, targetLanguage);
      } else {
        result[key] = value;
      }
    }

    return result;
  }

  /**
   * Adapte les formats selon la locale
   */
  private async adaptFormats(formats: any, targetLanguage: string): Promise<any> {
    const adaptedFormats = { ...formats };

    // Adaptation des formats de date
    const dateFormats = {
      'en': 'MM/DD/YYYY',
      'fr': 'DD/MM/YYYY',
      'de': 'DD.MM.YYYY',
      'es': 'DD/MM/YYYY'
    };

    adaptedFormats.date = dateFormats[targetLanguage] || dateFormats['en'];

    // Adaptation des formats de nombre
    const numberFormats = {
      'en': { decimal: '.', thousands: ',' },
      'fr': { decimal: ',', thousands: ' ' },
      'de': { decimal: ',', thousands: '.' },
      'es': { decimal: ',', thousands: '.' }
    };

    adaptedFormats.number = numberFormats[targetLanguage] || numberFormats['en'];

    return adaptedFormats;
  }

  /**
   * Préserve le formatage original
   */
  private preserveFormatting(original: string, translated: string): string {
    // Logique de préservation du formatage (HTML, Markdown, etc.)
    return translated;
  }

  /**
   * Adapte les devises
   */
  private async adaptCurrency(text: string, targetLanguage: string): Promise<CulturalAdaptation[]> {
    // Simulation d'adaptation de devise
    return [];
  }

  /**
   * Adapte les dates
   */
  private async adaptDates(text: string, targetLanguage: string): Promise<CulturalAdaptation[]> {
    // Simulation d'adaptation de dates
    return [];
  }

  /**
   * Adapte les références culturelles
   */
  private async adaptCulturalReferences(text: string, targetLanguage: string): Promise<CulturalAdaptation[]> {
    // Simulation d'adaptation culturelle
    return [];
  }

  /**
   * Vérifie la cohérence terminologique
   */
  private hasTerminologyConsistency(source: string, target: string): boolean {
    // Simulation de vérification de cohérence
    return true;
  }

  /**
   * Vérifie la fluidité
   */
  private hasGoodFluency(text: string, language: string): boolean {
    // Simulation de vérification de fluidité
    return true;
  }

  /**
   * Arrête l'agent
   */
  async shutdown(): Promise<void> {
    this.logger.info('Arrêt de l\'Agent Translation...');
    this.isInitialized = false;
    this.logger.info('Agent Translation arrêté avec succès');
    this.emit('shutdown');
  }
}
