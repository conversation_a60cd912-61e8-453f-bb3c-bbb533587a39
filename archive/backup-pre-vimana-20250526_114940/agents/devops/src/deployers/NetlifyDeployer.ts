import { Logger } from 'winston';
import { 
  DeploymentRequest,
  DeploymentResult
} from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import axios from 'axios';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as archiver from 'archiver';

/**
 * Déployeur Netlify
 * 
 * Gère les déploiements sur la plateforme Netlify avec support
 * pour les sites statiques, fonctions serverless et formulaires.
 */
export class NetlifyDeployer {
  private logger: Logger;
  private memory: WeaviateMemory;
  private apiToken: string;
  private baseUrl = 'https://api.netlify.com/api/v1';

  constructor(logger: Logger, memory: WeaviateMemory) {
    this.logger = logger;
    this.memory = memory;
    this.apiToken = process.env.NETLIFY_TOKEN || '';

    if (!this.apiToken) {
      this.logger.warn('Token Netlify non configuré');
    }
  }

  /**
   * Déploie une application sur Netlify
   */
  async deploy(request: DeploymentRequest, buildResult: any): Promise<DeploymentResult> {
    this.logger.info('Déploiement Netlify', { 
      app: request.applicationName,
      environment: request.environment 
    });

    const startTime = new Date();
    const deploymentId = request.id;

    try {
      // 1. Créer ou récupérer le site Netlify
      const site = await this.ensureSite(request);

      // 2. Préparer les fichiers pour le déploiement
      const deploymentFiles = await this.prepareDeploymentFiles(request, buildResult);

      // 3. Créer un déploiement
      const deployment = await this.createDeployment(site.id, deploymentFiles);

      // 4. Uploader les fichiers
      await this.uploadFiles(deployment.id, deploymentFiles);

      // 5. Attendre que le déploiement soit prêt
      const deploymentUrl = await this.waitForDeploymentReady(deployment.id);

      // 6. Configurer le domaine personnalisé si spécifié
      if (request.configuration.networking?.ingress?.hostname) {
        await this.configureDomain(site.id, request.configuration.networking.ingress.hostname);
      }

      // 7. Configurer les variables d'environnement
      if (Object.keys(request.configuration.environment).length > 0) {
        await this.configureEnvironmentVariables(site.id, request.configuration.environment);
      }

      // 8. Configurer les redirections et headers
      await this.configureNetlifySettings(site.id, request);

      const result: DeploymentResult = {
        id: deploymentId,
        status: 'running',
        url: deploymentUrl,
        endpoints: [{
          name: 'main',
          url: deploymentUrl,
          type: 'web',
          status: 'healthy'
        }],
        resources: [{
          type: 'deployment',
          name: request.applicationName,
          status: 'running',
          created: new Date(),
          metadata: {
            netlifyId: deployment.id,
            siteId: site.id,
            netlifyUrl: deploymentUrl
          }
        }],
        metrics: await this.collectInitialMetrics(site.id),
        logs: [],
        startedAt: startTime,
        completedAt: new Date(),
        duration: Date.now() - startTime.getTime()
      };

      this.logger.info('Déploiement Netlify réussi', { 
        deploymentId,
        url: result.url,
        duration: result.duration 
      });

      return result;

    } catch (error) {
      this.logger.error('Erreur lors du déploiement Netlify', { 
        deploymentId,
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Supprime un site Netlify
   */
  async delete(siteId: string): Promise<void> {
    this.logger.info('Suppression du site Netlify', { siteId });

    try {
      await this.makeNetlifyRequest('DELETE', `/sites/${siteId}`);
      this.logger.info('Site Netlify supprimé', { siteId });
    } catch (error) {
      this.logger.error('Erreur lors de la suppression Netlify', { 
        siteId,
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Obtient les métriques d'un site
   */
  async getMetrics(siteId: string): Promise<any> {
    try {
      const response = await this.makeNetlifyRequest('GET', `/sites/${siteId}/analytics`);
      return this.parseMetrics(response.data);
    } catch (error) {
      this.logger.error('Erreur lors de la récupération des métriques Netlify', { 
        siteId,
        error: error.message 
      });
      return this.getEmptyMetrics();
    }
  }

  // Méthodes privées

  private async ensureSite(request: DeploymentRequest): Promise<any> {
    const siteName = request.applicationName.toLowerCase().replace(/[^a-z0-9-]/g, '-');

    try {
      // Essayer de récupérer le site existant
      const response = await this.makeNetlifyRequest('GET', `/sites/${siteName}`);
      this.logger.info('Site Netlify existant trouvé', { siteName });
      return response.data;
    } catch (error) {
      if (error.response?.status === 404) {
        // Créer un nouveau site
        this.logger.info('Création d\'un nouveau site Netlify', { siteName });
        const siteData = {
          name: siteName,
          custom_domain: request.configuration.networking?.ingress?.hostname,
          build_settings: {
            cmd: this.getBuildCommand(request),
            dir: this.getBuildDirectory(request)
          }
        };

        const response = await this.makeNetlifyRequest('POST', '/sites', siteData);
        return response.data;
      }
      throw error;
    }
  }

  private async prepareDeploymentFiles(request: DeploymentRequest, buildResult: any): Promise<Map<string, Buffer>> {
    const files = new Map<string, Buffer>();

    if (request.source.type === 'generated-code' && request.source.generatedCode) {
      // Utiliser le code généré par l'Agent Frontend
      for (const file of request.source.generatedCode.files) {
        files.set(file.path, Buffer.from(file.content));
      }

      // Ajouter les fichiers de configuration Netlify
      files.set('_redirects', Buffer.from(this.generateRedirects(request)));
      files.set('_headers', Buffer.from(this.generateHeaders(request)));
      
      // Ajouter netlify.toml si nécessaire
      const netlifyConfig = this.generateNetlifyConfig(request);
      if (netlifyConfig) {
        files.set('netlify.toml', Buffer.from(netlifyConfig));
      }
    } else {
      // Utiliser les fichiers du build
      for (const file of buildResult.files || []) {
        files.set(file.path, Buffer.from(file.content));
      }
    }

    return files;
  }

  private async createDeployment(siteId: string, files: Map<string, Buffer>): Promise<any> {
    const fileHashes = new Map<string, string>();
    
    // Calculer les hashes des fichiers
    for (const [filePath, content] of files) {
      const hash = require('crypto').createHash('sha1').update(content).digest('hex');
      fileHashes.set(filePath, hash);
    }

    const deploymentData = {
      files: Object.fromEntries(fileHashes),
      draft: false,
      async: false
    };

    const response = await this.makeNetlifyRequest('POST', `/sites/${siteId}/deploys`, deploymentData);
    return response.data;
  }

  private async uploadFiles(deploymentId: string, files: Map<string, Buffer>): Promise<void> {
    this.logger.info('Upload des fichiers vers Netlify', { 
      deploymentId,
      fileCount: files.size 
    });

    const uploadPromises = [];

    for (const [filePath, content] of files) {
      const uploadPromise = this.uploadFile(deploymentId, filePath, content);
      uploadPromises.push(uploadPromise);
    }

    await Promise.all(uploadPromises);
    this.logger.info('Tous les fichiers uploadés vers Netlify', { deploymentId });
  }

  private async uploadFile(deploymentId: string, filePath: string, content: Buffer): Promise<void> {
    try {
      await this.makeNetlifyRequest(
        'PUT',
        `/deploys/${deploymentId}/files/${encodeURIComponent(filePath)}`,
        content,
        { 'Content-Type': 'application/octet-stream' }
      );
    } catch (error) {
      this.logger.error('Erreur lors de l\'upload du fichier', { 
        deploymentId,
        filePath,
        error: error.message 
      });
      throw error;
    }
  }

  private async waitForDeploymentReady(deploymentId: string): Promise<string> {
    const maxWaitTime = 600000; // 10 minutes
    const checkInterval = 10000; // 10 secondes
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const response = await this.makeNetlifyRequest('GET', `/deploys/${deploymentId}`);
        const deployment = response.data;

        if (deployment.state === 'ready') {
          this.logger.info('Déploiement Netlify prêt', { 
            deploymentId,
            url: deployment.deploy_ssl_url || deployment.deploy_url 
          });
          return deployment.deploy_ssl_url || deployment.deploy_url;
        } else if (deployment.state === 'error') {
          throw new Error(`Déploiement Netlify échoué: ${deployment.error_message || 'Erreur inconnue'}`);
        }

        this.logger.info('Attente du déploiement Netlify', { 
          deploymentId,
          state: deployment.state 
        });

        await new Promise(resolve => setTimeout(resolve, checkInterval));
      } catch (error) {
        if (error.response?.status === 404) {
          this.logger.warn('Déploiement Netlify non trouvé', { deploymentId });
          await new Promise(resolve => setTimeout(resolve, checkInterval));
          continue;
        }
        throw error;
      }
    }

    throw new Error(`Timeout: Le déploiement Netlify ${deploymentId} n'est pas prêt après ${maxWaitTime}ms`);
  }

  private async configureDomain(siteId: string, domain: string): Promise<void> {
    try {
      this.logger.info('Configuration du domaine Netlify', { siteId, domain });

      await this.makeNetlifyRequest('POST', `/sites/${siteId}/domains`, {
        domain_name: domain
      });

      this.logger.info('Domaine Netlify configuré', { siteId, domain });
    } catch (error) {
      this.logger.warn('Erreur lors de la configuration du domaine Netlify', { 
        siteId,
        domain,
        error: error.message 
      });
    }
  }

  private async configureEnvironmentVariables(
    siteId: string,
    environment: Record<string, string>
  ): Promise<void> {
    try {
      this.logger.info('Configuration des variables d\'environnement Netlify', { siteId });

      const envVars = Object.entries(environment).map(([key, value]) => ({
        key,
        value,
        scopes: ['builds', 'functions']
      }));

      await this.makeNetlifyRequest('PUT', `/sites/${siteId}/env`, envVars);

      this.logger.info('Variables d\'environnement Netlify configurées', { siteId });
    } catch (error) {
      this.logger.warn('Erreur lors de la configuration des variables d\'environnement Netlify', { 
        siteId,
        error: error.message 
      });
    }
  }

  private async configureNetlifySettings(siteId: string, request: DeploymentRequest): Promise<void> {
    try {
      const settings: any = {};

      // Configuration des headers de sécurité
      if (request.configuration.networking) {
        settings.processing_settings = {
          css: { bundle: true, minify: true },
          js: { bundle: true, minify: true },
          images: { optimize: true },
          html: { pretty_urls: true }
        };
      }

      if (Object.keys(settings).length > 0) {
        await this.makeNetlifyRequest('PATCH', `/sites/${siteId}`, settings);
        this.logger.info('Paramètres Netlify configurés', { siteId });
      }
    } catch (error) {
      this.logger.warn('Erreur lors de la configuration des paramètres Netlify', { 
        siteId,
        error: error.message 
      });
    }
  }

  private generateRedirects(request: DeploymentRequest): string {
    const redirects = [];

    // Redirection pour SPA
    if (request.source.generatedCode?.framework === 'react' || 
        request.source.generatedCode?.framework === 'vue') {
      redirects.push('/*    /index.html   200');
    }

    // Redirections personnalisées
    if (request.configuration.networking?.ingress?.hostname) {
      redirects.push(`http://${request.configuration.networking.ingress.hostname}/*    https://${request.configuration.networking.ingress.hostname}/:splat    301!`);
    }

    return redirects.join('\n');
  }

  private generateHeaders(request: DeploymentRequest): string {
    const headers = [];

    // Headers de sécurité
    headers.push('/*');
    headers.push('  X-Frame-Options: DENY');
    headers.push('  X-XSS-Protection: 1; mode=block');
    headers.push('  X-Content-Type-Options: nosniff');
    headers.push('  Referrer-Policy: strict-origin-when-cross-origin');

    // Headers de cache
    headers.push('/static/*');
    headers.push('  Cache-Control: public, max-age=31536000, immutable');

    return headers.join('\n');
  }

  private generateNetlifyConfig(request: DeploymentRequest): string | null {
    const config: any = {
      build: {
        command: this.getBuildCommand(request),
        publish: this.getBuildDirectory(request)
      }
    };

    // Configuration des fonctions si nécessaire
    if (request.configuration.environment.NETLIFY_FUNCTIONS) {
      config.functions = {
        directory: 'netlify/functions'
      };
    }

    // Configuration des redirections
    config.redirects = [
      {
        from: '/*',
        to: '/index.html',
        status: 200,
        conditions: {
          Role: ['admin']
        }
      }
    ];

    return `[build]\n  command = "${config.build.command}"\n  publish = "${config.build.publish}"\n\n[[redirects]]\n  from = "/*"\n  to = "/index.html"\n  status = 200`;
  }

  private getBuildCommand(request: DeploymentRequest): string {
    const framework = request.source.generatedCode?.framework;
    
    switch (framework) {
      case 'react':
        return 'npm run build';
      case 'vue':
        return 'npm run build';
      default:
        return 'echo "No build command needed"';
    }
  }

  private getBuildDirectory(request: DeploymentRequest): string {
    const framework = request.source.generatedCode?.framework;
    
    switch (framework) {
      case 'react':
        return 'build';
      case 'vue':
        return 'dist';
      default:
        return '.';
    }
  }

  private async collectInitialMetrics(siteId: string): Promise<any> {
    try {
      const metrics = await this.getMetrics(siteId);
      return metrics;
    } catch (error) {
      return this.getEmptyMetrics();
    }
  }

  private async makeNetlifyRequest(
    method: string, 
    endpoint: string, 
    data?: any, 
    additionalHeaders?: Record<string, string>
  ): Promise<any> {
    const config: any = {
      method,
      url: `${this.baseUrl}${endpoint}`,
      headers: {
        'Authorization': `Bearer ${this.apiToken}`,
        'Content-Type': 'application/json',
        ...additionalHeaders
      }
    };

    if (data) {
      if (Buffer.isBuffer(data)) {
        config.data = data;
        config.headers['Content-Type'] = 'application/octet-stream';
      } else {
        config.data = data;
      }
    }

    try {
      const response = await axios(config);
      return response;
    } catch (error) {
      this.logger.error('Erreur API Netlify', { 
        method,
        endpoint,
        status: error.response?.status,
        message: error.response?.data?.message || error.message 
      });
      throw error;
    }
  }

  private parseMetrics(analytics: any): any {
    const metrics = this.getEmptyMetrics();

    if (analytics && analytics.data) {
      metrics.requests.total = analytics.data.pageviews || 0;
      metrics.network.bytesOut = analytics.data.bandwidth || 0;
    }

    return metrics;
  }

  private getEmptyMetrics(): any {
    return {
      cpu: { current: 0, average: 0, peak: 0, unit: 'cores' },
      memory: { current: 0, average: 0, peak: 0, unit: 'MB' },
      network: { bytesIn: 0, bytesOut: 0, connectionsActive: 0, connectionsTotal: 0 },
      requests: { total: 0, rate: 0, latencyP50: 0, latencyP95: 0, latencyP99: 0 },
      errors: { total: 0, rate: 0, types: {} }
    };
  }
}
