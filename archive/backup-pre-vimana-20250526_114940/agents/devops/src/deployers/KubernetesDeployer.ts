import { Logger } from 'winston';
import { 
  DeploymentRequest,
  DeploymentResult,
  DeploymentStatus,
  DeployedResource,
  KubernetesManifest
} from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import * as k8s from '@kubernetes/client-node';
import * as yaml from 'yaml';

/**
 * Déployeur Kubernetes
 * 
 * Gère les déploiements sur clusters Kubernetes avec support
 * pour les manifests, Helm charts et opérateurs.
 */
export class KubernetesDeployer {
  private logger: Logger;
  private memory: WeaviateMemory;
  private k8sApi: k8s.KubernetesApi;
  private appsV1Api: k8s.AppsV1Api;
  private coreV1Api: k8s.CoreV1Api;
  private networkingV1Api: k8s.NetworkingV1Api;

  constructor(logger: Logger, memory: WeaviateMemory) {
    this.logger = logger;
    this.memory = memory;
    
    // Initialiser le client Kubernetes
    const kc = new k8s.KubeConfig();
    
    // Charger la configuration depuis le fichier ou les variables d'environnement
    if (process.env.KUBECONFIG) {
      kc.loadFromFile(process.env.KUBECONFIG);
    } else {
      kc.loadFromDefault();
    }

    this.k8sApi = kc.makeApiClient(k8s.KubernetesApi);
    this.appsV1Api = kc.makeApiClient(k8s.AppsV1Api);
    this.coreV1Api = kc.makeApiClient(k8s.CoreV1Api);
    this.networkingV1Api = kc.makeApiClient(k8s.NetworkingV1Api);
  }

  /**
   * Déploie une application sur Kubernetes
   */
  async deploy(request: DeploymentRequest, buildResult: any): Promise<DeploymentResult> {
    this.logger.info('Déploiement Kubernetes', { 
      app: request.applicationName,
      namespace: request.platform.namespace || 'default'
    });

    const startTime = new Date();
    const deploymentId = request.id;
    const namespace = request.platform.namespace || 'default';

    try {
      // 1. Créer le namespace si nécessaire
      await this.ensureNamespace(namespace);

      // 2. Générer les manifests Kubernetes
      const manifests = await this.generateManifests(request, buildResult);

      // 3. Appliquer les ConfigMaps et Secrets
      const configResources = await this.applyConfigResources(manifests, namespace);

      // 4. Appliquer les Services
      const serviceResources = await this.applyServices(manifests, namespace);

      // 5. Appliquer les Deployments
      const deploymentResources = await this.applyDeployments(manifests, namespace);

      // 6. Appliquer les Ingress
      const ingressResources = await this.applyIngress(manifests, namespace);

      // 7. Attendre que le déploiement soit prêt
      await this.waitForDeploymentReady(request.applicationName, namespace);

      // 8. Collecter les informations de déploiement
      const endpoints = await this.getEndpoints(request.applicationName, namespace);
      const allResources = [
        ...configResources,
        ...serviceResources,
        ...deploymentResources,
        ...ingressResources
      ];

      const result: DeploymentResult = {
        id: deploymentId,
        status: 'running',
        url: endpoints.length > 0 ? endpoints[0].url : undefined,
        endpoints,
        resources: allResources,
        metrics: await this.collectInitialMetrics(request.applicationName, namespace),
        logs: [],
        startedAt: startTime,
        completedAt: new Date(),
        duration: Date.now() - startTime.getTime()
      };

      this.logger.info('Déploiement Kubernetes réussi', { 
        deploymentId,
        url: result.url,
        duration: result.duration 
      });

      return result;

    } catch (error) {
      this.logger.error('Erreur lors du déploiement Kubernetes', { 
        deploymentId,
        error: error.message 
      });

      // Nettoyer en cas d'erreur
      await this.cleanup(request.applicationName, namespace);

      throw error;
    }
  }

  /**
   * Effectue un rollback d'un déploiement
   */
  async rollback(deploymentName: string, namespace: string, revision?: string): Promise<void> {
    this.logger.info('Rollback Kubernetes', { deploymentName, namespace, revision });

    try {
      // Utiliser kubectl rollout undo
      const command = revision 
        ? `kubectl rollout undo deployment/${deploymentName} --to-revision=${revision} -n ${namespace}`
        : `kubectl rollout undo deployment/${deploymentName} -n ${namespace}`;

      await this.executeCommand(command);

      // Attendre que le rollback soit terminé
      await this.waitForRolloutComplete(deploymentName, namespace);

      this.logger.info('Rollback Kubernetes terminé', { deploymentName, namespace });

    } catch (error) {
      this.logger.error('Erreur lors du rollback Kubernetes', { 
        deploymentName,
        namespace,
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Met à l'échelle un déploiement
   */
  async scale(deploymentName: string, namespace: string, replicas: number): Promise<void> {
    this.logger.info('Mise à l\'échelle Kubernetes', { 
      deploymentName,
      namespace,
      replicas 
    });

    try {
      // Mettre à jour le nombre de replicas
      const deployment = await this.appsV1Api.readNamespacedDeployment(deploymentName, namespace);
      deployment.body.spec!.replicas = replicas;

      await this.appsV1Api.patchNamespacedDeployment(
        deploymentName,
        namespace,
        deployment.body,
        undefined,
        undefined,
        undefined,
        undefined,
        { headers: { 'Content-Type': 'application/merge-patch+json' } }
      );

      // Attendre que la mise à l'échelle soit terminée
      await this.waitForScalingComplete(deploymentName, namespace, replicas);

      this.logger.info('Mise à l\'échelle Kubernetes terminée', { 
        deploymentName,
        namespace,
        replicas 
      });

    } catch (error) {
      this.logger.error('Erreur lors de la mise à l\'échelle Kubernetes', { 
        deploymentName,
        namespace,
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Supprime un déploiement
   */
  async delete(deploymentName: string, namespace: string): Promise<void> {
    this.logger.info('Suppression du déploiement Kubernetes', { deploymentName, namespace });

    try {
      await this.cleanup(deploymentName, namespace);
      this.logger.info('Déploiement Kubernetes supprimé', { deploymentName, namespace });
    } catch (error) {
      this.logger.error('Erreur lors de la suppression Kubernetes', { 
        deploymentName,
        namespace,
        error: error.message 
      });
      throw error;
    }
  }

  // Méthodes privées

  private async ensureNamespace(namespace: string): Promise<void> {
    try {
      await this.coreV1Api.readNamespace(namespace);
      this.logger.info('Namespace existe déjà', { namespace });
    } catch (error) {
      if (error.response?.statusCode === 404) {
        this.logger.info('Création du namespace', { namespace });
        await this.coreV1Api.createNamespace({
          metadata: {
            name: namespace,
            labels: {
              'managed-by': 'agent-devops',
              'created-at': new Date().toISOString()
            }
          }
        });
      } else {
        throw error;
      }
    }
  }

  private async generateManifests(request: DeploymentRequest, buildResult: any): Promise<KubernetesManifest[]> {
    const manifests: KubernetesManifest[] = [];
    const appName = request.applicationName;
    const labels = {
      app: appName,
      version: request.version,
      'managed-by': 'agent-devops'
    };

    // ConfigMap pour la configuration
    if (Object.keys(request.configuration.environment).length > 0) {
      manifests.push({
        apiVersion: 'v1',
        kind: 'ConfigMap',
        metadata: {
          name: `${appName}-config`,
          labels
        },
        spec: {
          data: request.configuration.environment
        }
      });
    }

    // Secret pour les données sensibles
    if (request.configuration.secrets && Object.keys(request.configuration.secrets).length > 0) {
      manifests.push({
        apiVersion: 'v1',
        kind: 'Secret',
        metadata: {
          name: `${appName}-secrets`,
          labels
        },
        spec: {
          type: 'Opaque',
          data: this.encodeSecrets(request.configuration.secrets)
        }
      });
    }

    // Deployment
    manifests.push({
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: appName,
        labels
      },
      spec: {
        replicas: request.configuration.replicas || 1,
        selector: {
          matchLabels: { app: appName }
        },
        template: {
          metadata: {
            labels
          },
          spec: {
            containers: [{
              name: appName,
              image: buildResult.image || `${appName}:${request.version}`,
              ports: request.configuration.networking?.ports.map(p => ({
                containerPort: p.targetPort,
                name: p.name
              })) || [{ containerPort: 3000, name: 'http' }],
              env: this.generateEnvVars(request),
              resources: request.configuration.resources ? {
                requests: {
                  cpu: request.configuration.resources.cpu.request,
                  memory: request.configuration.resources.memory.request
                },
                limits: {
                  cpu: request.configuration.resources.cpu.limit,
                  memory: request.configuration.resources.memory.limit
                }
              } : undefined,
              livenessProbe: request.configuration.monitoring?.healthChecks?.liveness ? {
                httpGet: {
                  path: request.configuration.monitoring.healthChecks.liveness.path,
                  port: request.configuration.monitoring.healthChecks.liveness.port
                },
                initialDelaySeconds: request.configuration.monitoring.healthChecks.liveness.initialDelaySeconds,
                periodSeconds: request.configuration.monitoring.healthChecks.liveness.periodSeconds
              } : undefined,
              readinessProbe: request.configuration.monitoring?.healthChecks?.readiness ? {
                httpGet: {
                  path: request.configuration.monitoring.healthChecks.readiness.path,
                  port: request.configuration.monitoring.healthChecks.readiness.port
                },
                initialDelaySeconds: request.configuration.monitoring.healthChecks.readiness.initialDelaySeconds,
                periodSeconds: request.configuration.monitoring.healthChecks.readiness.periodSeconds
              } : undefined
            }]
          }
        }
      }
    });

    // Service
    if (request.configuration.networking?.ports) {
      manifests.push({
        apiVersion: 'v1',
        kind: 'Service',
        metadata: {
          name: `${appName}-service`,
          labels
        },
        spec: {
          selector: { app: appName },
          ports: request.configuration.networking.ports.map(p => ({
            name: p.name,
            port: p.port,
            targetPort: p.targetPort,
            protocol: p.protocol
          })),
          type: request.configuration.networking.service?.type || 'ClusterIP'
        }
      });
    }

    // Ingress
    if (request.configuration.networking?.ingress?.enabled) {
      manifests.push({
        apiVersion: 'networking.k8s.io/v1',
        kind: 'Ingress',
        metadata: {
          name: `${appName}-ingress`,
          labels,
          annotations: request.configuration.networking.ingress.annotations || {}
        },
        spec: {
          rules: [{
            host: request.configuration.networking.ingress.hostname,
            http: {
              paths: [{
                path: request.configuration.networking.ingress.path || '/',
                pathType: 'Prefix',
                backend: {
                  service: {
                    name: `${appName}-service`,
                    port: {
                      number: request.configuration.networking.ports[0].port
                    }
                  }
                }
              }]
            }
          }],
          tls: request.configuration.networking.ingress.tls ? [{
            hosts: [request.configuration.networking.ingress.hostname],
            secretName: `${appName}-tls`
          }] : undefined
        }
      });
    }

    return manifests;
  }

  private async applyConfigResources(manifests: KubernetesManifest[], namespace: string): Promise<DeployedResource[]> {
    const resources: DeployedResource[] = [];

    for (const manifest of manifests) {
      if (manifest.kind === 'ConfigMap') {
        try {
          await this.coreV1Api.createNamespacedConfigMap(namespace, manifest as any);
          resources.push({
            type: 'configmap',
            name: manifest.metadata.name,
            namespace,
            status: 'created',
            created: new Date()
          });
        } catch (error) {
          if (error.response?.statusCode === 409) {
            // ConfigMap existe déjà, le mettre à jour
            await this.coreV1Api.patchNamespacedConfigMap(
              manifest.metadata.name,
              namespace,
              manifest as any
            );
            resources.push({
              type: 'configmap',
              name: manifest.metadata.name,
              namespace,
              status: 'updated',
              created: new Date()
            });
          } else {
            throw error;
          }
        }
      } else if (manifest.kind === 'Secret') {
        try {
          await this.coreV1Api.createNamespacedSecret(namespace, manifest as any);
          resources.push({
            type: 'secret',
            name: manifest.metadata.name,
            namespace,
            status: 'created',
            created: new Date()
          });
        } catch (error) {
          if (error.response?.statusCode === 409) {
            // Secret existe déjà, le mettre à jour
            await this.coreV1Api.patchNamespacedSecret(
              manifest.metadata.name,
              namespace,
              manifest as any
            );
            resources.push({
              type: 'secret',
              name: manifest.metadata.name,
              namespace,
              status: 'updated',
              created: new Date()
            });
          } else {
            throw error;
          }
        }
      }
    }

    return resources;
  }

  private async applyServices(manifests: KubernetesManifest[], namespace: string): Promise<DeployedResource[]> {
    const resources: DeployedResource[] = [];

    for (const manifest of manifests) {
      if (manifest.kind === 'Service') {
        try {
          await this.coreV1Api.createNamespacedService(namespace, manifest as any);
          resources.push({
            type: 'service',
            name: manifest.metadata.name,
            namespace,
            status: 'created',
            created: new Date()
          });
        } catch (error) {
          if (error.response?.statusCode === 409) {
            // Service existe déjà, le mettre à jour
            await this.coreV1Api.patchNamespacedService(
              manifest.metadata.name,
              namespace,
              manifest as any
            );
            resources.push({
              type: 'service',
              name: manifest.metadata.name,
              namespace,
              status: 'updated',
              created: new Date()
            });
          } else {
            throw error;
          }
        }
      }
    }

    return resources;
  }

  private async applyDeployments(manifests: KubernetesManifest[], namespace: string): Promise<DeployedResource[]> {
    const resources: DeployedResource[] = [];

    for (const manifest of manifests) {
      if (manifest.kind === 'Deployment') {
        try {
          await this.appsV1Api.createNamespacedDeployment(namespace, manifest as any);
          resources.push({
            type: 'deployment',
            name: manifest.metadata.name,
            namespace,
            status: 'created',
            created: new Date()
          });
        } catch (error) {
          if (error.response?.statusCode === 409) {
            // Deployment existe déjà, le mettre à jour
            await this.appsV1Api.patchNamespacedDeployment(
              manifest.metadata.name,
              namespace,
              manifest as any
            );
            resources.push({
              type: 'deployment',
              name: manifest.metadata.name,
              namespace,
              status: 'updated',
              created: new Date()
            });
          } else {
            throw error;
          }
        }
      }
    }

    return resources;
  }

  private async applyIngress(manifests: KubernetesManifest[], namespace: string): Promise<DeployedResource[]> {
    const resources: DeployedResource[] = [];

    for (const manifest of manifests) {
      if (manifest.kind === 'Ingress') {
        try {
          await this.networkingV1Api.createNamespacedIngress(namespace, manifest as any);
          resources.push({
            type: 'ingress',
            name: manifest.metadata.name,
            namespace,
            status: 'created',
            created: new Date()
          });
        } catch (error) {
          if (error.response?.statusCode === 409) {
            // Ingress existe déjà, le mettre à jour
            await this.networkingV1Api.patchNamespacedIngress(
              manifest.metadata.name,
              namespace,
              manifest as any
            );
            resources.push({
              type: 'ingress',
              name: manifest.metadata.name,
              namespace,
              status: 'updated',
              created: new Date()
            });
          } else {
            throw error;
          }
        }
      }
    }

    return resources;
  }

  private async waitForDeploymentReady(deploymentName: string, namespace: string): Promise<void> {
    const maxWaitTime = 300000; // 5 minutes
    const checkInterval = 5000; // 5 secondes
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const deployment = await this.appsV1Api.readNamespacedDeployment(deploymentName, namespace);
        const status = deployment.body.status;

        if (status?.readyReplicas === status?.replicas && status?.replicas > 0) {
          this.logger.info('Déploiement prêt', { deploymentName, namespace });
          return;
        }

        this.logger.info('Attente du déploiement', { 
          deploymentName,
          namespace,
          ready: status?.readyReplicas || 0,
          desired: status?.replicas || 0
        });

        await new Promise(resolve => setTimeout(resolve, checkInterval));
      } catch (error) {
        this.logger.warn('Erreur lors de la vérification du déploiement', { 
          deploymentName,
          namespace,
          error: error.message 
        });
        await new Promise(resolve => setTimeout(resolve, checkInterval));
      }
    }

    throw new Error(`Timeout: Le déploiement ${deploymentName} n'est pas prêt après ${maxWaitTime}ms`);
  }

  private async getEndpoints(appName: string, namespace: string): Promise<any[]> {
    const endpoints = [];

    try {
      // Récupérer les services
      const services = await this.coreV1Api.listNamespacedService(
        namespace,
        undefined,
        undefined,
        undefined,
        undefined,
        `app=${appName}`
      );

      for (const service of services.body.items) {
        if (service.spec?.type === 'LoadBalancer' && service.status?.loadBalancer?.ingress) {
          for (const ingress of service.status.loadBalancer.ingress) {
            endpoints.push({
              name: service.metadata?.name,
              url: `http://${ingress.ip || ingress.hostname}`,
              type: 'web',
              status: 'healthy'
            });
          }
        }
      }

      // Récupérer les ingress
      const ingresses = await this.networkingV1Api.listNamespacedIngress(
        namespace,
        undefined,
        undefined,
        undefined,
        undefined,
        `app=${appName}`
      );

      for (const ingress of ingresses.body.items) {
        if (ingress.spec?.rules) {
          for (const rule of ingress.spec.rules) {
            if (rule.host) {
              endpoints.push({
                name: ingress.metadata?.name,
                url: `https://${rule.host}`,
                type: 'web',
                status: 'healthy'
              });
            }
          }
        }
      }
    } catch (error) {
      this.logger.warn('Erreur lors de la récupération des endpoints', { 
        appName,
        namespace,
        error: error.message 
      });
    }

    return endpoints;
  }

  private async collectInitialMetrics(appName: string, namespace: string): Promise<any> {
    // Collecter les métriques initiales
    return {
      cpu: { current: 0, average: 0, peak: 0, unit: 'cores' },
      memory: { current: 0, average: 0, peak: 0, unit: 'MB' },
      network: { bytesIn: 0, bytesOut: 0, connectionsActive: 0, connectionsTotal: 0 },
      requests: { total: 0, rate: 0, latencyP50: 0, latencyP95: 0, latencyP99: 0 },
      errors: { total: 0, rate: 0, types: {} }
    };
  }

  private async cleanup(appName: string, namespace: string): Promise<void> {
    this.logger.info('Nettoyage des ressources Kubernetes', { appName, namespace });

    try {
      // Supprimer le déploiement
      await this.appsV1Api.deleteNamespacedDeployment(appName, namespace);
      
      // Supprimer le service
      await this.coreV1Api.deleteNamespacedService(`${appName}-service`, namespace);
      
      // Supprimer l'ingress
      await this.networkingV1Api.deleteNamespacedIngress(`${appName}-ingress`, namespace);
      
      // Supprimer les ConfigMaps et Secrets
      await this.coreV1Api.deleteNamespacedConfigMap(`${appName}-config`, namespace);
      await this.coreV1Api.deleteNamespacedSecret(`${appName}-secrets`, namespace);
      
    } catch (error) {
      this.logger.warn('Erreur lors du nettoyage', { 
        appName,
        namespace,
        error: error.message 
      });
    }
  }

  private encodeSecrets(secrets: Record<string, string>): Record<string, string> {
    const encoded: Record<string, string> = {};
    for (const [key, value] of Object.entries(secrets)) {
      encoded[key] = Buffer.from(value).toString('base64');
    }
    return encoded;
  }

  private generateEnvVars(request: DeploymentRequest): any[] {
    const envVars = [];

    // Variables d'environnement depuis ConfigMap
    if (Object.keys(request.configuration.environment).length > 0) {
      for (const key of Object.keys(request.configuration.environment)) {
        envVars.push({
          name: key,
          valueFrom: {
            configMapKeyRef: {
              name: `${request.applicationName}-config`,
              key
            }
          }
        });
      }
    }

    // Variables d'environnement depuis Secret
    if (request.configuration.secrets && Object.keys(request.configuration.secrets).length > 0) {
      for (const key of Object.keys(request.configuration.secrets)) {
        envVars.push({
          name: key,
          valueFrom: {
            secretKeyRef: {
              name: `${request.applicationName}-secrets`,
              key
            }
          }
        });
      }
    }

    return envVars;
  }

  private async executeCommand(command: string): Promise<string> {
    // Exécuter une commande shell (simulation)
    this.logger.info('Exécution de la commande', { command });
    return 'success';
  }

  private async waitForRolloutComplete(deploymentName: string, namespace: string): Promise<void> {
    // Attendre que le rollout soit terminé
    await this.waitForDeploymentReady(deploymentName, namespace);
  }

  private async waitForScalingComplete(deploymentName: string, namespace: string, expectedReplicas: number): Promise<void> {
    const maxWaitTime = 300000; // 5 minutes
    const checkInterval = 5000; // 5 secondes
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const deployment = await this.appsV1Api.readNamespacedDeployment(deploymentName, namespace);
        const status = deployment.body.status;

        if (status?.readyReplicas === expectedReplicas && status?.replicas === expectedReplicas) {
          this.logger.info('Mise à l\'échelle terminée', { 
            deploymentName,
            namespace,
            replicas: expectedReplicas 
          });
          return;
        }

        await new Promise(resolve => setTimeout(resolve, checkInterval));
      } catch (error) {
        this.logger.warn('Erreur lors de la vérification de la mise à l\'échelle', { 
          deploymentName,
          namespace,
          error: error.message 
        });
        await new Promise(resolve => setTimeout(resolve, checkInterval));
      }
    }

    throw new Error(`Timeout: La mise à l'échelle de ${deploymentName} n'est pas terminée après ${maxWaitTime}ms`);
  }
}
