import weaviate, { WeaviateClient } from 'weaviate-ts-client';
import { <PERSON>gger } from 'winston';
import { DeploymentResult, InfrastructureRequest, Pipeline } from '../types';

/**
 * Système de Mémoire Weaviate pour l'Agent DevOps
 * 
 * Stocke et récupère les déploiements, configurations d'infrastructure,
 * et patterns DevOps pour l'apprentissage et la réutilisation.
 */
export class WeaviateMemory {
  private client: WeaviateClient;
  private logger: Logger;
  private isConnected: boolean = false;

  // Collections Weaviate
  private readonly collections = {
    Deployment: 'Deployment',
    Infrastructure: 'Infrastructure',
    Pipeline: 'Pipeline',
    Configuration: 'Configuration',
    Metric: 'Metric',
    Alert: 'Alert'
  };

  constructor(logger: Logger, weaviateUrl: string = 'http://weaviate:8080') {
    this.logger = logger;
    this.client = weaviate.client({
      scheme: 'http',
      host: weaviateUrl.replace('http://', '').replace('https://', ''),
    });
    
    this.initializeConnection();
  }

  /**
   * Initialise la connexion à Weaviate
   */
  private async initializeConnection(): Promise<void> {
    try {
      this.logger.info('Initialisation de la connexion Weaviate DevOps');
      
      const isReady = await this.client.misc.readyChecker().do();
      if (isReady) {
        this.isConnected = true;
        await this.ensureSchemas();
        this.logger.info('Connexion Weaviate DevOps établie avec succès');
      } else {
        throw new Error('Weaviate n\'est pas prêt');
      }
    } catch (error) {
      this.logger.error('Erreur lors de la connexion à Weaviate DevOps', { error: error.message });
      this.isConnected = false;
    }
  }

  /**
   * S'assure que les schémas existent
   */
  private async ensureSchemas(): Promise<void> {
    try {
      // Schéma pour les déploiements
      await this.createSchemaIfNotExists(this.collections.Deployment, {
        class: this.collections.Deployment,
        description: 'Déploiements effectués par l\'agent DevOps',
        properties: [
          {
            name: 'applicationName',
            dataType: ['text'],
            description: 'Nom de l\'application déployée'
          },
          {
            name: 'platform',
            dataType: ['text'],
            description: 'Plateforme de déploiement'
          },
          {
            name: 'environment',
            dataType: ['text'],
            description: 'Environnement de déploiement'
          },
          {
            name: 'status',
            dataType: ['text'],
            description: 'Statut du déploiement'
          },
          {
            name: 'url',
            dataType: ['text'],
            description: 'URL du déploiement'
          },
          {
            name: 'version',
            dataType: ['text'],
            description: 'Version déployée'
          },
          {
            name: 'configuration',
            dataType: ['text'],
            description: 'Configuration du déploiement (JSON)'
          },
          {
            name: 'metrics',
            dataType: ['text'],
            description: 'Métriques du déploiement (JSON)'
          },
          {
            name: 'deployedAt',
            dataType: ['date'],
            description: 'Date de déploiement'
          },
          {
            name: 'duration',
            dataType: ['number'],
            description: 'Durée du déploiement en ms'
          },
          {
            name: 'success',
            dataType: ['boolean'],
            description: 'Succès du déploiement'
          }
        ]
      });

      // Schéma pour l'infrastructure
      await this.createSchemaIfNotExists(this.collections.Infrastructure, {
        class: this.collections.Infrastructure,
        description: 'Infrastructure gérée par l\'agent DevOps',
        properties: [
          {
            name: 'provider',
            dataType: ['text'],
            description: 'Fournisseur cloud (aws, gcp, azure, kubernetes)'
          },
          {
            name: 'region',
            dataType: ['text'],
            description: 'Région de déploiement'
          },
          {
            name: 'environment',
            dataType: ['text'],
            description: 'Environnement'
          },
          {
            name: 'resources',
            dataType: ['text'],
            description: 'Ressources créées (JSON)'
          },
          {
            name: 'configuration',
            dataType: ['text'],
            description: 'Configuration Terraform/K8s (JSON)'
          },
          {
            name: 'state',
            dataType: ['text'],
            description: 'État de l\'infrastructure'
          },
          {
            name: 'cost',
            dataType: ['number'],
            description: 'Coût estimé'
          },
          {
            name: 'createdAt',
            dataType: ['date'],
            description: 'Date de création'
          },
          {
            name: 'tags',
            dataType: ['text[]'],
            description: 'Tags pour la recherche'
          }
        ]
      });

      // Schéma pour les pipelines
      await this.createSchemaIfNotExists(this.collections.Pipeline, {
        class: this.collections.Pipeline,
        description: 'Pipelines CI/CD',
        properties: [
          {
            name: 'name',
            dataType: ['text'],
            description: 'Nom du pipeline'
          },
          {
            name: 'type',
            dataType: ['text'],
            description: 'Type de pipeline'
          },
          {
            name: 'stages',
            dataType: ['text'],
            description: 'Étapes du pipeline (JSON)'
          },
          {
            name: 'triggers',
            dataType: ['text'],
            description: 'Déclencheurs (JSON)'
          },
          {
            name: 'configuration',
            dataType: ['text'],
            description: 'Configuration du pipeline (JSON)'
          },
          {
            name: 'lastRun',
            dataType: ['date'],
            description: 'Dernière exécution'
          },
          {
            name: 'successRate',
            dataType: ['number'],
            description: 'Taux de succès'
          },
          {
            name: 'averageDuration',
            dataType: ['number'],
            description: 'Durée moyenne en ms'
          }
        ]
      });

      // Schéma pour les métriques
      await this.createSchemaIfNotExists(this.collections.Metric, {
        class: this.collections.Metric,
        description: 'Métriques de monitoring',
        properties: [
          {
            name: 'deploymentId',
            dataType: ['text'],
            description: 'ID du déploiement'
          },
          {
            name: 'metricType',
            dataType: ['text'],
            description: 'Type de métrique'
          },
          {
            name: 'value',
            dataType: ['number'],
            description: 'Valeur de la métrique'
          },
          {
            name: 'unit',
            dataType: ['text'],
            description: 'Unité de mesure'
          },
          {
            name: 'timestamp',
            dataType: ['date'],
            description: 'Horodatage'
          },
          {
            name: 'labels',
            dataType: ['text'],
            description: 'Labels additionnels (JSON)'
          }
        ]
      });

      this.logger.info('Schémas Weaviate DevOps initialisés');
    } catch (error) {
      this.logger.error('Erreur lors de la création des schémas DevOps', { error: error.message });
    }
  }

  /**
   * Stocke un déploiement
   */
  async storeDeployment(deployment: DeploymentResult): Promise<string> {
    if (!this.isConnected) {
      throw new Error('Weaviate non connecté');
    }

    try {
      this.logger.info('Stockage du déploiement', { id: deployment.id });

      const data = {
        applicationName: deployment.id.split('-')[0] || 'unknown',
        platform: 'unknown', // À extraire des métadonnées
        environment: 'unknown', // À extraire des métadonnées
        status: deployment.status,
        url: deployment.url || '',
        version: '1.0.0', // À extraire des métadonnées
        configuration: JSON.stringify(deployment.resources),
        metrics: JSON.stringify(deployment.metrics),
        deployedAt: deployment.completedAt?.toISOString() || new Date().toISOString(),
        duration: deployment.duration || 0,
        success: deployment.status === 'running'
      };

      const result = await this.client.data
        .creator()
        .withClassName(this.collections.Deployment)
        .withProperties(data)
        .do();

      this.logger.info('Déploiement stocké avec succès', { id: result.id });
      return result.id;

    } catch (error) {
      this.logger.error('Erreur lors du stockage du déploiement', { error: error.message });
      throw error;
    }
  }

  /**
   * Stocke une infrastructure
   */
  async storeInfrastructure(infrastructure: any): Promise<string> {
    if (!this.isConnected) {
      throw new Error('Weaviate non connecté');
    }

    try {
      this.logger.info('Stockage de l\'infrastructure', { id: infrastructure.id });

      const data = {
        provider: infrastructure.provider,
        region: infrastructure.region || 'unknown',
        environment: infrastructure.environment || 'unknown',
        resources: JSON.stringify(infrastructure.resources || []),
        configuration: JSON.stringify(infrastructure.configuration || {}),
        state: infrastructure.status || 'unknown',
        cost: infrastructure.cost || 0,
        createdAt: new Date().toISOString(),
        tags: infrastructure.tags || []
      };

      const result = await this.client.data
        .creator()
        .withClassName(this.collections.Infrastructure)
        .withProperties(data)
        .do();

      this.logger.info('Infrastructure stockée avec succès', { id: result.id });
      return result.id;

    } catch (error) {
      this.logger.error('Erreur lors du stockage de l\'infrastructure', { error: error.message });
      throw error;
    }
  }

  /**
   * Stocke un pipeline
   */
  async storePipeline(pipeline: Pipeline): Promise<string> {
    if (!this.isConnected) {
      throw new Error('Weaviate non connecté');
    }

    try {
      this.logger.info('Stockage du pipeline', { id: pipeline.id });

      const data = {
        name: pipeline.name,
        type: pipeline.type,
        stages: JSON.stringify(pipeline.stages),
        triggers: JSON.stringify(pipeline.triggers),
        configuration: JSON.stringify(pipeline.configuration),
        lastRun: new Date().toISOString(),
        successRate: 1.0, // À calculer
        averageDuration: 0 // À calculer
      };

      const result = await this.client.data
        .creator()
        .withClassName(this.collections.Pipeline)
        .withProperties(data)
        .do();

      this.logger.info('Pipeline stocké avec succès', { id: result.id });
      return result.id;

    } catch (error) {
      this.logger.error('Erreur lors du stockage du pipeline', { error: error.message });
      throw error;
    }
  }

  /**
   * Stocke des métriques
   */
  async storeMetrics(deploymentId: string, metrics: any): Promise<void> {
    if (!this.isConnected) {
      return;
    }

    try {
      const timestamp = new Date().toISOString();

      // Stocker chaque type de métrique
      for (const [metricType, metricData] of Object.entries(metrics)) {
        if (typeof metricData === 'object' && metricData !== null) {
          for (const [key, value] of Object.entries(metricData as any)) {
            if (typeof value === 'number') {
              await this.client.data
                .creator()
                .withClassName(this.collections.Metric)
                .withProperties({
                  deploymentId,
                  metricType: `${metricType}.${key}`,
                  value,
                  unit: this.getMetricUnit(metricType, key),
                  timestamp,
                  labels: JSON.stringify({ type: metricType, metric: key })
                })
                .do();
            }
          }
        }
      }

      this.logger.debug('Métriques stockées', { deploymentId });

    } catch (error) {
      this.logger.error('Erreur lors du stockage des métriques', { 
        deploymentId,
        error: error.message 
      });
    }
  }

  /**
   * Recherche des déploiements similaires
   */
  async findSimilarDeployments(
    platform: string,
    environment: string,
    limit: number = 5
  ): Promise<any[]> {
    if (!this.isConnected) {
      return [];
    }

    try {
      this.logger.info('Recherche de déploiements similaires', { platform, environment });

      const result = await this.client.graphql
        .get()
        .withClassName(this.collections.Deployment)
        .withFields('applicationName platform environment status url version deployedAt duration success')
        .withWhere({
          operator: 'And',
          operands: [
            {
              path: ['platform'],
              operator: 'Equal',
              valueText: platform
            },
            {
              path: ['environment'],
              operator: 'Equal',
              valueText: environment
            },
            {
              path: ['success'],
              operator: 'Equal',
              valueBoolean: true
            }
          ]
        })
        .withLimit(limit)
        .do();

      return result.data.Get[this.collections.Deployment] || [];

    } catch (error) {
      this.logger.error('Erreur lors de la recherche de déploiements', { error: error.message });
      return [];
    }
  }

  /**
   * Obtient les métriques d'un déploiement
   */
  async getDeploymentMetrics(deploymentId: string, timeRange?: string): Promise<any[]> {
    if (!this.isConnected) {
      return [];
    }

    try {
      let whereClause: any = {
        path: ['deploymentId'],
        operator: 'Equal',
        valueText: deploymentId
      };

      if (timeRange) {
        const since = new Date(Date.now() - this.parseTimeRange(timeRange));
        whereClause = {
          operator: 'And',
          operands: [
            whereClause,
            {
              path: ['timestamp'],
              operator: 'GreaterThan',
              valueDate: since.toISOString()
            }
          ]
        };
      }

      const result = await this.client.graphql
        .get()
        .withClassName(this.collections.Metric)
        .withFields('metricType value unit timestamp labels')
        .withWhere(whereClause)
        .withSort([{ path: ['timestamp'], order: 'desc' }])
        .withLimit(1000)
        .do();

      return result.data.Get[this.collections.Metric] || [];

    } catch (error) {
      this.logger.error('Erreur lors de la récupération des métriques', { 
        deploymentId,
        error: error.message 
      });
      return [];
    }
  }

  /**
   * Obtient les statistiques de déploiement
   */
  async getDeploymentStats(platform?: string, environment?: string): Promise<any> {
    if (!this.isConnected) {
      return { connected: false };
    }

    try {
      let whereClause: any = null;

      if (platform && environment) {
        whereClause = {
          operator: 'And',
          operands: [
            {
              path: ['platform'],
              operator: 'Equal',
              valueText: platform
            },
            {
              path: ['environment'],
              operator: 'Equal',
              valueText: environment
            }
          ]
        };
      } else if (platform) {
        whereClause = {
          path: ['platform'],
          operator: 'Equal',
          valueText: platform
        };
      }

      let query = this.client.graphql
        .aggregate()
        .withClassName(this.collections.Deployment)
        .withFields('meta { count } success { count }');

      if (whereClause) {
        query = query.withWhere(whereClause);
      }

      const result = await query.do();
      const aggregation = result.data.Aggregate[this.collections.Deployment]?.[0];

      return {
        connected: true,
        totalDeployments: aggregation?.meta?.count || 0,
        successfulDeployments: aggregation?.success?.count || 0,
        successRate: aggregation?.meta?.count > 0 
          ? (aggregation.success.count / aggregation.meta.count) 
          : 0
      };

    } catch (error) {
      this.logger.error('Erreur lors de la récupération des stats', { error: error.message });
      return { connected: false, error: error.message };
    }
  }

  // Méthodes utilitaires

  private async createSchemaIfNotExists(className: string, schema: any): Promise<void> {
    try {
      const exists = await this.client.schema.exists(className);
      if (!exists) {
        await this.client.schema.classCreator().withClass(schema).do();
        this.logger.info(`Schéma ${className} créé`);
      }
    } catch (error) {
      this.logger.warn(`Erreur lors de la création du schéma ${className}`, { error: error.message });
    }
  }

  private getMetricUnit(metricType: string, key: string): string {
    const units: Record<string, Record<string, string>> = {
      cpu: { current: 'cores', average: 'cores', peak: 'cores' },
      memory: { current: 'MB', average: 'MB', peak: 'MB' },
      network: { bytesIn: 'bytes', bytesOut: 'bytes', connectionsActive: 'count', connectionsTotal: 'count' },
      requests: { total: 'count', rate: 'req/s', latencyP50: 'ms', latencyP95: 'ms', latencyP99: 'ms' },
      errors: { total: 'count', rate: 'err/s' }
    };

    return units[metricType]?.[key] || 'unknown';
  }

  private parseTimeRange(timeRange: string): number {
    const units: Record<string, number> = {
      'm': 60 * 1000,
      'h': 60 * 60 * 1000,
      'd': 24 * 60 * 60 * 1000
    };

    const match = timeRange.match(/^(\d+)([mhd])$/);
    if (match) {
      const value = parseInt(match[1]);
      const unit = match[2];
      return value * units[unit];
    }

    return 60 * 60 * 1000; // 1 heure par défaut
  }

  /**
   * Vérifie l'état de la connexion
   */
  isConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Ferme la connexion
   */
  async close(): Promise<void> {
    this.isConnected = false;
    this.logger.info('Connexion Weaviate DevOps fermée');
  }
}
