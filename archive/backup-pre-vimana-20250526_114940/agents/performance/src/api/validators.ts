import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

/**
 * Schéma de validation pour les requêtes de benchmark
 */
const benchmarkRequestSchema = Joi.object({
  id: Joi.string().optional(),
  type: Joi.string().valid('lighthouse', 'load', 'profiling', 'full').required(),
  target: Joi.object({
    url: Joi.string().uri().when('type', {
      is: Joi.string().valid('lighthouse', 'load'),
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    service: Joi.string().when('type', {
      is: 'profiling',
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    endpoint: Joi.string().optional()
  }).required(),
  configuration: Joi.object({
    lighthouse: Joi.object({
      categories: Joi.array().items(Joi.string()).optional(),
      formFactor: Joi.string().valid('mobile', 'desktop').optional(),
      throttling: Joi.string().optional(),
      emulatedUserAgent: Joi.string().optional(),
      locale: Joi.string().optional()
    }).optional(),
    loadTesting: Joi.object({
      duration: Joi.string().optional(),
      connections: Joi.number().integer().min(1).max(1000).optional(),
      rps: Joi.number().integer().min(1).optional(),
      headers: Joi.object().optional(),
      body: Joi.string().optional(),
      method: Joi.string().valid('GET', 'POST', 'PUT', 'DELETE').optional()
    }).optional(),
    profiling: Joi.object({
      duration: Joi.number().integer().min(10).max(3600).optional(),
      sampleRate: Joi.number().min(0.1).max(1).optional(),
      includeMemory: Joi.boolean().optional(),
      includeCpu: Joi.boolean().optional()
    }).optional()
  }).optional(),
  metadata: Joi.object().optional()
});

/**
 * Schéma de validation pour les requêtes d'optimisation
 */
const optimizationRequestSchema = Joi.object({
  id: Joi.string().optional(),
  target: Joi.object({
    codebase: Joi.string().optional(),
    service: Joi.string().optional(),
    architecture: Joi.string().optional()
  }).required(),
  scope: Joi.array().items(Joi.string()).min(1).required(),
  constraints: Joi.object({
    budget: Joi.number().min(0).optional(),
    timeline: Joi.string().valid('urgent', 'normal', 'flexible').optional(),
    technologies: Joi.array().items(Joi.string()).optional()
  }).optional(),
  goals: Joi.object({
    performance: Joi.number().min(0).max(100).optional(),
    scalability: Joi.number().min(0).max(100).optional(),
    maintainability: Joi.number().min(0).max(100).optional()
  }).optional()
});

/**
 * Schéma de validation pour les requêtes de monitoring
 */
const monitoringRequestSchema = Joi.object({
  id: Joi.string().optional(),
  targets: Joi.array().items(Joi.string()).min(1).required(),
  metrics: Joi.array().items(Joi.string()).min(1).required(),
  thresholds: Joi.object().pattern(
    Joi.string(),
    Joi.number().min(0)
  ).required(),
  duration: Joi.string().optional(),
  interval: Joi.number().integer().min(1000).optional() // minimum 1 seconde
});

/**
 * Schéma de validation pour les requêtes d'analyse
 */
const analysisRequestSchema = Joi.object({
  id: Joi.string().optional(),
  type: Joi.string().valid('code', 'architecture', 'infrastructure').required(),
  target: Joi.string().required(),
  depth: Joi.string().valid('shallow', 'deep', 'comprehensive').optional(),
  focus: Joi.array().items(Joi.string()).optional()
});

/**
 * Middleware de validation générique
 */
function validateSchema(schema: Joi.ObjectSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      return res.status(400).json({
        error: 'Données de requête invalides',
        details: errorDetails
      });
    }

    // Remplacer req.body par les données validées et nettoyées
    req.body = value;
    next();
  };
}

/**
 * Validation des requêtes de benchmark
 */
export const validateBenchmarkRequest = validateSchema(benchmarkRequestSchema);

/**
 * Validation des requêtes d'optimisation
 */
export const validateOptimizationRequest = validateSchema(optimizationRequestSchema);

/**
 * Validation des requêtes de monitoring
 */
export const validateMonitoringRequest = validateSchema(monitoringRequestSchema);

/**
 * Validation des requêtes d'analyse
 */
export const validateAnalysisRequest = validateSchema(analysisRequestSchema);

/**
 * Validation des paramètres de requête pour les rapports
 */
export const validateReportsQuery = (req: Request, res: Response, next: NextFunction) => {
  const schema = Joi.object({
    type: Joi.string().valid('benchmarks', 'optimizations', 'analyses', 'all').optional(),
    limit: Joi.number().integer().min(1).max(100).optional(),
    offset: Joi.number().integer().min(0).optional(),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    status: Joi.string().valid('success', 'error', 'timeout', 'all').optional()
  });

  const { error, value } = schema.validate(req.query, {
    abortEarly: false,
    stripUnknown: true
  });

  if (error) {
    const errorDetails = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));

    return res.status(400).json({
      error: 'Paramètres de requête invalides',
      details: errorDetails
    });
  }

  req.query = value;
  next();
};

/**
 * Validation des en-têtes d'authentification (si nécessaire)
 */
export const validateAuthHeaders = (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  
  // Si l'authentification est requise
  if (process.env.REQUIRE_AUTH === 'true') {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Token d\'authentification requis',
        message: 'Veuillez fournir un token Bearer valide'
      });
    }

    const token = authHeader.substring(7);
    
    // Validation basique du token (à adapter selon vos besoins)
    if (!token || token.length < 10) {
      return res.status(401).json({
        error: 'Token d\'authentification invalide',
        message: 'Le token fourni n\'est pas valide'
      });
    }
  }

  next();
};

/**
 * Validation du rate limiting personnalisé
 */
export const validateRateLimit = (maxRequests: number, windowMs: number) => {
  const requests = new Map<string, number[]>();

  return (req: Request, res: Response, next: NextFunction) => {
    const clientId = req.ip || 'unknown';
    const now = Date.now();
    
    if (!requests.has(clientId)) {
      requests.set(clientId, []);
    }

    const clientRequests = requests.get(clientId)!;
    
    // Nettoyer les anciennes requêtes
    const validRequests = clientRequests.filter(timestamp => now - timestamp < windowMs);
    requests.set(clientId, validRequests);

    if (validRequests.length >= maxRequests) {
      return res.status(429).json({
        error: 'Trop de requêtes',
        message: `Limite de ${maxRequests} requêtes par ${windowMs / 1000} secondes dépassée`,
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }

    validRequests.push(now);
    next();
  };
};

/**
 * Validation de la taille du payload
 */
export const validatePayloadSize = (maxSizeBytes: number) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = parseInt(req.headers['content-length'] || '0');
    
    if (contentLength > maxSizeBytes) {
      return res.status(413).json({
        error: 'Payload trop volumineux',
        message: `La taille du payload (${contentLength} bytes) dépasse la limite autorisée (${maxSizeBytes} bytes)`,
        maxSize: maxSizeBytes
      });
    }

    next();
  };
};

/**
 * Validation des types de contenu acceptés
 */
export const validateContentType = (allowedTypes: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentType = req.headers['content-type'];
    
    if (!contentType || !allowedTypes.some(type => contentType.includes(type))) {
      return res.status(415).json({
        error: 'Type de contenu non supporté',
        message: `Types acceptés: ${allowedTypes.join(', ')}`,
        received: contentType
      });
    }

    next();
  };
};
