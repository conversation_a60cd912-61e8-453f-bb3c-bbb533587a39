#!/usr/bin/env node

import dotenv from 'dotenv';
import { promises as fs } from 'fs';
import path from 'path';
import { PerformanceAgent } from './core/PerformanceAgent';
import { PerformanceServer } from './api/server';
import { loadConfig } from './utils/config';
import { createLogger } from './utils/logger';

// Charger les variables d'environnement
dotenv.config();

const logger = createLogger('Main');

/**
 * Fonction principale de démarrage
 */
async function main(): Promise<void> {
  try {
    logger.info('🚀 Démarrage de l\'Agent Performance...');
    
    // Créer les répertoires nécessaires
    await ensureDirectories();
    
    // Charger la configuration
    const config = loadConfig();
    logger.info('Configuration chargée', { 
      port: config.port,
      kafkaBrokers: config.kafka.brokers.length,
      weaviateHost: config.weaviate.host
    });
    
    // Initialiser l'agent
    const agent = new PerformanceAgent(config);
    
    // Initialiser le serveur API
    const server = new PerformanceServer(config, logger, agent);
    
    // Gestionnaires de signaux pour un arrêt propre
    setupGracefulShutdown(agent, server);
    
    // Démarrer l'agent
    await agent.start();
    logger.info('✅ Agent Performance démarré');
    
    // Démarrer le serveur API
    await server.start();
    logger.info('✅ Serveur API démarré');
    
    // Afficher les informations de démarrage
    displayStartupInfo(config);
    
    // Enregistrer les gestionnaires d'événements
    setupEventHandlers(agent);
    
    logger.info('🎯 Agent Performance prêt à recevoir des requêtes');
    
  } catch (error) {
    logger.error('❌ Erreur lors du démarrage:', error);
    process.exit(1);
  }
}

/**
 * Créer les répertoires nécessaires
 */
async function ensureDirectories(): Promise<void> {
  const directories = [
    'logs',
    'benchmarks',
    'reports',
    'temp'
  ];
  
  for (const dir of directories) {
    try {
      await fs.access(dir);
    } catch {
      await fs.mkdir(dir, { recursive: true });
      logger.info(`Répertoire créé: ${dir}`);
    }
  }
}

/**
 * Configuration de l'arrêt propre
 */
function setupGracefulShutdown(agent: PerformanceAgent, server: PerformanceServer): void {
  const shutdown = async (signal: string) => {
    logger.info(`Signal ${signal} reçu, arrêt en cours...`);
    
    try {
      // Arrêter le serveur API
      await server.stop();
      logger.info('Serveur API arrêté');
      
      // Arrêter l'agent
      await agent.stop();
      logger.info('Agent Performance arrêté');
      
      logger.info('Arrêt terminé avec succès');
      process.exit(0);
    } catch (error) {
      logger.error('Erreur lors de l\'arrêt:', error);
      process.exit(1);
    }
  };
  
  // Gestionnaires de signaux
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));
  
  // Gestionnaire d'erreurs non capturées
  process.on('uncaughtException', (error) => {
    logger.error('Exception non capturée:', error);
    shutdown('UNCAUGHT_EXCEPTION');
  });
  
  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Promesse rejetée non gérée:', { reason, promise });
    shutdown('UNHANDLED_REJECTION');
  });
}

/**
 * Afficher les informations de démarrage
 */
function displayStartupInfo(config: any): void {
  const info = `
╔══════════════════════════════════════════════════════════════╗
║                    🚀 AGENT PERFORMANCE                      ║
╠══════════════════════════════════════════════════════════════╣
║ Version: 1.0.0                                               ║
║ Port API: ${config.port.toString().padEnd(51)} ║
║ Kafka: ${config.kafka.brokers.join(', ').padEnd(53)} ║
║ Weaviate: ${config.weaviate.host.padEnd(50)} ║
║ Monitoring: ${(config.monitoring.interval / 1000 + 's').padEnd(48)} ║
╠══════════════════════════════════════════════════════════════╣
║ Endpoints disponibles:                                       ║
║ • GET  /health        - Health check                         ║
║ • GET  /metrics       - Métriques Prometheus                 ║
║ • POST /benchmark     - Lancer un benchmark                  ║
║ • POST /optimize      - Demander des optimisations           ║
║ • POST /monitor       - Configurer le monitoring             ║
║ • POST /analyze       - Analyser une application             ║
║ • GET  /reports       - Obtenir les rapports                 ║
║ • GET  /status        - Statut de l'agent                    ║
║ • GET  /api-docs      - Documentation API                    ║
╚══════════════════════════════════════════════════════════════╝
  `;
  
  console.log(info);
}

/**
 * Configuration des gestionnaires d'événements
 */
function setupEventHandlers(agent: PerformanceAgent): void {
  // Benchmark terminé
  agent.on('benchmarkCompleted', (result) => {
    logger.info(`📊 Benchmark terminé: ${result.id}`, {
      type: result.type,
      duration: result.duration,
      status: result.status,
      recommendations: result.recommendations.length
    });
  });
  
  // Optimisations générées
  agent.on('optimizationsGenerated', (recommendations) => {
    logger.info(`💡 ${recommendations.length} optimisations générées`, {
      priorities: recommendations.reduce((acc, rec) => {
        acc[rec.priority] = (acc[rec.priority] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    });
  });
  
  // Monitoring configuré
  agent.on('monitoringConfigured', (request) => {
    logger.info(`📈 Monitoring configuré: ${request.id}`, {
      targets: request.targets.length,
      metrics: request.metrics.length
    });
  });
  
  // Analyse terminée
  agent.on('analysisCompleted', (analysis) => {
    logger.info(`🔍 Analyse terminée: ${analysis.id}`, {
      type: analysis.type,
      findings: analysis.findings.length,
      recommendations: analysis.recommendations.length
    });
  });
  
  // Métrique reçue
  agent.on('metric', (metric) => {
    if (metric.value > 1000) { // Log seulement les métriques importantes
      logger.debug(`📊 Métrique: ${metric.name} = ${metric.value}${metric.unit}`);
    }
  });
  
  // Alerte générée
  agent.on('alert', (alert) => {
    const emoji = alert.severity === 'critical' ? '🚨' : 
                  alert.severity === 'error' ? '⚠️' : 
                  alert.severity === 'warning' ? '⚡' : 'ℹ️';
    
    logger.warn(`${emoji} Alerte ${alert.severity}: ${alert.title}`, {
      metric: alert.metric,
      value: alert.value,
      threshold: alert.threshold
    });
  });
  
  // Démarrage/arrêt
  agent.on('started', () => {
    logger.info('🟢 Agent Performance démarré');
  });
  
  agent.on('stopped', () => {
    logger.info('🔴 Agent Performance arrêté');
  });
  
  // Erreurs
  agent.on('error', (error) => {
    logger.error('❌ Erreur dans l\'agent:', error);
  });
}

/**
 * Fonction utilitaire pour les tests
 */
export async function createTestAgent(config?: any) {
  const testConfig = config || loadConfig();
  return new PerformanceAgent(testConfig);
}

/**
 * Fonction utilitaire pour les tests du serveur
 */
export async function createTestServer(agent: PerformanceAgent, config?: any) {
  const testConfig = config || loadConfig();
  const testLogger = createLogger('Test');
  return new PerformanceServer(testConfig, testLogger, agent);
}

// Démarrer l'application si ce fichier est exécuté directement
if (require.main === module) {
  main().catch((error) => {
    logger.error('Erreur fatale:', error);
    process.exit(1);
  });
}

// Exports pour les tests et l'utilisation en tant que module
export { PerformanceAgent, PerformanceServer, loadConfig, createLogger };
