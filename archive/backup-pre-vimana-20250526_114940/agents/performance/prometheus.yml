global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Agent Performance metrics
  - job_name: 'agent-performance'
    static_configs:
      - targets: ['agent-performance:3007']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Kafka metrics (if JMX exporter is enabled)
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka:9308']
    scrape_interval: 30s

  # Weaviate metrics
  - job_name: 'weaviate'
    static_configs:
      - targets: ['weaviate:8080']
    metrics_path: '/v1/meta'
    scrape_interval: 30s

  # Redis metrics (if redis_exporter is enabled)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  # Node exporter for system metrics
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093
