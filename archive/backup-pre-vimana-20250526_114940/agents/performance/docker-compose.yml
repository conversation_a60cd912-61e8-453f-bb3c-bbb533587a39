version: '3.8'

services:
  # Agent Performance
  agent-performance:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: agent-performance
    ports:
      - "3007:3007"
      - "9090:9090"  # Prometheus metrics
    environment:
      - NODE_ENV=production
      - PORT=3007
      - LOG_LEVEL=info
      
      # Kafka
      - KAFKA_BROKERS=kafka:9092
      - KAFKA_CLIENT_ID=performance-agent
      - KAFKA_GROUP_ID=performance-agent-group
      
      # Weaviate
      - WEAVIATE_HOST=weaviate:8080
      - WEAVIATE_SCHEME=http
      
      # Monitoring
      - MONITORING_INTERVAL=30000
      - THRESHOLD_RESPONSE_TIME=1000
      - THRESHOLD_CPU_USAGE=80
      - THRESHOLD_MEMORY_USAGE=85
      
      # Benchmarking
      - LIGHTHOUSE_ENABLED=true
      - LOAD_TESTING_ENABLED=true
      - PROFILING_ENABLED=true
    volumes:
      - ./logs:/app/logs
      - ./benchmarks:/app/benchmarks
      - ./reports:/app/reports
    depends_on:
      - kafka
      - weaviate
      - redis
    networks:
      - agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3007/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Kafka pour la communication
  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: kafka-performance
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    depends_on:
      - zookeeper
    networks:
      - agent-network
    volumes:
      - kafka-data:/var/lib/kafka/data

  # Zookeeper pour Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: zookeeper-performance
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - agent-network
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data

  # Weaviate pour la mémoire vectorielle
  weaviate:
    image: semitechnologies/weaviate:latest
    container_name: weaviate-performance
    ports:
      - "8080:8080"
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'none'
      ENABLE_MODULES: 'text2vec-openai,generative-openai'
      CLUSTER_HOSTNAME: 'node1'
    volumes:
      - weaviate-data:/var/lib/weaviate
    networks:
      - agent-network

  # Redis pour le cache
  redis:
    image: redis:7-alpine
    container_name: redis-performance
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    networks:
      - agent-network

  # Prometheus pour les métriques
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus-performance
    ports:
      - "9091:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - agent-network

  # Grafana pour la visualisation
  grafana:
    image: grafana/grafana:latest
    container_name: grafana-performance
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - agent-network
    depends_on:
      - prometheus

networks:
  agent-network:
    driver: bridge

volumes:
  kafka-data:
  zookeeper-data:
  weaviate-data:
  redis-data:
  prometheus-data:
  grafana-data:
