{"name": "agent-web-research", "version": "1.0.0", "description": "Agent spécialisé dans la recherche web intelligente et la veille concurrentielle", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["web-research", "competitive-analysis", "market-research", "web-scraping", "trend-detection", "intelligence"], "author": "Retreat And Be", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "winston": "^3.11.0", "kafkajs": "^2.2.4", "weaviate-ts-client": "^1.5.0", "puppeteer": "^21.5.2", "cheerio": "^1.0.0-rc.12", "axios": "^1.6.2", "dotenv": "^16.3.1", "joi": "^17.11.0", "node-cron": "^3.0.3", "redis": "^4.6.10", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.9.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/uuid": "^9.0.7", "@types/cheerio": "^0.22.35", "@types/jest": "^29.5.8", "typescript": "^5.2.2", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0"}}