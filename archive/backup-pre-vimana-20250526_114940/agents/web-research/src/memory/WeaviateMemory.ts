import weaviate, { WeaviateClient } from 'weaviate-ts-client';
import { Logger } from 'winston';
import { ResearchResult, DesignTrends, MonitoringAlert, UserBehaviorData, CompetitorUXAnalysis } from '../types';

/**
 * Mémoire Weaviate pour l'Agent Web Research
 */
export class WeaviateMemory {
  private client: WeaviateClient;
  private logger: Logger;
  private isConnected: boolean = false;

  // Noms des classes Weaviate
  private readonly classes = {
    RESEARCH_RESULTS: 'WebResearchResult',
    DESIGN_TRENDS: 'DesignTrend',
    MONITORING_ALERTS: 'MonitoringAlert',
    USER_BEHAVIOR: 'UserBehavior',
    COMPETITOR_ANALYSIS: 'CompetitorAnalysis',
    SEARCH_RESULTS: 'SearchResult',
    MARKET_INSIGHTS: 'MarketInsight'
  };

  constructor(config: any, logger: Logger) {
    this.logger = logger;

    this.client = weaviate.client({
      scheme: config.scheme || 'http',
      host: config.host || 'localhost:8080'
    });
  }

  /**
   * Initialise la mémoire Weaviate
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing Weaviate memory...');

      // Vérifier la connexion
      const isReady = await this.client.misc.readyChecker().do();
      if (!isReady) {
        throw new Error('Weaviate is not ready');
      }

      // Créer les schémas si nécessaire
      await this.createSchemas();

      this.isConnected = true;
      this.logger.info('Weaviate memory initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Weaviate memory:', error);
      throw error;
    }
  }

  /**
   * Crée les schémas Weaviate
   */
  private async createSchemas(): Promise<void> {
    try {
      // Schéma pour les résultats de recherche
      await this.createResearchResultSchema();
      
      // Schéma pour les tendances design
      await this.createDesignTrendSchema();
      
      // Schéma pour les alertes de monitoring
      await this.createMonitoringAlertSchema();
      
      // Schéma pour le comportement utilisateur
      await this.createUserBehaviorSchema();
      
      // Schéma pour l'analyse concurrentielle
      await this.createCompetitorAnalysisSchema();

      this.logger.info('Weaviate schemas created successfully');
    } catch (error) {
      this.logger.error('Failed to create Weaviate schemas:', error);
      throw error;
    }
  }

  /**
   * Crée le schéma pour les résultats de recherche
   */
  private async createResearchResultSchema(): Promise<void> {
    const schema = {
      class: this.classes.RESEARCH_RESULTS,
      description: 'Web research results and analysis',
      properties: [
        {
          name: 'query',
          dataType: ['text'],
          description: 'The search query'
        },
        {
          name: 'summary',
          dataType: ['text'],
          description: 'Analysis summary'
        },
        {
          name: 'keyInsights',
          dataType: ['text[]'],
          description: 'Key insights from the research'
        },
        {
          name: 'confidence',
          dataType: ['number'],
          description: 'Confidence score of the analysis'
        },
        {
          name: 'timestamp',
          dataType: ['date'],
          description: 'When the research was conducted'
        },
        {
          name: 'sources',
          dataType: ['text[]'],
          description: 'Sources used in the research'
        }
      ]
    };

    await this.createClassIfNotExists(schema);
  }

  /**
   * Crée le schéma pour les tendances design
   */
  private async createDesignTrendSchema(): Promise<void> {
    const schema = {
      class: this.classes.DESIGN_TRENDS,
      description: 'Design trends analysis',
      properties: [
        {
          name: 'industry',
          dataType: ['text'],
          description: 'Industry for the design trends'
        },
        {
          name: 'timeframe',
          dataType: ['text'],
          description: 'Timeframe of the trends'
        },
        {
          name: 'confidence',
          dataType: ['number'],
          description: 'Confidence in the trends analysis'
        },
        {
          name: 'colorTrends',
          dataType: ['text'],
          description: 'Color trends data as JSON'
        },
        {
          name: 'typographyTrends',
          dataType: ['text'],
          description: 'Typography trends data as JSON'
        },
        {
          name: 'layoutTrends',
          dataType: ['text'],
          description: 'Layout trends data as JSON'
        },
        {
          name: 'timestamp',
          dataType: ['date'],
          description: 'When the analysis was performed'
        }
      ]
    };

    await this.createClassIfNotExists(schema);
  }

  /**
   * Crée le schéma pour les alertes de monitoring
   */
  private async createMonitoringAlertSchema(): Promise<void> {
    const schema = {
      class: this.classes.MONITORING_ALERTS,
      description: 'Monitoring alerts and notifications',
      properties: [
        {
          name: 'type',
          dataType: ['text'],
          description: 'Type of alert'
        },
        {
          name: 'trigger',
          dataType: ['text'],
          description: 'What triggered the alert'
        },
        {
          name: 'severity',
          dataType: ['text'],
          description: 'Severity level of the alert'
        },
        {
          name: 'message',
          dataType: ['text'],
          description: 'Alert message'
        },
        {
          name: 'data',
          dataType: ['text'],
          description: 'Alert data as JSON'
        },
        {
          name: 'timestamp',
          dataType: ['date'],
          description: 'When the alert was generated'
        }
      ]
    };

    await this.createClassIfNotExists(schema);
  }

  /**
   * Crée le schéma pour le comportement utilisateur
   */
  private async createUserBehaviorSchema(): Promise<void> {
    const schema = {
      class: this.classes.USER_BEHAVIOR,
      description: 'User behavior analysis data',
      properties: [
        {
          name: 'domain',
          dataType: ['text'],
          description: 'Domain analyzed'
        },
        {
          name: 'timeframe',
          dataType: ['text'],
          description: 'Analysis timeframe'
        },
        {
          name: 'patterns',
          dataType: ['text'],
          description: 'Behavior patterns as JSON'
        },
        {
          name: 'preferences',
          dataType: ['text'],
          description: 'User preferences as JSON'
        },
        {
          name: 'demographics',
          dataType: ['text'],
          description: 'Demographics data as JSON'
        },
        {
          name: 'timestamp',
          dataType: ['date'],
          description: 'When the analysis was performed'
        }
      ]
    };

    await this.createClassIfNotExists(schema);
  }

  /**
   * Crée le schéma pour l'analyse concurrentielle
   */
  private async createCompetitorAnalysisSchema(): Promise<void> {
    const schema = {
      class: this.classes.COMPETITOR_ANALYSIS,
      description: 'Competitor analysis data',
      properties: [
        {
          name: 'competitor',
          dataType: ['text'],
          description: 'Competitor name or domain'
        },
        {
          name: 'uxScore',
          dataType: ['number'],
          description: 'UX score of the competitor'
        },
        {
          name: 'strengths',
          dataType: ['text'],
          description: 'Competitor strengths as JSON'
        },
        {
          name: 'weaknesses',
          dataType: ['text'],
          description: 'Competitor weaknesses as JSON'
        },
        {
          name: 'opportunities',
          dataType: ['text'],
          description: 'Identified opportunities as JSON'
        },
        {
          name: 'analysis',
          dataType: ['text'],
          description: 'Full analysis text'
        },
        {
          name: 'timestamp',
          dataType: ['date'],
          description: 'When the analysis was performed'
        }
      ]
    };

    await this.createClassIfNotExists(schema);
  }

  /**
   * Crée une classe si elle n'existe pas
   */
  private async createClassIfNotExists(schema: any): Promise<void> {
    try {
      const exists = await this.client.schema.exists(schema.class);
      if (!exists) {
        await this.client.schema.classCreator().withClass(schema).do();
        this.logger.info(`Created Weaviate class: ${schema.class}`);
      }
    } catch (error) {
      this.logger.warn(`Failed to create class ${schema.class}:`, error);
    }
  }

  /**
   * Stocke un résultat de recherche
   */
  async storeResearchResult(result: ResearchResult): Promise<void> {
    try {
      const data = {
        query: result.query,
        summary: result.analysis.summary,
        keyInsights: result.analysis.keyInsights,
        confidence: result.analysis.confidence,
        timestamp: result.metadata.timestamp.toISOString(),
        sources: result.metadata.sources
      };

      await this.client.data.creator()
        .withClassName(this.classes.RESEARCH_RESULTS)
        .withProperties(data)
        .do();

      this.logger.info(`Stored research result: ${result.id}`);
    } catch (error) {
      this.logger.error('Failed to store research result:', error);
      throw error;
    }
  }

  /**
   * Stocke des tendances design
   */
  async storeDesignTrends(trends: DesignTrends): Promise<void> {
    try {
      const data = {
        industry: trends.industry,
        timeframe: trends.timeframe,
        confidence: trends.confidence,
        colorTrends: JSON.stringify(trends.colorTrends),
        typographyTrends: JSON.stringify(trends.typographyTrends),
        layoutTrends: JSON.stringify(trends.layoutTrends),
        timestamp: new Date().toISOString()
      };

      await this.client.data.creator()
        .withClassName(this.classes.DESIGN_TRENDS)
        .withProperties(data)
        .do();

      this.logger.info(`Stored design trends for industry: ${trends.industry}`);
    } catch (error) {
      this.logger.error('Failed to store design trends:', error);
      throw error;
    }
  }

  /**
   * Stocke une alerte de monitoring
   */
  async storeAlert(alert: MonitoringAlert): Promise<void> {
    try {
      const data = {
        type: alert.type,
        trigger: alert.trigger,
        severity: alert.severity,
        message: alert.message,
        data: JSON.stringify(alert.data),
        timestamp: alert.timestamp.toISOString()
      };

      await this.client.data.creator()
        .withClassName(this.classes.MONITORING_ALERTS)
        .withProperties(data)
        .do();

      this.logger.info(`Stored monitoring alert: ${alert.id}`);
    } catch (error) {
      this.logger.error('Failed to store monitoring alert:', error);
      throw error;
    }
  }

  /**
   * Stocke des données de comportement utilisateur
   */
  async storeUserBehaviorData(behaviorData: UserBehaviorData): Promise<void> {
    try {
      const data = {
        domain: 'analyzed_domain', // À adapter selon les données
        timeframe: behaviorData.timeframe,
        patterns: JSON.stringify(behaviorData.patterns),
        preferences: JSON.stringify(behaviorData.preferences),
        demographics: JSON.stringify(behaviorData.demographics),
        timestamp: new Date().toISOString()
      };

      await this.client.data.creator()
        .withClassName(this.classes.USER_BEHAVIOR)
        .withProperties(data)
        .do();

      this.logger.info('Stored user behavior data');
    } catch (error) {
      this.logger.error('Failed to store user behavior data:', error);
      throw error;
    }
  }

  /**
   * Stocke des analyses UX concurrentielles
   */
  async storeCompetitorUXAnalyses(analyses: CompetitorUXAnalysis[]): Promise<void> {
    try {
      for (const analysis of analyses) {
        const data = {
          competitor: analysis.competitor,
          uxScore: analysis.uxScore,
          strengths: JSON.stringify(analysis.strengths),
          weaknesses: JSON.stringify(analysis.weaknesses),
          opportunities: JSON.stringify(analysis.opportunities),
          analysis: analysis.analysis,
          timestamp: new Date().toISOString()
        };

        await this.client.data.creator()
          .withClassName(this.classes.COMPETITOR_ANALYSIS)
          .withProperties(data)
          .do();
      }

      this.logger.info(`Stored ${analyses.length} competitor UX analyses`);
    } catch (error) {
      this.logger.error('Failed to store competitor UX analyses:', error);
      throw error;
    }
  }

  /**
   * Recherche des résultats similaires
   */
  async searchSimilarResults(query: string, limit: number = 10): Promise<any[]> {
    try {
      const result = await this.client.graphql.get()
        .withClassName(this.classes.RESEARCH_RESULTS)
        .withFields('query summary keyInsights confidence timestamp')
        .withNearText({ concepts: [query] })
        .withLimit(limit)
        .do();

      return result.data?.Get?.[this.classes.RESEARCH_RESULTS] || [];
    } catch (error) {
      this.logger.error('Failed to search similar results:', error);
      return [];
    }
  }

  /**
   * Déconnecte la mémoire
   */
  async disconnect(): Promise<void> {
    try {
      this.isConnected = false;
      this.logger.info('Weaviate memory disconnected');
    } catch (error) {
      this.logger.error('Error disconnecting Weaviate memory:', error);
    }
  }

  /**
   * Vérifie si la mémoire est connectée
   */
  isConnectedToWeaviate(): boolean {
    return this.isConnected;
  }
}
