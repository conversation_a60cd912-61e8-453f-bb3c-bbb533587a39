import { Logger } from 'winston';
import weaviate, { WeaviateClient } from 'weaviate-ts-client';
import { 
  MarketingStrategy, 
  Campaign, 
  ABTest, 
  MarketingAnalytics,
  MarketingResponse,
  SocialMediaPost
} from '../types';

/**
 * Système de mémoire pour l'Agent Marketing
 * Utilise Weaviate pour le stockage vectoriel des données marketing
 */
export class MarketingMemory {
  private client: WeaviateClient;
  private logger: Logger;
  private isConnected: boolean = false;

  // Collections Weaviate
  private readonly collections = {
    strategies: 'MarketingStrategy',
    campaigns: 'MarketingCampaign',
    abTests: 'ABTest',
    analytics: 'MarketingAnalytics',
    responses: 'MarketingResponse',
    socialPosts: 'SocialMediaPost',
    audiences: 'TargetAudience',
    insights: 'MarketingInsight',
    templates: 'CampaignTemplate',
    patterns: 'MarketingPattern'
  };

  constructor(logger: Logger) {
    this.logger = logger;
    this.client = weaviate.client({
      scheme: process.env.WEAVIATE_SCHEME || 'http',
      host: process.env.WEAVIATE_HOST || 'localhost:8080',
    });
  }

  /**
   * Initialise la connexion à Weaviate
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initialisation de la mémoire marketing...');

      // Test de connexion
      const isReady = await this.client.misc.readyChecker().do();
      if (!isReady) {
        throw new Error('Weaviate n\'est pas prêt');
      }

      // Création des schémas
      await this.createSchemas();

      this.isConnected = true;
      this.logger.info('Mémoire marketing initialisée avec succès');

    } catch (error) {
      this.logger.error('Erreur lors de l\'initialisation de la mémoire marketing:', error);
      throw error;
    }
  }

  /**
   * Stocke une stratégie marketing
   */
  async storeStrategy(strategy: MarketingStrategy): Promise<void> {
    try {
      await this.client.data
        .creator()
        .withClassName(this.collections.strategies)
        .withProperties({
          strategyId: strategy.id,
          name: strategy.name,
          description: strategy.description,
          targetAudience: JSON.stringify(strategy.targetAudience),
          objectives: JSON.stringify(strategy.objectives),
          channels: JSON.stringify(strategy.channels),
          budget: JSON.stringify(strategy.budget),
          timeline: JSON.stringify(strategy.timeline),
          kpis: JSON.stringify(strategy.kpis),
          createdAt: strategy.createdAt.toISOString(),
          updatedAt: strategy.updatedAt.toISOString()
        })
        .do();

      this.logger.info(`Stratégie stockée: ${strategy.id}`);

    } catch (error) {
      this.logger.error('Erreur lors du stockage de la stratégie:', error);
      throw error;
    }
  }

  /**
   * Récupère une stratégie marketing
   */
  async getStrategy(strategyId: string): Promise<MarketingStrategy | null> {
    try {
      const result = await this.client.graphql
        .get()
        .withClassName(this.collections.strategies)
        .withWhere({
          path: ['strategyId'],
          operator: 'Equal',
          valueText: strategyId
        })
        .withFields('strategyId name description targetAudience objectives channels budget timeline kpis createdAt updatedAt')
        .do();

      if (result.data.Get[this.collections.strategies]?.length > 0) {
        const data = result.data.Get[this.collections.strategies][0];
        return this.parseStrategy(data);
      }

      return null;

    } catch (error) {
      this.logger.error('Erreur lors de la récupération de la stratégie:', error);
      throw error;
    }
  }

  /**
   * Stocke une campagne
   */
  async storeCampaign(campaign: Campaign): Promise<void> {
    try {
      await this.client.data
        .creator()
        .withClassName(this.collections.campaigns)
        .withProperties({
          campaignId: campaign.id,
          name: campaign.name,
          description: campaign.description,
          type: campaign.type,
          status: campaign.status,
          strategy: campaign.strategy,
          channels: JSON.stringify(campaign.channels),
          content: JSON.stringify(campaign.content),
          targeting: JSON.stringify(campaign.targeting),
          budget: JSON.stringify(campaign.budget),
          schedule: JSON.stringify(campaign.schedule),
          metrics: JSON.stringify(campaign.metrics),
          createdAt: campaign.createdAt.toISOString(),
          updatedAt: campaign.updatedAt.toISOString()
        })
        .do();

      this.logger.info(`Campagne stockée: ${campaign.id}`);

    } catch (error) {
      this.logger.error('Erreur lors du stockage de la campagne:', error);
      throw error;
    }
  }

  /**
   * Récupère une campagne
   */
  async getCampaign(campaignId: string): Promise<Campaign | null> {
    try {
      const result = await this.client.graphql
        .get()
        .withClassName(this.collections.campaigns)
        .withWhere({
          path: ['campaignId'],
          operator: 'Equal',
          valueText: campaignId
        })
        .withFields('campaignId name description type status strategy channels content targeting budget schedule metrics createdAt updatedAt')
        .do();

      if (result.data.Get[this.collections.campaigns]?.length > 0) {
        const data = result.data.Get[this.collections.campaigns][0];
        return this.parseCampaign(data);
      }

      return null;

    } catch (error) {
      this.logger.error('Erreur lors de la récupération de la campagne:', error);
      throw error;
    }
  }

  /**
   * Met à jour une campagne
   */
  async updateCampaign(campaign: Campaign): Promise<void> {
    try {
      // Recherche de l'objet existant
      const existing = await this.client.graphql
        .get()
        .withClassName(this.collections.campaigns)
        .withWhere({
          path: ['campaignId'],
          operator: 'Equal',
          valueText: campaign.id
        })
        .withFields('_additional { id }')
        .do();

      if (existing.data.Get[this.collections.campaigns]?.length > 0) {
        const objectId = existing.data.Get[this.collections.campaigns][0]._additional.id;

        await this.client.data
          .updater()
          .withId(objectId)
          .withClassName(this.collections.campaigns)
          .withProperties({
            name: campaign.name,
            description: campaign.description,
            status: campaign.status,
            channels: JSON.stringify(campaign.channels),
            content: JSON.stringify(campaign.content),
            targeting: JSON.stringify(campaign.targeting),
            budget: JSON.stringify(campaign.budget),
            schedule: JSON.stringify(campaign.schedule),
            metrics: JSON.stringify(campaign.metrics),
            updatedAt: campaign.updatedAt.toISOString()
          })
          .do();

        this.logger.info(`Campagne mise à jour: ${campaign.id}`);
      } else {
        // Si la campagne n'existe pas, la créer
        await this.storeCampaign(campaign);
      }

    } catch (error) {
      this.logger.error('Erreur lors de la mise à jour de la campagne:', error);
      throw error;
    }
  }

  /**
   * Stocke un test A/B
   */
  async storeABTest(abTest: ABTest): Promise<void> {
    try {
      await this.client.data
        .creator()
        .withClassName(this.collections.abTests)
        .withProperties({
          testId: abTest.id,
          name: abTest.name,
          description: abTest.description,
          hypothesis: abTest.hypothesis,
          campaign: abTest.campaign,
          variations: JSON.stringify(abTest.variations),
          trafficSplit: JSON.stringify(abTest.trafficSplit),
          metrics: JSON.stringify(abTest.metrics),
          status: abTest.status,
          startDate: abTest.startDate.toISOString(),
          endDate: abTest.endDate?.toISOString(),
          results: JSON.stringify(abTest.results),
          createdAt: abTest.createdAt.toISOString()
        })
        .do();

      this.logger.info(`Test A/B stocké: ${abTest.id}`);

    } catch (error) {
      this.logger.error('Erreur lors du stockage du test A/B:', error);
      throw error;
    }
  }

  /**
   * Stocke des analyses marketing
   */
  async storeAnalytics(analytics: MarketingAnalytics): Promise<void> {
    try {
      await this.client.data
        .creator()
        .withClassName(this.collections.analytics)
        .withProperties({
          period: JSON.stringify(analytics.period),
          overview: JSON.stringify(analytics.overview),
          campaigns: JSON.stringify(analytics.campaigns),
          channels: JSON.stringify(analytics.channels),
          audience: JSON.stringify(analytics.audience),
          conversion: JSON.stringify(analytics.conversion),
          trends: JSON.stringify(analytics.trends),
          generatedAt: new Date().toISOString()
        })
        .do();

      this.logger.info('Analyses marketing stockées');

    } catch (error) {
      this.logger.error('Erreur lors du stockage des analyses:', error);
      throw error;
    }
  }

  /**
   * Stocke une réponse marketing
   */
  async storeResponse(response: MarketingResponse): Promise<void> {
    try {
      await this.client.data
        .creator()
        .withClassName(this.collections.responses)
        .withProperties({
          requestId: response.requestId,
          type: response.type,
          data: JSON.stringify(response.data),
          insights: JSON.stringify(response.insights),
          recommendations: JSON.stringify(response.recommendations),
          nextSteps: JSON.stringify(response.nextSteps),
          confidence: response.confidence,
          generatedAt: response.generatedAt.toISOString()
        })
        .do();

      this.logger.info(`Réponse marketing stockée: ${response.requestId}`);

    } catch (error) {
      this.logger.error('Erreur lors du stockage de la réponse:', error);
      throw error;
    }
  }

  /**
   * Recherche des stratégies similaires
   */
  async findSimilarStrategies(strategy: MarketingStrategy, limit: number = 5): Promise<MarketingStrategy[]> {
    try {
      const result = await this.client.graphql
        .get()
        .withClassName(this.collections.strategies)
        .withNearText({
          concepts: [strategy.description, strategy.name],
          certainty: 0.7
        })
        .withLimit(limit)
        .withFields('strategyId name description targetAudience objectives channels budget timeline kpis createdAt updatedAt')
        .do();

      return result.data.Get[this.collections.strategies]?.map((data: any) => this.parseStrategy(data)) || [];

    } catch (error) {
      this.logger.error('Erreur lors de la recherche de stratégies similaires:', error);
      throw error;
    }
  }

  /**
   * Crée les schémas Weaviate
   */
  private async createSchemas(): Promise<void> {
    const schemas = [
      {
        class: this.collections.strategies,
        description: 'Stratégies marketing',
        properties: [
          { name: 'strategyId', dataType: ['text'] },
          { name: 'name', dataType: ['text'] },
          { name: 'description', dataType: ['text'] },
          { name: 'targetAudience', dataType: ['text'] },
          { name: 'objectives', dataType: ['text'] },
          { name: 'channels', dataType: ['text'] },
          { name: 'budget', dataType: ['text'] },
          { name: 'timeline', dataType: ['text'] },
          { name: 'kpis', dataType: ['text'] },
          { name: 'createdAt', dataType: ['date'] },
          { name: 'updatedAt', dataType: ['date'] }
        ]
      },
      {
        class: this.collections.campaigns,
        description: 'Campagnes marketing',
        properties: [
          { name: 'campaignId', dataType: ['text'] },
          { name: 'name', dataType: ['text'] },
          { name: 'description', dataType: ['text'] },
          { name: 'type', dataType: ['text'] },
          { name: 'status', dataType: ['text'] },
          { name: 'strategy', dataType: ['text'] },
          { name: 'channels', dataType: ['text'] },
          { name: 'content', dataType: ['text'] },
          { name: 'targeting', dataType: ['text'] },
          { name: 'budget', dataType: ['text'] },
          { name: 'schedule', dataType: ['text'] },
          { name: 'metrics', dataType: ['text'] },
          { name: 'createdAt', dataType: ['date'] },
          { name: 'updatedAt', dataType: ['date'] }
        ]
      }
      // Autres schémas...
    ];

    for (const schema of schemas) {
      try {
        await this.client.schema.classCreator().withClass(schema).do();
        this.logger.info(`Schéma créé: ${schema.class}`);
      } catch (error) {
        // Ignorer si le schéma existe déjà
        if (!error.message.includes('already exists')) {
          throw error;
        }
      }
    }
  }

  /**
   * Parse une stratégie depuis Weaviate
   */
  private parseStrategy(data: any): MarketingStrategy {
    return {
      id: data.strategyId,
      name: data.name,
      description: data.description,
      targetAudience: JSON.parse(data.targetAudience),
      objectives: JSON.parse(data.objectives),
      channels: JSON.parse(data.channels),
      budget: JSON.parse(data.budget),
      timeline: JSON.parse(data.timeline),
      kpis: JSON.parse(data.kpis),
      createdAt: new Date(data.createdAt),
      updatedAt: new Date(data.updatedAt)
    };
  }

  /**
   * Parse une campagne depuis Weaviate
   */
  private parseCampaign(data: any): Campaign {
    return {
      id: data.campaignId,
      name: data.name,
      description: data.description,
      type: data.type,
      status: data.status,
      strategy: data.strategy,
      channels: JSON.parse(data.channels),
      content: JSON.parse(data.content),
      targeting: JSON.parse(data.targeting),
      budget: JSON.parse(data.budget),
      schedule: JSON.parse(data.schedule),
      metrics: JSON.parse(data.metrics),
      createdAt: new Date(data.createdAt),
      updatedAt: new Date(data.updatedAt)
    };
  }

  /**
   * Déconnexion
   */
  async disconnect(): Promise<void> {
    this.isConnected = false;
    this.logger.info('Mémoire marketing déconnectée');
  }
}
