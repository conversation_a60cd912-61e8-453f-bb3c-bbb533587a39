import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { 
  EvolutionRequest, 
  EvolutionResult, 
  GeneType, 
  Priority,
  EvolutionType 
} from '../types/evolution';
import { GeneticMemoryEngine } from '../engines/GeneticMemoryEngine';
import { AlphaEvolveEngine } from '../core/AlphaEvolveEngine';
import { NeuroplasticityEngine } from '../engines/NeuroplasticityEngine';

/**
 * API d'Évolution pour l'intégration avec le Cortex Central
 * 
 * Fournit une interface unifiée pour déclencher et monitorer l'évolution :
 * - Endpoints RESTful pour le Cortex Central
 * - Streaming en temps réel des résultats
 * - Orchestration intelligente des demandes
 * - Priorisation et allocation des ressources
 */
export class EvolutionAPI extends EventEmitter {
  private logger: Logger;
  private geneticMemoryEngine: GeneticMemoryEngine;
  private alphaEvolveEngine: AlphaEvolveEngine;
  private neuroplasticityEngine: NeuroplasticityEngine;
  
  // Gestion des demandes et orchestration
  private requestQueue: PrioritizedRequest[] = [];
  private activeRequests: Map<string, ActiveEvolutionRequest> = new Map();
  private resourcePool: ResourcePool;
  private maxConcurrentRequests: number = 5;
  
  // Métriques et monitoring
  private metrics: EvolutionMetrics = {
    totalRequests: 0,
    completedRequests: 0,
    failedRequests: 0,
    averageExecutionTime: 0,
    activeRequestsCount: 0,
    queueLength: 0
  };

  constructor(
    logger: Logger,
    geneticMemoryEngine: GeneticMemoryEngine,
    alphaEvolveEngine: AlphaEvolveEngine,
    neuroplasticityEngine: NeuroplasticityEngine
  ) {
    super();
    this.logger = logger;
    this.geneticMemoryEngine = geneticMemoryEngine;
    this.alphaEvolveEngine = alphaEvolveEngine;
    this.neuroplasticityEngine = neuroplasticityEngine;
    
    this.resourcePool = new ResourcePool(logger);
  }

  /**
   * Initialise l'API d'évolution
   */
  async initialize(): Promise<void> {
    this.logger.info('🚀 Initialisation de l\'API d\'évolution');
    
    await this.resourcePool.initialize();
    this.startRequestProcessor();
    
    this.logger.info('✅ API d\'évolution initialisée');
  }

  /**
   * Déclenche une évolution (endpoint principal pour le Cortex Central)
   */
  async triggerEvolution(request: EvolutionRequest): Promise<EvolutionRequestResponse> {
    this.logger.info(`🧬 Nouvelle demande d'évolution: ${request.id} (${request.type})`);
    
    // Validation de la demande
    this.validateRequest(request);
    
    // Création de la demande priorisée
    const prioritizedRequest: PrioritizedRequest = {
      ...request,
      submittedAt: new Date(),
      priority: this.calculatePriority(request),
      estimatedDuration: this.estimateExecutionTime(request)
    };

    // Ajout à la queue
    this.addToQueue(prioritizedRequest);
    
    // Mise à jour des métriques
    this.metrics.totalRequests++;
    this.metrics.queueLength = this.requestQueue.length;
    
    // Émission d'événement
    this.emit('request-queued', prioritizedRequest);
    
    return {
      requestId: request.id,
      status: 'queued',
      estimatedStartTime: this.estimateStartTime(),
      estimatedDuration: prioritizedRequest.estimatedDuration,
      queuePosition: this.requestQueue.length
    };
  }

  /**
   * Obtient le statut d'une demande d'évolution
   */
  getEvolutionStatus(requestId: string): EvolutionStatusResponse {
    const activeRequest = this.activeRequests.get(requestId);
    
    if (activeRequest) {
      return {
        requestId,
        status: 'running',
        progress: activeRequest.progress,
        currentGeneration: activeRequest.currentGeneration,
        bestFitness: activeRequest.bestFitness,
        startedAt: activeRequest.startedAt,
        estimatedCompletion: activeRequest.estimatedCompletion
      };
    }
    
    const queuedRequest = this.requestQueue.find(req => req.id === requestId);
    if (queuedRequest) {
      return {
        requestId,
        status: 'queued',
        queuePosition: this.requestQueue.indexOf(queuedRequest) + 1,
        estimatedStartTime: this.estimateStartTime()
      };
    }
    
    return {
      requestId,
      status: 'not_found'
    };
  }

  /**
   * Annule une demande d'évolution
   */
  async cancelEvolution(requestId: string): Promise<boolean> {
    // Vérifier si la demande est en cours
    const activeRequest = this.activeRequests.get(requestId);
    if (activeRequest) {
      activeRequest.cancelled = true;
      this.logger.info(`❌ Annulation de l'évolution en cours: ${requestId}`);
      return true;
    }
    
    // Vérifier si la demande est en queue
    const queueIndex = this.requestQueue.findIndex(req => req.id === requestId);
    if (queueIndex !== -1) {
      this.requestQueue.splice(queueIndex, 1);
      this.metrics.queueLength = this.requestQueue.length;
      this.logger.info(`❌ Suppression de la queue: ${requestId}`);
      return true;
    }
    
    return false;
  }

  /**
   * Obtient les métriques de l'API
   */
  getMetrics(): EvolutionMetrics {
    return {
      ...this.metrics,
      activeRequestsCount: this.activeRequests.size,
      queueLength: this.requestQueue.length,
      resourceUtilization: this.resourcePool.getUtilization()
    };
  }

  /**
   * Obtient les demandes actives
   */
  getActiveRequests(): ActiveEvolutionRequest[] {
    return Array.from(this.activeRequests.values());
  }

  /**
   * Stream en temps réel des résultats d'évolution
   */
  createEvolutionStream(requestId: string): EvolutionStream {
    return new EvolutionStream(requestId, this);
  }

  // Méthodes privées

  /**
   * Valide une demande d'évolution
   */
  private validateRequest(request: EvolutionRequest): void {
    if (!request.id || !request.type || !request.target) {
      throw new Error('Demande d\'évolution invalide: champs requis manquants');
    }
    
    if (!Object.values(EvolutionType).includes(request.type)) {
      throw new Error(`Type d'évolution non supporté: ${request.type}`);
    }
  }

  /**
   * Calcule la priorité d'une demande
   */
  private calculatePriority(request: EvolutionRequest): number {
    let priority = 0;
    
    // Priorité basée sur le niveau de priorité
    switch (request.priority) {
      case Priority.CRITICAL: priority += 100; break;
      case Priority.HIGH: priority += 75; break;
      case Priority.MEDIUM: priority += 50; break;
      case Priority.LOW: priority += 25; break;
    }
    
    // Bonus pour certains types d'évolution
    if (request.type === EvolutionType.BUG_FIXING) priority += 20;
    if (request.type === EvolutionType.SECURITY_HARDENING) priority += 15;
    
    // Malus pour les demandes complexes
    if (request.constraints.maxExecutionTime > 3600000) priority -= 10; // > 1h
    
    return priority;
  }

  /**
   * Estime le temps d'exécution
   */
  private estimateExecutionTime(request: EvolutionRequest): number {
    let baseTime = 60000; // 1 minute de base
    
    // Facteurs d'ajustement
    if (request.type === EvolutionType.ALGORITHM_OPTIMIZATION) baseTime *= 2;
    if (request.type === EvolutionType.ARCHITECTURE_EVOLUTION) baseTime *= 3;
    
    const populationSize = request.constraints.maxExecutionTime || 300000;
    baseTime += populationSize / 1000; // Ajustement basé sur la complexité
    
    return Math.min(baseTime, request.constraints.maxExecutionTime || 1800000); // Max 30 min
  }

  /**
   * Estime l'heure de début
   */
  private estimateStartTime(): Date {
    const now = new Date();
    const queueTime = this.requestQueue.reduce((total, req) => total + req.estimatedDuration, 0);
    return new Date(now.getTime() + queueTime);
  }

  /**
   * Ajoute une demande à la queue avec priorisation
   */
  private addToQueue(request: PrioritizedRequest): void {
    // Insertion triée par priorité
    let insertIndex = 0;
    while (insertIndex < this.requestQueue.length && 
           this.requestQueue[insertIndex].priority >= request.priority) {
      insertIndex++;
    }
    
    this.requestQueue.splice(insertIndex, 0, request);
  }

  /**
   * Processeur de demandes en arrière-plan
   */
  private startRequestProcessor(): void {
    setInterval(async () => {
      await this.processNextRequest();
    }, 1000); // Vérification chaque seconde
  }

  /**
   * Traite la prochaine demande dans la queue
   */
  private async processNextRequest(): Promise<void> {
    if (this.activeRequests.size >= this.maxConcurrentRequests || this.requestQueue.length === 0) {
      return;
    }
    
    const request = this.requestQueue.shift()!;
    this.metrics.queueLength = this.requestQueue.length;
    
    // Vérification des ressources disponibles
    if (!this.resourcePool.hasAvailableResources(request)) {
      // Remettre en queue si pas de ressources
      this.requestQueue.unshift(request);
      return;
    }
    
    // Démarrage de l'évolution
    await this.executeEvolution(request);
  }

  /**
   * Exécute une évolution
   */
  private async executeEvolution(request: PrioritizedRequest): Promise<void> {
    const startTime = new Date();
    const activeRequest: ActiveEvolutionRequest = {
      ...request,
      startedAt: startTime,
      progress: 0,
      currentGeneration: 0,
      bestFitness: 0,
      estimatedCompletion: new Date(startTime.getTime() + request.estimatedDuration),
      cancelled: false
    };
    
    this.activeRequests.set(request.id, activeRequest);
    this.metrics.activeRequestsCount = this.activeRequests.size;
    
    try {
      this.logger.info(`🚀 Démarrage de l'évolution: ${request.id}`);
      this.emit('evolution-started', activeRequest);
      
      // Allocation des ressources
      const resources = await this.resourcePool.allocateResources(request);
      
      // Configuration de l'évolution avec callbacks de progression
      const evolutionConfig = {
        ...request.config,
        onProgress: (progress: EvolutionProgress) => {
          activeRequest.progress = progress.percentage;
          activeRequest.currentGeneration = progress.generation;
          activeRequest.bestFitness = progress.bestFitness;
          this.emit('evolution-progress', { requestId: request.id, progress });
        }
      };
      
      // Exécution de l'évolution
      const result = await this.alphaEvolveEngine.evolve({
        problem: request.target.problem,
        domain: request.target.domain,
        metrics: request.objectives.map(obj => obj.metric),
        constraints: request.constraints,
        config: evolutionConfig
      });
      
      // Stockage des résultats dans la mémoire génétique
      await this.geneticMemoryEngine.extractGenesFromSolution(result.bestSolution);
      
      // Finalisation
      const executionTime = Date.now() - startTime.getTime();
      this.updateMetrics(executionTime, true);
      
      this.logger.info(`✅ Évolution terminée: ${request.id} (${executionTime}ms)`);
      this.emit('evolution-completed', { requestId: request.id, result, executionTime });
      
    } catch (error) {
      this.logger.error(`❌ Erreur lors de l'évolution ${request.id}:`, error);
      this.updateMetrics(Date.now() - startTime.getTime(), false);
      this.emit('evolution-failed', { requestId: request.id, error });
      
    } finally {
      // Libération des ressources
      await this.resourcePool.releaseResources(request.id);
      this.activeRequests.delete(request.id);
      this.metrics.activeRequestsCount = this.activeRequests.size;
    }
  }

  /**
   * Met à jour les métriques
   */
  private updateMetrics(executionTime: number, success: boolean): void {
    if (success) {
      this.metrics.completedRequests++;
    } else {
      this.metrics.failedRequests++;
    }
    
    // Calcul de la moyenne mobile du temps d'exécution
    const totalCompleted = this.metrics.completedRequests + this.metrics.failedRequests;
    this.metrics.averageExecutionTime = 
      (this.metrics.averageExecutionTime * (totalCompleted - 1) + executionTime) / totalCompleted;
  }
}

// Interfaces et types

export interface EvolutionRequestResponse {
  requestId: string;
  status: 'queued' | 'running' | 'completed' | 'failed';
  estimatedStartTime?: Date;
  estimatedDuration?: number;
  queuePosition?: number;
}

export interface EvolutionStatusResponse {
  requestId: string;
  status: 'queued' | 'running' | 'completed' | 'failed' | 'not_found';
  progress?: number;
  currentGeneration?: number;
  bestFitness?: number;
  startedAt?: Date;
  estimatedCompletion?: Date;
  queuePosition?: number;
  estimatedStartTime?: Date;
}

export interface PrioritizedRequest extends EvolutionRequest {
  submittedAt: Date;
  priority: number;
  estimatedDuration: number;
}

export interface ActiveEvolutionRequest extends PrioritizedRequest {
  startedAt: Date;
  progress: number;
  currentGeneration: number;
  bestFitness: number;
  estimatedCompletion: Date;
  cancelled: boolean;
}

export interface EvolutionMetrics {
  totalRequests: number;
  completedRequests: number;
  failedRequests: number;
  averageExecutionTime: number;
  activeRequestsCount: number;
  queueLength: number;
  resourceUtilization?: number;
}

export interface EvolutionProgress {
  percentage: number;
  generation: number;
  bestFitness: number;
  diversity: number;
  convergenceScore: number;
}

/**
 * Pool de ressources pour l'allocation dynamique
 */
class ResourcePool {
  private logger: Logger;
  private availableWorkers: number = 4;
  private allocatedResources: Map<string, AllocatedResource> = new Map();

  constructor(logger: Logger) {
    this.logger = logger;
  }

  async initialize(): Promise<void> {
    this.logger.info('🔧 Initialisation du pool de ressources');
  }

  hasAvailableResources(request: PrioritizedRequest): boolean {
    const requiredWorkers = this.calculateRequiredWorkers(request);
    return this.availableWorkers >= requiredWorkers;
  }

  async allocateResources(request: PrioritizedRequest): Promise<AllocatedResource> {
    const requiredWorkers = this.calculateRequiredWorkers(request);
    
    if (this.availableWorkers < requiredWorkers) {
      throw new Error('Ressources insuffisantes');
    }
    
    const resource: AllocatedResource = {
      requestId: request.id,
      workers: requiredWorkers,
      allocatedAt: new Date()
    };
    
    this.allocatedResources.set(request.id, resource);
    this.availableWorkers -= requiredWorkers;
    
    this.logger.debug(`🔧 Ressources allouées: ${requiredWorkers} workers pour ${request.id}`);
    
    return resource;
  }

  async releaseResources(requestId: string): Promise<void> {
    const resource = this.allocatedResources.get(requestId);
    if (resource) {
      this.availableWorkers += resource.workers;
      this.allocatedResources.delete(requestId);
      this.logger.debug(`🔧 Ressources libérées: ${resource.workers} workers de ${requestId}`);
    }
  }

  getUtilization(): number {
    const totalWorkers = 4; // Configuration par défaut
    return (totalWorkers - this.availableWorkers) / totalWorkers;
  }

  private calculateRequiredWorkers(request: PrioritizedRequest): number {
    // Calcul basé sur la complexité et la priorité
    let workers = 1;
    
    if (request.priority > 75) workers = 2; // Haute priorité
    if (request.type === EvolutionType.ARCHITECTURE_EVOLUTION) workers += 1;
    
    return Math.min(workers, this.availableWorkers);
  }
}

interface AllocatedResource {
  requestId: string;
  workers: number;
  allocatedAt: Date;
}

/**
 * Stream en temps réel pour les résultats d'évolution
 */
export class EvolutionStream extends EventEmitter {
  private requestId: string;
  private api: EvolutionAPI;

  constructor(requestId: string, api: EvolutionAPI) {
    super();
    this.requestId = requestId;
    this.api = api;
    
    // Écoute des événements de l'API
    this.api.on('evolution-progress', (data) => {
      if (data.requestId === this.requestId) {
        this.emit('progress', data.progress);
      }
    });
    
    this.api.on('evolution-completed', (data) => {
      if (data.requestId === this.requestId) {
        this.emit('completed', data.result);
      }
    });
    
    this.api.on('evolution-failed', (data) => {
      if (data.requestId === this.requestId) {
        this.emit('failed', data.error);
      }
    });
  }
}
