import * as ts from 'typescript';
import { GeneType } from '../types/evolution';

/**
 * Analyseur de patterns basé sur l'AST TypeScript
 * 
 * Extrait des patterns de code sophistiqués en analysant l'arbre syntaxique
 * plutôt que d'utiliser des expressions régulières simples.
 */
export class ASTPatternAnalyzer {
  private sourceFile: ts.SourceFile | null = null;

  /**
   * Analyse un code source et extrait les patterns
   */
  analyzeCode(code: string): CodePattern[] {
    try {
      this.sourceFile = ts.createSourceFile(
        'temp.ts',
        code,
        ts.ScriptTarget.Latest,
        true
      );

      const patterns: CodePattern[] = [];
      this.visitNode(this.sourceFile, patterns);
      
      return patterns;
    } catch (error) {
      console.warn('Erreur lors de l\'analyse AST:', error);
      return this.fallbackRegexAnalysis(code);
    }
  }

  /**
   * Visite récursivement les nœuds de l'AST
   */
  private visitNode(node: ts.Node, patterns: CodePattern[]): void {
    switch (node.kind) {
      case ts.SyntaxKind.FunctionDeclaration:
        this.analyzeFunctionDeclaration(node as ts.FunctionDeclaration, patterns);
        break;
      
      case ts.SyntaxKind.ClassDeclaration:
        this.analyzeClassDeclaration(node as ts.ClassDeclaration, patterns);
        break;
      
      case ts.SyntaxKind.ForStatement:
        this.analyzeForLoop(node as ts.ForStatement, patterns);
        break;
      
      case ts.SyntaxKind.WhileStatement:
        this.analyzeWhileLoop(node as ts.WhileStatement, patterns);
        break;
      
      case ts.SyntaxKind.IfStatement:
        this.analyzeIfStatement(node as ts.IfStatement, patterns);
        break;
      
      case ts.SyntaxKind.TryStatement:
        this.analyzeTryStatement(node as ts.TryStatement, patterns);
        break;
      
      case ts.SyntaxKind.VariableDeclaration:
        this.analyzeVariableDeclaration(node as ts.VariableDeclaration, patterns);
        break;
      
      case ts.SyntaxKind.CallExpression:
        this.analyzeCallExpression(node as ts.CallExpression, patterns);
        break;
    }

    // Continuer la visite récursive
    ts.forEachChild(node, child => this.visitNode(child, patterns));
  }

  /**
   * Analyse une déclaration de fonction
   */
  private analyzeFunctionDeclaration(node: ts.FunctionDeclaration, patterns: CodePattern[]): void {
    const functionText = this.getNodeText(node);
    const functionName = node.name?.text || 'anonymous';
    
    // Analyser la complexité
    const complexity = this.calculateComplexity(node);
    const parameters = node.parameters.length;
    
    // Déterminer le type de pattern
    let geneType: GeneType = GeneType.BEHAVIOR;
    let description = `Fonction ${functionName}`;
    
    // Classification intelligente
    if (this.isAlgorithmicFunction(node)) {
      geneType = GeneType.ALGORITHM;
      description = `Algorithme ${functionName}`;
    } else if (this.isOptimizationPattern(node)) {
      geneType = GeneType.OPTIMIZATION;
      description = `Optimisation ${functionName}`;
    } else if (this.isUtilityFunction(node)) {
      geneType = GeneType.PATTERN;
      description = `Pattern utilitaire ${functionName}`;
    }

    patterns.push({
      code: functionText,
      type: geneType,
      description,
      complexity,
      parameters,
      quality: this.assessQuality(node),
      reusability: this.assessReusability(node)
    });
  }

  /**
   * Analyse une déclaration de classe
   */
  private analyzeClassDeclaration(node: ts.ClassDeclaration, patterns: CodePattern[]): void {
    const classText = this.getNodeText(node);
    const className = node.name?.text || 'AnonymousClass';
    
    const methods = node.members.filter(member => 
      ts.isFunctionLike(member)
    ).length;
    
    const properties = node.members.filter(member => 
      ts.isPropertyDeclaration(member)
    ).length;

    patterns.push({
      code: classText,
      type: GeneType.STRUCTURE,
      description: `Classe ${className} (${methods} méthodes, ${properties} propriétés)`,
      complexity: this.calculateComplexity(node),
      parameters: methods + properties,
      quality: this.assessQuality(node),
      reusability: this.assessReusability(node)
    });
  }

  /**
   * Analyse une boucle for
   */
  private analyzeForLoop(node: ts.ForStatement, patterns: CodePattern[]): void {
    const loopText = this.getNodeText(node);
    const complexity = this.calculateComplexity(node);
    
    let description = 'Boucle for';
    if (this.isNestedLoop(node)) {
      description = 'Boucle for imbriquée';
    }

    patterns.push({
      code: loopText,
      type: GeneType.ALGORITHM,
      description,
      complexity,
      parameters: 1,
      quality: this.assessQuality(node),
      reusability: this.assessReusability(node)
    });
  }

  /**
   * Analyse une boucle while
   */
  private analyzeWhileLoop(node: ts.WhileStatement, patterns: CodePattern[]): void {
    const loopText = this.getNodeText(node);
    
    patterns.push({
      code: loopText,
      type: GeneType.ALGORITHM,
      description: 'Boucle while',
      complexity: this.calculateComplexity(node),
      parameters: 1,
      quality: this.assessQuality(node),
      reusability: this.assessReusability(node)
    });
  }

  /**
   * Analyse une instruction if
   */
  private analyzeIfStatement(node: ts.IfStatement, patterns: CodePattern[]): void {
    const ifText = this.getNodeText(node);
    const hasElse = node.elseStatement !== undefined;
    
    patterns.push({
      code: ifText,
      type: GeneType.STRUCTURE,
      description: hasElse ? 'Condition if-else' : 'Condition if',
      complexity: this.calculateComplexity(node),
      parameters: hasElse ? 2 : 1,
      quality: this.assessQuality(node),
      reusability: this.assessReusability(node)
    });
  }

  /**
   * Analyse un bloc try-catch
   */
  private analyzeTryStatement(node: ts.TryStatement, patterns: CodePattern[]): void {
    const tryText = this.getNodeText(node);
    
    patterns.push({
      code: tryText,
      type: GeneType.PATTERN,
      description: 'Gestion d\'erreur try-catch',
      complexity: this.calculateComplexity(node),
      parameters: 1,
      quality: this.assessQuality(node),
      reusability: this.assessReusability(node)
    });
  }

  /**
   * Analyse une déclaration de variable
   */
  private analyzeVariableDeclaration(node: ts.VariableDeclaration, patterns: CodePattern[]): void {
    if (!node.initializer) return;

    const varText = this.getNodeText(node);
    const varName = node.name.getText();
    
    // Détecter les patterns spéciaux
    if (this.isMemoizationPattern(node)) {
      patterns.push({
        code: varText,
        type: GeneType.OPTIMIZATION,
        description: `Pattern de mémoisation: ${varName}`,
        complexity: 1,
        parameters: 1,
        quality: 0.8,
        reusability: 0.9
      });
    } else if (this.isCachePattern(node)) {
      patterns.push({
        code: varText,
        type: GeneType.PATTERN,
        description: `Pattern de cache: ${varName}`,
        complexity: 1,
        parameters: 1,
        quality: 0.7,
        reusability: 0.8
      });
    }
  }

  /**
   * Analyse un appel de fonction
   */
  private analyzeCallExpression(node: ts.CallExpression, patterns: CodePattern[]): void {
    const callText = this.getNodeText(node);
    
    // Détecter les patterns d'appel spéciaux
    if (this.isRecursiveCall(node)) {
      patterns.push({
        code: callText,
        type: GeneType.ALGORITHM,
        description: 'Appel récursif',
        complexity: this.calculateComplexity(node),
        parameters: node.arguments.length,
        quality: this.assessQuality(node),
        reusability: this.assessReusability(node)
      });
    }
  }

  /**
   * Calcule la complexité d'un nœud
   */
  private calculateComplexity(node: ts.Node): number {
    let complexity = 1;
    
    const visit = (n: ts.Node) => {
      switch (n.kind) {
        case ts.SyntaxKind.IfStatement:
        case ts.SyntaxKind.ForStatement:
        case ts.SyntaxKind.WhileStatement:
        case ts.SyntaxKind.DoStatement:
        case ts.SyntaxKind.SwitchStatement:
          complexity++;
          break;
      }
      ts.forEachChild(n, visit);
    };
    
    visit(node);
    return complexity;
  }

  /**
   * Évalue la qualité d'un pattern
   */
  private assessQuality(node: ts.Node): number {
    let quality = 0.5;
    
    // Facteurs positifs
    if (this.hasGoodNaming(node)) quality += 0.2;
    if (this.hasDocumentation(node)) quality += 0.1;
    if (this.isWellStructured(node)) quality += 0.1;
    if (this.hasErrorHandling(node)) quality += 0.1;
    
    // Facteurs négatifs
    if (this.isTooComplex(node)) quality -= 0.2;
    if (this.hasMagicNumbers(node)) quality -= 0.1;
    
    return Math.max(0, Math.min(1, quality));
  }

  /**
   * Évalue la réutilisabilité d'un pattern
   */
  private assessReusability(node: ts.Node): number {
    let reusability = 0.5;
    
    // Facteurs positifs
    if (this.isGeneric(node)) reusability += 0.2;
    if (this.hasMinimalDependencies(node)) reusability += 0.2;
    if (this.isStateless(node)) reusability += 0.1;
    
    // Facteurs négatifs
    if (this.hasHardcodedValues(node)) reusability -= 0.2;
    if (this.hasExternalDependencies(node)) reusability -= 0.1;
    
    return Math.max(0, Math.min(1, reusability));
  }

  // Méthodes utilitaires pour la classification

  private isAlgorithmicFunction(node: ts.FunctionDeclaration): boolean {
    const name = node.name?.text.toLowerCase() || '';
    const algorithmKeywords = ['sort', 'search', 'find', 'filter', 'map', 'reduce', 'calculate'];
    return algorithmKeywords.some(keyword => name.includes(keyword));
  }

  private isOptimizationPattern(node: ts.Node): boolean {
    const text = this.getNodeText(node).toLowerCase();
    const optimizationKeywords = ['memo', 'cache', 'optimize', 'efficient', 'fast'];
    return optimizationKeywords.some(keyword => text.includes(keyword));
  }

  private isUtilityFunction(node: ts.FunctionDeclaration): boolean {
    const name = node.name?.text.toLowerCase() || '';
    const utilityKeywords = ['helper', 'util', 'format', 'validate', 'convert'];
    return utilityKeywords.some(keyword => name.includes(keyword));
  }

  private isMemoizationPattern(node: ts.VariableDeclaration): boolean {
    const text = this.getNodeText(node).toLowerCase();
    return text.includes('memo') || text.includes('cache') || text.includes('map');
  }

  private isCachePattern(node: ts.VariableDeclaration): boolean {
    const text = this.getNodeText(node).toLowerCase();
    return text.includes('cache') || text.includes('store');
  }

  private isNestedLoop(node: ts.ForStatement): boolean {
    let hasNestedLoop = false;
    const visit = (n: ts.Node) => {
      if (n !== node && (ts.isForStatement(n) || ts.isWhileStatement(n))) {
        hasNestedLoop = true;
      }
      if (!hasNestedLoop) {
        ts.forEachChild(n, visit);
      }
    };
    visit(node);
    return hasNestedLoop;
  }

  private isRecursiveCall(node: ts.CallExpression): boolean {
    // Logique simplifiée pour détecter la récursion
    const callText = this.getNodeText(node);
    return callText.includes('recursive') || callText.includes('self');
  }

  private hasGoodNaming(node: ts.Node): boolean {
    const text = this.getNodeText(node);
    // Vérifier si les noms sont descriptifs (plus de 3 caractères, pas de x, y, z)
    const names = text.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || [];
    return names.some(name => name.length > 3 && !['temp', 'tmp', 'x', 'y', 'z'].includes(name));
  }

  private hasDocumentation(node: ts.Node): boolean {
    const text = this.getNodeText(node);
    return text.includes('/**') || text.includes('//');
  }

  private isWellStructured(node: ts.Node): boolean {
    const complexity = this.calculateComplexity(node);
    return complexity <= 10; // Complexité cyclomatique raisonnable
  }

  private hasErrorHandling(node: ts.Node): boolean {
    const text = this.getNodeText(node);
    return text.includes('try') || text.includes('catch') || text.includes('throw');
  }

  private isTooComplex(node: ts.Node): boolean {
    const complexity = this.calculateComplexity(node);
    return complexity > 15;
  }

  private hasMagicNumbers(node: ts.Node): boolean {
    const text = this.getNodeText(node);
    const numbers = text.match(/\b\d+\b/g) || [];
    return numbers.some(num => !['0', '1', '2'].includes(num));
  }

  private isGeneric(node: ts.Node): boolean {
    const text = this.getNodeText(node);
    return text.includes('<T>') || text.includes('generic') || text.includes('any');
  }

  private hasMinimalDependencies(node: ts.Node): boolean {
    const text = this.getNodeText(node);
    const imports = (text.match(/import/g) || []).length;
    return imports <= 3;
  }

  private isStateless(node: ts.Node): boolean {
    const text = this.getNodeText(node);
    return !text.includes('this.') && !text.includes('global');
  }

  private hasHardcodedValues(node: ts.Node): boolean {
    const text = this.getNodeText(node);
    return text.includes('"') || text.includes("'") || this.hasMagicNumbers(node);
  }

  private hasExternalDependencies(node: ts.Node): boolean {
    const text = this.getNodeText(node);
    return text.includes('require(') || text.includes('import');
  }

  /**
   * Obtient le texte d'un nœud
   */
  private getNodeText(node: ts.Node): string {
    if (!this.sourceFile) return '';
    return node.getFullText(this.sourceFile).trim();
  }

  /**
   * Analyse de fallback avec regex (si l'AST échoue)
   */
  private fallbackRegexAnalysis(code: string): CodePattern[] {
    const patterns: CodePattern[] = [];
    
    // Patterns simples comme dans l'implémentation originale
    const loopPattern = /for\s*\([^)]+\)\s*{[^}]+}/g;
    const functionPattern = /function\s+\w+\s*\([^)]*\)\s*{[^}]+}/g;
    const conditionalPattern = /if\s*\([^)]+\)\s*{[^}]+}/g;
    
    let match;
    
    while ((match = loopPattern.exec(code)) !== null) {
      patterns.push({
        code: match[0],
        type: GeneType.ALGORITHM,
        description: 'Boucle for (regex)',
        complexity: 2,
        parameters: 1,
        quality: 0.5,
        reusability: 0.5
      });
    }
    
    while ((match = functionPattern.exec(code)) !== null) {
      patterns.push({
        code: match[0],
        type: GeneType.BEHAVIOR,
        description: 'Fonction (regex)',
        complexity: 3,
        parameters: 1,
        quality: 0.5,
        reusability: 0.5
      });
    }
    
    while ((match = conditionalPattern.exec(code)) !== null) {
      patterns.push({
        code: match[0],
        type: GeneType.STRUCTURE,
        description: 'Condition if (regex)',
        complexity: 2,
        parameters: 1,
        quality: 0.5,
        reusability: 0.5
      });
    }
    
    return patterns;
  }
}

/**
 * Interface pour les patterns de code extraits
 */
export interface CodePattern {
  code: string;
  type: GeneType;
  description: string;
  complexity: number;
  parameters: number;
  quality: number;
  reusability: number;
}
