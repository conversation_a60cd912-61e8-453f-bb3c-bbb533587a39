import { NeuroplasticityEngine } from '../engines/NeuroplasticityEngine';
import { NeuroplasticityDashboard } from '../monitoring/NeuroplasticityDashboard';
import { AdaptiveRouter } from '../routing/AdaptiveRouter';
import { SimpleLogger } from '../utils/logger';
import { AdaptationType, MessagePriority } from '../types/evolution';

/**
 * Démonstration du Système de Neuroplasticité
 * 
 * Cette démonstration montre les capacités complètes du système :
 * - Création et adaptation de connexions synaptiques
 * - Monitoring en temps réel
 * - Routage adaptatif des messages
 * - Optimisation automatique des voies de communication
 */
export class NeuroplasticityDemo {
  private engine: NeuroplasticityEngine;
  private dashboard: NeuroplasticityDashboard;
  private router: AdaptiveRouter;
  private logger: SimpleLogger;

  constructor() {
    this.logger = new SimpleLogger();
    this.engine = new NeuroplasticityEngine(this.logger);
    this.dashboard = new NeuroplasticityDashboard(this.engine, this.logger);
    this.router = new AdaptiveRouter(this.engine, this.logger);
  }

  /**
   * Lance la démonstration complète
   */
  public async runDemo(): Promise<void> {
    console.log('🧠 === DÉMONSTRATION DU SYSTÈME DE NEUROPLASTICITÉ === 🧠\n');

    try {
      // 1. Initialisation
      await this.initializeSystem();

      // 2. Création du réseau initial
      await this.createInitialNetwork();

      // 3. Simulation d'activité
      await this.simulateNetworkActivity();

      // 4. Démonstration du monitoring
      await this.demonstrateMonitoring();

      // 5. Démonstration du routage adaptatif
      await this.demonstrateAdaptiveRouting();

      // 6. Optimisation automatique
      await this.demonstrateOptimization();

      // 7. Rapport final
      await this.generateFinalReport();

    } catch (error) {
      console.error('❌ Erreur pendant la démonstration:', error);
    } finally {
      this.dashboard.stopMonitoring();
    }
  }

  /**
   * Initialise le système
   */
  private async initializeSystem(): Promise<void> {
    console.log('🔧 Initialisation du système...');
    
    await this.engine.initialize();
    await this.router.initialize();
    await this.dashboard.startMonitoring(5000); // Monitoring toutes les 5 secondes

    console.log('✅ Système initialisé avec succès\n');
  }

  /**
   * Crée un réseau initial d'agents
   */
  private async createInitialNetwork(): Promise<void> {
    console.log('🌐 Création du réseau initial...');

    const agents = [
      'cortex-central',
      'agent-frontend', 
      'agent-backend',
      'agent-database',
      'agent-cache',
      'agent-auth'
    ];

    // Créer des connexions hub-and-spoke avec le cortex central
    for (const agent of agents.slice(1)) {
      await this.engine.createConnection('cortex-central', agent, { 
        latency: Math.random() * 50 + 30,
        messageType: 'initialization'
      });
    }

    // Créer quelques connexions directes
    await this.engine.createConnection('agent-frontend', 'agent-backend', { latency: 25 });
    await this.engine.createConnection('agent-backend', 'agent-database', { latency: 80 });
    await this.engine.createConnection('agent-frontend', 'agent-cache', { latency: 15 });
    await this.engine.createConnection('agent-backend', 'agent-auth', { latency: 40 });

    const metrics = this.engine.getPlasticityMetrics();
    console.log(`✅ Réseau créé: ${metrics.totalConnections} connexions\n`);
  }

  /**
   * Simule l'activité du réseau
   */
  private async simulateNetworkActivity(): Promise<void> {
    console.log('⚡ Simulation d\'activité réseau...');

    // Simuler des interactions réussies
    for (let i = 0; i < 20; i++) {
      await this.engine.strengthenConnection('agent-frontend', 'agent-cache', {
        success: true,
        latency: Math.random() * 10 + 10,
        messageType: 'cache_hit'
      });

      await this.engine.strengthenConnection('agent-frontend', 'agent-backend', {
        success: true,
        latency: Math.random() * 20 + 20,
        messageType: 'api_call'
      });
    }

    // Simuler quelques échecs
    for (let i = 0; i < 5; i++) {
      await this.engine.weakenConnection('agent-backend', 'agent-database', 'timeout');
    }

    console.log('✅ Activité simulée terminée\n');
  }

  /**
   * Démontre les capacités de monitoring
   */
  private async demonstrateMonitoring(): Promise<void> {
    console.log('📊 Démonstration du monitoring...');

    // Attendre un cycle de collecte
    await new Promise(resolve => setTimeout(resolve, 6000));

    const currentMetrics = this.dashboard.getCurrentMetrics();
    if (currentMetrics) {
      console.log('📈 Métriques actuelles:');
      console.log(`   - Connexions totales: ${currentMetrics.plasticity.totalConnections}`);
      console.log(`   - Force moyenne: ${currentMetrics.plasticity.averageStrength.toFixed(3)}`);
      console.log(`   - Latence moyenne: ${currentMetrics.plasticity.averageLatency.toFixed(1)}ms`);
      console.log(`   - Efficacité réseau: ${(currentMetrics.plasticity.networkEfficiency * 100).toFixed(1)}%`);
      console.log(`   - Santé globale: ${currentMetrics.health.status} (${(currentMetrics.health.score * 100).toFixed(1)}%)`);
    }

    const patterns = this.engine.analyzeCommunicationPatterns();
    console.log(`\n🔍 Patterns identifiés: ${patterns.length}`);
    patterns.slice(0, 3).forEach((pattern, index) => {
      console.log(`   ${index + 1}. ${pattern.pattern} - Efficacité: ${(pattern.efficiency * 100).toFixed(1)}%`);
    });

    console.log('✅ Monitoring démontré\n');
  }

  /**
   * Démontre le routage adaptatif
   */
  private async demonstrateAdaptiveRouting(): Promise<void> {
    console.log('🛣️ Démonstration du routage adaptatif...');

    // Simuler plusieurs messages avec différentes priorités
    const messages = [
      { from: 'agent-frontend', to: 'agent-database', priority: MessagePriority.HIGH },
      { from: 'agent-frontend', to: 'agent-cache', priority: MessagePriority.CRITICAL },
      { from: 'cortex-central', to: 'agent-backend', priority: MessagePriority.NORMAL },
      { from: 'agent-backend', to: 'agent-auth', priority: MessagePriority.LOW }
    ];

    for (const msg of messages) {
      try {
        const routingMessage = {
          id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`,
          from: msg.from,
          to: msg.to,
          payload: { demo: true },
          priority: msg.priority,
          timestamp: new Date()
        };

        const result = await this.router.routeMessage(routingMessage);
        console.log(`📨 Message routé: ${msg.from} → ${msg.to} (${result.latency.toFixed(1)}ms, ${result.hops} sauts)`);
      } catch (error) {
        console.log(`❌ Échec routage: ${msg.from} → ${msg.to}`);
      }
    }

    const routingMetrics = this.router.getRoutingMetrics();
    console.log(`\n📊 Métriques de routage:`);
    console.log(`   - Messages routés: ${routingMetrics.totalRoutes}`);
    console.log(`   - Taux de succès: ${((routingMetrics.successfulRoutes / routingMetrics.totalRoutes) * 100).toFixed(1)}%`);
    console.log(`   - Latence moyenne: ${routingMetrics.averageLatency.toFixed(1)}ms`);

    console.log('✅ Routage adaptatif démontré\n');
  }

  /**
   * Démontre l'optimisation automatique
   */
  private async demonstrateOptimization(): Promise<void> {
    console.log('🔧 Démonstration de l\'optimisation automatique...');

    // Identifier les voies sous-optimales
    const suboptimalPaths = this.engine.identifySuboptimalPaths();
    console.log(`🔍 Voies sous-optimales détectées: ${suboptimalPaths.length}`);

    if (suboptimalPaths.length > 0) {
      suboptimalPaths.slice(0, 3).forEach((path, index) => {
        console.log(`   ${index + 1}. ${path.from} → ${path.to} (${path.bottleneckType})`);
      });

      // Lancer l'optimisation
      const optimizationResult = await this.engine.optimizeCommunicationPaths();
      console.log(`\n⚡ Optimisation terminée:`);
      console.log(`   - Voies analysées: ${optimizationResult.pathsAnalyzed}`);
      console.log(`   - Voies optimisées: ${optimizationResult.pathsOptimized}`);
      console.log(`   - Amélioration moyenne: ${(optimizationResult.averageImprovement * 100).toFixed(1)}%`);
      console.log(`   - Gain d'efficacité: ${(optimizationResult.networkEfficiencyGain * 100).toFixed(1)}%`);
    }

    console.log('✅ Optimisation démontrée\n');
  }

  /**
   * Génère un rapport final
   */
  private async generateFinalReport(): Promise<void> {
    console.log('📋 Génération du rapport final...');

    const healthReport = this.dashboard.generateHealthReport();
    const adaptationHistory = this.engine.getAdaptationHistory();
    const finalMetrics = this.engine.getPlasticityMetrics();

    console.log('\n🎯 === RAPPORT FINAL ===');
    console.log(`📊 Statut global: ${healthReport.status.toUpperCase()}`);
    console.log(`📝 Résumé: ${healthReport.summary}`);
    console.log(`🔄 Adaptations effectuées: ${adaptationHistory.length}`);
    console.log(`🌐 Connexions finales: ${finalMetrics.totalConnections}`);
    console.log(`💪 Force moyenne finale: ${finalMetrics.averageStrength.toFixed(3)}`);
    console.log(`⚡ Efficacité finale: ${(finalMetrics.networkEfficiency * 100).toFixed(1)}%`);

    console.log('\n💡 Recommandations:');
    healthReport.recommendations.forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`);
    });

    console.log('\n🎉 === DÉMONSTRATION TERMINÉE ===');
  }
}

// Fonction pour lancer la démonstration
export async function runNeuroplasticityDemo(): Promise<void> {
  const demo = new NeuroplasticityDemo();
  await demo.runDemo();
}

// Si ce fichier est exécuté directement
if (require.main === module) {
  runNeuroplasticityDemo().catch(console.error);
}
