import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { GeneticCode, GeneType } from '../types/evolution';

/**
 * Base de données génétique avancée avec indexation sémantique
 * 
 * Implémente un système de stockage optimisé pour les gènes algorithmiques :
 * - Indexation multi-dimensionnelle
 * - Compression et déduplication
 * - Recherche sémantique avancée
 * - Clustering automatique
 * - Persistance et récupération
 */
export class GeneticDatabase extends EventEmitter {
  private logger: Logger;
  private genes: Map<string, GeneticCode> = new Map();
  private semanticIndex: Map<string, Set<string>> = new Map();
  private typeIndex: Map<GeneType, Set<string>> = new Map();
  private fitnessIndex: Map<number, Set<string>> = new Map();
  private complexityIndex: Map<number, Set<string>> = new Map();
  private clusters: Map<string, GeneCluster> = new Map();
  private compressionCache: Map<string, CompressedGene> = new Map();

  constructor(logger: Logger) {
    super();
    this.logger = logger;
  }

  /**
   * Initialise la base de données
   */
  async initialize(): Promise<void> {
    this.logger.info('🗄️ Initialisation de la base de données génétique');
    
    await this.loadPersistedData();
    await this.buildIndexes();
    await this.initializeClusters();
    
    this.logger.info(`✅ Base de données génétique initialisée: ${this.genes.size} gènes`);
  }

  /**
   * Stocke un gène avec indexation automatique
   */
  async storeGene(gene: GeneticCode): Promise<void> {
    // Vérifier la déduplication
    const existingGene = await this.findDuplicate(gene);
    if (existingGene) {
      this.logger.debug(`🔄 Gène dupliqué détecté: ${gene.id} -> ${existingGene.id}`);
      await this.mergeDuplicateGenes(gene, existingGene);
      return;
    }

    // Compression si nécessaire
    const compressedGene = await this.compressGene(gene);
    if (compressedGene) {
      this.compressionCache.set(gene.id, compressedGene);
    }

    // Stockage
    this.genes.set(gene.id, gene);
    
    // Indexation
    await this.indexGene(gene);
    
    // Clustering
    await this.assignToCluster(gene);
    
    this.logger.debug(`💾 Gène stocké: ${gene.id} (${gene.type})`);
    this.emit('gene-stored', gene);
  }

  /**
   * Récupère un gène par ID
   */
  async getGene(id: string): Promise<GeneticCode | null> {
    const gene = this.genes.get(id);
    if (!gene) return null;

    // Décompression si nécessaire
    const compressed = this.compressionCache.get(id);
    if (compressed) {
      return await this.decompressGene(compressed);
    }

    return gene;
  }

  /**
   * Recherche sémantique avancée
   */
  async searchGenes(query: GeneSearchQuery): Promise<GeneSearchResult[]> {
    const results: GeneSearchResult[] = [];
    
    // Recherche par type
    let candidates = new Set<string>();
    if (query.type) {
      candidates = this.typeIndex.get(query.type) || new Set();
    } else {
      candidates = new Set(this.genes.keys());
    }

    // Filtrage par fitness
    if (query.minFitness !== undefined) {
      candidates = this.filterByFitness(candidates, query.minFitness);
    }

    // Filtrage par complexité
    if (query.maxComplexity !== undefined) {
      candidates = this.filterByComplexity(candidates, query.maxComplexity);
    }

    // Recherche sémantique
    if (query.semanticTerms && query.semanticTerms.length > 0) {
      candidates = await this.semanticSearch(candidates, query.semanticTerms);
    }

    // Calcul des scores et tri
    for (const geneId of candidates) {
      const gene = await this.getGene(geneId);
      if (gene) {
        const score = await this.calculateRelevanceScore(gene, query);
        results.push({ gene, score });
      }
    }

    // Tri par score décroissant
    results.sort((a, b) => b.score - a.score);
    
    // Limitation des résultats
    const limit = query.limit || 50;
    return results.slice(0, limit);
  }

  /**
   * Trouve les gènes similaires
   */
  async findSimilarGenes(geneId: string, threshold: number = 0.7): Promise<GeneticCode[]> {
    const targetGene = await this.getGene(geneId);
    if (!targetGene) return [];

    const similar: Array<{ gene: GeneticCode; similarity: number }> = [];
    
    for (const [id, gene] of this.genes) {
      if (id === geneId) continue;
      
      const similarity = await this.calculateSimilarity(targetGene, gene);
      if (similarity >= threshold) {
        similar.push({ gene, similarity });
      }
    }

    // Tri par similarité décroissante
    similar.sort((a, b) => b.similarity - a.similarity);
    
    return similar.map(item => item.gene);
  }

  /**
   * Obtient les statistiques de la base de données
   */
  getStatistics(): DatabaseStatistics {
    const typeDistribution = new Map<GeneType, number>();
    const fitnessDistribution: number[] = [];
    const complexityDistribution: number[] = [];
    
    for (const gene of this.genes.values()) {
      // Distribution par type
      const count = typeDistribution.get(gene.type) || 0;
      typeDistribution.set(gene.type, count + 1);
      
      // Distribution de fitness
      fitnessDistribution.push(gene.fitness);
      
      // Distribution de complexité (si disponible)
      // complexityDistribution.push(gene.complexity || 1);
    }

    return {
      totalGenes: this.genes.size,
      typeDistribution: Object.fromEntries(typeDistribution),
      averageFitness: fitnessDistribution.reduce((a, b) => a + b, 0) / fitnessDistribution.length,
      fitnessRange: {
        min: Math.min(...fitnessDistribution),
        max: Math.max(...fitnessDistribution)
      },
      clustersCount: this.clusters.size,
      compressionRatio: this.compressionCache.size / this.genes.size
    };
  }

  /**
   * Nettoie les gènes obsolètes
   */
  async cleanup(criteria: CleanupCriteria): Promise<number> {
    const toDelete: string[] = [];
    const now = new Date();
    
    for (const [id, gene] of this.genes) {
      let shouldDelete = false;
      
      // Critères de nettoyage
      if (criteria.maxAge && gene.age > criteria.maxAge) {
        shouldDelete = true;
      }
      
      if (criteria.minFitness && gene.fitness < criteria.minFitness) {
        shouldDelete = true;
      }
      
      if (criteria.maxUnusedDays) {
        const daysSinceLastUse = (now.getTime() - gene.lastUsed.getTime()) / (1000 * 60 * 60 * 24);
        if (daysSinceLastUse > criteria.maxUnusedDays) {
          shouldDelete = true;
        }
      }
      
      if (shouldDelete) {
        toDelete.push(id);
      }
    }

    // Suppression
    for (const id of toDelete) {
      await this.deleteGene(id);
    }

    this.logger.info(`🧹 Nettoyage terminé: ${toDelete.length} gènes supprimés`);
    return toDelete.length;
  }

  /**
   * Exporte la base de données
   */
  async export(): Promise<DatabaseExport> {
    const genes = Array.from(this.genes.values());
    const clusters = Array.from(this.clusters.values());
    
    return {
      version: '1.0',
      timestamp: new Date(),
      genes,
      clusters,
      statistics: this.getStatistics()
    };
  }

  /**
   * Importe une base de données
   */
  async import(data: DatabaseExport): Promise<void> {
    this.logger.info(`📥 Import de ${data.genes.length} gènes`);
    
    // Validation de version
    if (data.version !== '1.0') {
      throw new Error(`Version non supportée: ${data.version}`);
    }

    // Import des gènes
    for (const gene of data.genes) {
      await this.storeGene(gene);
    }

    // Import des clusters
    for (const cluster of data.clusters) {
      this.clusters.set(cluster.id, cluster);
    }

    await this.buildIndexes();
    this.logger.info('✅ Import terminé');
  }

  // Méthodes privées

  private async findDuplicate(gene: GeneticCode): Promise<GeneticCode | null> {
    const sequenceHash = this.hashSequence(gene.sequence);
    
    for (const existingGene of this.genes.values()) {
      const existingHash = this.hashSequence(existingGene.sequence);
      if (sequenceHash === existingHash && gene.type === existingGene.type) {
        return existingGene;
      }
    }
    
    return null;
  }

  private async mergeDuplicateGenes(newGene: GeneticCode, existingGene: GeneticCode): Promise<void> {
    // Mise à jour des statistiques
    existingGene.usageCount += newGene.usageCount;
    existingGene.fitness = (existingGene.fitness + newGene.fitness) / 2;
    existingGene.lastUsed = new Date(Math.max(
      existingGene.lastUsed.getTime(),
      newGene.lastUsed.getTime()
    ));
    
    this.emit('genes-merged', { existing: existingGene, duplicate: newGene });
  }

  private async compressGene(gene: GeneticCode): Promise<CompressedGene | null> {
    // Compression simple basée sur la taille
    if (gene.sequence.length > 1000) {
      return {
        id: gene.id,
        compressedSequence: this.compress(gene.sequence),
        originalSize: gene.sequence.length,
        compressionRatio: 0.7 // Simulation
      };
    }
    return null;
  }

  private async decompressGene(compressed: CompressedGene): Promise<GeneticCode> {
    const gene = this.genes.get(compressed.id);
    if (!gene) throw new Error(`Gène non trouvé: ${compressed.id}`);
    
    return {
      ...gene,
      sequence: this.decompress(compressed.compressedSequence)
    };
  }

  private async indexGene(gene: GeneticCode): Promise<void> {
    // Index par type
    if (!this.typeIndex.has(gene.type)) {
      this.typeIndex.set(gene.type, new Set());
    }
    this.typeIndex.get(gene.type)!.add(gene.id);

    // Index sémantique
    const terms = this.extractSemanticTerms(gene);
    for (const term of terms) {
      if (!this.semanticIndex.has(term)) {
        this.semanticIndex.set(term, new Set());
      }
      this.semanticIndex.get(term)!.add(gene.id);
    }

    // Index par fitness (buckets)
    const fitnessBucket = Math.floor(gene.fitness * 10) / 10;
    if (!this.fitnessIndex.has(fitnessBucket)) {
      this.fitnessIndex.set(fitnessBucket, new Set());
    }
    this.fitnessIndex.get(fitnessBucket)!.add(gene.id);
  }

  private async assignToCluster(gene: GeneticCode): Promise<void> {
    // Clustering simple basé sur le type et la fitness
    const clusterId = `${gene.type}_${Math.floor(gene.fitness * 10)}`;
    
    if (!this.clusters.has(clusterId)) {
      this.clusters.set(clusterId, {
        id: clusterId,
        type: gene.type,
        genes: new Set(),
        centroid: {
          fitness: gene.fitness,
          complexity: 1 // Valeur par défaut
        },
        createdAt: new Date()
      });
    }
    
    this.clusters.get(clusterId)!.genes.add(gene.id);
  }

  private filterByFitness(candidates: Set<string>, minFitness: number): Set<string> {
    const filtered = new Set<string>();
    
    for (const geneId of candidates) {
      const gene = this.genes.get(geneId);
      if (gene && gene.fitness >= minFitness) {
        filtered.add(geneId);
      }
    }
    
    return filtered;
  }

  private filterByComplexity(candidates: Set<string>, maxComplexity: number): Set<string> {
    // Pour l'instant, retourne tous les candidats
    // À implémenter quand la complexité sera ajoutée aux gènes
    return candidates;
  }

  private async semanticSearch(candidates: Set<string>, terms: string[]): Promise<Set<string>> {
    const results = new Set<string>();
    
    for (const term of terms) {
      const termResults = this.semanticIndex.get(term.toLowerCase()) || new Set();
      for (const geneId of termResults) {
        if (candidates.has(geneId)) {
          results.add(geneId);
        }
      }
    }
    
    return results;
  }

  private async calculateRelevanceScore(gene: GeneticCode, query: GeneSearchQuery): Promise<number> {
    let score = gene.fitness; // Score de base
    
    // Bonus pour correspondance sémantique
    if (query.semanticTerms) {
      const geneTerms = this.extractSemanticTerms(gene);
      const matches = query.semanticTerms.filter(term => 
        geneTerms.some(geneTerm => geneTerm.includes(term.toLowerCase()))
      );
      score += matches.length * 0.1;
    }
    
    // Bonus pour usage récent
    const daysSinceLastUse = (Date.now() - gene.lastUsed.getTime()) / (1000 * 60 * 60 * 24);
    score += Math.max(0, 0.1 - daysSinceLastUse * 0.01);
    
    return Math.min(1, score);
  }

  private async calculateSimilarity(gene1: GeneticCode, gene2: GeneticCode): Promise<number> {
    if (gene1.type !== gene2.type) return 0;
    
    // Similarité basée sur les termes sémantiques
    const terms1 = this.extractSemanticTerms(gene1);
    const terms2 = this.extractSemanticTerms(gene2);
    
    const intersection = terms1.filter(term => terms2.includes(term));
    const union = [...new Set([...terms1, ...terms2])];
    
    const semanticSimilarity = intersection.length / union.length;
    
    // Similarité de fitness
    const fitnessSimilarity = 1 - Math.abs(gene1.fitness - gene2.fitness);
    
    return (semanticSimilarity + fitnessSimilarity) / 2;
  }

  private extractSemanticTerms(gene: GeneticCode): string[] {
    const text = (gene.sequence + ' ' + gene.expression).toLowerCase();
    return text
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(term => term.length > 2)
      .slice(0, 20); // Limiter le nombre de termes
  }

  private hashSequence(sequence: string): string {
    // Hash simple pour la déduplication
    let hash = 0;
    for (let i = 0; i < sequence.length; i++) {
      const char = sequence.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  private compress(data: string): string {
    // Compression simulée
    return Buffer.from(data).toString('base64');
  }

  private decompress(data: string): string {
    // Décompression simulée
    return Buffer.from(data, 'base64').toString();
  }

  private async deleteGene(id: string): Promise<void> {
    this.genes.delete(id);
    this.compressionCache.delete(id);
    
    // Nettoyer les index
    for (const geneSet of this.typeIndex.values()) {
      geneSet.delete(id);
    }
    for (const geneSet of this.semanticIndex.values()) {
      geneSet.delete(id);
    }
    for (const geneSet of this.fitnessIndex.values()) {
      geneSet.delete(id);
    }
    
    // Nettoyer les clusters
    for (const cluster of this.clusters.values()) {
      cluster.genes.delete(id);
    }
  }

  private async loadPersistedData(): Promise<void> {
    // À implémenter : chargement depuis le stockage persistant
  }

  private async buildIndexes(): Promise<void> {
    this.semanticIndex.clear();
    this.typeIndex.clear();
    this.fitnessIndex.clear();
    
    for (const gene of this.genes.values()) {
      await this.indexGene(gene);
    }
  }

  private async initializeClusters(): Promise<void> {
    this.clusters.clear();
    
    for (const gene of this.genes.values()) {
      await this.assignToCluster(gene);
    }
  }
}

// Interfaces

export interface GeneSearchQuery {
  type?: GeneType;
  semanticTerms?: string[];
  minFitness?: number;
  maxComplexity?: number;
  limit?: number;
}

export interface GeneSearchResult {
  gene: GeneticCode;
  score: number;
}

export interface GeneCluster {
  id: string;
  type: GeneType;
  genes: Set<string>;
  centroid: {
    fitness: number;
    complexity: number;
  };
  createdAt: Date;
}

export interface CompressedGene {
  id: string;
  compressedSequence: string;
  originalSize: number;
  compressionRatio: number;
}

export interface DatabaseStatistics {
  totalGenes: number;
  typeDistribution: Record<string, number>;
  averageFitness: number;
  fitnessRange: {
    min: number;
    max: number;
  };
  clustersCount: number;
  compressionRatio: number;
}

export interface CleanupCriteria {
  maxAge?: number;
  minFitness?: number;
  maxUnusedDays?: number;
}

export interface DatabaseExport {
  version: string;
  timestamp: Date;
  genes: GeneticCode[];
  clusters: GeneCluster[];
  statistics: DatabaseStatistics;
}
