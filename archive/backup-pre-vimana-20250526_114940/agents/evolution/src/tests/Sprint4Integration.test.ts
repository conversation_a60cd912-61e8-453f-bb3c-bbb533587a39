import { EvolutionOrchestrator } from '../orchestration/EvolutionOrchestrator';
import { EvolutionAPI } from '../api/EvolutionAPI';
import { PerformanceOptimizer } from '../optimization/PerformanceOptimizer';
import { EvolutionDashboard } from '../monitoring/EvolutionDashboard';
import { GeneticMemoryEngine } from '../engines/GeneticMemoryEngine';
import { AlphaEvolveEngine } from '../core/AlphaEvolveEngine';
import { NeuroplasticityEngine } from '../engines/NeuroplasticityEngine';
import { EvolutionRequest, EvolutionType, Priority } from '../types/evolution';
import { mockLogger } from './setup';

describe('Sprint 4 Integration Tests', () => {
  let orchestrator: EvolutionOrchestrator;
  let evolutionAPI: EvolutionAPI;
  let performanceOptimizer: PerformanceOptimizer;
  let dashboard: EvolutionDashboard;
  let geneticMemoryEngine: GeneticMemoryEngine;
  let alphaEvolveEngine: AlphaEvolveEngine;
  let neuroplasticityEngine: NeuroplasticityEngine;

  beforeEach(async () => {
    // Initialisation des composants
    geneticMemoryEngine = new GeneticMemoryEngine(mockLogger);
    alphaEvolveEngine = new AlphaEvolveEngine(mockLogger);
    neuroplasticityEngine = new NeuroplasticityEngine(mockLogger);
    performanceOptimizer = new PerformanceOptimizer(mockLogger);
    
    evolutionAPI = new EvolutionAPI(
      mockLogger,
      geneticMemoryEngine,
      alphaEvolveEngine,
      neuroplasticityEngine
    );
    
    dashboard = new EvolutionDashboard(
      mockLogger,
      geneticMemoryEngine,
      performanceOptimizer,
      evolutionAPI
    );
    
    orchestrator = new EvolutionOrchestrator(mockLogger);
    
    // Initialisation
    await orchestrator.initialize();
    
    jest.clearAllMocks();
  });

  afterEach(async () => {
    await orchestrator.shutdown();
  });

  describe('API d\'Évolution et Orchestration', () => {
    test('should handle evolution request through API', async () => {
      const request: EvolutionRequest = {
        id: 'test-evolution-1',
        type: EvolutionType.ALGORITHM_OPTIMIZATION,
        priority: Priority.HIGH,
        target: {
          problem: 'Optimize sorting algorithm',
          domain: 'algorithms',
          context: { dataSize: 1000 }
        },
        objectives: [
          { metric: 'performance', weight: 0.4, target: 0.9 },
          { metric: 'efficiency', weight: 0.3, target: 0.8 },
          { metric: 'maintainability', weight: 0.3, target: 0.7 }
        ],
        constraints: {
          maxExecutionTime: 300000, // 5 minutes
          maxMemoryUsage: 1024,
          maxComplexity: 10
        },
        config: {
          populationSize: 50,
          maxGenerations: 100,
          mutationRate: 0.1,
          crossoverRate: 0.8
        }
      };

      // Déclenchement via l'API
      const response = await evolutionAPI.triggerEvolution(request);
      
      expect(response).toBeDefined();
      expect(response.requestId).toBe(request.id);
      expect(response.status).toBe('queued');
      expect(response.estimatedDuration).toBeGreaterThan(0);

      // Vérification du statut
      const status = evolutionAPI.getEvolutionStatus(request.id);
      expect(status.requestId).toBe(request.id);
      expect(['queued', 'running']).toContain(status.status);
    });

    test('should prioritize critical requests', async () => {
      const criticalRequest: EvolutionRequest = {
        id: 'critical-evolution',
        type: EvolutionType.BUG_FIXING,
        priority: Priority.CRITICAL,
        target: {
          problem: 'Fix critical security vulnerability',
          domain: 'security',
          context: { severity: 'critical' }
        },
        objectives: [{ metric: 'correctness', weight: 1.0, target: 1.0 }],
        constraints: { maxExecutionTime: 60000 },
        config: { populationSize: 20, maxGenerations: 50 }
      };

      const normalRequest: EvolutionRequest = {
        id: 'normal-evolution',
        type: EvolutionType.PERFORMANCE_TUNING,
        priority: Priority.MEDIUM,
        target: {
          problem: 'Optimize database queries',
          domain: 'database',
          context: {}
        },
        objectives: [{ metric: 'performance', weight: 1.0, target: 0.8 }],
        constraints: { maxExecutionTime: 300000 },
        config: { populationSize: 30, maxGenerations: 75 }
      };

      // Soumission des demandes (normale en premier)
      await evolutionAPI.triggerEvolution(normalRequest);
      await evolutionAPI.triggerEvolution(criticalRequest);

      // La demande critique devrait être traitée en premier
      const metrics = evolutionAPI.getMetrics();
      expect(metrics.queueLength).toBeGreaterThan(0);
    });

    test('should handle concurrent evolution requests', async () => {
      const requests: EvolutionRequest[] = [];
      
      for (let i = 0; i < 5; i++) {
        requests.push({
          id: `concurrent-evolution-${i}`,
          type: EvolutionType.ALGORITHM_OPTIMIZATION,
          priority: Priority.MEDIUM,
          target: {
            problem: `Optimization problem ${i}`,
            domain: 'algorithms',
            context: { index: i }
          },
          objectives: [{ metric: 'performance', weight: 1.0, target: 0.8 }],
          constraints: { maxExecutionTime: 120000 },
          config: { populationSize: 20, maxGenerations: 30 }
        });
      }

      // Soumission simultanée
      const responses = await Promise.all(
        requests.map(req => evolutionAPI.triggerEvolution(req))
      );

      expect(responses).toHaveLength(5);
      responses.forEach((response, index) => {
        expect(response.requestId).toBe(`concurrent-evolution-${index}`);
        expect(response.status).toBe('queued');
      });

      // Vérification des métriques
      const metrics = evolutionAPI.getMetrics();
      expect(metrics.totalRequests).toBe(5);
      expect(metrics.queueLength).toBeGreaterThan(0);
    });
  });

  describe('Optimisation des Performances', () => {
    test('should optimize performance automatically', async () => {
      const initialMetrics = performanceOptimizer.getPerformanceMetrics();
      
      // Simulation de charge
      const solutions = Array.from({ length: 100 }, (_, i) => ({
        id: `solution-${i}`,
        code: `function test${i}() { return ${i}; }`,
        description: `Test solution ${i}`,
        approach: 'test',
        fitness: {
          total: Math.random(),
          performance: Math.random(),
          correctness: Math.random(),
          efficiency: Math.random(),
          robustness: Math.random(),
          maintainability: Math.random(),
          innovation: Math.random()
        },
        generation: 1,
        parentIds: [],
        mutations: [],
        performance: {
          executionTime: Math.random() * 100,
          memoryUsage: Math.random() * 1024,
          cpuUsage: Math.random() * 100,
          complexity: {
            timeComplexity: 'O(1)',
            spaceComplexity: 'O(1)',
            cyclomaticComplexity: 1,
            cognitiveComplexity: 1
          },
          scalability: Math.random()
        }
      }));

      // Évaluation parallèle
      const evaluationFunction = async (solution: any) => ({
        total: Math.random(),
        performance: Math.random(),
        correctness: Math.random(),
        efficiency: Math.random(),
        robustness: Math.random(),
        maintainability: Math.random(),
        innovation: Math.random()
      });

      const startTime = Date.now();
      const results = await performanceOptimizer.evaluatePopulation(solutions, evaluationFunction);
      const executionTime = Date.now() - startTime;

      expect(results).toHaveLength(solutions.length);
      expect(executionTime).toBeLessThan(10000); // Moins de 10 secondes

      const finalMetrics = performanceOptimizer.getPerformanceMetrics();
      expect(finalMetrics.cache.size).toBeGreaterThanOrEqual(0);
      expect(finalMetrics.workerPool.totalWorkers).toBeGreaterThan(0);
    });

    test('should cache evaluation results', async () => {
      const solution = {
        id: 'cache-test-solution',
        code: 'function cacheTest() { return 42; }',
        description: 'Cache test solution',
        approach: 'test',
        fitness: {
          total: 0.8,
          performance: 0.8,
          correctness: 0.9,
          efficiency: 0.7,
          robustness: 0.8,
          maintainability: 0.8,
          innovation: 0.6
        },
        generation: 1,
        parentIds: [],
        mutations: [],
        performance: {
          executionTime: 50,
          memoryUsage: 256,
          cpuUsage: 30,
          complexity: {
            timeComplexity: 'O(1)',
            spaceComplexity: 'O(1)',
            cyclomaticComplexity: 1,
            cognitiveComplexity: 1
          },
          scalability: 0.8
        }
      };

      const evaluationFunction = async () => ({
        total: 0.8,
        performance: 0.8,
        correctness: 0.9,
        efficiency: 0.7,
        robustness: 0.8,
        maintainability: 0.8,
        innovation: 0.6
      });

      // Première évaluation
      const firstEvaluation = await performanceOptimizer.evaluatePopulation([solution], evaluationFunction);
      
      // Deuxième évaluation (devrait utiliser le cache)
      const secondEvaluation = await performanceOptimizer.evaluatePopulation([solution], evaluationFunction);

      expect(firstEvaluation).toEqual(secondEvaluation);
      
      const metrics = performanceOptimizer.getPerformanceMetrics();
      expect(metrics.cache.hitRate).toBeGreaterThan(0);
    });

    test('should handle memory optimization', async () => {
      const initialMetrics = performanceOptimizer.getPerformanceMetrics();
      
      // Simulation d'utilisation mémoire élevée
      const largeSolutions = Array.from({ length: 1000 }, (_, i) => ({
        id: `large-solution-${i}`,
        code: `function large${i}() { ${'return ' + i + ';'.repeat(100)} }`,
        description: `Large solution ${i}`,
        approach: 'memory-intensive',
        fitness: {
          total: Math.random(),
          performance: Math.random(),
          correctness: Math.random(),
          efficiency: Math.random(),
          robustness: Math.random(),
          maintainability: Math.random(),
          innovation: Math.random()
        },
        generation: 1,
        parentIds: [],
        mutations: [],
        performance: {
          executionTime: Math.random() * 100,
          memoryUsage: Math.random() * 2048,
          cpuUsage: Math.random() * 100,
          complexity: {
            timeComplexity: 'O(n)',
            spaceComplexity: 'O(n)',
            cyclomaticComplexity: 5,
            cognitiveComplexity: 4
          },
          scalability: Math.random()
        }
      }));

      const evaluationFunction = async () => ({
        total: Math.random(),
        performance: Math.random(),
        correctness: Math.random(),
        efficiency: Math.random(),
        robustness: Math.random(),
        maintainability: Math.random(),
        innovation: Math.random()
      });

      // L'optimiseur devrait gérer la mémoire automatiquement
      const results = await performanceOptimizer.evaluatePopulation(largeSolutions, evaluationFunction);
      
      expect(results).toHaveLength(largeSolutions.length);
      
      const finalMetrics = performanceOptimizer.getPerformanceMetrics();
      expect(finalMetrics.memory.usage).toBeLessThan(1.0); // Pas de dépassement
    });
  });

  describe('Dashboard et Monitoring', () => {
    test('should provide system status', async () => {
      const systemStatus = await dashboard.getSystemStatus();

      expect(systemStatus).toBeDefined();
      expect(systemStatus.timestamp).toBeInstanceOf(Date);
      expect(systemStatus.health).toBeDefined();
      expect(systemStatus.genetic).toBeDefined();
      expect(systemStatus.performance).toBeDefined();
      expect(systemStatus.api).toBeDefined();
      expect(systemStatus.alerts).toBeDefined();

      expect(systemStatus.health.status).toMatch(/healthy|warning|critical/);
      expect(systemStatus.health.score).toBeGreaterThanOrEqual(0);
      expect(systemStatus.health.score).toBeLessThanOrEqual(100);
    });

    test('should generate evolution report', async () => {
      const timeRange = {
        start: new Date(Date.now() - 3600000), // 1 heure
        end: new Date()
      };

      const report = await dashboard.generateEvolutionReport(timeRange);

      expect(report).toBeDefined();
      expect(report.timestamp).toBeInstanceOf(Date);
      expect(report.timeRange).toEqual(timeRange);
      expect(report.phylogenetic).toBeDefined();
      expect(report.performance).toBeDefined();
      expect(report.convergence).toBeDefined();
      expect(report.diversity).toBeDefined();
      expect(Array.isArray(report.recommendations)).toBe(true);
    });

    test('should provide real-time metrics', async () => {
      const realTimeMetrics = await dashboard.getRealTimeMetrics();

      expect(realTimeMetrics).toBeDefined();
      expect(realTimeMetrics.timestamp).toBeInstanceOf(Date);
      expect(realTimeMetrics.system).toBeDefined();
      expect(Array.isArray(realTimeMetrics.evolutions)).toBe(true);
      expect(Array.isArray(realTimeMetrics.alerts)).toBe(true);
      expect(realTimeMetrics.performance).toBeDefined();
    });

    test('should generate visualizations', async () => {
      const visualizations = await dashboard.generateVisualizations();

      expect(visualizations).toBeDefined();
      expect(visualizations.convergence).toBeDefined();
      expect(visualizations.diversity).toBeDefined();
      expect(visualizations.performance).toBeDefined();
      expect(visualizations.phylogenetic).toBeDefined();
      expect(visualizations.generatedAt).toBeInstanceOf(Date);

      // Vérification de la structure des graphiques
      expect(visualizations.convergence.type).toBeDefined();
      expect(visualizations.diversity.type).toBeDefined();
      expect(visualizations.performance.type).toBeDefined();
    });

    test('should export data in different formats', async () => {
      const timeRange = {
        start: new Date(Date.now() - 3600000),
        end: new Date()
      };

      // Export JSON
      const jsonExport = await dashboard.exportData('json', timeRange);
      expect(jsonExport.format).toBe('json');
      expect(jsonExport.filename).toContain('.json');
      expect(jsonExport.data).toBeDefined();

      // Export CSV
      const csvExport = await dashboard.exportData('csv', timeRange);
      expect(csvExport.format).toBe('csv');
      expect(csvExport.filename).toContain('.csv');
      expect(csvExport.data).toBeDefined();

      // Export PDF
      const pdfExport = await dashboard.exportData('pdf', timeRange);
      expect(pdfExport.format).toBe('pdf');
      expect(pdfExport.filename).toContain('.pdf');
      expect(pdfExport.data).toBeDefined();
    });
  });

  describe('Orchestration Intelligente', () => {
    test('should initialize orchestrator successfully', async () => {
      const systemState = orchestrator.getSystemState();

      expect(systemState.initialized).toBe(true);
      expect(systemState.health).toMatch(/healthy|warning|degraded/);
      expect(systemState.activeEvolutions).toBe(0);
      expect(systemState.totalCapacity).toBeGreaterThan(0);
      expect(systemState.lastUpdate).toBeInstanceOf(Date);
    });

    test('should optimize system automatically', async () => {
      const optimizationResult = await orchestrator.optimizeSystem();

      expect(optimizationResult).toBeDefined();
      expect(optimizationResult.timestamp).toBeInstanceOf(Date);
      expect(Array.isArray(optimizationResult.optimizations)).toBe(true);
      expect(optimizationResult.newConfiguration).toBeDefined();
      expect(optimizationResult.expectedImpact).toBeDefined();
    });

    test('should handle system shutdown gracefully', async () => {
      const initialState = orchestrator.getSystemState();
      expect(initialState.health).not.toBe('stopped');

      await orchestrator.shutdown();

      // Le système devrait être arrêté proprement
      // Note: après shutdown, getSystemState pourrait ne plus être disponible
      expect(true).toBe(true); // Test de non-régression
    });
  });

  describe('Intégration End-to-End', () => {
    test('should handle complete evolution workflow', async () => {
      const request: EvolutionRequest = {
        id: 'e2e-evolution-test',
        type: EvolutionType.ALGORITHM_OPTIMIZATION,
        priority: Priority.HIGH,
        target: {
          problem: 'End-to-end optimization test',
          domain: 'testing',
          context: { testType: 'integration' }
        },
        objectives: [
          { metric: 'performance', weight: 0.5, target: 0.8 },
          { metric: 'correctness', weight: 0.5, target: 0.9 }
        ],
        constraints: {
          maxExecutionTime: 60000, // 1 minute
          maxMemoryUsage: 512,
          maxComplexity: 5
        },
        config: {
          populationSize: 10,
          maxGenerations: 20,
          mutationRate: 0.1,
          crossoverRate: 0.7
        }
      };

      // 1. Soumission de la demande
      const response = await evolutionAPI.triggerEvolution(request);
      expect(response.status).toBe('queued');

      // 2. Monitoring du statut
      let status = evolutionAPI.getEvolutionStatus(request.id);
      expect(['queued', 'running']).toContain(status.status);

      // 3. Vérification des métriques système
      const systemStatus = await dashboard.getSystemStatus();
      expect(systemStatus.api.totalRequests).toBeGreaterThan(0);

      // 4. Vérification de l'état de l'orchestrateur
      const orchestratorState = orchestrator.getSystemState();
      expect(orchestratorState.initialized).toBe(true);

      // 5. Optimisation du système
      const optimization = await orchestrator.optimizeSystem();
      expect(optimization.optimizations.length).toBeGreaterThanOrEqual(0);
    });

    test('should maintain performance under load', async () => {
      const startTime = Date.now();
      const requests: Promise<any>[] = [];

      // Génération de charge
      for (let i = 0; i < 10; i++) {
        const request: EvolutionRequest = {
          id: `load-test-${i}`,
          type: EvolutionType.PERFORMANCE_TUNING,
          priority: Priority.MEDIUM,
          target: {
            problem: `Load test ${i}`,
            domain: 'performance',
            context: { loadTest: true, index: i }
          },
          objectives: [{ metric: 'performance', weight: 1.0, target: 0.8 }],
          constraints: { maxExecutionTime: 30000 },
          config: { populationSize: 5, maxGenerations: 10 }
        };

        requests.push(evolutionAPI.triggerEvolution(request));
      }

      // Attente de toutes les soumissions
      const responses = await Promise.all(requests);
      const submissionTime = Date.now() - startTime;

      expect(responses).toHaveLength(10);
      expect(submissionTime).toBeLessThan(5000); // Moins de 5 secondes

      // Vérification que le système reste stable
      const systemStatus = await dashboard.getSystemStatus();
      expect(systemStatus.health.status).not.toBe('critical');

      const metrics = evolutionAPI.getMetrics();
      expect(metrics.totalRequests).toBe(10);
    });
  });
});
