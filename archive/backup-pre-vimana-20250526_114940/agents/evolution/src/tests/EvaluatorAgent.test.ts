import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { <PERSON><PERSON> } from 'winston';
import { EvaluatorAgent } from '../agents/EvaluatorAgent';
import { EvolutionSolution } from '../types/evolution';

// Mock du logger
const mockLogger = {
  info: jest.fn(),
  debug: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
} as unknown as <PERSON><PERSON>;

describe('EvaluatorAgent', () => {
  let evaluatorAgent: EvaluatorAgent;

  beforeEach(() => {
    evaluatorAgent = new EvaluatorAgent(mockLogger);
    jest.clearAllMocks();
  });

  const mockSolution: EvolutionSolution = {
    id: 'solution-1',
    code: `
function quickSort(arr) {
  if (arr.length <= 1) return arr;
  
  const pivot = arr[Math.floor(arr.length / 2)];
  const left = arr.filter(x => x < pivot);
  const middle = arr.filter(x => x === pivot);
  const right = arr.filter(x => x > pivot);
  
  return [...quickSort(left), ...middle, ...quickSort(right)];
}`,
    description: 'Quick sort implementation',
    approach: 'divide-and-conquer',
    fitness: {
      total: 0,
      performance: 0,
      correctness: 0,
      efficiency: 0,
      robustness: 0,
      maintainability: 0,
      innovation: 0
    },
    generation: 1,
    parentIds: [],
    mutations: [],
    performance: {
      executionTime: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      complexity: {
        timeComplexity: 'O(n log n)',
        spaceComplexity: 'O(log n)',
        cyclomaticComplexity: 5,
        cognitiveComplexity: 3
      },
      scalability: 0
    }
  };

  const mockRequest = {
    problem: 'Sort array efficiently',
    domain: 'sorting',
    metrics: ['performance', 'correctness'],
    constraints: {},
    config: {} as any
  };

  describe('evaluateSolution', () => {
    it('should evaluate a solution and return updated fitness scores', async () => {
      const evaluatedSolution = await evaluatorAgent.evaluateSolution(mockSolution, mockRequest);

      expect(evaluatedSolution.id).toBe(mockSolution.id);
      expect(evaluatedSolution.fitness.total).toBeGreaterThan(0);
      expect(evaluatedSolution.fitness.correctness).toBeGreaterThanOrEqual(0);
      expect(evaluatedSolution.fitness.performance).toBeGreaterThanOrEqual(0);
    });

    it('should emit solution-evaluated event', async () => {
      const eventSpy = jest.fn();
      evaluatorAgent.on('solution-evaluated', eventSpy);

      await evaluatorAgent.evaluateSolution(mockSolution, mockRequest);

      expect(eventSpy).toHaveBeenCalledWith({
        solutionId: mockSolution.id,
        fitness: expect.any(Object),
        evaluationTime: expect.any(Number)
      });
    });

    it('should update performance metrics', async () => {
      const evaluatedSolution = await evaluatorAgent.evaluateSolution(mockSolution, mockRequest);

      expect(evaluatedSolution.performance.executionTime).toBeGreaterThanOrEqual(0);
      expect(evaluatedSolution.performance.memoryUsage).toBeGreaterThanOrEqual(0);
      expect(evaluatedSolution.performance.scalability).toBeGreaterThanOrEqual(0);
    });

    it('should handle evaluation errors gracefully', async () => {
      const problematicSolution = {
        ...mockSolution,
        code: 'invalid javascript syntax {'
      };

      // Ne devrait pas lever d'exception
      const evaluatedSolution = await evaluatorAgent.evaluateSolution(problematicSolution, mockRequest);
      
      expect(evaluatedSolution).toBeDefined();
      expect(evaluatedSolution.fitness.total).toBeGreaterThanOrEqual(0);
    });
  });

  describe('correctness evaluation', () => {
    it('should evaluate correctness with test cases', async () => {
      const sortingSolution = {
        ...mockSolution,
        code: `
function sort(arr) {
  return arr.sort((a, b) => a - b);
}`
      };

      const evaluatedSolution = await evaluatorAgent.evaluateSolution(sortingSolution, {
        ...mockRequest,
        domain: 'sorting'
      });

      expect(evaluatedSolution.fitness.correctness).toBeGreaterThan(0);
      expect(evaluatedSolution.fitness.correctness).toBeLessThanOrEqual(1);
    });

    it('should handle test case failures', async () => {
      const faultySolution = {
        ...mockSolution,
        code: `
function sort(arr) {
  return arr; // Ne trie pas vraiment
}`
      };

      const evaluatedSolution = await evaluatorAgent.evaluateSolution(faultySolution, mockRequest);

      // Devrait détecter que la solution ne fonctionne pas correctement
      expect(evaluatedSolution.fitness.correctness).toBeLessThan(1);
    });

    it('should test edge cases', async () => {
      const evaluatedSolution = await evaluatorAgent.evaluateSolution(mockSolution, mockRequest);

      // L'évaluation devrait inclure des tests de cas limites
      expect(evaluatedSolution.fitness.robustness).toBeGreaterThanOrEqual(0);
    });
  });

  describe('performance evaluation', () => {
    it('should measure execution time', async () => {
      const evaluatedSolution = await evaluatorAgent.evaluateSolution(mockSolution, mockRequest);

      expect(evaluatedSolution.performance.executionTime).toBeGreaterThanOrEqual(0);
      expect(evaluatedSolution.fitness.performance).toBeGreaterThanOrEqual(0);
    });

    it('should evaluate memory usage', async () => {
      const memoryIntensiveSolution = {
        ...mockSolution,
        code: `
function processLargeArray(arr) {
  const copies = [];
  for (let i = 0; i < 1000; i++) {
    copies.push([...arr]);
  }
  return copies;
}`
      };

      const evaluatedSolution = await evaluatorAgent.evaluateSolution(memoryIntensiveSolution, mockRequest);

      expect(evaluatedSolution.performance.memoryUsage).toBeGreaterThanOrEqual(0);
    });

    it('should calculate scalability metrics', async () => {
      const evaluatedSolution = await evaluatorAgent.evaluateSolution(mockSolution, mockRequest);

      expect(evaluatedSolution.performance.scalability).toBeGreaterThanOrEqual(0);
      expect(evaluatedSolution.performance.scalability).toBeLessThanOrEqual(1);
    });
  });

  describe('code quality evaluation', () => {
    it('should evaluate readability', async () => {
      const readableSolution = {
        ...mockSolution,
        code: `
// This function sorts an array using quick sort algorithm
function quickSort(array) {
  // Base case: arrays with 0 or 1 element are already sorted
  if (array.length <= 1) {
    return array;
  }
  
  // Choose pivot element
  const pivot = array[Math.floor(array.length / 2)];
  
  // Partition array into three parts
  const left = array.filter(element => element < pivot);
  const middle = array.filter(element => element === pivot);
  const right = array.filter(element => element > pivot);
  
  // Recursively sort and combine
  return [...quickSort(left), ...middle, ...quickSort(right)];
}`
      };

      const evaluatedSolution = await evaluatorAgent.evaluateSolution(readableSolution, mockRequest);

      expect(evaluatedSolution.fitness.maintainability).toBeGreaterThan(0.5);
    });

    it('should evaluate maintainability', async () => {
      const wellStructuredSolution = {
        ...mockSolution,
        code: `
class Sorter {
  static quickSort(arr) {
    return this.sort(arr, 0, arr.length - 1);
  }
  
  static sort(arr, low, high) {
    if (low < high) {
      const pi = this.partition(arr, low, high);
      this.sort(arr, low, pi - 1);
      this.sort(arr, pi + 1, high);
    }
    return arr;
  }
  
  static partition(arr, low, high) {
    // Implementation details...
    return low;
  }
}`
      };

      const evaluatedSolution = await evaluatorAgent.evaluateSolution(wellStructuredSolution, mockRequest);

      expect(evaluatedSolution.fitness.maintainability).toBeGreaterThan(0);
    });

    it('should penalize high complexity', async () => {
      const complexSolution = {
        ...mockSolution,
        code: `
function complexFunction(data) {
  if (data) {
    if (data.length > 0) {
      for (let i = 0; i < data.length; i++) {
        if (data[i]) {
          if (data[i].value) {
            if (data[i].value > 0) {
              if (data[i].value < 100) {
                // Deeply nested logic
                return data[i].value;
              }
            }
          }
        }
      }
    }
  }
  return null;
}`
      };

      const evaluatedSolution = await evaluatorAgent.evaluateSolution(complexSolution, mockRequest);

      // Devrait pénaliser la complexité élevée
      expect(evaluatedSolution.fitness.maintainability).toBeLessThan(0.8);
    });
  });

  describe('robustness evaluation', () => {
    it('should test edge cases', async () => {
      const evaluatedSolution = await evaluatorAgent.evaluateSolution(mockSolution, mockRequest);

      expect(evaluatedSolution.fitness.robustness).toBeGreaterThanOrEqual(0);
      expect(evaluatedSolution.fitness.robustness).toBeLessThanOrEqual(1);
    });

    it('should perform stress testing', async () => {
      const evaluatedSolution = await evaluatorAgent.evaluateSolution(mockSolution, mockRequest);

      // L'évaluation devrait inclure des tests de stress
      expect(evaluatedSolution.fitness.robustness).toBeDefined();
    });

    it('should handle null and undefined inputs', async () => {
      const robustSolution = {
        ...mockSolution,
        code: `
function safeSort(arr) {
  if (!arr || !Array.isArray(arr)) {
    return [];
  }
  return arr.sort((a, b) => a - b);
}`
      };

      const evaluatedSolution = await evaluatorAgent.evaluateSolution(robustSolution, mockRequest);

      // Devrait récompenser la gestion des cas d'erreur
      expect(evaluatedSolution.fitness.robustness).toBeGreaterThan(0.5);
    });
  });

  describe('innovation evaluation', () => {
    it('should evaluate novelty of approach', async () => {
      const innovativeSolution = {
        ...mockSolution,
        code: `
function hybridSort(arr) {
  // Novel hybrid approach combining multiple algorithms
  if (arr.length < 10) {
    return insertionSort(arr);
  } else if (arr.length < 100) {
    return quickSort(arr);
  } else {
    return mergeSort(arr);
  }
}`
      };

      const evaluatedSolution = await evaluatorAgent.evaluateSolution(innovativeSolution, mockRequest);

      expect(evaluatedSolution.fitness.innovation).toBeGreaterThanOrEqual(0);
      expect(evaluatedSolution.fitness.innovation).toBeLessThanOrEqual(1);
    });

    it('should reward creative solutions', async () => {
      const creativeSolution = {
        ...mockSolution,
        approach: 'creative-hybrid',
        code: `
function creativeSortWithMemoization(arr) {
  const memo = new Map();
  const key = JSON.stringify(arr);
  
  if (memo.has(key)) {
    return memo.get(key);
  }
  
  const result = arr.sort((a, b) => a - b);
  memo.set(key, result);
  return result;
}`
      };

      const evaluatedSolution = await evaluatorAgent.evaluateSolution(creativeSolution, mockRequest);

      expect(evaluatedSolution.fitness.innovation).toBeGreaterThan(0);
    });
  });

  describe('fitness score calculation', () => {
    it('should calculate balanced fitness scores', async () => {
      const evaluatedSolution = await evaluatorAgent.evaluateSolution(mockSolution, mockRequest);

      const fitness = evaluatedSolution.fitness;
      
      // Tous les scores devraient être dans la plage valide
      Object.values(fitness).forEach(score => {
        expect(score).toBeGreaterThanOrEqual(0);
        expect(score).toBeLessThanOrEqual(1);
      });

      // Le score total devrait être une combinaison pondérée
      expect(fitness.total).toBeGreaterThan(0);
      expect(fitness.total).toBeLessThanOrEqual(1);
    });

    it('should weight different aspects appropriately', async () => {
      const evaluatedSolution = await evaluatorAgent.evaluateSolution(mockSolution, mockRequest);

      const fitness = evaluatedSolution.fitness;
      
      // Correctness et performance devraient avoir un poids important
      expect(fitness.correctness).toBeDefined();
      expect(fitness.performance).toBeDefined();
      expect(fitness.efficiency).toBeDefined();
    });

    it('should handle perfect solutions', async () => {
      const perfectSolution = {
        ...mockSolution,
        code: `
function optimalSort(arr) {
  // Hypothetical perfect sorting algorithm
  return arr.sort((a, b) => a - b);
}`
      };

      const evaluatedSolution = await evaluatorAgent.evaluateSolution(perfectSolution, mockRequest);

      // Même une solution parfaite ne devrait pas dépasser 1.0
      expect(evaluatedSolution.fitness.total).toBeLessThanOrEqual(1);
    });
  });

  describe('evaluation history', () => {
    it('should track evaluation results', async () => {
      await evaluatorAgent.evaluateSolution(mockSolution, mockRequest);
      
      // L'historique devrait être maintenu (vérifié via les événements)
      expect(mockLogger.info).toHaveBeenCalled();
    });

    it('should handle multiple evaluations', async () => {
      const solution1 = await evaluatorAgent.evaluateSolution(mockSolution, mockRequest);
      const solution2 = await evaluatorAgent.evaluateSolution({
        ...mockSolution,
        id: 'solution-2'
      }, mockRequest);

      expect(solution1.fitness).toBeDefined();
      expect(solution2.fitness).toBeDefined();
    });
  });
});
