/**
 * Configuration globale pour les tests Jest
 * Agent Évolution AlphaEvolve
 */

import { Logger } from 'winston';

// Configuration des timeouts pour les tests longs
jest.setTimeout(30000);

// Mock logger pour les tests
export const mockLogger: Logger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  verbose: jest.fn(),
  silly: jest.fn(),
  log: jest.fn(),
  level: 'error',
  levels: {},
  transports: [],
  format: {},
  exitOnError: false,
  silent: false,
  exceptions: {
    handle: jest.fn(),
    unhandle: jest.fn(),
    getAllInfo: jest.fn(),
    getProcessInfo: jest.fn()
  },
  rejections: {
    handle: jest.fn(),
    unhandle: jest.fn(),
    getAllInfo: jest.fn(),
    getProcessInfo: jest.fn()
  },
  profiler: jest.fn(),
  startTimer: jest.fn(),
  add: jest.fn(),
  remove: jest.fn(),
  clear: jest.fn(),
  close: jest.fn(),
  configure: jest.fn(),
  child: jest.fn(),
  isLevelEnabled: jest.fn(),
  isDebugEnabled: jest.fn(),
  isInfoEnabled: jest.fn(),
  isWarnEnabled: jest.fn(),
  isErrorEnabled: jest.fn(),
  query: jest.fn(),
  stream: jest.fn(),
  on: jest.fn(),
  once: jest.fn(),
  emit: jest.fn(),
  addListener: jest.fn(),
  removeListener: jest.fn(),
  removeAllListeners: jest.fn(),
  setMaxListeners: jest.fn(),
  getMaxListeners: jest.fn(),
  listeners: jest.fn(),
  rawListeners: jest.fn(),
  listenerCount: jest.fn(),
  prependListener: jest.fn(),
  prependOnceListener: jest.fn(),
  eventNames: jest.fn()
} as any;

// Mock des modules externes si nécessaire
jest.mock('fs', () => ({
  promises: {
    readFile: jest.fn(),
    writeFile: jest.fn(),
    mkdir: jest.fn(),
    access: jest.fn()
  },
  existsSync: jest.fn(),
  readFileSync: jest.fn(),
  writeFileSync: jest.fn()
}));

// Configuration des variables d'environnement pour les tests
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error'; // Réduire les logs pendant les tests

// Suppression des warnings de deprecation pendant les tests
process.env.NO_DEPRECATION = 'true';

// Configuration globale pour les tests d'évolution
global.testConfig = {
  evolution: {
    populationSize: 5, // Réduit pour les tests
    maxGenerations: 3, // Réduit pour les tests
    convergenceThreshold: 0.1,
    timeout: 10000 // 10 secondes max par test
  }
};

// Utilitaires de test globaux
global.createMockSolution = (overrides = {}) => ({
  id: `test-solution-${Date.now()}`,
  code: 'function test() { return true; }',
  description: 'Test solution',
  approach: 'test-approach',
  fitness: {
    total: 0.5,
    performance: 0.5,
    correctness: 0.5,
    efficiency: 0.5,
    robustness: 0.5,
    maintainability: 0.5,
    innovation: 0.5
  },
  generation: 0,
  parentIds: [],
  mutations: [],
  performance: {
    executionTime: 100,
    memoryUsage: 1024,
    cpuUsage: 0.1,
    complexity: {
      timeComplexity: 'O(1)',
      spaceComplexity: 'O(1)',
      cyclomaticComplexity: 1,
      cognitiveComplexity: 1
    },
    scalability: 0.5
  },
  ...overrides
});

global.createMockRequest = (overrides = {}) => ({
  problem: 'Test problem',
  domain: 'test',
  metrics: ['performance', 'correctness'],
  constraints: {},
  config: {
    populationSize: 5,
    eliteRatio: 0.3,
    mutationRate: 0.1,
    crossoverRate: 0.7,
    maxGenerations: 3,
    convergenceThreshold: 0.1,
    fitnessWeights: {
      performance: 0.3,
      correctness: 0.3,
      efficiency: 0.2,
      robustness: 0.1,
      maintainability: 0.05,
      innovation: 0.05
    },
    selectionStrategy: 'tournament'
  },
  ...overrides
});

// Nettoyage après chaque test
afterEach(() => {
  jest.clearAllMocks();
});

// Nettoyage après tous les tests
afterAll(() => {
  jest.restoreAllMocks();
});

// Gestion des erreurs non capturées pendant les tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// Configuration des matchers Jest personnalisés
expect.extend({
  toBeValidFitnessScore(received) {
    const pass = typeof received === 'number' && received >= 0 && received <= 1;
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid fitness score`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid fitness score (0-1)`,
        pass: false,
      };
    }
  },

  toBeValidEvolutionSolution(received) {
    const requiredFields = ['id', 'code', 'description', 'approach', 'fitness', 'generation', 'parentIds', 'mutations', 'performance'];
    const hasAllFields = requiredFields.every(field => received.hasOwnProperty(field));

    if (hasAllFields) {
      return {
        message: () => `expected solution not to have all required fields`,
        pass: true,
      };
    } else {
      const missingFields = requiredFields.filter(field => !received.hasOwnProperty(field));
      return {
        message: () => `expected solution to have all required fields. Missing: ${missingFields.join(', ')}`,
        pass: false,
      };
    }
  },

  toHaveImprovedFitness(received, original) {
    const pass = received.fitness.total >= original.fitness.total;
    if (pass) {
      return {
        message: () => `expected fitness ${received.fitness.total} not to be improved from ${original.fitness.total}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected fitness ${received.fitness.total} to be improved from ${original.fitness.total}`,
        pass: false,
      };
    }
  }
});

// Types pour TypeScript
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidFitnessScore(): R;
      toBeValidEvolutionSolution(): R;
      toHaveImprovedFitness(original: any): R;
    }
  }

  var testConfig: {
    evolution: {
      populationSize: number;
      maxGenerations: number;
      convergenceThreshold: number;
      timeout: number;
    };
  };

  var createMockSolution: (overrides?: any) => any;
  var createMockRequest: (overrides?: any) => any;
}

export {};
