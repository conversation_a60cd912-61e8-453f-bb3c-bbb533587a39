import { Logger } from 'winston';
import { 
  ValidationResult,
  ValidationError,
  ValidationWarning,
  GeneratedCode,
  ComprehensiveUXDesign
} from '../types';

/**
 * Validateur de Code Frontend
 * 
 * Valide le code généré pour s'assurer qu'il respecte
 * les standards de qualité, d'accessibilité et de performance.
 */
export class CodeValidator {
  private logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  /**
   * Valide le code généré
   */
  async validateCode(
    generatedCode: GeneratedCode,
    originalDesign: ComprehensiveUXDesign
  ): Promise<ValidationResult> {
    this.logger.info('Validation du code généré', { 
      framework: generatedCode.framework,
      filesCount: generatedCode.files.length 
    });

    try {
      const errors: ValidationError[] = [];
      const warnings: ValidationWarning[] = [];

      // 1. Validation syntaxique
      const syntaxValidation = await this.validateSyntax(generatedCode);
      errors.push(...syntaxValidation.errors);
      warnings.push(...syntaxValidation.warnings);

      // 2. Validation de conformité au design
      const designValidation = await this.validateDesignCompliance(generatedCode, originalDesign);
      errors.push(...designValidation.errors);
      warnings.push(...designValidation.warnings);

      // 3. Validation d'accessibilité
      const accessibilityValidation = await this.validateAccessibility(generatedCode);
      errors.push(...accessibilityValidation.errors);
      warnings.push(...accessibilityValidation.warnings);

      // 4. Validation de performance
      const performanceValidation = await this.validatePerformance(generatedCode);
      errors.push(...performanceValidation.errors);
      warnings.push(...performanceValidation.warnings);

      // 5. Validation de sécurité
      const securityValidation = await this.validateSecurity(generatedCode);
      errors.push(...securityValidation.errors);
      warnings.push(...securityValidation.warnings);

      // 6. Validation de la qualité du code
      const qualityValidation = await this.validateCodeQuality(generatedCode);
      errors.push(...qualityValidation.errors);
      warnings.push(...qualityValidation.warnings);

      // Calcul du score global
      const score = this.calculateOverallScore(
        syntaxValidation,
        designValidation,
        accessibilityValidation,
        performanceValidation,
        securityValidation,
        qualityValidation
      );

      const result: ValidationResult = {
        isValid: errors.length === 0,
        errors,
        warnings,
        suggestions: await this.generateSuggestions(generatedCode, originalDesign),
        score,
        metrics: {
          designCompliance: designValidation.score,
          accessibilityScore: accessibilityValidation.score,
          performanceScore: performanceValidation.score,
          securityScore: securityValidation.score,
          codeQualityScore: qualityValidation.score,
          testCoverageScore: await this.calculateTestCoverage(generatedCode)
        }
      };

      this.logger.info('Validation terminée', { 
        isValid: result.isValid,
        errorsCount: errors.length,
        warningsCount: warnings.length,
        score: result.score
      });

      return result;

    } catch (error) {
      this.logger.error('Erreur lors de la validation', { error: error.message });
      throw error;
    }
  }

  /**
   * Analyse le code existant
   */
  async analyzeExistingCode(code: string, framework: string): Promise<any> {
    this.logger.info('Analyse du code existant', { framework });

    try {
      return {
        framework,
        complexity: await this.calculateComplexity(code),
        maintainability: await this.calculateMaintainability(code),
        testCoverage: await this.analyzeTestCoverage(code),
        dependencies: await this.analyzeDependencies(code),
        performance: await this.analyzePerformance(code),
        security: await this.analyzeSecurity(code),
        accessibility: await this.analyzeAccessibility(code),
        qualityScore: await this.calculateQualityScore(code)
      };
    } catch (error) {
      this.logger.error('Erreur lors de l\'analyse du code', { error: error.message });
      throw error;
    }
  }

  /**
   * Vérifie la conformité au design
   */
  async checkDesignCompliance(codeAnalysis: any, design: ComprehensiveUXDesign): Promise<any> {
    this.logger.info('Vérification de la conformité au design');

    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    let score = 1.0;

    // Vérifier les couleurs du design system
    const colorCompliance = await this.checkColorCompliance(codeAnalysis, design.designSystem);
    if (colorCompliance.score < 0.8) {
      errors.push({
        type: 'design-compliance',
        severity: 'warning',
        message: 'Couleurs du design system non respectées',
        file: 'styles',
        rule: 'design-system-colors',
        fix: 'Utiliser les couleurs définies dans le design system'
      });
    }

    // Vérifier la typographie
    const typographyCompliance = await this.checkTypographyCompliance(codeAnalysis, design.designSystem);
    if (typographyCompliance.score < 0.8) {
      warnings.push({
        type: 'typography',
        message: 'Typographie non conforme au design system',
        file: 'styles',
        suggestion: 'Utiliser les polices et tailles définies dans le design system'
      });
    }

    // Vérifier l'espacement
    const spacingCompliance = await this.checkSpacingCompliance(codeAnalysis, design.designSystem);
    if (spacingCompliance.score < 0.8) {
      warnings.push({
        type: 'spacing',
        message: 'Espacement non conforme au design system',
        file: 'styles',
        suggestion: 'Utiliser l\'échelle d\'espacement définie'
      });
    }

    score = (colorCompliance.score + typographyCompliance.score + spacingCompliance.score) / 3;

    return { errors, warnings, score };
  }

  /**
   * Validation syntaxique
   */
  private async validateSyntax(generatedCode: GeneratedCode): Promise<any> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    for (const file of generatedCode.files) {
      try {
        // Validation basique de la syntaxe selon le type de fichier
        if (file.language === 'typescript' || file.language === 'javascript') {
          await this.validateJSXSyntax(file.content, file.path);
        } else if (file.language === 'css' || file.language === 'scss') {
          await this.validateCSSSyntax(file.content, file.path);
        }
      } catch (error) {
        errors.push({
          type: 'syntax',
          severity: 'error',
          message: error.message,
          file: file.path,
          rule: 'syntax-validation',
          fix: 'Corriger la syntaxe du fichier'
        });
      }
    }

    return { errors, warnings, score: errors.length === 0 ? 1.0 : 0.5 };
  }

  /**
   * Validation d'accessibilité
   */
  async validateAccessibility(codeOrString: GeneratedCode | string): Promise<any> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    let score = 1.0;

    const code = typeof codeOrString === 'string' ? codeOrString : 
                 codeOrString.files.map(f => f.content).join('\n');

    // Vérifier les attributs alt sur les images
    if (!this.checkAltAttributes(code)) {
      errors.push({
        type: 'accessibility',
        severity: 'error',
        message: 'Images sans attribut alt détectées',
        file: 'components',
        rule: 'img-alt',
        fix: 'Ajouter des attributs alt descriptifs à toutes les images'
      });
      score -= 0.2;
    }

    // Vérifier les labels de formulaire
    if (!this.checkFormLabels(code)) {
      errors.push({
        type: 'accessibility',
        severity: 'error',
        message: 'Champs de formulaire sans label détectés',
        file: 'components',
        rule: 'form-labels',
        fix: 'Associer tous les champs de formulaire avec des labels'
      });
      score -= 0.2;
    }

    // Vérifier la structure des titres
    if (!this.checkHeadingStructure(code)) {
      warnings.push({
        type: 'heading-structure',
        message: 'Structure de titres non hiérarchique',
        file: 'components',
        suggestion: 'Utiliser une hiérarchie logique des titres (h1, h2, h3...)'
      });
      score -= 0.1;
    }

    // Vérifier les attributs ARIA
    if (!this.checkAriaAttributes(code)) {
      warnings.push({
        type: 'aria-attributes',
        message: 'Attributs ARIA manquants ou incorrects',
        file: 'components',
        suggestion: 'Ajouter les attributs ARIA appropriés pour améliorer l\'accessibilité'
      });
      score -= 0.1;
    }

    return { errors, warnings, score: Math.max(0, score) };
  }

  /**
   * Validation de performance
   */
  async validatePerformance(codeOrString: GeneratedCode | string): Promise<any> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    let score = 1.0;

    const code = typeof codeOrString === 'string' ? codeOrString : 
                 codeOrString.files.map(f => f.content).join('\n');

    // Vérifier l'utilisation du lazy loading
    if (!this.checkLazyLoading(code)) {
      warnings.push({
        type: 'performance',
        message: 'Lazy loading non implémenté pour les images',
        file: 'components',
        suggestion: 'Ajouter loading="lazy" aux images non critiques'
      });
      score -= 0.1;
    }

    // Vérifier l'optimisation des bundles
    if (!this.checkBundleOptimization(code)) {
      warnings.push({
        type: 'performance',
        message: 'Optimisation des bundles manquante',
        file: 'build',
        suggestion: 'Implémenter le code splitting et la compression'
      });
      score -= 0.1;
    }

    // Vérifier les imports inutilisés
    const unusedImports = this.findUnusedImports(code);
    if (unusedImports.length > 0) {
      warnings.push({
        type: 'performance',
        message: `${unusedImports.length} imports inutilisés détectés`,
        file: 'components',
        suggestion: 'Supprimer les imports non utilisés'
      });
      score -= 0.05;
    }

    return { errors, warnings, score: Math.max(0, score) };
  }

  /**
   * Validation de sécurité
   */
  async validateSecurity(codeOrString: GeneratedCode | string): Promise<any> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    let score = 1.0;

    const code = typeof codeOrString === 'string' ? codeOrString : 
                 codeOrString.files.map(f => f.content).join('\n');

    // Vérifier l'utilisation de dangerouslySetInnerHTML
    if (this.checkDangerousHTML(code)) {
      errors.push({
        type: 'security',
        severity: 'warning',
        message: 'Utilisation de dangerouslySetInnerHTML détectée',
        file: 'components',
        rule: 'no-dangerous-html',
        fix: 'Éviter dangerouslySetInnerHTML ou sanitiser le contenu'
      });
      score -= 0.2;
    }

    // Vérifier les clés API exposées
    if (this.checkExposedSecrets(code)) {
      errors.push({
        type: 'security',
        severity: 'error',
        message: 'Clés API ou secrets potentiellement exposés',
        file: 'config',
        rule: 'no-exposed-secrets',
        fix: 'Utiliser des variables d\'environnement pour les secrets'
      });
      score -= 0.3;
    }

    return { errors, warnings, score: Math.max(0, score) };
  }

  /**
   * Validation de la qualité du code
   */
  private async validateCodeQuality(generatedCode: GeneratedCode): Promise<any> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    let score = 1.0;

    // Vérifier la complexité cyclomatique
    const complexity = await this.calculateComplexity(generatedCode.files.map(f => f.content).join('\n'));
    if (complexity > 10) {
      warnings.push({
        type: 'complexity',
        message: 'Complexité cyclomatique élevée détectée',
        file: 'components',
        suggestion: 'Refactoriser pour réduire la complexité'
      });
      score -= 0.1;
    }

    // Vérifier la duplication de code
    const duplication = await this.checkCodeDuplication(generatedCode);
    if (duplication > 0.1) {
      warnings.push({
        type: 'duplication',
        message: 'Duplication de code détectée',
        file: 'components',
        suggestion: 'Extraire le code dupliqué dans des fonctions réutilisables'
      });
      score -= 0.1;
    }

    return { errors, warnings, score: Math.max(0, score) };
  }

  // Méthodes utilitaires de validation

  private async validateJSXSyntax(content: string, filePath: string): Promise<void> {
    // Validation basique de la syntaxe JSX
    const openTags = content.match(/<[^/][^>]*>/g) || [];
    const closeTags = content.match(/<\/[^>]*>/g) || [];
    
    if (openTags.length !== closeTags.length) {
      throw new Error(`Tags non fermés détectés dans ${filePath}`);
    }
  }

  private async validateCSSSyntax(content: string, filePath: string): Promise<void> {
    // Validation basique de la syntaxe CSS
    const openBraces = (content.match(/{/g) || []).length;
    const closeBraces = (content.match(/}/g) || []).length;
    
    if (openBraces !== closeBraces) {
      throw new Error(`Accolades non fermées détectées dans ${filePath}`);
    }
  }

  private checkAltAttributes(code: string): boolean {
    const imgTags = code.match(/<img[^>]*>/g) || [];
    return imgTags.every(tag => tag.includes('alt='));
  }

  private checkFormLabels(code: string): boolean {
    const inputTags = code.match(/<input[^>]*>/g) || [];
    return inputTags.every(tag => 
      tag.includes('aria-label=') || 
      code.includes(`<label`) // Simplification
    );
  }

  private checkHeadingStructure(code: string): boolean {
    const headings = code.match(/<h[1-6][^>]*>/g) || [];
    // Vérification simplifiée de la hiérarchie
    return headings.length > 0;
  }

  private checkAriaAttributes(code: string): boolean {
    // Vérification basique de la présence d'attributs ARIA
    return code.includes('aria-') || code.includes('role=');
  }

  private checkLazyLoading(code: string): boolean {
    const imgTags = code.match(/<img[^>]*>/g) || [];
    return imgTags.some(tag => tag.includes('loading="lazy"'));
  }

  private checkBundleOptimization(code: string): boolean {
    return code.includes('React.lazy') || code.includes('import(');
  }

  private findUnusedImports(code: string): string[] {
    // Logique simplifiée pour détecter les imports inutilisés
    return [];
  }

  private checkDangerousHTML(code: string): boolean {
    return code.includes('dangerouslySetInnerHTML');
  }

  private checkExposedSecrets(code: string): boolean {
    const secretPatterns = [
      /api[_-]?key/i,
      /secret/i,
      /token/i,
      /password/i
    ];
    return secretPatterns.some(pattern => pattern.test(code));
  }

  private async calculateComplexity(code: string): Promise<number> {
    // Calcul simplifié de la complexité cyclomatique
    const complexityKeywords = ['if', 'else', 'for', 'while', 'switch', 'case', '&&', '||'];
    let complexity = 1;
    
    complexityKeywords.forEach(keyword => {
      const matches = code.match(new RegExp(`\\b${keyword}\\b`, 'g'));
      if (matches) complexity += matches.length;
    });
    
    return complexity;
  }

  private async checkCodeDuplication(generatedCode: GeneratedCode): Promise<number> {
    // Calcul simplifié du taux de duplication
    return 0.05; // 5% de duplication simulée
  }

  private calculateOverallScore(...validations: any[]): number {
    const scores = validations.map(v => v.score).filter(s => typeof s === 'number');
    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }

  private async generateSuggestions(generatedCode: GeneratedCode, design: ComprehensiveUXDesign): Promise<string[]> {
    return [
      'Ajouter des tests unitaires pour les composants critiques',
      'Implémenter le lazy loading pour améliorer les performances',
      'Ajouter des attributs ARIA pour améliorer l\'accessibilité',
      'Optimiser les images avec des formats modernes (WebP, AVIF)',
      'Implémenter un système de cache pour les données API'
    ];
  }

  private async calculateTestCoverage(generatedCode: GeneratedCode): Promise<number> {
    const testFiles = generatedCode.files.filter(f => f.type === 'test');
    const componentFiles = generatedCode.files.filter(f => f.type === 'component');
    
    return componentFiles.length > 0 ? testFiles.length / componentFiles.length : 0;
  }

  // Méthodes d'analyse simplifiées
  private async calculateMaintainability(code: string): Promise<number> { return 0.8; }
  private async analyzeTestCoverage(code: string): Promise<number> { return 0.7; }
  private async analyzeDependencies(code: string): Promise<any> { return {}; }
  private async analyzePerformance(code: string): Promise<any> { return {}; }
  private async analyzeSecurity(code: string): Promise<any> { return {}; }
  private async analyzeAccessibility(code: string): Promise<any> { return {}; }
  private async calculateQualityScore(code: string): Promise<number> { return 0.85; }
  private async checkColorCompliance(analysis: any, designSystem: any): Promise<any> { return { score: 0.9 }; }
  private async checkTypographyCompliance(analysis: any, designSystem: any): Promise<any> { return { score: 0.85 }; }
  private async checkSpacingCompliance(analysis: any, designSystem: any): Promise<any> { return { score: 0.9 }; }
}
