export interface SecurityAgentConfig {
  id: string;
  name: string;
  version: string;
  capabilities: SecurityCapability[];
  scanners: ScannerConfig[];
  compliance: ComplianceConfig;
  threatIntelligence: ThreatIntelligenceConfig;
  monitoring: SecurityMonitoringConfig;
}

export type SecurityCapability =
  | 'vulnerability-scanning'
  | 'penetration-testing'
  | 'code-analysis'
  | 'dependency-scanning'
  | 'container-scanning'
  | 'infrastructure-scanning'
  | 'compliance-checking'
  | 'threat-intelligence'
  | 'incident-response'
  | 'security-monitoring'
  | 'malware-detection'
  | 'network-security'
  | 'web-security'
  | 'api-security'
  | 'data-protection'
  | 'identity-management'
  | 'access-control'
  | 'encryption'
  | 'forensics';

export interface ScannerConfig {
  name: string;
  type: ScannerType;
  enabled: boolean;
  configuration: any;
  schedule?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export type ScannerType =
  | 'sast' // Static Application Security Testing
  | 'dast' // Dynamic Application Security Testing
  | 'iast' // Interactive Application Security Testing
  | 'sca'  // Software Composition Analysis
  | 'container'
  | 'infrastructure'
  | 'network'
  | 'web'
  | 'api'
  | 'database'
  | 'cloud';

export interface SecurityScanRequest {
  id: string;
  type: ScannerType;
  target: ScanTarget;
  configuration: ScanConfiguration;
  priority: 'low' | 'medium' | 'high' | 'critical';
  requestedBy: string;
  timestamp: Date;
  context?: any;
}

export interface ScanTarget {
  type: 'code' | 'application' | 'infrastructure' | 'network' | 'container' | 'api';
  location: string;
  metadata: {
    language?: string;
    framework?: string;
    version?: string;
    environment?: string;
    [key: string]: any;
  };
}

export interface ScanConfiguration {
  depth: 'surface' | 'standard' | 'deep' | 'comprehensive';
  timeout: number;
  excludePatterns?: string[];
  includePatterns?: string[];
  customRules?: SecurityRule[];
  reportFormat: 'json' | 'xml' | 'html' | 'pdf' | 'sarif';
}

export interface SecurityRule {
  id: string;
  name: string;
  description: string;
  severity: VulnerabilitySeverity;
  category: SecurityCategory;
  pattern: string | RegExp;
  remediation: string;
  references: string[];
  cwe?: string;
  owasp?: string;
  mitre?: string;
}

export type VulnerabilitySeverity = 'info' | 'low' | 'medium' | 'high' | 'critical';

export type SecurityCategory =
  | 'injection'
  | 'authentication'
  | 'authorization'
  | 'data-exposure'
  | 'xml-external-entities'
  | 'broken-access-control'
  | 'security-misconfiguration'
  | 'cross-site-scripting'
  | 'insecure-deserialization'
  | 'vulnerable-components'
  | 'insufficient-logging'
  | 'cryptographic-failures'
  | 'server-side-request-forgery'
  | 'software-data-integrity'
  | 'security-logging-monitoring';

export interface SecurityScanResult {
  id: string;
  scanId: string;
  timestamp: Date;
  status: 'running' | 'completed' | 'failed' | 'partial';
  duration: number;
  summary: SecuritySummary;
  vulnerabilities: Vulnerability[];
  compliance: ComplianceResult[];
  recommendations: SecurityRecommendation[];
  metadata: any;
}

export interface SecuritySummary {
  totalVulnerabilities: number;
  criticalCount: number;
  highCount: number;
  mediumCount: number;
  lowCount: number;
  infoCount: number;
  riskScore: number; // 0-100
  complianceScore: number; // 0-100
  securityGrade: 'A' | 'B' | 'C' | 'D' | 'F';
}

export interface Vulnerability {
  id: string;
  title: string;
  description: string;
  severity: VulnerabilitySeverity;
  category: SecurityCategory;
  cwe?: string | undefined;
  cve?: string;
  owasp?: string;
  cvss?: CVSSScore | undefined;
  location: VulnerabilityLocation;
  evidence?: any;
  remediation: {
    description: string;
    effort: 'low' | 'medium' | 'high';
    priority: 'low' | 'medium' | 'high' | 'critical';
  };
  references: string[];
  discoveredAt: Date;
  status: 'open' | 'fixed' | 'suppressed' | 'false-positive';
  falsePositive?: boolean;
  suppressed?: boolean;
  firstFound?: Date;
  lastSeen?: Date;
  metadata?: any;
}

export interface CVSSScore {
  version: '3.1' | '3.0' | '2.0';
  baseScore: number;
  temporalScore?: number;
  environmentalScore?: number;
  vector: string;
  exploitability: number;
  impact: number;
}

export interface VulnerabilityLocation {
  file?: string;
  line?: number;
  column?: number;
  function?: string;
  url?: string;
  parameter?: string;
  method?: string;
  endpoint?: string;
  component?: string;
  version?: string;
  host?: string;
  port?: number;
  protocol?: string;
  service?: string;
  image?: string;
  layer?: string;
  package?: string;
  container?: string;
  configuration?: string;
  environment?: string;
  header?: string;
  cookie?: string;
  cloud?: string;
  region?: string;
  resource?: string;
}

export interface Evidence {
  type: 'code' | 'request' | 'response' | 'log' | 'screenshot' | 'network';
  content: string;
  metadata?: any;
}

export interface Remediation {
  description: string;
  steps: string[];
  effort: 'low' | 'medium' | 'high';
  priority: 'low' | 'medium' | 'high' | 'critical';
  automated: boolean;
  patches?: Patch[];
  workarounds?: string[];
}

export interface Patch {
  type: 'dependency' | 'code' | 'configuration';
  description: string;
  diff?: string;
  automated: boolean;
}

export interface ComplianceConfig {
  frameworks: ComplianceFramework[];
  customPolicies: CompliancePolicy[];
  reporting: ComplianceReporting;
}

export interface ComplianceFramework {
  name: string;
  version: string;
  enabled: boolean;
  controls: ComplianceControl[];
}

export interface ComplianceControl {
  id: string;
  name: string;
  description: string;
  category: string;
  mandatory: boolean;
  automated: boolean;
  checks: ComplianceCheck[];
}

export interface ComplianceCheck {
  id: string;
  name: string;
  description: string;
  query: string;
  expectedResult: any;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface CompliancePolicy {
  id: string;
  name: string;
  description: string;
  rules: PolicyRule[];
  enforcement: 'advisory' | 'blocking';
}

export interface PolicyRule {
  id: string;
  name: string;
  condition: string;
  action: 'allow' | 'deny' | 'warn' | 'log';
  message: string;
}

export interface ComplianceReporting {
  formats: ('json' | 'xml' | 'html' | 'pdf')[];
  schedule: string;
  recipients: string[];
  retention: number; // days
}

export interface ComplianceResult {
  frameworkName: string;
  controlId: string;
  status: 'compliant' | 'non-compliant' | 'not-applicable' | 'manual-review';
  score: number;
  findings: ComplianceFinding[];
  evidence: Evidence[];
  lastChecked: Date;
}

export interface ComplianceFinding {
  id: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  remediation: string;
  location?: string;
}

export interface ThreatIntelligenceConfig {
  sources: ThreatIntelSource[];
  feeds: ThreatFeed[];
  indicators: ThreatIndicator[];
  analysis: ThreatAnalysisConfig;
}

export interface ThreatIntelSource {
  name: string;
  type: 'commercial' | 'open-source' | 'government' | 'community';
  url: string;
  apiKey?: string;
  enabled: boolean;
  updateFrequency: string;
}

export interface ThreatFeed {
  name: string;
  source: string;
  type: 'ioc' | 'yara' | 'sigma' | 'stix' | 'taxii';
  format: string;
  lastUpdate: Date;
  indicators: number;
}

export interface ThreatIndicator {
  id: string;
  type: 'ip-address' | 'domain' | 'url' | 'file-hash' | 'email' | 'file' | 'registry';
  value: string;
  threatLevel: ThreatLevel;
  severity: ThreatLevel; // Alias pour compatibilité
  category: string;
  description: string;
  confidence: number;
  source: string;
  firstSeen: Date;
  lastSeen: Date;
  tags: string[];
  references: string[];
  context?: any;
}

export type ThreatLevel = 'low' | 'medium' | 'high' | 'critical';

export interface ThreatReport {
  id: string;
  timestamp: Date;
  threatLevel: ThreatLevel;
  category: SecurityCategory;
  description: string;
  indicators: ThreatIndicator[];
  recommendations: string[];
  confidence: number;
  source: string;
  metadata: {
    analysisTime: number;
    correlatedIndicators: number;
    riskScore: number;
  };
}

export interface ThreatAnalysisConfig {
  correlation: boolean;
  attribution: boolean;
  prediction: boolean;
  hunting: boolean;
  sandbox: SandboxConfig;
}

export interface SandboxConfig {
  enabled: boolean;
  type: 'cuckoo' | 'joe-sandbox' | 'hybrid-analysis' | 'any-run';
  timeout: number;
  environments: string[];
}

export interface SecurityMonitoringConfig {
  realTime: boolean;
  alerting: AlertingConfig;
  logging: SecurityLoggingConfig;
  metrics: SecurityMetricsConfig;
  dashboards: DashboardConfig[];
}

export interface AlertingConfig {
  enabled: boolean;
  channels: AlertChannel[];
  rules: AlertRule[];
  escalation: EscalationPolicy[];
}

export interface AlertChannel {
  type: 'email' | 'slack' | 'teams' | 'webhook' | 'sms' | 'pagerduty';
  configuration: any;
  enabled: boolean;
}

export interface AlertRule {
  id: string;
  name: string;
  condition: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  channels: string[];
  throttle: number;
}

export interface EscalationPolicy {
  id: string;
  name: string;
  levels: EscalationLevel[];
}

export interface EscalationLevel {
  delay: number; // minutes
  channels: string[];
  recipients: string[];
}

export interface SecurityLoggingConfig {
  level: 'debug' | 'info' | 'warn' | 'error';
  destinations: LogDestination[];
  retention: number; // days
  encryption: boolean;
}

export interface LogDestination {
  type: 'file' | 'syslog' | 'elasticsearch' | 'splunk' | 'cloudwatch';
  configuration: any;
  enabled: boolean;
}

export interface SecurityMetricsConfig {
  collection: boolean;
  retention: number; // days
  aggregation: string[];
  export: MetricsExportConfig[];
}

export interface MetricsExportConfig {
  type: 'prometheus' | 'graphite' | 'influxdb' | 'datadog';
  configuration: any;
  enabled: boolean;
}

export interface DashboardConfig {
  name: string;
  type: 'grafana' | 'kibana' | 'splunk' | 'custom';
  url: string;
  enabled: boolean;
}

export interface SecurityIncident {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'investigating' | 'contained' | 'resolved' | 'closed';
  category: IncidentCategory;
  source: string;
  detectedAt: Date;
  reportedAt: Date;
  assignedTo?: string;
  timeline: IncidentTimelineEntry[];
  artifacts: IncidentArtifact[];
  response: IncidentResponseData;
}

export interface IncidentResponseData {
  actions: ResponseAction[];
  containment: ContainmentAction[];
  eradication: EradicationAction[];
  recovery: RecoveryAction[];
  lessons_learned: string[];
}

export interface ResponseAction {
  id: string;
  type: 'automated' | 'manual';
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'failed';
  startedAt?: Date;
  completedAt?: Date;
  result?: string;
}

export interface ContainmentAction extends ResponseAction {
  scope: 'host' | 'network' | 'application' | 'user' | 'global';
  impact: 'low' | 'medium' | 'high';
}

export interface EradicationAction extends ResponseAction {
  target: string;
  method: 'removal' | 'patching' | 'reconfiguration' | 'replacement';
}

export interface RecoveryAction extends ResponseAction {
  service: string;
  verification: string[];
}

export interface IncidentTimelineEntry {
  timestamp: Date;
  type: 'detection' | 'analysis' | 'containment' | 'eradication' | 'recovery' | 'communication';
  description: string;
  actor: string;
}



export type IncidentCategory =
  | 'malware'
  | 'phishing'
  | 'data-breach'
  | 'unauthorized-access'
  | 'denial-of-service'
  | 'insider-threat'
  | 'supply-chain'
  | 'vulnerability-exploitation'
  | 'social-engineering'
  | 'physical-security';

export interface IncidentEvent {
  timestamp: Date;
  type: 'detection' | 'analysis' | 'containment' | 'eradication' | 'recovery' | 'lessons-learned';
  description: string;
  actor: string;
  evidence?: Evidence[];
}

export interface IncidentArtifact {
  id: string;
  type: 'file' | 'network-capture' | 'memory-dump' | 'log' | 'screenshot' | 'document';
  name: string;
  hash: string;
  size: number;
  location: string;
  collected: Date;
  chain_of_custody: ChainOfCustodyEntry[];
}

export interface ChainOfCustodyEntry {
  timestamp: Date;
  actor: string;
  action: 'collected' | 'transferred' | 'analyzed' | 'stored' | 'destroyed';
  location: string;
  notes?: string;
}

export interface IncidentResponse {
  playbook?: string;
  actions: ResponseAction[];
  containment: ContainmentAction[];
  eradication: EradicationAction[];
  recovery: RecoveryAction[];
  lessons_learned: string[];
}

export interface ResponseAction {
  id: string;
  type: 'manual' | 'automated';
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'failed';
  assignedTo?: string;
  startedAt?: Date;
  completedAt?: Date;
  result?: string;
}

export interface ContainmentAction extends ResponseAction {
  scope: 'host' | 'network' | 'application' | 'user' | 'global';
  impact: 'low' | 'medium' | 'high';
}

export interface EradicationAction extends ResponseAction {
  target: string;
  method: 'removal' | 'patching' | 'reconfiguration' | 'replacement';
}

export interface RecoveryAction extends ResponseAction {
  service: string;
  verification: string[];
}

export interface SecurityAlert {
  id: string;
  type: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  timestamp: Date;
  data?: any;
}



export interface SecurityReport {
  id: string;
  type: 'vulnerability' | 'compliance' | 'threat-intelligence' | 'incident' | 'risk-assessment';
  title: string;
  summary: string;
  generatedAt: Date;
  period: {
    start: Date;
    end: Date;
  };
  sections: ReportSection[];
  metadata: any;
}

export interface ReportSection {
  title: string;
  content: string;
  charts?: ReportChart[];
  tables?: ReportTable[];
  priority?: 'low' | 'medium' | 'high' | 'critical';
}

export interface ReportChart {
  type: 'pie' | 'bar' | 'line' | 'gauge';
  title: string;
  data: any;
}

export interface ReportTable {
  title: string;
  headers: string[];
  rows: any[][];
}

export interface ReportAppendix {
  id: string;
  title: string;
  content: string;
  type: 'technical' | 'executive' | 'compliance';
}
export interface ReportFinding {
  id: string;
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  evidence: Evidence[];
  impact: string;
  likelihood: string;
  risk: string;
}

export interface SecurityRecommendation {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  effort: 'low' | 'medium' | 'high';
  impact: 'low' | 'medium' | 'high';
  category: string;
  implementation: string[];
  timeline: string;
  cost?: string;
}

export interface SecurityMetrics {
  vulnerabilities: VulnerabilityMetrics;
  compliance: ComplianceMetrics;
  incidents: IncidentMetrics;
  threats: ThreatMetrics;
  coverage: CoverageMetrics;
  lastUpdate?: Date;
}

export interface VulnerabilityMetrics {
  total: number;
  bySeverity: Record<VulnerabilitySeverity, number>;
  byCategory: Record<SecurityCategory, number>;
  meanTimeToDetection: number;
  meanTimeToRemediation: number;
  trends: TrendData[];
}

export interface ComplianceMetrics {
  overallScore: number;
  byFramework: Record<string, number>;
  controlsTotal: number;
  controlsCompliant: number;
  controlsNonCompliant: number;
  trends: TrendData[];
}

export interface IncidentMetrics {
  total: number;
  bySeverity: Record<string, number>;
  byCategory: Record<IncidentCategory, number>;
  meanTimeToDetection: number;
  meanTimeToContainment: number;
  meanTimeToResolution: number;
  trends: TrendData[];
}

export interface ThreatMetrics {
  indicatorsTotal: number;
  indicatorsByType: Record<string, number>;
  threatsBlocked: number;
  threatsDetected: number;
  falsePositives: number;
  trends: TrendData[];
}

export interface CoverageMetrics {
  assetsScanned: number;
  assetsTotal: number;
  coveragePercentage: number;
  scanFrequency: number;
  lastScanAge: number;
}

export interface TrendData {
  timestamp: Date;
  value: number;
  change: number;
  changePercentage: number;
}
