#!/bin/bash

# 🔒 Script d'entrée Docker pour l'Agent Security
# Retreat And Be - Living AI Organism Architecture v3.8

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Fonction d'affichage avec style
print_header() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                    🔒 AGENT SECURITY STARTUP 🔒                             ║"
    echo "║                     Advanced Security & Compliance                          ║"
    echo "║                              Retreat And Be                                  ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_step() {
    echo -e "${CYAN}[$(date '+%H:%M:%S')] $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Affichage de l'en-tête
print_header

# Vérification des variables d'environnement essentielles
check_environment() {
    print_step "Vérification de l'environnement..."
    
    # Variables obligatoires
    local required_vars=(
        "NODE_ENV"
        "PORT"
        "AGENT_ID"
        "KAFKA_BROKERS"
        "WEAVIATE_URL"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        print_error "Variables d'environnement manquantes: ${missing_vars[*]}"
        exit 1
    fi
    
    print_success "Variables d'environnement vérifiées"
}

# Création des répertoires nécessaires
setup_directories() {
    print_step "Configuration des répertoires..."
    
    local directories=(
        "/app/logs"
        "/app/workspace"
        "/app/reports"
        "/app/temp"
        "/app/data"
        "/app/temp/trivy"
        "/app/temp/grype"
        "/app/temp/semgrep"
    )
    
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            print_step "Répertoire créé: $dir"
        fi
    done
    
    print_success "Répertoires configurés"
}

# Vérification des outils de sécurité
check_security_tools() {
    print_step "Vérification des outils de sécurité..."
    
    local tools=(
        "trivy:Trivy"
        "grype:Grype"
        "safety:Safety"
        "bandit:Bandit"
        "semgrep:Semgrep"
        "nmap:Nmap"
    )
    
    local missing_tools=()
    
    for tool_info in "${tools[@]}"; do
        local tool_cmd="${tool_info%%:*}"
        local tool_name="${tool_info##*:}"
        
        if ! command -v "$tool_cmd" &> /dev/null; then
            missing_tools+=("$tool_name")
        else
            print_step "$tool_name disponible"
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        print_warning "Outils manquants: ${missing_tools[*]}"
        print_warning "Certaines fonctionnalités de sécurité peuvent être limitées"
    else
        print_success "Tous les outils de sécurité sont disponibles"
    fi
}

# Mise à jour des bases de données de vulnérabilités
update_vulnerability_databases() {
    print_step "Mise à jour des bases de données de vulnérabilités..."
    
    # Mise à jour Trivy
    if command -v trivy &> /dev/null; then
        print_step "Mise à jour de la base Trivy..."
        trivy image --download-db-only &> /dev/null || print_warning "Échec de la mise à jour Trivy"
    fi
    
    # Mise à jour Grype
    if command -v grype &> /dev/null; then
        print_step "Mise à jour de la base Grype..."
        grype db update &> /dev/null || print_warning "Échec de la mise à jour Grype"
    fi
    
    # Mise à jour Semgrep
    if command -v semgrep &> /dev/null; then
        print_step "Mise à jour des règles Semgrep..."
        semgrep --update &> /dev/null || print_warning "Échec de la mise à jour Semgrep"
    fi
    
    print_success "Bases de données mises à jour"
}

# Attente des services dépendants
wait_for_services() {
    print_step "Attente des services dépendants..."
    
    # Fonction d'attente pour un service
    wait_for_service() {
        local service_name=$1
        local host=$2
        local port=$3
        local timeout=${4:-60}
        
        print_step "Attente de $service_name ($host:$port)..."
        
        local counter=0
        while ! nc -z "$host" "$port" 2>/dev/null; do
            if [[ $counter -ge $timeout ]]; then
                print_error "Timeout: $service_name non disponible après ${timeout}s"
                return 1
            fi
            
            sleep 2
            counter=$((counter + 2))
            
            if [[ $((counter % 10)) -eq 0 ]]; then
                print_step "Attente de $service_name... (${counter}s/${timeout}s)"
            fi
        done
        
        print_success "$service_name disponible"
        return 0
    }
    
    # Extraction des informations de connexion
    local kafka_host=$(echo "$KAFKA_BROKERS" | cut -d: -f1)
    local kafka_port=$(echo "$KAFKA_BROKERS" | cut -d: -f2)
    
    local weaviate_host=$(echo "$WEAVIATE_URL" | sed 's|http://||' | sed 's|https://||' | cut -d: -f1)
    local weaviate_port=$(echo "$WEAVIATE_URL" | sed 's|http://||' | sed 's|https://||' | cut -d: -f2 | cut -d/ -f1)
    
    # Attente des services
    wait_for_service "Kafka" "$kafka_host" "$kafka_port" 120
    wait_for_service "Weaviate" "$weaviate_host" "$weaviate_port" 60
    
    # Attente optionnelle de Redis si configuré
    if [[ -n "$REDIS_URL" ]]; then
        local redis_host=$(echo "$REDIS_URL" | sed 's|redis://||' | cut -d: -f1)
        local redis_port=$(echo "$REDIS_URL" | sed 's|redis://||' | cut -d: -f2)
        wait_for_service "Redis" "$redis_host" "$redis_port" 30
    fi
}

# Configuration des permissions
setup_permissions() {
    print_step "Configuration des permissions..."
    
    # Vérification que nous sommes l'utilisateur agentsecurity
    if [[ "$(id -u)" != "1001" ]]; then
        print_warning "Exécution en tant que root détectée"
    fi
    
    # Permissions sur les répertoires de travail
    local work_dirs=(
        "/app/logs"
        "/app/workspace"
        "/app/reports"
        "/app/temp"
        "/app/data"
    )
    
    for dir in "${work_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            chmod 755 "$dir" 2>/dev/null || true
        fi
    done
    
    print_success "Permissions configurées"
}

# Validation de la configuration
validate_configuration() {
    print_step "Validation de la configuration..."
    
    # Vérification de la configuration Node.js
    if [[ ! -f "/app/dist/index.js" ]]; then
        print_error "Fichier principal manquant: /app/dist/index.js"
        exit 1
    fi
    
    # Vérification des modules Node.js
    if [[ ! -d "/app/node_modules" ]]; then
        print_error "Modules Node.js manquants"
        exit 1
    fi
    
    # Test de syntaxe JavaScript
    if ! node -c "/app/dist/index.js" &>/dev/null; then
        print_error "Erreur de syntaxe dans le fichier principal"
        exit 1
    fi
    
    print_success "Configuration validée"
}

# Affichage des informations de démarrage
show_startup_info() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                           🔒 AGENT SECURITY READY 🔒                        ║"
    echo "╠══════════════════════════════════════════════════════════════════════════════╣"
    echo "║                                                                              ║"
    echo "║  Agent ID:             $AGENT_ID"
    echo "║  Environment:          $NODE_ENV"
    echo "║  Port:                 $PORT"
    echo "║  Kafka Brokers:        $KAFKA_BROKERS"
    echo "║  Weaviate URL:         $WEAVIATE_URL"
    echo "║  Log Level:            ${LOG_LEVEL:-info}"
    echo "║                                                                              ║"
    echo "║  Capabilities:                                                               ║"
    echo "║    • Vulnerability Scanning (SAST/DAST/SCA)                                 ║"
    echo "║    • Container & Infrastructure Security                                     ║"
    echo "║    • Compliance Checking (OWASP, CIS, NIST)                                 ║"
    echo "║    • Threat Intelligence & Analysis                                          ║"
    echo "║    • Incident Response & Forensics                                          ║"
    echo "║    • Security Monitoring & Alerting                                         ║"
    echo "║                                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Gestion des signaux
setup_signal_handlers() {
    # Fonction de nettoyage
    cleanup() {
        print_step "Signal d'arrêt reçu, nettoyage en cours..."
        
        # Arrêt gracieux de l'application Node.js
        if [[ -n "$NODE_PID" ]]; then
            kill -TERM "$NODE_PID" 2>/dev/null || true
            wait "$NODE_PID" 2>/dev/null || true
        fi
        
        print_success "Nettoyage terminé"
        exit 0
    }
    
    # Configuration des gestionnaires de signaux
    trap cleanup SIGTERM SIGINT SIGQUIT
}

# Fonction principale
main() {
    # Vérifications préliminaires
    check_environment
    setup_directories
    setup_permissions
    check_security_tools
    
    # Mise à jour des bases de données (en arrière-plan pour ne pas bloquer)
    update_vulnerability_databases &
    
    # Attente des services
    wait_for_services
    
    # Validation finale
    validate_configuration
    
    # Configuration des gestionnaires de signaux
    setup_signal_handlers
    
    # Affichage des informations
    show_startup_info
    
    print_success "Agent Security prêt à démarrer!"
    print_step "Démarrage de l'application Node.js..."
    
    # Démarrage de l'application
    exec "$@" &
    NODE_PID=$!
    
    # Attente de l'application
    wait "$NODE_PID"
}

# Exécution du script principal
main "$@"
