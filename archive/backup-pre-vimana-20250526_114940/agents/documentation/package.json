{"name": "agent-documentation", "version": "1.0.0", "description": "Agent spécialisé dans la génération automatique de documentation", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["documentation", "auto-generation", "api-docs", "user-guides", "technical-writing", "gitea-integration"], "author": "Retreat And Be", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "winston": "^3.11.0", "kafkajs": "^2.2.4", "weaviate-ts-client": "^1.5.0", "redis": "^4.6.10", "dotenv": "^16.3.1", "joi": "^17.11.0", "uuid": "^9.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "markdown-it": "^13.0.2", "puppeteer": "^21.6.1", "jsdoc": "^4.0.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "gitea-js": "^1.17.0", "pdf-lib": "^1.17.1", "docx": "^8.5.0", "handlebars": "^4.7.8", "highlight.js": "^11.9.0"}, "devDependencies": {"@types/node": "^20.9.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/jest": "^29.5.8", "@types/markdown-it": "^13.0.7", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "typescript": "^5.2.2", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0"}}