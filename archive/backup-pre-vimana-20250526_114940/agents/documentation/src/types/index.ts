/**
 * Types pour l'Agent Documentation
 */

export interface AgentConfig {
  port: number;
  kafka: {
    brokers: string[];
    clientId: string;
    groupId: string;
  };
  weaviate: {
    scheme: string;
    host: string;
    port: number;
  };
  redis: {
    host: string;
    port: number;
    password?: string;
  };
  gitea: {
    url: string;
    token: string;
    organization: string;
  };
}

export interface DocumentationRequest {
  id: string;
  type: DocumentationType;
  source: DocumentationSource;
  target: DocumentationTarget;
  options: DocumentationOptions;
  metadata?: Record<string, any>;
}

export type DocumentationType = 
  | 'api_documentation'
  | 'user_guide'
  | 'technical_specification'
  | 'installation_guide'
  | 'troubleshooting_guide'
  | 'changelog'
  | 'readme'
  | 'architecture_diagram'
  | 'code_documentation'
  | 'deployment_guide';

export interface DocumentationSource {
  type: 'codebase' | 'api' | 'database' | 'configuration' | 'manual';
  location: string;
  filters?: string[];
  includes?: string[];
  excludes?: string[];
}

export interface DocumentationTarget {
  format: DocumentFormat;
  language: string;
  audience: DocumentAudience;
  style: DocumentStyle;
  output: OutputConfiguration;
}

export type DocumentFormat = 
  | 'markdown'
  | 'html'
  | 'pdf'
  | 'docx'
  | 'confluence'
  | 'gitea_wiki'
  | 'swagger'
  | 'openapi';

export type DocumentAudience = 
  | 'developer'
  | 'end_user'
  | 'administrator'
  | 'business_user'
  | 'technical_writer'
  | 'stakeholder';

export interface DocumentStyle {
  template: string;
  theme: string;
  branding: BrandingOptions;
  formatting: FormattingOptions;
}

export interface BrandingOptions {
  logo?: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
  fonts: {
    heading: string;
    body: string;
    code: string;
  };
  footer?: string;
}

export interface FormattingOptions {
  codeHighlighting: boolean;
  tableOfContents: boolean;
  pageNumbers: boolean;
  lineNumbers: boolean;
  syntax: string;
}

export interface OutputConfiguration {
  destination: string;
  filename: string;
  versioning: boolean;
  publishing: PublishingOptions;
}

export interface PublishingOptions {
  autoPublish: boolean;
  platforms: PublishingPlatform[];
  notifications: NotificationOptions[];
}

export interface PublishingPlatform {
  type: 'gitea' | 'confluence' | 'github' | 'gitlab' | 'notion';
  config: Record<string, any>;
  path: string;
}

export interface NotificationOptions {
  type: 'email' | 'slack' | 'teams' | 'webhook';
  recipients: string[];
  template: string;
}

export interface DocumentationOptions {
  includeExamples: boolean;
  includeCodeSamples: boolean;
  includeScreenshots: boolean;
  includeDiagrams: boolean;
  autoUpdate: boolean;
  versionControl: boolean;
  multilingual: boolean;
  interactive: boolean;
}

export interface DocumentationResult {
  id: string;
  requestId: string;
  status: 'completed' | 'failed' | 'partial';
  documents: GeneratedDocument[];
  metadata: DocumentationMetadata;
  errors?: DocumentationError[];
}

export interface GeneratedDocument {
  id: string;
  title: string;
  type: DocumentationType;
  format: DocumentFormat;
  content: string;
  size: number;
  path: string;
  url?: string;
  version: string;
  lastUpdated: Date;
  sections: DocumentSection[];
  assets: DocumentAsset[];
}

export interface DocumentSection {
  id: string;
  title: string;
  level: number;
  content: string;
  subsections: DocumentSection[];
  examples: CodeExample[];
  diagrams: Diagram[];
}

export interface CodeExample {
  id: string;
  title: string;
  language: string;
  code: string;
  description: string;
  runnable: boolean;
  output?: string;
}

export interface Diagram {
  id: string;
  title: string;
  type: 'flowchart' | 'sequence' | 'class' | 'component' | 'deployment';
  source: string;
  format: 'mermaid' | 'plantuml' | 'graphviz' | 'draw.io';
  image?: string;
}

export interface DocumentAsset {
  id: string;
  type: 'image' | 'video' | 'audio' | 'file';
  name: string;
  path: string;
  url?: string;
  size: number;
  mimeType: string;
}

export interface DocumentationMetadata {
  generatedAt: Date;
  generatedBy: string;
  version: string;
  sourceVersion: string;
  duration: number;
  statistics: DocumentationStatistics;
  quality: DocumentationQuality;
}

export interface DocumentationStatistics {
  totalPages: number;
  totalWords: number;
  totalLines: number;
  codeExamples: number;
  diagrams: number;
  images: number;
  sections: number;
}

export interface DocumentationQuality {
  completeness: number;
  accuracy: number;
  readability: number;
  consistency: number;
  coverage: number;
  warnings: string[];
}

export interface DocumentationError {
  type: 'parsing' | 'generation' | 'publishing' | 'validation';
  message: string;
  location?: string;
  severity: 'low' | 'medium' | 'high';
  suggestion?: string;
}

export interface APIDocumentation {
  id: string;
  title: string;
  version: string;
  description: string;
  baseUrl: string;
  endpoints: APIEndpoint[];
  schemas: APISchema[];
  authentication: AuthenticationInfo;
  examples: APIExample[];
}

export interface APIEndpoint {
  id: string;
  path: string;
  method: HTTPMethod;
  summary: string;
  description: string;
  parameters: APIParameter[];
  requestBody?: APIRequestBody;
  responses: APIResponse[];
  tags: string[];
  deprecated: boolean;
  examples: APIExample[];
}

export type HTTPMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';

export interface APIParameter {
  name: string;
  in: 'query' | 'path' | 'header' | 'cookie';
  description: string;
  required: boolean;
  schema: APISchema;
  example?: any;
}

export interface APIRequestBody {
  description: string;
  required: boolean;
  content: APIContent[];
}

export interface APIContent {
  mediaType: string;
  schema: APISchema;
  examples: APIExample[];
}

export interface APIResponse {
  statusCode: string;
  description: string;
  headers?: APIHeader[];
  content?: APIContent[];
}

export interface APIHeader {
  name: string;
  description: string;
  schema: APISchema;
}

export interface APISchema {
  type: string;
  format?: string;
  description?: string;
  properties?: Record<string, APISchema>;
  items?: APISchema;
  required?: string[];
  enum?: any[];
  example?: any;
}

export interface AuthenticationInfo {
  type: 'none' | 'basic' | 'bearer' | 'apikey' | 'oauth2';
  description: string;
  flows?: OAuthFlow[];
}

export interface OAuthFlow {
  type: 'authorization_code' | 'client_credentials' | 'password';
  authorizationUrl?: string;
  tokenUrl?: string;
  scopes: Record<string, string>;
}

export interface APIExample {
  name: string;
  description: string;
  request?: any;
  response?: any;
}

export interface UserGuide {
  id: string;
  title: string;
  version: string;
  description: string;
  audience: DocumentAudience;
  chapters: UserGuideChapter[];
  glossary: GlossaryEntry[];
  faqs: FAQ[];
}

export interface UserGuideChapter {
  id: string;
  title: string;
  order: number;
  content: string;
  sections: UserGuideSection[];
  exercises: Exercise[];
}

export interface UserGuideSection {
  id: string;
  title: string;
  content: string;
  screenshots: Screenshot[];
  steps: Step[];
  tips: Tip[];
}

export interface Screenshot {
  id: string;
  title: string;
  description: string;
  image: string;
  annotations: Annotation[];
}

export interface Annotation {
  id: string;
  type: 'arrow' | 'circle' | 'rectangle' | 'text';
  position: Position;
  content: string;
  style: AnnotationStyle;
}

export interface Position {
  x: number;
  y: number;
  width?: number;
  height?: number;
}

export interface AnnotationStyle {
  color: string;
  fontSize?: number;
  fontWeight?: string;
  borderWidth?: number;
}

export interface Step {
  order: number;
  title: string;
  description: string;
  action: string;
  expected: string;
  screenshot?: string;
}

export interface Tip {
  type: 'info' | 'warning' | 'success' | 'error';
  title: string;
  content: string;
}

export interface Exercise {
  id: string;
  title: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number;
  steps: Step[];
  solution?: string;
}

export interface GlossaryEntry {
  term: string;
  definition: string;
  category: string;
  relatedTerms: string[];
}

export interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
  popularity: number;
}

export interface TechnicalSpecification {
  id: string;
  title: string;
  version: string;
  description: string;
  requirements: Requirement[];
  architecture: ArchitectureDescription;
  interfaces: InterfaceSpecification[];
  dataModels: DataModel[];
  algorithms: AlgorithmDescription[];
  constraints: Constraint[];
}

export interface Requirement {
  id: string;
  type: 'functional' | 'non_functional' | 'constraint';
  priority: 'must' | 'should' | 'could' | 'wont';
  description: string;
  rationale: string;
  acceptance: string[];
  dependencies: string[];
}

export interface ArchitectureDescription {
  overview: string;
  components: ComponentDescription[];
  patterns: ArchitecturalPattern[];
  decisions: ArchitecturalDecision[];
  diagrams: Diagram[];
}

export interface ComponentDescription {
  name: string;
  type: string;
  description: string;
  responsibilities: string[];
  interfaces: string[];
  dependencies: string[];
}

export interface ArchitecturalPattern {
  name: string;
  description: string;
  rationale: string;
  consequences: string[];
}

export interface ArchitecturalDecision {
  id: string;
  title: string;
  status: 'proposed' | 'accepted' | 'deprecated' | 'superseded';
  context: string;
  decision: string;
  consequences: string[];
  date: Date;
}

export interface InterfaceSpecification {
  name: string;
  type: 'api' | 'ui' | 'database' | 'file' | 'network';
  description: string;
  protocol: string;
  operations: Operation[];
  dataFormats: DataFormat[];
}

export interface Operation {
  name: string;
  description: string;
  inputs: Parameter[];
  outputs: Parameter[];
  preconditions: string[];
  postconditions: string[];
  exceptions: Exception[];
}

export interface Parameter {
  name: string;
  type: string;
  description: string;
  required: boolean;
  constraints: string[];
}

export interface Exception {
  name: string;
  description: string;
  conditions: string[];
  handling: string;
}

export interface DataFormat {
  name: string;
  description: string;
  schema: string;
  examples: string[];
}

export interface DataModel {
  name: string;
  description: string;
  entities: Entity[];
  relationships: Relationship[];
  constraints: DataConstraint[];
}

export interface Entity {
  name: string;
  description: string;
  attributes: Attribute[];
  primaryKey: string[];
  indexes: Index[];
}

export interface Attribute {
  name: string;
  type: string;
  description: string;
  nullable: boolean;
  defaultValue?: any;
  constraints: string[];
}

export interface Index {
  name: string;
  type: 'primary' | 'unique' | 'index';
  columns: string[];
}

export interface Relationship {
  name: string;
  type: 'one-to-one' | 'one-to-many' | 'many-to-many';
  from: string;
  to: string;
  description: string;
}

export interface DataConstraint {
  type: 'check' | 'foreign_key' | 'unique' | 'not_null';
  description: string;
  expression: string;
}

export interface AlgorithmDescription {
  name: string;
  purpose: string;
  description: string;
  complexity: ComplexityAnalysis;
  pseudocode: string;
  implementation?: string;
  examples: AlgorithmExample[];
}

export interface ComplexityAnalysis {
  time: string;
  space: string;
  best: string;
  average: string;
  worst: string;
}

export interface AlgorithmExample {
  input: any;
  output: any;
  explanation: string;
}

export interface Constraint {
  id: string;
  type: 'performance' | 'security' | 'usability' | 'reliability' | 'maintainability';
  description: string;
  rationale: string;
  measurement: string;
  target: string;
}

export interface DocumentationTemplate {
  id: string;
  name: string;
  description: string;
  type: DocumentationType;
  format: DocumentFormat;
  sections: TemplateSection[];
  variables: TemplateVariable[];
  styles: TemplateStyle[];
}

export interface TemplateSection {
  id: string;
  name: string;
  title: string;
  order: number;
  required: boolean;
  content: string;
  subsections: TemplateSection[];
}

export interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object';
  description: string;
  defaultValue?: any;
  required: boolean;
}

export interface TemplateStyle {
  selector: string;
  properties: Record<string, string>;
}
