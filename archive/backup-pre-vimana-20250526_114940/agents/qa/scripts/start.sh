#!/bin/bash

# Script de démarrage pour l'Agent QA
# Ce script configure et démarre tous les services nécessaires

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Vérification des prérequis
check_prerequisites() {
    log "Vérification des prérequis..."
    
    # Vérifier Docker
    if ! command -v docker &> /dev/null; then
        error "Docker n'est pas installé"
        exit 1
    fi
    
    # Vérifier Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose n'est pas installé"
        exit 1
    fi
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js n'est pas installé"
        exit 1
    fi
    
    # Vérifier la version de Node.js
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        error "Node.js version 18+ requis (version actuelle: $(node --version))"
        exit 1
    fi
    
    log "Prérequis validés ✓"
}

# Création des répertoires nécessaires
create_directories() {
    log "Création des répertoires..."
    
    mkdir -p logs
    mkdir -p reports
    mkdir -p test-workspace
    mkdir -p e2e-workspace
    mkdir -p performance-workspace
    mkdir -p accessibility-workspace
    mkdir -p security-workspace
    mkdir -p visual-workspace
    mkdir -p coverage
    mkdir -p artifacts
    mkdir -p monitoring/prometheus
    mkdir -p monitoring/grafana/dashboards
    mkdir -p monitoring/grafana/datasources
    mkdir -p nginx
    mkdir -p ssl
    
    log "Répertoires créés ✓"
}

# Configuration des fichiers
setup_configuration() {
    log "Configuration des fichiers..."
    
    # Copier .env si il n'existe pas
    if [ ! -f .env ]; then
        cp .env.example .env
        warn "Fichier .env créé depuis .env.example - Veuillez le configurer"
    fi
    
    # Créer la configuration Prometheus
    cat > monitoring/prometheus/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'agent-qa'
    static_configs:
      - targets: ['agent-qa:9090']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka:9101']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
EOF

    # Créer la configuration Nginx
    cat > nginx/nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    log_format main '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                    '\$status \$body_bytes_sent "\$http_referer" '
                    '"\$http_user_agent" "\$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;
    
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Agent QA API
    upstream agent-qa {
        server agent-qa:3008;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        # API Proxy
        location /api/ {
            proxy_pass http://agent-qa;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
        
        # Health checks
        location /health {
            proxy_pass http://agent-qa;
        }
        
        location /ready {
            proxy_pass http://agent-qa;
        }
        
        # Rapports statiques
        location /reports/ {
            alias /usr/share/nginx/html/reports/;
            autoindex on;
            autoindex_exact_size off;
            autoindex_localtime on;
        }
        
        # Page d'accueil
        location / {
            return 200 'Agent QA - Test Automation System';
            add_header Content-Type text/plain;
        }
    }
}
EOF
    
    log "Configuration terminée ✓"
}

# Installation des dépendances
install_dependencies() {
    log "Installation des dépendances..."
    
    if [ ! -d "node_modules" ]; then
        npm ci
    else
        info "Dépendances déjà installées"
    fi
    
    # Installer les navigateurs Playwright
    if [ ! -d "node_modules/playwright" ]; then
        warn "Playwright non trouvé, installation en cours..."
        npm install playwright
    fi
    
    npx playwright install
    
    log "Dépendances installées ✓"
}

# Build de l'application
build_application() {
    log "Build de l'application..."
    
    npm run build
    
    log "Build terminé ✓"
}

# Démarrage des services
start_services() {
    log "Démarrage des services..."
    
    # Arrêter les services existants
    docker-compose down
    
    # Démarrer les services
    docker-compose up -d
    
    log "Services démarrés ✓"
}

# Vérification de la santé des services
check_health() {
    log "Vérification de la santé des services..."
    
    # Attendre que les services soient prêts
    sleep 30
    
    # Vérifier l'Agent QA
    if curl -f http://localhost:3008/health > /dev/null 2>&1; then
        log "Agent QA: ✓ Healthy"
    else
        warn "Agent QA: ✗ Not responding"
    fi
    
    # Vérifier Weaviate
    if curl -f http://localhost:8080/v1/.well-known/ready > /dev/null 2>&1; then
        log "Weaviate: ✓ Ready"
    else
        warn "Weaviate: ✗ Not ready"
    fi
    
    # Vérifier Kafka UI
    if curl -f http://localhost:8081 > /dev/null 2>&1; then
        log "Kafka UI: ✓ Available"
    else
        warn "Kafka UI: ✗ Not available"
    fi
    
    # Vérifier SonarQube
    if curl -f http://localhost:9000 > /dev/null 2>&1; then
        log "SonarQube: ✓ Available"
    else
        warn "SonarQube: ✗ Not available (peut prendre quelques minutes)"
    fi
    
    # Vérifier Grafana
    if curl -f http://localhost:3001 > /dev/null 2>&1; then
        log "Grafana: ✓ Available"
    else
        warn "Grafana: ✗ Not available"
    fi
}

# Affichage des informations de connexion
show_info() {
    log "🧪 Agent QA démarré avec succès!"
    echo ""
    info "📊 Services disponibles:"
    echo "  • Agent QA API:     http://localhost:3008"
    echo "  • Agent QA Health:  http://localhost:3008/health"
    echo "  • Rapports:         http://localhost/reports"
    echo "  • Weaviate:         http://localhost:8080"
    echo "  • Kafka UI:         http://localhost:8081"
    echo "  • SonarQube:        http://localhost:9000 (admin/admin)"
    echo "  • Grafana:          http://localhost:3001 (admin/admin)"
    echo "  • Prometheus:       http://localhost:9090"
    echo ""
    info "📚 Documentation:"
    echo "  • API Docs:         http://localhost:3008/api/info"
    echo "  • README:           ./README.md"
    echo ""
    info "🔧 Commandes utiles:"
    echo "  • Logs:             docker-compose logs -f agent-qa"
    echo "  • Arrêt:            docker-compose down"
    echo "  • Redémarrage:      docker-compose restart agent-qa"
    echo "  • Tests:            npm test"
    echo ""
}

# Fonction principale
main() {
    log "🚀 Démarrage de l'Agent QA..."
    
    check_prerequisites
    create_directories
    setup_configuration
    install_dependencies
    build_application
    start_services
    check_health
    show_info
    
    log "✅ Démarrage terminé!"
}

# Gestion des signaux
trap 'error "Interruption détectée, arrêt des services..."; docker-compose down; exit 1' INT TERM

# Vérifier les arguments
case "${1:-}" in
    "dev")
        log "Mode développement"
        npm run dev
        ;;
    "test")
        log "Exécution des tests"
        npm test
        ;;
    "stop")
        log "Arrêt des services"
        docker-compose down
        ;;
    "restart")
        log "Redémarrage des services"
        docker-compose restart
        ;;
    "logs")
        log "Affichage des logs"
        docker-compose logs -f agent-qa
        ;;
    "clean")
        log "Nettoyage complet"
        docker-compose down -v
        docker system prune -f
        rm -rf node_modules dist coverage test-results
        ;;
    *)
        main
        ;;
esac
