import { Logger } from 'winston';
import { TestRequest, TestResult, TestStatus } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import axios from 'axios';

/**
 * Runner de Tests de Charge
 * 
 * Exécute les tests de charge et de stress pour valider
 * la performance sous charge élevée.
 */
export class LoadTestRunner {
  private logger: Logger;
  private memory: WeaviateMemory;

  constructor(logger: Logger, memory: WeaviateMemory) {
    this.logger = logger;
    this.memory = memory;
  }

  async run(request: TestRequest, generatedTests?: any): Promise<TestResult> {
    this.logger.info('Exécution des tests de charge', { id: request.id });

    const startTime = new Date();
    const testCases = [];
    let passed = 0, failed = 0;

    try {
      const baseUrl = this.getBaseUrl(request);
      
      // Test de charge basique
      const loadTestResult = await this.executeLoadTest(baseUrl, {
        concurrentUsers: 10,
        duration: 30000, // 30 secondes
        rampUp: 5000     // 5 secondes
      });

      const avgResponseTime = loadTestResult.averageResponseTime;
      const errorRate = loadTestResult.errorRate;
      
      const testCase = {
        name: 'Load Test - 10 concurrent users',
        status: avgResponseTime < 1000 && errorRate < 0.05 ? 'passed' : 'failed' as TestStatus,
        duration: 30000,
        steps: [
          {
            name: 'Average Response Time',
            status: avgResponseTime < 1000 ? 'passed' : 'failed' as TestStatus,
            duration: 0,
            action: 'load-test',
            expected: '< 1000ms',
            actual: `${avgResponseTime}ms`
          },
          {
            name: 'Error Rate',
            status: errorRate < 0.05 ? 'passed' : 'failed' as TestStatus,
            duration: 0,
            action: 'load-test',
            expected: '< 5%',
            actual: `${(errorRate * 100).toFixed(1)}%`
          }
        ]
      };

      testCases.push(testCase);
      if (testCase.status === 'passed') passed++;
      else failed++;

      const total = testCases.length;
      const successRate = total > 0 ? passed / total : 0;

      return {
        id: request.id,
        type: request.type,
        status: failed > 0 ? 'failed' : 'passed',
        summary: { total, passed, failed, skipped: 0, errors: 0, successRate },
        details: {
          suites: [{
            name: 'Load Tests',
            status: failed > 0 ? 'failed' : 'passed',
            tests: testCases,
            duration: Date.now() - startTime.getTime()
          }],
          environment: {
            os: process.platform,
            node: process.version,
            timestamp: new Date()
          },
          configuration: request.configuration
        },
        reports: [],
        artifacts: [],
        metrics: {
          load: loadTestResult
        },
        issues: [],
        recommendations: this.generateLoadTestRecommendations(loadTestResult),
        startedAt: startTime,
        completedAt: new Date(),
        duration: Date.now() - startTime.getTime()
      };

    } catch (error) {
      this.logger.error('Erreur lors des tests de charge', { error: error.message });
      throw error;
    }
  }

  private async executeLoadTest(baseUrl: string, config: any): Promise<any> {
    const results = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      minResponseTime: Infinity,
      maxResponseTime: 0,
      responseTimes: [],
      errorRate: 0,
      throughput: 0
    };

    const promises = [];
    const startTime = Date.now();

    // Simuler des utilisateurs concurrents
    for (let i = 0; i < config.concurrentUsers; i++) {
      promises.push(this.simulateUser(baseUrl, config.duration, results));
    }

    await Promise.all(promises);

    // Calculer les statistiques finales
    if (results.responseTimes.length > 0) {
      results.averageResponseTime = results.responseTimes.reduce((a, b) => a + b, 0) / results.responseTimes.length;
      results.minResponseTime = Math.min(...results.responseTimes);
      results.maxResponseTime = Math.max(...results.responseTimes);
    }

    results.errorRate = results.totalRequests > 0 ? results.failedRequests / results.totalRequests : 0;
    results.throughput = results.totalRequests / (config.duration / 1000); // requêtes par seconde

    return results;
  }

  private async simulateUser(baseUrl: string, duration: number, results: any): Promise<void> {
    const endTime = Date.now() + duration;

    while (Date.now() < endTime) {
      try {
        const requestStart = Date.now();
        
        await axios.get(baseUrl, { 
          timeout: 10000,
          validateStatus: () => true 
        });
        
        const responseTime = Date.now() - requestStart;
        
        results.totalRequests++;
        results.successfulRequests++;
        results.responseTimes.push(responseTime);

        // Pause entre les requêtes
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        results.totalRequests++;
        results.failedRequests++;
      }
    }
  }

  private generateLoadTestRecommendations(results: any): string[] {
    const recommendations = [];

    if (results.averageResponseTime > 1000) {
      recommendations.push('Optimiser les temps de réponse (actuellement > 1s)');
    }

    if (results.errorRate > 0.05) {
      recommendations.push('Réduire le taux d\'erreur sous charge');
    }

    if (results.throughput < 10) {
      recommendations.push('Améliorer le débit (throughput) de l\'application');
    }

    return recommendations;
  }

  private getBaseUrl(request: TestRequest): string {
    if (request.source.type === 'url') {
      return request.source.url!;
    }
    if (request.source.type === 'deployment' && request.source.deployment?.url) {
      return request.source.deployment.url;
    }
    return 'http://localhost:3000';
  }
}
