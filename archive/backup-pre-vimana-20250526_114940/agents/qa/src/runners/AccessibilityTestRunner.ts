import { Logger } from 'winston';
import { TestRequest, TestResult, TestStatus, AccessibilityMetrics } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import { chromium } from 'playwright';
import * as fs from 'fs-extra';
import * as path from 'path';

/**
 * Runner de Tests d'Accessibilité
 * 
 * Exécute les tests d'accessibilité avec axe-core, pa11y,
 * et autres outils pour valider la conformité WCAG.
 */
export class AccessibilityTestRunner {
  private logger: Logger;
  private memory: WeaviateMemory;
  private workingDirectory: string;

  constructor(logger: Logger, memory: WeaviateMemory) {
    this.logger = logger;
    this.memory = memory;
    this.workingDirectory = process.env.A11Y_WORKDIR || './accessibility-workspace';
    
    // Créer le répertoire de travail
    fs.ensureDirSync(this.workingDirectory);
  }

  /**
   * Exécute les tests d'accessibilité
   */
  async run(request: TestRequest, generatedTests?: any): Promise<TestResult> {
    this.logger.info('Exécution des tests d\'accessibilité', { id: request.id });

    const startTime = new Date();
    const testId = request.id;
    const resultsPath = path.join(this.workingDirectory, testId);

    try {
      // 1. Préparer l'environnement
      await this.prepareTestEnvironment(resultsPath);

      // 2. Déterminer les URLs à tester
      const urls = await this.getTestUrls(request);

      // 3. Exécuter les tests axe-core
      const axeResults = await this.runAxeTests(urls, resultsPath, request);

      // 4. Exécuter les tests pa11y (simulation)
      const pa11yResults = await this.runPa11yTests(urls, request);

      // 5. Tests de navigation au clavier
      const keyboardResults = await this.runKeyboardTests(urls, request);

      // 6. Tests de contraste des couleurs
      const contrastResults = await this.runContrastTests(urls, request);

      // 7. Consolider les résultats
      const testResult = await this.consolidateAccessibilityResults(
        axeResults,
        pa11yResults,
        keyboardResults,
        contrastResults,
        request,
        startTime
      );

      // 8. Générer les artefacts
      await this.generateArtifacts(resultsPath, testResult);

      this.logger.info('Tests d\'accessibilité terminés', { 
        id: testId,
        status: testResult.status,
        violations: testResult.metrics.accessibility?.violations.length || 0
      });

      return testResult;

    } catch (error) {
      this.logger.error('Erreur lors des tests d\'accessibilité', { 
        id: testId,
        error: error.message 
      });

      await this.cleanup(resultsPath);
      throw error;
    }
  }

  /**
   * Prépare l'environnement de test
   */
  private async prepareTestEnvironment(resultsPath: string): Promise<void> {
    await fs.ensureDir(resultsPath);
    await fs.ensureDir(path.join(resultsPath, 'axe'));
    await fs.ensureDir(path.join(resultsPath, 'pa11y'));
    await fs.ensureDir(path.join(resultsPath, 'screenshots'));
  }

  /**
   * Détermine les URLs à tester
   */
  private async getTestUrls(request: TestRequest): Promise<string[]> {
    if (request.source.type === 'url') {
      return [request.source.url!];
    }

    if (request.source.type === 'deployment' && request.source.deployment?.url) {
      return [request.source.deployment.url];
    }

    // URLs par défaut pour les tests locaux
    return ['http://localhost:3000'];
  }

  /**
   * Exécute les tests axe-core
   */
  private async runAxeTests(
    urls: string[],
    resultsPath: string,
    request: TestRequest
  ): Promise<any[]> {
    const results = [];
    const browser = await chromium.launch({
      headless: request.configuration.headless !== false
    });

    for (const url of urls) {
      this.logger.info(`Exécution des tests axe-core sur ${url}`);

      try {
        const page = await browser.newPage();

        // Aller à la page
        await page.goto(url, { waitUntil: 'networkidle' });

        // Injecter axe-core
        await page.addScriptTag({
          url: 'https://unpkg.com/axe-core@4.8.0/axe.min.js'
        });

        // Exécuter axe-core
        const axeResults = await page.evaluate(async () => {
          // Configuration axe selon les standards WCAG
          const config = {
            rules: {
              'color-contrast': { enabled: true },
              'keyboard-navigation': { enabled: true },
              'focus-management': { enabled: true },
              'aria-usage': { enabled: true },
              'semantic-markup': { enabled: true }
            },
            tags: ['wcag2a', 'wcag2aa', 'wcag21aa']
          };

          return await (window as any).axe.run(document, config);
        });

        // Prendre une capture d'écran
        const screenshotPath = path.join(
          resultsPath,
          'screenshots',
          `${this.sanitizeUrl(url)}-axe.png`
        );
        await page.screenshot({ path: screenshotPath, fullPage: true });

        // Sauvegarder les résultats
        const reportPath = path.join(resultsPath, 'axe', `${this.sanitizeUrl(url)}.json`);
        await fs.writeFile(reportPath, JSON.stringify(axeResults, null, 2));

        await page.close();

        results.push({
          url,
          axe: axeResults,
          reportPath,
          screenshotPath
        });

      } catch (error) {
        this.logger.error(`Erreur axe-core pour ${url}`, { error: error.message });
        results.push({
          url,
          error: error.message
        });
      }
    }

    await browser.close();
    return results;
  }

  /**
   * Exécute les tests pa11y (simulation)
   */
  private async runPa11yTests(urls: string[], request: TestRequest): Promise<any[]> {
    const results = [];

    for (const url of urls) {
      this.logger.info(`Exécution des tests pa11y sur ${url}`);

      try {
        // Simulation des résultats pa11y
        const pa11yResults = {
          documentTitle: 'Test Page',
          pageUrl: url,
          issues: [
            {
              code: 'WCAG2AA.Principle1.Guideline1_4.1_4_3.G18.Fail',
              type: 'error',
              typeCode: 1,
              message: 'Contraste insuffisant entre le texte et l\'arrière-plan',
              context: '<p style="color: #999; background: #fff;">Texte avec faible contraste</p>',
              selector: 'p',
              runner: 'htmlcs',
              runnerExtras: {}
            },
            {
              code: 'WCAG2AA.Principle4.Guideline4_1.4_1_2.H91.A.EmptyNoId',
              type: 'error',
              typeCode: 1,
              message: 'Élément d\'ancrage trouvé avec un attribut href, mais sans contenu de lien et sans attribut name ou id',
              context: '<a href="/page"></a>',
              selector: 'a[href="/page"]',
              runner: 'htmlcs',
              runnerExtras: {}
            }
          ]
        };

        results.push({
          url,
          pa11y: pa11yResults
        });

      } catch (error) {
        this.logger.error(`Erreur pa11y pour ${url}`, { error: error.message });
        results.push({
          url,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Exécute les tests de navigation au clavier
   */
  private async runKeyboardTests(urls: string[], request: TestRequest): Promise<any[]> {
    const results = [];
    const browser = await chromium.launch({
      headless: request.configuration.headless !== false
    });

    for (const url of urls) {
      this.logger.info(`Tests de navigation clavier sur ${url}`);

      try {
        const page = await browser.newPage();
        await page.goto(url, { waitUntil: 'networkidle' });

        // Tester la navigation par Tab
        const keyboardTestResults = await page.evaluate(() => {
          const focusableElements = document.querySelectorAll(
            'a[href], button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])'
          );

          const results = {
            totalFocusableElements: focusableElements.length,
            elementsWithoutVisibleFocus: 0,
            elementsWithTabIndex: 0,
            skipLinks: document.querySelectorAll('a[href^="#"]').length,
            issues: []
          };

          focusableElements.forEach((element, index) => {
            // Vérifier si l'élément a un focus visible
            const computedStyle = window.getComputedStyle(element);
            if (computedStyle.outline === 'none' && computedStyle.boxShadow === 'none') {
              results.elementsWithoutVisibleFocus++;
              results.issues.push({
                type: 'no-visible-focus',
                element: element.tagName.toLowerCase(),
                selector: element.getAttribute('class') ? `.${element.getAttribute('class')}` : element.tagName.toLowerCase()
              });
            }

            // Vérifier les tabindex
            const tabIndex = element.getAttribute('tabindex');
            if (tabIndex && parseInt(tabIndex) > 0) {
              results.elementsWithTabIndex++;
              results.issues.push({
                type: 'positive-tabindex',
                element: element.tagName.toLowerCase(),
                tabindex: tabIndex,
                selector: element.getAttribute('class') ? `.${element.getAttribute('class')}` : element.tagName.toLowerCase()
              });
            }
          });

          return results;
        });

        await page.close();

        results.push({
          url,
          keyboard: keyboardTestResults
        });

      } catch (error) {
        this.logger.error(`Erreur tests clavier pour ${url}`, { error: error.message });
        results.push({
          url,
          error: error.message
        });
      }
    }

    await browser.close();
    return results;
  }

  /**
   * Exécute les tests de contraste des couleurs
   */
  private async runContrastTests(urls: string[], request: TestRequest): Promise<any[]> {
    const results = [];
    const browser = await chromium.launch({
      headless: request.configuration.headless !== false
    });

    for (const url of urls) {
      this.logger.info(`Tests de contraste sur ${url}`);

      try {
        const page = await browser.newPage();
        await page.goto(url, { waitUntil: 'networkidle' });

        // Analyser les contrastes de couleurs
        const contrastResults = await page.evaluate(() => {
          const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, a, button, label');
          const results = {
            totalElements: textElements.length,
            lowContrastElements: 0,
            issues: []
          };

          textElements.forEach((element) => {
            const computedStyle = window.getComputedStyle(element);
            const color = computedStyle.color;
            const backgroundColor = computedStyle.backgroundColor;

            // Simulation d'un calcul de contraste
            // En réalité, il faudrait convertir les couleurs RGB et calculer le ratio
            const mockContrastRatio = Math.random() * 10 + 1; // Ratio simulé entre 1 et 11

            if (mockContrastRatio < 4.5) { // WCAG AA standard
              results.lowContrastElements++;
              results.issues.push({
                type: 'low-contrast',
                element: element.tagName.toLowerCase(),
                color,
                backgroundColor,
                contrastRatio: mockContrastRatio.toFixed(2),
                selector: element.getAttribute('class') ? `.${element.getAttribute('class')}` : element.tagName.toLowerCase()
              });
            }
          });

          return results;
        });

        await page.close();

        results.push({
          url,
          contrast: contrastResults
        });

      } catch (error) {
        this.logger.error(`Erreur tests contraste pour ${url}`, { error: error.message });
        results.push({
          url,
          error: error.message
        });
      }
    }

    await browser.close();
    return results;
  }

  /**
   * Consolide les résultats d'accessibilité
   */
  private async consolidateAccessibilityResults(
    axeResults: any[],
    pa11yResults: any[],
    keyboardResults: any[],
    contrastResults: any[],
    request: TestRequest,
    startTime: Date
  ): Promise<TestResult> {
    const testCases = [];
    let passed = 0, failed = 0;

    // Analyser les résultats axe-core
    for (const result of axeResults) {
      if (result.axe) {
        const violations = result.axe.violations || [];
        const passes = result.axe.passes || [];
        
        const testCase = {
          name: `Axe-core Accessibility - ${result.url}`,
          status: violations.length === 0 ? 'passed' : 'failed' as TestStatus,
          duration: 0,
          steps: [
            {
              name: 'WCAG Violations',
              status: violations.length === 0 ? 'passed' : 'failed' as TestStatus,
              duration: 0,
              action: 'axe-audit',
              expected: '0 violations',
              actual: `${violations.length} violations found`
            },
            {
              name: 'WCAG Passes',
              status: 'passed' as TestStatus,
              duration: 0,
              action: 'axe-audit',
              expected: 'Rules passed',
              actual: `${passes.length} rules passed`
            }
          ]
        };

        testCases.push(testCase);
        if (testCase.status === 'passed') passed++;
        else failed++;
      }
    }

    // Analyser les tests de navigation clavier
    for (const result of keyboardResults) {
      if (result.keyboard) {
        const keyboard = result.keyboard;
        const hasIssues = keyboard.issues.length > 0;
        
        const testCase = {
          name: `Keyboard Navigation - ${result.url}`,
          status: !hasIssues ? 'passed' : 'failed' as TestStatus,
          duration: 0,
          steps: [
            {
              name: 'Focusable Elements',
              status: keyboard.elementsWithoutVisibleFocus === 0 ? 'passed' : 'failed' as TestStatus,
              duration: 0,
              action: 'keyboard-test',
              expected: 'All focusable elements have visible focus',
              actual: `${keyboard.elementsWithoutVisibleFocus} elements without visible focus`
            },
            {
              name: 'Tab Index Usage',
              status: keyboard.elementsWithTabIndex === 0 ? 'passed' : 'failed' as TestStatus,
              duration: 0,
              action: 'keyboard-test',
              expected: 'No positive tabindex values',
              actual: `${keyboard.elementsWithTabIndex} elements with positive tabindex`
            }
          ]
        };

        testCases.push(testCase);
        if (testCase.status === 'passed') passed++;
        else failed++;
      }
    }

    // Analyser les tests de contraste
    for (const result of contrastResults) {
      if (result.contrast) {
        const contrast = result.contrast;
        
        const testCase = {
          name: `Color Contrast - ${result.url}`,
          status: contrast.lowContrastElements === 0 ? 'passed' : 'failed' as TestStatus,
          duration: 0,
          steps: [{
            name: 'Color Contrast Ratio',
            status: contrast.lowContrastElements === 0 ? 'passed' : 'failed' as TestStatus,
            duration: 0,
            action: 'contrast-test',
            expected: 'All text meets WCAG AA contrast requirements',
            actual: `${contrast.lowContrastElements} elements with low contrast`
          }]
        };

        testCases.push(testCase);
        if (testCase.status === 'passed') passed++;
        else failed++;
      }
    }

    // Créer les métriques d'accessibilité
    const accessibilityMetrics: AccessibilityMetrics = {
      score: this.calculateAccessibilityScore(axeResults, keyboardResults, contrastResults),
      violations: this.extractViolations(axeResults, pa11yResults, keyboardResults, contrastResults),
      passes: this.extractPasses(axeResults),
      incomplete: this.extractIncomplete(axeResults)
    };

    const total = testCases.length;
    const successRate = total > 0 ? passed / total : 0;

    return {
      id: request.id,
      type: request.type,
      status: failed > 0 ? 'failed' : 'passed',
      summary: {
        total,
        passed,
        failed,
        skipped: 0,
        errors: 0,
        successRate
      },
      details: {
        suites: [{
          name: 'Accessibility Tests',
          status: failed > 0 ? 'failed' : 'passed',
          tests: testCases,
          duration: Date.now() - startTime.getTime()
        }],
        environment: {
          os: process.platform,
          node: process.version,
          timestamp: new Date()
        },
        configuration: request.configuration
      },
      reports: [],
      artifacts: [],
      metrics: {
        accessibility: accessibilityMetrics
      },
      issues: this.identifyAccessibilityIssues(axeResults, keyboardResults, contrastResults),
      recommendations: this.generateAccessibilityRecommendations(axeResults, keyboardResults, contrastResults),
      startedAt: startTime,
      completedAt: new Date(),
      duration: Date.now() - startTime.getTime()
    };
  }

  // Méthodes utilitaires

  private calculateAccessibilityScore(axeResults: any[], keyboardResults: any[], contrastResults: any[]): number {
    let totalTests = 0;
    let passedTests = 0;

    // Compter les tests axe-core
    for (const result of axeResults) {
      if (result.axe) {
        const violations = result.axe.violations?.length || 0;
        const passes = result.axe.passes?.length || 0;
        totalTests += violations + passes;
        passedTests += passes;
      }
    }

    // Compter les tests clavier
    for (const result of keyboardResults) {
      if (result.keyboard) {
        totalTests += 2; // Focus visible + tabindex
        if (result.keyboard.elementsWithoutVisibleFocus === 0) passedTests++;
        if (result.keyboard.elementsWithTabIndex === 0) passedTests++;
      }
    }

    // Compter les tests de contraste
    for (const result of contrastResults) {
      if (result.contrast) {
        totalTests += 1;
        if (result.contrast.lowContrastElements === 0) passedTests++;
      }
    }

    return totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 100;
  }

  private extractViolations(axeResults: any[], pa11yResults: any[], keyboardResults: any[], contrastResults: any[]): any[] {
    const violations = [];

    // Violations axe-core
    for (const result of axeResults) {
      if (result.axe?.violations) {
        for (const violation of result.axe.violations) {
          violations.push({
            id: violation.id,
            impact: violation.impact,
            description: violation.description,
            help: violation.help,
            helpUrl: violation.helpUrl,
            nodes: violation.nodes.map((node: any) => ({
              target: node.target,
              html: node.html,
              impact: node.impact
            }))
          });
        }
      }
    }

    return violations;
  }

  private extractPasses(axeResults: any[]): any[] {
    const passes = [];

    for (const result of axeResults) {
      if (result.axe?.passes) {
        for (const pass of result.axe.passes) {
          passes.push({
            id: pass.id,
            description: pass.description,
            nodes: pass.nodes.map((node: any) => ({
              target: node.target,
              html: node.html
            }))
          });
        }
      }
    }

    return passes;
  }

  private extractIncomplete(axeResults: any[]): any[] {
    const incomplete = [];

    for (const result of axeResults) {
      if (result.axe?.incomplete) {
        for (const inc of result.axe.incomplete) {
          incomplete.push({
            id: inc.id,
            description: inc.description,
            nodes: inc.nodes.map((node: any) => ({
              target: node.target,
              html: node.html
            }))
          });
        }
      }
    }

    return incomplete;
  }

  private identifyAccessibilityIssues(axeResults: any[], keyboardResults: any[], contrastResults: any[]): any[] {
    const issues = [];

    // Issues critiques d'accessibilité
    for (const result of axeResults) {
      if (result.axe?.violations) {
        for (const violation of result.axe.violations) {
          if (violation.impact === 'critical' || violation.impact === 'serious') {
            issues.push({
              id: violation.id,
              type: 'accessibility',
              severity: violation.impact === 'critical' ? 'critical' : 'high',
              title: violation.help,
              description: violation.description,
              location: { url: result.url }
            });
          }
        }
      }
    }

    return issues;
  }

  private generateAccessibilityRecommendations(axeResults: any[], keyboardResults: any[], contrastResults: any[]): string[] {
    const recommendations = [];

    // Recommandations basées sur les violations communes
    const hasContrastIssues = contrastResults.some(r => r.contrast?.lowContrastElements > 0);
    if (hasContrastIssues) {
      recommendations.push('Améliorer le contraste des couleurs pour respecter WCAG AA (ratio 4.5:1)');
    }

    const hasKeyboardIssues = keyboardResults.some(r => r.keyboard?.issues.length > 0);
    if (hasKeyboardIssues) {
      recommendations.push('Améliorer la navigation au clavier et le focus visible');
    }

    const hasAriaIssues = axeResults.some(r => 
      r.axe?.violations?.some((v: any) => v.id.includes('aria'))
    );
    if (hasAriaIssues) {
      recommendations.push('Corriger l\'utilisation des attributs ARIA');
    }

    return recommendations;
  }

  private async generateArtifacts(resultsPath: string, testResult: TestResult): Promise<void> {
    const artifacts = [];

    // Rapports axe-core
    const axePath = path.join(resultsPath, 'axe');
    if (await fs.pathExists(axePath)) {
      artifacts.push({
        type: 'log' as const,
        name: 'Axe-core Reports',
        path: axePath,
        size: await this.getDirectorySize(axePath)
      });
    }

    // Screenshots
    const screenshotsPath = path.join(resultsPath, 'screenshots');
    if (await fs.pathExists(screenshotsPath)) {
      artifacts.push({
        type: 'screenshot' as const,
        name: 'Accessibility Screenshots',
        path: screenshotsPath,
        size: await this.getDirectorySize(screenshotsPath)
      });
    }

    testResult.artifacts = artifacts;
  }

  private async cleanup(resultsPath: string): Promise<void> {
    // Nettoyage optionnel
  }

  private sanitizeUrl(url: string): string {
    return url.replace(/[^a-zA-Z0-9]/g, '_');
  }

  private async getDirectorySize(dirPath: string): Promise<number> {
    try {
      const stats = await fs.stat(dirPath);
      return stats.isFile() ? stats.size : 0;
    } catch {
      return 0;
    }
  }
}
