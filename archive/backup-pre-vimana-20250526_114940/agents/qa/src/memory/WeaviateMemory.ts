import weaviate, { WeaviateClient } from 'weaviate-ts-client';
import { <PERSON><PERSON> } from 'winston';
import { TestResult, TestRequest } from '../types';

/**
 * Système de Mémoire Weaviate pour l'Agent QA
 * 
 * Stocke et récupère les résultats de tests, patterns de qualité,
 * et historique des tests pour l'apprentissage et l'amélioration continue.
 */
export class WeaviateMemory {
  private client: WeaviateClient;
  private logger: Logger;
  private isConnected: boolean = false;

  // Collections Weaviate
  private readonly collections = {
    TestResult: 'TestResult',
    TestPattern: 'TestPattern',
    QualityMetric: 'QualityMetric',
    TestTemplate: 'TestTemplate',
    Issue: 'Issue',
    Recommendation: 'Recommendation'
  };

  constructor(logger: Logger, weaviateUrl: string = 'http://weaviate:8080') {
    this.logger = logger;
    this.client = weaviate.client({
      scheme: 'http',
      host: weaviateUrl.replace('http://', '').replace('https://', ''),
    });
    
    this.initializeConnection();
  }

  /**
   * Initialise la connexion à Weaviate
   */
  private async initializeConnection(): Promise<void> {
    try {
      this.logger.info('Initialisation de la connexion Weaviate QA');
      
      const isReady = await this.client.misc.readyChecker().do();
      if (isReady) {
        this.isConnected = true;
        await this.ensureSchemas();
        this.logger.info('Connexion Weaviate QA établie avec succès');
      } else {
        throw new Error('Weaviate n\'est pas prêt');
      }
    } catch (error) {
      this.logger.error('Erreur lors de la connexion à Weaviate QA', { error: error.message });
      this.isConnected = false;
    }
  }

  /**
   * S'assure que les schémas existent
   */
  private async ensureSchemas(): Promise<void> {
    try {
      // Schéma pour les résultats de tests
      await this.createSchemaIfNotExists(this.collections.TestResult, {
        class: this.collections.TestResult,
        description: 'Résultats des tests exécutés par l\'agent QA',
        properties: [
          {
            name: 'testId',
            dataType: ['text'],
            description: 'Identifiant unique du test'
          },
          {
            name: 'testType',
            dataType: ['text'],
            description: 'Type de test (unit, e2e, performance, etc.)'
          },
          {
            name: 'status',
            dataType: ['text'],
            description: 'Statut du test (passed, failed, skipped)'
          },
          {
            name: 'framework',
            dataType: ['text'],
            description: 'Framework testé (react, vue, angular)'
          },
          {
            name: 'duration',
            dataType: ['number'],
            description: 'Durée d\'exécution en ms'
          },
          {
            name: 'successRate',
            dataType: ['number'],
            description: 'Taux de succès (0-1)'
          },
          {
            name: 'totalTests',
            dataType: ['number'],
            description: 'Nombre total de tests'
          },
          {
            name: 'passedTests',
            dataType: ['number'],
            description: 'Nombre de tests réussis'
          },
          {
            name: 'failedTests',
            dataType: ['number'],
            description: 'Nombre de tests échoués'
          },
          {
            name: 'metrics',
            dataType: ['text'],
            description: 'Métriques détaillées (JSON)'
          },
          {
            name: 'issues',
            dataType: ['text'],
            description: 'Problèmes identifiés (JSON)'
          },
          {
            name: 'recommendations',
            dataType: ['text[]'],
            description: 'Recommandations générées'
          },
          {
            name: 'executedAt',
            dataType: ['date'],
            description: 'Date d\'exécution'
          },
          {
            name: 'sourceAgent',
            dataType: ['text'],
            description: 'Agent source du code testé'
          },
          {
            name: 'tags',
            dataType: ['text[]'],
            description: 'Tags pour la recherche'
          }
        ]
      });

      // Schéma pour les patterns de test
      await this.createSchemaIfNotExists(this.collections.TestPattern, {
        class: this.collections.TestPattern,
        description: 'Patterns de tests identifiés et réutilisables',
        properties: [
          {
            name: 'patternName',
            dataType: ['text'],
            description: 'Nom du pattern'
          },
          {
            name: 'testType',
            dataType: ['text'],
            description: 'Type de test associé'
          },
          {
            name: 'framework',
            dataType: ['text'],
            description: 'Framework cible'
          },
          {
            name: 'template',
            dataType: ['text'],
            description: 'Template de test (code)'
          },
          {
            name: 'description',
            dataType: ['text'],
            description: 'Description du pattern'
          },
          {
            name: 'successRate',
            dataType: ['number'],
            description: 'Taux de succès historique'
          },
          {
            name: 'usageCount',
            dataType: ['number'],
            description: 'Nombre d\'utilisations'
          },
          {
            name: 'createdAt',
            dataType: ['date'],
            description: 'Date de création'
          },
          {
            name: 'lastUsed',
            dataType: ['date'],
            description: 'Dernière utilisation'
          }
        ]
      });

      // Schéma pour les métriques de qualité
      await this.createSchemaIfNotExists(this.collections.QualityMetric, {
        class: this.collections.QualityMetric,
        description: 'Métriques de qualité collectées',
        properties: [
          {
            name: 'testId',
            dataType: ['text'],
            description: 'ID du test associé'
          },
          {
            name: 'metricType',
            dataType: ['text'],
            description: 'Type de métrique'
          },
          {
            name: 'metricName',
            dataType: ['text'],
            description: 'Nom de la métrique'
          },
          {
            name: 'value',
            dataType: ['number'],
            description: 'Valeur de la métrique'
          },
          {
            name: 'unit',
            dataType: ['text'],
            description: 'Unité de mesure'
          },
          {
            name: 'threshold',
            dataType: ['number'],
            description: 'Seuil de qualité'
          },
          {
            name: 'passed',
            dataType: ['boolean'],
            description: 'Métrique dans les seuils'
          },
          {
            name: 'timestamp',
            dataType: ['date'],
            description: 'Horodatage'
          },
          {
            name: 'framework',
            dataType: ['text'],
            description: 'Framework testé'
          }
        ]
      });

      this.logger.info('Schémas Weaviate QA initialisés');
    } catch (error) {
      this.logger.error('Erreur lors de la création des schémas QA', { error: error.message });
    }
  }

  /**
   * Stocke un résultat de test
   */
  async storeTestResult(testResult: TestResult): Promise<string> {
    if (!this.isConnected) {
      throw new Error('Weaviate non connecté');
    }

    try {
      this.logger.info('Stockage du résultat de test', { id: testResult.id });

      const data = {
        testId: testResult.id,
        testType: testResult.type,
        status: testResult.status,
        framework: this.extractFramework(testResult),
        duration: testResult.duration || 0,
        successRate: testResult.summary.successRate,
        totalTests: testResult.summary.total,
        passedTests: testResult.summary.passed,
        failedTests: testResult.summary.failed,
        metrics: JSON.stringify(testResult.metrics),
        issues: JSON.stringify(testResult.issues),
        recommendations: testResult.recommendations,
        executedAt: testResult.completedAt?.toISOString() || new Date().toISOString(),
        sourceAgent: this.extractSourceAgent(testResult),
        tags: this.generateTags(testResult)
      };

      const result = await this.client.data
        .creator()
        .withClassName(this.collections.TestResult)
        .withProperties(data)
        .do();

      // Stocker les métriques détaillées
      await this.storeDetailedMetrics(testResult);

      this.logger.info('Résultat de test stocké avec succès', { id: result.id });
      return result.id;

    } catch (error) {
      this.logger.error('Erreur lors du stockage du résultat de test', { error: error.message });
      throw error;
    }
  }

  /**
   * Stocke les métriques détaillées
   */
  async storeDetailedMetrics(testResult: TestResult): Promise<void> {
    if (!testResult.metrics || !this.isConnected) {
      return;
    }

    try {
      const timestamp = new Date().toISOString();
      const framework = this.extractFramework(testResult);

      // Stocker chaque métrique individuellement
      for (const [metricType, metricData] of Object.entries(testResult.metrics)) {
        if (typeof metricData === 'object' && metricData !== null) {
          for (const [metricName, value] of Object.entries(metricData as any)) {
            if (typeof value === 'number') {
              await this.client.data
                .creator()
                .withClassName(this.collections.QualityMetric)
                .withProperties({
                  testId: testResult.id,
                  metricType,
                  metricName,
                  value,
                  unit: this.getMetricUnit(metricType, metricName),
                  threshold: this.getMetricThreshold(metricType, metricName),
                  passed: this.isMetricPassed(metricType, metricName, value),
                  timestamp,
                  framework
                })
                .do();
            }
          }
        }
      }

      this.logger.debug('Métriques détaillées stockées', { testId: testResult.id });

    } catch (error) {
      this.logger.error('Erreur lors du stockage des métriques détaillées', { 
        testId: testResult.id,
        error: error.message 
      });
    }
  }

  /**
   * Recherche des tests similaires
   */
  async findSimilarTests(
    testType: string,
    framework: string,
    limit: number = 5
  ): Promise<any[]> {
    if (!this.isConnected) {
      return [];
    }

    try {
      this.logger.info('Recherche de tests similaires', { testType, framework });

      const result = await this.client.graphql
        .get()
        .withClassName(this.collections.TestResult)
        .withFields('testId testType framework status successRate duration recommendations')
        .withWhere({
          operator: 'And',
          operands: [
            {
              path: ['testType'],
              operator: 'Equal',
              valueText: testType
            },
            {
              path: ['framework'],
              operator: 'Equal',
              valueText: framework
            }
          ]
        })
        .withSort([{ path: ['successRate'], order: 'desc' }])
        .withLimit(limit)
        .do();

      return result.data.Get[this.collections.TestResult] || [];

    } catch (error) {
      this.logger.error('Erreur lors de la recherche de tests similaires', { error: error.message });
      return [];
    }
  }

  /**
   * Obtient les métriques d'un test
   */
  async getTestMetrics(testId: string, timeRange?: string): Promise<any[]> {
    if (!this.isConnected) {
      return [];
    }

    try {
      let whereClause: any = {
        path: ['testId'],
        operator: 'Equal',
        valueText: testId
      };

      if (timeRange) {
        const since = new Date(Date.now() - this.parseTimeRange(timeRange));
        whereClause = {
          operator: 'And',
          operands: [
            whereClause,
            {
              path: ['timestamp'],
              operator: 'GreaterThan',
              valueDate: since.toISOString()
            }
          ]
        };
      }

      const result = await this.client.graphql
        .get()
        .withClassName(this.collections.QualityMetric)
        .withFields('metricType metricName value unit threshold passed timestamp')
        .withWhere(whereClause)
        .withSort([{ path: ['timestamp'], order: 'desc' }])
        .withLimit(1000)
        .do();

      return result.data.Get[this.collections.QualityMetric] || [];

    } catch (error) {
      this.logger.error('Erreur lors de la récupération des métriques de test', { 
        testId,
        error: error.message 
      });
      return [];
    }
  }

  /**
   * Obtient les métriques globales
   */
  async getOverallTestMetrics(timeRange?: string): Promise<any> {
    if (!this.isConnected) {
      return { connected: false };
    }

    try {
      let whereClause: any = null;

      if (timeRange) {
        const since = new Date(Date.now() - this.parseTimeRange(timeRange));
        whereClause = {
          path: ['executedAt'],
          operator: 'GreaterThan',
          valueDate: since.toISOString()
        };
      }

      let query = this.client.graphql
        .aggregate()
        .withClassName(this.collections.TestResult)
        .withFields('meta { count } successRate { mean } duration { mean }');

      if (whereClause) {
        query = query.withWhere(whereClause);
      }

      const result = await query.do();
      const aggregation = result.data.Aggregate[this.collections.TestResult]?.[0];

      return {
        connected: true,
        totalTests: aggregation?.meta?.count || 0,
        averageSuccessRate: aggregation?.successRate?.mean || 0,
        averageDuration: aggregation?.duration?.mean || 0
      };

    } catch (error) {
      this.logger.error('Erreur lors de la récupération des métriques globales', { error: error.message });
      return { connected: false, error: error.message };
    }
  }

  /**
   * Stocke un pattern de test
   */
  async storeTestPattern(pattern: any): Promise<string> {
    if (!this.isConnected) {
      throw new Error('Weaviate non connecté');
    }

    try {
      const result = await this.client.data
        .creator()
        .withClassName(this.collections.TestPattern)
        .withProperties({
          patternName: pattern.name,
          testType: pattern.type,
          framework: pattern.framework,
          template: pattern.template,
          description: pattern.description,
          successRate: pattern.successRate || 0,
          usageCount: 1,
          createdAt: new Date().toISOString(),
          lastUsed: new Date().toISOString()
        })
        .do();

      this.logger.info('Pattern de test stocké', { id: result.id, name: pattern.name });
      return result.id;

    } catch (error) {
      this.logger.error('Erreur lors du stockage du pattern', { error: error.message });
      throw error;
    }
  }

  // Méthodes utilitaires

  private async createSchemaIfNotExists(className: string, schema: any): Promise<void> {
    try {
      const exists = await this.client.schema.exists(className);
      if (!exists) {
        await this.client.schema.classCreator().withClass(schema).do();
        this.logger.info(`Schéma ${className} créé`);
      }
    } catch (error) {
      this.logger.warn(`Erreur lors de la création du schéma ${className}`, { error: error.message });
    }
  }

  private extractFramework(testResult: TestResult): string {
    // Extraire le framework depuis les tags ou la configuration
    const tags = testResult.details?.configuration?.tags || {};
    return tags.framework || 'unknown';
  }

  private extractSourceAgent(testResult: TestResult): string {
    // Extraire l'agent source depuis les métadonnées
    return 'agent-frontend'; // Par défaut
  }

  private generateTags(testResult: TestResult): string[] {
    const tags = [testResult.type, testResult.status];
    
    if (testResult.summary.successRate === 1) {
      tags.push('perfect-score');
    } else if (testResult.summary.successRate < 0.5) {
      tags.push('needs-attention');
    }

    return tags;
  }

  private getMetricUnit(metricType: string, metricName: string): string {
    const units: Record<string, Record<string, string>> = {
      performance: { 
        lcp: 'ms', fid: 'ms', cls: 'score', 
        performance: '/100', accessibility: '/100', seo: '/100' 
      },
      accessibility: { score: '/100', violations: 'count' },
      security: { score: '/100', vulnerabilities: 'count' },
      visual: { similarity: '%', pixelDifference: 'pixels' }
    };

    return units[metricType]?.[metricName] || 'unknown';
  }

  private getMetricThreshold(metricType: string, metricName: string): number {
    const thresholds: Record<string, Record<string, number>> = {
      performance: { 
        lcp: 2500, fid: 100, cls: 0.1,
        performance: 90, accessibility: 90, seo: 90 
      },
      accessibility: { score: 90 },
      security: { score: 95 },
      visual: { similarity: 95 }
    };

    return thresholds[metricType]?.[metricName] || 0;
  }

  private isMetricPassed(metricType: string, metricName: string, value: number): boolean {
    const threshold = this.getMetricThreshold(metricType, metricName);
    
    // Pour certaines métriques, plus c'est bas, mieux c'est
    const lowerIsBetter = ['lcp', 'fid', 'cls', 'violations', 'pixelDifference'];
    
    if (lowerIsBetter.includes(metricName)) {
      return value <= threshold;
    } else {
      return value >= threshold;
    }
  }

  private parseTimeRange(timeRange: string): number {
    const units: Record<string, number> = {
      'm': 60 * 1000,
      'h': 60 * 60 * 1000,
      'd': 24 * 60 * 60 * 1000
    };

    const match = timeRange.match(/^(\d+)([mhd])$/);
    if (match) {
      const value = parseInt(match[1]);
      const unit = match[2];
      return value * units[unit];
    }

    return 60 * 60 * 1000; // 1 heure par défaut
  }

  /**
   * Vérifie l'état de la connexion
   */
  isConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Ferme la connexion
   */
  async close(): Promise<void> {
    this.isConnected = false;
    this.logger.info('Connexion Weaviate QA fermée');
  }
}
