import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { 
  TestRequest,
  TestResult,
  TestType,
  AgentConfig,
  AgentMessage
} from '../types';
import { UnitTestRunner } from '../runners/UnitTestRunner';
import { E2ETestRunner } from '../runners/E2ETestRunner';
import { PerformanceTestRunner } from '../runners/PerformanceTestRunner';
import { AccessibilityTestRunner } from '../runners/AccessibilityTestRunner';
import { SecurityTestRunner } from '../runners/SecurityTestRunner';
import { VisualTestRunner } from '../runners/VisualTestRunner';
import { ApiTestRunner } from '../runners/ApiTestRunner';
import { LoadTestRunner } from '../runners/LoadTestRunner';
import { CodeQualityAnalyzer } from '../analyzers/CodeQualityAnalyzer';
import { TestReportGenerator } from '../reports/TestReportGenerator';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import { KafkaCommunication } from '../communication/KafkaCommunication';

/**
 * Agent QA - Automatisation des Tests et Assurance Qualité
 * 
 * Cet agent gère automatiquement tous les types de tests :
 * unitaires, intégration, e2e, performance, accessibilité, sécurité.
 */
export class QAAgent extends EventEmitter {
  private logger: Logger;
  private config: AgentConfig;
  private memory: WeaviateMemory;
  private communication: KafkaCommunication;
  
  // Test runners spécialisés
  private unitTestRunner: UnitTestRunner;
  private e2eTestRunner: E2ETestRunner;
  private performanceTestRunner: PerformanceTestRunner;
  private accessibilityTestRunner: AccessibilityTestRunner;
  private securityTestRunner: SecurityTestRunner;
  private visualTestRunner: VisualTestRunner;
  private apiTestRunner: ApiTestRunner;
  private loadTestRunner: LoadTestRunner;

  // Analyseurs et générateurs
  private codeQualityAnalyzer: CodeQualityAnalyzer;
  private reportGenerator: TestReportGenerator;

  // État interne
  private activeTests: Map<string, TestResult> = new Map();
  private testQueue: TestRequest[] = [];
  private isProcessingQueue: boolean = false;

  constructor(
    config: AgentConfig,
    logger: Logger,
    memory: WeaviateMemory,
    communication: KafkaCommunication
  ) {
    super();
    this.config = config;
    this.logger = logger;
    this.memory = memory;
    this.communication = communication;

    // Initialiser les test runners
    this.unitTestRunner = new UnitTestRunner(logger, memory);
    this.e2eTestRunner = new E2ETestRunner(logger, memory);
    this.performanceTestRunner = new PerformanceTestRunner(logger, memory);
    this.accessibilityTestRunner = new AccessibilityTestRunner(logger, memory);
    this.securityTestRunner = new SecurityTestRunner(logger, memory);
    this.visualTestRunner = new VisualTestRunner(logger, memory);
    this.apiTestRunner = new ApiTestRunner(logger, memory);
    this.loadTestRunner = new LoadTestRunner(logger, memory);

    // Initialiser les analyseurs
    this.codeQualityAnalyzer = new CodeQualityAnalyzer(logger, memory);
    this.reportGenerator = new TestReportGenerator(logger, memory);

    this.setupEventHandlers();
    this.startQueueProcessor();

    this.logger.info(`Agent QA ${config.id} initialisé`);
  }

  /**
   * Point d'entrée principal pour exécuter des tests
   */
  async runTests(request: TestRequest): Promise<TestResult> {
    this.logger.info('Début des tests', { 
      id: request.id,
      type: request.type,
      source: request.source.type 
    });

    try {
      // 1. Validation de la demande
      this.logger.info('Phase 1: Validation de la demande de test');
      await this.validateTestRequest(request);

      // 2. Préparation de l'environnement de test
      this.logger.info('Phase 2: Préparation de l\'environnement de test');
      await this.prepareTestEnvironment(request);

      // 3. Analyse du code source (si applicable)
      this.logger.info('Phase 3: Analyse du code source');
      const codeAnalysis = await this.analyzeSourceCode(request);

      // 4. Génération des tests automatiques
      this.logger.info('Phase 4: Génération des tests automatiques');
      const generatedTests = await this.generateTests(request, codeAnalysis);

      // 5. Exécution des tests selon le type
      this.logger.info('Phase 5: Exécution des tests');
      const testResult = await this.executeTests(request, generatedTests);

      // 6. Analyse des résultats
      this.logger.info('Phase 6: Analyse des résultats');
      const analyzedResult = await this.analyzeResults(testResult);

      // 7. Génération des rapports
      this.logger.info('Phase 7: Génération des rapports');
      const reports = await this.generateReports(analyzedResult);
      analyzedResult.reports = reports;

      // 8. Stockage en mémoire
      await this.memory.storeTestResult(analyzedResult);
      this.activeTests.set(analyzedResult.id, analyzedResult);

      // 9. Notification des résultats
      await this.notifyTestCompletion(analyzedResult);

      this.logger.info('Tests terminés', { 
        id: analyzedResult.id,
        status: analyzedResult.status,
        successRate: analyzedResult.summary.successRate 
      });

      this.emit('testCompleted', analyzedResult);
      return analyzedResult;

    } catch (error) {
      this.logger.error('Erreur lors des tests', { 
        id: request.id,
        error: error.message 
      });

      const failedResult: TestResult = {
        id: request.id,
        type: request.type,
        status: 'error',
        summary: {
          total: 0,
          passed: 0,
          failed: 1,
          skipped: 0,
          errors: 1,
          successRate: 0
        },
        details: {
          suites: [],
          environment: {
            os: process.platform,
            node: process.version,
            timestamp: new Date()
          },
          configuration: request.configuration
        },
        reports: [],
        artifacts: [],
        metrics: {},
        issues: [{
          id: 'test-error-001',
          type: 'bug',
          severity: 'critical',
          title: 'Erreur lors de l\'exécution des tests',
          description: error.message
        }],
        recommendations: ['Vérifier la configuration des tests', 'Consulter les logs pour plus de détails'],
        startedAt: new Date(),
        completedAt: new Date()
      };

      await this.notifyTestFailure(failedResult, error);
      this.emit('testFailed', failedResult);
      throw error;
    }
  }

  /**
   * Exécute une suite complète de tests
   */
  async runFullTestSuite(request: TestRequest): Promise<TestResult> {
    this.logger.info('Exécution de la suite complète de tests', { id: request.id });

    const testTypes: TestType[] = [
      'unit',
      'integration',
      'e2e',
      'performance',
      'accessibility',
      'security'
    ];

    const results: TestResult[] = [];

    for (const testType of testTypes) {
      try {
        const typeRequest: TestRequest = {
          ...request,
          id: `${request.id}-${testType}`,
          type: testType
        };

        const result = await this.runTests(typeRequest);
        results.push(result);
      } catch (error) {
        this.logger.warn(`Tests ${testType} échoués`, { error: error.message });
      }
    }

    // Consolider les résultats
    return this.consolidateResults(request.id, results);
  }

  /**
   * Analyse la qualité du code
   */
  async analyzeCodeQuality(code: string, framework: string): Promise<any> {
    this.logger.info('Analyse de la qualité du code', { framework });

    try {
      return await this.codeQualityAnalyzer.analyze(code, framework);
    } catch (error) {
      this.logger.error('Erreur lors de l\'analyse de qualité', { error: error.message });
      throw error;
    }
  }

  /**
   * Génère des tests automatiquement
   */
  async generateAutomaticTests(request: TestRequest): Promise<any> {
    this.logger.info('Génération automatique de tests', { 
      type: request.type,
      framework: request.source.code?.framework 
    });

    try {
      const codeAnalysis = await this.analyzeSourceCode(request);
      return await this.generateTests(request, codeAnalysis);
    } catch (error) {
      this.logger.error('Erreur lors de la génération de tests', { error: error.message });
      throw error;
    }
  }

  /**
   * Obtient les métriques de test
   */
  async getTestMetrics(testId?: string, timeRange?: string): Promise<any> {
    this.logger.info('Récupération des métriques de test', { testId, timeRange });

    try {
      if (testId) {
        return await this.memory.getTestMetrics(testId, timeRange);
      } else {
        return await this.memory.getOverallTestMetrics(timeRange);
      }
    } catch (error) {
      this.logger.error('Erreur lors de la récupération des métriques', { error: error.message });
      throw error;
    }
  }

  // Méthodes privées

  private async validateTestRequest(request: TestRequest): Promise<void> {
    if (!request.id) {
      throw new Error('ID de test requis');
    }
    if (!request.type) {
      throw new Error('Type de test requis');
    }
    if (!this.config.testing.supportedTypes.includes(request.type)) {
      throw new Error(`Type de test ${request.type} non supporté`);
    }
  }

  private async prepareTestEnvironment(request: TestRequest): Promise<void> {
    this.logger.info('Préparation de l\'environnement de test');

    // Créer les répertoires nécessaires
    // Configurer les outils de test
    // Installer les dépendances si nécessaire
  }

  private async analyzeSourceCode(request: TestRequest): Promise<any> {
    if (request.source.type === 'code' && request.source.code) {
      const code = request.source.code.files.map(f => f.content).join('\n');
      return await this.codeQualityAnalyzer.analyze(code, request.source.code.framework);
    }
    return null;
  }

  private async generateTests(request: TestRequest, codeAnalysis: any): Promise<any> {
    const tests = [];

    // Générer des tests basés sur l'analyse du code
    if (codeAnalysis) {
      // Générer des tests unitaires pour les fonctions détectées
      // Générer des tests d'intégration pour les composants
      // Générer des tests e2e pour les pages
    }

    return tests;
  }

  private async executeTests(request: TestRequest, generatedTests: any): Promise<TestResult> {
    switch (request.type) {
      case 'unit':
        return await this.unitTestRunner.run(request, generatedTests);
      case 'integration':
        return await this.unitTestRunner.run(request, generatedTests); // Réutilise le runner unitaire
      case 'e2e':
        return await this.e2eTestRunner.run(request, generatedTests);
      case 'performance':
        return await this.performanceTestRunner.run(request, generatedTests);
      case 'accessibility':
        return await this.accessibilityTestRunner.run(request, generatedTests);
      case 'security':
        return await this.securityTestRunner.run(request, generatedTests);
      case 'visual':
        return await this.visualTestRunner.run(request, generatedTests);
      case 'api':
        return await this.apiTestRunner.run(request, generatedTests);
      case 'load':
        return await this.loadTestRunner.run(request, generatedTests);
      case 'full-suite':
        return await this.runFullTestSuite(request);
      default:
        throw new Error(`Type de test ${request.type} non implémenté`);
    }
  }

  private async analyzeResults(testResult: TestResult): Promise<TestResult> {
    // Analyser les résultats pour détecter des patterns
    // Générer des recommandations
    // Identifier les problèmes récurrents

    const recommendations = [];

    if (testResult.summary.successRate < 0.8) {
      recommendations.push('Taux de succès faible - Réviser les tests ou le code');
    }

    if (testResult.metrics.performance) {
      // Analyser les métriques de performance
    }

    if (testResult.metrics.accessibility) {
      // Analyser les métriques d'accessibilité
    }

    testResult.recommendations = recommendations;
    return testResult;
  }

  private async generateReports(testResult: TestResult): Promise<any[]> {
    return await this.reportGenerator.generate(testResult);
  }

  private async consolidateResults(id: string, results: TestResult[]): Promise<TestResult> {
    const consolidatedResult: TestResult = {
      id,
      type: 'full-suite',
      status: results.every(r => r.status === 'passed') ? 'passed' : 'failed',
      summary: {
        total: results.reduce((sum, r) => sum + r.summary.total, 0),
        passed: results.reduce((sum, r) => sum + r.summary.passed, 0),
        failed: results.reduce((sum, r) => sum + r.summary.failed, 0),
        skipped: results.reduce((sum, r) => sum + r.summary.skipped, 0),
        errors: results.reduce((sum, r) => sum + r.summary.errors, 0),
        successRate: 0
      },
      details: {
        suites: results.flatMap(r => r.details.suites),
        environment: results[0]?.details.environment || {
          os: process.platform,
          node: process.version,
          timestamp: new Date()
        },
        configuration: results[0]?.details.configuration || {}
      },
      reports: results.flatMap(r => r.reports),
      artifacts: results.flatMap(r => r.artifacts),
      metrics: this.mergeMetrics(results.map(r => r.metrics)),
      issues: results.flatMap(r => r.issues),
      recommendations: [...new Set(results.flatMap(r => r.recommendations))],
      startedAt: new Date(Math.min(...results.map(r => r.startedAt.getTime()))),
      completedAt: new Date(Math.max(...results.map(r => (r.completedAt || r.startedAt).getTime())))
    };

    consolidatedResult.summary.successRate = consolidatedResult.summary.total > 0 
      ? consolidatedResult.summary.passed / consolidatedResult.summary.total 
      : 0;

    return consolidatedResult;
  }

  private mergeMetrics(metricsArray: any[]): any {
    // Fusionner les métriques de tous les tests
    const merged = {};
    
    for (const metrics of metricsArray) {
      Object.assign(merged, metrics);
    }
    
    return merged;
  }

  private setupEventHandlers(): void {
    this.communication.on('testRequest', this.handleTestRequest.bind(this));
    this.communication.on('codeGenerated', this.handleCodeGenerated.bind(this));
    this.communication.on('deploymentComplete', this.handleDeploymentComplete.bind(this));

    this.on('testCompleted', (result) => {
      this.logger.info('Test terminé', { id: result.id, status: result.status });
    });

    this.on('testFailed', (result) => {
      this.logger.error('Test échoué', { id: result.id, issues: result.issues.length });
    });
  }

  private startQueueProcessor(): void {
    setInterval(async () => {
      if (!this.isProcessingQueue && this.testQueue.length > 0) {
        this.isProcessingQueue = true;
        
        try {
          const request = this.testQueue.shift();
          if (request) {
            await this.runTests(request);
          }
        } catch (error) {
          this.logger.error('Erreur lors du traitement de la queue de tests', { 
            error: error.message 
          });
        } finally {
          this.isProcessingQueue = false;
        }
      }
    }, 5000);
  }

  // Gestionnaires d'événements

  private async handleTestRequest(message: AgentMessage): Promise<void> {
    try {
      const request: TestRequest = message.payload;
      
      if (this.activeTests.size < this.config.testing.maxParallelTests) {
        const result = await this.runTests(request);
        await this.communication.sendResponse(message.correlationId, result);
      } else {
        this.testQueue.push(request);
        await this.communication.sendResponse(message.correlationId, { 
          status: 'queued',
          position: this.testQueue.length 
        });
      }
    } catch (error) {
      await this.communication.sendError(message.correlationId, error.message);
    }
  }

  private async handleCodeGenerated(message: AgentMessage): Promise<void> {
    try {
      const { generatedCode } = message.payload;
      
      // Créer automatiquement une demande de test
      const testRequest: TestRequest = {
        id: `auto-test-${Date.now()}`,
        type: 'full-suite',
        source: {
          type: 'code',
          code: generatedCode
        },
        configuration: {
          timeout: this.config.testing.defaultTimeout,
          parallel: true,
          headless: true
        },
        metadata: {
          requestedBy: 'auto-generated',
          requestedAt: new Date(),
          sourceAgent: message.from,
          correlationId: message.correlationId,
          tags: { auto: 'true', framework: generatedCode.framework },
          priority: 'medium'
        }
      };

      const result = await this.runTests(testRequest);
      await this.communication.notifyTestComplete(result, message.correlationId);
    } catch (error) {
      this.logger.error('Erreur lors du test automatique du code généré', { 
        error: error.message 
      });
    }
  }

  private async handleDeploymentComplete(message: AgentMessage): Promise<void> {
    try {
      const { deployment } = message.payload;
      
      if (deployment.url) {
        // Créer automatiquement des tests e2e sur le déploiement
        const testRequest: TestRequest = {
          id: `deployment-test-${Date.now()}`,
          type: 'e2e',
          source: {
            type: 'url',
            url: deployment.url
          },
          configuration: {
            timeout: this.config.testing.defaultTimeout,
            browsers: [{ name: 'chromium', viewport: { width: 1920, height: 1080 } }],
            headless: true
          },
          metadata: {
            requestedBy: 'auto-generated',
            requestedAt: new Date(),
            sourceAgent: message.from,
            correlationId: message.correlationId,
            tags: { auto: 'true', deployment: 'true' },
            priority: 'high'
          }
        };

        const result = await this.runTests(testRequest);
        await this.communication.notifyTestComplete(result, message.correlationId);
      }
    } catch (error) {
      this.logger.error('Erreur lors du test automatique du déploiement', { 
        error: error.message 
      });
    }
  }

  private async notifyTestCompletion(result: TestResult): Promise<void> {
    await this.communication.notifyTestComplete(result);
  }

  private async notifyTestFailure(result: TestResult, error: Error): Promise<void> {
    await this.communication.notifyTestFailed(result, error);
  }

  /**
   * Obtient le statut de l'agent
   */
  getStatus(): any {
    return {
      activeTests: this.activeTests.size,
      queuedTests: this.testQueue.length,
      isProcessingQueue: this.isProcessingQueue,
      supportedTypes: this.config.testing.supportedTypes,
      maxParallelTests: this.config.testing.maxParallelTests,
      uptime: process.uptime()
    };
  }

  /**
   * Arrêt gracieux de l'agent
   */
  async shutdown(): Promise<void> {
    this.logger.info('Arrêt de l\'agent QA');
    
    // Attendre la fin des tests en cours
    while (this.isProcessingQueue) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Fermer les connexions
    await this.communication.disconnect();
    await this.memory.close();
    
    this.logger.info('Agent QA arrêté');
  }
}
