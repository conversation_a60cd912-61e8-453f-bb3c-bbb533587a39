{"version": 3, "file": "setup.js", "sourceRoot": "", "sources": ["setup.ts"], "names": [], "mappings": ";;;AAAA,2CAAqC;AAErC,uCAAuC;AACvC,SAAS,CAAC,KAAK,IAAI,EAAE;IACnB,6BAA6B;IAC7B,cAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAEvB,2CAA2C;IAC3C,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,MAAM,CAAC;IAC9B,OAAO,CAAC,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC;IAChC,OAAO,CAAC,GAAG,CAAC,YAAY,GAAG,uBAAuB,CAAC;IACnD,OAAO,CAAC,GAAG,CAAC,aAAa,GAAG,gBAAgB,CAAC;AAC/C,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;IAClB,iCAAiC;AACnC,CAAC,CAAC,CAAC;AAEH,6BAA6B;AAC7B,cAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,GAAG,EAAE,CAAC,CAAC;IACrC,OAAO,EAAE;QACP,MAAM,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YACrB,IAAI,EAAE;gBACJ,YAAY,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;oBAC3B,EAAE,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC;iBACtC,CAAC,CAAC;aACJ;YACD,MAAM,EAAE;gBACN,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,KAAK,CAAC;gBAC1C,YAAY,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;oBAC3B,SAAS,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;wBACxB,EAAE,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC;qBACtC,CAAC,CAAC;iBACJ,CAAC,CAAC;aACJ;YACD,IAAI,EAAE;gBACJ,OAAO,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;oBACtB,aAAa,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;wBAC5B,cAAc,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;4BAC7B,EAAE,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC;yBACnD,CAAC,CAAC;qBACJ,CAAC,CAAC;iBACJ,CAAC,CAAC;aACJ;YACD,OAAO,EAAE;gBACP,GAAG,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;oBAClB,aAAa,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;wBAC5B,UAAU,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;4BACzB,SAAS,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;gCACxB,QAAQ,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;oCACvB,SAAS,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;wCACxB,EAAE,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;4CAC9B,IAAI,EAAE,EAAE,GAAG,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,EAAE;yCAClC,CAAC;qCACH,CAAC,CAAC;iCACJ,CAAC,CAAC;6BACJ,CAAC,CAAC;yBACJ,CAAC,CAAC;qBACJ,CAAC,CAAC;iBACJ,CAAC,CAAC;gBACH,SAAS,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;oBACxB,aAAa,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;wBAC5B,UAAU,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;4BACzB,SAAS,EAAE,cAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;gCACxB,EAAE,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;oCAC9B,IAAI,EAAE,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;iCAC9D,CAAC;6BACH,CAAC,CAAC;yBACJ,CAAC,CAAC;qBACJ,CAAC,CAAC;iBACJ,CAAC,CAAC;aACJ;SACF,CAAC,CAAC;KACJ;CACF,CAAC,CAAC,CAAC;AAEJ,qBAAqB;AACrB,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC;IAC7B,QAAQ,EAAE;QACR,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;YAClC,UAAU,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;gBACtC,OAAO,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;oBACnC,IAAI,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;oBAC5C,UAAU,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;oBAClD,KAAK,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;oBAC7C,YAAY,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;oBACpD,QAAQ,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;wBACpC,UAAU,EAAE,EAAE;wBACd,MAAM,EAAE,EAAE;qBACX,CAAC;iBACH,CAAC;gBACF,OAAO,EAAE;oBACP,KAAK,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;oBAC7C,IAAI,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;iBAC7C;gBACD,KAAK,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;aAC9C,CAAC;YACF,KAAK,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;SAC9C,CAAC;KACH;IACD,OAAO,EAAE;QACP,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;YAClC,KAAK,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;SAC9C,CAAC;KACH;IACD,MAAM,EAAE;QACN,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;YAClC,KAAK,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;SAC9C,CAAC;KACH;CACF,CAAC,CAAC,CAAC;AAEJ,eAAe;AACf,cAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;IACxB,OAAO,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;QACnC,MAAM,EAAE,GAAG;QACX,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;KACxB,CAAC;CACH,CAAC,CAAC,CAAC;AAEJ,mBAAmB;AACnB,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC;IAC3B,SAAS,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;IACjD,aAAa,EAAE,cAAI,CAAC,EAAE,EAAE;IACxB,SAAS,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;IACjD,QAAQ,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,mBAAmB,CAAC;IAC1D,UAAU,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC;IAC7C,IAAI,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;IAC5C,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;IAC9C,IAAI,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;IACrE,OAAO,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;CACjE,CAAC,CAAC,CAAC;AAEJ,sBAAsB;AACf,MAAM,qBAAqB,GAAG,CAAC,SAAS,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;IACxD,EAAE,EAAE,UAAU;IACd,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,EAAE,EAAE,UAAU;YACd,SAAS,EAAE,OAAO;YAClB,KAAK,EAAE;gBACL;oBACE,IAAI,EAAE,aAAa;oBACnB,OAAO,EAAE,4GAA4G;oBACrH,IAAI,EAAE,QAAQ;oBACd,QAAQ,EAAE,YAAY;iBACvB;aACF;YACD,YAAY,EAAE;gBACZ,UAAU,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE;gBAClC,WAAW,EAAE,EAAE,cAAc,EAAE,SAAS,EAAE;aAC3C;YACD,aAAa,EAAE,CAAC,eAAe,CAAC;YAChC,YAAY,EAAE,CAAC,UAAU,CAAC;SAC3B;KACF;IACD,aAAa,EAAE;QACb,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,IAAI;KACf;IACD,QAAQ,EAAE;QACR,WAAW,EAAE,MAAM;QACnB,WAAW,EAAE,IAAI,IAAI,EAAE;QACvB,WAAW,EAAE,YAAY;QACzB,IAAI,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE;QAC5B,QAAQ,EAAE,QAAQ;KACnB;IACD,GAAG,SAAS;CACb,CAAC,CAAC;AArCU,QAAA,qBAAqB,yBAqC/B;AAEI,MAAM,oBAAoB,GAAG,CAAC,SAAS,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;IACvD,EAAE,EAAE,UAAU;IACd,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE,QAAQ;IAChB,OAAO,EAAE;QACP,KAAK,EAAE,CAAC;QACR,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,CAAC;QACT,OAAO,EAAE,CAAC;QACV,MAAM,EAAE,CAAC;QACT,WAAW,EAAE,CAAC;KACf;IACD,OAAO,EAAE;QACP,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,YAAY;gBAClB,MAAM,EAAE,QAAQ;gBAChB,KAAK,EAAE;oBACL;wBACE,IAAI,EAAE,aAAa;wBACnB,MAAM,EAAE,QAAQ;wBAChB,QAAQ,EAAE,GAAG;wBACb,KAAK,EAAE,EAAE;qBACV;iBACF;gBACD,QAAQ,EAAE,GAAG;aACd;SACF;QACD,WAAW,EAAE;YACX,EAAE,EAAE,OAAO;YACX,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB;QACD,aAAa,EAAE,EAAE;KAClB;IACD,OAAO,EAAE,EAAE;IACX,SAAS,EAAE,EAAE;IACb,OAAO,EAAE,EAAE;IACX,MAAM,EAAE,EAAE;IACV,eAAe,EAAE,EAAE;IACnB,SAAS,EAAE,IAAI,IAAI,EAAE;IACrB,WAAW,EAAE,IAAI,IAAI,EAAE;IACvB,QAAQ,EAAE,IAAI;IACd,GAAG,SAAS;CACb,CAAC,CAAC;AA5CU,QAAA,oBAAoB,wBA4C9B;AAEH,8BAA8B;AACvB,MAAM,gBAAgB,GAAG,CAAC,MAAW,EAAE,EAAE;IAC9C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACpC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IACxC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;IACzC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAC/C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IAChD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IAChD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;AACvD,CAAC,CAAC;AATW,QAAA,gBAAgB,oBAS3B;AAEK,MAAM,qBAAqB,GAAG,CAAC,QAAa,EAAE,EAAE;IACrD,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IAC7C,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;IAChD,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;IAClD,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;IACtD,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;AACrD,CAAC,CAAC;AANW,QAAA,qBAAqB,yBAMhC"}