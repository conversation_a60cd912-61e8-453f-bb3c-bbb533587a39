export declare const createMockTestRequest: (overrides?: {}) => {
    id: string;
    type: string;
    source: {
        type: string;
        code: {
            id: string;
            framework: string;
            files: {
                path: string;
                content: string;
                type: string;
                language: string;
            }[];
            dependencies: {
                production: {
                    react: string;
                };
                development: {
                    '@types/react': string;
                };
            };
            buildCommands: string[];
            testCommands: string[];
        };
    };
    configuration: {
        timeout: number;
        parallel: boolean;
        headless: boolean;
    };
    metadata: {
        requestedBy: string;
        requestedAt: Date;
        sourceAgent: string;
        tags: {
            framework: string;
        };
        priority: string;
    };
};
export declare const createMockTestResult: (overrides?: {}) => {
    id: string;
    type: string;
    status: string;
    summary: {
        total: number;
        passed: number;
        failed: number;
        skipped: number;
        errors: number;
        successRate: number;
    };
    details: {
        suites: {
            name: string;
            status: string;
            tests: {
                name: string;
                status: string;
                duration: number;
                steps: never[];
            }[];
            duration: number;
        }[];
        environment: {
            os: string;
            node: string;
            timestamp: Date;
        };
        configuration: {};
    };
    reports: never[];
    artifacts: never[];
    metrics: {};
    issues: never[];
    recommendations: never[];
    startedAt: Date;
    completedAt: Date;
    duration: number;
};
export declare const expectTestResult: (result: any) => void;
export declare const expectQualityAnalysis: (analysis: any) => void;
//# sourceMappingURL=setup.d.ts.map