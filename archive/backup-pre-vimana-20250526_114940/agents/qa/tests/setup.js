"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.expectQualityAnalysis = exports.expectTestResult = exports.createMockTestResult = exports.createMockTestRequest = void 0;
const globals_1 = require("@jest/globals");
// Configuration globale pour les tests
beforeAll(async () => {
    // Configuration des timeouts
    globals_1.jest.setTimeout(30000);
    // Variables d'environnement pour les tests
    process.env.NODE_ENV = 'test';
    process.env.LOG_LEVEL = 'error';
    process.env.WEAVIATE_URL = 'http://localhost:8080';
    process.env.KAFKA_BROKERS = 'localhost:9092';
});
afterAll(async () => {
    // Nettoyage après tous les tests
});
// Mock des services externes
globals_1.jest.mock('weaviate-ts-client', () => ({
    default: {
        client: globals_1.jest.fn(() => ({
            misc: {
                readyChecker: globals_1.jest.fn(() => ({
                    do: globals_1.jest.fn().mockResolvedValue(true)
                }))
            },
            schema: {
                exists: globals_1.jest.fn().mockResolvedValue(false),
                classCreator: globals_1.jest.fn(() => ({
                    withClass: globals_1.jest.fn(() => ({
                        do: globals_1.jest.fn().mockResolvedValue(true)
                    }))
                }))
            },
            data: {
                creator: globals_1.jest.fn(() => ({
                    withClassName: globals_1.jest.fn(() => ({
                        withProperties: globals_1.jest.fn(() => ({
                            do: globals_1.jest.fn().mockResolvedValue({ id: 'test-id' })
                        }))
                    }))
                }))
            },
            graphql: {
                get: globals_1.jest.fn(() => ({
                    withClassName: globals_1.jest.fn(() => ({
                        withFields: globals_1.jest.fn(() => ({
                            withWhere: globals_1.jest.fn(() => ({
                                withSort: globals_1.jest.fn(() => ({
                                    withLimit: globals_1.jest.fn(() => ({
                                        do: globals_1.jest.fn().mockResolvedValue({
                                            data: { Get: { TestResult: [] } }
                                        })
                                    }))
                                }))
                            }))
                        }))
                    }))
                })),
                aggregate: globals_1.jest.fn(() => ({
                    withClassName: globals_1.jest.fn(() => ({
                        withFields: globals_1.jest.fn(() => ({
                            withWhere: globals_1.jest.fn(() => ({
                                do: globals_1.jest.fn().mockResolvedValue({
                                    data: { Aggregate: { TestResult: [{ meta: { count: 0 } }] } }
                                })
                            }))
                        }))
                    }))
                }))
            }
        }))
    }
}));
// Mock de Playwright
globals_1.jest.mock('playwright', () => ({
    chromium: {
        launch: globals_1.jest.fn().mockResolvedValue({
            newContext: globals_1.jest.fn().mockResolvedValue({
                newPage: globals_1.jest.fn().mockResolvedValue({
                    goto: globals_1.jest.fn().mockResolvedValue(undefined),
                    screenshot: globals_1.jest.fn().mockResolvedValue(undefined),
                    close: globals_1.jest.fn().mockResolvedValue(undefined),
                    addScriptTag: globals_1.jest.fn().mockResolvedValue(undefined),
                    evaluate: globals_1.jest.fn().mockResolvedValue({
                        violations: [],
                        passes: []
                    })
                }),
                tracing: {
                    start: globals_1.jest.fn().mockResolvedValue(undefined),
                    stop: globals_1.jest.fn().mockResolvedValue(undefined)
                },
                close: globals_1.jest.fn().mockResolvedValue(undefined)
            }),
            close: globals_1.jest.fn().mockResolvedValue(undefined)
        })
    },
    firefox: {
        launch: globals_1.jest.fn().mockResolvedValue({
            close: globals_1.jest.fn().mockResolvedValue(undefined)
        })
    },
    webkit: {
        launch: globals_1.jest.fn().mockResolvedValue({
            close: globals_1.jest.fn().mockResolvedValue(undefined)
        })
    }
}));
// Mock d'Axios
globals_1.jest.mock('axios', () => ({
    default: globals_1.jest.fn().mockResolvedValue({
        status: 200,
        data: { message: 'OK' }
    })
}));
// Mock de fs-extra
globals_1.jest.mock('fs-extra', () => ({
    ensureDir: globals_1.jest.fn().mockResolvedValue(undefined),
    ensureDirSync: globals_1.jest.fn(),
    writeFile: globals_1.jest.fn().mockResolvedValue(undefined),
    readFile: globals_1.jest.fn().mockResolvedValue('mock file content'),
    pathExists: globals_1.jest.fn().mockResolvedValue(true),
    copy: globals_1.jest.fn().mockResolvedValue(undefined),
    remove: globals_1.jest.fn().mockResolvedValue(undefined),
    stat: globals_1.jest.fn().mockResolvedValue({ size: 1024, isFile: () => true }),
    readdir: globals_1.jest.fn().mockResolvedValue(['file1.txt', 'file2.txt'])
}));
// Utilitaires de test
const createMockTestRequest = (overrides = {}) => ({
    id: 'test-001',
    type: 'unit',
    source: {
        type: 'code',
        code: {
            id: 'code-001',
            framework: 'react',
            files: [
                {
                    path: 'src/App.tsx',
                    content: 'import React from "react";\n\nfunction App() {\n  return <div>Hello World</div>;\n}\n\nexport default App;',
                    type: 'source',
                    language: 'typescript'
                }
            ],
            dependencies: {
                production: { 'react': '^18.0.0' },
                development: { '@types/react': '^18.0.0' }
            },
            buildCommands: ['npm run build'],
            testCommands: ['npm test']
        }
    },
    configuration: {
        timeout: 30000,
        parallel: true,
        headless: true
    },
    metadata: {
        requestedBy: 'test',
        requestedAt: new Date(),
        sourceAgent: 'test-agent',
        tags: { framework: 'react' },
        priority: 'medium'
    },
    ...overrides
});
exports.createMockTestRequest = createMockTestRequest;
const createMockTestResult = (overrides = {}) => ({
    id: 'test-001',
    type: 'unit',
    status: 'passed',
    summary: {
        total: 5,
        passed: 5,
        failed: 0,
        skipped: 0,
        errors: 0,
        successRate: 1
    },
    details: {
        suites: [
            {
                name: 'Test Suite',
                status: 'passed',
                tests: [
                    {
                        name: 'Test Case 1',
                        status: 'passed',
                        duration: 100,
                        steps: []
                    }
                ],
                duration: 500
            }
        ],
        environment: {
            os: 'linux',
            node: 'v18.0.0',
            timestamp: new Date()
        },
        configuration: {}
    },
    reports: [],
    artifacts: [],
    metrics: {},
    issues: [],
    recommendations: [],
    startedAt: new Date(),
    completedAt: new Date(),
    duration: 1000,
    ...overrides
});
exports.createMockTestResult = createMockTestResult;
// Helpers pour les assertions
const expectTestResult = (result) => {
    expect(result).toHaveProperty('id');
    expect(result).toHaveProperty('type');
    expect(result).toHaveProperty('status');
    expect(result).toHaveProperty('summary');
    expect(result.summary).toHaveProperty('total');
    expect(result.summary).toHaveProperty('passed');
    expect(result.summary).toHaveProperty('failed');
    expect(result.summary).toHaveProperty('successRate');
};
exports.expectTestResult = expectTestResult;
const expectQualityAnalysis = (analysis) => {
    expect(analysis).toHaveProperty('framework');
    expect(analysis).toHaveProperty('overallScore');
    expect(analysis).toHaveProperty('staticAnalysis');
    expect(analysis).toHaveProperty('complexityAnalysis');
    expect(analysis).toHaveProperty('recommendations');
};
exports.expectQualityAnalysis = expectQualityAnalysis;
//# sourceMappingURL=setup.js.map