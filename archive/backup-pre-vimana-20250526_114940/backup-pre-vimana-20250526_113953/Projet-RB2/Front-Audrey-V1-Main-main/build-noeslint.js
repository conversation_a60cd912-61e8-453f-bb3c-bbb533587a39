const { execSync } = require('child_process');

// Définir les variables d'environnement pour ignorer ESLint pendant le build
process.env.DISABLE_ESLINT_PLUGIN = 'true';
process.env.ESLINT_NO_DEV_ERRORS = 'true';

try {
  console.log('Building with ESLint disabled...');
  execSync('npx react-scripts build', { stdio: 'inherit' });
  console.log('Build completed successfully!');
} catch (error) {
  console.error('Build failed:', error);
  process.exit(1);
} 