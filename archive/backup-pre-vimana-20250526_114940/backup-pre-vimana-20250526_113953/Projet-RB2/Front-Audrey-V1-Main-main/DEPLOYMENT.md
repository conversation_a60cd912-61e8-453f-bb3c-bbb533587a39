# Deployment Guide for Front-Audrey-V1-Main-main

This document provides instructions for deploying the Front-Audrey-V1-Main-main frontend application using Docker and Kubernetes.

## Prerequisites

- Docker installed on your local machine
- kubectl installed and configured to connect to your Kubernetes cluster
- Helm installed (for Helm chart deployment)
- Access to a Docker registry

## Docker Deployment

### Building the Docker Image

To build the Docker image locally:

```bash
cd Front-Audrey-V1-Main-main
docker build -t audrey-frontend:latest .
```

### Running the Docker Container Locally

To run the Docker container locally:

```bash
docker run -p 3000:80 audrey-frontend:latest
```

The application will be available at http://localhost:3000.

### Using Docker Compose

For local development with Docker Compose:

```bash
cd Front-Audrey-V1-Main-main
docker-compose up -d
```

## Kubernetes Deployment

### Using kubectl

To deploy the application to Kubernetes using kubectl:

```bash
cd Front-Audrey-V1-Main-main/kubernetes
./deploy.sh [tag]
```

Where `[tag]` is the Docker image tag to deploy (defaults to `latest`).

### Using Helm

To deploy the application using Helm:

```bash
cd Front-Audrey-V1-Main-main
helm install audrey-frontend ./helm
```

To customize the deployment, you can create a values file:

```bash
helm install audrey-frontend ./helm -f my-values.yaml
```

## Environment Variables

The following environment variables can be configured:

- `REACT_APP_API_URL`: The URL of the backend API
- `NODE_ENV`: The environment (development, production)

## Scaling

The application is configured to scale automatically based on CPU usage. You can manually scale the deployment using:

```bash
kubectl scale deployment audrey-frontend --replicas=5 -n retreat-and-be
```

## Monitoring

The application exposes a `/health` endpoint for health checks. You can monitor the application using:

```bash
kubectl get pods -n retreat-and-be
kubectl logs deployment/audrey-frontend -n retreat-and-be
```

## Troubleshooting

If you encounter issues with the deployment:

1. Check the pod status:
   ```bash
   kubectl get pods -n retreat-and-be
   ```

2. Check the pod logs:
   ```bash
   kubectl logs <pod-name> -n retreat-and-be
   ```

3. Check the deployment status:
   ```bash
   kubectl describe deployment audrey-frontend -n retreat-and-be
   ```

4. Check the service status:
   ```bash
   kubectl describe service audrey-frontend -n retreat-and-be
   ```

5. Check the ingress status:
   ```bash
   kubectl describe ingress audrey-frontend-ingress -n retreat-and-be
   ```
