/**
 * Tests E2E - Réservation de Retraites - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Tests complets du processus de réservation de retraites
 * depuis la recherche jusqu'à la confirmation.
 */

describe('Retreat Booking Flow', () => {
  beforeEach(() => {
    // Intercepter les appels API
    cy.intercept('GET', '/api/retreats*').as('getRetreats');
    cy.intercept('GET', '/api/retreats/*').as('getRetreat');
    cy.intercept('POST', '/api/bookings').as('createBooking');
    cy.intercept('POST', '/api/payments/process').as('processPayment');
    
    // Se connecter avant chaque test
    cy.login();
  });

  describe('Retreat Search and Discovery', () => {
    it('should display retreats on the main page', () => {
      cy.visitRetreats();
      
      // Vérifier que la page se charge
      cy.wait('@getRetreats');
      cy.get('[data-cy="retreat-list"]').should('be.visible');
      
      // Vérifier qu'au moins une retraite est affichée
      cy.get('[data-cy^="retreat-card-"]').should('have.length.at.least', 1);
      
      // Vérifier les informations essentielles sur chaque carte
      cy.get('[data-cy^="retreat-card-"]').first().within(() => {
        cy.get('[data-cy="retreat-title"]').should('be.visible');
        cy.get('[data-cy="retreat-price"]').should('be.visible');
        cy.get('[data-cy="retreat-location"]').should('be.visible');
        cy.get('[data-cy="retreat-duration"]').should('be.visible');
        cy.get('[data-cy="retreat-rating"]').should('be.visible');
        cy.get('[data-cy="book-button"]').should('be.visible');
      });
    });

    it('should search retreats by keyword', () => {
      cy.visitRetreats();
      
      // Rechercher "yoga"
      cy.searchRetreats('yoga');
      cy.wait('@getRetreats');
      
      // Vérifier que les résultats contiennent le mot-clé
      cy.get('[data-cy="search-results"]').should('be.visible');
      cy.get('[data-cy^="retreat-card-"]').each(($card) => {
        cy.wrap($card).should('contain.text', 'yoga');
      });
      
      // Vérifier le nombre de résultats
      cy.get('[data-cy="results-count"]').should('contain.text', 'résultats trouvés');
    });

    it('should filter retreats by price range', () => {
      cy.visitRetreats();
      
      // Ouvrir les filtres
      cy.get('[data-cy="filters-toggle"]').click();
      cy.get('[data-cy="filters-panel"]').should('be.visible');
      
      // Définir une fourchette de prix
      cy.get('[data-cy="price-min"]').clear().type('200');
      cy.get('[data-cy="price-max"]').clear().type('500');
      cy.get('[data-cy="apply-filters"]').click();
      
      cy.wait('@getRetreats');
      
      // Vérifier que tous les résultats sont dans la fourchette
      cy.get('[data-cy^="retreat-card-"]').each(($card) => {
        cy.wrap($card).find('[data-cy="retreat-price"]').then(($price) => {
          const price = parseInt($price.text().replace(/[^\d]/g, ''));
          expect(price).to.be.within(200, 500);
        });
      });
    });

    it('should filter retreats by location', () => {
      cy.visitRetreats();
      
      cy.get('[data-cy="filters-toggle"]').click();
      cy.get('[data-cy="location-filter"]').select('France');
      cy.get('[data-cy="apply-filters"]').click();
      
      cy.wait('@getRetreats');
      
      // Vérifier que tous les résultats sont en France
      cy.get('[data-cy^="retreat-card-"]').each(($card) => {
        cy.wrap($card).find('[data-cy="retreat-location"]').should('contain.text', 'France');
      });
    });

    it('should sort retreats by different criteria', () => {
      cy.visitRetreats();
      
      // Trier par prix croissant
      cy.get('[data-cy="sort-select"]').select('price-asc');
      cy.wait('@getRetreats');
      
      // Vérifier l'ordre des prix
      cy.get('[data-cy^="retreat-card-"]').then(($cards) => {
        const prices = [];
        $cards.each((index, card) => {
          const priceText = Cypress.$(card).find('[data-cy="retreat-price"]').text();
          const price = parseInt(priceText.replace(/[^\d]/g, ''));
          prices.push(price);
        });
        
        // Vérifier que les prix sont en ordre croissant
        for (let i = 1; i < prices.length; i++) {
          expect(prices[i]).to.be.at.least(prices[i - 1]);
        }
      });
    });
  });

  describe('Retreat Details and Selection', () => {
    it('should display retreat details when clicked', () => {
      cy.visitRetreats();
      cy.wait('@getRetreats');
      
      // Cliquer sur la première retraite
      cy.get('[data-cy^="retreat-card-"]').first().click();
      cy.wait('@getRetreat');
      
      // Vérifier que la page de détails s'affiche
      cy.url().should('include', '/retreats/');
      cy.get('[data-cy="retreat-details"]').should('be.visible');
      
      // Vérifier les informations détaillées
      cy.get('[data-cy="retreat-title"]').should('be.visible');
      cy.get('[data-cy="retreat-description"]').should('be.visible');
      cy.get('[data-cy="retreat-program"]').should('be.visible');
      cy.get('[data-cy="retreat-instructor"]').should('be.visible');
      cy.get('[data-cy="retreat-amenities"]').should('be.visible');
      cy.get('[data-cy="retreat-gallery"]').should('be.visible');
      cy.get('[data-cy="book-now-button"]').should('be.visible');
    });

    it('should show retreat availability calendar', () => {
      cy.visitRetreats();
      cy.wait('@getRetreats');
      
      cy.get('[data-cy^="retreat-card-"]').first().click();
      cy.wait('@getRetreat');
      
      // Vérifier le calendrier de disponibilité
      cy.get('[data-cy="availability-calendar"]').should('be.visible');
      cy.get('[data-cy="available-dates"]').should('have.length.at.least', 1);
      
      // Sélectionner une date disponible
      cy.get('[data-cy="available-dates"]').first().click();
      cy.get('[data-cy="selected-date"]').should('be.visible');
      cy.get('[data-cy="book-now-button"]').should('not.be.disabled');
    });

    it('should add retreat to favorites', () => {
      cy.visitRetreats();
      cy.wait('@getRetreats');
      
      // Ajouter aux favoris depuis la liste
      cy.get('[data-cy^="retreat-card-"]').first().within(() => {
        cy.get('[data-cy="favorite-button"]').click();
      });
      
      // Vérifier le toast de confirmation
      cy.checkToast('Ajouté aux favoris', 'success');
      
      // Vérifier que l'icône change
      cy.get('[data-cy^="retreat-card-"]').first().within(() => {
        cy.get('[data-cy="favorite-button"]').should('have.class', 'favorited');
      });
    });
  });

  describe('Booking Process', () => {
    it('should complete a full booking flow', () => {
      // Mocker les données de retraite
      cy.intercept('GET', '/api/retreats/test-retreat-1', {
        statusCode: 200,
        body: {
          id: 'test-retreat-1',
          title: 'Test Yoga Retreat',
          price: 299,
          duration: '3 days',
          location: 'Provence, France',
          availableDates: ['2025-06-15', '2025-06-22', '2025-06-29']
        }
      }).as('getTestRetreat');

      cy.visit('/app/retreats/test-retreat-1');
      cy.wait('@getTestRetreat');
      
      // Sélectionner une date
      cy.get('[data-cy="date-2025-06-15"]').click();
      cy.get('[data-cy="book-now-button"]').click();
      
      // Vérifier l'ouverture du modal de réservation
      cy.get('[data-cy="booking-modal"]').should('be.visible');
      
      // Étape 1: Informations personnelles
      cy.get('[data-cy="booking-step-1"]').should('be.visible');
      cy.fillForm({
        'first-name': 'Jean',
        'last-name': 'Dupont',
        'phone': '0123456789',
        'emergency-contact': 'Marie Dupont - 0987654321'
      });
      cy.get('[data-cy="next-step"]').click();
      
      // Étape 2: Préférences et besoins spéciaux
      cy.get('[data-cy="booking-step-2"]').should('be.visible');
      cy.get('[data-cy="dietary-preferences"]').select('Végétarien');
      cy.get('[data-cy="special-needs"]').type('Allergie aux noix');
      cy.get('[data-cy="next-step"]').click();
      
      // Étape 3: Récapitulatif et conditions
      cy.get('[data-cy="booking-step-3"]').should('be.visible');
      cy.get('[data-cy="booking-summary"]').should('contain.text', 'Test Yoga Retreat');
      cy.get('[data-cy="total-price"]').should('contain.text', '299€');
      cy.get('[data-cy="terms-checkbox"]').check();
      cy.get('[data-cy="confirm-booking"]').click();
      
      // Vérifier l'appel API de création de réservation
      cy.wait('@createBooking').then((interception) => {
        expect(interception.request.body).to.deep.include({
          retreatId: 'test-retreat-1',
          date: '2025-06-15',
          personalInfo: {
            firstName: 'Jean',
            lastName: 'Dupont',
            phone: '0123456789'
          }
        });
      });
      
      // Vérifier la redirection vers le paiement
      cy.url().should('include', '/payment');
    });

    it('should handle booking validation errors', () => {
      cy.visit('/app/retreats/test-retreat-1');
      cy.get('[data-cy="date-2025-06-15"]').click();
      cy.get('[data-cy="book-now-button"]').click();
      
      // Essayer de passer à l'étape suivante sans remplir les champs
      cy.get('[data-cy="next-step"]').click();
      
      // Vérifier les messages d'erreur
      cy.get('[data-cy="first-name-error"]').should('contain.text', 'Prénom requis');
      cy.get('[data-cy="last-name-error"]').should('contain.text', 'Nom requis');
      cy.get('[data-cy="phone-error"]').should('contain.text', 'Téléphone requis');
    });

    it('should handle retreat unavailability', () => {
      // Mocker une retraite complète
      cy.intercept('POST', '/api/bookings', {
        statusCode: 409,
        body: {
          success: false,
          message: 'Cette date n\'est plus disponible'
        }
      }).as('unavailableBooking');

      cy.visit('/app/retreats/test-retreat-1');
      cy.get('[data-cy="date-2025-06-15"]').click();
      cy.get('[data-cy="book-now-button"]').click();
      
      // Remplir le formulaire rapidement
      cy.fillForm({
        'first-name': 'Jean',
        'last-name': 'Dupont',
        'phone': '0123456789'
      });
      cy.get('[data-cy="next-step"]').click();
      cy.get('[data-cy="next-step"]').click();
      cy.get('[data-cy="terms-checkbox"]').check();
      cy.get('[data-cy="confirm-booking"]').click();
      
      cy.wait('@unavailableBooking');
      
      // Vérifier le message d'erreur
      cy.get('[data-cy="error-message"]').should('contain.text', 'Cette date n\'est plus disponible');
      cy.checkToast('Cette date n\'est plus disponible', 'error');
    });
  });

  describe('Payment Process', () => {
    beforeEach(() => {
      // Créer une réservation en attente de paiement
      cy.intercept('GET', '/api/bookings/pending-payment', {
        statusCode: 200,
        body: {
          id: 'booking-123',
          retreat: {
            title: 'Test Yoga Retreat',
            price: 299
          },
          totalAmount: 299,
          status: 'pending_payment'
        }
      }).as('getPendingBooking');
    });

    it('should process payment successfully', () => {
      // Mocker le succès du paiement
      cy.intercept('POST', '/api/payments/process', {
        statusCode: 200,
        body: {
          success: true,
          paymentId: 'payment-456',
          bookingId: 'booking-123'
        }
      }).as('successfulPayment');

      cy.visit('/app/payment/booking-123');
      cy.wait('@getPendingBooking');
      
      // Vérifier les informations de paiement
      cy.get('[data-cy="payment-summary"]').should('contain.text', 'Test Yoga Retreat');
      cy.get('[data-cy="payment-amount"]').should('contain.text', '299€');
      
      // Remplir les informations de carte
      cy.fillForm({
        'card-number': '****************',
        'card-expiry': '12/25',
        'card-cvc': '123',
        'card-name': 'Jean Dupont'
      });
      
      // Traiter le paiement
      cy.get('[data-cy="pay-button"]').click();
      cy.wait('@successfulPayment');
      
      // Vérifier la confirmation
      cy.url().should('include', '/booking-confirmation');
      cy.get('[data-cy="confirmation-message"]').should('contain.text', 'Réservation confirmée');
      cy.checkToast('Paiement réussi', 'success');
    });

    it('should handle payment failures', () => {
      // Mocker l'échec du paiement
      cy.intercept('POST', '/api/payments/process', {
        statusCode: 402,
        body: {
          success: false,
          message: 'Carte refusée'
        }
      }).as('failedPayment');

      cy.visit('/app/payment/booking-123');
      cy.wait('@getPendingBooking');
      
      cy.fillForm({
        'card-number': '****************', // Carte de test refusée
        'card-expiry': '12/25',
        'card-cvc': '123',
        'card-name': 'Jean Dupont'
      });
      
      cy.get('[data-cy="pay-button"]').click();
      cy.wait('@failedPayment');
      
      // Vérifier le message d'erreur
      cy.get('[data-cy="payment-error"]').should('contain.text', 'Carte refusée');
      cy.checkToast('Paiement échoué', 'error');
      
      // Vérifier que l'utilisateur peut réessayer
      cy.get('[data-cy="pay-button"]').should('not.be.disabled');
    });
  });

  describe('Booking Management', () => {
    it('should display user bookings in dashboard', () => {
      // Mocker les réservations de l'utilisateur
      cy.intercept('GET', '/api/user/bookings', {
        statusCode: 200,
        body: [
          {
            id: 'booking-123',
            retreat: {
              title: 'Test Yoga Retreat',
              location: 'Provence, France'
            },
            date: '2025-06-15',
            status: 'confirmed',
            totalAmount: 299
          }
        ]
      }).as('getUserBookings');

      cy.visitDashboard();
      cy.wait('@getUserBookings');
      
      // Vérifier l'affichage des réservations
      cy.get('[data-cy="user-bookings"]').should('be.visible');
      cy.get('[data-cy="booking-123"]').should('contain.text', 'Test Yoga Retreat');
      cy.get('[data-cy="booking-123"]').should('contain.text', 'Confirmée');
    });

    it('should allow booking cancellation', () => {
      cy.intercept('PUT', '/api/bookings/booking-123/cancel', {
        statusCode: 200,
        body: { success: true }
      }).as('cancelBooking');

      cy.visitDashboard();
      
      // Annuler une réservation
      cy.get('[data-cy="booking-123"]').within(() => {
        cy.get('[data-cy="cancel-booking"]').click();
      });
      
      // Confirmer l'annulation
      cy.get('[data-cy="cancel-confirmation-modal"]').should('be.visible');
      cy.get('[data-cy="confirm-cancel"]').click();
      
      cy.wait('@cancelBooking');
      cy.checkToast('Réservation annulée', 'success');
    });
  });

  describe('Responsive Booking Flow', () => {
    it('should work on mobile devices', () => {
      cy.viewport('iphone-x');
      
      cy.visitRetreats();
      cy.wait('@getRetreats');
      
      // Vérifier que les cartes s'affichent correctement sur mobile
      cy.get('[data-cy^="retreat-card-"]').should('be.visible');
      
      // Tester le processus de réservation sur mobile
      cy.get('[data-cy^="retreat-card-"]').first().click();
      cy.get('[data-cy="book-now-button"]').should('be.visible');
    });
  });
});
