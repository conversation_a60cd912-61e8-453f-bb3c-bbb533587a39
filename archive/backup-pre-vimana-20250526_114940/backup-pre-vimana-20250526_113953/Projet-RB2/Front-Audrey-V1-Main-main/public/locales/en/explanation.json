{"preferences": {"title": "Explanation Preferences", "pageTitle": "Explanation Preferences", "pageSubtitle": "Customize how recommendation explanations are presented to you", "preferredStyle": "Preferred explanation style", "detailLevel": "Detail level", "preferredFormat": "Preferred explanation format", "language": "Explanation language", "highlightedFactors": "Factors to highlight", "hiddenFactors": "Factors to hide", "saveSuccess": "Explanation preferences saved successfully", "saveError": "Error saving explanation preferences", "loadError": "Error loading explanation preferences", "resetSuccess": "Explanation preferences reset successfully", "resetError": "Error resetting explanation preferences", "helpTitle": "How to customize your explanations", "styleTitle": "Explanation style", "styleDescription": "Choose the style that works best for you to understand recommendations", "detailTitle": "Detail level", "detailDescription": "Set the amount of information you want to see in explanations", "formatTitle": "Explanation format", "formatDescription": "Select the formats in which you prefer to receive explanations", "languageTitle": "Language", "languageDescription": "Choose the language in which you want to receive explanations", "exampleTitle": "Example explanation", "exampleHeader": "Yoga Retreat in Provence", "exampleDescription": "Here's an example explanation customized according to your preferences", "exampleFactors": "Main factors:", "exampleFactor1": "Matches your interests in yoga and meditation", "exampleFactor2": "Popular destination among users similar to you", "exampleFactor3": "Matches your budget and preferred dates", "exampleSummary": "Summary:", "exampleText": "This retreat is recommended because it perfectly matches your interests in yoga and meditation. Additionally, it is highly appreciated by users with preferences similar to yours."}, "style": {"conversational": "Conversational and friendly", "technical": "Technical and precise", "concise": "Concise and direct", "narrative": "Narrative and descriptive", "educational": "Educational with additional information"}, "detailLevel": {"minimal": "Minimal - Just the essentials", "moderate": "Moderate - Balanced", "detailed": "Detailed - Complete information", "comprehensive": "Comprehensive - Very detailed with additional information"}, "format": {"text": "Simple text", "visual": "Visual (graphics, icons)", "mixed": "Mixed (text and visual elements)", "interactive": "Interactive (explorable)"}, "factors": {"similarity": "Similarity to your interests", "popularity": "Popularity among similar users", "rating": "Ratings and reviews", "price": "Price and budget", "location": "Location and accessibility", "duration": "Duration and dates", "instructor": "Instructor or guide", "amenities": "Amenities and services", "activities": "Activities offered", "theme": "Theme and ambiance"}}