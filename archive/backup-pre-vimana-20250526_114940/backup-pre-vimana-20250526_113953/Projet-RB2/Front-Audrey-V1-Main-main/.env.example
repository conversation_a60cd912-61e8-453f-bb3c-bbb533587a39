# API URLs
REACT_APP_API_URL=http://localhost:3000/api
REACT_APP_SECURITY_URL=http://localhost:3001/api
REACT_APP_AGENT_IA_URL=http://localhost:3002/api
REACT_APP_SOCIAL_URL=http://localhost:3003/api
REACT_APP_FINANCIAL_URL=http://localhost:3004/api
REACT_APP_SUPERAGENT_URL=http://localhost:8001

# Authentication
REACT_APP_AUTH_DOMAIN=retreatandbe.auth.com
REACT_APP_AUTH_CLIENT_ID=your-client-id
REACT_APP_AUTH_AUDIENCE=your-audience

# Feature flags
REACT_APP_ENABLE_CHATBOT=true
REACT_APP_ENABLE_ANALYTICS=true

# Other settings
REACT_APP_ENVIRONMENT=development
