name: Security Scan

on:
  push:
    branches: [ main ]
    paths:
      - 'Front-Audrey-V1-Main-main/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'Front-Audrey-V1-Main-main/**'
  schedule:
    - cron: '0 0 * * 0'  # Run every Sunday at midnight
  workflow_dispatch:

jobs:
  scan:
    name: Security Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Build image
        run: |
          cd Front-Audrey-V1-Main-main
          docker build -t audrey-frontend:${{ github.sha }} .

      - name: Scan image with Trivy
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'audrey-frontend:${{ github.sha }}'
          format: 'table'
          exit-code: '1'
          ignore-unfixed: true
          vuln-type: 'os,library'
          severity: 'CRITICAL,HIGH'

      - name: Scan dependencies with npm audit
        run: |
          cd Front-Audrey-V1-Main-main
          npm audit --audit-level=high

      - name: Run OWASP ZAP scan
        uses: zaproxy/action-baseline@v0.7.0
        with:
          target: 'http://localhost:3000'
          docker_name: 'owasp/zap2docker-stable'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'
