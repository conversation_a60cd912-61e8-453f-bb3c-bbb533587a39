/**
 * Tests de Performance K6 - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Tests de charge pour valider les performances
 * de l'application sous différents scénarios.
 */

import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';

// Métriques personnalisées
export const errorRate = new Rate('errors');
export const responseTime = new Trend('response_time');
export const requestCount = new Counter('requests');

// Configuration des tests
export const options = {
  stages: [
    // Montée progressive
    { duration: '2m', target: 50 },   // 0 à 50 utilisateurs en 2 minutes
    { duration: '5m', target: 50 },   // Maintien à 50 utilisateurs pendant 5 minutes
    { duration: '2m', target: 100 },  // Montée à 100 utilisateurs en 2 minutes
    { duration: '5m', target: 100 },  // Maintien à 100 utilisateurs pendant 5 minutes
    { duration: '2m', target: 200 },  // Pic à 200 utilisateurs en 2 minutes
    { duration: '5m', target: 200 },  // Maintien du pic pendant 5 minutes
    { duration: '2m', target: 0 },    // Descente progressive en 2 minutes
  ],
  
  // Seuils de performance
  thresholds: {
    // 95% des requêtes doivent être sous 500ms
    http_req_duration: ['p(95)<500'],
    
    // 99% des requêtes doivent être sous 1000ms
    'http_req_duration{name:api}': ['p(99)<1000'],
    
    // Taux d'erreur doit être inférieur à 1%
    http_req_failed: ['rate<0.01'],
    
    // Métriques personnalisées
    errors: ['rate<0.01'],
    response_time: ['p(95)<500'],
  },
  
  // Configuration des navigateurs
  browser: {
    type: 'chromium',
  },
};

// Configuration de l'environnement
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const API_URL = __ENV.API_URL || 'http://localhost:3001/api';

// Données de test
const testUsers = [
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
];

// Fonction utilitaire pour les headers
function getHeaders(token = null) {
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
}

// Fonction d'authentification
function authenticate() {
  const user = testUsers[Math.floor(Math.random() * testUsers.length)];
  
  const loginResponse = http.post(
    `${API_URL}/auth/login`,
    JSON.stringify(user),
    { headers: getHeaders() }
  );
  
  check(loginResponse, {
    'login status is 200': (r) => r.status === 200,
    'login response time < 1000ms': (r) => r.timings.duration < 1000,
  });
  
  if (loginResponse.status === 200) {
    const body = JSON.parse(loginResponse.body);
    return body.token;
  }
  
  return null;
}

// Test principal
export default function () {
  group('Page d\'accueil', () => {
    const homeResponse = http.get(BASE_URL);
    
    check(homeResponse, {
      'home page status is 200': (r) => r.status === 200,
      'home page loads in <2s': (r) => r.timings.duration < 2000,
      'home page contains title': (r) => r.body.includes('Retreat & Be'),
    });
    
    responseTime.add(homeResponse.timings.duration);
    requestCount.add(1);
    
    if (homeResponse.status !== 200) {
      errorRate.add(1);
    } else {
      errorRate.add(0);
    }
  });
  
  sleep(1);
  
  group('API - Liste des retraites', () => {
    const retreatsResponse = http.get(
      `${API_URL}/retreats?limit=10`,
      { headers: getHeaders() }
    );
    
    check(retreatsResponse, {
      'retreats API status is 200': (r) => r.status === 200,
      'retreats API response time < 500ms': (r) => r.timings.duration < 500,
      'retreats API returns data': (r) => {
        const body = JSON.parse(r.body);
        return body.success && Array.isArray(body.data);
      },
    });
    
    responseTime.add(retreatsResponse.timings.duration);
    requestCount.add(1);
    
    if (retreatsResponse.status !== 200) {
      errorRate.add(1);
    } else {
      errorRate.add(0);
    }
  });
  
  sleep(1);
  
  group('Authentification et Dashboard', () => {
    const token = authenticate();
    
    if (token) {
      // Test du dashboard
      const dashboardResponse = http.get(
        `${BASE_URL}/app/dashboard`,
        { headers: getHeaders(token) }
      );
      
      check(dashboardResponse, {
        'dashboard status is 200': (r) => r.status === 200,
        'dashboard loads in <2s': (r) => r.timings.duration < 2000,
      });
      
      responseTime.add(dashboardResponse.timings.duration);
      requestCount.add(1);
      
      // Test de l'API profil utilisateur
      const profileResponse = http.get(
        `${API_URL}/user/profile`,
        { headers: getHeaders(token) }
      );
      
      check(profileResponse, {
        'profile API status is 200': (r) => r.status === 200,
        'profile API response time < 300ms': (r) => r.timings.duration < 300,
      });
      
      responseTime.add(profileResponse.timings.duration);
      requestCount.add(1);
    }
  });
  
  sleep(1);
  
  group('Recherche de retraites', () => {
    const searchQueries = ['yoga', 'meditation', 'detox', 'wellness'];
    const query = searchQueries[Math.floor(Math.random() * searchQueries.length)];
    
    const searchResponse = http.get(
      `${API_URL}/retreats?search=${query}&limit=20`,
      { headers: getHeaders() }
    );
    
    check(searchResponse, {
      'search API status is 200': (r) => r.status === 200,
      'search API response time < 800ms': (r) => r.timings.duration < 800,
      'search returns results': (r) => {
        const body = JSON.parse(r.body);
        return body.success && body.data.length >= 0;
      },
    });
    
    responseTime.add(searchResponse.timings.duration);
    requestCount.add(1);
  });
  
  sleep(1);
  
  group('Détails d\'une retraite', () => {
    // Simuler la consultation d'une retraite spécifique
    const retreatId = 'retreat-1'; // ID de test
    
    const retreatResponse = http.get(
      `${API_URL}/retreats/${retreatId}`,
      { headers: getHeaders() }
    );
    
    check(retreatResponse, {
      'retreat details status is 200': (r) => r.status === 200,
      'retreat details response time < 400ms': (r) => r.timings.duration < 400,
      'retreat details contains data': (r) => {
        const body = JSON.parse(r.body);
        return body.success && body.data && body.data.id === retreatId;
      },
    });
    
    responseTime.add(retreatResponse.timings.duration);
    requestCount.add(1);
  });
  
  sleep(2);
}

// Test de stress spécifique
export function stressTest() {
  group('Stress Test - Pic de charge', () => {
    // Simuler de nombreuses requêtes simultanées
    const responses = http.batch([
      ['GET', `${BASE_URL}/`],
      ['GET', `${API_URL}/retreats`],
      ['GET', `${API_URL}/professionals`],
      ['GET', `${API_URL}/health`],
    ]);
    
    responses.forEach((response, index) => {
      check(response, {
        [`batch request ${index} status is 200`]: (r) => r.status === 200,
        [`batch request ${index} response time < 1s`]: (r) => r.timings.duration < 1000,
      });
      
      responseTime.add(response.timings.duration);
      requestCount.add(1);
    });
  });
}

// Test de spike (pic soudain)
export function spikeTest() {
  group('Spike Test - Pic soudain', () => {
    // Test avec une charge soudaine très élevée
    for (let i = 0; i < 10; i++) {
      const response = http.get(`${API_URL}/retreats?page=${i}`);
      
      check(response, {
        'spike test status is 200': (r) => r.status === 200,
        'spike test response time < 2s': (r) => r.timings.duration < 2000,
      });
      
      responseTime.add(response.timings.duration);
      requestCount.add(1);
    }
  });
}

// Configuration pour différents types de tests
export const stressOptions = {
  stages: [
    { duration: '1m', target: 500 },  // Montée rapide à 500 utilisateurs
    { duration: '5m', target: 500 },  // Maintien pendant 5 minutes
    { duration: '1m', target: 0 },    // Descente rapide
  ],
  thresholds: {
    http_req_duration: ['p(95)<1000'],
    http_req_failed: ['rate<0.05'], // Tolérance plus élevée pour le stress test
  },
};

export const spikeOptions = {
  stages: [
    { duration: '30s', target: 100 },  // Montée normale
    { duration: '1m', target: 1000 },  // Pic soudain
    { duration: '30s', target: 100 },  // Retour à la normale
    { duration: '30s', target: 0 },    // Arrêt
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'],
    http_req_failed: ['rate<0.1'], // Tolérance élevée pour le spike test
  },
};

// Fonction de teardown
export function teardown(data) {
  console.log('Test de performance terminé');
  console.log(`Nombre total de requêtes: ${requestCount.count}`);
  console.log(`Taux d'erreur moyen: ${(errorRate.rate * 100).toFixed(2)}%`);
}
