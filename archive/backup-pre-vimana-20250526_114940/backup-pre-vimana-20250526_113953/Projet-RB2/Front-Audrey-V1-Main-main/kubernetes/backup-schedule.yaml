apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: audrey-frontend-daily-backup
  namespace: velero
spec:
  schedule: "0 1 * * *"  # Daily at 1 AM
  template:
    includedNamespaces:
      - retreat-and-be
    includedResources:
      - deployments
      - services
      - configmaps
      - secrets
      - persistentvolumeclaims
    labelSelector:
      matchLabels:
        app: audrey-frontend
    ttl: 720h  # 30 days
    storageLocation: default
    volumeSnapshotLocations:
      - default
