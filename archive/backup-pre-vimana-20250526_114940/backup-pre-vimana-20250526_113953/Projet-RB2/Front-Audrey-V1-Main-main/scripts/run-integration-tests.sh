#!/bin/bash

# Script pour exécuter les tests d'intégration

# Couleurs pour les messages
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Début des tests d'intégration...${NC}"

# Vérifier si les dossiers de tests d'intégration existent
if [ ! -d "./src/components/moderation/__tests__/integration" ] && [ ! -d "./src/components/analytics/__tests__/integration" ]; then
    echo -e "${RED}Erreur: Les dossiers de tests d'intégration n'existent pas.${NC}"
    exit 1
fi

# Installer les dépendances nécessaires pour les tests
echo -e "${YELLOW}Installation des dépendances pour les tests...${NC}"
npm install --save-dev @testing-library/react @testing-library/jest-dom @testing-library/user-event jest-mock-extended
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'installation des dépendances pour les tests.${NC}"
    exit 1
fi
echo -e "${GREEN}Dépendances installées avec succès.${NC}"

# Exécuter les tests d'intégration pour le module de modération
echo -e "${YELLOW}Exécution des tests d'intégration pour le module de modération...${NC}"
npm test -- --testPathPattern=src/components/moderation/__tests__/integration
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'exécution des tests d'intégration pour le module de modération.${NC}"
    exit 1
fi
echo -e "${GREEN}Tests d'intégration pour le module de modération exécutés avec succès.${NC}"

# Exécuter les tests d'intégration pour le module d'analyse
echo -e "${YELLOW}Exécution des tests d'intégration pour le module d'analyse...${NC}"
npm test -- --testPathPattern=src/components/analytics/__tests__/integration
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'exécution des tests d'intégration pour le module d'analyse.${NC}"
    exit 1
fi
echo -e "${GREEN}Tests d'intégration pour le module d'analyse exécutés avec succès.${NC}"

# Exécuter les tests de bout en bout (E2E)
echo -e "${YELLOW}Exécution des tests de bout en bout...${NC}"
npm run test:e2e
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'exécution des tests de bout en bout.${NC}"
    exit 1
fi
echo -e "${GREEN}Tests de bout en bout exécutés avec succès.${NC}"

echo -e "${GREEN}Tous les tests d'intégration ont été exécutés avec succès.${NC}"
