# Dependencies
node_modules
/.pnp
.pnp.js

# Testing
/coverage

# Production
/build

# Misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Git files
.git
.gitignore

# Docker files
Dockerfile
docker-compose.yml
.dockerignore

# Kubernetes files
kubernetes/
helm/
