# Default values for audrey-frontend chart
replicaCount: 3

image:
  repository: registry.retreat-and-be.com/retreat-and-be/audrey-frontend
  tag: latest
  pullPolicy: Always

imagePullSecrets:
  - name: regcred

nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: true
  annotations: {}
  name: ""

podAnnotations: {}

podSecurityContext: {}

securityContext: {}

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: nginx
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - host: app.retreat-and-be.com
      paths:
        - path: /(.*)
          pathType: Prefix
  tls:
    - secretName: audrey-frontend-tls
      hosts:
        - app.retreat-and-be.com

resources:
  limits:
    cpu: 300m
    memory: 256Mi
  requests:
    cpu: 100m
    memory: 128Mi

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70

nodeSelector: {}

tolerations: []

affinity: {}

env:
  NODE_ENV: production
  REACT_APP_API_URL: https://api.retreat-and-be.com
  REACT_APP_SECURITY_URL: https://api.retreat-and-be.com/security
  REACT_APP_AGENT_IA_URL: https://api.retreat-and-be.com/agent-ia
  REACT_APP_SOCIAL_URL: https://api.retreat-and-be.com/social
  REACT_APP_FINANCIAL_URL: https://api.retreat-and-be.com/financial
