@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import 'mapbox-gl/dist/mapbox-gl.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    font-family:
      'Inter',
      system-ui,
      -apple-system,
      sans-serif;
    /* Couleur spécifique */
    --retreat-green: 150 55% 50%;
    --retreat-green-dark: 150 55% 40%;
    /* Couleurs vertes élégantes (menthe) */
    --mint-50: 160 100% 98%;
    --mint-100: 160 100% 95%;
    --mint-200: 160 100% 90%;
    --mint-300: 160 100% 85%;
    --mint-400: 160 100% 80%;
    --mint-500: 160 100% 75%;
    --mint-600: 160 100% 70%;
    --mint-700: 160 100% 65%;
    --mint-800: 160 100% 60%;
    --mint-900: 160 100% 55%;

    /* Couleurs vertes élégantes (originales) */
    --green-50: 150 40% 98%;
    --green-100: 150 40% 95%;
    --green-200: 150 40% 90%;
    --green-300: 150 40% 85%;
    --green-400: 150 40% 80%;
    --green-500: 150 40% 75%;
    --green-600: 150 40% 70%;
    --green-700: 150 40% 65%;
    --green-800: 150 40% 60%;
    --green-900: 150 40% 55%;

    /* Couleurs principales */
    --background: var(--mint-50);
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: var(--retreat-green);
    --primary-foreground: 210 40% 98%;
    --secondary: 160 100% 90%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 160 100% 95%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 160 100% 85%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 160 100% 90%;
    --input: 160 100% 90%;
    --ring: 160 100% 75%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 160 100% 10%;
    --foreground: 210 40% 98%;
    --card: 160 100% 15%;
    --card-foreground: 210 40% 98%;
    --popover: 160 100% 15%;
    --popover-foreground: 210 40% 98%;
    --primary: 160 100% 75%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 160 100% 20%;
    --secondary-foreground: 210 40% 98%;
    --muted: 160 100% 20%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 160 100% 25%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 160 100% 25%;
    --input: 160 100% 25%;
    --ring: 160 100% 75%;
  }

  * {
    @apply border-[hsl(var(--border))];
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }

  body {
    margin: 0;
    font-family:
      'Inter',
      system-ui,
      -apple-system,
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: var(--background);
    color: var(--text);
  }

  /* Style global pour toutes les checkboxes */
  input[type='checkbox'] {
    display: inline-block;
    height: 1rem;
    width: 1rem;
    border-radius: 0.25rem;
    border-color: var(--retreat-green);
    color: var(--retreat-green);
    transition: all 0.2s ease-in-out;
  }
  input[type='checkbox']:focus {
    border: 1px solid var(--retreat-green);
    background-color: var(--retreat-green-dark);
    color: white;
  }
  input[type='checkbox']:checked {
    border: 1px solid var(--retreat-green);
    background-color: var(--retreat-green-dark);
    color: white;
  }

  input[type='checkbox']:hover:not(:checked) {
    border: 1px solid var(--retreat-green);
  }
  /* Styles pour le ratio d'or */
  .golden-ratio {
    aspect-ratio: 1.618;
  }

  /* Animations */
  .fade-in {
    animation: fadeIn 0.5s ease-in;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes favorite {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.3);
    }
    100% {
      transform: scale(1);
    }
  }

  .animate-favorite {
    animation: favorite 0.3s ease-in-out;
  }

  .hide-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
}

@tailwind components {
  /* Styles pour le calendrier */
  .react-datepicker-wrapper {
    width: 100% !important;
  }

  .react-datepicker__input-container {
    width: 100% !important;
    display: block !important;
  }

  .react-datepicker__input-container input {
    width: 100% !important;
    display: block !important;
  }

  .calendar-container {
    display: flex;
    gap: 1rem;
  }

  .react-datepicker {
    width: 100% !important; /* Réduction de la largeur */
  }

  .react-datepicker__month-container {
    float: none !important;
    width: auto !important;
  }

  .react-datepicker__header {
    background-color: white !important;
    border-bottom: none !important;
    width: auto !important;
  }

  .react-datepicker__month {
    margin: 0 !important;
    padding: 0.5rem !important;
  }

  .react-datepicker__day {
    width: 2rem !important;
    line-height: 2rem !important;
    margin: 0.2rem !important;
  }

  .react-datepicker__day--selected,
  .react-datepicker__day--in-selecting-range,
  .react-datepicker__day--in-range {
    background-color: #4f7942 !important;
    color: white !important;
  }

  .react-datepicker__day--selected:hover,
  .react-datepicker__day--in-selecting-range:hover,
  .react-datepicker__day--in-range:hover {
    background-color: #3d5a33 !important;
  }

  /* Styles pour les selects */
  .select-custom {
    @apply w-full rounded-lg border-gray-200 text-gray-900 
    focus:border-[hsl(var(--retreat-green))] focus:ring 
    focus:ring-[hsl(var(--retreat-green))/20] 
    py-2.5 pl-3 pr-10 text-sm transition-colors duration-200;
  }

  .option-custom {
    @apply py-2 px-3 hover:bg-[hsl(var(--retreat-green))/10] cursor-pointer;
  }

  /* Style pour le groupe d'options */
  .select-group {
    @apply space-y-2;
  }

  /* Style pour le label du select */
  .select-label {
    @apply flex items-center text-sm font-medium text-gray-700 mb-1;
  }
}
