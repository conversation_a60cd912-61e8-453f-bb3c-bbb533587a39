import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON>Chart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  ResponsiveC<PERSON>r,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from 'recharts';
import { socialAnalyticsService } from '../../services/api/socialAnalyticsService';
import LoadingSpinner from '../atoms/LoadingSpinner/LoadingSpinner';

interface ContentAnalyticsProps {
  userId?: string;
  timeRange?: 'week' | 'month' | 'year';
}

interface AnalyticsData {
  viewsByDay: {
    date: string;
    views: number;
  }[];
  engagementByType: {
    type: string;
    count: number;
  }[];
  contentByStatus: {
    status: string;
    count: number;
  }[];
  topContent: {
    id: string;
    title: string;
    views: number;
    engagement: number;
  }[];
  totalStats: {
    totalViews: number;
    totalLikes: number;
    totalComments: number;
    totalShares: number;
    totalContent: number;
  };
}

const ContentAnalytics: React.FC<ContentAnalyticsProps> = ({ 
  userId,
  timeRange = 'month' 
}) => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState<'week' | 'month' | 'year'>(timeRange);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

  useEffect(() => {
    fetchAnalyticsData();
  }, [userId, selectedTimeRange]);

  const fetchAnalyticsData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch analytics data from the service
      const data = await socialAnalyticsService.getContentAnalytics({
        userId,
        timeRange: selectedTimeRange
      });

      setAnalyticsData(data);
    } catch (err) {
      console.error('Error fetching analytics data:', err);
      setError('Impossible de charger les données d\'analyse');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !analyticsData) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
        {error || 'Aucune donnée d\'analyse disponible'}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Time range selector */}
      <div className="flex justify-end space-x-2">
        <button
          onClick={() => setSelectedTimeRange('week')}
          className={`px-3 py-1 rounded-md text-sm ${
            selectedTimeRange === 'week'
              ? 'bg-retreat-green text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          7 jours
        </button>
        <button
          onClick={() => setSelectedTimeRange('month')}
          className={`px-3 py-1 rounded-md text-sm ${
            selectedTimeRange === 'month'
              ? 'bg-retreat-green text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          30 jours
        </button>
        <button
          onClick={() => setSelectedTimeRange('year')}
          className={`px-3 py-1 rounded-md text-sm ${
            selectedTimeRange === 'year'
              ? 'bg-retreat-green text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          12 mois
        </button>
      </div>

      {/* Summary stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="text-sm text-gray-500">Vues totales</div>
          <div className="text-2xl font-semibold">{analyticsData.totalStats.totalViews}</div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="text-sm text-gray-500">J'aime</div>
          <div className="text-2xl font-semibold">{analyticsData.totalStats.totalLikes}</div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="text-sm text-gray-500">Commentaires</div>
          <div className="text-2xl font-semibold">{analyticsData.totalStats.totalComments}</div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="text-sm text-gray-500">Partages</div>
          <div className="text-2xl font-semibold">{analyticsData.totalStats.totalShares}</div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="text-sm text-gray-500">Contenu total</div>
          <div className="text-2xl font-semibold">{analyticsData.totalStats.totalContent}</div>
        </div>
      </div>

      {/* Views over time chart */}
      <div className="bg-white rounded-lg shadow-md p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Vues au fil du temps</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={analyticsData.viewsByDay}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="views"
                stroke="#8884d8"
                activeDot={{ r: 8 }}
                name="Vues"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Engagement by type */}
        <div className="bg-white rounded-lg shadow-md p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Engagement par type</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={analyticsData.engagementByType}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="type" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" fill="#8884d8" name="Nombre" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Content by status */}
        <div className="bg-white rounded-lg shadow-md p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Contenu par statut</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={analyticsData.contentByStatus}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                  nameKey="status"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {analyticsData.contentByStatus.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Top performing content */}
      <div className="bg-white rounded-lg shadow-md p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Contenu le plus performant</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Titre
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vues
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Engagement
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {analyticsData.topContent.map((content) => (
                <tr key={content.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {content.title}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {content.views}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {content.engagement}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ContentAnalytics;
