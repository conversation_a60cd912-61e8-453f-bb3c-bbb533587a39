import React, { useState, useRef, useEffect } from 'react';
import { useMessagingContext } from '../../contexts/MessagingContext';
import { CreateMessageDto, IMessage } from '../../services/api/messagingService';

interface MessageInputProps {
  conversationId: string;
  replyToMessage?: IMessage;
  onCancelReply?: () => void;
}

const MessageInput: React.FC<MessageInputProps> = ({
  conversationId,
  replyToMessage,
  onCancelReply,
}) => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [attachments, setAttachments] = useState<File[]>([]);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const { sendMessage, setTyping } = useMessagingContext();
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Focus input when component mounts or replyToMessage changes
    inputRef.current?.focus();
  }, [replyToMessage]);

  const handleTyping = () => {
    if (!isTyping) {
      setIsTyping(true);
      setTyping(conversationId, true);
    }

    // Clear previous timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      setTyping(conversationId, false);
    }, 3000);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    handleTyping();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleSendMessage = async () => {
    if (message.trim() === '' && attachments.length === 0) return;

    // Clear typing indicator
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    setIsTyping(false);
    setTyping(conversationId, false);

    // Create message DTO
    const messageDto: CreateMessageDto = {
      content: message.trim(),
      conversationId,
      ...(replyToMessage && { replyToId: replyToMessage.id }),
    };

    // Send message
    const sentMessage = await sendMessage(messageDto);

    // Clear input
    setMessage('');

    // Cancel reply if needed
    if (replyToMessage && onCancelReply) {
      onCancelReply();
    }

    // Handle attachments if any
    if (sentMessage && attachments.length > 0) {
      // TODO: Implement file upload and attachment creation
      setAttachments([]);
    }
  };

  const handleAttachmentClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setAttachments(Array.from(e.target.files));
    }
  };

  const handleRemoveAttachment = (index: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  };

  const handleEmojiClick = (emoji: string) => {
    setMessage((prev) => prev + emoji);
    setShowEmojiPicker(false);
    inputRef.current?.focus();
  };

  return (
    <div className='p-4 border-t border-gray-200'>
      {replyToMessage && (
        <div className='flex items-center p-2 mb-2 bg-gray-100 rounded-lg'>
          <div className='flex-1'>
            <p className='text-xs font-semibold'>
              <span>
                Répondre à{' '}
                {replyToMessage.senderId === localStorage.getItem('userId')
                  ? 'vous-même'
                  : replyToMessage.sender.firstName}
              </span>
            </p>
            <p className='text-sm truncate'>{replyToMessage.content}</p>
          </div>
          <button onClick={onCancelReply} className='ml-2 text-gray-500 hover:text-gray-700'>
            <svg
              className='w-5 h-5'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M6 18L18 6M6 6l12 12'
              />
            </svg>
          </button>
        </div>
      )}

      {attachments.length > 0 && (
        <div className='flex flex-wrap gap-2 mb-2'>
          {attachments.map((file, index) => (
            <div key={index} className='flex items-center p-2 bg-gray-100 rounded-lg'>
              <span className='text-sm truncate max-w-xs'>{file.name}</span>
              <button
                onClick={() => handleRemoveAttachment(index)}
                className='ml-2 text-gray-500 hover:text-gray-700'
              >
                <svg
                  className='w-4 h-4'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M6 18L18 6M6 6l12 12'
                  />
                </svg>
              </button>
            </div>
          ))}
        </div>
      )}

      <div className='flex items-end'>
        <button onClick={handleAttachmentClick} className='p-2 text-gray-500 hover:text-gray-700'>
          <svg
            className='w-6 h-6'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13'
            />
          </svg>
        </button>

        <input
          type='file'
          ref={fileInputRef}
          onChange={handleFileChange}
          multiple
          className='hidden'
        />

        <div className='relative flex-1 mx-2'>
          <textarea
            ref={inputRef}
            value={message}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder='Écrivez votre message...'
            className='w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none'
            rows={1}
          />

          <div className='absolute right-2 bottom-2'>
            <button
              onClick={() => setShowEmojiPicker(!showEmojiPicker)}
              className='p-1 text-gray-500 hover:text-gray-700'
            >
              <svg
                className='w-5 h-5'
                fill='currentColor'
                viewBox='0 0 20 20'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  fillRule='evenodd'
                  d='M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 100-2 1 1 0 000 2zm7-1a1 1 0 11-2 0 1 1 0 012 0zm-7.536 5.879a1 1 0 001.415 0 3 3 0 014.242 0 1 1 0 001.415-1.415 5 5 0 00-7.072 0 1 1 0 000 1.415z'
                  clipRule='evenodd'
                />
              </svg>
            </button>
          </div>

          {showEmojiPicker && (
            <div className='absolute right-0 bottom-12 bg-white border border-gray-300 rounded-lg shadow-lg p-2'>
              <div className='grid grid-cols-8 gap-1'>
                {[
                  '😊',
                  '😂',
                  '❤️',
                  '👍',
                  '👎',
                  '😍',
                  '😢',
                  '😡',
                  '🎉',
                  '🔥',
                  '👋',
                  '🤔',
                  '👀',
                  '💪',
                  '🙏',
                  '🤝',
                ].map((emoji) => (
                  <button
                    key={emoji}
                    onClick={() => handleEmojiClick(emoji)}
                    className='p-1 text-xl hover:bg-gray-100 rounded'
                  >
                    {emoji}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        <button
          onClick={handleSendMessage}
          disabled={message.trim() === '' && attachments.length === 0}
          className={`p-3 rounded-full ${
            message.trim() === '' && attachments.length === 0
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-green-500 text-white hover:bg-green-600'
          }`}
        >
          <svg
            className='w-5 h-5'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M12 19l9 2-9-18-9 18 9-2zm0 0v-8'
            />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default MessageInput;
