import React, { useEffect } from 'react';
import { useMessagingContext } from '../../contexts/MessagingContext';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import { IConversation, ParticipantLike } from '../../services/api/messagingService';

// Remove or comment out the local Participant type if ParticipantLike is sufficient
// interface Participant {
//   id: string;
//   firstName?: string;
//   lastName?: string;
//   image?: string;
//   // Add other properties if used from participant object
// }

interface ConversationListProps {
  onSelectConversation: (conversationId: string) => void;
  selectedConversationId?: string;
}

const ConversationList: React.FC<ConversationListProps> = ({
  onSelectConversation,
  selectedConversationId,
}) => {
  const { conversations, loadConversations, loading } = useMessagingContext();

  useEffect(() => {
    loadConversations();
  }, [loadConversations]);

  const getLastMessage = (conversation: IConversation) => {
    if (!conversation.messages || conversation.messages.length === 0) {
      return 'Pas de messages';
    }

    const lastMessage = conversation.messages[0];
    return lastMessage.content.length > 30
      ? `${lastMessage.content.substring(0, 30)}...`
      : lastMessage.content;
  };

  const getLastMessageTime = (conversation: IConversation) => {
    if (!conversation.messages || conversation.messages.length === 0) {
      return '';
    }

    const lastMessage = conversation.messages[0];
    return formatDistanceToNow(new Date(lastMessage.sentAt), {
      addSuffix: true,
      locale: fr,
    });
  };

  const getConversationTitle = (conversation: IConversation) => {
    if (conversation.title) {
      return conversation.title;
    }

    // For direct conversations, use the other participant's name
    if (conversation.type === 'DIRECT' && conversation.participants.length === 2) {
      const otherParticipant = conversation.participants.find(
        (p: ParticipantLike) => p.id !== localStorage.getItem('userId')
      );

      if (otherParticipant) {
        return `${otherParticipant.firstName} ${otherParticipant.lastName}`;
      }
    }

    return 'Conversation';
  };

  const getParticipantInitials = (conversation: IConversation) => {
    if (conversation.type !== 'DIRECT' || conversation.participants.length !== 2) {
      return 'G';
    }

    const otherParticipant = conversation.participants.find(
      (p: ParticipantLike) => p.id !== localStorage.getItem('userId')
    );

    if (!otherParticipant) return '?';

    return `${otherParticipant.firstName?.[0] || ''}${otherParticipant.lastName?.[0] || ''}`;
  };

  const getParticipantImage = (conversation: IConversation) => {
    if (conversation.type !== 'DIRECT' || conversation.participants.length !== 2) {
      return undefined;
    }

    const otherParticipant = conversation.participants.find(
      (p: ParticipantLike) => p.id !== localStorage.getItem('userId')
    );

    return otherParticipant?.image || undefined;
  };

  if (loading && conversations.length === 0) {
    return (
      <div className='flex flex-col h-full p-4 border-r border-gray-200'>
        <h2 className='text-lg font-semibold mb-4'>Conversations</h2>
        <div className='flex justify-center items-center h-full'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-green-500'></div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-col h-full p-4 border-r border-gray-200'>
      <h2 className='text-lg font-semibold mb-4'>Conversations</h2>

      {conversations.length === 0 ? (
        <div className='flex flex-col items-center justify-center h-full text-gray-500'>
          <p>Aucune conversation</p>
        </div>
      ) : (
        <div className='overflow-y-auto'>
          {conversations.map((conversation: IConversation) => (
            <div
              key={conversation.id}
              role='button'
              tabIndex={0}
              className={`flex items-center p-3 mb-2 rounded-lg cursor-pointer hover:bg-gray-100 ${
                selectedConversationId === conversation.id ? 'bg-gray-100' : ''
              }`}
              onClick={() => onSelectConversation(conversation.id)}
              onKeyDown={(e: React.KeyboardEvent<HTMLDivElement>) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  onSelectConversation(conversation.id);
                }
              }}
            >
              {getParticipantImage(conversation) ? (
                <img
                  src={getParticipantImage(conversation)}
                  alt='Avatar'
                  className='w-12 h-12 rounded-full mr-3'
                />
              ) : (
                <div className='w-12 h-12 rounded-full bg-green-500 text-white flex items-center justify-center mr-3'>
                  {getParticipantInitials(conversation)}
                </div>
              )}

              <div className='flex-1 min-w-0'>
                <div className='flex justify-between items-center'>
                  <h3 className='text-sm font-semibold truncate'>
                    {getConversationTitle(conversation)}
                  </h3>
                  <span className='text-xs text-gray-500'>{getLastMessageTime(conversation)}</span>
                </div>
                <p className='text-sm text-gray-600 truncate'>{getLastMessage(conversation)}</p>
              </div>

              {(conversation.unreadCount || 0) > 0 && (
                <div className='ml-2 bg-green-500 text-white text-xs font-semibold rounded-full w-5 h-5 flex items-center justify-center'>
                  {conversation.unreadCount}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ConversationList;
