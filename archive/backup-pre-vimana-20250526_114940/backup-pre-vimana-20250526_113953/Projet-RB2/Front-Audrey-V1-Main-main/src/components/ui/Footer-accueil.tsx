import React from 'react';
import { Link } from 'react-router-dom';

const FooterAccueil: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className='bg-white border-t border-gray-200'>
      <div className='max-w-7xl mx-auto px-4 py-6'>
        <div className='flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0'>
          <div className='text-sm text-gray-600'>
            © {currentYear} Retreat & Be. Tous droits réservés.
          </div>
          <div className='flex space-x-6'>
            <Link
              to='/mentions-legales'
              className='text-sm text-gray-600 hover:text-retreat-green transition-colors'
            >
              Mentions légales
            </Link>
            <Link
              to='/politique-confidentialite'
              className='text-sm text-gray-600 hover:text-retreat-green transition-colors'
            >
              Politique de confidentialité
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default FooterAccueil;
