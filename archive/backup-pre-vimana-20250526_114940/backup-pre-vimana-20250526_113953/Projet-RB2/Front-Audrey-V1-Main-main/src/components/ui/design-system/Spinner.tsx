/**
 * Composant Spinner Unifié - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Composant de chargement standardisé pour toute l'application.
 */

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../../utils/cn';

const spinnerVariants = cva(
  'animate-spin rounded-full border-2 border-current border-t-transparent',
  {
    variants: {
      size: {
        sm: 'h-4 w-4',
        md: 'h-6 w-6',
        lg: 'h-8 w-8',
        xl: 'h-12 w-12',
      },
      color: {
        primary: 'text-primary-600',
        secondary: 'text-secondary-600',
        neutral: 'text-neutral-600',
        white: 'text-white',
        current: 'text-current',
      },
    },
    defaultVariants: {
      size: 'md',
      color: 'current',
    },
  }
);

export interface SpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spinnerVariants> {
  label?: string;
}

export const Spinner: React.FC<SpinnerProps> = ({
  className,
  size,
  color,
  label = 'Loading...',
  ...props
}) => {
  return (
    <div
      className={cn(spinnerVariants({ size, color }), className)}
      role="status"
      aria-label={label}
      {...props}
    >
      <span className="sr-only">{label}</span>
    </div>
  );
};

// Composant de chargement avec texte
export interface LoadingProps {
  size?: VariantProps<typeof spinnerVariants>['size'];
  color?: VariantProps<typeof spinnerVariants>['color'];
  text?: string;
  className?: string;
}

export const Loading: React.FC<LoadingProps> = ({
  size = 'md',
  color = 'primary',
  text = 'Chargement...',
  className,
}) => {
  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <Spinner size={size} color={color} />
      <span className="text-sm text-neutral-600">{text}</span>
    </div>
  );
};

// Composant de chargement pleine page
export const FullPageLoading: React.FC<{
  text?: string;
}> = ({ text = 'Chargement de l\'application...' }) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/80 backdrop-blur-sm">
      <div className="flex flex-col items-center space-y-4">
        <Spinner size="xl" color="primary" />
        <p className="text-lg font-medium text-neutral-700">{text}</p>
      </div>
    </div>
  );
};

// Composant de chargement pour les cartes/sections
export const SectionLoading: React.FC<{
  height?: string;
  text?: string;
}> = ({ height = 'h-32', text = 'Chargement...' }) => {
  return (
    <div className={cn('flex items-center justify-center', height)}>
      <Loading text={text} />
    </div>
  );
};

// Skeleton loader pour le contenu
export const Skeleton: React.FC<{
  className?: string;
  lines?: number;
}> = ({ className, lines = 1 }) => {
  return (
    <div className={cn('animate-pulse', className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className={cn(
            'bg-neutral-200 rounded',
            i === 0 ? 'h-4' : 'h-3 mt-2',
            i === lines - 1 ? 'w-3/4' : 'w-full'
          )}
        />
      ))}
    </div>
  );
};

// Exemples d'utilisation
export const SpinnerExamples = () => {
  return (
    <div className="space-y-6 p-6">
      <div>
        <h3 className="mb-3 text-lg font-semibold">Tailles</h3>
        <div className="flex items-center space-x-4">
          <Spinner size="sm" />
          <Spinner size="md" />
          <Spinner size="lg" />
          <Spinner size="xl" />
        </div>
      </div>

      <div>
        <h3 className="mb-3 text-lg font-semibold">Couleurs</h3>
        <div className="flex items-center space-x-4">
          <Spinner color="primary" />
          <Spinner color="secondary" />
          <Spinner color="neutral" />
        </div>
      </div>

      <div>
        <h3 className="mb-3 text-lg font-semibold">Avec texte</h3>
        <Loading text="Chargement des données..." />
      </div>

      <div>
        <h3 className="mb-3 text-lg font-semibold">Section loading</h3>
        <div className="border rounded-lg">
          <SectionLoading height="h-24" text="Chargement du contenu..." />
        </div>
      </div>

      <div>
        <h3 className="mb-3 text-lg font-semibold">Skeleton</h3>
        <div className="space-y-3">
          <Skeleton lines={1} className="w-1/2" />
          <Skeleton lines={3} />
        </div>
      </div>
    </div>
  );
};
