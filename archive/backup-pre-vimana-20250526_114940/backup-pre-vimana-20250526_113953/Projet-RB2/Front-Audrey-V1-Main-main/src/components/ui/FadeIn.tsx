import React, { useEffect, useRef, useState } from 'react';

interface FadeInProps {
  children: React.ReactNode;
  duration?: number;
  delay?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
  distance?: number;
  className?: string;
}

export const FadeIn: React.FC<FadeInProps> = ({
  children,
  duration = 500,
  delay = 0,
  direction = 'up',
  distance = 20,
  className = '',
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const domRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(entries => {
      // Si l'élément est visible dans le viewport
      if (entries[0].isIntersecting) {
        setIsVisible(true);
        // Désinscrire l'observer une fois que l'élément est visible
        if (domRef.current) observer.unobserve(domRef.current);
      }
    });
    
    // Observer l'élément
    if (domRef.current) observer.observe(domRef.current);
    
    // Nettoyer l'observer lors du démontage du composant
    return () => {
      if (domRef.current) observer.unobserve(domRef.current);
    };
  }, []);

  // Déterminer les styles de transformation en fonction de la direction
  const getTransform = () => {
    switch (direction) {
      case 'up':
        return `translateY(${distance}px)`;
      case 'down':
        return `translateY(-${distance}px)`;
      case 'left':
        return `translateX(${distance}px)`;
      case 'right':
        return `translateX(-${distance}px)`;
      case 'none':
      default:
        return 'none';
    }
  };

  // Styles pour l'animation
  const fadeInStyle: React.CSSProperties = {
    opacity: isVisible ? 1 : 0,
    transform: isVisible ? 'none' : getTransform(),
    transition: `opacity ${duration}ms ease-out ${delay}ms, transform ${duration}ms ease-out ${delay}ms`,
    willChange: 'opacity, transform',
  };

  return (
    <div
      ref={domRef}
      className={className}
      style={fadeInStyle}
    >
      {children}
    </div>
  );
};

export default FadeIn;
