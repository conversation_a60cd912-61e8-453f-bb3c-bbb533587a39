import React, { useState } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { moderationService } from '../../services/api/moderationService';
import { Report, ModerationAction } from './ReportList';

export interface ReportDetailsProps {
  report: Report;
  onClose: () => void;
  userRole: 'admin' | 'moderator' | 'user';
}

export const ReportDetails: React.FC<ReportDetailsProps> = ({ report, onClose, userRole }) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [actionType, setActionType] = useState<string>('');
  const [comment, setComment] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const canModerate = userRole === 'admin' || userRole === 'moderator';
  const isResolved = report.status === 'APPROVED' || report.status === 'REJECTED';

  const handleActionSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!actionType) {
      setError(t('moderation.reports.details.errors.actionRequired'));
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      await moderationService.addModerationAction(report.id, {
        action: actionType,
        comment: comment.trim() || undefined,
        moderatorId: 'current-user-id', // This should be replaced with the actual user ID
      });
      
      setSuccess(t('moderation.reports.details.success'));
      setActionType('');
      setComment('');
      
      // Refresh the report after a short delay
      setTimeout(() => {
        onClose();
      }, 1500);
      
    } catch (error) {
      console.error('Error adding moderation action:', error);
      setError(t('moderation.reports.details.errors.actionFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'IN_REVIEW':
        return 'bg-blue-100 text-blue-800';
      case 'APPROVED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      case 'ESCALATED':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getActionBadgeClass = (action: string) => {
    switch (action) {
      case 'APPROVE':
        return 'bg-green-100 text-green-800';
      case 'REJECT':
        return 'bg-red-100 text-red-800';
      case 'ESCALATE':
        return 'bg-purple-100 text-purple-800';
      case 'WARN_USER':
        return 'bg-orange-100 text-orange-800';
      case 'BAN_USER':
        return 'bg-red-100 text-red-800';
      case 'DELETE_CONTENT':
        return 'bg-red-100 text-red-800';
      case 'HIDE_CONTENT':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="flex justify-between items-center border-b px-6 py-4">
        <h2 className="text-xl font-semibold">{t('moderation.reports.details.title')}</h2>
        <button
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h3 className="text-lg font-medium mb-4">{t('moderation.reports.details.reportInfo')}</h3>
            
            <div className="space-y-3">
              <div>
                <span className="text-sm text-gray-500">{t('moderation.reports.details.id')}:</span>
                <p className="text-sm font-medium">{report.id}</p>
              </div>
              
              <div>
                <span className="text-sm text-gray-500">{t('moderation.reports.details.contentType')}:</span>
                <p className="text-sm font-medium">{report.contentType}</p>
              </div>
              
              <div>
                <span className="text-sm text-gray-500">{t('moderation.reports.details.contentId')}:</span>
                <p className="text-sm font-medium">{report.contentId}</p>
              </div>
              
              <div>
                <span className="text-sm text-gray-500">{t('moderation.reports.details.reporterId')}:</span>
                <p className="text-sm font-medium">{report.reporterId}</p>
              </div>
              
              <div>
                <span className="text-sm text-gray-500">{t('moderation.reports.details.createdAt')}:</span>
                <p className="text-sm font-medium">{formatDate(report.createdAt)}</p>
              </div>
              
              <div>
                <span className="text-sm text-gray-500">{t('moderation.reports.details.status')}:</span>
                <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(report.status)}`}>
                  {report.status}
                </span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-4">{t('moderation.reports.details.reportContent')}</h3>
            
            <div className="space-y-3">
              <div>
                <span className="text-sm text-gray-500">{t('moderation.reports.details.reason')}:</span>
                <p className="text-sm font-medium">{report.reason}</p>
              </div>
              
              {report.description && (
                <div>
                  <span className="text-sm text-gray-500">{t('moderation.reports.details.description')}:</span>
                  <p className="text-sm mt-1 whitespace-pre-wrap">{report.description}</p>
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="mt-8">
          <h3 className="text-lg font-medium mb-4">{t('moderation.reports.details.moderationActions')}</h3>
          
          {report.moderationActions.length === 0 ? (
            <p className="text-sm text-gray-500">{t('moderation.reports.details.noActions')}</p>
          ) : (
            <div className="space-y-4">
              {report.moderationActions.map((action) => (
                <div key={action.id} className="bg-gray-50 p-4 rounded-md">
                  <div className="flex justify-between items-start">
                    <div>
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getActionBadgeClass(action.action)}`}>
                        {action.action}
                      </span>
                      <p className="text-sm text-gray-500 mt-1">
                        {t('moderation.reports.details.by')} {action.moderatorId} • {formatDate(action.createdAt)}
                      </p>
                    </div>
                  </div>
                  
                  {action.comment && (
                    <div className="mt-2">
                      <p className="text-sm whitespace-pre-wrap">{action.comment}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
        
        {canModerate && !isResolved && (
          <div className="mt-8 border-t pt-6">
            <h3 className="text-lg font-medium mb-4">{t('moderation.reports.details.takeAction')}</h3>
            
            {error && (
              <div className="bg-red-50 text-red-700 p-3 rounded-md mb-4">
                {error}
              </div>
            )}
            
            {success && (
              <div className="bg-green-50 text-green-700 p-3 rounded-md mb-4">
                {success}
              </div>
            )}
            
            <form onSubmit={handleActionSubmit}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('moderation.reports.details.actionType')}
                </label>
                <select
                  value={actionType}
                  onChange={(e) => setActionType(e.target.value)}
                  className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  disabled={isLoading}
                >
                  <option value="">{t('moderation.reports.details.selectAction')}</option>
                  <option value="APPROVE">{t('moderation.reports.details.actions.approve')}</option>
                  <option value="REJECT">{t('moderation.reports.details.actions.reject')}</option>
                  <option value="ESCALATE">{t('moderation.reports.details.actions.escalate')}</option>
                  <option value="WARN_USER">{t('moderation.reports.details.actions.warnUser')}</option>
                  <option value="BAN_USER">{t('moderation.reports.details.actions.banUser')}</option>
                  <option value="DELETE_CONTENT">{t('moderation.reports.details.actions.deleteContent')}</option>
                  <option value="HIDE_CONTENT">{t('moderation.reports.details.actions.hideContent')}</option>
                </select>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('moderation.reports.details.comment')}
                </label>
                <textarea
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  rows={3}
                  className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder={t('moderation.reports.details.commentPlaceholder')}
                  disabled={isLoading}
                ></textarea>
              </div>
              
              <div className="flex justify-end">
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {t('common.processing')}
                    </span>
                  ) : (
                    t('moderation.reports.details.submitAction')
                  )}
                </button>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReportDetails;
