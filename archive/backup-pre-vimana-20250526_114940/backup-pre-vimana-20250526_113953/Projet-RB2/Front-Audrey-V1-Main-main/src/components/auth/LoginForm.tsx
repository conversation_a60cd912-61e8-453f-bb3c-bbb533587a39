import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '../../contexts/AuthContext'; // Adjusted path
import { LoginCredentials } from '../../services/api/authService'; // Adjusted path
import { useNavigate } from 'react-router-dom';

const loginSchema = z.object({
  email: z.string().email({ message: 'Adresse e-mail invalide' }),
  password: z.string().min(6, { message: 'Le mot de passe doit contenir au moins 6 caractères' }),
});

type LoginFormInputs = z.infer<typeof loginSchema>;

export const LoginForm: React.FC = () => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormInputs>({
    resolver: zodResolver(loginSchema),
  });

  const { login, error: authError, clearError } = useAuth();
  const navigate = useNavigate();

  const onSubmit = async (data: LoginFormInputs) => {
    clearError(); // Clear previous auth errors
    try {
      const credentials: LoginCredentials = { email: data.email, password: data.password };
      await login(credentials);
      navigate('/'); // Redirect to homepage or dashboard on successful login
    } catch (err) {
      // Error is already set in AuthContext, or can be handled here if more specific UI is needed
      console.error('Login failed:', err);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className='space-y-6 w-full max-w-md p-8 bg-white shadow-xl rounded-lg'>
      <h2 className='text-3xl font-bold text-center text-gray-800'>Connexion</h2>
      
      {authError && (
        <div role='alert' className='p-4 mb-4 text-sm text-red-700 bg-red-100 rounded-lg'>
          {authError}
        </div>
      )}

      <div>
        <label htmlFor='email' className='block text-sm font-medium text-gray-700 mb-1'>
          Adresse E-mail
        </label>
        <input
          id='email'
          type='email'
          {...register('email')}
          className={`mt-1 block w-full px-4 py-2 border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
          aria-invalid={errors.email ? 'true' : 'false'}
        />
        {errors.email && (
          <p role='alert' className='mt-2 text-sm text-red-600'>
            {errors.email.message}
          </p>
        )}
      </div>

      <div>
        <label htmlFor='password' className='block text-sm font-medium text-gray-700 mb-1'>
          Mot de passe
        </label>
        <input
          id='password'
          type='password'
          {...register('password')}
          className={`mt-1 block w-full px-4 py-2 border ${errors.password ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
          aria-invalid={errors.password ? 'true' : 'false'}
        />
        {errors.password && (
          <p role='alert' className='mt-2 text-sm text-red-600'>
            {errors.password.message}
          </p>
        )}
      </div>

      {/* Optional: Add remember me and forgot password links here */}
      {/*
      <div className='flex items-center justify-between'>
        <div className='flex items-center'>
          <input id='remember-me' name='remember-me' type='checkbox' className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded' />
          <label htmlFor='remember-me' className='ml-2 block text-sm text-gray-900'>Se souvenir de moi</label>
        </div>
        <div className='text-sm'>
          <a href='#' className='font-medium text-indigo-600 hover:text-indigo-500'>Mot de passe oublié ?</a>
        </div>
      </div>
      */}

      <div>
        <button
          type='submit'
          disabled={isSubmitting}
          className='w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50'
        >
          {isSubmitting ? 'Connexion en cours...' : 'Se connecter'}
        </button>
      </div>
    </form>
  );
}; 