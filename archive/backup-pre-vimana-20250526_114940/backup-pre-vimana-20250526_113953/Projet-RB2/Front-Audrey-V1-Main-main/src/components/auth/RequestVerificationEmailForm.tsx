'use client';

import React, { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '../../contexts/AuthContext';
import { toast } from 'react-toastify';

const requestVerificationEmailSchema = z.object({
  email: z.string().email({ message: "Invalid email address" }),
});

type RequestVerificationEmailFormValues = z.infer<typeof requestVerificationEmailSchema>;

const RequestVerificationEmailForm: React.FC = () => {
  const { requestVerificationEmail } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<RequestVerificationEmailFormValues>({
    resolver: zodResolver(requestVerificationEmailSchema),
  });

  const onSubmit: SubmitHandler<RequestVerificationEmailFormValues> = async (data) => {
    setIsLoading(true);
    setMessage(null);
    try {
      await requestVerificationEmail(data.email);
      setMessage("If an account with this email exists, a new verification email has been sent. Please check your inbox.");
      toast.success("Verification email request sent successfully.");
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || "An unexpected error occurred. Please try again.";
      setMessage(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 bg-white p-8 rounded-lg shadow-md w-full max-w-md">
      <h2 className='text-2xl font-bold text-center text-gray-800 dark:text-white mb-6'>Request New Verification Email</h2>
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
        <input
          id="email"
          type="email"
          {...register("email")}
          className={`mt-1 block w-full px-3 py-2 border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
          placeholder="<EMAIL>"
        />
        {errors.email && <p className="mt-2 text-sm text-red-600">{errors.email.message}</p>}
      </div>

      {message && <p className={`mt-4 text-sm ${message.startsWith("If an account") ? 'text-green-600' : 'text-red-600'}`}>{message}</p>}

      <div>
        <button 
            type="submit" 
            disabled={isLoading} 
            className="w-full mt-6 flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
            {isLoading ? 'Sending...' : 'Send Verification Email'}
        </button>
      </div>
    </form>
  );
};

export default RequestVerificationEmailForm; 