import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const passwordSchema = z.object({
  password: z.string().min(8, 'Le mot de passe doit contenir au moins 8 caractères'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Les mots de passe ne correspondent pas',
  path: ['confirmPassword'], // path of error
});

type ResetPasswordFormInputs = z.infer<typeof passwordSchema>;

interface ResetPasswordFormProps {
  token: string;
}

export const ResetPasswordForm: React.FC<ResetPasswordFormProps> = ({ token }) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<ResetPasswordFormInputs>({
    resolver: zodResolver(passwordSchema),
  });

  const { resetPassword, error: authError, clearError } = useAuth();
  const navigate = useNavigate();
  const [message, setMessage] = useState<string | null>(null);

  const onSubmit = async (data: ResetPasswordFormInputs) => {
    clearError();
    setMessage(null);
    try {
      await resetPassword(token, data.password);
      setMessage('Votre mot de passe a été réinitialisé avec succès. Vous pouvez maintenant vous connecter.');
      // Optional: redirect to login after a delay
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (err) {
      // AuthError will be set by AuthContext, or we can set a generic one here
      console.error('Reset password failed:', err);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className='space-y-6 w-full max-w-md p-8 bg-white shadow-xl rounded-lg'>
      <h2 className='text-3xl font-bold text-center text-gray-800'>Réinitialiser le mot de passe</h2>
      
      {message && (
        <div role='status' className='p-4 mb-4 text-sm text-green-700 bg-green-100 rounded-lg'>
          {message}
        </div>
      )}
      {authError && !message && (
        <div role='alert' className='p-4 mb-4 text-sm text-red-700 bg-red-100 rounded-lg'>
          {authError}
        </div>
      )}

      <div>
        <label htmlFor='password' className='block text-sm font-medium text-gray-700 mb-1'>
          Nouveau mot de passe
        </label>
        <input
          id='password'
          type='password'
          {...register('password')}
          className={`mt-1 block w-full px-4 py-2 border ${errors.password ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
          aria-invalid={errors.password ? 'true' : 'false'}
        />
        {errors.password && (
          <p role='alert' className='mt-2 text-sm text-red-600'>
            {errors.password.message}
          </p>
        )}
      </div>

      <div>
        <label htmlFor='confirmPassword' className='block text-sm font-medium text-gray-700 mb-1'>
          Confirmer le nouveau mot de passe
        </label>
        <input
          id='confirmPassword'
          type='password'
          {...register('confirmPassword')}
          className={`mt-1 block w-full px-4 py-2 border ${errors.confirmPassword ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
          aria-invalid={errors.confirmPassword ? 'true' : 'false'}
        />
        {errors.confirmPassword && (
          <p role='alert' className='mt-2 text-sm text-red-600'>
            {errors.confirmPassword.message}
          </p>
        )}
      </div>

      <div>
        <button
          type='submit'
          disabled={isSubmitting || !!message} // Disable if submitting or if success message is shown
          className='w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50'
        >
          {isSubmitting ? 'Réinitialisation en cours...' : 'Réinitialiser le mot de passe'}
        </button>
      </div>
    </form>
  );
}; 