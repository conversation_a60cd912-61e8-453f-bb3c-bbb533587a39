import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { matchingVideoService } from '../../services/api/matchingVideoService';
import { matchingAnalyticsService } from '../../services/api/matchingAnalyticsService';
import { MatchingResult } from '../../services/api/matchingService';
import LoadingSpinner from '../atoms/LoadingSpinner/LoadingSpinner';

interface VideoRoom {
  id: string;
  externalRoomId: string;
  partnerId: string;
  retreatId: string;
  title: string;
  description?: string;
  scheduledFor: string;
  duration: number;
  isPrivate: boolean;
  status: string;
  joinUrl: string;
  hostUrl: string;
  createdBy: string;
  createdAt: string;
}

interface MatchingVideoRoomsProps {
  matchingResult: MatchingResult;
  userRole: string;
  userId: string;
  onCreateRoom?: () => void;
}

const MatchingVideoRooms: React.FC<MatchingVideoRoomsProps> = ({
  matchingResult,
  userRole,
  userId,
  onCreateRoom,
}) => {
  const [videoRooms, setVideoRooms] = useState<VideoRoom[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [joiningRoom, setJoiningRoom] = useState<string | null>(null);

  // Charger les salles de vidéoconférence
  useEffect(() => {
    const fetchVideoRooms = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const rooms = await matchingVideoService.getVideoRooms(
          matchingResult.partnerId,
          matchingResult.retreatId,
        );
        
        setVideoRooms(rooms);
      } catch (error) {
        console.error('Error fetching video rooms:', error);
        setError('Erreur lors du chargement des salles de vidéoconférence');
      } finally {
        setIsLoading(false);
      }
    };

    fetchVideoRooms();
  }, [matchingResult.partnerId, matchingResult.retreatId]);

  // Fonction pour rejoindre une salle
  const handleJoinRoom = async (roomId: string) => {
    try {
      setJoiningRoom(roomId);
      
      // Générer le lien de participation
      const joinUrl = await matchingVideoService.generateJoinLink(roomId);
      
      // Enregistrer l'événement d'analyse
      await matchingAnalyticsService.recordMatchingInteraction(
        matchingResult,
        'VIDEO_ROOM_JOINED',
        { roomId },
      );
      
      // Ouvrir le lien dans un nouvel onglet
      window.open(joinUrl, '_blank');
    } catch (error) {
      console.error('Error joining video room:', error);
      setError('Erreur lors de la connexion à la salle de vidéoconférence');
    } finally {
      setJoiningRoom(null);
    }
  };

  // Fonction pour terminer une salle
  const handleEndRoom = async (roomId: string) => {
    try {
      await matchingVideoService.endVideoRoom(roomId);
      
      // Mettre à jour la liste des salles
      setVideoRooms(prevRooms => 
        prevRooms.map(room => 
          room.id === roomId ? { ...room, status: 'ENDED' } : room
        )
      );
      
      // Enregistrer l'événement d'analyse
      await matchingAnalyticsService.recordMatchingInteraction(
        matchingResult,
        'VIDEO_ROOM_ENDED',
        { roomId },
      );
    } catch (error) {
      console.error('Error ending video room:', error);
      setError('Erreur lors de la fermeture de la salle de vidéoconférence');
    }
  };

  // Fonction pour formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'PPP à HH:mm', { locale: fr });
  };

  // Fonction pour obtenir le statut en français
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'SCHEDULED':
        return 'Programmée';
      case 'ACTIVE':
        return 'En cours';
      case 'ENDED':
        return 'Terminée';
      case 'CANCELLED':
        return 'Annulée';
      default:
        return status;
    }
  };

  // Fonction pour obtenir la couleur du statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SCHEDULED':
        return 'bg-blue-100 text-blue-800';
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'ENDED':
        return 'bg-gray-100 text-gray-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-center items-center h-32">
          <LoadingSpinner />
          <span className="ml-2 text-gray-600">Chargement des salles de vidéoconférence...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center text-red-600">
          <p>{error}</p>
          <button
            className="mt-2 text-retreat-green hover:text-retreat-green-dark"
            onClick={() => window.location.reload()}
          >
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Vidéoconférences
        </h3>
        {onCreateRoom && (
          <button
            onClick={onCreateRoom}
            className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
          >
            <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Nouvelle vidéoconférence
          </button>
        )}
      </div>
      
      {videoRooms.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
          <p className="mt-2">Aucune vidéoconférence n'a été programmée pour ce matching.</p>
          {onCreateRoom && (
            <button
              onClick={onCreateRoom}
              className="mt-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
            >
              Programmer une vidéoconférence
            </button>
          )}
        </div>
      ) : (
        <div className="overflow-hidden">
          <ul className="divide-y divide-gray-200">
            {videoRooms.map((room) => (
              <li key={room.id} className="py-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">{room.title}</p>
                    {room.description && (
                      <p className="mt-1 text-sm text-gray-500">{room.description}</p>
                    )}
                    <div className="mt-2 flex items-center text-sm text-gray-500">
                      <svg className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <span>{formatDate(room.scheduledFor)}</span>
                    </div>
                    <div className="mt-1 flex items-center text-sm text-gray-500">
                      <svg className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>{room.duration} minutes</span>
                    </div>
                    <div className="mt-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(room.status)}`}>
                        {getStatusLabel(room.status)}
                      </span>
                    </div>
                  </div>
                  <div className="ml-4 flex-shrink-0 flex">
                    {room.status !== 'ENDED' && room.status !== 'CANCELLED' && (
                      <button
                        onClick={() => handleJoinRoom(room.id)}
                        disabled={joiningRoom === room.id}
                        className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-2"
                      >
                        {joiningRoom === room.id ? (
                          <LoadingSpinner size="small" color="white" />
                        ) : (
                          <>
                            <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                            Rejoindre
                          </>
                        )}
                      </button>
                    )}
                    {(room.createdBy === userId || userRole === 'ADMIN') && room.status !== 'ENDED' && room.status !== 'CANCELLED' && (
                      <button
                        onClick={() => handleEndRoom(room.id)}
                        className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
                      >
                        <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        Terminer
                      </button>
                    )}
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default MatchingVideoRooms;
