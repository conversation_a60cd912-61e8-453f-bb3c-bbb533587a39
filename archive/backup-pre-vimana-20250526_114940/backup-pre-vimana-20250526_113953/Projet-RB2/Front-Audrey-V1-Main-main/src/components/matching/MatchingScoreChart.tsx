import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

interface CompatibilityFactors {
  skillMatch: number;
  availabilityMatch: number;
  locationMatch: number;
  ratingMatch: number;
  budgetMatch: number;
}

interface MatchingScoreChartProps {
  score: number;
  compatibilityFactors: CompatibilityFactors;
  width?: number;
  height?: number;
}

const MatchingScoreChart: React.FC<MatchingScoreChartProps> = ({
  score,
  compatibilityFactors,
  width = 500,
  height = 400,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current) return;

    // Nettoyer le SVG
    d3.select(svgRef.current).selectAll('*').remove();

    // Préparer les données
    const data = [
      { name: 'Compétences', value: compatibilityFactors.skillMatch, weight: 0.35 },
      { name: 'Disponibilité', value: compatibilityFactors.availabilityMatch, weight: 0.15 },
      { name: 'Localisation', value: compatibilityFactors.locationMatch, weight: 0.20 },
      { name: 'Évaluations', value: compatibilityFactors.ratingMatch, weight: 0.20 },
      { name: 'Budget', value: compatibilityFactors.budgetMatch, weight: 0.10 },
    ];

    // Définir les marges
    const margin = { top: 40, right: 30, bottom: 50, left: 60 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    // Créer le SVG
    const svg = d3
      .select(svgRef.current)
      .attr('width', width)
      .attr('height', height)
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Échelles
    const x = d3
      .scaleBand()
      .domain(data.map((d) => d.name))
      .range([0, innerWidth])
      .padding(0.3);

    const y = d3
      .scaleLinear()
      .domain([0, 100])
      .range([innerHeight, 0]);

    // Fonction pour obtenir la couleur en fonction du score
    const getColor = (value: number) => {
      if (value >= 80) return '#10B981'; // vert
      if (value >= 60) return '#FBBF24'; // jaune
      if (value >= 40) return '#F97316'; // orange
      return '#EF4444'; // rouge
    };

    // Ajouter les barres
    svg
      .selectAll('.bar')
      .data(data)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('x', (d) => x(d.name) || 0)
      .attr('width', x.bandwidth())
      .attr('y', (d) => y(0))
      .attr('height', 0)
      .attr('fill', (d) => getColor(d.value))
      .attr('rx', 4) // coins arrondis
      .attr('ry', 4)
      .transition()
      .duration(800)
      .attr('y', (d) => y(d.value))
      .attr('height', (d) => innerHeight - y(d.value));

    // Ajouter les valeurs sur les barres
    svg
      .selectAll('.bar-value')
      .data(data)
      .enter()
      .append('text')
      .attr('class', 'bar-value')
      .attr('x', (d) => (x(d.name) || 0) + x.bandwidth() / 2)
      .attr('y', (d) => y(d.value) - 5)
      .attr('text-anchor', 'middle')
      .attr('font-size', '12px')
      .attr('font-weight', 'bold')
      .attr('fill', (d) => getColor(d.value))
      .text((d) => `${d.value}%`)
      .style('opacity', 0)
      .transition()
      .duration(800)
      .style('opacity', 1);

    // Ajouter les poids sous les barres
    svg
      .selectAll('.bar-weight')
      .data(data)
      .enter()
      .append('text')
      .attr('class', 'bar-weight')
      .attr('x', (d) => (x(d.name) || 0) + x.bandwidth() / 2)
      .attr('y', innerHeight + 20)
      .attr('text-anchor', 'middle')
      .attr('font-size', '10px')
      .attr('fill', '#6B7280')
      .text((d) => `Poids: ${d.weight * 100}%`);

    // Ajouter l'axe X
    svg
      .append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(x))
      .selectAll('text')
      .attr('font-size', '12px')
      .style('text-anchor', 'middle');

    // Ajouter l'axe Y
    svg
      .append('g')
      .call(d3.axisLeft(y).ticks(5).tickFormat((d) => `${d}%`))
      .selectAll('text')
      .attr('font-size', '12px');

    // Ajouter le titre
    svg
      .append('text')
      .attr('x', innerWidth / 2)
      .attr('y', -15)
      .attr('text-anchor', 'middle')
      .attr('font-size', '16px')
      .attr('font-weight', 'bold')
      .text(`Score global: ${score}%`);

    // Ajouter une ligne pour le score global
    svg
      .append('line')
      .attr('x1', 0)
      .attr('x2', innerWidth)
      .attr('y1', y(score))
      .attr('y2', y(score))
      .attr('stroke', getColor(score))
      .attr('stroke-width', 2)
      .attr('stroke-dasharray', '5,5')
      .style('opacity', 0)
      .transition()
      .duration(1000)
      .style('opacity', 1);

    // Ajouter un label pour la ligne du score global
    svg
      .append('text')
      .attr('x', innerWidth)
      .attr('y', y(score) - 5)
      .attr('text-anchor', 'end')
      .attr('font-size', '12px')
      .attr('font-weight', 'bold')
      .attr('fill', getColor(score))
      .text(`Score global: ${score}%`)
      .style('opacity', 0)
      .transition()
      .duration(1000)
      .style('opacity', 1);

  }, [score, compatibilityFactors, width, height]);

  return (
    <div className="bg-white p-4 rounded-lg shadow-md">
      <svg ref={svgRef} className="w-full h-auto"></svg>
    </div>
  );
};

export default MatchingScoreChart;
