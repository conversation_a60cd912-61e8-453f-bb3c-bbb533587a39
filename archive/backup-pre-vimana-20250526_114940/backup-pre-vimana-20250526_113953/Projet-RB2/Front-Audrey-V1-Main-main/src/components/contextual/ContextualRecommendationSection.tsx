import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { contextualRecommendationService, UserContext } from '../../services/api/contextualRecommendationService';
import { useAuthContext } from '../../hooks/useAuthContext';
import { toast } from 'react-toastify';
import { t } from '../../services/i18n/i18nService';
import RetreatCard from '../retreats/RetreatCard';
import LoadingSpinner from '../common/LoadingSpinner';

/**
 * Interface pour les propriétés du composant
 */
interface ContextualRecommendationSectionProps {
  /** Nombre de recommandations à afficher */
  count?: number;
  
  /** Titre de la section */
  title?: string;
  
  /** Description de la section */
  description?: string;
  
  /** Types de contexte à prendre en compte */
  contextTypes?: Array<'location' | 'weather' | 'season' | 'time' | 'events' | 'cultural'>;
  
  /** Classe CSS supplémentaire */
  className?: string;
}

/**
 * Composant pour afficher les recommandations contextuelles
 */
const ContextualRecommendationSection: React.FC<ContextualRecommendationSectionProps> = ({
  count = 3,
  title = t('contextual.title'),
  description = t('contextual.description'),
  contextTypes = ['location', 'weather', 'season', 'time', 'events', 'cultural'],
  className = '',
}) => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [userContext, setUserContext] = useState<UserContext | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Charger le contexte utilisateur et les recommandations contextuelles
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Récupérer le contexte utilisateur
        const context = await contextualRecommendationService.getMyContext();
        setUserContext(context);
        
        // Récupérer les recommandations contextuelles
        const contextualRecommendations = await contextualRecommendationService.getContextualRecommendations({
          contextFactor: 0.7,
          maxRecommendations: count,
          contextTypes,
        });
        
        setRecommendations(contextualRecommendations);
      } catch (error) {
        console.error('Erreur lors du chargement des recommandations contextuelles:', error);
        setError(t('contextual.loadError'));
      } finally {
        setLoading(false);
      }
    };
    
    if (user) {
      loadData();
    }
  }, [user, count, contextTypes]);
  
  // Générer un message contextuel en fonction du contexte utilisateur
  const getContextualMessage = () => {
    if (!userContext) {
      return '';
    }
    
    const messages = [];
    
    // Message basé sur la localisation
    if (userContext.location) {
      messages.push(t('contextual.locationMessage', { city: userContext.location.city }));
    }
    
    // Message basé sur la météo
    if (userContext.weather) {
      const condition = userContext.weather.condition.toLowerCase();
      const temperature = Math.round(userContext.weather.temperature);
      
      if (condition === 'sunny' || condition === 'clear') {
        messages.push(t('contextual.weatherSunnyMessage', { temperature }));
      } else if (condition === 'rainy' || condition === 'rain') {
        messages.push(t('contextual.weatherRainyMessage'));
      } else if (condition === 'cloudy' || condition === 'clouds') {
        messages.push(t('contextual.weatherCloudyMessage'));
      } else if (temperature > 25) {
        messages.push(t('contextual.weatherHotMessage'));
      } else if (temperature < 10) {
        messages.push(t('contextual.weatherColdMessage'));
      }
    }
    
    // Message basé sur la saison
    if (userContext.seasonData) {
      const seasonName = contextualRecommendationService.getSeasonName(userContext.seasonData.currentSeason);
      messages.push(t('contextual.seasonMessage', { season: seasonName }));
    }
    
    // Message basé sur l'heure
    if (userContext.localTime) {
      const timeOfDay = userContext.localTime.timeOfDay;
      
      if (timeOfDay === 'morning') {
        messages.push(t('contextual.timeMorningMessage'));
      } else if (timeOfDay === 'afternoon') {
        messages.push(t('contextual.timeAfternoonMessage'));
      } else if (timeOfDay === 'evening') {
        messages.push(t('contextual.timeEveningMessage'));
      } else if (timeOfDay === 'night') {
        messages.push(t('contextual.timeNightMessage'));
      }
    }
    
    // Message basé sur les événements locaux
    if (userContext.localEvents && userContext.localEvents.length > 0) {
      messages.push(t('contextual.eventsMessage', { count: userContext.localEvents.length }));
    }
    
    // Sélectionner un message aléatoire
    if (messages.length > 0) {
      return messages[Math.floor(Math.random() * messages.length)];
    }
    
    return '';
  };
  
  // Gérer le clic sur "Voir plus"
  const handleSeeMore = () => {
    navigate('/contextual');
  };
  
  // Afficher un indicateur de chargement
  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
        <p className="text-gray-600 mb-6">{description}</p>
        
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner />
        </div>
      </div>
    );
  }
  
  // Afficher un message d'erreur
  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
        <p className="text-gray-600 mb-6">{description}</p>
        
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // Afficher les recommandations
  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
        <p className="text-gray-600">{description}</p>
      </div>
      
      {userContext && (
        <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">{getContextualMessage()}</p>
            </div>
          </div>
        </div>
      )}
      
      {recommendations.length === 0 ? (
        <div className="text-center py-12">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">{t('contextual.noRecommendations')}</h3>
          <p className="text-gray-500 mb-6">{t('contextual.tryDifferentFilters')}</p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            {recommendations.map((recommendation) => (
              <RetreatCard
                key={recommendation.id}
                retreat={recommendation}
                isContextual={true}
                contextScore={recommendation.contextScore}
              />
            ))}
          </div>
          
          <div className="flex justify-center">
            <button
              onClick={handleSeeMore}
              className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
            >
              {t('contextual.seeMore')}
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default ContextualRecommendationSection;
