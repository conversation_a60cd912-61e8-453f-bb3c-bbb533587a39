import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowPathIcon } from '@heroicons/react/24/outline';
import ConversionService from '../../services/ConversionService';

interface ConversionRateProps {
  eurAmount: number;
}

const ConversionRate: React.FC<ConversionRateProps> = ({ eurAmount }) => {
  const [rates, setRates] = useState(ConversionService.getInstance().getCurrentRates());
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  const updateRates = async () => {
    try {
      setIsLoading(true);
      await ConversionService.getInstance().updateRates();
      setRates(ConversionService.getInstance().getCurrentRates());
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Erreur lors de la mise à jour des taux:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const interval = setInterval(updateRates, 30000); // Mise à jour toutes les 30 secondes
    return () => clearInterval(interval);
  }, []);

  const randBAmount = ConversionService.getInstance().convertEurToRandB(eurAmount);

  return (
    <div className='bg-white rounded-lg shadow-md p-6'>
      <div className='flex items-center justify-between mb-4'>
        <h3 className='text-lg font-semibold'>Taux de change en temps réel</h3>
        <button
          onClick={updateRates}
          disabled={isLoading}
          className='p-2 text-gray-600 hover:text-retreat-green transition-colors'
        >
          <ArrowPathIcon className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      <div className='space-y-4'>
        <div className='grid grid-cols-2 gap-4'>
          <div className='bg-gray-50 p-4 rounded-lg'>
            <p className='text-sm text-gray-600'>1 EUR =</p>
            <p className='text-xl font-bold text-gray-900'>{rates.eurToRandB.toFixed(6)} RandB</p>
          </div>
          <div className='bg-gray-50 p-4 rounded-lg'>
            <p className='text-sm text-gray-600'>1 RandB =</p>
            <p className='text-xl font-bold text-gray-900'>{rates.randBToEur.toFixed(2)} EUR</p>
          </div>
        </div>

        <div className='bg-gray-50 p-4 rounded-lg'>
          <p className='text-sm text-gray-600'>Montant en RandB</p>
          <p className='text-2xl font-bold text-gray-900'>{randBAmount.toFixed(4)} RandB</p>
        </div>

        <p className='text-xs text-gray-500 text-right'>
          Dernière mise à jour : {lastUpdate.toLocaleTimeString()}
        </p>
      </div>
    </div>
  );
};

export default ConversionRate;
