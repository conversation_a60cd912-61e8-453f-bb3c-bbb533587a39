import React from 'react';
import { motion } from 'framer-motion';
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/outline';

interface Transaction {
  id: string;
  type: 'deposit' | 'withdrawal';
  amount: number;
  date: Date;
  status: 'completed' | 'pending' | 'failed';
}

interface TransactionHistoryProps {
  transactions: Transaction[];
}

const TransactionHistory: React.FC<TransactionHistoryProps> = ({ transactions }) => {
  return (
    <div className='bg-white rounded-lg shadow-md p-6'>
      <h3 className='text-lg font-semibold mb-4'>Historique des transactions</h3>
      <div className='space-y-4'>
        {transactions.map((transaction) => (
          <motion.div
            key={transaction.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className='flex items-center justify-between p-4 bg-gray-50 rounded-lg'
          >
            <div className='flex items-center gap-3'>
              {transaction.type === 'deposit' ? (
                <ArrowDownIcon className='w-5 h-5 text-green-500' />
              ) : (
                <ArrowUpIcon className='w-5 h-5 text-red-500' />
              )}
              <div>
                <p className='font-medium'>
                  {transaction.type === 'deposit' ? 'Dépôt' : 'Retrait'}
                </p>
                <p className='text-sm text-gray-600'>{transaction.date.toLocaleDateString()}</p>
              </div>
            </div>
            <div className='text-right'>
              <p
                className={`font-semibold ${
                  transaction.type === 'deposit' ? 'text-green-600' : 'text-red-600'
                }`}
              >
                {transaction.type === 'deposit' ? '+' : '-'}
                {transaction.amount.toFixed(4)} RandB
              </p>
              <p
                className={`text-sm ${
                  transaction.status === 'completed'
                    ? 'text-green-600'
                    : transaction.status === 'pending'
                      ? 'text-yellow-600'
                      : 'text-red-600'
                }`}
              >
                {transaction.status === 'completed'
                  ? 'Complété'
                  : transaction.status === 'pending'
                    ? 'En attente'
                    : 'Échoué'}
              </p>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default TransactionHistory;
