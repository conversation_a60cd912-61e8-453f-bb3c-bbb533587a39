import React, { useState, useRef, useEffect } from 'react';
import { Range } from 'react-range';
import { Link } from 'react-router-dom';
import {
  UserCircleIcon,
  ShoppingBagIcon,
  ArrowPathIcon,
  Bars3Icon,
  XMarkIcon,
  MagnifyingGlassIcon,
  MapPinIcon,
  CalendarIcon,
  CurrencyEuroIcon,
  HomeIcon,
  UsersIcon,
  PhoneXMarkIcon,
  TagIcon,
  HeartIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline';
import MessageBadge from '../../messaging/MessageBadge';
import { motion, AnimatePresence } from 'framer-motion';
import DatePicker, { registerLocale } from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { fr } from 'date-fns/locale';
import type { IRenderTrackParams, IThumbProps } from 'react-range/lib/types';
import { ArrowRightIcon } from '@heroicons/react/24/solid';
import { useAuth } from '../../../contexts/AuthContext';

registerLocale('fr', fr);

interface MenuItem {
  label: string;
  href: string;
  subItems?: MenuItem[];
}

const menuItems: MenuItem[] = [
  { label: 'Aide', href: '/aide' },
  { label: 'Communauté', href: '/communaute' },
  { label: 'Devenir Partenaire', href: '/become-partner' },
  {
    label: 'Blog',
    href: '/blog',
    subItems: [
      {
        label: 'Pratiques & Activités',
        href: '/blog/pratiques',
        subItems: [
          { label: 'Yoga & Méditation', href: '/blog/pratiques/yoga-meditation' },
          { label: 'Bien-être & Spa', href: '/blog/pratiques/bien-etre-spa' },
          { label: 'Développement personnel', href: '/blog/pratiques/developpement-personnel' },
          { label: 'Nutrition & Détox', href: '/blog/pratiques/nutrition-detox' },
          { label: 'Art-thérapie', href: '/blog/pratiques/art-therapie' },
        ],
      },
      {
        label: 'Destinations',
        href: '/blog/destinations',
        subItems: [
          { label: 'France', href: '/blog/destinations/france' },
          { label: 'Europe', href: '/blog/destinations/europe' },
          { label: 'Asie', href: '/blog/destinations/asie' },
          { label: 'Amériques', href: '/blog/destinations/ameriques' },
          { label: 'Afrique', href: '/blog/destinations/afrique' },
        ],
      },
      {
        label: 'Thématiques',
        href: '/blog/thematiques',
        subItems: [
          { label: 'Retraites en solo', href: '/blog/thematiques/solo' },
          { label: 'Séjours en couple', href: '/blog/thematiques/couple' },
          { label: 'Retraites spirituelles', href: '/blog/thematiques/spirituel' },
          { label: 'Digital Detox', href: '/blog/thematiques/digital-detox' },
          { label: 'Eco-responsable', href: '/blog/thematiques/eco-responsable' },
        ],
      },
      {
        label: 'Conseils & Guides',
        href: '/blog/guides',
        subItems: [
          { label: 'Préparer sa retraite', href: '/blog/guides/preparation' },
          { label: 'Choisir sa destination', href: '/blog/guides/choix-destination' },
          { label: 'Budget & Financement', href: '/blog/guides/budget' },
          { label: 'Témoignages', href: '/blog/guides/temoignages' },
          { label: 'FAQ', href: '/blog/guides/faq' },
        ],
      },
      {
        label: 'Actualités',
        href: '/blog/actualites',
        subItems: [
          { label: 'Nouveaux lieux', href: '/blog/actualites/nouveaux-lieux' },
          { label: 'Tendances bien-être', href: '/blog/actualites/tendances' },
          { label: 'Interviews', href: '/blog/actualites/interviews' },
          { label: 'Événements', href: '/blog/actualites/evenements' },
        ],
      },
    ],
  },
  {
    label: 'Espace PRO',
    href: '/professional',
    subItems: [
      { label: 'Devenir hôte', href: '/professional/become-host' },
      { label: 'Dashboard', href: '/professional/dashboard' },
      { label: 'Ressources', href: '/professional/resources' },
    ],
  },
];

const NavbarClient: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [selectedDates, setSelectedDates] = useState<[Date | null, Date | null]>([null, null]);
  const [selectedFilters, setSelectedFilters] = useState<Record<string, string>>({});
  const [priceRange, setPriceRange] = useState([500, 2000]);
  const [flexibleDates, setFlexibleDates] = useState(false);
  const [selectedThemes, setSelectedThemes] = useState<string[]>([]);
  const [groupSize, setGroupSize] = useState(1);
  const [adults, setAdults] = useState(1);
  const [children, setChildren] = useState(0);
  const [selectedDietaryRestrictions, setSelectedDietaryRestrictions] = useState<string[]>([]);
  const [selectedWellnessPreferences, setSelectedWellnessPreferences] = useState<string[]>([]);
  const [isGroup, setIsGroup] = useState(false);
  const searchPanelRef = useRef<HTMLDivElement>(null);
  const searchButtonRef = useRef<HTMLButtonElement>(null);

  const { isAuthenticated, user, logout } = useAuth();

  const themes = [
    { id: 'yoga', label: 'Yoga' },
    { id: 'meditation', label: 'Méditation' },
    { id: 'wellness', label: 'Bien-être' },
    { id: 'detox', label: 'Detox' },
    { id: 'spiritual', label: 'Spirituel' },
  ];

  const popularPresets = [
    {
      id: 'family-wellness',
      label: 'Séjour Famille & Bien-être',
      icon: HomeIcon,
      filters: {
        themes: ['yoga', 'wellness'],
        adults: 2,
        children: 2,
        dietaryRestrictions: ['family-friendly'],
      },
    },
    {
      id: 'detox-retreat',
      label: 'Cure Détox Premium',
      icon: SparklesIcon,
      filters: {
        themes: ['detox', 'wellness'],
        dietaryRestrictions: ['gluten-free', 'vegan'],
      },
    },
    {
      id: 'digital-detox',
      label: 'Digital Detox Nature',
      icon: PhoneXMarkIcon,
      filters: {
        themes: ['meditation', 'nature'],
        wellnessPreferences: ['silence'],
      },
    },
  ];

  const dietaryOptions = [
    { id: 'gluten-free', label: 'Sans Gluten' },
    { id: 'lactose-free', label: 'Sans Lactose' },
    { id: 'vegetarian', label: 'Végétarien' },
    { id: 'vegan', label: 'Végétalien' },
    { id: 'raw', label: 'Alimentation Vivante' },
    { id: 'fasting', label: 'Jeûne Intermittent' },
  ];

  const wellnessPreferences = [
    { id: 'massage', label: 'Massages' },
    { id: 'spa', label: 'Spa & Balnéo' },
    { id: 'ayurveda', label: 'Soins Ayurvédiques' },
    { id: 'sound-therapy', label: 'Thérapie Sonore' },
    { id: 'forest-therapy', label: 'Sylvothérapie' },
    { id: 'aromatherapy', label: 'Aromathérapie' },
  ];

  // Mettre à jour les options de logement
  const accommodationTypes = [
    { id: 'single-room', label: 'Chambre simple' },
    { id: 'double-room', label: 'Chambre double' },
    { id: 'suite', label: 'Suite' },
    { id: 'private-villa', label: 'Villa privée' },
    { id: 'bungalow', label: 'Bungalow' },
    { id: 'shared-dorm', label: 'Dortoir partagé' },
    { id: 'eco-lodge', label: 'Éco-lodge' },
  ];

  // Dans le composant, ajouter l'état
  const [selectedAccommodation, setSelectedAccommodation] = useState<string[]>([]);

  // Gestionnaire pour le panneau de recherche
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isSearchExpanded &&
        searchPanelRef.current &&
        searchButtonRef.current &&
        !searchPanelRef.current.contains(event.target as Node) &&
        !searchButtonRef.current.contains(event.target as Node)
      ) {
        setIsSearchExpanded(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isSearchExpanded]);

  const filterOptions = {
    destinations: [
      { label: 'Toutes les destinations', value: '' },
      { label: '── CONTINENTS ──', value: 'continents-separator', disabled: true },
      { label: 'Europe', value: 'europe' },
      { label: 'Asie', value: 'asie' },
      { label: 'Amériques', value: 'ameriques' },
      { label: 'Afrique', value: 'afrique' },
      { label: '── PAYS ──', value: 'countries-separator', disabled: true },
      { label: 'France', value: 'france' },
      { label: 'Bali', value: 'bali' },
      { label: 'Thaïlande', value: 'thailand' },
      { label: 'Portugal', value: 'portugal' },
      { label: 'Espagne', value: 'spain' },
    ],
    themes: [
      { label: 'Yoga', value: 'yoga' },
      { label: 'Méditation', value: 'meditation' },
      { label: 'Bien-être', value: 'wellness' },
      { label: 'Detox', value: 'detox' },
      { label: 'Spirituel', value: 'spiritual' },
    ],
    experiences: [
      { label: 'Débutant', value: 'beginner' },
      { label: 'Intermédiaire', value: 'intermediate' },
      { label: 'Avancé', value: 'advanced' },
    ],
    prices: [
      { label: '< 500€', value: '0-500' },
      { label: '500€ - 1000€', value: '500-1000' },
      { label: '1000€ - 2000€', value: '1000-2000' },
      { label: '> 2000€', value: '2000+' },
    ],
  };

  const toggleMenu = () => setIsOpen(!isOpen);
  const toggleSubmenu = (label: string) => {
    setActiveSubmenu(activeSubmenu === label ? null : label);
  };

  const handleGroupToggle = (checked: boolean) => {
    setIsGroup(checked);
    if (checked) {
      // Reset individual values when switching to group mode
      setAdults(1);
      setChildren(0);
    } else {
      // Reset group value when switching to individual mode
      setGroupSize(5);
    }
  };

  // Styles personnalisés pour le calendrier
  const customDatePickerStyles = `
    .react-datepicker {
      font-family: inherit;
      border-radius: 0.5rem;
      border: 1px solid #e5e7eb;
    }

    .react-datepicker__header {
      background-color: white;
      border-bottom: 1px solid #e5e7eb;
      border-top-left-radius: 0.5rem;
      border-top-right-radius: 0.5rem;
      padding-top: 0.5rem;
    }

    .react-datepicker__month-container {
      float: left;
      background-color: white;
    }

    .react-datepicker__day--selected,
    .react-datepicker__day--in-selecting-range,
    .react-datepicker__day--in-range {
      background-color: #4F7942;
      color: white;
    }

    .react-datepicker__day--selected:hover,
    .react-datepicker__day--in-selecting-range:hover,
    .react-datepicker__day--in-range:hover {
      background-color: #3D5A33;
    }

    .react-datepicker__day:hover {
      background-color: #f3f4f6;
    }

    .react-datepicker__day--keyboard-selected {
      background-color: #4F7942;
      color: white;
    }

    .react-datepicker__day--today {
      font-weight: bold;
    }

    .react-datepicker__navigation {
      top: 0.5rem;
    }

    .react-datepicker__navigation--previous {
      left: 0.5rem;
    }

    .react-datepicker__navigation--next {
      right: 0.5rem;
    }
  `;

  // Ajouter les styles personnalisés au composant
  const StyleWrapper = () => <style>{customDatePickerStyles}</style>;

  return (
    <>
      <StyleWrapper />
      <nav className='bg-white shadow-sm sticky top-0 z-50'>
        <div className='max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex items-center justify-between h-20'>
            <div className='flex-shrink-0'>
              <Link to='/' className='flex items-center space-x-2'>
                <img className='h-10 w-auto' src='/logo.png' alt='Retreat and Be' />
                <span className='text-2xl font-bold text-gray-800'>Retreat & Be</span>
              </Link>
            </div>

            <div className='hidden lg:flex items-center space-x-6'>
              <Link to='/aide' className='text-gray-600 hover:text-indigo-600 transition-colors'>Aide</Link>

              {isAuthenticated ? (
                <>
                  <Link to='/compte' className='flex items-center text-gray-600 hover:text-indigo-600 transition-colors'>
                    <UserCircleIcon className='h-7 w-7 mr-1' />
                    Mon Compte ({user?.firstName || user?.email})
                  </Link>
                  <button 
                    onClick={logout} 
                    className='text-gray-600 hover:text-indigo-600 transition-colors px-3 py-2 rounded-md text-sm font-medium'
                  >
                    Déconnexion
                  </button>
                </>
              ) : (
                <>
                  <Link to='/login' className='text-gray-600 hover:text-indigo-600 transition-colors px-3 py-2 rounded-md text-sm font-medium'>
                    Connexion
                  </Link>
                  <Link to='/register' className='text-white bg-indigo-600 hover:bg-indigo-700 transition-colors px-4 py-2 rounded-md text-sm font-medium'>
                    Inscription
                  </Link>
                </>
              )}
              <MessageBadge />
            </div>

            <div className='lg:hidden flex items-center'>
              <button
                onClick={toggleMenu}
                className='text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 p-2 rounded-md'
              >
                <span className='sr-only'>Ouvrir le menu principal</span>
                {isOpen ? (
                  <XMarkIcon className='block h-7 w-7' aria-hidden='true' />
                ) : (
                  <Bars3Icon className='block h-7 w-7' aria-hidden='true' />
                )}
              </button>
            </div>
          </div>
        </div>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className='nav:hidden'
            >
              <div className='px-2 pt-2 pb-3 space-y-1 sm:px-3'>
                <button
                  type='button'
                  onClick={() => {
                    setIsSearchExpanded(true);
                    toggleMenu();
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      setIsSearchExpanded(true);
                      toggleMenu();
                    }
                  }}
                  className='block w-full px-3 py-2 text-base font-medium text-gray-600 hover:text-retreat-green hover:bg-gray-100 rounded-md cursor-pointer text-left'
                >
                  <div className='flex items-center space-x-2'>
                    <MagnifyingGlassIcon className='w-5 h-5 text-retreat-green' />
                    <span>Trouver sa retraite</span>
                  </div>
                </button>

                {menuItems.map((item) => (
                  <div key={item.label}>
                    {item.subItems ? (
                      <>
                        <button
                          className='w-full flex items-center justify-between px-3 py-2 text-base font-medium text-gray-600 hover:text-retreat-green hover:bg-gray-100 rounded-md'
                          onClick={() => toggleSubmenu(item.label)}
                        >
                          {item.label}
                          <svg
                            className={`w-4 h-4 transform transition-transform duration-200 ${
                              activeSubmenu === item.label ? 'rotate-180' : ''
                            }`}
                            viewBox='0 0 20 20'
                            fill='currentColor'
                          >
                            <path
                              fillRule='evenodd'
                              d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z'
                              clipRule='evenodd'
                            />
                          </svg>
                        </button>
                        <AnimatePresence>
                          {activeSubmenu === item.label && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              className='pl-4'
                            >
                              {item.subItems.map((subItem) => (
                                <Link
                                  key={subItem.label}
                                  to={subItem.href}
                                  className='block px-3 py-2 text-base font-medium text-gray-500 hover:text-retreat-green hover:bg-gray-100 rounded-md'
                                  onClick={toggleMenu}
                                >
                                  {subItem.label}
                                </Link>
                              ))}
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </>
                    ) : (
                      <Link
                        to={item.href}
                        className='block px-3 py-2 text-base font-medium text-gray-600 hover:text-retreat-green hover:bg-gray-100 rounded-md'
                        onClick={toggleMenu}
                      >
                        {item.label}
                      </Link>
                    )}
                  </div>
                ))}
                <div className='flex justify-center space-x-4 pt-4'>
                  <Link to='/panier' className='text-gray-600 hover:text-retreat-green'>
                    <ShoppingBagIcon className='h-6 w-6' />
                  </Link>
                  <MessageBadge />
                  <Link to='/auth' className='text-gray-600 hover:text-retreat-green'>
                    <UserCircleIcon className='h-6 w-6' />
                  </Link>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <AnimatePresence>
          {isSearchExpanded && (
            <motion.div
              ref={searchPanelRef}
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className='border-t border-gray-100 bg-white'
            >
              <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
                <div className='flex justify-between items-center mb-6'>
                  <h2 className='text-2xl font-semibold text-gray-800'>Trouver sa retraite</h2>
                  <button
                    onClick={() => setIsSearchExpanded(false)}
                    className='p-2 hover:bg-gray-100 rounded-full'
                  >
                    <XMarkIcon className='w-6 h-6 text-gray-500' />
                  </button>
                </div>

                <div className='mb-8'>
                  <h3 className='text-sm font-medium text-gray-700 mb-3'>Suggestions populaires</h3>
                  <div className='flex gap-4 overflow-x-auto pb-2'>
                    {popularPresets.map((preset) => (
                      <button
                        key={preset.id}
                        onClick={() => {
                          if (preset.filters.themes) setSelectedThemes(preset.filters.themes);
                          if (preset.filters.adults) setAdults(preset.filters.adults);
                          if (preset.filters.children) setChildren(preset.filters.children);
                          if (preset.filters.dietaryRestrictions)
                            setSelectedDietaryRestrictions(preset.filters.dietaryRestrictions);
                        }}
                        className='flex items-center space-x-2 px-4 py-2 rounded-full border border-gray-200 hover:border-retreat-green hover:bg-retreat-green/10 transition-all duration-200'
                      >
                        <preset.icon className='w-5 h-5 text-retreat-green' />
                        <span className='whitespace-nowrap'>{preset.label}</span>
                      </button>
                    ))}
                  </div>
                </div>

                <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
                  <div className='space-y-2'>
                    <h3 className='flex items-center text-sm font-medium text-retreat-green-700'>
                      <MapPinIcon className='w-4 h-4 mr-2 text-retreat-green' />
                      Destination
                    </h3>
                    <div className='relative'>
                      <select
                        className='w-full rounded-lg border-gray-200 text-gray-900 py-2.5 pl-3 pr-10 text-sm focus:border-[hsl(var(--retreat-green))] focus:ring focus:ring-[hsl(var(--retreat-green))/20] py-2.5 pl-3 pr-10 text-sm] transition-colors duration-200'
                        onChange={(e) =>
                          setSelectedFilters({ ...selectedFilters, destination: e.target.value })
                        }
                      >
                        {filterOptions.destinations.map((option) => (
                          <option
                            className='bg-[hsl(var(--retreat-green-dark))] text-white'
                            key={option.value}
                            value={option.value}
                            disabled={option.disabled}
                          >
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className='space-y-4'>
                    <h3 className='flex items-center text-sm font-medium text-retreat-retreat-green-700 '>
                      <CurrencyEuroIcon className='w-4 h-4 mr-2 text-retreat-green' />
                      Budget (par personne)
                    </h3>
                    <div className='px-2'>
                      <Range
                        values={priceRange}
                        step={100}
                        min={0}
                        max={5000}
                        onChange={(values: number[]) => setPriceRange(values)}
                        renderTrack={({ props, children }: IRenderTrackParams) => (
                          <div {...props} className='h-2 w-full bg-gray-200 rounded-full'>
                            <div
                              className='h-2 bg-retreat-green rounded-full'
                              style={{
                                width: `${((priceRange[1] - priceRange[0]) / 5000) * 100}%`,
                                left: `${(priceRange[0] / 5000) * 100}%`,
                                position: 'absolute',
                              }}
                            />
                            {children}
                          </div>
                        )}
                        renderThumb={({ props }: { props: IThumbProps }) => (
                          <div
                            {...props}
                            className='h-4 w-4 bg-white rounded-full shadow-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-retreat-green'
                          />
                        )}
                      />
                      <div className='flex justify-between mt-2 text-sm text-gray-600'>
                        <span>{priceRange[0]}€</span>
                        <span>{priceRange[1]}€</span>
                      </div>
                    </div>
                  </div>

                  <div className='space-y-2'>
                    <h3 className='flex items-center text-sm font-medium text-retreat-green-700'>
                      <CalendarIcon className='w-4 h-4 mr-2 text-retreat-green' />
                      Dates
                    </h3>
                    <div className='relative'>
                      <DatePicker
                        selectsRange={true}
                        startDate={selectedDates[0]}
                        endDate={selectedDates[1]}
                        onChange={(dates) => setSelectedDates(dates)}
                        locale='fr'
                        dateFormat='dd MMMM yyyy'
                        minDate={new Date()}
                        maxDate={new Date(new Date().setFullYear(new Date().getFullYear() + 1))}
                        placeholderText='Sélectionnez vos dates'
                        className='w-auto rounded-lg border-gray-200 focus:border-retreat-green focus:ring focus:ring-retreat-green/20 py-2.5 pr-16 pl-3 text-sm'
                        isClearable
                        showPopperArrow={false}
                        monthsShown={1}
                        calendarClassName='shadow-lg rounded-lg border border-gray-200'
                        popperClassName='z-50 w-auto'
                        popperPlacement='bottom-start'
                      />
                      <div className='absolute right-0 top-1/2 -translate-y-1/2 flex items-center space-x-1 pr-3'>
                        <CalendarIcon className='w-5 h-5 text-gray-400 pointer-events-none' />
                      </div>
                    </div>
                    <div className='flex items-center space-x-2 mt-2'>
                      <input
                        type='checkbox'
                        id='flexibleDates'
                        checked={flexibleDates}
                        onChange={(e) => setFlexibleDates(e.target.checked)}
                        className='rounded border-gray-300 text-retreat-green focus:ring-retreat-green'
                      />
                      <label htmlFor='flexibleDates' className='text-sm text-retreat-green-600'>
                        Dates flexibles (±3 jours)
                      </label>
                    </div>
                    {flexibleDates && (
                      <div className='text-xs text-gray-500 italic bg-gray-50 p-2 rounded'>
                        Nous vous montrerons aussi les retraites disponibles 3 jours avant et après
                        les dates sélectionnées
                      </div>
                    )}
                  </div>

                  <div className='space-y-2'>
                    <h3 className='flex items-center text-sm font-medium text-retreat-green-700'>
                      <HomeIcon className='w-4 h-4 mr-2 text-retreat-green' />
                      Type de logement
                    </h3>
                    <div className='flex flex-wrap gap-2'>
                      {accommodationTypes.map((accommodation) => (
                        <button
                          key={accommodation.id}
                          onClick={() => {
                            setSelectedAccommodation(
                              selectedAccommodation.includes(accommodation.id)
                                ? selectedAccommodation.filter((id) => id !== accommodation.id)
                                : [...selectedAccommodation, accommodation.id]
                            );
                          }}
                          className={`px-3 py-1.5 rounded-full text-sm transition-colors duration-200 ${
                            selectedAccommodation.includes(accommodation.id)
                              ? 'bg-retreat-green text-white'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                        >
                          {accommodation.label}
                        </button>
                      ))}
                    </div>
                  </div>

                  <div className='space-y-2'>
                    <h3 className='flex items-center text-sm font-medium text-retreat-green-700'>
                      <TagIcon className='w-4 h-4 mr-2 text-retreat-green' />
                      Thèmes
                    </h3>
                    <div className='flex flex-wrap gap-2'>
                      {themes.map((theme) => (
                        <button
                          key={theme.id}
                          onClick={() => {
                            setSelectedThemes(
                              selectedThemes.includes(theme.id)
                                ? selectedThemes.filter((t) => t !== theme.id)
                                : [...selectedThemes, theme.id]
                            );
                          }}
                          className={`px-3 py-1.5 rounded-full text-sm transition-colors duration-200 ${
                            selectedThemes.includes(theme.id)
                              ? 'bg-retreat-green text-white'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                        >
                          {theme.label}
                        </button>
                      ))}
                    </div>
                  </div>

                  <div className='space-y-2'>
                    <h3 className='flex items-center text-sm font-medium text-retreat-green-700'>
                      <UsersIcon className='w-4 h-4 mr-2 text-retreat-green' />
                      Voyageurs
                    </h3>
                    <div className='flex flex-col space-y-4'>
                      <div className='space-y-2'>
                        <div className='flex items-center space-x-2'>
                          <input
                            type='checkbox'
                            id='isGroup'
                            checked={isGroup}
                            onChange={(e) => handleGroupToggle(e.target.checked)}
                            className='rounded border-gray-300 text-[hsl(var(--retreat-green-dark))] focus:ring-[hsl(var(--retreat-green-dark))]'
                          />
                          <label htmlFor='isGroup' className='text-sm text-retreat-green-600'>
                            Réservation de groupe
                          </label>
                        </div>

                        {isGroup && (
                          <div className='flex items-center justify-between pl-6'>
                            <span className='text-sm text-gray-600'>Participants</span>
                            <div className='flex items-center space-x-3'>
                              <button
                                onClick={() => setGroupSize(Math.max(5, groupSize - 1))}
                                className='w-8 h-8 rounded-full border border-gray-200 flex items-center justify-center hover:border-[hsl(var(--retreat-green))] hover:bg-[hsl(var(--retreat-green))] hover:text-white active:bg-[hsl(var(--green-700))] transition-all duration-200'
                              >
                                -
                              </button>
                              <span className='w-8 text-center'>{groupSize}</span>
                              <button
                                onClick={() => setGroupSize(groupSize + 1)}
                                className='w-8 h-8 rounded-full border border-gray-200 flex items-center justify-center hover:border-[hsl(var(--retreat-green))] hover:bg-[hsl(var(--retreat-green))] hover:text-white active:bg-[hsl(var(--green-700))] transition-all duration-200'
                              >
                                +
                              </button>
                            </div>
                          </div>
                        )}
                      </div>

                      {isGroup && <div className='border-t border-gray-200' />}

                      <div
                        className={`flex flex-col space-y-3 transition-opacity duration-200 ${isGroup ? 'opacity-40 pointer-events-none' : ''}`}
                      >
                        <div className='flex items-center justify-between'>
                          <span className='text-sm text-gray-600'>Adultes</span>
                          <div className='flex items-center space-x-3'>
                            <button
                              disabled={isGroup}
                              onClick={() => setAdults(Math.max(1, adults - 1))}
                              className='w-8 h-8 rounded-full border border-gray-200 flex items-center justify-center hover:border-[hsl(var(--retreat-green))] hover:bg-[hsl(var(--retreat-green))] hover:text-white active:bg-[hsl(var(--green-700))] transition-all duration-200 disabled:cursor-not-allowed'
                            >
                              -
                            </button>
                            <span className='w-8 text-center'>{adults}</span>
                            <button
                              disabled={isGroup}
                              onClick={() => setAdults(adults + 1)}
                              className='w-8 h-8 rounded-full border border-gray-200 flex items-center justify-center hover:border-[hsl(var(--retreat-green))] hover:bg-[hsl(var(--retreat-green))] hover:text-white active:bg-[hsl(var(--green-700))] transition-all duration-200 disabled:cursor-not-allowed'
                            >
                              +
                            </button>
                          </div>
                        </div>
                        <div className='flex items-center justify-between'>
                          <span className='text-sm text-gray-600'>Enfants</span>
                          <div className='flex items-center space-x-3'>
                            <button
                              disabled={isGroup}
                              onClick={() => setChildren(Math.max(0, children - 1))}
                              className='w-8 h-8 rounded-full border border-gray-200 flex items-center justify-center hover:border-[hsl(var(--retreat-green))] hover:bg-[hsl(var(--retreat-green))] hover:text-white active:bg-[hsl(var(--green-700))] transition-all duration-200 disabled:cursor-not-allowed'
                            >
                              -
                            </button>
                            <span className='w-8 text-center'>{children}</span>
                            <button
                              disabled={isGroup}
                              onClick={() => setChildren(children + 1)}
                              className='w-8 h-8 rounded-full border border-gray-200 flex items-center justify-center hover:border-[hsl(var(--retreat-green))] hover:bg-[hsl(var(--retreat-green))] hover:text-white active:bg-[hsl(var(--green-700))] transition-all duration-200 disabled:cursor-not-allowed'
                            >
                              +
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className='space-y-2'>
                    <h3 className='flex items-center text-sm font-medium text-retreat-green-700'>
                      <SparklesIcon className='w-4 h-4 mr-2 text-retreat-green' />
                      Régimes alimentaires
                    </h3>
                    <div className='flex flex-wrap gap-2'>
                      {dietaryOptions.map((option) => (
                        <button
                          key={option.id}
                          onClick={() => {
                            setSelectedDietaryRestrictions(
                              selectedDietaryRestrictions.includes(option.id)
                                ? selectedDietaryRestrictions.filter((id) => id !== option.id)
                                : [...selectedDietaryRestrictions, option.id]
                            );
                          }}
                          className={`px-3 py-1.5 rounded-full text-sm transition-colors duration-200 ${
                            selectedDietaryRestrictions.includes(option.id)
                              ? 'bg-retreat-green text-white'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                        >
                          {option.label}
                        </button>
                      ))}
                    </div>
                  </div>

                  <div className='space-y-2'>
                    <h3 className='flex items-center text-sm font-medium text-retreat-green-700'>
                      <HeartIcon className='w-4 h-4 mr-2 text-retreat-green' />
                      Préférences bien-être
                    </h3>
                    <div className='flex flex-wrap gap-2'>
                      {wellnessPreferences.map((pref) => (
                        <button
                          key={pref.id}
                          onClick={() => {
                            setSelectedWellnessPreferences(
                              selectedWellnessPreferences.includes(pref.id)
                                ? selectedWellnessPreferences.filter((id) => id !== pref.id)
                                : [...selectedWellnessPreferences, pref.id]
                            );
                          }}
                          className={`px-3 py-1.5 rounded-full text-sm transition-colors duration-200 ${
                            selectedWellnessPreferences.includes(pref.id)
                              ? 'bg-retreat-green text-white'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                        >
                          {pref.label}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                <div className='mt-8 flex justify-end space-x-4'>
                  <button
                    onClick={() => {
                      setSelectedFilters({});
                      setSelectedDates([null, null]);
                      setPriceRange([500, 2000]);
                      setSelectedThemes([]);
                      setAdults(1);
                      setChildren(0);
                      setSelectedDietaryRestrictions([]);
                      setSelectedWellnessPreferences([]);
                      setFlexibleDates(false);
                    }}
                    className='flex items-center space-x-2 px-4 py-2 rounded-full border border-gray-200 hover:border-[hsl(var(--retreat-green))] hover:bg-retreat-green/10 transition-all duration-200'
                  >
                    <ArrowPathIcon className='w-5 h-5 text-[hsl(var(--retreat-green))]' />
                    <span>Réinitialiser</span>
                  </button>
                  <button
                    onClick={() => {
                      console.log('Recherche avec les filtres:', {
                        dates: selectedDates,
                        flexibleDates,
                        priceRange,
                        themes: selectedThemes,
                        adults,
                        children,
                        dietaryRestrictions: selectedDietaryRestrictions,
                        wellnessPreferences: selectedWellnessPreferences,
                      });
                    }}
                    className='px-8 py-2.5 bg-retreat-green text-white rounded-lg hover:bg-retreat-green/90 transition-colors duration-200 flex items-center space-x-2'
                  >
                    <span>Rechercher</span>
                    <ArrowRightIcon className='w-4 h-4' />
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </nav>
    </>
  );
};

export default NavbarClient;
