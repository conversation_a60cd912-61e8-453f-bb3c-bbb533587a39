import React from 'react';
import { Link } from 'react-router-dom';
import Icon from '../../atoms/Icon/Icon';
import {
  ChatBubbleLeftRightIcon,
  PhotoIcon,
  ChatBubbleOvalLeftIcon,
  PlayCircleIcon,
} from '@heroicons/react/24/outline';

interface FooterLink {
  label: string;
  href: string;
  subLinks?: FooterLink[];
}

interface FooterSection {
  title: string;
  links: FooterLink[];
}

const footerSections: FooterSection[] = [
  {
    title: 'Découvrir',
    links: [
      { label: 'Retraites', href: '/retreats' },
      { label: 'Destinations', href: '/destinations' },
      { label: 'Pratiques', href: '/practices' },
      { label: 'Blog', href: '/blog' },
      { label: 'Témoignages', href: '/testimonials' },
    ],
  },
  {
    title: 'À propos',
    links: [
      { label: 'Notre mission', href: '/about/mission' },
      { label: 'Notre équipe', href: '/about/team' },
      { label: 'Carrières', href: '/about/careers' },
      { label: 'Presse', href: '/about/press' },
      { label: 'Contact', href: '/about/contact' },
    ],
  },
  {
    title: 'Support',
    links: [
      { label: "Centre d'aide", href: '/help' },
      { label: 'FAQ', href: '/help/faq' },
      { label: 'Confidentialité', href: '/privacy' },
      { label: 'Conditions', href: '/terms' },
      { label: 'Cookies', href: '/cookies' },
    ],
  },
  {
    title: 'Professionnels',
    links: [
      { label: 'Devenir hôte', href: '/professional/become-host' },
      { label: 'Ressources', href: '/professional/resources' },
      { label: 'Tarifs', href: '/professional/pricing' },
      { label: 'Sécurité', href: '/professional/safety' },
      { label: 'API', href: '/professional/api' },
    ],
  },
];

const socialLinks = [
  { icon: ChatBubbleLeftRightIcon, href: 'https://facebook.com', label: 'Facebook' },
  { icon: PhotoIcon, href: 'https://instagram.com', label: 'Instagram' },
  { icon: ChatBubbleOvalLeftIcon, href: 'https://twitter.com', label: 'Twitter' },
  { icon: PlayCircleIcon, href: 'https://youtube.com', label: 'YouTube' },
];

const Footer: React.FC = () => {
  return (
    <footer className='bg-gray-50 border-t'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12'>
        <div className='grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-8'>
          {/* Logo et description */}
          <div className='col-span-2 lg:col-span-1'>
            <Link to='/' className='inline-block'>
              <img src='/logo.svg' alt='Retreat & Be' className='h-8 w-auto' />
            </Link>
            <p className='mt-4 text-sm text-gray-600'>
              Découvrez des retraites bien-être authentiques et transformatrices à travers le monde.
            </p>
            {/* Réseaux sociaux */}
            <div className='mt-6 flex space-x-4'>
              {socialLinks.map((social) => (
                <a
                  key={social.label}
                  href={social.href}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='text-gray-400 hover:text-gray-500'
                  aria-label={social.label}
                >
                  <Icon icon={social.icon} size='sm' />
                </a>
              ))}
            </div>
          </div>

          {/* Sections de liens */}
          {footerSections.map((section) => (
            <div key={section.title}>
              <h3 className='text-sm font-semibold text-gray-900 tracking-wider uppercase'>
                {section.title}
              </h3>
              <ul className='mt-4 space-y-3'>
                {section.links.map((link) => (
                  <li key={link.label}>
                    <Link
                      to={link.href}
                      className='text-sm text-gray-600 hover:text-retreat-green transition-colors duration-200'
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Copyright */}
        <div className='mt-12 pt-8 border-t border-gray-200'>
          <p className='text-sm text-gray-500 text-center'>
            © {new Date().getFullYear()} Retreat & Be. Tous droits réservés.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
