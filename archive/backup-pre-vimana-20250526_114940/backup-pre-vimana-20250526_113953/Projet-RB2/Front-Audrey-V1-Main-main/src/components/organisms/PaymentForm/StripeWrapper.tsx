import React from 'react';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe, StripeElementsOptions } from '@stripe/stripe-js';
import PaymentForm from './PaymentForm';

// Load Stripe outside of a component's render to avoid recreating the Stripe object on every render
// Replace with your own publishable key from Stripe Dashboard
const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || '');

interface StripeWrapperProps {
  bookingId: string;
  amount: number;
  currency: string;
  onSuccess: (paymentId: string) => void;
  onCancel: () => void;
  className?: string;
}

const StripeWrapper: React.FC<StripeWrapperProps> = ({
  bookingId,
  amount,
  currency,
  onSuccess,
  onCancel,
  className,
}) => {
  const options: StripeElementsOptions = {
    // Passing the client secret obtained from the server
    // clientSecret should be dynamically set, not an empty string here.
    // This will be an issue later if not already handled by clientSecret being updated before payment attempt.
    clientSecret: '', // Placeholder, needs to be updated dynamically
    appearance: {
      theme: 'stripe',
      variables: {
        colorPrimary: '#4F7942', // Retreat green color
        colorBackground: '#ffffff',
        colorText: '#30313d',
        colorDanger: '#df1b41',
        fontFamily: 'Poppins, system-ui, sans-serif',
        spacingUnit: '4px',
        borderRadius: '4px',
      },
    },
  };

  return (
    <Elements stripe={stripePromise} options={options}>
      <PaymentForm
        bookingId={bookingId}
        amount={amount}
        currency={currency}
        onSuccess={onSuccess}
        onCancel={onCancel}
        className={className}
      />
    </Elements>
  );
};

export default StripeWrapper;
