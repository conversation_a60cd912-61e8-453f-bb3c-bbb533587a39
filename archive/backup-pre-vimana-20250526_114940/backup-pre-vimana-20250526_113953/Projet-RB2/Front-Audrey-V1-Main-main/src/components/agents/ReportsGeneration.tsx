/**
 * Génération de Rapports
 * Interface pour créer et gérer les rapports de performance
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  Grid,
  Badge,
  Input,
  Select,
  Modal,
  ModalContent,
  ModalFooter,
  Progress
} from '../ui/design-system';
import { useToast } from '../ui/design-system/Toast';
import { reportsService } from '../../services/api/reportsService';

interface Report {
  id: string;
  name: string;
  description: string;
  type: 'performance' | 'security' | 'quality' | 'usage' | 'custom';
  status: 'generating' | 'completed' | 'failed' | 'scheduled';
  progress: number;
  createdAt: Date;
  completedAt?: Date;
  size: number;
  format: 'pdf' | 'excel' | 'csv' | 'json';
  downloadUrl?: string;
  parameters: ReportParameters;
  creator: string;
}

interface ReportParameters {
  dateRange: {
    start: Date;
    end: Date;
  };
  agents?: string[];
  metrics?: string[];
  includeCharts: boolean;
  includeRawData: boolean;
  groupBy?: 'hour' | 'day' | 'week' | 'month';
}

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: string;
  defaultParameters: Partial<ReportParameters>;
  estimatedDuration: number;
}

export const ReportsGeneration: React.FC = () => {
  const { toast } = useToast();
  
  // États
  const [reports, setReports] = useState<Report[]>([]);
  const [templates, setTemplates] = useState<ReportTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState<string | null>(null);
  
  // Nouveau rapport
  const [newReport, setNewReport] = useState({
    name: '',
    description: '',
    type: 'performance' as const,
    format: 'pdf' as const,
    template: '',
    parameters: {
      dateRange: {
        start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 jours
        end: new Date()
      },
      agents: [] as string[],
      metrics: [] as string[],
      includeCharts: true,
      includeRawData: false,
      groupBy: 'day' as const
    }
  });

  // Charger les rapports
  const loadReports = async () => {
    try {
      setIsLoading(true);
      const [reportsResponse, templatesResponse] = await Promise.all([
        reportsService.getReports(),
        reportsService.getTemplates()
      ]);
      
      setReports(reportsResponse.data);
      setTemplates(templatesResponse.data);
    } catch (error) {
      console.error('Erreur lors du chargement des rapports:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de charger les rapports',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadReports();
    
    // Rafraîchissement automatique toutes les 30 secondes pour les rapports en cours
    const interval = setInterval(() => {
      if (reports.some(r => r.status === 'generating')) {
        loadReports();
      }
    }, 30000);
    
    return () => clearInterval(interval);
  }, [reports]);

  // Créer un nouveau rapport
  const handleCreateReport = async () => {
    try {
      await reportsService.createReport(newReport);
      toast({
        title: 'Succès',
        description: 'Rapport en cours de génération',
        variant: 'default'
      });
      setShowCreateModal(false);
      setNewReport({
        name: '',
        description: '',
        type: 'performance',
        format: 'pdf',
        template: '',
        parameters: {
          dateRange: {
            start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            end: new Date()
          },
          agents: [],
          metrics: [],
          includeCharts: true,
          includeRawData: false,
          groupBy: 'day'
        }
      });
      loadReports();
    } catch (error) {
      console.error('Erreur lors de la création:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de créer le rapport',
        variant: 'destructive'
      });
    }
  };

  // Télécharger un rapport
  const handleDownloadReport = async (reportId: string) => {
    try {
      const response = await reportsService.downloadReport(reportId);
      
      // Créer un lien de téléchargement
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `rapport-${reportId}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      toast({
        title: 'Succès',
        description: 'Rapport téléchargé',
        variant: 'default'
      });
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de télécharger le rapport',
        variant: 'destructive'
      });
    }
  };

  // Supprimer un rapport
  const handleDeleteReport = async (reportId: string) => {
    try {
      await reportsService.deleteReport(reportId);
      toast({
        title: 'Succès',
        description: 'Rapport supprimé',
        variant: 'default'
      });
      loadReports();
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de supprimer le rapport',
        variant: 'destructive'
      });
    }
  };

  // Formater la taille du fichier
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Obtenir la couleur du statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'generating': return 'warning';
      case 'failed': return 'destructive';
      case 'scheduled': return 'default';
      default: return 'secondary';
    }
  };

  // Obtenir l'icône du type
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'performance': return '⚡';
      case 'security': return '🔒';
      case 'quality': return '✅';
      case 'usage': return '📊';
      case 'custom': return '🔧';
      default: return '📋';
    }
  };

  const selectedReportData = reports.find(r => r.id === selectedReport);

  return (
    <div className="space-y-6">
      {/* En-tête et actions */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Génération de Rapports</h2>
          <p className="text-gray-600">Créer et gérer les rapports de performance et d'analyse</p>
        </div>
        
        <div className="flex space-x-3">
          <Button onClick={loadReports} variant="outline" leftIcon="🔄">
            Actualiser
          </Button>
          <Button onClick={() => setShowCreateModal(true)} leftIcon="➕">
            Nouveau Rapport
          </Button>
        </div>
      </div>

      {/* Statistiques */}
      <Grid cols={4} gap="md">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-blue-600">
              {reports.filter(r => r.status === 'completed').length}
            </div>
            <p className="text-sm text-gray-500">Rapports terminés</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-yellow-600">
              {reports.filter(r => r.status === 'generating').length}
            </div>
            <p className="text-sm text-gray-500">En cours</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-green-600">
              {reports.filter(r => r.status === 'scheduled').length}
            </div>
            <p className="text-sm text-gray-500">Programmés</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-purple-600">
              {formatFileSize(reports.reduce((sum, r) => sum + r.size, 0))}
            </div>
            <p className="text-sm text-gray-500">Taille totale</p>
          </CardContent>
        </Card>
      </Grid>

      {/* Liste des rapports */}
      <Grid cols={selectedReport ? 2 : 1} gap="lg">
        <Card>
          <CardHeader>
            <CardTitle>Rapports ({reports.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {reports.map((report) => (
                <motion.div
                  key={report.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedReport === report.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedReport(report.id)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <span className="text-lg">{getTypeIcon(report.type)}</span>
                      <div>
                        <h3 className="font-medium">{report.name}</h3>
                        <p className="text-sm text-gray-600">{report.description}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Badge variant={getStatusColor(report.status)}>
                        {report.status}
                      </Badge>
                      <Badge variant="outline">
                        {report.format.toUpperCase()}
                      </Badge>
                    </div>
                  </div>
                  
                  {report.status === 'generating' && (
                    <div className="mb-2">
                      <div className="flex justify-between text-xs text-gray-500 mb-1">
                        <span>Progression</span>
                        <span>{report.progress}%</span>
                      </div>
                      <Progress value={report.progress} className="h-2" />
                    </div>
                  )}
                  
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>{report.creator}</span>
                    <span>{new Date(report.createdAt).toLocaleDateString()}</span>
                  </div>
                  
                  {report.size > 0 && (
                    <div className="text-xs text-gray-500 mt-1">
                      Taille: {formatFileSize(report.size)}
                    </div>
                  )}
                  
                  <div className="flex space-x-2 mt-3">
                    {report.status === 'completed' && (
                      <Button
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDownloadReport(report.id);
                        }}
                        leftIcon="⬇️"
                      >
                        Télécharger
                      </Button>
                    )}
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteReport(report.id);
                      }}
                      leftIcon="🗑️"
                    >
                      Supprimer
                    </Button>
                  </div>
                </motion.div>
              ))}
              
              {reports.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  Aucun rapport généré
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Détails du rapport sélectionné */}
        {selectedReport && selectedReportData && (
          <Card>
            <CardHeader>
              <CardTitle>Détails du rapport</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2 flex items-center space-x-2">
                    <span>{getTypeIcon(selectedReportData.type)}</span>
                    <span>{selectedReportData.name}</span>
                  </h3>
                  <p className="text-gray-600">{selectedReportData.description}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Type:</span>
                    <span className="ml-2">{selectedReportData.type}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Format:</span>
                    <span className="ml-2">{selectedReportData.format.toUpperCase()}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Créé le:</span>
                    <span className="ml-2">{new Date(selectedReportData.createdAt).toLocaleString()}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Créateur:</span>
                    <span className="ml-2">{selectedReportData.creator}</span>
                  </div>
                </div>
                
                {selectedReportData.completedAt && (
                  <div className="p-3 bg-green-50 rounded-lg">
                    <p className="text-sm">
                      <strong>Terminé le:</strong> {new Date(selectedReportData.completedAt).toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-500">
                      Taille: {formatFileSize(selectedReportData.size)}
                    </p>
                  </div>
                )}
                
                <div>
                  <h4 className="font-medium mb-2">Paramètres</h4>
                  <div className="text-sm space-y-1">
                    <div>
                      <span className="text-gray-500">Période:</span>
                      <span className="ml-2">
                        {new Date(selectedReportData.parameters.dateRange.start).toLocaleDateString()} - {' '}
                        {new Date(selectedReportData.parameters.dateRange.end).toLocaleDateString()}
                      </span>
                    </div>
                    {selectedReportData.parameters.agents && selectedReportData.parameters.agents.length > 0 && (
                      <div>
                        <span className="text-gray-500">Agents:</span>
                        <span className="ml-2">{selectedReportData.parameters.agents.join(', ')}</span>
                      </div>
                    )}
                    <div>
                      <span className="text-gray-500">Graphiques:</span>
                      <span className="ml-2">{selectedReportData.parameters.includeCharts ? 'Oui' : 'Non'}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Données brutes:</span>
                      <span className="ml-2">{selectedReportData.parameters.includeRawData ? 'Oui' : 'Non'}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </Grid>

      {/* Modal de création de rapport */}
      <Modal open={showCreateModal} onOpenChange={setShowCreateModal}>
        <ModalContent className="max-w-2xl">
          <div className="p-6">
            <h2 className="text-xl font-bold mb-4">Créer un nouveau rapport</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Nom</label>
                <Input
                  value={newReport.name}
                  onChange={(e) => setNewReport({...newReport, name: e.target.value})}
                  placeholder="Nom du rapport"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <Input
                  value={newReport.description}
                  onChange={(e) => setNewReport({...newReport, description: e.target.value})}
                  placeholder="Description du rapport"
                />
              </div>
              
              <Grid cols={2} gap="md">
                <div>
                  <label className="block text-sm font-medium mb-1">Type</label>
                  <Select
                    value={newReport.type}
                    onValueChange={(value) => setNewReport({...newReport, type: value as any})}
                  >
                    <option value="performance">Performance</option>
                    <option value="security">Sécurité</option>
                    <option value="quality">Qualité</option>
                    <option value="usage">Utilisation</option>
                    <option value="custom">Personnalisé</option>
                  </Select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Format</label>
                  <Select
                    value={newReport.format}
                    onValueChange={(value) => setNewReport({...newReport, format: value as any})}
                  >
                    <option value="pdf">PDF</option>
                    <option value="excel">Excel</option>
                    <option value="csv">CSV</option>
                    <option value="json">JSON</option>
                  </Select>
                </div>
              </Grid>
              
              <div>
                <label className="block text-sm font-medium mb-1">Template</label>
                <Select
                  value={newReport.template}
                  onValueChange={(value) => setNewReport({...newReport, template: value})}
                >
                  <option value="">Sélectionner un template</option>
                  {templates.map((template) => (
                    <option key={template.id} value={template.id}>
                      {template.name}
                    </option>
                  ))}
                </Select>
              </div>
              
              <Grid cols={2} gap="md">
                <div>
                  <label className="block text-sm font-medium mb-1">Date de début</label>
                  <Input
                    type="date"
                    value={newReport.parameters.dateRange.start.toISOString().split('T')[0]}
                    onChange={(e) => setNewReport({
                      ...newReport,
                      parameters: {
                        ...newReport.parameters,
                        dateRange: {
                          ...newReport.parameters.dateRange,
                          start: new Date(e.target.value)
                        }
                      }
                    })}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Date de fin</label>
                  <Input
                    type="date"
                    value={newReport.parameters.dateRange.end.toISOString().split('T')[0]}
                    onChange={(e) => setNewReport({
                      ...newReport,
                      parameters: {
                        ...newReport.parameters,
                        dateRange: {
                          ...newReport.parameters.dateRange,
                          end: new Date(e.target.value)
                        }
                      }
                    })}
                  />
                </div>
              </Grid>
              
              <div className="flex items-center space-x-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={newReport.parameters.includeCharts}
                    onChange={(e) => setNewReport({
                      ...newReport,
                      parameters: {
                        ...newReport.parameters,
                        includeCharts: e.target.checked
                      }
                    })}
                  />
                  <span className="text-sm">Inclure les graphiques</span>
                </label>
                
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={newReport.parameters.includeRawData}
                    onChange={(e) => setNewReport({
                      ...newReport,
                      parameters: {
                        ...newReport.parameters,
                        includeRawData: e.target.checked
                      }
                    })}
                  />
                  <span className="text-sm">Inclure les données brutes</span>
                </label>
              </div>
            </div>
          </div>
          
          <ModalFooter>
            <Button variant="outline" onClick={() => setShowCreateModal(false)}>
              Annuler
            </Button>
            <Button onClick={handleCreateReport} disabled={!newReport.name}>
              Générer
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};
