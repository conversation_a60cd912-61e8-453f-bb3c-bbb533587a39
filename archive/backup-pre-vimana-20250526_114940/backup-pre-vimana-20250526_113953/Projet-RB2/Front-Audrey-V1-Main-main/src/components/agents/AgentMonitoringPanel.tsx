/**
 * Panel de Monitoring des Agents
 * Surveillance temps réel des agents et de leurs performances
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  Grid,
  Badge,
  Table,
  Progress,
  Alert
} from '../ui/design-system';
import { useToast } from '../ui/design-system/Toast';
import { agentsService } from '../../services/api/agentsService';

interface AgentStatus {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'error' | 'maintenance';
  uptime: number;
  lastHeartbeat: Date;
  activeJobs: number;
  totalJobs: number;
  errorRate: number;
  responseTime: number;
  memoryUsage: number;
  cpuUsage: number;
  version: string;
  endpoint: string;
}

interface AgentMonitoringPanelProps {
  agents: AgentStatus[];
  onRefresh: () => void;
}

export const AgentMonitoringPanel: React.FC<AgentMonitoringPanelProps> = ({
  agents,
  onRefresh
}) => {
  const { toast } = useToast();
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [agentDetails, setAgentDetails] = useState<any>(null);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);

  // Charger les détails d'un agent
  const loadAgentDetails = async (agentId: string) => {
    try {
      setIsLoadingDetails(true);
      const response = await agentsService.getAgentDetails(agentId);
      setAgentDetails(response.data);
    } catch (error) {
      console.error('Erreur lors du chargement des détails:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de charger les détails de l\'agent',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingDetails(false);
    }
  };

  // Actions sur les agents
  const handleAgentAction = async (agentId: string, action: 'restart' | 'stop' | 'start') => {
    try {
      await agentsService.controlAgent(agentId, action);
      toast({
        title: 'Succès',
        description: `Action ${action} exécutée sur l'agent`,
        variant: 'default'
      });
      onRefresh();
    } catch (error) {
      console.error('Erreur lors de l\'action:', error);
      toast({
        title: 'Erreur',
        description: `Impossible d'exécuter l'action ${action}`,
        variant: 'destructive'
      });
    }
  };

  // Formater l'uptime
  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) return `${days}j ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  // Obtenir la couleur du statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'offline': return 'bg-gray-400';
      case 'error': return 'bg-red-500';
      case 'maintenance': return 'bg-yellow-500';
      default: return 'bg-gray-400';
    }
  };

  // Obtenir la couleur de la performance
  const getPerformanceColor = (value: number, type: 'cpu' | 'memory' | 'response') => {
    if (type === 'response') {
      if (value < 500) return 'text-green-600';
      if (value < 1000) return 'text-yellow-600';
      return 'text-red-600';
    } else {
      if (value < 50) return 'text-green-600';
      if (value < 80) return 'text-yellow-600';
      return 'text-red-600';
    }
  };

  return (
    <div className="space-y-6">
      {/* Vue d'ensemble */}
      <Grid cols={3} gap="md">
        <Card>
          <CardHeader>
            <CardTitle>Agents en ligne</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600">
              {agents.filter(a => a.status === 'online').length}
            </div>
            <p className="text-sm text-gray-500">sur {agents.length} agents</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Jobs actifs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-600">
              {agents.reduce((sum, agent) => sum + agent.activeJobs, 0)}
            </div>
            <p className="text-sm text-gray-500">en cours d'exécution</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Temps de réponse moyen</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-600">
              {Math.round(agents.reduce((sum, agent) => sum + agent.responseTime, 0) / agents.length)}ms
            </div>
            <p className="text-sm text-gray-500">moyenne globale</p>
          </CardContent>
        </Card>
      </Grid>

      {/* Liste détaillée des agents */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>État détaillé des agents</CardTitle>
            <Button onClick={onRefresh} size="sm" leftIcon="🔄">
              Actualiser
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">Agent</th>
                  <th className="text-left py-3 px-4">Statut</th>
                  <th className="text-left py-3 px-4">Uptime</th>
                  <th className="text-left py-3 px-4">Jobs</th>
                  <th className="text-left py-3 px-4">CPU</th>
                  <th className="text-left py-3 px-4">Mémoire</th>
                  <th className="text-left py-3 px-4">Réponse</th>
                  <th className="text-left py-3 px-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {agents.map((agent) => (
                  <motion.tr
                    key={agent.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="border-b hover:bg-gray-50 cursor-pointer"
                    onClick={() => {
                      setSelectedAgent(agent.id);
                      loadAgentDetails(agent.id);
                    }}
                  >
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${getStatusColor(agent.status)}`} />
                        <div>
                          <p className="font-medium">{agent.name}</p>
                          <p className="text-sm text-gray-500">{agent.version}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <Badge 
                        variant={
                          agent.status === 'online' ? 'success' :
                          agent.status === 'offline' ? 'secondary' :
                          agent.status === 'error' ? 'destructive' :
                          'warning'
                        }
                      >
                        {agent.status}
                      </Badge>
                    </td>
                    <td className="py-3 px-4">
                      <span className="text-sm">{formatUptime(agent.uptime)}</span>
                    </td>
                    <td className="py-3 px-4">
                      <div className="text-sm">
                        <span className="font-medium">{agent.activeJobs}</span>
                        <span className="text-gray-500"> / {agent.totalJobs}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        <Progress value={agent.cpuUsage} className="w-16" />
                        <span className={`text-sm ${getPerformanceColor(agent.cpuUsage, 'cpu')}`}>
                          {agent.cpuUsage}%
                        </span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        <Progress value={agent.memoryUsage} className="w-16" />
                        <span className={`text-sm ${getPerformanceColor(agent.memoryUsage, 'memory')}`}>
                          {agent.memoryUsage}%
                        </span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`text-sm ${getPerformanceColor(agent.responseTime, 'response')}`}>
                        {agent.responseTime}ms
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex space-x-1">
                        {agent.status === 'online' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAgentAction(agent.id, 'restart');
                            }}
                          >
                            🔄
                          </Button>
                        )}
                        {agent.status === 'offline' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAgentAction(agent.id, 'start');
                            }}
                          >
                            ▶️
                          </Button>
                        )}
                        {agent.status === 'online' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAgentAction(agent.id, 'stop');
                            }}
                          >
                            ⏹️
                          </Button>
                        )}
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Détails de l'agent sélectionné */}
      {selectedAgent && (
        <Card>
          <CardHeader>
            <CardTitle>
              Détails de l'agent {agents.find(a => a.id === selectedAgent)?.name}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingDetails ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : agentDetails ? (
              <Grid cols={2} gap="md">
                <div>
                  <h4 className="font-medium mb-2">Informations générales</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Endpoint:</span>
                      <span className="font-mono">{agentDetails.endpoint}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Dernière activité:</span>
                      <span>{new Date(agentDetails.lastHeartbeat).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Taux d'erreur:</span>
                      <span className={agentDetails.errorRate > 5 ? 'text-red-600' : 'text-green-600'}>
                        {agentDetails.errorRate}%
                      </span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Métriques récentes</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Requêtes/min:</span>
                      <span>{agentDetails.requestsPerMinute || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Succès:</span>
                      <span className="text-green-600">{agentDetails.successRate || 0}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Latence P95:</span>
                      <span>{agentDetails.p95Latency || 0}ms</span>
                    </div>
                  </div>
                </div>
              </Grid>
            ) : (
              <Alert>
                Aucun détail disponible pour cet agent
              </Alert>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
