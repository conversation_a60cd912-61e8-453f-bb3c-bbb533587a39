/**
 * Layout Principal de l'Application - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Composant de layout principal qui unifie l'interface
 * de toute l'application avec navigation, header et contenu.
 */

import React, { Suspense } from 'react';
import { Outlet } from 'react-router-dom';
import { UnifiedNavigation } from './UnifiedNavigation';
import { FullPageLoading } from '../ui/design-system/Spinner';
import { cn } from '../../utils/cn';

interface AppLayoutProps {
  user?: {
    name: string;
    email: string;
    avatar?: string;
    role: string;
  };
  onLogout?: () => void;
  className?: string;
}

export const AppLayout: React.FC<AppLayoutProps> = ({
  user,
  onLogout,
  className,
}) => {
  return (
    <div className={cn('min-h-screen bg-neutral-50', className)}>
      {/* Navigation */}
      <UnifiedNavigation user={user} onLogout={onLogout} />
      
      {/* Contenu principal */}
      <div className="lg:pl-64">
        {/* Header mobile/desktop */}
        <header className="hidden lg:block bg-white border-b border-neutral-200">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-semibold text-neutral-900">
                  Bienvenue sur Retreat & Be
                </h1>
                <p className="text-sm text-neutral-600">
                  Découvrez votre chemin vers le bien-être
                </p>
              </div>
              
              {/* Actions rapides */}
              <div className="flex items-center space-x-4">
                <button className="relative rounded-full bg-white p-1 text-neutral-400 hover:text-neutral-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                  <span className="sr-only">Voir les notifications</span>
                  <span className="text-xl">🔔</span>
                  {/* Badge de notification */}
                  <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-error-500 text-xs text-white flex items-center justify-center">
                    3
                  </span>
                </button>
                
                <button className="rounded-full bg-white p-1 text-neutral-400 hover:text-neutral-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                  <span className="sr-only">Rechercher</span>
                  <span className="text-xl">🔍</span>
                </button>
              </div>
            </div>
          </div>
        </header>
        
        {/* Contenu de la page */}
        <main className="flex-1">
          <div className="px-4 py-6 lg:px-6">
            <Suspense fallback={<FullPageLoading text="Chargement de la page..." />}>
              <Outlet />
            </Suspense>
          </div>
        </main>
      </div>
    </div>
  );
};

// Layout pour les pages d'authentification
export const AuthLayout: React.FC<{
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
}> = ({ children, title, subtitle }) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-neutral-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="flex justify-center">
            <span className="text-6xl">🧘‍♀️</span>
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-neutral-900">
            {title || 'Retreat & Be'}
          </h2>
          {subtitle && (
            <p className="mt-2 text-sm text-neutral-600">
              {subtitle}
            </p>
          )}
        </div>
        <div className="bg-white py-8 px-6 shadow-lg rounded-lg">
          {children}
        </div>
      </div>
    </div>
  );
};

// Layout pour les pages d'erreur
export const ErrorLayout: React.FC<{
  error: {
    status: number;
    title: string;
    message: string;
  };
  onRetry?: () => void;
  onGoHome?: () => void;
}> = ({ error, onRetry, onGoHome }) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-neutral-50">
      <div className="max-w-md w-full text-center space-y-6">
        <div className="text-8xl">
          {error.status === 404 ? '🔍' : '⚠️'}
        </div>
        <div>
          <h1 className="text-4xl font-bold text-neutral-900">
            {error.status}
          </h1>
          <h2 className="mt-2 text-xl font-semibold text-neutral-700">
            {error.title}
          </h2>
          <p className="mt-2 text-neutral-600">
            {error.message}
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {onRetry && (
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
            >
              Réessayer
            </button>
          )}
          {onGoHome && (
            <button
              onClick={onGoHome}
              className="px-4 py-2 bg-neutral-200 text-neutral-700 rounded-md hover:bg-neutral-300 transition-colors"
            >
              Retour à l'accueil
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

// Layout pour les pages de maintenance
export const MaintenanceLayout: React.FC<{
  title?: string;
  message?: string;
  estimatedTime?: string;
}> = ({
  title = 'Maintenance en cours',
  message = 'Nous effectuons actuellement une maintenance pour améliorer votre expérience.',
  estimatedTime,
}) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-neutral-50">
      <div className="max-w-md w-full text-center space-y-6">
        <div className="text-8xl">🔧</div>
        <div>
          <h1 className="text-2xl font-bold text-neutral-900">
            {title}
          </h1>
          <p className="mt-2 text-neutral-600">
            {message}
          </p>
          {estimatedTime && (
            <p className="mt-2 text-sm text-neutral-500">
              Temps estimé : {estimatedTime}
            </p>
          )}
        </div>
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </div>
    </div>
  );
};

// Hook pour gérer les layouts
export const useLayout = () => {
  const [layoutType, setLayoutType] = React.useState<'app' | 'auth' | 'error' | 'maintenance'>('app');
  
  const showAuthLayout = () => setLayoutType('auth');
  const showAppLayout = () => setLayoutType('app');
  const showErrorLayout = () => setLayoutType('error');
  const showMaintenanceLayout = () => setLayoutType('maintenance');
  
  return {
    layoutType,
    showAuthLayout,
    showAppLayout,
    showErrorLayout,
    showMaintenanceLayout,
  };
};
