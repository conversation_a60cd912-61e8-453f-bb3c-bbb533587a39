import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from '../../hooks/useTranslation';
import { useAuthContext } from '../../hooks/useAuthContext';
import { 
  CriterionWeight, 
  MultiCriteriaOptions, 
  OptimizationMethod,
  multiCriteriaRecommendationService
} from '../../services/api/multiCriteriaRecommendationService';
import MultiCriteriaRecommendationList from './MultiCriteriaRecommendationList';

interface MultiCriteriaRecommendationSectionProps {
  title?: string;
  description?: string;
  count?: number;
  showControls?: boolean;
  className?: string;
}

/**
 * Composant pour afficher une section de recommandations multi-critères
 */
const MultiCriteriaRecommendationSection: React.FC<MultiCriteriaRecommendationSectionProps> = ({
  title,
  description,
  count = 3,
  showControls = false,
  className = '',
}) => {
  const { t } = useTranslation();
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  
  const [criteria, setCriteria] = useState<CriterionWeight[]>([
    { criterionId: 'relevance', weight: 0.6, direction: 'maximize' },
    { criterionId: 'rating', weight: 0.3, direction: 'maximize' },
    { criterionId: 'price', weight: 0.1, direction: 'minimize' },
  ]);
  
  const [optimizationMethod, setOptimizationMethod] = useState<OptimizationMethod>(
    OptimizationMethod.WEIGHTED_SUM
  );
  
  // Charger les préférences utilisateur et les recommandations
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Récupérer les préférences utilisateur
        if (user) {
          try {
            const preferences = await multiCriteriaRecommendationService.getUserPreferences();
            if (preferences && preferences.criteriaWeights && preferences.criteriaWeights.length > 0) {
              setCriteria(preferences.criteriaWeights);
            }
            
            if (preferences && preferences.preferredMethod) {
              setOptimizationMethod(preferences.preferredMethod);
            }
          } catch (error) {
            console.error('Erreur lors de la récupération des préférences:', error);
            // Continuer avec les valeurs par défaut
          }
        }
        
        // Récupérer les recommandations
        await loadRecommendations();
      } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        setError(t('multiCriteria.loadError'));
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, [user]);
  
  // Charger les recommandations
  const loadRecommendations = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const options: MultiCriteriaOptions = {
        criteria,
        optimizationMethod,
        normalizeScores: true,
      };
      
      const multiCriteriaRecommendations = await multiCriteriaRecommendationService.getMultiCriteriaRecommendations(
        options,
        'RETREAT',
        count
      );
      
      setRecommendations(multiCriteriaRecommendations);
    } catch (error) {
      console.error('Erreur lors du chargement des recommandations:', error);
      setError(t('multiCriteria.loadError'));
    } finally {
      setLoading(false);
    }
  };
  
  // Gérer le clic sur "Voir plus"
  const handleSeeMore = () => {
    navigate('/multi-criteria', { 
      state: { 
        criteria, 
        optimizationMethod 
      } 
    });
  };
  
  // Gérer le clic sur "Voir les détails"
  const handleViewDetails = (recommendation: any) => {
    navigate(`/retreats/${recommendation.id}`, {
      state: {
        from: 'multi-criteria',
        multiCriteriaScore: recommendation.multiCriteriaScore,
      },
    });
  };
  
  return (
    <div className={`py-8 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold mb-2">
            {title || t('multiCriteria.sectionTitle')}
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            {description || t('multiCriteria.sectionDescription')}
          </p>
        </div>
        
        {error && (
          <div className="bg-red-100 text-red-700 p-4 rounded-md mb-6">
            {error}
          </div>
        )}
        
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-retreat-green"></div>
          </div>
        ) : (
          <>
            <MultiCriteriaRecommendationList
              recommendations={recommendations}
              optimizationMethod={optimizationMethod}
              onViewDetails={handleViewDetails}
              className="mb-6"
            />
            
            <div className="flex justify-center">
              <button
                onClick={handleSeeMore}
                className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
              >
                {t('multiCriteria.seeMore')}
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default MultiCriteriaRecommendationSection;
