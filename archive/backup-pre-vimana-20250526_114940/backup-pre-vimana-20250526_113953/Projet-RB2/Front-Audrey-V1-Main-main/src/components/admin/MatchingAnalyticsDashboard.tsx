import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>hart, Line, BarChart, Bar, PieChart, Pie, 
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, 
  ResponsiveContainer, Cell 
} from 'recharts';
import { matchingAnalyticsService } from '../../services/api/matchingAnalyticsService';
import LoadingSpinner from '../atoms/LoadingSpinner/LoadingSpinner';

interface MatchingAnalyticsDashboardProps {
  period?: 'day' | 'week' | 'month' | 'year';
}

const MatchingAnalyticsDashboard: React.FC<MatchingAnalyticsDashboardProps> = ({ 
  period = 'month' 
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [trendsData, setTrendsData] = useState<any>(null);
  const [performanceData, setPerformanceData] = useState<any>(null);
  const [activePeriod, setActivePeriod] = useState<'day' | 'week' | 'month' | 'year'>(period);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        
        // Charger les données du tableau de bord
        const dashboardResponse = await matchingAnalyticsService.getDashboardData(activePeriod);
        setDashboardData(dashboardResponse);
        
        // Charger les données de tendances
        const trendsResponse = await matchingAnalyticsService.getTrendsData(activePeriod);
        setTrendsData(trendsResponse);
        
        // Charger les données de performance
        const performanceResponse = await matchingAnalyticsService.getPerformanceData(activePeriod);
        setPerformanceData(performanceResponse);
        
        setError(null);
      } catch (error) {
        console.error('Erreur lors du chargement des données d\'analyse:', error);
        setError('Impossible de charger les données d\'analyse');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [activePeriod]);

  // Données de démonstration pour le développement
  const demoData = {
    matchingStats: {
      totalMatchings: 1245,
      averageScore: 78,
      highQualityMatchings: 532,
      conversionRate: 23.5,
    },
    matchingTrends: [
      { date: '2023-01', matchings: 120, conversions: 25 },
      { date: '2023-02', matchings: 150, conversions: 32 },
      { date: '2023-03', matchings: 180, conversions: 45 },
      { date: '2023-04', matchings: 210, conversions: 52 },
      { date: '2023-05', matchings: 250, conversions: 65 },
      { date: '2023-06', matchings: 280, conversions: 72 },
    ],
    matchingDistribution: [
      { name: '90-100%', value: 125 },
      { name: '80-89%', value: 286 },
      { name: '70-79%', value: 420 },
      { name: '60-69%', value: 314 },
      { name: '<60%', value: 100 },
    ],
    partnerPerformance: [
      { name: 'Yoga Studio', matchings: 85, conversions: 32 },
      { name: 'Wellness Center', matchings: 72, conversions: 28 },
      { name: 'Meditation Retreat', matchings: 65, conversions: 24 },
      { name: 'Spa Resort', matchings: 58, conversions: 18 },
      { name: 'Nutrition Coach', matchings: 45, conversions: 15 },
    ],
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-8">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">{error}</h3>
          <p className="mt-1 text-sm text-gray-500">
            Veuillez réessayer ultérieurement.
          </p>
        </div>
      </div>
    );
  }

  // Utiliser les données réelles si disponibles, sinon utiliser les données de démonstration
  const data = dashboardData || demoData;

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            Tableau de bord d'analyse des matchings
          </h2>
          <div className="flex space-x-2">
            <button
              onClick={() => setActivePeriod('day')}
              className={`px-3 py-1 text-sm rounded-md ${
                activePeriod === 'day'
                  ? 'bg-retreat-green text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Jour
            </button>
            <button
              onClick={() => setActivePeriod('week')}
              className={`px-3 py-1 text-sm rounded-md ${
                activePeriod === 'week'
                  ? 'bg-retreat-green text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Semaine
            </button>
            <button
              onClick={() => setActivePeriod('month')}
              className={`px-3 py-1 text-sm rounded-md ${
                activePeriod === 'month'
                  ? 'bg-retreat-green text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Mois
            </button>
            <button
              onClick={() => setActivePeriod('year')}
              className={`px-3 py-1 text-sm rounded-md ${
                activePeriod === 'year'
                  ? 'bg-retreat-green text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Année
            </button>
          </div>
        </div>

        {/* Statistiques générales */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-500">Total des matchings</div>
            <div className="mt-1 text-2xl font-semibold text-gray-900">
              {data.matchingStats.totalMatchings.toLocaleString()}
            </div>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-500">Score moyen</div>
            <div className="mt-1 text-2xl font-semibold text-gray-900">
              {data.matchingStats.averageScore}%
            </div>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-500">Matchings de haute qualité</div>
            <div className="mt-1 text-2xl font-semibold text-gray-900">
              {data.matchingStats.highQualityMatchings.toLocaleString()}
            </div>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-500">Taux de conversion</div>
            <div className="mt-1 text-2xl font-semibold text-gray-900">
              {data.matchingStats.conversionRate}%
            </div>
          </div>
        </div>

        {/* Graphiques */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Tendances des matchings */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Tendances des matchings</h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={data.matchingTrends}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="matchings"
                    stroke="#8884d8"
                    activeDot={{ r: 8 }}
                    name="Matchings"
                  />
                  <Line
                    type="monotone"
                    dataKey="conversions"
                    stroke="#82ca9d"
                    name="Conversions"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Distribution des scores */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Distribution des scores</h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.matchingDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {data.matchingDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Performance des partenaires */}
          <div className="bg-gray-50 rounded-lg p-4 lg:col-span-2">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Performance des partenaires</h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={data.partnerPerformance}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="matchings" fill="#8884d8" name="Matchings" />
                  <Bar dataKey="conversions" fill="#82ca9d" name="Conversions" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MatchingAnalyticsDashboard;
