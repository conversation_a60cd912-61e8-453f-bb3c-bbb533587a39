import React, { useState } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { Benchmark } from '../../services/api/analyticsService';
import { MetricsChart } from './MetricsChart';
import { FadeIn } from '../ui/FadeIn';

export interface BenchmarkMetricsProps {
  benchmark: Benchmark;
  categories: string[];
  onCategoryChange: (category: string) => void;
  compact?: boolean;
}

export const BenchmarkMetrics: React.FC<BenchmarkMetricsProps> = ({
  benchmark,
  categories,
  onCategoryChange,
  compact = false,
}) => {
  const { t } = useTranslation();
  
  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };
  
  const formatPercentage = (num: number) => {
    return `${num.toFixed(2)}%`;
  };
  
  const getPercentileClass = (percentile: number) => {
    if (percentile >= 75) return 'text-green-600';
    if (percentile >= 50) return 'text-blue-600';
    if (percentile >= 25) return 'text-yellow-600';
    return 'text-red-600';
  };
  
  const getPerformanceData = (metric: { value: number; percentile: number; categoryAverage: number }) => {
    return [
      {
        name: t('analytics.benchmarks.yourPerformance'),
        value: metric.value,
      },
      {
        name: t('analytics.benchmarks.categoryAverage'),
        value: metric.categoryAverage,
      },
    ];
  };
  
  const getPercentileIndicator = (percentile: number) => {
    return (
      <div className="flex items-center">
        <div className="w-full bg-gray-200 rounded-full h-2.5">
          <div
            className={`h-2.5 rounded-full ${
              percentile >= 75
                ? 'bg-green-600'
                : percentile >= 50
                ? 'bg-blue-600'
                : percentile >= 25
                ? 'bg-yellow-600'
                : 'bg-red-600'
            }`}
            style={{ width: `${percentile}%` }}
          ></div>
        </div>
        <span className={`ml-2 text-sm font-medium ${getPercentileClass(percentile)}`}>
          {formatPercentage(percentile)}
        </span>
      </div>
    );
  };
  
  if (compact) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-sm font-medium text-gray-500">{t('analytics.benchmarks.category')}: {benchmark.category}</h3>
          
          <select
            value={benchmark.category}
            onChange={(e) => onCategoryChange(e.target.value)}
            className="text-sm border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            {categories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h4 className="text-xs font-medium text-gray-500">{t('analytics.benchmarks.metrics.views')}</h4>
            <p className="text-lg font-bold">{formatNumber(benchmark.engagement.views.value)}</p>
            {getPercentileIndicator(benchmark.engagement.views.percentile)}
          </div>
          
          <div>
            <h4 className="text-xs font-medium text-gray-500">{t('analytics.benchmarks.metrics.followers')}</h4>
            <p className="text-lg font-bold">{formatNumber(benchmark.audience.followers.value)}</p>
            {getPercentileIndicator(benchmark.audience.followers.percentile)}
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <FadeIn>
      <h2 className="text-xl font-semibold mb-6">{t('analytics.benchmarks.title')}</h2>
      
      <div className="mb-6 flex justify-between items-center">
        <div className="text-sm font-medium text-gray-500">
          {t('analytics.benchmarks.category')}: <span className="font-bold">{benchmark.category}</span>
        </div>
        
        <div>
          <select
            value={benchmark.category}
            onChange={(e) => onCategoryChange(e.target.value)}
            className="border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            {categories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium mb-4">{t('analytics.benchmarks.metrics.views')}</h3>
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm text-gray-500">{t('analytics.benchmarks.yourPerformance')}</p>
              <p className="text-2xl font-bold">{formatNumber(benchmark.engagement.views.value)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('analytics.benchmarks.percentile')}</p>
              <p className={`text-2xl font-bold ${getPercentileClass(benchmark.engagement.views.percentile)}`}>
                {formatPercentage(benchmark.engagement.views.percentile)}
              </p>
            </div>
          </div>
          <div className="mb-4">
            <p className="text-sm text-gray-500">{t('analytics.benchmarks.categoryAverage')}</p>
            <p className="text-lg font-medium">{formatNumber(benchmark.engagement.views.categoryAverage)}</p>
          </div>
          <MetricsChart
            data={getPerformanceData(benchmark.engagement.views)}
            dataKeys={['value']}
            xAxisDataKey="name"
            height={150}
            type="bar"
          />
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium mb-4">{t('analytics.benchmarks.metrics.engagementRate')}</h3>
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm text-gray-500">{t('analytics.benchmarks.yourPerformance')}</p>
              <p className="text-2xl font-bold">{formatPercentage(benchmark.engagement.engagementRate.value)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('analytics.benchmarks.percentile')}</p>
              <p className={`text-2xl font-bold ${getPercentileClass(benchmark.engagement.engagementRate.percentile)}`}>
                {formatPercentage(benchmark.engagement.engagementRate.percentile)}
              </p>
            </div>
          </div>
          <div className="mb-4">
            <p className="text-sm text-gray-500">{t('analytics.benchmarks.categoryAverage')}</p>
            <p className="text-lg font-medium">{formatPercentage(benchmark.engagement.engagementRate.categoryAverage)}</p>
          </div>
          <MetricsChart
            data={getPerformanceData(benchmark.engagement.engagementRate)}
            dataKeys={['value']}
            xAxisDataKey="name"
            height={150}
            type="bar"
          />
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium mb-4">{t('analytics.benchmarks.metrics.followers')}</h3>
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm text-gray-500">{t('analytics.benchmarks.yourPerformance')}</p>
              <p className="text-2xl font-bold">{formatNumber(benchmark.audience.followers.value)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('analytics.benchmarks.percentile')}</p>
              <p className={`text-2xl font-bold ${getPercentileClass(benchmark.audience.followers.percentile)}`}>
                {formatPercentage(benchmark.audience.followers.percentile)}
              </p>
            </div>
          </div>
          <div className="mb-4">
            <p className="text-sm text-gray-500">{t('analytics.benchmarks.categoryAverage')}</p>
            <p className="text-lg font-medium">{formatNumber(benchmark.audience.followers.categoryAverage)}</p>
          </div>
          <MetricsChart
            data={getPerformanceData(benchmark.audience.followers)}
            dataKeys={['value']}
            xAxisDataKey="name"
            height={150}
            type="bar"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium mb-4">{t('analytics.benchmarks.metrics.growth')}</h3>
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm text-gray-500">{t('analytics.benchmarks.yourPerformance')}</p>
              <p className="text-2xl font-bold">{formatPercentage(benchmark.audience.growth.value)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('analytics.benchmarks.percentile')}</p>
              <p className={`text-2xl font-bold ${getPercentileClass(benchmark.audience.growth.percentile)}`}>
                {formatPercentage(benchmark.audience.growth.percentile)}
              </p>
            </div>
          </div>
          <div className="mb-4">
            <p className="text-sm text-gray-500">{t('analytics.benchmarks.categoryAverage')}</p>
            <p className="text-lg font-medium">{formatPercentage(benchmark.audience.growth.categoryAverage)}</p>
          </div>
          <MetricsChart
            data={getPerformanceData(benchmark.audience.growth)}
            dataKeys={['value']}
            xAxisDataKey="name"
            height={150}
            type="bar"
          />
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium mb-4">{t('analytics.benchmarks.metrics.revenue')}</h3>
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm text-gray-500">{t('analytics.benchmarks.yourPerformance')}</p>
              <p className="text-2xl font-bold">{formatNumber(benchmark.revenue.amount.value)} €</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('analytics.benchmarks.percentile')}</p>
              <p className={`text-2xl font-bold ${getPercentileClass(benchmark.revenue.amount.percentile)}`}>
                {formatPercentage(benchmark.revenue.amount.percentile)}
              </p>
            </div>
          </div>
          <div className="mb-4">
            <p className="text-sm text-gray-500">{t('analytics.benchmarks.categoryAverage')}</p>
            <p className="text-lg font-medium">{formatNumber(benchmark.revenue.amount.categoryAverage)} €</p>
          </div>
          <MetricsChart
            data={getPerformanceData(benchmark.revenue.amount)}
            dataKeys={['value']}
            xAxisDataKey="name"
            height={150}
            type="bar"
          />
        </div>
      </div>
    </FadeIn>
  );
};

export default BenchmarkMetrics;
