import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import RetreatCard from './RetreatCard';

// Mock the Icon component to avoid issues with Heroicons
jest.mock('../../atoms/Icon/Icon', () => ({
  __esModule: true,
  default: ({ children }: { children?: React.ReactNode }) => (
    <div data-testid='mock-icon'>{children}</div>
  ),
}));

describe('RetreatCard', () => {
  const mockProps = {
    id: 'retreat-123',
    title: 'Yoga Retreat in Bali',
    image: '/images/retreat-bali.jpg',
    location: 'Bali, Indonesia',
    price: 1500,
    duration: '7 days',
    maxParticipants: 20,
    rating: 4.8,
    tags: ['yoga', 'meditation', 'wellness'],
    host: {
      name: '<PERSON>',
      avatar: '/images/avatar.jpg',
    },
  };

  it('renders correctly with all props', () => {
    render(
      <BrowserRouter>
        <RetreatCard {...mockProps} />
      </BrowserRouter>
    );

    // Check if title is rendered
    expect(screen.getByText(mockProps.title)).toBeInTheDocument();

    // Check if location is rendered
    expect(screen.getByText(mockProps.location)).toBeInTheDocument();

    // Check if price is rendered
    expect(screen.getByText(/1 500 €/)).toBeInTheDocument();

    // Check if duration is rendered
    expect(screen.getByText(mockProps.duration)).toBeInTheDocument();

    // Check if max participants is rendered
    expect(screen.getByText(`Max ${mockProps.maxParticipants}`)).toBeInTheDocument();

    // Check if rating is rendered
    expect(screen.getByText(mockProps.rating.toString())).toBeInTheDocument();

    // Check if host name is rendered
    expect(screen.getByText(mockProps.host.name)).toBeInTheDocument();

    // Check if tags are rendered
    mockProps.tags.forEach((tag) => {
      expect(screen.getByText(tag)).toBeInTheDocument();
    });

    // Check if images are rendered
    const image = screen.getByAltText(mockProps.title);
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', mockProps.image);

    // Check if host avatar is rendered
    const avatar = screen.getByAltText(mockProps.host.name);
    expect(avatar).toBeInTheDocument();
    expect(avatar).toHaveAttribute('src', mockProps.host.avatar);

    // Check if link is correct
    const link = screen.getByRole('link');
    expect(link).toHaveAttribute('href', `/retreats/${mockProps.id}`);
  });

  it('applies custom className when provided', () => {
    const customClass = 'custom-class';
    const { container } = render(
      <BrowserRouter>
        <RetreatCard {...mockProps} className={customClass} />
      </BrowserRouter>
    );

    // Check if the custom class is applied
    expect(container.firstChild).toHaveClass(customClass);
  });

  it('renders with default className when no custom className is provided', () => {
    const { container } = render(
      <BrowserRouter>
        <RetreatCard {...mockProps} />
      </BrowserRouter>
    );

    // Check if the default class is applied
    expect(container.firstChild).toHaveClass('bg-white');
    expect(container.firstChild).toHaveClass('rounded-lg');
    expect(container.firstChild).toHaveClass('shadow-sm');
    expect(container.firstChild).toHaveClass('overflow-hidden');
  });
});
