import React from 'react';
import { ChatMessage } from '../../../../hooks/useChatbot';
import MessageFeedback from '../MessageFeedback';

interface TextMessageProps {
  message: ChatMessage;
  onFeedback?: (messageId: string, isPositive: boolean) => void;
}

/**
 * Component to render a text message
 */
const TextMessage: React.FC<TextMessageProps> = ({ message, onFeedback }) => {
  return (
    <div className='flex flex-col'>
      <p className='whitespace-pre-wrap'>{message.content}</p>

      {/* Timestamp */}
      <div className='text-xs opacity-70 mt-1 text-right'>
        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
      </div>

      {/* Feedback buttons (only for assistant messages) */}
      {message.role === 'assistant' && onFeedback && (
        <MessageFeedback messageId={message.id} onFeedback={onFeedback} />
      )}
    </div>
  );
};

export default TextMessage;
