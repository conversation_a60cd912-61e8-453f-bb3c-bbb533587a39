import React from 'react';

/**
 * Component to render a typing indicator
 */
const TypingIndicator: React.FC = () => {
  return (
    <div className='flex items-center space-x-2'>
      <div
        className='w-2 h-2 bg-green-500 rounded-full animate-bounce'
        style={{ animationDelay: '0ms' }}
      ></div>
      <div
        className='w-2 h-2 bg-green-500 rounded-full animate-bounce'
        style={{ animationDelay: '150ms' }}
      ></div>
      <div
        className='w-2 h-2 bg-green-500 rounded-full animate-bounce'
        style={{ animationDelay: '300ms' }}
      ></div>
    </div>
  );
};

export default TypingIndicator;
