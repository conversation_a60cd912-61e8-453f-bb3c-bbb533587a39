import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import FilterBar from './FilterBar';

// Mock the retreatService
jest.mock('../../../services/api/retreatService', () => ({
  retreatService: {
    getCategories: jest.fn().mockResolvedValue(['yoga', 'meditation', 'wellness', 'adventure']),
    getLocations: jest.fn().mockResolvedValue(['Bali', 'Thailand', 'Costa Rica', 'Portugal']),
  },
}));

describe('FilterBar', () => {
  const mockOnFilterChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with default props', async () => {
    render(<FilterBar onFilterChange={mockOnFilterChange} />);

    // Check if search input is rendered
    expect(screen.getByPlaceholderText('Search retreats...')).toBeInTheDocument();

    // Check if "More Filters" button is rendered
    expect(screen.getByText('More Filters')).toBeInTheDocument();

    // Wait for categories to load
    await waitFor(() => {
      expect(screen.getByText('Categories')).toBeInTheDocument();
    });
  });

  it('calls onFilterChange when search input changes', async () => {
    render(<FilterBar onFilterChange={mockOnFilterChange} />);

    const searchInput = screen.getByPlaceholderText('Search retreats...');
    fireEvent.change(searchInput, { target: { value: 'yoga' } });

    // Wait for debounce
    await waitFor(
      () => {
        expect(mockOnFilterChange).toHaveBeenCalledWith(
          expect.objectContaining({
            search: 'yoga',
          })
        );
      },
      { timeout: 1000 }
    );
  });

  it('toggles advanced filters when "More Filters" button is clicked', () => {
    render(<FilterBar onFilterChange={mockOnFilterChange} />);

    const moreFiltersButton = screen.getByText('More Filters');

    // Advanced filters should be hidden initially
    expect(screen.queryByText('Price Range')).not.toBeInTheDocument();

    // Click to show advanced filters
    fireEvent.click(moreFiltersButton);

    // Advanced filters should be visible now
    expect(screen.getByText('Price Range')).toBeInTheDocument();

    // Button text should change
    expect(screen.getByText('Hide Filters')).toBeInTheDocument();

    // Click again to hide advanced filters
    fireEvent.click(screen.getByText('Hide Filters'));

    // Advanced filters should be hidden again
    expect(screen.queryByText('Price Range')).not.toBeInTheDocument();
  });

  it('shows reset button when filters are applied', async () => {
    render(<FilterBar onFilterChange={mockOnFilterChange} />);

    // Reset button should not be visible initially
    expect(screen.queryByText('Reset')).not.toBeInTheDocument();

    // Apply a filter
    const searchInput = screen.getByPlaceholderText('Search retreats...');
    fireEvent.change(searchInput, { target: { value: 'yoga' } });

    // Wait for debounce
    await waitFor(
      () => {
        // Reset button should be visible now
        expect(screen.getByText('Reset')).toBeInTheDocument();
      },
      { timeout: 1000 }
    );

    // Click reset button
    fireEvent.click(screen.getByText('Reset'));

    // Wait for reset to complete
    await waitFor(() => {
      // onFilterChange should be called with empty filters
      expect(mockOnFilterChange).toHaveBeenCalledWith({
        search: '',
        category: '',
        minPrice: '',
        maxPrice: '',
        location: '',
        startDate: '',
        endDate: '',
      });

      // Reset button should be hidden again
      expect(screen.queryByText('Reset')).not.toBeInTheDocument();
    });
  });

  it('applies custom className when provided', () => {
    const customClass = 'custom-class';
    const { container } = render(
      <FilterBar onFilterChange={mockOnFilterChange} className={customClass} />
    );

    // Check if the custom class is applied
    expect(container.firstChild).toHaveClass(customClass);
  });
});
