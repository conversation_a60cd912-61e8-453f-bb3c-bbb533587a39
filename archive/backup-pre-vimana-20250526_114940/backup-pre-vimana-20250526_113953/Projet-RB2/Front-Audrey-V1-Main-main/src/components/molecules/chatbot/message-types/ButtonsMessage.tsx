import React from 'react';
import { ChatMessage } from '../../../../hooks/useChatbot';
import { Button } from '../../../../services/api/chatbotService';
import MessageFeedback from '../MessageFeedback';

interface ButtonsMessageProps {
  message: ChatMessage;
  onButtonClick?: (button: Button) => void;
  onFeedback?: (messageId: string, isPositive: boolean) => void;
}

/**
 * Component to render a message with buttons
 */
const ButtonsMessage: React.FC<ButtonsMessageProps> = ({ message, onButtonClick, onFeedback }) => {
  const handleButtonClick = (button: Button) => {
    if (onButtonClick) {
      onButtonClick(button);
    }
  };

  return (
    <div className='flex flex-col'>
      {/* Message content */}
      <p className='whitespace-pre-wrap mb-3'>{message.content}</p>

      {/* Buttons */}
      <div className='flex flex-col gap-2 mt-2'>
        {message.buttons?.map((button, index) => (
          <button
            key={index}
            onClick={() => handleButtonClick(button)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              button.type === 'url'
                ? 'bg-blue-500 text-white hover:bg-blue-600'
                : 'bg-retreat-green text-black hover:bg-opacity-90'
            }`}
          >
            {button.text}
          </button>
        ))}
      </div>

      {/* Timestamp */}
      <div className='text-xs opacity-70 mt-3 text-right'>
        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
      </div>

      {/* Feedback buttons */}
      {message.role === 'assistant' && onFeedback && (
        <MessageFeedback messageId={message.id} onFeedback={onFeedback} />
      )}
    </div>
  );
};

export default ButtonsMessage;
