import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { useAuthContext } from '../../hooks/useAuthContext';
import { userPreferencesService } from '../../services/api/userPreferencesService';
import { FadeIn } from '../ui/FadeIn';
import { <PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, Tab, TabPanel } from '../ui/Tabs';
import { Button } from '../ui/Button';
import { Spinner } from '../ui/Spinner';
import { Alert } from '../ui/Alert';
import { Slider } from '../ui/Slider';
import { Switch } from '../ui/Switch';
import { MultiSelect } from '../ui/MultiSelect';
import { Badge } from '../ui/Badge';

interface UserPreference {
  userId: string;
  category: string;
  weight: number;
}

interface UserInterest {
  userId: string;
  interest: string;
  type: 'CATEGORY' | 'TAG' | 'KEYWORD';
  source: 'EXPLICIT' | 'IMPLICIT' | 'INFERRED';
  weight: number;
}

interface PersonalizationSettings {
  userId: string;
  enablePersonalization: boolean;
  diversityLevel: number;
  noveltyLevel: number;
  contentFilters: {
    excludedCategories: string[];
    excludedTags: string[];
    contentTypes: string[];
    minContentRating: number;
    languagePreferences: string[];
  };
  privacySettings: {
    allowInteractionTracking: boolean;
    allowContentAnalysis: boolean;
    allowThirdPartyData: boolean;
    dataRetentionPeriod: number;
  };
}

interface UserPreferencesPanelProps {
  onSave?: () => void;
}

export const UserPreferencesPanel: React.FC<UserPreferencesPanelProps> = ({ onSave }) => {
  const { t } = useTranslation();
  const { user } = useAuthContext();
  const [preferences, setPreferences] = useState<UserPreference[]>([]);
  const [interests, setInterests] = useState<UserInterest[]>([]);
  const [settings, setSettings] = useState<PersonalizationSettings | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [saving, setSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  const [availableTags, setAvailableTags] = useState<string[]>([]);
  const [availableContentTypes, setAvailableContentTypes] = useState<string[]>([
    'RETREAT', 'COURSE', 'PARTNER', 'POST', 'VIDEO', 'ARTICLE'
  ]);
  const [availableLanguages, setAvailableLanguages] = useState<string[]>([
    'en', 'fr', 'es', 'de', 'it', 'pt', 'ja', 'zh'
  ]);

  useEffect(() => {
    fetchUserPreferences();
  }, []);

  const fetchUserPreferences = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Récupérer les préférences
      const preferencesData = await userPreferencesService.getUserPreferences();
      setPreferences(preferencesData);
      
      // Récupérer les intérêts
      const interestsData = await userPreferencesService.getUserInterests();
      setInterests(interestsData);
      
      // Récupérer les paramètres de personnalisation
      const settingsData = await userPreferencesService.getPersonalizationSettings();
      setSettings(settingsData);
      
      // Récupérer les catégories et tags disponibles
      // Dans une application réelle, ces données viendraient d'une API
      setAvailableCategories([
        'Yoga', 'Meditation', 'Wellness', 'Fitness', 'Nutrition', 
        'Mindfulness', 'Spirituality', 'Personal Development', 
        'Nature', 'Adventure', 'Art', 'Music', 'Dance', 'Cooking'
      ]);
      
      setAvailableTags([
        'beginner', 'intermediate', 'advanced', 'relaxation', 'stress-relief',
        'healing', 'detox', 'energy', 'balance', 'strength', 'flexibility',
        'focus', 'creativity', 'community', 'silence', 'luxury', 'budget',
        'weekend', 'week-long', 'month-long', 'vegan', 'vegetarian', 'gluten-free'
      ]);
      
      setLoading(false);
    } catch (error) {
      console.error('Error fetching user preferences:', error);
      setError(t('preferences.errors.fetchFailed'));
      setLoading(false);
    }
  };

  const handlePreferenceChange = (category: string, weight: number) => {
    setPreferences(prev => {
      const existing = prev.find(p => p.category === category);
      if (existing) {
        return prev.map(p => p.category === category ? { ...p, weight } : p);
      } else {
        return [...prev, { userId: user?.id || '', category, weight }];
      }
    });
  };

  const handleRemovePreference = (category: string) => {
    setPreferences(prev => prev.filter(p => p.category !== category));
  };

  const handleAddPreference = (category: string) => {
    if (!preferences.some(p => p.category === category)) {
      setPreferences(prev => [...prev, { userId: user?.id || '', category, weight: 0.5 }]);
    }
  };

  const handleInterestChange = (interest: UserInterest, weight: number) => {
    setInterests(prev => {
      return prev.map(i => 
        i.interest === interest.interest && i.type === interest.type
          ? { ...i, weight }
          : i
      );
    });
  };

  const handleRemoveInterest = (interest: UserInterest) => {
    setInterests(prev => prev.filter(i => 
      !(i.interest === interest.interest && i.type === interest.type)
    ));
  };

  const handleAddInterest = (interest: string, type: 'CATEGORY' | 'TAG' | 'KEYWORD') => {
    if (!interests.some(i => i.interest === interest && i.type === type)) {
      setInterests(prev => [
        ...prev,
        {
          userId: user?.id || '',
          interest,
          type,
          source: 'EXPLICIT',
          weight: 0.5,
        }
      ]);
    }
  };

  const handleSettingChange = (path: string, value: any) => {
    if (!settings) return;
    
    setSettings(prev => {
      if (!prev) return null;
      
      const newSettings = { ...prev };
      const parts = path.split('.');
      
      if (parts.length === 1) {
        (newSettings as any)[parts[0]] = value;
      } else if (parts.length === 2) {
        (newSettings as any)[parts[0]][parts[1]] = value;
      }
      
      return newSettings;
    });
  };

  const handleSavePreferences = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);
      
      // Sauvegarder les préférences
      await userPreferencesService.updateUserPreferences({
        preferences: preferences.map(p => ({
          category: p.category,
          weight: p.weight,
        })),
      });
      
      // Sauvegarder les intérêts
      await userPreferencesService.updateUserInterests({
        interests: interests
          .filter(i => i.source === 'EXPLICIT')
          .map(i => ({
            interest: i.interest,
            type: i.type,
            weight: i.weight,
          })),
      });
      
      // Sauvegarder les paramètres de personnalisation
      if (settings) {
        await userPreferencesService.updatePersonalizationSettings(settings);
      }
      
      setSuccess(t('preferences.saveSuccess'));
      setSaving(false);
      
      if (onSave) {
        onSave();
      }
    } catch (error) {
      console.error('Error saving user preferences:', error);
      setError(t('preferences.errors.saveFailed'));
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <FadeIn>
      <div className="mb-6">
        <h2 className="text-xl font-semibold">{t('preferences.title')}</h2>
        <p className="text-gray-500">{t('preferences.description')}</p>
      </div>
      
      {error && (
        <Alert variant="error" title={t('common.error')} message={error} className="mb-4" />
      )}
      
      {success && (
        <Alert variant="success" title={t('common.success')} message={success} className="mb-4" />
      )}
      
      <Tabs>
        <TabList>
          <Tab>{t('preferences.tabs.categories')}</Tab>
          <Tab>{t('preferences.tabs.interests')}</Tab>
          <Tab>{t('preferences.tabs.personalization')}</Tab>
          <Tab>{t('preferences.tabs.privacy')}</Tab>
        </TabList>
        
        <TabPanel>
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 className="text-lg font-medium mb-4">{t('preferences.categories.title')}</h3>
            <p className="text-gray-500 mb-4">{t('preferences.categories.description')}</p>
            
            <div className="mb-6">
              <h4 className="font-medium mb-2">{t('preferences.categories.yourPreferences')}</h4>
              {preferences.length === 0 ? (
                <p className="text-gray-500">{t('preferences.categories.noPreferences')}</p>
              ) : (
                <div className="space-y-4">
                  {preferences.map(preference => (
                    <div key={preference.category} className="flex items-center space-x-4">
                      <div className="w-1/3">
                        <span className="font-medium">{preference.category}</span>
                      </div>
                      <div className="flex-grow">
                        <Slider
                          min={0}
                          max={1}
                          step={0.1}
                          value={preference.weight}
                          onChange={value => handlePreferenceChange(preference.category, value)}
                        />
                      </div>
                      <div className="w-16 text-center">
                        {Math.round(preference.weight * 100)}%
                      </div>
                      <button
                        onClick={() => handleRemovePreference(preference.category)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            <div>
              <h4 className="font-medium mb-2">{t('preferences.categories.addPreference')}</h4>
              <div className="flex space-x-2">
                <select
                  className="border rounded-md px-3 py-2 flex-grow"
                  onChange={e => e.target.value && handleAddPreference(e.target.value)}
                  value=""
                >
                  <option value="">{t('preferences.categories.selectCategory')}</option>
                  {availableCategories
                    .filter(category => !preferences.some(p => p.category === category))
                    .map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))
                  }
                </select>
                <Button
                  variant="secondary"
                  onClick={() => {
                    const select = document.querySelector('select') as HTMLSelectElement;
                    if (select.value) {
                      handleAddPreference(select.value);
                      select.value = '';
                    }
                  }}
                >
                  {t('common.add')}
                </Button>
              </div>
            </div>
          </div>
        </TabPanel>
        
        <TabPanel>
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 className="text-lg font-medium mb-4">{t('preferences.interests.title')}</h3>
            <p className="text-gray-500 mb-4">{t('preferences.interests.description')}</p>
            
            <div className="mb-6">
              <h4 className="font-medium mb-2">{t('preferences.interests.yourInterests')}</h4>
              
              {interests.length === 0 ? (
                <p className="text-gray-500">{t('preferences.interests.noInterests')}</p>
              ) : (
                <div className="space-y-6">
                  {['EXPLICIT', 'IMPLICIT', 'INFERRED'].map(source => {
                    const sourceInterests = interests.filter(i => i.source === source);
                    if (sourceInterests.length === 0) return null;
                    
                    return (
                      <div key={source} className="space-y-4">
                        <h5 className="font-medium text-sm text-gray-500">
                          {t(`preferences.interests.sources.${source.toLowerCase()}`)}
                        </h5>
                        
                        <div className="space-y-2">
                          {sourceInterests.map(interest => (
                            <div key={`${interest.type}-${interest.interest}`} className="flex items-center space-x-4">
                              <div className="w-1/3 flex items-center space-x-2">
                                <Badge color={
                                  interest.type === 'CATEGORY' ? 'blue' :
                                  interest.type === 'TAG' ? 'green' : 'purple'
                                }>
                                  {interest.type}
                                </Badge>
                                <span className="font-medium">{interest.interest}</span>
                              </div>
                              
                              {interest.source === 'EXPLICIT' ? (
                                <>
                                  <div className="flex-grow">
                                    <Slider
                                      min={0}
                                      max={1}
                                      step={0.1}
                                      value={interest.weight}
                                      onChange={value => handleInterestChange(interest, value)}
                                    />
                                  </div>
                                  <div className="w-16 text-center">
                                    {Math.round(interest.weight * 100)}%
                                  </div>
                                  <button
                                    onClick={() => handleRemoveInterest(interest)}
                                    className="text-red-500 hover:text-red-700"
                                  >
                                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                  </button>
                                </>
                              ) : (
                                <div className="flex-grow text-gray-500">
                                  {t('preferences.interests.inferredWeight', { weight: Math.round(interest.weight * 100) })}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
            
            <div>
              <h4 className="font-medium mb-2">{t('preferences.interests.addInterest')}</h4>
              <div className="flex flex-col space-y-2">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    className="border rounded-md px-3 py-2 flex-grow"
                    placeholder={t('preferences.interests.enterInterest')}
                    id="new-interest"
                  />
                  <select
                    className="border rounded-md px-3 py-2 w-40"
                    id="interest-type"
                    defaultValue="KEYWORD"
                  >
                    <option value="KEYWORD">{t('preferences.interests.types.keyword')}</option>
                    <option value="TAG">{t('preferences.interests.types.tag')}</option>
                    <option value="CATEGORY">{t('preferences.interests.types.category')}</option>
                  </select>
                </div>
                <Button
                  variant="secondary"
                  onClick={() => {
                    const input = document.getElementById('new-interest') as HTMLInputElement;
                    const select = document.getElementById('interest-type') as HTMLSelectElement;
                    if (input.value) {
                      handleAddInterest(
                        input.value,
                        select.value as 'CATEGORY' | 'TAG' | 'KEYWORD'
                      );
                      input.value = '';
                    }
                  }}
                >
                  {t('common.add')}
                </Button>
              </div>
            </div>
          </div>
        </TabPanel>
        
        <TabPanel>
          {settings && (
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h3 className="text-lg font-medium mb-4">{t('preferences.personalization.title')}</h3>
              <p className="text-gray-500 mb-4">{t('preferences.personalization.description')}</p>
              
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{t('preferences.personalization.enablePersonalization')}</h4>
                    <p className="text-sm text-gray-500">{t('preferences.personalization.enablePersonalizationDescription')}</p>
                  </div>
                  <Switch
                    checked={settings.enablePersonalization}
                    onChange={checked => handleSettingChange('enablePersonalization', checked)}
                  />
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">{t('preferences.personalization.diversityLevel')}</h4>
                  <p className="text-sm text-gray-500 mb-2">{t('preferences.personalization.diversityLevelDescription')}</p>
                  <div className="flex items-center space-x-4">
                    <div className="flex-grow">
                      <Slider
                        min={0}
                        max={100}
                        step={1}
                        value={settings.diversityLevel}
                        onChange={value => handleSettingChange('diversityLevel', value)}
                      />
                    </div>
                    <div className="w-16 text-center">
                      {settings.diversityLevel}%
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">{t('preferences.personalization.noveltyLevel')}</h4>
                  <p className="text-sm text-gray-500 mb-2">{t('preferences.personalization.noveltyLevelDescription')}</p>
                  <div className="flex items-center space-x-4">
                    <div className="flex-grow">
                      <Slider
                        min={0}
                        max={100}
                        step={1}
                        value={settings.noveltyLevel}
                        onChange={value => handleSettingChange('noveltyLevel', value)}
                      />
                    </div>
                    <div className="w-16 text-center">
                      {settings.noveltyLevel}%
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">{t('preferences.personalization.contentFilters')}</h4>
                  
                  <div className="space-y-4">
                    <div>
                      <h5 className="text-sm font-medium mb-1">{t('preferences.personalization.excludedCategories')}</h5>
                      <MultiSelect
                        options={availableCategories.map(category => ({ value: category, label: category }))}
                        value={settings.contentFilters.excludedCategories.map(category => ({ value: category, label: category }))}
                        onChange={selected => handleSettingChange(
                          'contentFilters.excludedCategories',
                          selected.map(option => option.value)
                        )}
                        placeholder={t('preferences.personalization.selectCategories')}
                      />
                    </div>
                    
                    <div>
                      <h5 className="text-sm font-medium mb-1">{t('preferences.personalization.excludedTags')}</h5>
                      <MultiSelect
                        options={availableTags.map(tag => ({ value: tag, label: tag }))}
                        value={settings.contentFilters.excludedTags.map(tag => ({ value: tag, label: tag }))}
                        onChange={selected => handleSettingChange(
                          'contentFilters.excludedTags',
                          selected.map(option => option.value)
                        )}
                        placeholder={t('preferences.personalization.selectTags')}
                      />
                    </div>
                    
                    <div>
                      <h5 className="text-sm font-medium mb-1">{t('preferences.personalization.contentTypes')}</h5>
                      <MultiSelect
                        options={availableContentTypes.map(type => ({ value: type, label: t(`contentTypes.${type.toLowerCase()}`) }))}
                        value={settings.contentFilters.contentTypes.map(type => ({ value: type, label: t(`contentTypes.${type.toLowerCase()}`) }))}
                        onChange={selected => handleSettingChange(
                          'contentFilters.contentTypes',
                          selected.map(option => option.value)
                        )}
                        placeholder={t('preferences.personalization.selectContentTypes')}
                      />
                    </div>
                    
                    <div>
                      <h5 className="text-sm font-medium mb-1">{t('preferences.personalization.minContentRating')}</h5>
                      <div className="flex items-center space-x-4">
                        <div className="flex-grow">
                          <Slider
                            min={0}
                            max={5}
                            step={0.5}
                            value={settings.contentFilters.minContentRating}
                            onChange={value => handleSettingChange('contentFilters.minContentRating', value)}
                          />
                        </div>
                        <div className="w-16 text-center">
                          {settings.contentFilters.minContentRating}
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h5 className="text-sm font-medium mb-1">{t('preferences.personalization.languagePreferences')}</h5>
                      <MultiSelect
                        options={availableLanguages.map(lang => ({ value: lang, label: t(`languages.${lang}`) }))}
                        value={settings.contentFilters.languagePreferences.map(lang => ({ value: lang, label: t(`languages.${lang}`) }))}
                        onChange={selected => handleSettingChange(
                          'contentFilters.languagePreferences',
                          selected.map(option => option.value)
                        )}
                        placeholder={t('preferences.personalization.selectLanguages')}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </TabPanel>
        
        <TabPanel>
          {settings && (
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h3 className="text-lg font-medium mb-4">{t('preferences.privacy.title')}</h3>
              <p className="text-gray-500 mb-4">{t('preferences.privacy.description')}</p>
              
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{t('preferences.privacy.allowInteractionTracking')}</h4>
                    <p className="text-sm text-gray-500">{t('preferences.privacy.allowInteractionTrackingDescription')}</p>
                  </div>
                  <Switch
                    checked={settings.privacySettings.allowInteractionTracking}
                    onChange={checked => handleSettingChange('privacySettings.allowInteractionTracking', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{t('preferences.privacy.allowContentAnalysis')}</h4>
                    <p className="text-sm text-gray-500">{t('preferences.privacy.allowContentAnalysisDescription')}</p>
                  </div>
                  <Switch
                    checked={settings.privacySettings.allowContentAnalysis}
                    onChange={checked => handleSettingChange('privacySettings.allowContentAnalysis', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{t('preferences.privacy.allowThirdPartyData')}</h4>
                    <p className="text-sm text-gray-500">{t('preferences.privacy.allowThirdPartyDataDescription')}</p>
                  </div>
                  <Switch
                    checked={settings.privacySettings.allowThirdPartyData}
                    onChange={checked => handleSettingChange('privacySettings.allowThirdPartyData', checked)}
                  />
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">{t('preferences.privacy.dataRetentionPeriod')}</h4>
                  <p className="text-sm text-gray-500 mb-2">{t('preferences.privacy.dataRetentionPeriodDescription')}</p>
                  <select
                    className="border rounded-md px-3 py-2 w-full"
                    value={settings.privacySettings.dataRetentionPeriod}
                    onChange={e => handleSettingChange('privacySettings.dataRetentionPeriod', parseInt(e.target.value))}
                  >
                    <option value={30}>{t('preferences.privacy.retention.30days')}</option>
                    <option value={90}>{t('preferences.privacy.retention.90days')}</option>
                    <option value={180}>{t('preferences.privacy.retention.180days')}</option>
                    <option value={365}>{t('preferences.privacy.retention.365days')}</option>
                    <option value={730}>{t('preferences.privacy.retention.730days')}</option>
                  </select>
                </div>
                
                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-2">{t('preferences.privacy.dataExport')}</h4>
                  <p className="text-sm text-gray-500 mb-4">{t('preferences.privacy.dataExportDescription')}</p>
                  <div className="flex space-x-2">
                    <Button variant="secondary">
                      {t('preferences.privacy.exportData')}
                    </Button>
                    <Button variant="danger">
                      {t('preferences.privacy.deleteData')}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </TabPanel>
      </Tabs>
      
      <div className="flex justify-end mt-6">
        <Button
          variant="primary"
          onClick={handleSavePreferences}
          disabled={saving}
        >
          {saving ? <Spinner size="sm" /> : t('common.save')}
        </Button>
      </div>
    </FadeIn>
  );
};

export default UserPreferencesPanel;
