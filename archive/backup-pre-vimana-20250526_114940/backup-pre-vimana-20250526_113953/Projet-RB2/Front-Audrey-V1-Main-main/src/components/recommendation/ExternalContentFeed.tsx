import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { userPreferencesService, ExternalContent, ExternalContentQueryParams } from '../../services/api/userPreferencesService';
import { FadeIn } from '../ui/FadeIn';
import { Button } from '../ui/Button';
import { Spinner } from '../ui/Spinner';
import { Alert } from '../ui/Alert';
import { Badge } from '../ui/Badge';
import { MultiSelect } from '../ui/MultiSelect';
import { DatePicker } from '../ui/DatePicker';

interface ExternalContentFeedProps {
  initialLimit?: number;
  showFilters?: boolean;
  onContentClick?: (content: ExternalContent) => void;
}

export const ExternalContentFeed: React.FC<ExternalContentFeedProps> = ({
  initialLimit = 10,
  showFilters = true,
  onContentClick,
}) => {
  const { t } = useTranslation();
  const [content, setContent] = useState<ExternalContent[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ExternalContentQueryParams>({
    limit: initialLimit,
  });
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  const [availableTags, setAvailableTags] = useState<string[]>([]);
  const [availableSources, setAvailableSources] = useState<string[]>([]);
  const [showMoreFilters, setShowMoreFilters] = useState<boolean>(false);

  useEffect(() => {
    fetchExternalContent();
  }, []);

  const fetchExternalContent = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const content = await userPreferencesService.getExternalContent(filters);
      setContent(content);
      
      // Extraire les catégories, tags et sources disponibles
      const categories = new Set<string>();
      const tags = new Set<string>();
      const sources = new Set<string>();
      
      content.forEach(item => {
        item.categories.forEach(category => categories.add(category));
        item.tags.forEach(tag => tags.add(tag));
        sources.add(item.source);
      });
      
      setAvailableCategories(Array.from(categories));
      setAvailableTags(Array.from(tags));
      setAvailableSources(Array.from(sources));
      
      setLoading(false);
    } catch (error) {
      console.error('Error fetching external content:', error);
      setError(t('externalContent.errors.fetchFailed'));
      setLoading(false);
    }
  };

  const handleFilterChange = (key: keyof ExternalContentQueryParams, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleApplyFilters = () => {
    fetchExternalContent();
  };

  const handleResetFilters = () => {
    setFilters({
      limit: initialLimit,
    });
    fetchExternalContent();
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const handleContentClick = (item: ExternalContent) => {
    if (onContentClick) {
      onContentClick(item);
    } else {
      window.open(item.url, '_blank');
    }
  };

  if (loading && content.length === 0) {
    return (
      <div className="flex justify-center py-12">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error && content.length === 0) {
    return (
      <Alert variant="error" title={t('common.error')} message={error} />
    );
  }

  return (
    <FadeIn>
      {showFilters && (
        <div className="bg-white rounded-lg shadow-md p-4 mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
            <h3 className="text-lg font-medium">{t('externalContent.filters.title')}</h3>
            <div className="mt-2 md:mt-0 flex space-x-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setShowMoreFilters(!showMoreFilters)}
              >
                {showMoreFilters ? t('externalContent.filters.less') : t('externalContent.filters.more')}
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={handleResetFilters}
              >
                {t('externalContent.filters.reset')}
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('externalContent.filters.categories')}
              </label>
              <MultiSelect
                options={availableCategories.map(category => ({ value: category, label: category }))}
                value={(filters.categories || []).map(category => ({ value: category, label: category }))}
                onChange={selected => handleFilterChange('categories', selected.map(option => option.value))}
                placeholder={t('externalContent.filters.selectCategories')}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('externalContent.filters.sources')}
              </label>
              <MultiSelect
                options={availableSources.map(source => ({ value: source, label: source }))}
                value={(filters.sources || []).map(source => ({ value: source, label: source }))}
                onChange={selected => handleFilterChange('sources', selected.map(option => option.value))}
                placeholder={t('externalContent.filters.selectSources')}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('externalContent.filters.limit')}
              </label>
              <select
                className="border rounded-md px-3 py-2 w-full"
                value={filters.limit || initialLimit}
                onChange={e => handleFilterChange('limit', parseInt(e.target.value))}
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
              </select>
            </div>
          </div>
          
          {showMoreFilters && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('externalContent.filters.tags')}
                </label>
                <MultiSelect
                  options={availableTags.map(tag => ({ value: tag, label: tag }))}
                  value={(filters.tags || []).map(tag => ({ value: tag, label: tag }))}
                  onChange={selected => handleFilterChange('tags', selected.map(option => option.value))}
                  placeholder={t('externalContent.filters.selectTags')}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('externalContent.filters.minDate')}
                </label>
                <DatePicker
                  selected={filters.minDate ? new Date(filters.minDate) : null}
                  onChange={date => handleFilterChange('minDate', date ? date.toISOString() : undefined)}
                  placeholderText={t('externalContent.filters.selectDate')}
                  maxDate={filters.maxDate ? new Date(filters.maxDate) : undefined}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('externalContent.filters.maxDate')}
                </label>
                <DatePicker
                  selected={filters.maxDate ? new Date(filters.maxDate) : null}
                  onChange={date => handleFilterChange('maxDate', date ? date.toISOString() : undefined)}
                  placeholderText={t('externalContent.filters.selectDate')}
                  minDate={filters.minDate ? new Date(filters.minDate) : undefined}
                />
              </div>
            </div>
          )}
          
          <div className="mt-4 flex justify-end">
            <Button
              variant="primary"
              onClick={handleApplyFilters}
            >
              {t('externalContent.filters.apply')}
            </Button>
          </div>
        </div>
      )}
      
      {content.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <h3 className="text-lg font-medium mb-2">{t('externalContent.noContent.title')}</h3>
          <p className="text-gray-500">{t('externalContent.noContent.description')}</p>
        </div>
      ) : (
        <div className="space-y-6">
          {content.map(item => (
            <div
              key={item.id}
              className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200 cursor-pointer"
              onClick={() => handleContentClick(item)}
            >
              <div className="flex flex-col md:flex-row">
                {item.imageUrl && (
                  <div className="md:w-1/3 h-48 md:h-auto">
                    <img
                      src={item.imageUrl}
                      alt={item.title}
                      className="w-full h-full object-cover"
                      onError={e => {
                        // Fallback image on error
                        (e.target as HTMLImageElement).src = 'https://via.placeholder.com/300x200?text=No+Image';
                      }}
                    />
                  </div>
                )}
                
                <div className={`p-6 ${item.imageUrl ? 'md:w-2/3' : 'w-full'}`}>
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-xl font-bold">{item.title}</h3>
                    <div className="flex items-center">
                      <span className="text-sm text-gray-500 mr-2">{formatDate(item.publishedAt)}</span>
                      <Badge color="blue">{item.source}</Badge>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-4 line-clamp-3">{item.description}</p>
                  
                  <div className="flex flex-wrap gap-2 mb-4">
                    {item.categories.map(category => (
                      <Badge key={`category-${category}`} color="green">{category}</Badge>
                    ))}
                    {item.tags.map(tag => (
                      <Badge key={`tag-${tag}`} color="gray">{tag}</Badge>
                    ))}
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <span className="text-sm text-gray-500 mr-1">{t('externalContent.relevance')}:</span>
                      <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-blue-600"
                          style={{ width: `${Math.round(item.relevanceScore * 100)}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-500 ml-1">{Math.round(item.relevanceScore * 100)}%</span>
                    </div>
                    
                    <Button
                      variant="link"
                      onClick={e => {
                        e.stopPropagation();
                        window.open(item.url, '_blank');
                      }}
                    >
                      {t('externalContent.readMore')}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
          
          {content.length >= (filters.limit || initialLimit) && (
            <div className="flex justify-center mt-6">
              <Button
                variant="secondary"
                onClick={() => {
                  const newLimit = (filters.limit || initialLimit) + 10;
                  handleFilterChange('limit', newLimit);
                  fetchExternalContent();
                }}
              >
                {t('externalContent.loadMore')}
              </Button>
            </div>
          )}
        </div>
      )}
    </FadeIn>
  );
};

export default ExternalContentFeed;
