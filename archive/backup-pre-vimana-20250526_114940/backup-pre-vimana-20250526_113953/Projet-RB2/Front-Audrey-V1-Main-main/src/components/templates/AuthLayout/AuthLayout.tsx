import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

interface AuthLayoutProps {
  children: React.ReactNode;
  className?: string;
  title: string;
  subtitle?: string;
  showFooter?: boolean;
  backgroundImage?: string;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({
  children,
  className = '',
  title,
  subtitle,
  showFooter = true,
  backgroundImage,
}) => {
  return (
    <div className='min-h-screen flex'>
      {/* Left side - Form */}
      <div className='flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24'>
        <div className='mx-auto w-full max-w-sm lg:w-96'>
          {/* Logo */}
          <div className='mb-8'>
            <Link to='/' className='inline-block'>
              <img src='/logo.svg' alt='Retreat & Be' className='h-8 w-auto' />
            </Link>
          </div>

          {/* Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className='text-3xl font-bold text-gray-900 mb-2'>{title}</h2>
            {subtitle && <p className='text-gray-600 mb-8'>{subtitle}</p>}

            <div className={className}>{children}</div>
          </motion.div>

          {/* Footer */}
          {showFooter && (
            <div className='mt-8 text-center text-sm text-gray-600'>
              <p>
                En continuant, vous acceptez nos{' '}
                <Link to='/terms' className='text-retreat-green hover:text-retreat-green-dark'>
                  conditions d'utilisation
                </Link>{' '}
                et notre{' '}
                <Link to='/privacy' className='text-retreat-green hover:text-retreat-green-dark'>
                  politique de confidentialité
                </Link>
                .
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Right side - Background */}
      {backgroundImage && (
        <div className='hidden lg:block relative w-0 flex-1'>
          <img
            src={backgroundImage}
            alt=''
            className='absolute inset-0 h-full w-full object-cover'
          />
          <div className='absolute inset-0 bg-gradient-to-r from-white/90 to-white/50' />
        </div>
      )}
    </div>
  );
};

export default AuthLayout;
