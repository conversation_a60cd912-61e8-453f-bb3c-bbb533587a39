# Templates

Les templates sont des structures de page qui définissent la mise en page et l'organisation des organismes, molécules et atomes. Ils servent de squelettes pour les pages finales.

## Structure des dossiers

- `HomeTemplate/` : Template de la page d'accueil

  - `HomeTemplate.tsx` : Structure de la page d'accueil

- `SearchTemplate/` : Template de la page de recherche

  - `SearchTemplate.tsx` : Structure de la page de recherche

- `BlogTemplate/` : Template de la page blog

  - `BlogTemplate.tsx` : Structure de la page blog

- `RetreatTemplate/` : Template de la page retraite
  - `RetreatTemplate.tsx` : Structure de la page retraite

## Règles d'utilisation

1. Les templates doivent :

   - Définir la structure de base des pages
   - Être composés d'organismes
   - Ne pas contenir de données réelles
   - Être réutilisables pour différentes pages

2. Chaque template doit :

   - Avoir une structure claire et cohérente
   - Être documenté avec des props typées
   - Inclure des placeholders pour les données
   - Suivre les conventions de nommage

3. Bonnes pratiques :
   - Maintenir une cohérence visuelle
   - Utiliser des composants réutilisables
   - Prévoir des espaces pour le contenu dynamique
   - Documenter les cas d'utilisation
   - Implémenter une gestion responsive
