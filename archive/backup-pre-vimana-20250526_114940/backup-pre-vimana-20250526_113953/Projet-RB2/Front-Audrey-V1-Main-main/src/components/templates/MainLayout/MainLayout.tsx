import React from 'react';
import NavBarClient from '../../organisms/NavBarClient/_NavBarClient';
import Footer from '../../organisms/Footer/Footer';
import NotificationCenter from '../../organisms/NotificationCenter/NotificationCenter';

interface MainLayoutProps {
  children: React.ReactNode;
  className?: string;
  showFooter?: boolean;
  showNotifications?: boolean;
}

const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  className = '',
  showFooter = true,
  showNotifications = true,
}) => {
  return (
    <div className='min-h-screen flex flex-col'>
      <NavBarClient />

      <main className={`flex-grow ${className}`}>{children}</main>

      {showFooter && <Footer />}

      {showNotifications && <NotificationCenter position='top-right' maxNotifications={5} />}
    </div>
  );
};

export default MainLayout;
