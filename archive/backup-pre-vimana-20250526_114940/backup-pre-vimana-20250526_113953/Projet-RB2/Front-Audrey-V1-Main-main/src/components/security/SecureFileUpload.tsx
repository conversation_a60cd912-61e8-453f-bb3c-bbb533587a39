import React, { useState, useRef } from 'react';
import { useSecurity } from '../../contexts/SecurityContext';

interface SecureFileUploadProps {
  onFileUploaded?: (file: File, secureUrl: string) => void;
  allowedTypes?: string[];
  maxSizeMB?: number;
  label?: string;
  className?: string;
}

const SecureFileUpload: React.FC<SecureFileUploadProps> = ({
  onFileUploaded,
  allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
  maxSizeMB = 10,
  label = 'Télécharger un fichier',
  className = '',
}) => {
  const { validateFile, isLoading, error } = useSecurity();
  const [file, setFile] = useState<File | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (!selectedFile) return;

    // Réinitialiser les états
    setFile(selectedFile);
    setValidationError(null);
    setUploadProgress(0);
    setUploadSuccess(false);

    // Validation côté client basique
    if (!allowedTypes.includes(selectedFile.type)) {
      setValidationError(
        `Type de fichier non autorisé. Types autorisés: ${allowedTypes.join(', ')}`
      );
      return;
    }

    if (selectedFile.size > maxSizeMB * 1024 * 1024) {
      setValidationError(`La taille du fichier dépasse la limite de ${maxSizeMB} MB`);
      return;
    }

    // Validation de sécurité côté serveur
    setIsValidating(true);
    try {
      const validationResult = await validateFile(selectedFile);
      if (!validationResult.valid) {
        setValidationError(validationResult.reason || 'Validation du fichier échouée');
        setFile(null);
      }
    } catch (_err) {
      setValidationError('Erreur lors de la validation du fichier');
      setFile(null);
    } finally {
      setIsValidating(false);
    }
  };

  const simulateUpload = async () => {
    if (!file) return;

    setIsUploading(true);
    setUploadProgress(0);

    // Simuler une progression d'upload
    const interval = setInterval(() => {
      setUploadProgress((prev) => {
        const newProgress = prev + 10;
        if (newProgress >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            setUploadSuccess(true);
            setIsUploading(false);
            // Appeler le callback avec le fichier et une URL simulée
            if (onFileUploaded) {
              const secureUrl = URL.createObjectURL(file);
              onFileUploaded(file, secureUrl);
            }
          }, 500);
          return 100;
        }
        return newProgress;
      });
    }, 300);
  };

  const resetUpload = () => {
    setFile(null);
    setValidationError(null);
    setUploadProgress(0);
    setIsUploading(false);
    setUploadSuccess(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow p-4 ${className}`}>
      <div className='mb-4'>
        <label className='block text-sm font-medium text-gray-700 mb-2'>{label}</label>
        <div className='flex items-center justify-center w-full'>
          <label
            className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer ${
              validationError
                ? 'border-red-300 bg-red-50'
                : file
                  ? 'border-green-300 bg-green-50'
                  : 'border-gray-300 bg-gray-50'
            } hover:bg-gray-100`}
          >
            <div className='flex flex-col items-center justify-center pt-5 pb-6'>
              {!file ? (
                <>
                  <svg
                    className='w-8 h-8 mb-4 text-gray-500'
                    aria-hidden='true'
                    xmlns='http://www.w3.org/2000/svg'
                    fill='none'
                    viewBox='0 0 20 16'
                  >
                    <path
                      stroke='currentColor'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth='2'
                      d='M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2'
                    />
                  </svg>
                  <p className='mb-2 text-sm text-gray-500'>
                    <span className='font-semibold'>Cliquez pour télécharger</span> ou
                    glissez-déposez
                  </p>
                  <p className='text-xs text-gray-500'>
                    {allowedTypes.join(', ')} (Max: {maxSizeMB} MB)
                  </p>
                </>
              ) : (
                <>
                  <svg
                    className='w-8 h-8 mb-4 text-green-500'
                    aria-hidden='true'
                    xmlns='http://www.w3.org/2000/svg'
                    fill='none'
                    viewBox='0 0 20 20'
                  >
                    <path
                      stroke='currentColor'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth='2'
                      d='M10 2a8 8 0 100 16 8 8 0 000-16z'
                    />
                    <path
                      stroke='currentColor'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth='2'
                      d='M7 10l2 2 4-4'
                    />
                  </svg>
                  <p className='mb-2 text-sm text-gray-700 font-medium'>{file.name}</p>
                  <p className='text-xs text-gray-500'>{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </>
              )}
            </div>
            <input
              ref={fileInputRef}
              type='file'
              className='hidden'
              onChange={handleFileChange}
              accept={allowedTypes.join(',')}
              disabled={isValidating || isUploading}
            />
          </label>
        </div>
      </div>

      {validationError && (
        <div className='mb-4 p-2 bg-red-100 border border-red-400 text-red-700 rounded'>
          <p className='text-sm'>{validationError}</p>
        </div>
      )}

      {error && (
        <div className='mb-4 p-2 bg-red-100 border border-red-400 text-red-700 rounded'>
          <p className='text-sm'>{error}</p>
        </div>
      )}

      {isValidating && (
        <div className='mb-4 flex items-center'>
          <svg
            className='animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500'
            xmlns='http://www.w3.org/2000/svg'
            fill='none'
            viewBox='0 0 24 24'
          >
            <circle
              className='opacity-25'
              cx='12'
              cy='12'
              r='10'
              stroke='currentColor'
              strokeWidth='4'
            ></circle>
            <path
              className='opacity-75'
              fill='currentColor'
              d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
            ></path>
          </svg>
          <span className='text-sm text-gray-700'>Validation du fichier en cours...</span>
        </div>
      )}

      {file && !validationError && !isValidating && (
        <>
          {isUploading && (
            <div className='mb-4'>
              <div className='w-full bg-gray-200 rounded-full h-2.5'>
                <div
                  className='bg-blue-600 h-2.5 rounded-full'
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <p className='text-xs text-gray-500 mt-1'>Téléchargement: {uploadProgress}%</p>
            </div>
          )}

          {uploadSuccess ? (
            <div className='mb-4 p-2 bg-green-100 border border-green-400 text-green-700 rounded'>
              <p className='text-sm'>Fichier téléchargé avec succès!</p>
            </div>
          ) : (
            <div className='flex space-x-2'>
              <button
                type='button'
                onClick={simulateUpload}
                disabled={isLoading || isUploading}
                className='px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50'
              >
                Télécharger
              </button>
              <button
                type='button'
                onClick={resetUpload}
                className='px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2'
              >
                Annuler
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default SecureFileUpload;
