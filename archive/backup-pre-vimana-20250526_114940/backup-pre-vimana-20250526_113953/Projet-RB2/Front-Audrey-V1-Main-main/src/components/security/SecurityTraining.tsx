import React, { useState, useEffect } from 'react';
import { useSecurity } from '../../contexts/SecurityContext';

interface Simulation {
  id: string;
  title: string;
  description: string;
  type: 'PHISHING' | 'PASSWORD' | 'DATA_HANDLING';
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  completed: boolean;
  completedAt: string | null;
  score: number | null;
  attempts: number;
}

interface SimulationCardProps {
  simulation: Simulation;
  onStart: (simulation: Simulation) => void;
}

const SimulationCard: React.FC<SimulationCardProps> = ({ simulation, onStart }) => {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'bg-green-100 text-green-800';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800';
      case 'HARD':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'PHISHING':
        return (
          <svg
            className='w-6 h-6 text-blue-500'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z'
            />
          </svg>
        );
      case 'PASSWORD':
        return (
          <svg
            className='w-6 h-6 text-purple-500'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
            />
          </svg>
        );
      case 'DATA_HANDLING':
        return (
          <svg
            className='w-6 h-6 text-green-500'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
            />
          </svg>
        );
      default:
        return (
          <svg
            className='w-6 h-6 text-gray-500'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z'
            />
          </svg>
        );
    }
  };

  return (
    <div className='bg-white rounded-lg shadow-md overflow-hidden'>
      <div className='p-4'>
        <div className='flex items-center justify-between mb-2'>
          <div className='flex items-center'>
            {getTypeIcon(simulation.type)}
            <span
              className={`ml-2 px-2 py-1 text-xs font-medium rounded ${getDifficultyColor(
                simulation.difficulty
              )}`}
            >
              {simulation.difficulty}
            </span>
          </div>
          {simulation.completed && (
            <div className='flex items-center text-green-600'>
              <svg
                className='w-5 h-5 mr-1'
                fill='currentColor'
                viewBox='0 0 20 20'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  fillRule='evenodd'
                  d='M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z'
                  clipRule='evenodd'
                />
              </svg>
              <span className='text-sm font-medium'>Complété</span>
            </div>
          )}
        </div>
        <h3 className='text-lg font-semibold text-gray-800 mb-1'>{simulation.title}</h3>
        <p className='text-sm text-gray-600 mb-4'>{simulation.description}</p>
        <div className='flex justify-between items-center'>
          {simulation.completed ? (
            <div className='text-sm text-gray-600'>
              Score: <span className='font-medium'>{simulation.score}%</span>
            </div>
          ) : (
            <div className='text-sm text-gray-600'>
              Tentatives: <span className='font-medium'>{simulation.attempts}</span>
            </div>
          )}
          <button
            onClick={() => onStart(simulation)}
            className='px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2'
          >
            {simulation.completed ? 'Refaire' : 'Commencer'}
          </button>
        </div>
      </div>
    </div>
  );
};

const SecurityTraining: React.FC = () => {
  const { isLoading, error } = useSecurity();
  const [simulations, setSimulations] = useState<Simulation[]>([]);

  // Fetch simulations from API
  useEffect(() => {
    // This is a mock implementation
    // In a real application, this would fetch data from the API
    const mockSimulations: Simulation[] = [
      {
        id: '1',
        title: 'Reconnaître les emails de phishing',
        description:
          'Apprenez à identifier les tentatives de phishing pour protéger vos informations personnelles.',
        type: 'PHISHING',
        difficulty: 'EASY',
        completed: true,
        completedAt: '2023-05-15T10:30:00Z',
        score: 85,
        attempts: 1,
      },
      {
        id: '2',
        title: 'Créer des mots de passe forts',
        description:
          'Découvrez comment créer et gérer des mots de passe sécurisés pour vos comptes en ligne.',
        type: 'PASSWORD',
        difficulty: 'MEDIUM',
        completed: false,
        completedAt: null,
        score: null,
        attempts: 0,
      },
      {
        id: '3',
        title: 'Manipulation sécurisée des données sensibles',
        description:
          'Apprenez les bonnes pratiques pour manipuler et partager des données sensibles en toute sécurité.',
        type: 'DATA_HANDLING',
        difficulty: 'HARD',
        completed: false,
        completedAt: null,
        score: null,
        attempts: 2,
      },
    ];

    setSimulations(mockSimulations);
  }, []);

  const handleStartSimulation = (simulation: Simulation) => {
    // In a real application, this would call the API to start the simulation
    console.log(`Starting simulation: ${simulation.id}`);
  };

  if (isLoading) {
    return (
      <div className='flex justify-center items-center h-64'>
        <div className='animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500'></div>
      </div>
    );
  }

  return (
    <div className='bg-white rounded-lg shadow p-6'>
      <h2 className='text-2xl font-bold text-gray-900 mb-6'>Formation à la sécurité</h2>

      {error && (
        <div className='bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6'>
          <p>{error}</p>
        </div>
      )}

      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
        {simulations.map((simulation) => (
          <SimulationCard
            key={simulation.id}
            simulation={simulation}
            onStart={handleStartSimulation}
          />
        ))}
      </div>

      {simulations.length === 0 && !isLoading && (
        <div className='text-center py-12 text-gray-500'>
          Aucune simulation de formation disponible pour le moment.
        </div>
      )}
    </div>
  );
};

export default SecurityTraining;
