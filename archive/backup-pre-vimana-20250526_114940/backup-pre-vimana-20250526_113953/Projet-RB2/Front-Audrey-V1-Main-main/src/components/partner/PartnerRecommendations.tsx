import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { matchingRecommendationService } from '../../services/api/matchingRecommendationService';
import { MatchingResult } from '../../services/api/matchingService';
import LoadingSpinner from '../atoms/LoadingSpinner/LoadingSpinner';

interface PartnerRecommendationsProps {
  partnerId: string;
}

const PartnerRecommendations: React.FC<PartnerRecommendationsProps> = ({ partnerId }) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [recommendations, setRecommendations] = useState<MatchingResult[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadRecommendations = async () => {
      try {
        setIsLoading(true);
        
        // Charger les recommandations pour le partenaire
        const recommendationsData = await matchingRecommendationService.getPartnerRecommendations(partnerId);
        setRecommendations(recommendationsData);
        
        setError(null);
      } catch (error) {
        console.error('Erreur lors du chargement des recommandations:', error);
        setError('Impossible de charger les recommandations');
      } finally {
        setIsLoading(false);
      }
    };

    if (partnerId) {
      loadRecommendations();
    }
  }, [partnerId]);

  // Formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    }).format(date);
  };

  // Fonction pour obtenir la couleur en fonction du score
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-500';
    return 'text-red-600';
  };

  // Fonction pour obtenir la couleur de fond en fonction du score
  const getScoreBackgroundColor = (score: number) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    if (score >= 40) return 'bg-orange-100';
    return 'bg-red-100';
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-8">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">{error}</h3>
          <p className="mt-1 text-sm text-gray-500">
            Veuillez réessayer ultérieurement.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-gray-900">
            Retraites recommandées pour vous
          </h2>
          <Link
            to="/partners/matching"
            className="text-sm text-retreat-green hover:text-retreat-green-dark"
          >
            Voir toutes les opportunités
          </Link>
        </div>

        {recommendations.length === 0 ? (
          <div className="text-center py-8">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              Aucune retraite recommandée pour le moment
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              Nous vous enverrons des recommandations dès que nous trouverons des retraites qui correspondent à votre profil.
            </p>
          </div>
        ) : (
          <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table className="min-w-full divide-y divide-gray-300">
              <thead className="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                  >
                    Retraite
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                  >
                    Dates
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                  >
                    Lieu
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                  >
                    Compatibilité
                  </th>
                  <th
                    scope="col"
                    className="relative py-3.5 pl-3 pr-4 sm:pr-6"
                  >
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {recommendations.map((result) => (
                  <tr key={result.retreatId}>
                    <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                      <div className="font-medium text-gray-900">
                        {result.retreat?.title}
                      </div>
                      <div className="text-gray-500 truncate max-w-xs">
                        {result.retreat?.description?.substring(0, 60)}...
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      <div>Du {formatDate(result.retreat?.startDate || '')}</div>
                      <div>au {formatDate(result.retreat?.endDate || '')}</div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      {result.retreat?.location}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm">
                      <div className={`font-medium ${getScoreColor(result.score)}`}>
                        {result.score}%
                      </div>
                    </td>
                    <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <Link
                        to={`/matching/details/${partnerId}/${result.retreatId}`}
                        className="text-retreat-green hover:text-retreat-green-dark"
                      >
                        Voir détails
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default PartnerRecommendations;
