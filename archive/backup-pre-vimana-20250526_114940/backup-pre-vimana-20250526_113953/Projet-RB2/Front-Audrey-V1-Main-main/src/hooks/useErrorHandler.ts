import { useState, useCallback } from 'react';
import { errorService, FormattedError, ErrorType } from '../services/errorService';
import { useAuthContext } from './useAuthContext';

interface UseErrorHandlerReturn {
  error: FormattedError | null;
  setError: (error: any) => void;
  clearError: () => void;
  handleError: (error: any) => void;
  errorMessage: string | null;
}

/**
 * Hook pour gérer les erreurs de manière cohérente
 * @returns Méthodes et état pour la gestion des erreurs
 */
export const useErrorHandler = (): UseErrorHandlerReturn => {
  const [error, setFormattedError] = useState<FormattedError | null>(null);
  const { logout } = useAuthContext();

  /**
   * Définir une erreur
   * @param error Erreur à définir
   */
  const setError = useCallback((error: any) => {
    const formattedError = errorService.formatError(error);
    setFormattedError(formattedError);
    errorService.logError(formattedError);
  }, []);

  /**
   * Effacer l'erreur actuelle
   */
  const clearError = useCallback(() => {
    setFormattedError(null);
  }, []);

  /**
   * Gérer une erreur avec des actions spécifiques en fonction du type
   * @param error Erreur à gérer
   */
  const handleError = useCallback(
    (error: any) => {
      const formattedError = errorService.formatError(error);

      // Journaliser l'erreur
      errorService.logError(formattedError);

      // Définir l'erreur dans l'état
      setFormattedError(formattedError);

      // Actions spécifiques en fonction du type d'erreur
      switch (formattedError.type) {
        case ErrorType.AUTHENTICATION:
          // Déconnecter l'utilisateur en cas d'erreur d'authentification
          logout();
          break;

        case ErrorType.NETWORK:
          // Afficher une notification pour les erreurs réseau
          // toast.error('Problème de connexion au serveur. Veuillez vérifier votre connexion internet.');
          console.error('Network error:', formattedError.message);
          break;

        case ErrorType.SERVER:
          // Afficher une notification pour les erreurs serveur
          // toast.error('Une erreur est survenue sur le serveur. Veuillez réessayer ultérieurement.');
          console.error('Server error:', formattedError.message);
          break;

        default:
          // Pas d'action spécifique pour les autres types d'erreurs
          break;
      }
    },
    [logout]
  );

  // Calculer le message d'erreur convivial
  const errorMessage = error ? errorService.getUserFriendlyMessage(error) : null;

  return {
    error,
    setError,
    clearError,
    handleError,
    errorMessage,
  };
};
