import { useState, useCallback } from 'react';
import {
  bookingService,
  Booking,
  BookingFilters,
  CreateBookingRequest,
  UpdateBookingRequest,
  PaginatedBookings,
} from '../../services/api';

interface UseBookingsReturn {
  bookings: Booking[];
  totalBookings: number;
  currentPage: number;
  totalPages: number;
  isLoading: boolean;
  error: string | null;
  getMyBookings: (filters?: BookingFilters) => Promise<PaginatedBookings>;
  getBookingById: (id: string) => Promise<Booking>;
  createBooking: (bookingData: CreateBookingRequest) => Promise<Booking>;
  updateBooking: (id: string, bookingData: UpdateBookingRequest) => Promise<Booking>;
  cancelBooking: (id: string) => Promise<Booking>;
  getBookingsForRetreat: (
    retreatId: string,
    filters?: BookingFilters
  ) => Promise<PaginatedBookings>;
  confirmBooking: (id: string) => Promise<Booking>;
}

export const useBookings = (): UseBookingsReturn => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [totalBookings, setTotalBookings] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const getMyBookings = useCallback(
    async (filters: BookingFilters = {}): Promise<PaginatedBookings> => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await bookingService.getMyBookings(filters);
        setBookings(response.data);
        setTotalBookings(response.total);
        setCurrentPage(response.page);
        setTotalPages(response.totalPages);
        return response;
      } catch (err: any) {
        setError(err.message || 'Une erreur est survenue lors de la récupération des réservations');
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const getBookingById = useCallback(async (id: string): Promise<Booking> => {
    setIsLoading(true);
    setError(null);
    try {
      return await bookingService.getBookingById(id);
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue lors de la récupération de la réservation');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const createBooking = useCallback(async (bookingData: CreateBookingRequest): Promise<Booking> => {
    setIsLoading(true);
    setError(null);
    try {
      const newBooking = await bookingService.createBooking(bookingData);
      // Mettre à jour la liste des réservations si nécessaire
      setBookings((prevBookings) => [newBooking, ...prevBookings]);
      setTotalBookings((prev) => prev + 1);
      return newBooking;
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue lors de la création de la réservation');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateBooking = useCallback(
    async (id: string, bookingData: UpdateBookingRequest): Promise<Booking> => {
      setIsLoading(true);
      setError(null);
      try {
        const updatedBooking = await bookingService.updateBooking(id, bookingData);
        // Mettre à jour la liste des réservations si nécessaire
        setBookings((prevBookings) =>
          prevBookings.map((booking) => (booking.id === id ? updatedBooking : booking))
        );
        return updatedBooking;
      } catch (err: any) {
        setError(err.message || 'Une erreur est survenue lors de la mise à jour de la réservation');
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const cancelBooking = useCallback(async (id: string): Promise<Booking> => {
    setIsLoading(true);
    setError(null);
    try {
      const cancelledBooking = await bookingService.cancelBooking(id);
      // Mettre à jour la liste des réservations
      setBookings((prevBookings) =>
        prevBookings.map((booking) => (booking.id === id ? cancelledBooking : booking))
      );
      return cancelledBooking;
    } catch (err: any) {
      setError(err.message || "Une erreur est survenue lors de l'annulation de la réservation");
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getBookingsForRetreat = useCallback(
    async (retreatId: string, filters: BookingFilters = {}): Promise<PaginatedBookings> => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await bookingService.getBookingsForRetreat(retreatId, filters);
        return response;
      } catch (err: any) {
        setError(
          err.message ||
            'Une erreur est survenue lors de la récupération des réservations pour cette retraite'
        );
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const confirmBooking = useCallback(async (id: string): Promise<Booking> => {
    setIsLoading(true);
    setError(null);
    try {
      const confirmedBooking = await bookingService.confirmBooking(id);
      // Mettre à jour la liste des réservations
      setBookings((prevBookings) =>
        prevBookings.map((booking) => (booking.id === id ? confirmedBooking : booking))
      );
      return confirmedBooking;
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue lors de la confirmation de la réservation');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    bookings,
    totalBookings,
    currentPage,
    totalPages,
    isLoading,
    error,
    getMyBookings,
    getBookingById,
    createBooking,
    updateBooking,
    cancelBooking,
    getBookingsForRetreat,
    confirmBooking,
  };
};
