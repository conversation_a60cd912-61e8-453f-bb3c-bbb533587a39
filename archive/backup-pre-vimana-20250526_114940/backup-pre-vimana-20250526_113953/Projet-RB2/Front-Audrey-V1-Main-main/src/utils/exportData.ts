/**
 * Utilitaires pour l'exportation de données
 */

/**
 * Convertit un objet JavaScript en CSV
 * @param data Tableau d'objets à convertir
 * @param headers En-têtes personnalisés (facultatif)
 * @returns Chaîne CSV
 */
export const convertToCSV = (data: any[], headers?: { [key: string]: string }): string => {
  if (!data || !data.length) {
    return '';
  }

  // Obtenir les clés de l'objet (colonnes)
  const keys = Object.keys(data[0]);
  
  // Créer la ligne d'en-tête
  const headerRow = headers 
    ? keys.map(key => headers[key] || key).join(',')
    : keys.join(',');
  
  // Créer les lignes de données
  const rows = data.map(item => {
    return keys.map(key => {
      const value = item[key];
      
      // Gérer les valeurs spéciales
      if (value === null || value === undefined) {
        return '';
      }
      
      // Échapper les virgules et les guillemets
      const valueStr = String(value);
      if (valueStr.includes(',') || valueStr.includes('"') || valueStr.includes('\n')) {
        return `"${valueStr.replace(/"/g, '""')}"`;
      }
      
      return valueStr;
    }).join(',');
  }).join('\n');
  
  // Combiner l'en-tête et les lignes
  return `${headerRow}\n${rows}`;
};

/**
 * Convertit un objet JavaScript en JSON
 * @param data Données à convertir
 * @returns Chaîne JSON
 */
export const convertToJSON = (data: any): string => {
  return JSON.stringify(data, null, 2);
};

/**
 * Convertit un objet JavaScript en XML
 * @param data Données à convertir
 * @param rootName Nom de l'élément racine
 * @param itemName Nom des éléments d'élément
 * @returns Chaîne XML
 */
export const convertToXML = (data: any[], rootName: string = 'data', itemName: string = 'item'): string => {
  if (!data || !data.length) {
    return `<?xml version="1.0" encoding="UTF-8"?>\n<${rootName}></${rootName}>`;
  }
  
  // Fonction pour convertir un objet en XML
  const objectToXML = (obj: any, name: string): string => {
    if (obj === null || obj === undefined) {
      return `<${name}></${name}>`;
    }
    
    if (typeof obj !== 'object') {
      // Échapper les caractères spéciaux XML
      const escapedValue = String(obj)
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&apos;');
      
      return `<${name}>${escapedValue}</${name}>`;
    }
    
    if (Array.isArray(obj)) {
      return `<${name}>${obj.map((item, index) => objectToXML(item, `item_${index}`)).join('')}</${name}>`;
    }
    
    const properties = Object.keys(obj).map(key => objectToXML(obj[key], key)).join('');
    return `<${name}>${properties}</${name}>`;
  };
  
  // Convertir chaque élément du tableau
  const items = data.map(item => objectToXML(item, itemName)).join('');
  
  // Créer le document XML
  return `<?xml version="1.0" encoding="UTF-8"?>\n<${rootName}>${items}</${rootName}>`;
};

/**
 * Télécharge des données sous forme de fichier
 * @param data Données à télécharger
 * @param filename Nom du fichier
 * @param type Type MIME
 */
export const downloadFile = (data: string, filename: string, type: string): void => {
  const blob = new Blob([data], { type });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  
  // Ajouter le lien au document
  document.body.appendChild(link);
  
  // Cliquer sur le lien
  link.click();
  
  // Nettoyer
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

/**
 * Exporte des données au format CSV
 * @param data Données à exporter
 * @param filename Nom du fichier
 * @param headers En-têtes personnalisés (facultatif)
 */
export const exportToCSV = (data: any[], filename: string, headers?: { [key: string]: string }): void => {
  const csv = convertToCSV(data, headers);
  downloadFile(csv, `${filename}.csv`, 'text/csv;charset=utf-8;');
};

/**
 * Exporte des données au format JSON
 * @param data Données à exporter
 * @param filename Nom du fichier
 */
export const exportToJSON = (data: any, filename: string): void => {
  const json = convertToJSON(data);
  downloadFile(json, `${filename}.json`, 'application/json;charset=utf-8;');
};

/**
 * Exporte des données au format XML
 * @param data Données à exporter
 * @param filename Nom du fichier
 * @param rootName Nom de l'élément racine
 * @param itemName Nom des éléments d'élément
 */
export const exportToXML = (data: any[], filename: string, rootName: string = 'data', itemName: string = 'item'): void => {
  const xml = convertToXML(data, rootName, itemName);
  downloadFile(xml, `${filename}.xml`, 'application/xml;charset=utf-8;');
};

/**
 * Exporte des données au format Excel (XLSX)
 * Cette fonction nécessite la bibliothèque xlsx
 * @param data Données à exporter
 * @param filename Nom du fichier
 * @param sheetName Nom de la feuille
 */
export const exportToExcel = async (data: any[], filename: string, sheetName: string = 'Sheet1'): Promise<void> => {
  try {
    // Importer dynamiquement la bibliothèque xlsx
    const XLSX = await import('xlsx');
    
    // Créer un classeur
    const workbook = XLSX.utils.book_new();
    
    // Créer une feuille de calcul
    const worksheet = XLSX.utils.json_to_sheet(data);
    
    // Ajouter la feuille au classeur
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    
    // Générer le fichier XLSX
    XLSX.writeFile(workbook, `${filename}.xlsx`);
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    
    // Fallback to CSV if XLSX export fails
    exportToCSV(data, filename);
  }
};

/**
 * Exporte des données au format PDF
 * Cette fonction nécessite la bibliothèque jspdf et jspdf-autotable
 * @param data Données à exporter
 * @param filename Nom du fichier
 * @param title Titre du document
 * @param headers En-têtes personnalisés (facultatif)
 */
export const exportToPDF = async (
  data: any[],
  filename: string,
  title: string = '',
  headers?: { [key: string]: string }
): Promise<void> => {
  try {
    // Importer dynamiquement les bibliothèques
    const jsPDF = (await import('jspdf')).default;
    const autoTable = (await import('jspdf-autotable')).default;
    
    // Créer un nouveau document PDF
    const doc = new jsPDF();
    
    // Ajouter un titre
    if (title) {
      doc.text(title, 14, 15);
    }
    
    // Obtenir les clés de l'objet (colonnes)
    const keys = Object.keys(data[0]);
    
    // Créer les en-têtes
    const headerRow = headers 
      ? keys.map(key => headers[key] || key)
      : keys;
    
    // Créer les lignes de données
    const rows = data.map(item => keys.map(key => item[key]));
    
    // Ajouter la table au document
    autoTable(doc, {
      head: [headerRow],
      body: rows,
      startY: title ? 20 : 10,
    });
    
    // Enregistrer le document
    doc.save(`${filename}.pdf`);
  } catch (error) {
    console.error('Error exporting to PDF:', error);
    
    // Fallback to CSV if PDF export fails
    exportToCSV(data, filename, headers);
  }
};
