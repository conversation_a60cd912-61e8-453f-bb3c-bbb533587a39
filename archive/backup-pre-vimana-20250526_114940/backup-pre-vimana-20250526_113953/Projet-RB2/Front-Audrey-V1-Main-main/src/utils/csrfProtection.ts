/**
 * Utilitaire pour la protection CSRF (Cross-Site Request Forgery)
 *
 * Cette protection est particulièrement importante si l'application utilise
 * des cookies pour l'authentification. Pour les applications utilisant uniquement
 * des tokens JWT stockés en local, le risque CSRF est moins important.
 */

// Déclaration d'interface pour étendre l'objet window avec axios
interface AxiosRequestConfig {
  method?: string;
  headers?: Record<string, string>;
}

interface AxiosInterceptor {
  request: {
    use: (
      onFulfilled: (config: AxiosRequestConfig) => AxiosRequestConfig | Promise<AxiosRequestConfig>,
      onRejected?: (error: unknown) => Promise<unknown>
    ) => void;
  };
}

interface WindowWithAxios extends Window {
  axios?: {
    interceptors: AxiosInterceptor;
  };
}

/**
 * Génère un token CSRF aléatoire
 * @returns {string} Un token CSRF
 */
export const generateCSRFToken = (): string => {
  // Génération d'un token aléatoire
  const array = new Uint8Array(32);
  window.crypto.getRandomValues(array);
  return Array.from(array, (byte) => ('0' + (byte & 0xff).toString(16)).slice(-2)).join('');
};

/**
 * Stocke le token CSRF dans le localStorage et/ou un cookie avec les attributs de sécurité
 * @param {string} token - Le token CSRF à stocker
 */
export const storeCSRFToken = (token: string): void => {
  // Stockage dans localStorage pour un accès facile via JavaScript
  localStorage.setItem('csrf_token', token);

  // Stockage dans un cookie avec des attributs de sécurité
  // (utile pour les requêtes automatiquement envoyées avec les cookies)
  document.cookie = `csrf_token=${token}; path=/; SameSite=Strict; Secure${
    window.location.protocol === 'https:' ? '; Secure' : ''
  }`;
};

/**
 * Récupère le token CSRF actuel
 * @returns {string|null} Le token CSRF ou null s'il n'existe pas
 */
export const getCSRFToken = (): string | null => {
  return localStorage.getItem('csrf_token');
};

/**
 * Ajoute le token CSRF à tous les formulaires de la page
 */
export const injectCSRFTokenToForms = (): void => {
  const token = getCSRFToken();
  if (!token) return;

  // Itérer sur tous les formulaires de la page
  document.querySelectorAll('form').forEach((form) => {
    // Vérifier si le formulaire n'a pas déjà un champ CSRF
    if (!form.querySelector('input[name="csrf_token"]')) {
      // Créer un champ caché pour le token CSRF
      const csrfInput = document.createElement('input');
      csrfInput.type = 'hidden';
      csrfInput.name = 'csrf_token';
      csrfInput.value = token;

      // Ajouter le champ au formulaire
      form.appendChild(csrfInput);
    }
  });
};

/**
 * Configure l'intercepteur pour ajouter le token CSRF à toutes les requêtes fetch/axios
 */
export const setupCSRFInterceptor = (): void => {
  // Intercepter les requêtes fetch
  const originalFetch = window.fetch;
  window.fetch = function (input, init = {}) {
    // Ajouter le token CSRF à l'en-tête pour les requêtes mutatives
    const method = init.method?.toUpperCase() || 'GET';
    if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
      const token = getCSRFToken();
      if (token) {
        // Créer ou modifier les en-têtes pour inclure le token CSRF
        const headers = new Headers(init.headers || {});
        headers.set('X-CSRF-Token', token);
        init.headers = headers;
      }
    }

    return originalFetch.call(this, input, init);
  };

  // Si axios est utilisé dans votre projet, vous pouvriez également configurer un intercepteur similaire
  const windowWithAxios = window as WindowWithAxios;
  if (typeof windowWithAxios.axios !== 'undefined') {
    windowWithAxios.axios.interceptors.request.use(
      (config: AxiosRequestConfig) => {
        // Ajouter le token CSRF à l'en-tête pour les requêtes mutatives
        const method = config.method?.toUpperCase() || 'GET';
        if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
          const token = getCSRFToken();
          if (token) {
            config.headers = config.headers || {};
            config.headers['X-CSRF-Token'] = token;
          }
        }

        return config;
      },
      (error: unknown) => Promise.reject(error)
    );
  }
};

/**
 * Initialise la protection CSRF
 * Cette fonction doit être appelée au démarrage de l'application
 */
export const initCSRFProtection = (): void => {
  // Ne fonctionne que dans un environnement navigateur
  if (typeof window === 'undefined') return;

  // Générer et stocker un nouveau token CSRF si aucun n'existe
  if (!getCSRFToken()) {
    const token = generateCSRFToken();
    storeCSRFToken(token);
  }

  // Injecter le token dans les formulaires existants
  injectCSRFTokenToForms();

  // Configuration de l'observateur pour injecter automatiquement le token dans les nouveaux formulaires
  const observer = new MutationObserver(() => {
    injectCSRFTokenToForms();
  });

  // Observer les changements dans le DOM pour détecter les nouveaux formulaires
  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });

  // Configurer l'intercepteur pour les requêtes AJAX
  setupCSRFInterceptor();
};

// Exporter un objet avec toutes les fonctions
export default {
  generateCSRFToken,
  storeCSRFToken,
  getCSRFToken,
  injectCSRFTokenToForms,
  setupCSRFInterceptor,
  initCSRFProtection,
};
