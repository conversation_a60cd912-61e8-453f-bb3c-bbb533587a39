/**
 * Utilitaire pour la gestion des classes CSS
 * Date de création: 24 mai 2025
 * 
 * Fonction utilitaire pour combiner et conditionner les classes CSS
 * avec support de clsx et tailwind-merge.
 */

import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Combine et merge les classes CSS avec Tailwind CSS
 * @param inputs - Classes CSS à combiner
 * @returns String de classes CSS optimisées
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Utilitaire pour les classes conditionnelles
 * @param condition - Condition à évaluer
 * @param trueClasses - Classes si la condition est vraie
 * @param falseClasses - Classes si la condition est fausse
 * @returns String de classes CSS
 */
export function conditionalClasses(
  condition: boolean,
  trueClasses: string,
  falseClasses: string = ''
) {
  return condition ? trueClasses : falseClasses;
}

/**
 * Utilitaire pour les variantes de composants
 * @param variants - Objet des variantes possibles
 * @param selectedVariant - Variante sélectionnée
 * @param defaultClasses - Classes par défaut
 * @returns String de classes CSS
 */
export function variantClasses<T extends string>(
  variants: Record<T, string>,
  selectedVariant: T,
  defaultClasses: string = ''
) {
  return cn(defaultClasses, variants[selectedVariant]);
}

/**
 * Utilitaire pour les classes responsives
 * @param base - Classes de base
 * @param sm - Classes pour écrans sm et plus
 * @param md - Classes pour écrans md et plus
 * @param lg - Classes pour écrans lg et plus
 * @param xl - Classes pour écrans xl et plus
 * @returns String de classes CSS responsives
 */
export function responsiveClasses({
  base = '',
  sm = '',
  md = '',
  lg = '',
  xl = '',
}: {
  base?: string;
  sm?: string;
  md?: string;
  lg?: string;
  xl?: string;
}) {
  return cn(
    base,
    sm && `sm:${sm}`,
    md && `md:${md}`,
    lg && `lg:${lg}`,
    xl && `xl:${xl}`
  );
}

/**
 * Utilitaire pour les états de focus, hover, etc.
 * @param base - Classes de base
 * @param hover - Classes au hover
 * @param focus - Classes au focus
 * @param active - Classes à l'état actif
 * @param disabled - Classes à l'état désactivé
 * @returns String de classes CSS avec états
 */
export function stateClasses({
  base = '',
  hover = '',
  focus = '',
  active = '',
  disabled = '',
}: {
  base?: string;
  hover?: string;
  focus?: string;
  active?: string;
  disabled?: string;
}) {
  return cn(
    base,
    hover && `hover:${hover}`,
    focus && `focus:${focus}`,
    active && `active:${active}`,
    disabled && `disabled:${disabled}`
  );
}
