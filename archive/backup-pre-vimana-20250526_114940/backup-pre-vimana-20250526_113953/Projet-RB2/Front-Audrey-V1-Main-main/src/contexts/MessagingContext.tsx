import React, { createContext, useContext, ReactNode } from 'react';
import { useMessaging } from '../hooks/useMessaging';
import {
  IConversation,
  IMessage,
  CreateMessageDto,
  UpdateMessageDto,
} from '../services/api/messagingService';
import * as messagingService from '../services/api/messagingService';

interface MessagingContextType {
  conversations: IConversation[];
  currentConversation: IConversation | null;
  messages: IMessage[];
  loading: boolean;
  error: string | null;
  unreadCount: number;
  isConnected: boolean;
  typingUsers: Record<string, string[]>;
  loadConversations: () => Promise<IConversation[]>;
  loadMessages: (
    conversationId: string,
    options?: { limit?: number; before?: string }
  ) => Promise<IMessage[]>;
  sendMessage: (data: CreateMessageDto) => Promise<IMessage | null>;
  updateMessage: (id: string, data: UpdateMessageDto) => Promise<IMessage | null>;
  deleteMessage: (id: string) => Promise<boolean>;
  markAsRead: (conversationId: string) => Promise<void>;
  createConversation: (
    data: messagingService.CreateConversationDto
  ) => Promise<IConversation | null>;
  addParticipant: (conversationId: string, participantId: string) => Promise<IConversation | null>;
  removeParticipant: (
    conversationId: string,
    participantId: string
  ) => Promise<IConversation | null>;
  addReaction: (messageId: string, emoji: string) => Promise<IMessage | null>;
  setTyping: (conversationId: string, isTyping: boolean) => void;
  searchMessages: (query: string) => Promise<IMessage[]>;
}

const MessagingContext = createContext<MessagingContextType | undefined>(undefined);

export const MessagingProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const messaging = useMessaging();

  return <MessagingContext.Provider value={messaging}>{children}</MessagingContext.Provider>;
};

export const useMessagingContext = (): MessagingContextType => {
  const context = useContext(MessagingContext);
  if (context === undefined) {
    throw new Error('useMessagingContext must be used within a MessagingProvider');
  }
  return context;
};
