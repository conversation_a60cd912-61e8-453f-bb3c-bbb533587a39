/**
 * Module Dashboard Unifié - Retreat And Be
 * Date de création: 25 mai 2025
 * 
 * Dashboard principal utilisant le nouveau design system
 * avec toutes les métriques et actions rapides.
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../store/globalStore';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardContent,
  StatsCard,
  Button,
  Container,
  Grid,
  Stack,
  Badge,
  Avatar,
  Table,
  Spinner
} from '../../components/ui/design-system';
import { useToast } from '../../components/ui/design-system/Toast';

interface DashboardStats {
  totalBookings: number;
  upcomingRetreats: number;
  favoritesProfessionals: number;
  totalSpent: number;
  bookingsChange: { value: number; type: 'increase' | 'decrease' };
  retreatsChange: { value: number; type: 'increase' | 'decrease' };
}

interface RecentActivity {
  id: string;
  type: 'booking' | 'favorite' | 'review' | 'message';
  title: string;
  description: string;
  date: string;
  status?: 'confirmed' | 'pending' | 'cancelled';
}

interface UpcomingRetreat {
  id: string;
  title: string;
  location: string;
  date: string;
  duration: string;
  instructor: string;
  image: string;
  status: 'confirmed' | 'pending' | 'cancelled';
}

const DashboardModule: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [upcomingRetreats, setUpcomingRetreats] = useState<UpcomingRetreat[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // Simuler le chargement des données
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Données mockées
      setStats({
        totalBookings: 12,
        upcomingRetreats: 3,
        favoritesProfessionals: 8,
        totalSpent: 2450,
        bookingsChange: { value: 15, type: 'increase' },
        retreatsChange: { value: 2, type: 'increase' },
      });

      setRecentActivity([
        {
          id: '1',
          type: 'booking',
          title: 'Retraite Yoga confirmée',
          description: 'Retraite Yoga en Provence - 15-18 juin',
          date: '2025-05-20',
          status: 'confirmed',
        },
        {
          id: '2',
          type: 'favorite',
          title: 'Nouveau professionnel favori',
          description: 'Marie Dubois - Coach en nutrition',
          date: '2025-05-18',
        },
        {
          id: '3',
          type: 'review',
          title: 'Avis publié',
          description: 'Retraite Méditation en Bretagne - 5/5 étoiles',
          date: '2025-05-15',
        },
        {
          id: '4',
          type: 'message',
          title: 'Message reçu',
          description: 'Jean Martin vous a envoyé un message',
          date: '2025-05-14',
        },
      ]);

      setUpcomingRetreats([
        {
          id: '1',
          title: 'Retraite Yoga Provence',
          location: 'Aix-en-Provence, France',
          date: '2025-06-15',
          duration: '3 jours',
          instructor: 'Sophie Laurent',
          image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300',
          status: 'confirmed',
        },
        {
          id: '2',
          title: 'Méditation Pleine Conscience',
          location: 'Annecy, France',
          date: '2025-07-02',
          duration: '5 jours',
          instructor: 'Marc Dubois',
          image: 'https://images.unsplash.com/photo-1545389336-cf090694435e?w=300',
          status: 'confirmed',
        },
        {
          id: '3',
          title: 'Détox Numérique',
          location: 'Bretagne, France',
          date: '2025-07-20',
          duration: '7 jours',
          instructor: 'Claire Martin',
          image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=300',
          status: 'pending',
        },
      ]);

    } catch (error) {
      console.error('Erreur lors du chargement du dashboard:', error);
      toast.error('Erreur lors du chargement des données');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <Container className="py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <Spinner size="lg" />
        </div>
      </Container>
    );
  }

  return (
    <Container className="py-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* En-tête de bienvenue */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-neutral-900">
                Bonjour, {user?.name || 'Utilisateur'} ! 👋
              </h1>
              <p className="text-neutral-600 mt-2">
                Voici un aperçu de votre activité bien-être
              </p>
            </div>
            <Avatar
              src={user?.avatar}
              alt={user?.name}
              size="lg"
              fallback={user?.name?.charAt(0) || 'U'}
            />
          </div>
        </div>

        {/* Statistiques principales */}
        <Grid cols={4} gap="md" className="mb-8">
          <StatsCard
            title="Réservations"
            value={stats?.totalBookings.toString() || '0'}
            icon="📅"
            change={stats?.bookingsChange}
            description="ce mois"
          />
          <StatsCard
            title="Retraites à venir"
            value={stats?.upcomingRetreats.toString() || '0'}
            icon="🧘‍♀️"
            change={stats?.retreatsChange}
            description="confirmées"
          />
          <StatsCard
            title="Professionnels favoris"
            value={stats?.favoritesProfessionals.toString() || '0'}
            icon="⭐"
            description="dans votre liste"
          />
          <StatsCard
            title="Total dépensé"
            value={`${stats?.totalSpent || 0}€`}
            icon="💰"
            description="cette année"
          />
        </Grid>

        <Grid cols={3} gap="lg">
          {/* Actions rapides */}
          <Card>
            <CardHeader>
              <CardTitle>Actions rapides</CardTitle>
            </CardHeader>
            <CardContent>
              <Stack spacing="sm">
                <Button
                  variant="primary"
                  fullWidth
                  leftIcon="🔍"
                  onClick={() => window.location.href = '/app/retreats'}
                >
                  Rechercher une retraite
                </Button>
                <Button
                  variant="outline"
                  fullWidth
                  leftIcon="👥"
                  onClick={() => window.location.href = '/app/professionals'}
                >
                  Trouver un professionnel
                </Button>
                <Button
                  variant="ghost"
                  fullWidth
                  leftIcon="📚"
                  onClick={() => window.location.href = '/app/content'}
                >
                  Explorer le contenu
                </Button>
                <Button
                  variant="ghost"
                  fullWidth
                  leftIcon="⚙️"
                  onClick={() => window.location.href = '/app/settings'}
                >
                  Paramètres
                </Button>
              </Stack>
            </CardContent>
          </Card>

          {/* Activité récente */}
          <Card>
            <CardHeader>
              <CardTitle>Activité récente</CardTitle>
            </CardHeader>
            <CardContent>
              <Stack spacing="sm">
                {recentActivity.map((activity) => (
                  <div
                    key={activity.id}
                    className="flex items-start space-x-3 p-3 rounded-lg hover:bg-neutral-50 transition-colors"
                  >
                    <div className="flex-shrink-0">
                      {activity.type === 'booking' && <span className="text-lg">📅</span>}
                      {activity.type === 'favorite' && <span className="text-lg">⭐</span>}
                      {activity.type === 'review' && <span className="text-lg">📝</span>}
                      {activity.type === 'message' && <span className="text-lg">💬</span>}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-neutral-900 truncate">
                        {activity.title}
                      </p>
                      <p className="text-sm text-neutral-600 truncate">
                        {activity.description}
                      </p>
                      <p className="text-xs text-neutral-500 mt-1">
                        {new Date(activity.date).toLocaleDateString('fr-FR')}
                      </p>
                    </div>
                    {activity.status && (
                      <Badge
                        variant={
                          activity.status === 'confirmed' ? 'success' :
                          activity.status === 'pending' ? 'warning' : 'error'
                        }
                      >
                        {activity.status === 'confirmed' ? 'Confirmé' :
                         activity.status === 'pending' ? 'En attente' : 'Annulé'}
                      </Badge>
                    )}
                  </div>
                ))}
              </Stack>
            </CardContent>
          </Card>

          {/* Retraites à venir */}
          <Card>
            <CardHeader>
              <CardTitle>Retraites à venir</CardTitle>
            </CardHeader>
            <CardContent>
              <Stack spacing="sm">
                {upcomingRetreats.map((retreat) => (
                  <div
                    key={retreat.id}
                    className="flex items-center space-x-3 p-3 rounded-lg hover:bg-neutral-50 transition-colors cursor-pointer"
                    onClick={() => window.location.href = `/app/retreats/${retreat.id}`}
                  >
                    <img
                      src={retreat.image}
                      alt={retreat.title}
                      className="w-12 h-12 rounded-lg object-cover"
                    />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-neutral-900 truncate">
                        {retreat.title}
                      </p>
                      <p className="text-sm text-neutral-600 truncate">
                        {retreat.location}
                      </p>
                      <p className="text-xs text-neutral-500">
                        {new Date(retreat.date).toLocaleDateString('fr-FR')} • {retreat.duration}
                      </p>
                    </div>
                    <Badge
                      variant={retreat.status === 'confirmed' ? 'success' : 'warning'}
                    >
                      {retreat.status === 'confirmed' ? 'Confirmé' : 'En attente'}
                    </Badge>
                  </div>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Recommandations personnalisées */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Recommandations pour vous</CardTitle>
          </CardHeader>
          <CardContent>
            <Grid cols={3} gap="md">
              <div className="p-4 border border-neutral-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <div className="text-2xl mb-2">🧘‍♀️</div>
                <h3 className="font-semibold mb-1">Yoga Débutant</h3>
                <p className="text-sm text-neutral-600 mb-3">
                  Parfait pour commencer votre pratique du yoga
                </p>
                <Button size="sm" variant="outline" fullWidth>
                  Découvrir
                </Button>
              </div>

              <div className="p-4 border border-neutral-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <div className="text-2xl mb-2">🌿</div>
                <h3 className="font-semibold mb-1">Nutrition Holistique</h3>
                <p className="text-sm text-neutral-600 mb-3">
                  Consultations avec nos experts en nutrition
                </p>
                <Button size="sm" variant="outline" fullWidth>
                  Découvrir
                </Button>
              </div>

              <div className="p-4 border border-neutral-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <div className="text-2xl mb-2">🏔️</div>
                <h3 className="font-semibold mb-1">Retraite Montagne</h3>
                <p className="text-sm text-neutral-600 mb-3">
                  Reconnectez-vous avec la nature
                </p>
                <Button size="sm" variant="outline" fullWidth>
                  Découvrir
                </Button>
              </div>
            </Grid>
          </CardContent>
        </Card>
      </motion.div>
    </Container>
  );
};

export default DashboardModule;
