import { apiClient } from './apiClient';

/**
 * Interface pour une alerte
 */
export interface Alert {
  id: string;
  type: 'info' | 'warning' | 'success' | 'error';
  title: string;
  message: string;
  source: string;
  timestamp: string;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
  actionRequired: boolean;
  actionLink?: string;
  actionText?: string;
  metadata?: Record<string, any>;
}

/**
 * Interface pour les paramètres de configuration des alertes
 */
export interface AlertConfig {
  enabled: boolean;
  performanceThreshold: number;
  anomalyDetectionEnabled: boolean;
  anomalyThreshold: number;
  notifyByEmail: boolean;
  notifyInApp: boolean;
  minPriority: 'low' | 'medium' | 'high';
}

/**
 * Service pour gérer les alertes
 */
class AlertService {
  /**
   * Récupère toutes les alertes
   * @param read Filtre par statut de lecture (optionnel)
   * @param limit Nombre maximum d'alertes à récupérer (optionnel)
   * @returns Liste des alertes
   */
  async getAlerts(read?: boolean, limit?: number): Promise<Alert[]> {
    try {
      const params: any = {};
      if (read !== undefined) params.read = read;
      if (limit !== undefined) params.limit = limit;
      
      const response = await apiClient.get('/alerts', { params });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des alertes:', error);
      throw error;
    }
  }
  
  /**
   * Récupère une alerte par son ID
   * @param id ID de l'alerte
   * @returns Alerte
   */
  async getAlertById(id: string): Promise<Alert> {
    try {
      const response = await apiClient.get(`/alerts/${id}`);
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération de l'alerte ${id}:`, error);
      throw error;
    }
  }
  
  /**
   * Marque une alerte comme lue
   * @param id ID de l'alerte
   * @returns Alerte mise à jour
   */
  async markAsRead(id: string): Promise<Alert> {
    try {
      const response = await apiClient.put(`/alerts/${id}/read`);
      return response;
    } catch (error) {
      console.error(`Erreur lors du marquage de l'alerte ${id} comme lue:`, error);
      throw error;
    }
  }
  
  /**
   * Marque toutes les alertes comme lues
   * @returns Statut de l'opération
   */
  async markAllAsRead(): Promise<{ success: boolean; count: number }> {
    try {
      const response = await apiClient.put('/alerts/read-all');
      return response;
    } catch (error) {
      console.error('Erreur lors du marquage de toutes les alertes comme lues:', error);
      throw error;
    }
  }
  
  /**
   * Supprime une alerte
   * @param id ID de l'alerte
   * @returns Statut de l'opération
   */
  async deleteAlert(id: string): Promise<{ success: boolean }> {
    try {
      const response = await apiClient.delete(`/alerts/${id}`);
      return response;
    } catch (error) {
      console.error(`Erreur lors de la suppression de l'alerte ${id}:`, error);
      throw error;
    }
  }
  
  /**
   * Récupère la configuration des alertes
   * @returns Configuration des alertes
   */
  async getAlertConfig(): Promise<AlertConfig> {
    try {
      const response = await apiClient.get('/alerts/config');
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de la configuration des alertes:', error);
      throw error;
    }
  }
  
  /**
   * Met à jour la configuration des alertes
   * @param config Nouvelle configuration
   * @returns Configuration mise à jour
   */
  async updateAlertConfig(config: AlertConfig): Promise<AlertConfig> {
    try {
      const response = await apiClient.put('/alerts/config', config);
      return response;
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la configuration des alertes:', error);
      throw error;
    }
  }
  
  /**
   * Crée une nouvelle alerte (pour les tests)
   * @param alert Données de l'alerte
   * @returns Alerte créée
   */
  async createAlert(alert: Omit<Alert, 'id' | 'timestamp'>): Promise<Alert> {
    try {
      const response = await apiClient.post('/alerts', alert);
      return response;
    } catch (error) {
      console.error('Erreur lors de la création de l\'alerte:', error);
      throw error;
    }
  }
  
  /**
   * Récupère les statistiques des alertes
   * @returns Statistiques des alertes
   */
  async getAlertStats(): Promise<{
    total: number;
    unread: number;
    highPriority: number;
    byType: Record<string, number>;
    bySource: Record<string, number>;
  }> {
    try {
      const response = await apiClient.get('/alerts/stats');
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques des alertes:', error);
      throw error;
    }
  }
}

export const alertService = new AlertService();
