import axios, { AxiosError } from 'axios';
import { getAuthToken } from '../../utils/authUtils';

// URL for the superagent chatbot API
const CHATBOT_API_URL = process.env.REACT_APP_SUPERAGENT_URL || 'http://localhost:8001';

// Message interface
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: Date;
  messageType?: MessageType;
  metadata?: Record<string, unknown>;
}

// Message type enum
export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  BUTTONS = 'buttons',
  QUICK_REPLIES = 'quick_replies',
  CARD = 'card',
  CAROUSEL = 'carousel',
  AUDIO = 'audio',
  VIDEO = 'video',
  FILE = 'file',
  LOCATION = 'location',
  TYPING = 'typing',
}

// Button interface
export interface Button {
  text: string;
  value: string;
  type?: 'url' | 'postback' | 'phone';
  url?: string;
}

// Request interface
export interface ChatRequest {
  messages: ChatMessage[];
  user_id: string;
  conversation_id?: string;
  metadata?: Record<string, unknown>;
}

// Response interface
export interface ChatResponse {
  content: string;
  intent?: string;
  entities?: Record<string, unknown>;
  conversation_id?: string;
  suggestions?: string[];
  messageType?: MessageType;
  buttons?: Button[];
  metadata?: Record<string, unknown>;
}

// Error types
export enum ChatbotErrorType {
  NETWORK_ERROR = 'network_error',
  AUTH_ERROR = 'auth_error',
  SERVER_ERROR = 'server_error',
  UNKNOWN_ERROR = 'unknown_error',
}

// Custom error class
export class ChatbotError extends Error {
  type: ChatbotErrorType;
  statusCode?: number;

  constructor(message: string, type: ChatbotErrorType, statusCode?: number) {
    super(message);
    this.type = type;
    this.statusCode = statusCode;
    this.name = 'ChatbotError';
  }
}

/**
 * Get authorization headers with token
 *
 * @returns Headers object with authorization token
 */
const getAuthHeaders = () => {
  const token = getAuthToken();
  return token ? { Authorization: `Bearer ${token}` } : {};
};

/**
 * Handle API errors
 *
 * @param error - The error from axios
 * @throws ChatbotError with appropriate type
 */
const handleApiError = (error: unknown): never => {
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError;

    if (!axiosError.response) {
      throw new ChatbotError(
        'Impossible de se connecter au service chatbot. Vérifiez votre connexion internet.',
        ChatbotErrorType.NETWORK_ERROR
      );
    }

    const statusCode = axiosError.response.status;

    if (statusCode === 401 || statusCode === 403) {
      throw new ChatbotError(
        'Authentification requise. Veuillez vous connecter pour continuer.',
        ChatbotErrorType.AUTH_ERROR,
        statusCode
      );
    }

    if (statusCode >= 500) {
      throw new ChatbotError(
        'Le service chatbot est actuellement indisponible. Veuillez réessayer plus tard.',
        ChatbotErrorType.SERVER_ERROR,
        statusCode
      );
    }

    throw new ChatbotError(
      `Erreur lors de la communication avec le service chatbot: ${axiosError.message}`,
      ChatbotErrorType.UNKNOWN_ERROR,
      statusCode
    );
  }

  throw new ChatbotError(
    'Une erreur inconnue est survenue lors de la communication avec le service chatbot.',
    ChatbotErrorType.UNKNOWN_ERROR
  );
};

/**
 * Send a message to the chatbot and get a response
 *
 * @param messages - Array of messages in the conversation
 * @param userId - User ID for personalization
 * @param conversationId - Optional conversation ID for context
 * @param metadata - Optional metadata for the request
 * @returns The chatbot response
 */
export const sendChatMessage = async (
  messages: ChatMessage[],
  userId: string = 'anonymous',
  conversationId?: string,
  metadata?: Record<string, unknown>
): Promise<ChatResponse> => {
  try {
    const headers = getAuthHeaders();

    const response = await axios.post<ChatResponse>(
      `${CHATBOT_API_URL}/chat`,
      {
        messages,
        user_id: userId,
        conversation_id: conversationId,
        metadata,
      },
      { headers }
    );

    return response.data;
  } catch (error) {
    handleApiError(error);
    throw error;
  }
};

/**
 * Send a single message to the chatbot
 *
 * @param message - The message content
 * @param userId - User ID for personalization
 * @param conversationId - Optional conversation ID for context
 * @param metadata - Optional metadata for the request
 * @returns The chatbot response
 */
export const sendSingleMessage = async (
  message: string,
  userId: string = 'anonymous',
  conversationId?: string,
  metadata?: Record<string, unknown>
): Promise<ChatResponse> => {
  return sendChatMessage([{ role: 'user', content: message }], userId, conversationId, metadata);
};

/**
 * Check if the chatbot service is available
 *
 * @returns True if the service is available, false otherwise
 */
export const checkChatbotHealth = async (): Promise<boolean> => {
  try {
    const headers = getAuthHeaders();
    const response = await axios.get(`${CHATBOT_API_URL}/health`, { headers });
    return response.data.status === 'ok';
  } catch (error) {
    console.error('Error checking chatbot health:', error);
    return false;
  }
};

/**
 * Get conversation history from the server
 *
 * @param conversationId - The ID of the conversation
 * @param limit - Maximum number of messages to retrieve
 * @returns Array of messages in the conversation
 */
export const getConversationHistory = async (
  conversationId: string,
  limit: number = 50
): Promise<ChatMessage[]> => {
  try {
    const headers = getAuthHeaders();
    const response = await axios.get<{ messages: ChatMessage[] }>(
      `${CHATBOT_API_URL}/conversations/${conversationId}`,
      {
        params: { limit },
        headers,
      }
    );
    return response.data.messages;
  } catch (error) {
    handleApiError(error);
    throw error;
  }
};
