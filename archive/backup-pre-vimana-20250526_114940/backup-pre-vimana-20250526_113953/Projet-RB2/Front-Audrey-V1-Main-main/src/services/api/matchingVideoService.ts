import { apiClient } from './apiClient';

interface CreateVideoRoomParams {
  partnerId: string;
  retreatId: string;
  title?: string;
  description?: string;
  scheduledFor?: Date;
  duration?: number;
  isPrivate?: boolean;
  password?: string;
  createdBy: string;
  createdAt: string;
}

interface VideoRoom {
  id: string;
  externalRoomId: string;
  partnerId: string;
  retreatId: string;
  title: string;
  description?: string;
  scheduledFor: string;
  duration: number;
  isPrivate: boolean;
  status: string;
  joinUrl: string;
  hostUrl: string;
  createdBy: string;
  createdAt: string;
}

// Response for simple video operations like create/end
interface VideoOperationResponse {
  success: boolean;
  message?: string;
  roomId?: string;
  details?: Record<string, any>;
}

class MatchingVideoService {
  /**
   * Crée une salle de vidéoconférence pour un matching
   * @param params Paramètres de la salle
   * @returns Informations sur la salle créée
   */
  async createVideoRoom(params: CreateVideoRoomParams): Promise<VideoRoom> {
    try {
      const response = await apiClient.post<VideoRoom>('/matching/video/rooms', params);
      return response;
    } catch (error) {
      console.error('Error creating video room:', error);
      throw error;
    }
  }

  /**
   * Récupère les salles de vidéoconférence pour un matching
   * @param partnerId ID du partenaire
   * @param retreatId ID de la retraite
   * @returns Liste des salles de vidéoconférence
   */
  async getVideoRooms(partnerId: string, retreatId: string): Promise<VideoRoom[]> {
    try {
      const response = await apiClient.get<VideoRoom[]>(`/matching/video/rooms/${partnerId}/${retreatId}`);
      return response;
    } catch (error) {
      console.error('Error getting video rooms:', error);
      return [];
    }
  }

  /**
   * Récupère les détails d'une salle de vidéoconférence
   * @param roomId ID de la salle
   * @returns Détails de la salle
   */
  async getVideoRoomDetails(roomId: string): Promise<VideoRoom> {
    try {
      const response = await apiClient.get<VideoRoom>(`/matching/video/rooms/details/${roomId}`);
      return response;
    } catch (error) {
      console.error('Error getting video room details:', error);
      throw error;
    }
  }

  /**
   * Génère un lien de participation pour une salle de vidéoconférence
   * @param roomId ID de la salle
   * @returns Lien de participation
   */
  async generateJoinLink(roomId: string): Promise<string> {
    try {
      const response = await apiClient.get<{ joinUrl: string }>(`/matching/video/join/${roomId}`);
      return response.joinUrl;
    } catch (error) {
      console.error('Error generating join link:', error);
      throw error;
    }
  }

  /**
   * Termine une salle de vidéoconférence
   * @param roomId ID de la salle
   * @returns Résultat de l'opération
   */
  async endVideoRoom(roomId: string): Promise<VideoOperationResponse> {
    try {
      const response = await apiClient.post<VideoOperationResponse>(`/matching/video/rooms/${roomId}/end`);
      return response;
    } catch (error) {
      console.error('Error ending video room:', error);
      throw error;
    }
  }
}

export const matchingVideoService = new MatchingVideoService();
