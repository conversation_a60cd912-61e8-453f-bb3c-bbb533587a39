import { apiClient } from './apiClient';

export interface UserPreference {
  userId: string;
  category: string;
  weight: number;
}

export interface UserInterest {
  userId: string;
  interest: string;
  type: 'CATEGORY' | 'TAG' | 'KEYWORD';
  source: 'EXPLICIT' | 'IMPLICIT' | 'INFERRED';
  weight: number;
}

export interface PersonalizationSettings {
  userId: string;
  enablePersonalization: boolean;
  diversityLevel: number;
  noveltyLevel: number;
  contentFilters: {
    excludedCategories: string[];
    excludedTags: string[];
    contentTypes: string[];
    minContentRating: number;
    languagePreferences: string[];
  };
  privacySettings: {
    allowInteractionTracking: boolean;
    allowContentAnalysis: boolean;
    allowThirdPartyData: boolean;
    dataRetentionPeriod: number;
  };
}

export interface UpdatePreferencesRequest {
  preferences: { category: string; weight: number }[];
}

export interface UpdateInterestsRequest {
  interests: { interest: string; type: 'CATEGORY' | 'TAG' | 'KEYWORD'; weight: number }[];
}

export interface ExternalContentQueryParams {
  limit?: number;
  categories?: string[];
  tags?: string[];
  sources?: string[];
  minDate?: string;
  maxDate?: string;
}

export interface ExternalContent {
  id: string;
  source: string;
  title: string;
  description: string;
  url: string;
  imageUrl?: string;
  categories: string[];
  tags: string[];
  publishedAt: string;
  popularity: number;
  relevanceScore: number;
  metadata: Record<string, any>;
}

class UserPreferencesService {
  /**
   * Récupère les préférences de l'utilisateur actuel
   */
  async getUserPreferences(): Promise<UserPreference[]> {
    try {
      const response = await apiClient.get('/user-preferences');
      return response.data;
    } catch (error) {
      console.error('Error fetching user preferences:', error);
      throw error;
    }
  }

  /**
   * Met à jour les préférences de l'utilisateur actuel
   */
  async updateUserPreferences(request: UpdatePreferencesRequest): Promise<UserPreference[]> {
    try {
      const response = await apiClient.put('/user-preferences', request);
      return response.data;
    } catch (error) {
      console.error('Error updating user preferences:', error);
      throw error;
    }
  }

  /**
   * Récupère les intérêts de l'utilisateur actuel
   */
  async getUserInterests(): Promise<UserInterest[]> {
    try {
      const response = await apiClient.get('/user-preferences/interests');
      return response.data;
    } catch (error) {
      console.error('Error fetching user interests:', error);
      throw error;
    }
  }

  /**
   * Met à jour les intérêts de l'utilisateur actuel
   */
  async updateUserInterests(request: UpdateInterestsRequest): Promise<UserInterest[]> {
    try {
      const response = await apiClient.put('/user-preferences/interests', request);
      return response.data;
    } catch (error) {
      console.error('Error updating user interests:', error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres de personnalisation de l'utilisateur actuel
   */
  async getPersonalizationSettings(): Promise<PersonalizationSettings> {
    try {
      const response = await apiClient.get('/user-preferences/settings');
      return response.data;
    } catch (error) {
      console.error('Error fetching personalization settings:', error);
      throw error;
    }
  }

  /**
   * Met à jour les paramètres de personnalisation de l'utilisateur actuel
   */
  async updatePersonalizationSettings(settings: Partial<PersonalizationSettings>): Promise<PersonalizationSettings> {
    try {
      const response = await apiClient.put('/user-preferences/settings', settings);
      return response.data;
    } catch (error) {
      console.error('Error updating personalization settings:', error);
      throw error;
    }
  }

  /**
   * Récupère du contenu externe personnalisé
   */
  async getExternalContent(params: ExternalContentQueryParams = {}): Promise<ExternalContent[]> {
    try {
      const response = await apiClient.get('/user-preferences/external-content', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching external content:', error);
      throw error;
    }
  }

  /**
   * Récupère les préférences d'un utilisateur spécifique (admin uniquement)
   */
  async getUserPreferencesById(userId: string): Promise<UserPreference[]> {
    try {
      const response = await apiClient.get(`/user-preferences/${userId}/preferences`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching preferences for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les intérêts d'un utilisateur spécifique (admin uniquement)
   */
  async getUserInterestsById(userId: string): Promise<UserInterest[]> {
    try {
      const response = await apiClient.get(`/user-preferences/${userId}/interests`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching interests for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres de personnalisation d'un utilisateur spécifique (admin uniquement)
   */
  async getPersonalizationSettingsById(userId: string): Promise<PersonalizationSettings> {
    try {
      const response = await apiClient.get(`/user-preferences/${userId}/settings`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching personalization settings for user ${userId}:`, error);
      throw error;
    }
  }
}

export const userPreferencesService = new UserPreferencesService();
