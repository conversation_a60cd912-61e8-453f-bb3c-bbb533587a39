import { apiClient } from './apiClient';

export interface RecommendationMetrics {
  totalRecommendations: number;
  totalImpressions: number;
  totalClicks: number;
  totalConversions: number;
  clickThroughRate: number;
  conversionRate: number;
  averageRelevanceScore: number;
  averageSatisfactionScore: number;
}

export interface StrategyPerformance {
  strategy: string;
  impressions: number;
  clicks: number;
  conversions: number;
  clickThroughRate: number;
  conversionRate: number;
  relevanceScore: number;
  satisfactionScore: number;
}

export interface CategoryPerformance {
  category: string;
  impressions: number;
  clicks: number;
  conversions: number;
  clickThroughRate: number;
  conversionRate: number;
}

export interface UserSegmentPerformance {
  segment: string;
  userCount: number;
  impressionsPerUser: number;
  clicksPerUser: number;
  conversionsPerUser: number;
  clickThroughRate: number;
  conversionRate: number;
}

export interface TimeSeriesData {
  date: string;
  impressions: number;
  clicks: number;
  conversions: number;
  clickThroughRate: number;
  conversionRate: number;
}

export interface RecommendationFlow {
  source: string;
  target: string;
  value: number;
}

export interface AvailableFilters {
  strategies: string[];
  categories: string[];
  segments: string[];
}

export interface AnalyticsQueryParams {
  userId?: string;
  startDate: string;
  endDate: string;
  strategy?: string;
  category?: string;
  segment?: string;
  granularity?: 'day' | 'week' | 'month';
}

class RecommendationAnalyticsService {
  /**
   * Récupère les métriques globales des recommandations
   */
  async getRecommendationMetrics(params: AnalyticsQueryParams): Promise<RecommendationMetrics> {
    try {
      const response = await apiClient.get('/recommendation/analytics/metrics', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching recommendation metrics:', error);
      throw error;
    }
  }

  /**
   * Récupère les performances par stratégie de recommandation
   */
  async getStrategyPerformance(params: AnalyticsQueryParams): Promise<StrategyPerformance[]> {
    try {
      const response = await apiClient.get('/recommendation/analytics/strategy-performance', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching strategy performance:', error);
      throw error;
    }
  }

  /**
   * Récupère les performances par catégorie
   */
  async getCategoryPerformance(params: AnalyticsQueryParams): Promise<CategoryPerformance[]> {
    try {
      const response = await apiClient.get('/recommendation/analytics/category-performance', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching category performance:', error);
      throw error;
    }
  }

  /**
   * Récupère les performances par segment d'utilisateurs
   */
  async getUserSegmentPerformance(params: AnalyticsQueryParams): Promise<UserSegmentPerformance[]> {
    try {
      const response = await apiClient.get('/recommendation/analytics/segment-performance', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching user segment performance:', error);
      throw error;
    }
  }

  /**
   * Récupère les données de série temporelle
   */
  async getTimeSeriesData(params: AnalyticsQueryParams): Promise<TimeSeriesData[]> {
    try {
      const response = await apiClient.get('/recommendation/analytics/time-series', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching time series data:', error);
      throw error;
    }
  }

  /**
   * Récupère les données de flux de recommandation
   */
  async getRecommendationFlow(params: AnalyticsQueryParams): Promise<RecommendationFlow[]> {
    try {
      const response = await apiClient.get('/recommendation/analytics/flow', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching recommendation flow:', error);
      throw error;
    }
  }

  /**
   * Récupère les filtres disponibles
   */
  async getAvailableFilters(params: Pick<AnalyticsQueryParams, 'userId' | 'startDate' | 'endDate'>): Promise<AvailableFilters> {
    try {
      const response = await apiClient.get('/recommendation/analytics/filters', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching available filters:', error);
      throw error;
    }
  }

  /**
   * Exporte les données d'analyse au format CSV
   */
  async exportAnalyticsData(params: AnalyticsQueryParams & { format: 'csv' | 'json' | 'excel' }): Promise<Blob> {
    try {
      const response = await apiClient.get('/recommendation/analytics/export', {
        params,
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting analytics data:', error);
      throw error;
    }
  }

  /**
   * Récupère les recommandations les plus populaires
   */
  async getTopRecommendations(params: AnalyticsQueryParams & { limit?: number }): Promise<any[]> {
    try {
      const response = await apiClient.get('/recommendation/analytics/top-recommendations', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching top recommendations:', error);
      throw error;
    }
  }

  /**
   * Récupère les utilisateurs les plus actifs
   */
  async getTopUsers(params: AnalyticsQueryParams & { limit?: number }): Promise<any[]> {
    try {
      const response = await apiClient.get('/recommendation/analytics/top-users', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching top users:', error);
      throw error;
    }
  }

  /**
   * Récupère les prévisions de performance
   */
  async getPerformanceForecasts(params: AnalyticsQueryParams & { forecastDays?: number }): Promise<any> {
    try {
      const response = await apiClient.get('/recommendation/analytics/forecasts', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching performance forecasts:', error);
      throw error;
    }
  }

  /**
   * Récupère les anomalies détectées
   */
  async getAnomalies(params: AnalyticsQueryParams): Promise<any[]> {
    try {
      const response = await apiClient.get('/recommendation/analytics/anomalies', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching anomalies:', error);
      throw error;
    }
  }

  /**
   * Récupère les métriques de diversité
   */
  async getDiversityMetrics(params: AnalyticsQueryParams): Promise<any> {
    try {
      const response = await apiClient.get('/recommendation/analytics/diversity', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching diversity metrics:', error);
      throw error;
    }
  }

  /**
   * Récupère les métriques de nouveauté
   */
  async getNoveltyMetrics(params: AnalyticsQueryParams): Promise<any> {
    try {
      const response = await apiClient.get('/recommendation/analytics/novelty', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching novelty metrics:', error);
      throw error;
    }
  }

  /**
   * Récupère les métriques de satisfaction utilisateur
   */
  async getSatisfactionMetrics(params: AnalyticsQueryParams): Promise<any> {
    try {
      const response = await apiClient.get('/recommendation/analytics/satisfaction', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching satisfaction metrics:', error);
      throw error;
    }
  }
}

export const recommendationAnalyticsService = new RecommendationAnalyticsService();
