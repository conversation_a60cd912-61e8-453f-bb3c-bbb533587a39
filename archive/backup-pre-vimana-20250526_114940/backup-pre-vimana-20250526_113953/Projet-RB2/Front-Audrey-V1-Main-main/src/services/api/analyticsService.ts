import { apiClient } from './apiClient';

export interface MetricsFilters {
  startDate?: string;
  endDate?: string;
  contentType?: string;
}

export interface EngagementMetrics {
  summary: {
    totalViews: number;
    totalLikes: number;
    totalComments: number;
    totalShares: number;
    totalBookmarks: number;
    totalClickThroughs: number;
    engagementRate: number;
    clickThroughRate: number;
  };
  timeSeries: {
    date: string;
    views: number;
    likes: number;
    comments: number;
    shares: number;
    bookmarks: number;
    clickThroughs: number;
  }[];
  period: {
    startDate: string | Date;
    endDate: string | Date;
  };
}

export interface AudienceMetrics {
  totalFollowers: number;
  newFollowers: number;
  lostFollowers: number;
  activeFollowers: number;
  demographics?: {
    age?: Record<string, number>;
    gender?: Record<string, number>;
    location?: Record<string, number>;
  };
  period: {
    startDate: string | Date;
    endDate: string | Date;
  };
}

export interface RevenueMetrics {
  totalRevenue: number;
  bySources: {
    source: string;
    amount: number;
    percentage: number;
  }[];
  timeSeries: {
    date: string;
    amount: number;
  }[];
  period: {
    startDate: string | Date;
    endDate: string | Date;
  };
}

export interface ContentPerformance {
  contentId: string;
  contentType: string;
  views: number;
  likes: number;
  comments: number;
  shares: number;
  bookmarks: number;
  clickThroughs: number;
  engagement: number;
  engagementRate: number;
}

export interface CreatorMetrics {
  engagement: EngagementMetrics;
  audience: AudienceMetrics;
  revenue: RevenueMetrics;
  topContent: ContentPerformance[];
  period: {
    startDate: string | Date;
    endDate: string | Date;
  };
}

export interface ContentMetrics {
  engagement: EngagementMetrics;
  revenue: RevenueMetrics;
  period: {
    startDate: string | Date;
    endDate: string | Date;
  };
}

export interface CreatorSummary {
  engagement: {
    current: {
      totalViews: number;
      totalLikes: number;
      totalComments: number;
      totalShares: number;
      engagementRate: number;
    };
    changes: {
      viewsChange: number;
      likesChange: number;
      commentsChange: number;
      sharesChange: number;
      engagementRateChange: number;
    };
  };
  audience: {
    totalFollowers: number;
    newFollowers: number;
    activeFollowers: number;
    followerGrowth: number;
  };
  revenue: {
    totalRevenue: number;
    revenueGrowth: number;
    topSource: string;
  };
  topContent: ContentPerformance[];
}

export interface Forecast {
  engagement: {
    views: {
      date: string;
      value: number;
      confidence: number;
    }[];
    engagementRate: {
      date: string;
      value: number;
      confidence: number;
    }[];
  };
  audience: {
    followers: {
      date: string;
      value: number;
      confidence: number;
    }[];
  };
  revenue: {
    amount: {
      date: string;
      value: number;
      confidence: number;
    }[];
  };
}

export interface Benchmark {
  engagement: {
    views: {
      value: number;
      percentile: number;
      categoryAverage: number;
    };
    engagementRate: {
      value: number;
      percentile: number;
      categoryAverage: number;
    };
  };
  audience: {
    followers: {
      value: number;
      percentile: number;
      categoryAverage: number;
    };
    growth: {
      value: number;
      percentile: number;
      categoryAverage: number;
    };
  };
  revenue: {
    amount: {
      value: number;
      percentile: number;
      categoryAverage: number;
    };
  };
  category: string;
}

export interface Dashboard {
  id: string;
  creatorId: string;
  name: string;
  description?: string;
  layout: any;
  widgets: Widget[];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Widget {
  id: string;
  dashboardId: string;
  type: string;
  title: string;
  config: any;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  createdAt: string;
  updatedAt: string;
}

class AnalyticsService {
  async getCreatorMetrics(creatorId: string, filters: MetricsFilters = {}): Promise<CreatorMetrics> {
    const queryParams = new URLSearchParams();
    
    if (filters.startDate) queryParams.append('startDate', filters.startDate);
    if (filters.endDate) queryParams.append('endDate', filters.endDate);
    if (filters.contentType) queryParams.append('contentType', filters.contentType);
    
    const response = await apiClient.get(`/analytics/metrics/${creatorId}?${queryParams.toString()}`);
    return response.data;
  }

  async getContentMetrics(creatorId: string, contentId: string, filters: MetricsFilters = {}): Promise<ContentMetrics> {
    const queryParams = new URLSearchParams();
    
    if (filters.startDate) queryParams.append('startDate', filters.startDate);
    if (filters.endDate) queryParams.append('endDate', filters.endDate);
    
    const response = await apiClient.get(`/analytics/metrics/${creatorId}/content/${contentId}?${queryParams.toString()}`);
    return response.data;
  }

  async getCreatorSummary(creatorId: string): Promise<CreatorSummary> {
    const response = await apiClient.get(`/analytics/metrics/${creatorId}/summary`);
    return response.data;
  }

  async getCreatorForecasts(creatorId: string): Promise<Forecast> {
    const response = await apiClient.get(`/analytics/forecasting/${creatorId}`);
    return response.data;
  }

  async getCreatorBenchmarks(creatorId: string, category: string = 'all'): Promise<Benchmark> {
    const response = await apiClient.get(`/analytics/benchmarks/${creatorId}?category=${category}`);
    return response.data;
  }

  async getCreatorDashboards(creatorId: string): Promise<Dashboard[]> {
    const response = await apiClient.get(`/analytics/dashboards/${creatorId}`);
    return response.data;
  }

  async getDashboard(dashboardId: string): Promise<Dashboard> {
    const response = await apiClient.get(`/analytics/dashboards/detail/${dashboardId}`);
    return response.data;
  }

  async createDashboard(creatorId: string, dashboardData: {
    name: string;
    description?: string;
    layout: any;
    widgets?: any[];
    isDefault?: boolean;
  }): Promise<Dashboard> {
    const response = await apiClient.post(`/analytics/dashboards/${creatorId}`, dashboardData);
    return response.data;
  }

  async updateDashboard(dashboardId: string, dashboardData: {
    name?: string;
    description?: string;
    layout?: any;
    isDefault?: boolean;
  }): Promise<Dashboard> {
    const response = await apiClient.put(`/analytics/dashboards/${dashboardId}`, dashboardData);
    return response.data;
  }

  async deleteDashboard(dashboardId: string): Promise<void> {
    await apiClient.delete(`/analytics/dashboards/${dashboardId}`);
  }

  async addWidget(dashboardId: string, widgetData: {
    type: string;
    title: string;
    config: any;
    position: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  }): Promise<Widget> {
    const response = await apiClient.post(`/analytics/dashboards/${dashboardId}/widgets`, widgetData);
    return response.data;
  }

  async updateWidget(dashboardId: string, widgetId: string, widgetData: {
    type?: string;
    title?: string;
    config?: any;
    position?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  }): Promise<Widget> {
    const response = await apiClient.put(`/analytics/dashboards/${dashboardId}/widgets/${widgetId}`, widgetData);
    return response.data;
  }

  async deleteWidget(dashboardId: string, widgetId: string): Promise<void> {
    await apiClient.delete(`/analytics/dashboards/${dashboardId}/widgets/${widgetId}`);
  }
}

export const analyticsService = new AnalyticsService();
