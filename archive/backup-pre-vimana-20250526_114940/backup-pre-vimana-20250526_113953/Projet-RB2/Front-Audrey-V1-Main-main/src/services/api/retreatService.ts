import { apiClient } from './apiClient';
import { API_ENDPOINTS } from './apiConfig';

// Types pour les retraites
export interface Retreat {
  id: string;
  title: string;
  description: string;
  location: string;
  startDate: string;
  endDate: string;
  price: number;
  currency: string;
  capacity: number;
  availableSpots: number;
  images: string[];
  categories: string[];
  amenities: string[];
  hostId: string;
  host?: {
    id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
  };
  rating?: number;
  reviewCount?: number;
  createdAt: string;
  updatedAt: string;
}

export interface RetreatFilters {
  search?: string;
  location?: string;
  startDate?: string;
  endDate?: string;
  minPrice?: number;
  maxPrice?: number;
  categories?: string[];
  sortBy?: 'price' | 'date' | 'rating';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface CreateRetreatRequest {
  title: string;
  description: string;
  location: string;
  startDate: string;
  endDate: string;
  price: number;
  currency: string;
  capacity: number;
  categories: string[];
  amenities: string[];
}

export interface UpdateRetreatRequest {
  title?: string;
  description?: string;
  location?: string;
  startDate?: string;
  endDate?: string;
  price?: number;
  currency?: string;
  capacity?: number;
  categories?: string[];
  amenities?: string[];
}

export interface PaginatedRetreats {
  data: Retreat[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface IReview {
  id: string;
  userId: string;
  retreatId: string;
  rating: number;
  comment: string;
  createdAt: string;
  user?: {
    firstName: string;
    lastName: string;
    profilePicture?: string;
  };
}

/**
 * Service de retraites pour gérer les opérations liées aux retraites
 */
export const retreatService = {
  /**
   * Obtenir la liste des retraites avec filtres
   * @param filters Filtres pour la recherche
   * @returns Liste paginée des retraites
   */
  async getRetreats(filters: RetreatFilters = {}): Promise<PaginatedRetreats> {
    return apiClient.get<PaginatedRetreats>(API_ENDPOINTS.RETREAT.BASE, { params: filters });
  },

  /**
   * Obtenir une retraite par son ID
   * @param id ID de la retraite
   * @returns Détails de la retraite
   */
  async getRetreatById(id: string): Promise<Retreat> {
    return apiClient.get<Retreat>(`${API_ENDPOINTS.RETREAT.BASE}/${id}`);
  },

  /**
   * Créer une nouvelle retraite
   * @param retreatData Données de la retraite
   * @returns Retraite créée
   */
  async createRetreat(retreatData: CreateRetreatRequest): Promise<Retreat> {
    return apiClient.post<Retreat>(API_ENDPOINTS.RETREAT.BASE, retreatData);
  },

  /**
   * Mettre à jour une retraite
   * @param id ID de la retraite
   * @param retreatData Données à mettre à jour
   * @returns Retraite mise à jour
   */
  async updateRetreat(id: string, retreatData: UpdateRetreatRequest): Promise<Retreat> {
    return apiClient.put<Retreat>(`${API_ENDPOINTS.RETREAT.BASE}/${id}`, retreatData);
  },

  /**
   * Supprimer une retraite
   * @param id ID de la retraite
   */
  async deleteRetreat(id: string): Promise<void> {
    await apiClient.delete(`${API_ENDPOINTS.RETREAT.BASE}/${id}`);
  },

  /**
   * Télécharger une image pour une retraite
   * @param id ID de la retraite
   * @param file Fichier image
   * @returns URL de l'image téléchargée
   */
  async uploadRetreatImage(id: string, file: File): Promise<{ url: string }> {
    const formData = new FormData();
    formData.append('file', file);

    return apiClient.post<{ url: string }>(`${API_ENDPOINTS.RETREAT.BASE}/${id}/images`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * Supprimer une image d'une retraite
   * @param id ID de la retraite
   * @param imageUrl URL de l'image à supprimer
   */
  async deleteRetreatImage(id: string, imageUrl: string): Promise<void> {
    await apiClient.delete(`${API_ENDPOINTS.RETREAT.BASE}/${id}/images`, {
      params: { imageUrl },
    });
  },

  /**
   * Obtenir les retraites organisées par l'utilisateur connecté
   * @returns Liste des retraites organisées
   */
  async getMyRetreats(): Promise<Retreat[]> {
    return apiClient.get<Retreat[]>(`${API_ENDPOINTS.RETREAT.BASE}/my-retreats`);
  },

  /**
   * Obtenir les retraites en vedette
   * @returns Liste des retraites en vedette
   */
  async getFeaturedRetreats(): Promise<Retreat[]> {
    return apiClient.get<Retreat[]>(API_ENDPOINTS.RETREAT.FEATURED);
  },

  /**
   * Obtenir les prochaines retraites
   * @returns Liste des prochaines retraites
   */
  async getUpcomingRetreats(): Promise<Retreat[]> {
    return apiClient.get<Retreat[]>(API_ENDPOINTS.RETREAT.UPCOMING);
  },

  /**
   * Obtenir les catégories de retraites
   * @returns Liste des catégories
   */
  async getCategories(): Promise<string[]> {
    return apiClient.get<string[]>(API_ENDPOINTS.RETREAT.CATEGORIES);
  },

  /**
   * Obtenir les avis sur une retraite
   * @param id ID de la retraite
   * @returns Liste des avis
   */
  async getRetreatReviews(id: string): Promise<IReview[]> {
    return apiClient.get<IReview[]>(API_ENDPOINTS.RETREAT.REVIEWS(id));
  },
};
