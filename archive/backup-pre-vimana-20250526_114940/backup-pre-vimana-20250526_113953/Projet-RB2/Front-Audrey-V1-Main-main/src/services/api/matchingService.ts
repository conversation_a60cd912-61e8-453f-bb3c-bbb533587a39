import { apiClient } from './apiClient';
import { API_ENDPOINTS } from './apiConfig';

export interface DateRange {
  start: string;
  end: string;
}

export interface LocationPreference {
  country: string;
  region?: string;
  city?: string;
  radius?: number;
}

export interface MatchingCriteria {
  retreatId?: string;
  partnerId?: string;
  categories?: string[];
  types?: string[];
  specializations?: string[];
  languages?: string[];
  maxBudget?: number;
  dateRange?: DateRange;
  location?: LocationPreference;
  minExperience?: number;
  minRating?: number;
  minCapacity?: number;
  certifiedOnly?: boolean;
  limit?: number;
}

export interface CompatibilityFactors {
  skillMatch: number;
  availabilityMatch: number;
  locationMatch: number;
  ratingMatch: number;
  budgetMatch: number;
}

export interface PartnerMatch {
  id: string;
  companyName: string;
  type: string;
  category: string;
  description: string;
  logo?: string;
  website?: string;
  specializations: string[];
  languages: string[];
  averageRating?: number;
  totalReviews?: number;
  completedServices: number;
}

export interface RetreatMatch {
  id: string;
  title: string;
  description: string;
  startDate: string;
  endDate: string;
  location: string;
  capacity: number;
  price: number;
  images: string[];
}

export interface MatchingResult {
  partnerId: string;
  retreatId?: string;
  score: number;
  compatibilityFactors: CompatibilityFactors;
  partner?: PartnerMatch;
  retreat?: RetreatMatch;
}

export interface MatchingResponse {
  results: MatchingResult[];
  total: number;
  executionTimeMs: number;
}

class MatchingService {
  /**
   * Trouve des partenaires correspondant aux critères
   * @param criteria Critères de recherche
   * @returns Résultats du matching
   */
  async findPartners(criteria: MatchingCriteria): Promise<MatchingResponse> {
    try {
      const response = await apiClient.post<MatchingResponse>(API_ENDPOINTS.MATCHING.PARTNERS, criteria);
      return response;
    } catch (error) {
      console.error('Erreur lors de la recherche de partenaires:', error);
      throw error;
    }
  }

  /**
   * Trouve des retraites correspondant aux critères
   * @param criteria Critères de recherche
   * @returns Résultats du matching
   */
  async findRetreats(criteria: MatchingCriteria): Promise<MatchingResponse> {
    try {
      const response = await apiClient.post<MatchingResponse>(API_ENDPOINTS.MATCHING.RETREATS, criteria);
      return response;
    } catch (error) {
      console.error('Erreur lors de la recherche de retraites:', error);
      throw error;
    }
  }

  /**
   * Trouve des partenaires pour une retraite spécifique
   * @param retreatId ID de la retraite
   * @param options Options de recherche
   * @returns Résultats du matching
   */
  async findPartnersForRetreat(
    retreatId: string,
    options?: {
      categories?: string[];
      types?: string[];
      specializations?: string[];
      languages?: string[];
      minExperience?: number;
      minRating?: number;
      limit?: number;
    },
  ): Promise<MatchingResponse> {
    try {
      const params = new URLSearchParams();
      
      if (options?.categories) {
        options.categories.forEach(category => params.append('categories', category));
      }
      
      if (options?.types) {
        options.types.forEach(type => params.append('types', type));
      }
      
      if (options?.specializations) {
        options.specializations.forEach(spec => params.append('specializations', spec));
      }
      
      if (options?.languages) {
        options.languages.forEach(lang => params.append('languages', lang));
      }
      
      if (options?.minExperience) {
        params.append('minExperience', options.minExperience.toString());
      }
      
      if (options?.minRating) {
        params.append('minRating', options.minRating.toString());
      }
      
      if (options?.limit) {
        params.append('limit', options.limit.toString());
      }
      
      const url = `${API_ENDPOINTS.MATCHING.PARTNERS_FOR_RETREAT(retreatId)}?${params.toString()}`;
      const response = await apiClient.get<MatchingResponse>(url);
      return response;
    } catch (error) {
      console.error('Erreur lors de la recherche de partenaires pour la retraite:', error);
      throw error;
    }
  }

  /**
   * Trouve des retraites pour un partenaire spécifique
   * @param partnerId ID du partenaire
   * @param options Options de recherche
   * @returns Résultats du matching
   */
  async findRetreatsForPartner(
    partnerId: string,
    options?: {
      startDate?: string;
      endDate?: string;
      maxBudget?: number;
      country?: string;
      minCapacity?: number;
      limit?: number;
    },
  ): Promise<MatchingResponse> {
    try {
      const params = new URLSearchParams();
      
      if (options?.startDate) {
        params.append('startDate', options.startDate);
      }
      
      if (options?.endDate) {
        params.append('endDate', options.endDate);
      }
      
      if (options?.maxBudget) {
        params.append('maxBudget', options.maxBudget.toString());
      }
      
      if (options?.country) {
        params.append('country', options.country);
      }
      
      if (options?.minCapacity) {
        params.append('minCapacity', options.minCapacity.toString());
      }
      
      if (options?.limit) {
        params.append('limit', options.limit.toString());
      }
      
      const url = `${API_ENDPOINTS.MATCHING.RETREATS_FOR_PARTNER(partnerId)}?${params.toString()}`;
      const response = await apiClient.get<MatchingResponse>(url);
      return response;
    } catch (error) {
      console.error('Erreur lors de la recherche de retraites pour le partenaire:', error);
      throw error;
    }
  }

  /**
   * Obtient le score de matching entre un partenaire et une retraite
   * @param partnerId ID du partenaire
   * @param retreatId ID de la retraite
   * @returns Score et facteurs de compatibilité
   */
  async getMatchingScore(
    partnerId: string,
    retreatId: string,
  ): Promise<{ score: number; compatibilityFactors: CompatibilityFactors }> {
    try {
      const response = await apiClient.get<{ score: number; compatibilityFactors: CompatibilityFactors }>(
        API_ENDPOINTS.MATCHING.SCORE(partnerId, retreatId)
      );
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'obtention du score de matching:', error);
      throw error;
    }
  }

  /* // Commenting out these methods as API_ENDPOINTS.MATCHING.PREFERENCES is not defined
  async saveMatchingPreference(criteria: MatchingCriteria): Promise<{ success: boolean; preferenceId: string }> {
    try {
      const response = await apiClient.post<{ success: boolean; preferenceId: string }>(API_ENDPOINTS.MATCHING.PREFERENCES, criteria);
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement de la préférence de matching:', error);
      throw error;
    }
  }

  async getMatchingPreferences(): Promise<MatchingCriteria[]> {
    try {
      const response = await apiClient.get<MatchingCriteria[]>(API_ENDPOINTS.MATCHING.PREFERENCES);
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des préférences de matching:', error);
      throw error;
    }
  }
  */
}

export const matchingService = new MatchingService();
