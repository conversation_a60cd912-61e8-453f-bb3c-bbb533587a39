import { apiClient } from './apiClient';
import { MatchingResult } from './matchingService';

// Define a generic response type for generation operations
interface GenerationResponse {
  success: boolean;
  message?: string;
  taskId?: string; // Example if it's an async task
  itemsGenerated?: number;
}

class MatchingRecommendationService {
  /**
   * Obtient les recommandations pour un partenaire
   * @param partnerId ID du partenaire
   * @returns Recommandations pour le partenaire
   */
  async getPartnerRecommendations(partnerId: string): Promise<MatchingResult[]> {
    try {
      const response = await apiClient.get<MatchingResult[]>(`/matching/recommendations/partner/${partnerId}`);
      return response;
    } catch (error) {
      console.error('Error fetching partner recommendations:', error);
      return [];
    }
  }

  /**
   * Génère des recommandations pour un partenaire
   * @param partnerId ID du partenaire
   * @returns Résultat de la génération
   */
  async generatePartnerRecommendations(partnerId: string): Promise<GenerationResponse> {
    try {
      const response = await apiClient.post<GenerationResponse>(`/matching/recommendations/generate/partner/${partnerId}`);
      return response;
    } catch (error) {
      console.error('Error generating partner recommendations:', error);
      throw error;
    }
  }

  /**
   * Génère des recommandations pour une retraite
   * @param retreatId ID de la retraite
   * @returns Résultat de la génération
   */
  async generateRetreatRecommendations(retreatId: string): Promise<GenerationResponse> {
    try {
      const response = await apiClient.post<GenerationResponse>(`/matching/recommendations/generate/retreat/${retreatId}`);
      return response;
    } catch (error) {
      console.error('Error generating retreat recommendations:', error);
      throw error;
    }
  }

  /**
   * Génère des recommandations pour tous les partenaires
   * @returns Résultat de la génération
   */
  async generateAllPartnerRecommendations(): Promise<GenerationResponse> {
    try {
      const response = await apiClient.post<GenerationResponse>('/matching/recommendations/generate/all-partners');
      return response;
    } catch (error) {
      console.error('Error generating all partner recommendations:', error);
      throw error;
    }
  }

  /**
   * Génère des recommandations pour toutes les retraites
   * @returns Résultat de la génération
   */
  async generateAllRetreatRecommendations(): Promise<GenerationResponse> {
    try {
      const response = await apiClient.post<GenerationResponse>('/matching/recommendations/generate/all-retreats');
      return response;
    } catch (error) {
      console.error('Error generating all retreat recommendations:', error);
      throw error;
    }
  }
}

export const matchingRecommendationService = new MatchingRecommendationService();
