import { AnalyticsEvent, AnalyticsEventType } from '../hooks/useChatbot';

// Configuration
const ANALYTICS_ENDPOINT = process.env.REACT_APP_ANALYTICS_URL || 'http://localhost:8002/analytics';
const ENABLE_ANALYTICS = process.env.REACT_APP_ENABLE_ANALYTICS === 'true';
const STORAGE_KEY_ANALYTICS = 'rb_chatbot_analytics';
const MAX_STORED_EVENTS = 100;

/**
 * Track an analytics event
 *
 * @param event - The event to track
 */
export const trackEvent = async (event: AnalyticsEvent): Promise<void> => {
  if (!ENABLE_ANALYTICS) {
    // Analytics disabled, just log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Analytics event (disabled):', event);
    }
    return;
  }

  try {
    // Store event locally
    storeEvent(event);

    // Send event to analytics service
    await sendEvent(event);
  } catch (error) {
    console.error('Error tracking analytics event:', error);

    // Store failed event for later retry
    storeFailedEvent(event);
  }
};

/**
 * Store an event in local storage
 *
 * @param event - The event to store
 */
const storeEvent = (event: AnalyticsEvent): void => {
  try {
    const storedEvents = getStoredEvents();

    // Add new event
    storedEvents.push({
      ...event,
      timestamp: event.timestamp.toISOString(),
    });

    // Limit the number of stored events
    if (storedEvents.length > MAX_STORED_EVENTS) {
      storedEvents.shift(); // Remove oldest event
    }

    // Save to local storage
    localStorage.setItem(STORAGE_KEY_ANALYTICS, JSON.stringify(storedEvents));
  } catch (error) {
    console.error('Error storing analytics event:', error);
  }
};

/**
 * Store a failed event for later retry
 *
 * @param event - The event that failed to send
 */
const storeFailedEvent = (event: AnalyticsEvent): void => {
  try {
    const failedEvents = getFailedEvents();

    // Add new failed event
    failedEvents.push({
      ...event,
      timestamp: event.timestamp.toISOString(),
    });

    // Save to local storage
    localStorage.setItem(`${STORAGE_KEY_ANALYTICS}_failed`, JSON.stringify(failedEvents));
  } catch (error) {
    console.error('Error storing failed analytics event:', error);
  }
};

/**
 * Get stored events from local storage
 *
 * @returns Array of stored events
 */
export const getStoredEvents = (): any[] => {
  try {
    const storedData = localStorage.getItem(STORAGE_KEY_ANALYTICS);
    if (!storedData) return [];

    return JSON.parse(storedData);
  } catch (error) {
    console.error('Error retrieving stored analytics events:', error);
    return [];
  }
};

/**
 * Get failed events from local storage
 *
 * @returns Array of failed events
 */
export const getFailedEvents = (): any[] => {
  try {
    const storedData = localStorage.getItem(`${STORAGE_KEY_ANALYTICS}_failed`);
    if (!storedData) return [];

    return JSON.parse(storedData);
  } catch (error) {
    console.error('Error retrieving failed analytics events:', error);
    return [];
  }
};

/**
 * Send an event to the analytics service
 *
 * @param event - The event to send
 */
const sendEvent = async (event: AnalyticsEvent): Promise<void> => {
  try {
    const response = await fetch(ANALYTICS_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...event,
        timestamp: event.timestamp.toISOString(),
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to send analytics event: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Error sending analytics event:', error);
    throw error;
  }
};

/**
 * Retry sending failed events
 *
 * @returns Number of successfully retried events
 */
export const retryFailedEvents = async (): Promise<number> => {
  if (!ENABLE_ANALYTICS) return 0;

  const failedEvents = getFailedEvents();
  if (failedEvents.length === 0) return 0;

  let successCount = 0;
  const remainingFailedEvents = [];

  for (const event of failedEvents) {
    try {
      await sendEvent({
        ...event,
        timestamp: new Date(event.timestamp),
      });
      successCount++;
    } catch (error) {
      remainingFailedEvents.push(event);
    }
  }

  // Update failed events storage
  localStorage.setItem(`${STORAGE_KEY_ANALYTICS}_failed`, JSON.stringify(remainingFailedEvents));

  return successCount;
};

/**
 * Clear all stored analytics data
 */
export const clearAnalyticsData = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEY_ANALYTICS);
    localStorage.removeItem(`${STORAGE_KEY_ANALYTICS}_failed`);
  } catch (error) {
    console.error('Error clearing analytics data:', error);
  }
};

/**
 * Get analytics summary
 *
 * @returns Summary of analytics data
 */
export const getAnalyticsSummary = (): Record<string, number> => {
  const events = getStoredEvents();
  const summary: Record<string, number> = {};

  // Count events by type
  for (const event of events) {
    const type = event.type;
    summary[type] = (summary[type] || 0) + 1;
  }

  return summary;
};
