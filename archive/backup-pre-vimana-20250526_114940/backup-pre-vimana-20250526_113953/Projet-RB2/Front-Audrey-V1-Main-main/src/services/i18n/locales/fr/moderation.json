{"dashboard": {"title": "Tableau de bord de modération", "tabs": {"reports": "Signalements", "rules": "<PERSON><PERSON><PERSON>", "stats": "Statistiques"}}, "reports": {"title": "Signalements", "noReports": "Aucun signalement trouvé", "filters": {"allStatuses": "Tous les statuts", "pending": "En attente", "inReview": "En cours d'examen", "approved": "Approu<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "escalated": "<PERSON><PERSON><PERSON><PERSON>", "allContentTypes": "Tous les types de contenu", "text": "Texte", "image": "Image", "video": "Vidéo", "comment": "Commentaire", "post": "Publication"}, "table": {"id": "ID", "contentType": "Type de contenu", "reason": "<PERSON>son", "status": "Statut", "date": "Date", "actions": "Actions", "view": "Voir"}, "details": {"title": "Détails du signalement", "reportInfo": "Informations sur le signalement", "reportContent": "Contenu signalé", "moderationActions": "Actions de modération", "noActions": "Aucune action de modération n'a été prise pour ce signalement.", "takeAction": "Prendre une action", "id": "ID", "contentType": "Type de contenu", "contentId": "ID du contenu", "reporterId": "ID du signaleur", "createdAt": "<PERSON><PERSON><PERSON>", "status": "Statut", "reason": "<PERSON>son", "description": "Description", "actionType": "Type d'action", "comment": "Commentaire", "commentPlaceholder": "Ajoutez un commentaire expliquant votre décision...", "submitAction": "Soumettre l'action", "by": "Par", "selectAction": "Sélectionnez une action", "actions": {"approve": "Approuver le signalement", "reject": "Rejeter le signalement", "escalate": "Escalader à un administrateur", "warnUser": "Avertir l'utilisateur", "banUser": "Bannir l'utilisateur", "deleteContent": "Supp<PERSON>er le contenu", "hideContent": "Masquer le contenu"}, "errors": {"actionRequired": "Veuillez sélectionner un type d'action", "actionFailed": "Échec de l'ajout de l'action de modération"}, "success": "Action de modération ajoutée avec succès"}}, "contentForm": {"title": "Vérification de contenu", "contentType": "Type de contenu", "types": {"text": "Texte", "comment": "Commentaire", "article": "Article", "image": "Image", "video": "Vidéo"}, "text": "Texte", "textPlaceholder": "Entrez le texte à vérifier...", "imageUrl": "URL de l'image", "imageUrlPlaceholder": "Entrez l'URL de l'image à vérifier...", "uploadImage": "Télécharger une image", "maxFileSize": "Taille maximale du fichier : 5 Mo", "preview": "<PERSON><PERSON><PERSON><PERSON>", "checkContent": "Vérifier le contenu", "newCheck": "Nouvelle vérification", "result": {"title": "Résultat de la vérification", "appropriate": "Contenu approprié", "inappropriate": "Contenu inapproprié", "severity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "Confiance", "matchedRules": "<PERSON><PERSON><PERSON> correspondantes", "categories": "Catégories détectées"}, "errors": {"textRequired": "Veuillez entrer du texte à vérifier", "imageRequired": "Veuillez fournir une URL d'image ou télécharger une image", "fileTooLarge": "Le fichier est trop volumineux. La taille maximale est de 5 Mo", "fileReadError": "<PERSON><PERSON><PERSON> lors de la lecture du fi<PERSON>er", "moderationFailed": "Échec de la vérification du contenu"}}, "rules": {"title": "Règles de modération", "tabs": {"text": "<PERSON><PERSON><PERSON> de texte", "image": "R<PERSON>gles d'image"}, "noRules": "<PERSON><PERSON><PERSON> règle trouvée", "addRule": "Ajouter une règle", "editRule": "Modifier la règle", "deleteRule": "Supprimer la règle", "deleteConfirm": "Êtes-vous sûr de vouloir supprimer cette règle ?", "table": {"name": "Nom", "pattern": "<PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON><PERSON>", "threshold": "<PERSON><PERSON>", "severity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "status": "Statut", "actions": "Actions"}, "form": {"name": "Nom", "namePlaceholder": "Entrez un nom pour la règle", "description": "Description", "descriptionPlaceholder": "Entrez une description pour la règle", "pattern": "<PERSON><PERSON><PERSON> (regex)", "patternPlaceholder": "Entrez un motif regex", "category": "<PERSON><PERSON><PERSON><PERSON>", "categoryPlaceholder": "Entrez une catégorie", "threshold": "Seuil de confiance", "severity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isActive": "Actif", "submit": "Enregistrer la règle", "cancel": "Annuler"}, "errors": {"nameRequired": "Le nom est requis", "patternRequired": "Le motif est requis", "categoryRequired": "La catégorie est requise", "thresholdRequired": "Le seuil est requis", "severityRequired": "La sévérité est requise", "saveFailed": "Échec de l'enregistrement de la règle", "deleteFailed": "Échec de la suppression de la règle"}, "success": {"saved": "<PERSON><PERSON>gle enregistrée avec succès", "deleted": "Règle supprimée avec succès"}}, "stats": {"title": "Statistiques de modération", "total": "Total des signalements", "pending": "En attente", "inReview": "En cours d'examen", "approved": "Approuvés", "rejected": "Rejetés", "escalated": "Escaladés", "averageTime": "Temps moyen de traitement", "byContentType": "Signalements par type de contenu", "byStatus": "Signalements par statut", "byDate": "Signalements par date"}, "contentModeration": {"title": "Modération de contenu", "description": "Utilisez cet outil pour vérifier si un contenu est approprié selon nos règles de modération. Vous pouvez vérifier du texte, des commentaires, des articles, des images ou des vidéos."}, "accessDeniedMessage": "Vous n'avez pas les autorisations nécessaires pour accéder au tableau de bord de modération. Veuillez contacter un administrateur si vous pensez que c'est une erreur."}