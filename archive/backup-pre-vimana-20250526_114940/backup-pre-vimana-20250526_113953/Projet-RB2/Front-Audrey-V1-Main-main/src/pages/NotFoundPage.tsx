/**
 * Page 404 - Retreat And Be
 * Date de création: 25 mai 2025
 * 
 * Page d'erreur 404 avec design unifié.
 */

import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Con<PERSON>er,
  Card,
  CardContent,
  Button,
  Stack
} from '../components/ui/design-system';

const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex items-center justify-center">
      <Container>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center"
        >
          <Card className="max-w-md mx-auto">
            <CardContent className="py-12">
              <div className="text-8xl mb-6">🧘‍♀️</div>
              <h1 className="text-6xl font-bold text-primary-600 mb-4">404</h1>
              <h2 className="text-2xl font-semibold text-neutral-900 mb-4">
                Page introuvable
              </h2>
              <p className="text-neutral-600 mb-8">
                Oups ! Il semblerait que cette page ait pris une pause bien-être. 
                Retournons ensemble vers la sérénité.
              </p>
              
              <Stack spacing="sm">
                <Button
                  variant="primary"
                  size="lg"
                  fullWidth
                  onClick={() => navigate('/')}
                  leftIcon="🏠"
                >
                  Retour à l'accueil
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  fullWidth
                  onClick={() => window.history.back()}
                  leftIcon="←"
                >
                  Page précédente
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </motion.div>
      </Container>
    </div>
  );
};

export default NotFoundPage;
