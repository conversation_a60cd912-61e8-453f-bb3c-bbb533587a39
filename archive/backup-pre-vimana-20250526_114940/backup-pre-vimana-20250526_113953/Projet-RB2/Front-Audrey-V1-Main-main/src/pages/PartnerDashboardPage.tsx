import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useAuthContext } from '../hooks/useAuthContext';
import { partnerService } from '../services/api/partnerService';
import { bookingService, Booking } from '../services/api/bookingService';
import { toast } from 'react-toastify';
import Navbar from '../components/organisms/Navbar/Navbar';
import Footer from '../components/organisms/Footer/Footer';
import PartnerMatchingDashboard from '../components/partner/PartnerMatchingDashboard';
import PartnerRecommendations from '../components/partner/PartnerRecommendations';
import LoadingSpinner from '../components/atoms/LoadingSpinner/LoadingSpinner';

const PartnerDashboardPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();

  const [partner, setPartner] = useState<any>(null);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [stats, setStats] = useState<{
    totalBookings: number;
    pendingBookings: number;
    completedBookings: number;
    totalRevenue: number;
  }>({
    totalBookings: 0,
    pendingBookings: 0,
    completedBookings: 0,
    totalRevenue: 0,
  });
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Rediriger si l'utilisateur n'est pas connecté
    if (!user) {
      navigate('/login');
      return;
    }

    const loadData = async () => {
      try {
        setIsLoading(true);

        // Charger les informations du partenaire
        const partnerData = await partnerService.getPartnerById(user.id);
        setPartner(partnerData);

        if (!partnerData) {
          setError('Vous n\'êtes pas enregistré en tant que partenaire');
          setIsLoading(false);
          return;
        }

        // Charger les réservations du partenaire
        const bookingsData = await bookingService.getHostBookings();
        setBookings(bookingsData.data || []);

        // Calculer les statistiques
        const totalBookings = bookingsData.data?.length || 0;
        const pendingBookings = bookingsData.data?.filter((b: Booking) => b.status === 'pending').length || 0;
        const completedBookings = bookingsData.data?.filter((b: Booking) => b.status === 'completed').length || 0;
        const totalRevenue = bookingsData.data?.reduce((sum: number, b: Booking) => sum + (b.totalPrice || 0), 0) || 0;

        setStats({
          totalBookings,
          pendingBookings,
          completedBookings,
          totalRevenue,
        });

        setError(null);
      } catch (error) {
        console.error('Erreur lors du chargement des données du tableau de bord:', error);
        setError('Impossible de charger les données du tableau de bord');
        toast.error('Erreur lors du chargement des données');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [user, navigate]);

  // Formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    }).format(date);
  };

  // Formater le montant
  const formatAmount = (amount: number | undefined) => {
    if (typeof amount !== 'number') return 'N/A'; // Handle undefined amount
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
    }).format(amount);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Navbar />
        <main className="pt-24 pb-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner />
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error || !partner) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Navbar />
        <main className="pt-24 pb-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="text-center py-8">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  {error || 'Vous n\'êtes pas enregistré en tant que partenaire'}
                </h3>
                <div className="mt-6">
                  <Link
                    to="/become-partner"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
                  >
                    Devenir partenaire
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>Tableau de bord Partenaire | Retreat And Be</title>
        <meta
          name="description"
          content="Gérez votre activité de partenaire, consultez vos réservations et trouvez de nouvelles opportunités."
        />
      </Helmet>

      <Navbar />

      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Tableau de bord Partenaire</h1>
            <p className="mt-1 text-sm text-gray-500">
              Bienvenue, {partner.companyName}. Gérez votre activité et trouvez de nouvelles opportunités.
            </p>
          </div>

          {/* Statistiques */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-retreat-green rounded-md p-3">
                  <svg
                    className="h-6 w-6 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Réservations totales
                    </dt>
                    <dd>
                      <div className="text-lg font-medium text-gray-900">{stats.totalBookings}</div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-yellow-500 rounded-md p-3">
                  <svg
                    className="h-6 w-6 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Réservations en attente
                    </dt>
                    <dd>
                      <div className="text-lg font-medium text-gray-900">{stats.pendingBookings}</div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-green-500 rounded-md p-3">
                  <svg
                    className="h-6 w-6 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Réservations complétées
                    </dt>
                    <dd>
                      <div className="text-lg font-medium text-gray-900">{stats.completedBookings}</div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-purple-500 rounded-md p-3">
                  <svg
                    className="h-6 w-6 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Revenus totaux
                    </dt>
                    <dd>
                      <div className="text-lg font-medium text-gray-900">{formatAmount(stats.totalRevenue)}</div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* Recommandations personnalisées */}
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Recommandations personnalisées</h2>
            <PartnerRecommendations partnerId={partner.id} />
          </div>

          {/* Gestion de contenu */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Gestion de contenu</h2>
              <Link
                to="/content-management"
                className="text-sm text-retreat-green hover:text-retreat-green-dark"
              >
                Gérer tout le contenu
              </Link>
            </div>
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex flex-col items-center p-4 border border-gray-200 rounded-lg">
                  <svg className="h-12 w-12 text-retreat-green mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-1">Publications</h3>
                  <p className="text-sm text-gray-500 text-center mb-3">Gérez vos publications et vidéos</p>
                  <Link
                    to="/content-management"
                    className="mt-auto px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                  >
                    Gérer
                  </Link>
                </div>
                <div className="flex flex-col items-center p-4 border border-gray-200 rounded-lg">
                  <svg className="h-12 w-12 text-retreat-green mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-1">Vidéos</h3>
                  <p className="text-sm text-gray-500 text-center mb-3">Créez et gérez vos vidéos</p>
                  <Link
                    to="/content-management"
                    className="mt-auto px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                  >
                    Gérer
                  </Link>
                </div>
                <div className="flex flex-col items-center p-4 border border-gray-200 rounded-lg">
                  <svg className="h-12 w-12 text-retreat-green mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-1">Événements</h3>
                  <p className="text-sm text-gray-500 text-center mb-3">Planifiez vos événements en direct</p>
                  <Link
                    to="/content-management"
                    className="mt-auto px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                  >
                    Gérer
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Tableau de bord de matching */}
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Opportunités de matching</h2>
            <PartnerMatchingDashboard partnerId={partner.id} />
          </div>

          {/* Réservations récentes */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Réservations récentes</h2>
              <Link
                to="/partner/bookings"
                className="text-sm text-retreat-green hover:text-retreat-green-dark"
              >
                Voir toutes les réservations
              </Link>
            </div>

            {bookings.length === 0 ? (
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="text-center py-8">
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    Aucune réservation pour le moment
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Vos réservations apparaîtront ici une fois que vous aurez reçu des demandes.
                  </p>
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Retraite
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Client
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Date
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Statut
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Montant
                        </th>
                        <th
                          scope="col"
                          className="relative px-6 py-3"
                        >
                          <span className="sr-only">Actions</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {bookings.slice(0, 5).map((booking) => (
                        <tr key={booking.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {booking.retreat?.title || 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {booking.user ? `${booking.user.firstName || ''} ${booking.user.lastName || ''}`.trim() : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(booking.createdAt)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                booking.status === 'completed'
                                  ? 'bg-green-100 text-green-800'
                                  : booking.status === 'pending'
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : booking.status === 'confirmed'
                                  ? 'bg-blue-100 text-blue-800'
                                  : booking.status === 'cancelled'
                                  ? 'bg-red-100 text-red-800'
                                  : 'bg-gray-100 text-gray-800'
                              }`}
                            >
                              {booking.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatAmount(booking.totalPrice)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <Link
                              to={`/partner/bookings/${booking.id}`}
                              className="text-retreat-green hover:text-retreat-green-dark"
                            >
                              Détails
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default PartnerDashboardPage;
