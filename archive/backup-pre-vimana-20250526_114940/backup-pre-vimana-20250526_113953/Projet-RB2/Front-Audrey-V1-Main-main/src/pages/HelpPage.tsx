import React, { useState } from 'react';
import { motion } from 'framer-motion';
import NavBarClient from '../components/organisms/NavBarClient/NavBarClient';
import Footer from '../components/ui/Footer';
import { ChevronDownIcon } from '@heroicons/react/24/outline';
import ChatBot from '../components/atoms/ChatBot';

interface FAQItem {
  question: string;
  answer: React.ReactNode;
}

interface FAQCategory {
  title: string;
  icon: string;
  questions: FAQItem[];
}

const HelpPage: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [openQuestions, setOpenQuestions] = useState<Set<string>>(new Set());

  const faqCategories: FAQCategory[] = [
    {
      title: 'Réservation et paiement',
      icon: '💳',
      questions: [
        {
          question: 'Comment réserver une retraite ?',
          answer: (
            <div className='space-y-2'>
              <p>Pour réserver une retraite, suivez ces étapes simples :</p>
              <ol className='list-decimal list-inside pl-4'>
                <li>Sélectionnez la retraite qui vous intéresse</li>
                <li>Vérifiez les disponibilités sur le calendrier</li>
                <li>Cliquez sur &quot;Réserver&quot;</li>
                <li>Remplissez vos informations personnelles</li>
                <li>Procédez au paiement sécurisé</li>
              </ol>
            </div>
          ),
        },
        {
          question: 'Quels sont les modes de paiement acceptés ?',
          answer: (
            <div className='space-y-2'>
              <p>Nous acceptons plusieurs modes de paiement :</p>
              <ul className='list-disc list-inside pl-4'>
                <li>Cartes bancaires : Visa, Mastercard, American Express</li>
                <li>PayPal</li>
                <li>Virements bancaires</li>
                <li>Crypto-monnaie RandB (avec 5% de bonus sur chaque transaction)</li>
              </ul>
              <p className='mt-2 text-sm text-gray-600'>
                Pour certaines retraites, nous proposons également le paiement en plusieurs fois
                (jusqu&apos;à 3x sans frais).
              </p>
            </div>
          ),
        },
        {
          question: 'Comment payer avec la crypto-monnaie RandB ?',
          answer: (
            <div className='space-y-2'>
              <p>Pour payer avec RandB, suivez ces étapes :</p>
              <ol className='list-decimal list-inside pl-4'>
                <li>Sélectionnez &quot;Payer avec RandB&quot; lors du checkout</li>
                <li>Scannez le QR code avec votre wallet RandB</li>
                <li>
                  Confirmez le montant en RandB (le taux de conversion est mis à jour en temps réel)
                </li>
                <li>Validez la transaction dans votre wallet</li>
              </ol>
              <div className='mt-4 p-4 bg-mint-50 rounded-lg border border-mint-200'>
                <h4 className='font-semibold text-retreat-green mb-2'>Avantages RandB :</h4>
                <ul className='list-disc list-inside space-y-2 text-gray-700'>
                  <li>Bonus immédiat de 5% en RandB sur chaque réservation</li>
                  <li>
                    Programme de fidélité exclusif :
                    <ul className='list-circle list-inside ml-4 mt-1'>
                      <li>Niveau Bronze (1-3 réservations) : +2% de RandB bonus</li>
                      <li>Niveau Argent (4-6 réservations) : +5% de RandB bonus</li>
                      <li>Niveau Or (7+ réservations) : +10% de RandB bonus</li>
                    </ul>
                  </li>
                  <li>Accès prioritaire aux nouvelles retraites</li>
                  <li>Réductions exclusives sur les services premium</li>
                </ul>
              </div>
              <p className='mt-2 text-sm text-gray-600'>
                Note : Les bonus RandB sont cumulables avec les offres saisonnières.
              </p>
            </div>
          ),
        },
        {
          question: 'Programme de fidélité RandB',
          answer: (
            <div className='space-y-4'>
              <p>Notre programme de fidélité RandB récompense votre engagement :</p>
              <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                <div className='p-4 bg-amber-50 rounded-lg border border-amber-200'>
                  <h4 className='font-semibold text-amber-700 mb-2'>Niveau Bronze</h4>
                  <ul className='list-disc list-inside text-sm space-y-1'>
                    <li>1-3 réservations</li>
                    <li>+2% bonus RandB</li>
                    <li>Accès aux ventes privées</li>
                  </ul>
                </div>
                <div className='p-4 bg-gray-50 rounded-lg border border-gray-200'>
                  <h4 className='font-semibold text-gray-700 mb-2'>Niveau Argent</h4>
                  <ul className='list-disc list-inside text-sm space-y-1'>
                    <li>4-6 réservations</li>
                    <li>+5% bonus RandB</li>
                    <li>Accès prioritaire aux retraites</li>
                    <li>-10% sur les services premium</li>
                  </ul>
                </div>
                <div className='p-4 bg-yellow-50 rounded-lg border border-yellow-200'>
                  <h4 className='font-semibold text-yellow-700 mb-2'>Niveau Or</h4>
                  <ul className='list-disc list-inside text-sm space-y-1'>
                    <li>7+ réservations</li>
                    <li>+10% bonus RandB</li>
                    <li>Accès VIP aux nouvelles retraites</li>
                    <li>-20% sur les services premium</li>
                    <li>Concierge personnel</li>
                  </ul>
                </div>
              </div>
              <p className='text-sm text-gray-600 mt-4'>
                Les niveaux sont calculés sur une période de 12 mois glissants. Les bonus sont
                automatiquement appliqués à chaque réservation.
              </p>
            </div>
          ),
        },
        {
          question: 'Quelle est la politique d&apos;annulation ?',
          answer: (
            <div className='space-y-2'>
              <p>Notre politique d&apos;annulation varie selon le délai avant la retraite :</p>
              <ul className='list-disc list-inside pl-4'>
                <li>Plus de 30 jours avant : remboursement complet</li>
                <li>Entre 30 et 14 jours : remboursement de 50%</li>
                <li>Moins de 14 jours : aucun remboursement</li>
              </ul>
              <div className='mt-4 p-4 bg-mint-50 rounded-lg border border-mint-200'>
                <h4 className='font-semibold text-retreat-green mb-2'>
                  Spécificités pour les paiements en RandB :
                </h4>
                <ul className='list-disc list-inside space-y-2 text-gray-700'>
                  <li>Le remboursement est effectué dans la même devise (RandB)</li>
                  <li>Le taux de conversion appliqué est celui du jour du remboursement</li>
                  <li>
                    Bonus de remboursement :
                    <ul className='list-circle list-inside ml-4 mt-1'>
                      <li>+2% si annulation à plus de 30 jours</li>
                      <li>+1% si annulation entre 14 et 30 jours</li>
                    </ul>
                  </li>
                  <li>Conservation des RandB bonus déjà acquis</li>
                </ul>
              </div>
              <p className='mt-2 text-sm text-gray-600'>
                Note : Les conditions peuvent varier pour les offres spéciales et les réservations
                de groupe.
              </p>
            </div>
          ),
        },
      ],
    },
    {
      title: 'Préparation et logistique',
      icon: '🎒',
      questions: [
        {
          question: 'Que dois-je apporter ?',
          answer: (
            <div className='space-y-4'>
              <div>
                <h4 className='font-semibold mb-2'>Essentiels de base :</h4>
                <ul className='list-disc list-inside pl-4'>
                  <li>Vêtements confortables pour la pratique</li>
                  <li>Tapis de yoga (si non fourni par le centre)</li>
                  <li>Produits d&apos;hygiène personnelle</li>
                  <li>Médicaments personnels</li>
                  <li>Documents d&apos;identité et de voyage</li>
                  <li>Carnet de notes</li>
                </ul>
              </div>

              <div className='mt-4'>
                <h4 className='font-semibold mb-2'>Selon le type de retraite :</h4>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <div className='p-3 bg-green-50 rounded-lg'>
                    <h5 className='font-medium text-green-700'>Yoga & Méditation</h5>
                    <ul className='list-disc list-inside text-sm'>
                      <li>Coussin de méditation</li>
                      <li>Châle ou couverture légère</li>
                      <li>Blocs et sangles de yoga</li>
                      <li>Vêtements amples</li>
                    </ul>
                  </div>
                  <div className='p-3 bg-blue-50 rounded-lg'>
                    <h5 className='font-medium text-blue-700'>Bien-être & Spa</h5>
                    <ul className='list-disc list-inside text-sm'>
                      <li>Maillot de bain</li>
                      <li>Peignoir</li>
                      <li>Sandales de piscine</li>
                      <li>Tenue de sport légère</li>
                    </ul>
                  </div>
                  <div className='p-3 bg-amber-50 rounded-lg'>
                    <h5 className='font-medium text-amber-700'>Nature & Aventure</h5>
                    <ul className='list-disc list-inside text-sm'>
                      <li>Chaussures de randonnée</li>
                      <li>Protection solaire</li>
                      <li>Sac à dos de jour</li>
                      <li>Gourde isotherme</li>
                    </ul>
                  </div>
                  <div className='p-3 bg-purple-50 rounded-lg'>
                    <h5 className='font-medium text-purple-700'>Detox & Jeûne</h5>
                    <ul className='list-disc list-inside text-sm'>
                      <li>Tisanes préférées</li>
                      <li>Complément alimentaires habituels</li>
                      <li>Bouillotte</li>
                      <li>Vêtements confortables</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className='mt-4'>
                <h4 className='font-semibold mb-2'>Selon la destination :</h4>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <div className='p-3 bg-yellow-50 rounded-lg'>
                    <h5 className='font-medium text-yellow-700'>Destinations tropicales</h5>
                    <ul className='list-disc list-inside text-sm'>
                      <li>Répulsif anti-moustiques naturel</li>
                      <li>Chapeau ou casquette</li>
                      <li>Vêtements légers et respirants</li>
                      <li>Adaptateur électrique universel</li>
                    </ul>
                  </div>
                  <div className='p-3 bg-indigo-50 rounded-lg'>
                    <h5 className='font-medium text-indigo-700'>Destinations montagneuses</h5>
                    <ul className='list-disc list-inside text-sm'>
                      <li>Vêtements chauds et superposables</li>
                      <li>Protection contre le soleil en altitude</li>
                      <li>Bâtons de marche</li>
                      <li>Sac de couchage (si spécifié)</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          ),
        },
        {
          question: 'Comment organiser mon voyage ?',
          answer: (
            <div className='space-y-4'>
              <p>Notre équipe vous accompagne dans l&apos;organisation de votre voyage :</p>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='p-3 bg-teal-50 rounded-lg'>
                  <h5 className='font-medium text-teal-700'>Services inclus</h5>
                  <ul className='list-disc list-inside text-sm'>
                    <li>Transferts depuis l&apos;aéroport/gare</li>
                    <li>Navettes inter-sites</li>
                    <li>Guide de préparation personnalisé</li>
                    <li>Assistance 24/7</li>
                  </ul>
                </div>
                <div className='p-3 bg-rose-50 rounded-lg'>
                  <h5 className='font-medium text-rose-700'>Services optionnels</h5>
                  <ul className='list-disc list-inside text-sm'>
                    <li>Réservation des vols</li>
                    <li>Nuits d&apos;hôtel pré/post retraite</li>
                    <li>Visites touristiques</li>
                    <li>Location de matériel spécifique</li>
                  </ul>
                </div>
              </div>
            </div>
          ),
        },
      ],
    },
    {
      title: 'Pendant votre séjour',
      icon: '🌟',
      questions: [
        {
          question: 'Les repas sont-ils inclus ?',
          answer:
            'La majorité de nos retraites incluent la pension complète avec des repas végétariens ou végans. Les régimes spéciaux sont généralement accommodés sur demande préalable.',
        },
        {
          question: 'Y a-t-il du Wi-Fi ?',
          answer:
            "La disponibilité du Wi-Fi varie selon les centres. Certaines retraites sont spécifiquement 'digital detox' sans connexion internet. Cette information est clairement indiquée dans la description de chaque retraite.",
        },
      ],
    },
    {
      title: 'Compte et profil',
      icon: '👤',
      questions: [
        {
          question: 'Comment modifier mes informations personnelles ?',
          answer:
            "Connectez-vous à votre compte, allez dans 'Paramètres du compte' pour modifier vos informations personnelles, préférences de communication et coordonnées.",
        },
        {
          question: 'Comment gérer mes réservations ?',
          answer:
            "Dans votre espace personnel, la section 'Mes réservations' vous permet de consulter vos réservations en cours et passées, télécharger vos factures et modifier vos réservations si nécessaire.",
        },
      ],
    },
    {
      title: 'Assurances et Protection',
      icon: '🛡️',
      questions: [
        {
          question: 'Quelles assurances sont incluses ?',
          answer: (
            <div className='space-y-4'>
              <p>Toutes nos retraites incluent une couverture de base :</p>
              <ul className='list-disc list-inside pl-4'>
                <li>Responsabilité civile pendant les activités</li>
                <li>Assistance médicale de base</li>
                <li>Protection des bagages pendant les transferts</li>
                <li>Garantie COVID-19 standard</li>
              </ul>
            </div>
          ),
        },
        {
          question: 'Quelles assurances complémentaires sont recommandées ?',
          answer: (
            <div className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='p-3 bg-emerald-50 rounded-lg'>
                  <h5 className='font-medium text-emerald-700'>Assurance Premium</h5>
                  <ul className='list-disc list-inside text-sm'>
                    <li>Couverture médicale étendue</li>
                    <li>Rapatriement médical</li>
                    <li>Annulation tous risques</li>
                    <li>Protection des objets de valeur</li>
                    <li>29€/séjour</li>
                  </ul>
                </div>
                <div className='p-3 bg-cyan-50 rounded-lg'>
                  <h5 className='font-medium text-cyan-700'>Assurance Aventure</h5>
                  <ul className='list-disc list-inside text-sm'>
                    <li>Sports et activités extrêmes</li>
                    <li>Secours en montagne</li>
                    <li>Matériel sportif</li>
                    <li>Responsabilité civile renforcée</li>
                    <li>39€/séjour</li>
                  </ul>
                </div>
              </div>
              <div className='mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200'>
                <h4 className='font-semibold text-gray-700 mb-2'>Garanties spéciales</h4>
                <ul className='list-disc list-inside text-sm space-y-1'>
                  <li>Extension COVID-19 Premium (+15€)</li>
                  <li>Garantie Sérénité - remboursement sans justification (+25€)</li>
                  <li>Option Accompagnants - couverture famille (+19€/pers)</li>
                  <li>Protection des appareils électroniques (+12€)</li>
                </ul>
              </div>
            </div>
          ),
        },
        {
          question: 'Comment souscrire une assurance ?',
          answer: (
            <div className='space-y-2'>
              <p>Vous pouvez souscrire une assurance :</p>
              <ul className='list-disc list-inside pl-4'>
                <li>Lors de votre réservation en ligne</li>
                <li>Dans votre espace client jusqu&apos;à 7 jours avant le départ</li>
                <li>Auprès de notre service client</li>
              </ul>
              <div className='mt-4 p-4 bg-blue-50 rounded-lg'>
                <p className='text-sm text-blue-700'>
                  Pour les paiements en RandB, bénéficiez de -10% sur toutes les assurances
                  complémentaires.
                </p>
              </div>
            </div>
          ),
        },
      ],
    },
  ];

  const toggleCategory = (title: string) => {
    setActiveCategory(activeCategory === title ? null : title);
  };

  const toggleQuestion = (question: string) => {
    const newOpenQuestions = new Set(openQuestions);
    if (newOpenQuestions.has(question)) {
      newOpenQuestions.delete(question);
    } else {
      newOpenQuestions.add(question);
    }
    setOpenQuestions(newOpenQuestions);
  };

  return (
    <div className='min-h-screen bg-gray-50'>
      <NavBarClient />

      <main className='pt-24 pb-16'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          {/* Hero Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className='text-center mb-16'
          >
            <h1 className='text-4xl font-bold text-retreat-green mb-4'>Centre d&apos;aide</h1>
            <p className='text-xl text-gray-600 max-w-2xl mx-auto'>
              Trouvez rapidement des réponses à vos questions et profitez pleinement de votre
              expérience de retraite.
            </p>
          </motion.div>

          {/* Search Bar */}
          <div className='max-w-2xl mx-auto mb-16'>
            <div className='relative'>
              <input
                type='text'
                placeholder='Rechercher une question...'
                className='w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-retreat-green focus:border-transparent'
              />
              <button className='absolute right-3 top-1/2 transform -translate-y-1/2 text-retreat-green'>
                🔍
              </button>
            </div>
          </div>

          {/* FAQ Categories */}
          <div className='grid gap-8 md:grid-cols-2 lg:grid-cols-1 max-w-4xl mx-auto'>
            {faqCategories.map((category) => (
              <motion.div
                key={category.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className='bg-white rounded-xl shadow-sm overflow-hidden'
              >
                <button
                  onClick={() => toggleCategory(category.title)}
                  className='w-full px-6 py-4 flex items-center justify-between text-left hover:bg-gray-50'
                >
                  <div className='flex items-center space-x-3'>
                    <span className='text-2xl'>{category.icon}</span>
                    <h2 className='text-xl font-semibold text-gray-900'>{category.title}</h2>
                  </div>
                  <ChevronDownIcon
                    className={`w-5 h-5 transform transition-transform ${
                      activeCategory === category.title ? 'rotate-180' : ''
                    }`}
                  />
                </button>

                {activeCategory === category.title && (
                  <div className='px-6 pb-4'>
                    {category.questions.map((item) => (
                      <div key={item.question} className='border-b border-gray-100 last:border-0'>
                        <button
                          onClick={() => toggleQuestion(item.question)}
                          className='w-full py-4 flex items-center justify-between text-left hover:text-retreat-green'
                        >
                          <span className='font-medium'>{item.question}</span>
                          <ChevronDownIcon
                            className={`w-4 h-4 transform transition-transform ${
                              openQuestions.has(item.question) ? 'rotate-180' : ''
                            }`}
                          />
                        </button>

                        {openQuestions.has(item.question) && (
                          <div className='pb-4 text-gray-600'>{item.answer}</div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </motion.div>
            ))}
          </div>

          {/* Contact Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className='mt-16 text-center bg-white rounded-xl shadow-sm p-8 max-w-2xl mx-auto'
          >
            <h2 className='text-2xl font-semibold text-retreat-green mb-4'>
              Vous n&apos;avez pas trouvé votre réponse ?
            </h2>
            <p className='text-gray-600 mb-6'>
              Notre équipe est là pour vous aider. Contactez-nous directement ou utilisez notre chat
              en direct.
            </p>
            <div className='flex justify-center space-x-4'>
              <button className='px-6 py-2 bg-retreat-green text-white rounded-lg hover:bg-retreat-green-dark transition-colors'>
                Nous contacter
              </button>
            </div>
          </motion.div>
        </div>
      </main>

      <Footer />

      {/* Ajout du ChatBot */}
      <ChatBot />
    </div>
  );
};

export default HelpPage;
