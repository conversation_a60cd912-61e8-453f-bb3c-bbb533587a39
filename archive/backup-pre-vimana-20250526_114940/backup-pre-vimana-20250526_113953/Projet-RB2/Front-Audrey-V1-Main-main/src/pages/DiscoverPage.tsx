import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '../hooks/useAuthContext';
import { diversityFairnessService } from '../services/api/diversityFairnessService';
import { toast } from 'react-toastify';
import { t } from '../services/i18n/i18nService';
import NavBar from '../components/organisms/NavBar/NavBar';
import Footer from '../components/organisms/Footer/Footer';
import RetreatCard from '../components/retreats/RetreatCard';
import LoadingSpinner from '../components/common/LoadingSpinner';

/**
 * Page "Découvrir" pour explorer de nouvelles retraites
 */
const DiscoverPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<{
    category: string;
    priceRange: string;
    duration: string;
    location: string;
  }>({
    category: '',
    priceRange: '',
    duration: '',
    location: '',
  });
  
  // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
  useEffect(() => {
    if (!user) {
      navigate('/login', { state: { from: '/discover' } });
    }
  }, [user, navigate]);
  
  // Charger les recommandations surprenantes
  useEffect(() => {
    const loadDiscoverRecommendations = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const discoverRecommendations = await diversityFairnessService.getDiscoverRecommendations(12);
        setRecommendations(discoverRecommendations);
      } catch (error) {
        console.error('Erreur lors du chargement des recommandations surprenantes:', error);
        setError(t('discover.loadError'));
      } finally {
        setLoading(false);
      }
    };
    
    if (user) {
      loadDiscoverRecommendations();
    }
  }, [user]);
  
  // Gérer le changement de filtre
  const handleFilterChange = (name: string, value: string) => {
    setFilters({
      ...filters,
      [name]: value,
    });
  };
  
  // Filtrer les recommandations
  const filteredRecommendations = recommendations.filter((recommendation) => {
    // Filtrer par catégorie
    if (filters.category && recommendation.category !== filters.category) {
      return false;
    }
    
    // Filtrer par plage de prix
    if (filters.priceRange) {
      const price = recommendation.price || 0;
      
      if (
        (filters.priceRange === 'budget' && price >= 500) ||
        (filters.priceRange === 'standard' && (price < 500 || price >= 1500)) ||
        (filters.priceRange === 'premium' && price < 1500)
      ) {
        return false;
      }
    }
    
    // Filtrer par durée
    if (filters.duration) {
      const duration = recommendation.duration || 0;
      
      if (
        (filters.duration === 'short' && duration > 3) ||
        (filters.duration === 'medium' && (duration <= 3 || duration > 7)) ||
        (filters.duration === 'long' && duration <= 7)
      ) {
        return false;
      }
    }
    
    // Filtrer par localisation
    if (filters.location && recommendation.location !== filters.location) {
      return false;
    }
    
    return true;
  });
  
  // Gérer le rafraîchissement des recommandations
  const handleRefresh = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const discoverRecommendations = await diversityFairnessService.getDiscoverRecommendations(12);
      setRecommendations(discoverRecommendations);
      
      // Réinitialiser les filtres
      setFilters({
        category: '',
        priceRange: '',
        duration: '',
        location: '',
      });
      
      toast.success(t('discover.refreshSuccess'));
    } catch (error) {
      console.error('Erreur lors du rafraîchissement des recommandations surprenantes:', error);
      setError(t('discover.refreshError'));
      toast.error(t('discover.refreshError'));
    } finally {
      setLoading(false);
    }
  };
  
  // Récupérer les options de filtre
  const categoryOptions = [
    { value: '', label: t('discover.filters.allCategories') },
    { value: 'yoga', label: t('discover.filters.categories.yoga') },
    { value: 'meditation', label: t('discover.filters.categories.meditation') },
    { value: 'wellness', label: t('discover.filters.categories.wellness') },
    { value: 'fitness', label: t('discover.filters.categories.fitness') },
    { value: 'spiritual', label: t('discover.filters.categories.spiritual') },
    { value: 'nature', label: t('discover.filters.categories.nature') },
  ];
  
  const priceRangeOptions = [
    { value: '', label: t('discover.filters.allPrices') },
    { value: 'budget', label: t('discover.filters.prices.budget') },
    { value: 'standard', label: t('discover.filters.prices.standard') },
    { value: 'premium', label: t('discover.filters.prices.premium') },
  ];
  
  const durationOptions = [
    { value: '', label: t('discover.filters.allDurations') },
    { value: 'short', label: t('discover.filters.durations.short') },
    { value: 'medium', label: t('discover.filters.durations.medium') },
    { value: 'long', label: t('discover.filters.durations.long') },
  ];
  
  const locationOptions = [
    { value: '', label: t('discover.filters.allLocations') },
    { value: 'europe', label: t('discover.filters.locations.europe') },
    { value: 'asia', label: t('discover.filters.locations.asia') },
    { value: 'americas', label: t('discover.filters.locations.americas') },
    { value: 'africa', label: t('discover.filters.locations.africa') },
    { value: 'oceania', label: t('discover.filters.locations.oceania') },
  ];
  
  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>{t('discover.pageTitle')} | Retreat And Be</title>
        <meta
          name="description"
          content={t('discover.pageDescription')}
        />
      </Helmet>
      
      <NavBar />
      
      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('discover.title')}</h1>
            <p className="text-lg text-gray-600">{t('discover.description')}</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('discover.filters.title')}</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Filtre de catégorie */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('discover.filters.category')}
                </label>
                <select
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                >
                  {categoryOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Filtre de prix */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('discover.filters.price')}
                </label>
                <select
                  value={filters.priceRange}
                  onChange={(e) => handleFilterChange('priceRange', e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                >
                  {priceRangeOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Filtre de durée */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('discover.filters.duration')}
                </label>
                <select
                  value={filters.duration}
                  onChange={(e) => handleFilterChange('duration', e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                >
                  {durationOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Filtre de localisation */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('discover.filters.location')}
                </label>
                <select
                  value={filters.location}
                  onChange={(e) => handleFilterChange('location', e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                >
                  {locationOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="mt-4 flex justify-end">
              <button
                onClick={handleRefresh}
                className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
              >
                {t('discover.refresh')}
              </button>
            </div>
          </div>
          
          {/* Contenu principal */}
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner />
            </div>
          ) : error ? (
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-center">
                <button
                  onClick={handleRefresh}
                  className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                >
                  {t('discover.tryAgain')}
                </button>
              </div>
            </div>
          ) : filteredRecommendations.length === 0 ? (
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="text-center py-12">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{t('discover.noFilteredResults')}</h3>
                <p className="text-gray-500 mb-6">{t('discover.tryDifferentFilters')}</p>
                
                <button
                  onClick={() => setFilters({ category: '', priceRange: '', duration: '', location: '' })}
                  className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                >
                  {t('discover.clearFilters')}
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredRecommendations.map((recommendation) => (
                <RetreatCard
                  key={recommendation.id}
                  retreat={recommendation}
                  isSurprising={recommendation.isSurprising}
                />
              ))}
            </div>
          )}
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default DiscoverPage;
