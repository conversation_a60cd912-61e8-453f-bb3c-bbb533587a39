import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useAuthContext } from '../../hooks/useAuthContext';
import AdminNavBar from '../../components/organisms/AdminNavBar/AdminNavBar';
import { toast } from 'react-toastify';
import { t } from '../../services/i18n/i18nService';

/**
 * Interface pour les métriques du tableau de bord
 */
interface DashboardMetrics {
  interactionRate: number;
  interactionRateChange: number;
  conversionRate: number;
  conversionRateChange: number;
  averageRating: number;
  averageRatingChange: number;
  totalOptimizations: number;
  lastOptimizationDate: string | null;
  topFactors: Array<{
    name: string;
    weight: number;
    impact: number;
  }>;
  performanceHistory: Array<{
    date: string;
    interactionRate: number;
    conversionRate: number;
    averageRating: number;
  }>;
  recentOptimizations: Array<{
    date: string;
    description: string;
    improvement: number;
  }>;
}

/**
 * Page de tableau de bord pour les explications
 */
const ExplanationDashboardPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();

  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter' | 'year'>('month');

  // Vérifier si l'utilisateur est administrateur
  useEffect(() => {
    if (user && user.role !== 'ADMIN') {
      toast.error('Accès non autorisé');
      navigate('/');
    }
  }, [user, navigate]);

  // Charger les métriques
  useEffect(() => {
    const loadMetrics = async () => {
      try {
        setLoading(true);
        setError(null);

        // Dans une implémentation réelle, nous récupérerions les métriques depuis l'API
        // Pour cet exemple, nous utilisons des données fictives
        const mockMetrics: DashboardMetrics = {
          interactionRate: 0.55,
          interactionRateChange: 0.13,
          conversionRate: 0.23,
          conversionRateChange: 0.05,
          averageRating: 4.2,
          averageRatingChange: 0.7,
          totalOptimizations: 12,
          lastOptimizationDate: '2023-06-30T00:00:00.000Z',
          topFactors: [
            {
              name: 'Similarité des intérêts',
              weight: 0.35,
              impact: 0.25,
            },
            {
              name: 'Expérience précédente',
              weight: 0.25,
              impact: 0.18,
            },
            {
              name: 'Évaluations des utilisateurs',
              weight: 0.20,
              impact: 0.15,
            },
            {
              name: 'Disponibilité',
              weight: 0.15,
              impact: 0.10,
            },
            {
              name: 'Localisation',
              weight: 0.05,
              impact: 0.05,
            },
          ],
          performanceHistory: [
            {
              date: '2023-01-01',
              interactionRate: 0.32,
              conversionRate: 0.12,
              averageRating: 3.2,
            },
            {
              date: '2023-02-01',
              interactionRate: 0.35,
              conversionRate: 0.14,
              averageRating: 3.3,
            },
            {
              date: '2023-03-01',
              interactionRate: 0.38,
              conversionRate: 0.15,
              averageRating: 3.4,
            },
            {
              date: '2023-04-01',
              interactionRate: 0.42,
              conversionRate: 0.16,
              averageRating: 3.5,
            },
            {
              date: '2023-05-01',
              interactionRate: 0.45,
              conversionRate: 0.18,
              averageRating: 3.7,
            },
            {
              date: '2023-06-01',
              interactionRate: 0.48,
              conversionRate: 0.20,
              averageRating: 3.9,
            },
            {
              date: '2023-07-01',
              interactionRate: 0.55,
              conversionRate: 0.23,
              averageRating: 4.2,
            },
          ],
          recentOptimizations: [
            {
              date: '2023-06-30',
              description: 'Optimisation des poids des facteurs',
              improvement: 0.15,
            },
            {
              date: '2023-05-15',
              description: 'Mise à jour des templates d\'explication',
              improvement: 0.08,
            },
            {
              date: '2023-04-01',
              description: 'Déploiement de la variante "Détaillée"',
              improvement: 0.22,
            },
          ],
        };

        setMetrics(mockMetrics);
      } catch (err: any) {
        console.error('Erreur lors du chargement des métriques:', err);
        setError(err.message || 'Erreur lors du chargement des métriques');
      } finally {
        setLoading(false);
      }
    };

    if (user && user.role === 'ADMIN') {
      loadMetrics();
    }
  }, [user, timeRange]);

  // Formater une date
  const formatDate = (dateString: string | null): string => {
    if (!dateString) return 'Jamais';

    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  // Formater un pourcentage
  const formatPercent = (value: number): string => {
    return `${(value * 100).toFixed(1)}%`;
  };

  // Obtenir la classe de couleur pour un changement
  const getChangeColorClass = (change: number): string => {
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-500';
  };

  // Afficher un indicateur de chargement
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Helmet>
          <title>Tableau de Bord des Explications | Retreat And Be</title>
          <meta
            name="description"
            content="Tableau de bord des explications pour les administrateurs de Retreat And Be."
          />
        </Helmet>

        <AdminNavBar />

        <main className="pt-24 pb-12">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-retreat-green"></div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>Tableau de Bord des Explications | Retreat And Be</title>
        <meta
          name="description"
          content="Tableau de bord des explications pour les administrateurs de Retreat And Be."
        />
      </Helmet>

      <AdminNavBar />

      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="mb-6 flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Tableau de Bord des Explications</h1>
              <p className="text-gray-600">Suivi des performances et des optimisations des explications</p>
            </div>

            <div className="flex space-x-2">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value as any)}
                className="rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
              >
                <option value="week">7 derniers jours</option>
                <option value="month">30 derniers jours</option>
                <option value="quarter">3 derniers mois</option>
                <option value="year">12 derniers mois</option>
              </select>

              <button
                onClick={() => navigate('/admin/explanation-learning')}
                className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
              >
                Gérer l'apprentissage
              </button>
            </div>
          </div>

          {/* Afficher un message d'erreur */}
          {error && (
            <div className="mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <p>{error}</p>
              </div>
            </div>
          )}

          {metrics && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Métriques principales */}
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Métriques principales</h2>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500">Taux d'interaction</span>
                        <div className="flex items-center">
                          <span className="text-xl font-semibold">{formatPercent(metrics.interactionRate)}</span>
                          <span className={`ml-2 text-sm ${getChangeColorClass(metrics.interactionRateChange)}`}>
                            {metrics.interactionRateChange > 0 ? '+' : ''}{formatPercent(metrics.interactionRateChange)}
                          </span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div className="bg-retreat-green h-2 rounded-full" style={{ width: `${metrics.interactionRate * 100}%` }}></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500">Taux de conversion</span>
                        <div className="flex items-center">
                          <span className="text-xl font-semibold">{formatPercent(metrics.conversionRate)}</span>
                          <span className={`ml-2 text-sm ${getChangeColorClass(metrics.conversionRateChange)}`}>
                            {metrics.conversionRateChange > 0 ? '+' : ''}{formatPercent(metrics.conversionRateChange)}
                          </span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div className="bg-retreat-green h-2 rounded-full" style={{ width: `${metrics.conversionRate * 100}%` }}></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500">Évaluation moyenne</span>
                        <div className="flex items-center">
                          <span className="text-xl font-semibold">{metrics.averageRating.toFixed(1)}</span>
                          <span className={`ml-2 text-sm ${getChangeColorClass(metrics.averageRatingChange)}`}>
                            {metrics.averageRatingChange > 0 ? '+' : ''}{metrics.averageRatingChange.toFixed(1)}
                          </span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div className="bg-retreat-green h-2 rounded-full" style={{ width: `${(metrics.averageRating / 5) * 100}%` }}></div>
                      </div>
                    </div>

                    <div className="pt-4 border-t border-gray-200">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500">Optimisations totales</span>
                        <span className="text-xl font-semibold">{metrics.totalOptimizations}</span>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500">Dernière optimisation</span>
                        <span className="text-gray-700">{formatDate(metrics.lastOptimizationDate)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Facteurs principaux */}
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Facteurs principaux</h2>
                  <div className="space-y-4">
                    {metrics.topFactors.map((factor, index) => (
                      <div key={index}>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-700">{factor.name}</span>
                          <div className="flex items-center">
                            <span className="text-sm text-gray-500 mr-2">Poids: {formatPercent(factor.weight)}</span>
                            <span className="text-sm text-gray-500">Impact: {formatPercent(factor.impact)}</span>
                          </div>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                          <div className="bg-retreat-green h-2 rounded-full" style={{ width: `${factor.weight * 100}%` }}></div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6">
                    <button
                      onClick={() => navigate('/admin/explanation-learning')}
                      className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                    >
                      Gérer les facteurs
                    </button>
                  </div>
                </div>
              </div>

              {/* Optimisations récentes */}
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Optimisations récentes</h2>
                  <div className="space-y-4">
                    {metrics.recentOptimizations.map((optimization, index) => (
                      <div key={index} className="flex items-start p-3 bg-gray-50 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-retreat-green mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                        </svg>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <div className="text-sm font-medium text-gray-900">{optimization.description}</div>
                              <div className="text-xs text-gray-500">{formatDate(optimization.date)}</div>
                            </div>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              +{formatPercent(optimization.improvement)}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6">
                    <button
                      onClick={() => navigate('/admin/ab-testing')}
                      className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                    >
                      Voir tous les tests A/B
                    </button>
                  </div>
                </div>
              </div>

              {/* Graphique d'évolution */}
              <div className="md:col-span-3 bg-white rounded-lg shadow-md overflow-hidden">
                <div className="p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold text-gray-900">Évolution des performances</h2>
                    <div className="flex space-x-2">
                      <button
                        className={`px-2 py-1 text-xs rounded-md ${timeRange === 'week' ? 'bg-retreat-green text-white' : 'bg-gray-100 text-gray-700'}`}
                        onClick={() => setTimeRange('week')}
                      >
                        7 jours
                      </button>
                      <button
                        className={`px-2 py-1 text-xs rounded-md ${timeRange === 'month' ? 'bg-retreat-green text-white' : 'bg-gray-100 text-gray-700'}`}
                        onClick={() => setTimeRange('month')}
                      >
                        30 jours
                      </button>
                      <button
                        className={`px-2 py-1 text-xs rounded-md ${timeRange === 'quarter' ? 'bg-retreat-green text-white' : 'bg-gray-100 text-gray-700'}`}
                        onClick={() => setTimeRange('quarter')}
                      >
                        3 mois
                      </button>
                      <button
                        className={`px-2 py-1 text-xs rounded-md ${timeRange === 'year' ? 'bg-retreat-green text-white' : 'bg-gray-100 text-gray-700'}`}
                        onClick={() => setTimeRange('year')}
                      >
                        12 mois
                      </button>
                    </div>
                  </div>

                  {metrics && metrics.performanceHistory && metrics.performanceHistory.length > 0 ? (
                    <div className="h-80 relative">
                      {/* Simulation d'un graphique avec des données réelles */}
                      <div className="absolute inset-0">
                        <div className="h-full w-full flex items-end">
                          {metrics.performanceHistory.map((point, index) => (
                            <div key={index} className="h-full flex-1 flex flex-col justify-end items-center group relative">
                              <div
                                className="w-full bg-retreat-green bg-opacity-20 hover:bg-opacity-30 transition-colors"
                                style={{ height: `${point.interactionRate * 100 * 2}%` }}
                              >
                                <div
                                  className="w-full bg-retreat-green"
                                  style={{ height: `${point.conversionRate * 100 * 3}%` }}
                                ></div>
                              </div>

                              {/* Tooltip au survol */}
                              <div className="absolute bottom-full mb-2 bg-gray-800 text-white text-xs rounded p-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                                <div>Date: {new Date(point.date).toLocaleDateString()}</div>
                                <div>Interaction: {formatPercent(point.interactionRate)}</div>
                                <div>Conversion: {formatPercent(point.conversionRate)}</div>
                                <div>Évaluation: {point.averageRating.toFixed(1)}/5</div>
                              </div>

                              <div className="text-xs text-gray-500 mt-1 transform -rotate-45 origin-top-left">
                                {new Date(point.date).toLocaleDateString('fr-FR', { month: 'short' })}
                              </div>
                            </div>
                          ))}
                        </div>

                        {/* Légende */}
                        <div className="absolute top-0 right-0 bg-white bg-opacity-80 p-2 rounded-md">
                          <div className="flex items-center text-xs mb-1">
                            <div className="w-3 h-3 bg-retreat-green bg-opacity-20 mr-1"></div>
                            <span>Taux d'interaction</span>
                          </div>
                          <div className="flex items-center text-xs">
                            <div className="w-3 h-3 bg-retreat-green mr-1"></div>
                            <span>Taux de conversion</span>
                          </div>
                        </div>

                        {/* Axe Y */}
                        <div className="absolute left-0 top-0 bottom-0 flex flex-col justify-between text-xs text-gray-500 py-2">
                          <span>100%</span>
                          <span>75%</span>
                          <span>50%</span>
                          <span>25%</span>
                          <span>0%</span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="h-80 flex items-center justify-center">
                      <div className="text-center text-gray-500">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        <p className="mt-2">Aucune donnée disponible</p>
                        <p className="text-sm">Les données de performance s'afficheront ici</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Carte de chaleur des facteurs */}
              <div className="md:col-span-3 bg-white rounded-lg shadow-md overflow-hidden mt-6">
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Impact des facteurs par segment utilisateur</h2>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Facteur
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Nouveaux utilisateurs
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Utilisateurs occasionnels
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Utilisateurs réguliers
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Impact global
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {metrics?.topFactors.map((factor, index) => {
                          // Simuler des valeurs d'impact différentes par segment
                          const newUserImpact = factor.impact * (0.8 + Math.random() * 0.4);
                          const occasionalUserImpact = factor.impact * (0.8 + Math.random() * 0.4);
                          const regularUserImpact = factor.impact * (0.8 + Math.random() * 0.4);

                          // Fonction pour obtenir la couleur de fond en fonction de l'impact
                          const getBackgroundColor = (impact: number) => {
                            const intensity = Math.min(255, Math.round(255 - (impact * 255 * 2)));
                            return `rgb(${intensity}, 255, ${intensity})`;
                          };

                          return (
                            <tr key={index}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {factor.name}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div className="flex items-center">
                                  <div
                                    className="w-16 h-6 rounded-sm mr-2 flex items-center justify-center text-xs"
                                    style={{ backgroundColor: getBackgroundColor(newUserImpact) }}
                                  >
                                    {formatPercent(newUserImpact)}
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div className="flex items-center">
                                  <div
                                    className="w-16 h-6 rounded-sm mr-2 flex items-center justify-center text-xs"
                                    style={{ backgroundColor: getBackgroundColor(occasionalUserImpact) }}
                                  >
                                    {formatPercent(occasionalUserImpact)}
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div className="flex items-center">
                                  <div
                                    className="w-16 h-6 rounded-sm mr-2 flex items-center justify-center text-xs"
                                    style={{ backgroundColor: getBackgroundColor(regularUserImpact) }}
                                  >
                                    {formatPercent(regularUserImpact)}
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div className="flex items-center">
                                  <div
                                    className="w-16 h-6 rounded-sm mr-2 flex items-center justify-center text-xs font-medium"
                                    style={{ backgroundColor: getBackgroundColor(factor.impact) }}
                                  >
                                    {formatPercent(factor.impact)}
                                  </div>
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default ExplanationDashboardPage;
