import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useAuthContext } from '../hooks/useAuthContext';
import Navbar from '../components/organisms/Navbar/Navbar';
import Footer from '../components/organisms/Footer/Footer';
import ContentManagement from '../components/content/ContentManagement';

const ContentManagementPage: React.FC = () => {
  const { user } = useAuthContext();
  const [activeTab, setActiveTab] = useState('published');

  return (
    <>
      <Helmet>
        <title>Gestion de contenu - Retreat And Be</title>
        <meta
          name="description"
          content="Gérez vos publications, vidéos et autres contenus sur la plateforme Retreat And Be."
        />
      </Helmet>

      <Navbar />

      <main className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Gestion de contenu</h1>
            <p className="mt-2 text-gray-600">
              Gérez vos publications, vidéos et autres contenus
            </p>
          </div>
          <div className="flex space-x-3">
            <a
              href="/content-search"
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              Rechercher
            </a>
            <a
              href="/content-analytics"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Voir les analyses
            </a>
          </div>
        </div>

        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('published')}
              className={`${
                activeTab === 'published'
                  ? 'border-retreat-green text-retreat-green'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Publiés
            </button>
            <button
              onClick={() => setActiveTab('archived')}
              className={`${
                activeTab === 'archived'
                  ? 'border-retreat-green text-retreat-green'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Archivés
            </button>
            <button
              onClick={() => setActiveTab('deleted')}
              className={`${
                activeTab === 'deleted'
                  ? 'border-retreat-green text-retreat-green'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Corbeille
            </button>
          </nav>
        </div>

        <div className="mt-6">
          <ContentManagement activeTab={activeTab} userId={user?.id} />
        </div>
      </main>

      <Footer />
    </>
  );
};

export default ContentManagementPage;
