import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useAuthContext } from '../hooks/useAuthContext';
import Navbar from '../components/organisms/Navbar/Navbar';
import Footer from '../components/organisms/Footer/Footer';
import PartnerRegistrationForm from '../components/forms/PartnerRegistrationForm';

const PartnerRegistrationPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();

  const handleRegistrationSuccess = (partnerId: string) => {
    navigate(`/partner-status/${partnerId}`);
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>Devenir Partenaire | Retreat And Be</title>
        <meta
          name="description"
          content="Rejoignez notre réseau de partenaires professionnels et développez votre activité avec Retreat And Be."
        />
      </Helmet>

      <Navbar />

      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-12 text-center">
            <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              Devenez Partenaire Retreat And Be
            </h1>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              Rejoignez notre réseau de professionnels du bien-être et développez votre activité en
              proposant vos services à notre communauté de clients passionnés.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="md:col-span-2">
              <PartnerRegistrationForm onSuccess={handleRegistrationSuccess} />
            </div>

            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold mb-4">Pourquoi nous rejoindre ?</h2>
                <ul className="space-y-4">
                  <li className="flex">
                    <svg
                      className="h-6 w-6 text-retreat-green flex-shrink-0"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span className="ml-3 text-gray-700">
                      Accédez à une clientèle qualifiée et passionnée de bien-être
                    </span>
                  </li>
                  <li className="flex">
                    <svg
                      className="h-6 w-6 text-retreat-green flex-shrink-0"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span className="ml-3 text-gray-700">
                      Bénéficiez de notre système de matching intelligent pour trouver les clients
                      idéaux
                    </span>
                  </li>
                  <li className="flex">
                    <svg
                      className="h-6 w-6 text-retreat-green flex-shrink-0"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span className="ml-3 text-gray-700">
                      Développez votre visibilité grâce à notre plateforme en pleine croissance
                    </span>
                  </li>
                  <li className="flex">
                    <svg
                      className="h-6 w-6 text-retreat-green flex-shrink-0"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span className="ml-3 text-gray-700">
                      Gérez facilement vos réservations et paiements via notre plateforme sécurisée
                    </span>
                  </li>
                </ul>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold mb-4">Processus de vérification</h2>
                <p className="text-gray-700 mb-4">
                  Pour garantir la qualité de notre réseau, tous les partenaires passent par un
                  processus de vérification :
                </p>
                <ol className="list-decimal list-inside space-y-2 text-gray-700">
                  <li>Soumission de votre candidature</li>
                  <li>Vérification de vos documents et qualifications</li>
                  <li>Entretien avec notre équipe</li>
                  <li>Validation et activation de votre compte</li>
                </ol>
              </div>

              <div className="bg-retreat-green rounded-lg shadow-md p-6 text-white">
                <h2 className="text-xl font-semibold mb-4">Besoin d'aide ?</h2>
                <p className="mb-4">
                  Notre équipe est disponible pour répondre à toutes vos questions concernant le
                  processus de partenariat.
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="inline-block px-4 py-2 border border-white rounded-md text-white hover:bg-white hover:text-retreat-green transition-colors"
                >
                  Contactez-nous
                </a>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default PartnerRegistrationPage;
