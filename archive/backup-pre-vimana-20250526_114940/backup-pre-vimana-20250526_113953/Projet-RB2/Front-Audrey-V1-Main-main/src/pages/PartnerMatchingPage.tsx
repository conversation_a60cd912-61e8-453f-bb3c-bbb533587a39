import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-toastify';
import { matchingService, MatchingCriteria, MatchingResponse } from '../services/api/matchingService';
import { retreatService } from '../services/api/retreatService';
import PartnerSearchForm from '../components/matching/PartnerSearchForm';
import PartnerMatchResults from '../components/matching/PartnerMatchResults';
import Navbar from '../components/organisms/Navbar/Navbar';
import Footer from '../components/organisms/Footer/Footer';

const PartnerMatchingPage: React.FC = () => {
  const { retreatId } = useParams<{ retreatId?: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  
  const [retreat, setRetreat] = useState<any>(null);
  const [isLoadingRetreat, setIsLoadingRetreat] = useState<boolean>(false);
  const [matchingResults, setMatchingResults] = useState<MatchingResponse | null>(null);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [hasSearched, setHasSearched] = useState<boolean>(false);
  const [initialCriteria, setInitialCriteria] = useState<Partial<MatchingCriteria> | undefined>(
    undefined
  );

  // Charger les détails de la retraite si un ID est fourni
  useEffect(() => {
    const loadRetreat = async () => {
      if (!retreatId) return;

      try {
        setIsLoadingRetreat(true);
        const retreatData = await retreatService.getRetreatById(retreatId);
        setRetreat(retreatData);

        // Préremplir les critères de recherche en fonction de la retraite
        const categories = retreatData.categories?.map((cat: any) => cat.name) || [];
        
        setInitialCriteria({
          retreatId,
          specializations: categories,
          dateRange: {
            start: new Date(retreatData.startDate).toISOString().split('T')[0],
            end: new Date(retreatData.endDate).toISOString().split('T')[0],
          },
          location: {
            country: retreatData.location.split(',').pop()?.trim() || '',
          },
          minCapacity: retreatData.capacity,
        });
      } catch (error) {
        console.error('Erreur lors du chargement de la retraite:', error);
        toast.error('Impossible de charger les détails de la retraite');
      } finally {
        setIsLoadingRetreat(false);
      }
    };

    loadRetreat();
  }, [retreatId]);

  // Effectuer une recherche automatique si des critères sont fournis dans l'URL
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const autoSearch = searchParams.get('autoSearch') === 'true';
    
    if (autoSearch && initialCriteria && !hasSearched) {
      handleSearch(initialCriteria as MatchingCriteria);
    }
  }, [initialCriteria, location.search, hasSearched]);

  const handleSearch = async (criteria: MatchingCriteria) => {
    try {
      setIsSearching(true);
      setHasSearched(true);
      
      const results = await matchingService.findPartners(criteria);
      setMatchingResults(results);
      
      // Mettre à jour l'URL pour permettre le partage de la recherche
      const searchParams = new URLSearchParams(location.search);
      searchParams.set('autoSearch', 'true');
      navigate(`${location.pathname}?${searchParams.toString()}`, { replace: true });
    } catch (error) {
      console.error('Erreur lors de la recherche de partenaires:', error);
      toast.error('Une erreur est survenue lors de la recherche de partenaires');
      setMatchingResults(null);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSelectPartner = (partnerId: string) => {
    // Cette fonction peut être utilisée pour des actions supplémentaires
    // lorsqu'un partenaire est sélectionné, comme l'affichage de détails
    // supplémentaires ou la navigation vers une page de détails
    console.log('Partenaire sélectionné:', partnerId);
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>
          {retreat
            ? `Trouver des partenaires pour ${retreat.title} | Retreat And Be`
            : 'Recherche de partenaires professionnels | Retreat And Be'}
        </title>
        <meta
          name="description"
          content="Trouvez les meilleurs partenaires professionnels pour votre retraite de bien-être avec notre système de matching intelligent."
        />
      </Helmet>

      <Navbar />

      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* En-tête de la page */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              {retreat
                ? `Trouver des partenaires pour "${retreat.title}"`
                : 'Recherche de partenaires professionnels'}
            </h1>
            <p className="mt-2 text-lg text-gray-600">
              {retreat
                ? 'Notre système de matching intelligent vous aide à trouver les meilleurs partenaires professionnels pour votre retraite.'
                : 'Trouvez les meilleurs partenaires professionnels pour votre projet de retraite de bien-être.'}
            </p>
          </div>

          {/* Formulaire de recherche */}
          <div className="mb-8">
            <PartnerSearchForm
              onSearch={handleSearch}
              retreatId={retreatId}
              initialCriteria={initialCriteria}
              isLoading={isSearching}
            />
          </div>

          {/* Résultats de la recherche */}
          {isSearching ? (
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-retreat-green"></div>
              </div>
            </div>
          ) : matchingResults ? (
            <PartnerMatchResults
              results={matchingResults.results}
              total={matchingResults.total}
              executionTimeMs={matchingResults.executionTimeMs}
              onSelectPartner={handleSelectPartner}
            />
          ) : hasSearched ? (
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="text-center py-8">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  Aucun résultat trouvé
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  Essayez de modifier vos critères de recherche pour trouver des partenaires.
                </p>
              </div>
            </div>
          ) : null}
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default PartnerMatchingPage;
