import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { MessagingProvider } from '../contexts/MessagingContext';
import ConversationList from '../components/messaging/ConversationList';
import MessageList from '../components/messaging/MessageList';
import MessageInput from '../components/messaging/MessageInput';
import ConversationHeader from '../components/messaging/ConversationHeader';
import NewConversationModal from '../components/messaging/NewConversationModal';
import { IMessage } from '../services/api/messagingService';

const MessagingPage: React.FC = () => {
  const { conversationId } = useParams<{ conversationId: string }>();
  const [showNewConversationModal, setShowNewConversationModal] = useState(false);
  const [showConversationList, setShowConversationList] = useState(true);
  const [replyToMessage, setReplyToMessage] = useState<IMessage | undefined>(undefined);
  const navigate = useNavigate();

  // Handle responsive layout
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setShowConversationList(true);
      } else if (conversationId) {
        setShowConversationList(false);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [conversationId]);

  useEffect(() => {
    if (window.innerWidth < 768 && conversationId) {
      setShowConversationList(false);
    }
  }, [conversationId]);

  const handleSelectConversation = (id: string) => {
    navigate(`/messaging/${id}`);
  };

  const handleBackToList = () => {
    setShowConversationList(true);
    navigate('/messaging');
  };

  const handleNewConversation = () => {
    setShowNewConversationModal(true);
  };

  const handleConversationCreated = (id: string) => {
    navigate(`/messaging/${id}`);
  };

  return (
    <MessagingProvider>
      <div className='flex flex-col h-screen bg-white'>
        <div className='flex-1 flex overflow-hidden'>
          {/* Conversation List */}
          {showConversationList && (
            <div className='w-full md:w-1/3 lg:w-1/4 flex flex-col border-r border-gray-200'>
              <div className='p-4 border-b border-gray-200 flex justify-between items-center'>
                <h1 className='text-xl font-semibold'>Messages</h1>
                <button
                  onClick={handleNewConversation}
                  className='p-2 text-green-500 hover:text-green-600'
                >
                  <svg
                    className='w-6 h-6'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M12 4v16m8-8H4'
                    />
                  </svg>
                </button>
              </div>

              <ConversationList
                onSelectConversation={handleSelectConversation}
                selectedConversationId={conversationId}
              />
            </div>
          )}

          {/* Conversation */}
          <div className={`${showConversationList ? 'hidden md:flex' : 'flex'} flex-col flex-1`}>
            {conversationId ? (
              <>
                <ConversationHeader onBack={handleBackToList} />

                <MessageList conversationId={conversationId} />

                <MessageInput
                  conversationId={conversationId}
                  replyToMessage={replyToMessage}
                  onCancelReply={() => setReplyToMessage(undefined)}
                />
              </>
            ) : (
              <div className='flex flex-col items-center justify-center h-full text-gray-500'>
                <svg
                  className='w-16 h-16 mb-4'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={1}
                    d='M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z'
                  />
                </svg>
                <p className='text-xl font-semibold mb-2'>Vos messages</p>
                <p className='text-center max-w-md px-4'>
                  Sélectionnez une conversation ou commencez-en une nouvelle pour envoyer des
                  messages.
                </p>
                <button
                  onClick={handleNewConversation}
                  className='mt-4 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600'
                >
                  Nouvelle conversation
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      <NewConversationModal
        isOpen={showNewConversationModal}
        onClose={() => setShowNewConversationModal(false)}
        onConversationCreated={handleConversationCreated}
      />
    </MessagingProvider>
  );
};

export default MessagingPage;
