import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  UserGroupIcon,
  ChartBarIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  StarIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline';
import NavBarClient from '../components/organisms/NavBarClient/NavBarClient';
import Footer from '../components/ui/Footer';
import ScrollToTop from '../components/ui/ScrollToTop';

interface FAQItem {
  question: string;
  answer: string;
}

const faqItems: FAQItem[] = [
  {
    question: 'Quels sont les critères pour devenir hôte ?',
    answer:
      "Pour devenir hôte, vous devez disposer d'un espace adapté aux retraites bien-être, avoir une expérience dans le domaine du bien-être ou être prêt à accueillir des praticiens, et respecter nos standards de qualité et de sécurité.",
  },
  {
    question: 'Comment sont fixés les tarifs ?',
    answer:
      'Les tarifs sont fixés par vous-même en fonction de votre espace, de sa localisation, des équipements disponibles et de la demande. Nous vous fournissons des recommandations basées sur le marché pour vous aider à fixer vos prix.',
  },
  {
    question: 'Quelle est la commission prélevée ?',
    answer:
      'Nous prélevons une commission de 15% sur chaque réservation. Cette commission couvre les frais de plateforme, le support client, et la visibilité internationale de votre espace.',
  },
  {
    question: 'Comment sont gérées les réservations ?',
    answer:
      'Les réservations sont gérées via notre plateforme. Vous recevez des notifications pour chaque demande, pouvez accepter ou refuser les réservations, et gérez votre calendrier en temps réel.',
  },
  {
    question: "Quel type d'assurance est nécessaire ?",
    answer:
      "Une assurance responsabilité civile professionnelle est obligatoire. Nous vous aidons à trouver l'assurance adaptée à votre activité d'hôte de retraite.",
  },
];

const advantages = [
  {
    icon: UserGroupIcon,
    title: 'Clientèle Premium',
    description:
      'Accédez à une clientèle internationale recherchant des expériences bien-être authentiques.',
  },
  {
    icon: ChartBarIcon,
    title: 'Visibilité Maximale',
    description:
      "Bénéficiez d'une visibilité optimale sur notre plateforme et nos réseaux sociaux.",
  },
  {
    icon: GlobeAltIcon,
    title: 'International',
    description: "Développez votre activité à l'international avec notre réseau de partenaires.",
  },
  {
    icon: ShieldCheckIcon,
    title: 'Sécurité',
    description:
      "Profitez d'un système de paiement sécurisé et d'une assurance responsabilité civile.",
  },
  {
    icon: StarIcon,
    title: 'Qualité',
    description:
      "Rejoignez un réseau d'hôtes sélectionnés pour leur excellence et leur professionnalisme.",
  },
  {
    icon: CurrencyDollarIcon,
    title: 'Revenus',
    description: 'Générez des revenus complémentaires en valorisant votre espace.',
  },
];

const BecomeHost: React.FC = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  return (
    <div className='min-h-screen bg-gray-100'>
      <header className='fixed top-0 left-0 right-0 bg-white shadow-sm z-50'>
        <NavBarClient />
      </header>

      <main className='pt-20 pb-12'>
        <div className='max-w-7xl mx-auto px-4'>
          {/* Hero Section */}
          <div className='text-center mb-12'>
            <h1 className='text-4xl font-bold text-gray-900 mb-4'>Devenez Hôte de Retraite</h1>
            <p className='text-xl text-gray-600 max-w-3xl mx-auto'>
              Partagez votre espace et votre expertise en accueillant des retraites bien-être.
              Rejoignez notre communauté d'hôtes passionnés et développez votre activité.
            </p>
          </div>

          {/* Avantages Section */}
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12'>
            {advantages.map((advantage, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className='bg-white rounded-lg shadow-md p-6'
              >
                <advantage.icon className='w-12 h-12 text-retreat-green mb-4' />
                <h3 className='text-lg font-semibold mb-2'>{advantage.title}</h3>
                <p className='text-gray-600'>{advantage.description}</p>
              </motion.div>
            ))}
          </div>

          {/* FAQ Section */}
          <div className='bg-white rounded-2xl shadow-lg p-8 mb-12'>
            <h2 className='text-2xl font-bold text-gray-900 mb-6'>Questions fréquentes</h2>
            <div className='space-y-4'>
              {faqItems.map((item, index) => (
                <div key={index} className='border border-gray-200 rounded-lg overflow-hidden'>
                  <button
                    className='w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50'
                    onClick={() => setOpenIndex(openIndex === index ? null : index)}
                  >
                    <span className='font-medium text-gray-900'>{item.question}</span>
                    <svg
                      className={`w-5 h-5 text-gray-500 transition-transform ${
                        openIndex === index ? 'transform rotate-180' : ''
                      }`}
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M19 9l-7 7-7-7'
                      />
                    </svg>
                  </button>
                  {openIndex === index && (
                    <div className='px-6 py-4 bg-gray-50'>
                      <p className='text-gray-600'>{item.answer}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* CTA Section */}
          <div className='text-center'>
            <h2 className='text-2xl font-bold text-gray-900 mb-4'>
              Prêt à rejoindre notre communauté ?
            </h2>
            <p className='text-gray-600 mb-8 max-w-2xl mx-auto'>
              Rejoignez notre réseau d'hôtes et commencez à accueillir des retraites bien-être dans
              votre espace.
            </p>
            <button
              onClick={() => (window.location.href = '/inscription-hote')}
              className='px-8 py-4 bg-retreat-green text-white rounded-lg hover:bg-retreat-green-dark transition-colors text-lg font-medium'
            >
              Devenir hôte
            </button>
          </div>
        </div>
      </main>

      <footer>
        <Footer />
      </footer>
      <ScrollToTop />
    </div>
  );
};

export default BecomeHost;
