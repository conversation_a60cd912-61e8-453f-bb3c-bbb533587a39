import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuthContext } from '../hooks/useAuthContext';
import { contextualRecommendationService, UserContext } from '../services/api/contextualRecommendationService';
import { toast } from 'react-toastify';
import { t } from '../services/i18n/i18nService';
import NavBar from '../components/organisms/NavBar/NavBar';
import Footer from '../components/organisms/Footer/Footer';
import RetreatCard from '../components/retreats/RetreatCard';
import LoadingSpinner from '../components/common/LoadingSpinner';

/**
 * Page pour afficher les recommandations saisonnières
 */
const SeasonalRecommendationPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  const { season } = useParams<{ season?: string }>();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [userContext, setUserContext] = useState<UserContext | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [seasonFactor, setSeasonFactor] = useState<number>(0.7);
  const [includeNextSeason, setIncludeNextSeason] = useState<boolean>(false);
  
  // Déterminer si on affiche la saison actuelle ou la saison suivante
  const isNextSeason = season === 'next';
  
  // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
  useEffect(() => {
    if (!user) {
      navigate('/login', { state: { from: `/seasonal/${season || 'current'}` } });
    }
  }, [user, navigate, season]);
  
  // Charger le contexte utilisateur et les recommandations saisonnières
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Récupérer le contexte utilisateur
        const context = await contextualRecommendationService.getMyContext();
        setUserContext(context);
        
        // Récupérer les recommandations saisonnières
        let seasonalRecommendations;
        if (isNextSeason) {
          seasonalRecommendations = await contextualRecommendationService.getNextSeasonRecommendations(12);
        } else {
          seasonalRecommendations = await contextualRecommendationService.getSeasonalRecommendations({
            seasonFactor,
            includeNextSeason,
            maxRecommendations: 12,
          });
        }
        
        setRecommendations(seasonalRecommendations);
      } catch (error) {
        console.error('Erreur lors du chargement des recommandations saisonnières:', error);
        setError(t('seasonal.loadError'));
      } finally {
        setLoading(false);
      }
    };
    
    if (user) {
      loadData();
    }
  }, [user, seasonFactor, includeNextSeason, isNextSeason]);
  
  // Gérer le changement de facteur de saison
  const handleSeasonFactorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSeasonFactor(parseFloat(e.target.value));
  };
  
  // Gérer le changement d'inclusion de la saison suivante
  const handleIncludeNextSeasonChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIncludeNextSeason(e.target.checked);
  };
  
  // Gérer le rafraîchissement des recommandations
  const handleRefresh = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Récupérer le contexte utilisateur
      const context = await contextualRecommendationService.getMyContext(true);
      setUserContext(context);
      
      // Récupérer les recommandations saisonnières
      let seasonalRecommendations;
      if (isNextSeason) {
        seasonalRecommendations = await contextualRecommendationService.getNextSeasonRecommendations(12);
      } else {
        seasonalRecommendations = await contextualRecommendationService.getSeasonalRecommendations({
          seasonFactor,
          includeNextSeason,
          maxRecommendations: 12,
        });
      }
      
      setRecommendations(seasonalRecommendations);
      
      toast.success(t('seasonal.refreshSuccess'));
    } catch (error) {
      console.error('Erreur lors du rafraîchissement des recommandations saisonnières:', error);
      setError(t('seasonal.refreshError'));
      toast.error(t('seasonal.refreshError'));
    } finally {
      setLoading(false);
    }
  };
  
  // Générer le titre et la description en fonction de la saison
  const getSeasonalTitle = () => {
    if (!userContext || !userContext.seasonData) {
      return isNextSeason ? t('seasonal.nextSeasonTitle', { season: '' }) : t('seasonal.currentSeasonTitle', { season: '' });
    }
    
    const season = isNextSeason ? userContext.seasonData.nextSeason : userContext.seasonData.currentSeason;
    const seasonName = contextualRecommendationService.getSeasonName(season);
    
    return isNextSeason
      ? t('seasonal.nextSeasonTitle', { season: seasonName })
      : t('seasonal.currentSeasonTitle', { season: seasonName });
  };
  
  const getSeasonalDescription = () => {
    if (!userContext || !userContext.seasonData) {
      return isNextSeason ? t('seasonal.nextSeasonDescription', { season: '' }) : t('seasonal.currentSeasonDescription', { season: '' });
    }
    
    const season = isNextSeason ? userContext.seasonData.nextSeason : userContext.seasonData.currentSeason;
    const seasonName = contextualRecommendationService.getSeasonName(season);
    
    return isNextSeason
      ? t('seasonal.nextSeasonDescription', { season: seasonName })
      : t('seasonal.currentSeasonDescription', { season: seasonName });
  };
  
  // Récupérer la classe CSS de fond en fonction de la saison
  const getSeasonalBackgroundClass = () => {
    if (!userContext || !userContext.seasonData) {
      return 'bg-white';
    }
    
    const season = isNextSeason ? userContext.seasonData.nextSeason : userContext.seasonData.currentSeason;
    return contextualRecommendationService.getSeasonBackgroundClass(season);
  };
  
  // Générer un résumé de la saison
  const getSeasonSummary = () => {
    if (!userContext || !userContext.seasonData) {
      return null;
    }
    
    const season = isNextSeason ? userContext.seasonData.nextSeason : userContext.seasonData.currentSeason;
    const seasonName = contextualRecommendationService.getSeasonName(season);
    const seasonIcon = contextualRecommendationService.getSeasonIcon(season);
    const seasonColorClass = contextualRecommendationService.getSeasonColorClass(season);
    
    return (
      <div className={`rounded-lg shadow-md p-6 mb-6 ${getSeasonalBackgroundClass()}`}>
        <div className="flex items-center mb-4">
          <div className={`p-3 rounded-full ${seasonColorClass} bg-opacity-20 mr-4`}>
            {seasonIcon === 'flower' && (
              <svg className={`h-6 w-6 ${seasonColorClass}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v1.323l3.954 1.582 1.599-.8a1 1 0 01.894 1.79l-1.233.616 1.738 5.42a1 1 0 01-.285 1.05A3.989 3.989 0 0115 15a3.989 3.989 0 01-2.667-1.019 1 1 0 01-.285-1.05l1.715-5.349L11 6.477V16h2a1 1 0 110 2H7a1 1 0 110-2h2V6.477L6.237 7.582l1.715 5.349a1 1 0 01-.285 1.05A3.989 3.989 0 015 15a3.989 3.989 0 01-2.667-1.019 1 1 0 01-.285-1.05l1.738-5.42-1.233-.617a1 1 0 01.894-1.788l1.599.799L9 4.323V4a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
            )}
            {seasonIcon === 'sun' && (
              <svg className={`h-6 w-6 ${seasonColorClass}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
              </svg>
            )}
            {seasonIcon === 'leaf' && (
              <svg className={`h-6 w-6 ${seasonColorClass}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
              </svg>
            )}
            {seasonIcon === 'snowflake' && (
              <svg className={`h-6 w-6 ${seasonColorClass}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M11 17a1 1 0 001.447.894l4-2A1 1 0 0017 15V9.236a1 1 0 00-1.447-.894l-4 2a1 1 0 00-.553.894V17zM15.211 6.276a1 1 0 000-1.788l-4.764-2.382a1 1 0 00-.894 0L4.789 4.488a1 1 0 000 1.788l4.764 2.382a1 1 0 00.894 0l4.764-2.382zM4.447 8.342A1 1 0 003 9.236V15a1 1 0 00.553.894l4 2A1 1 0 009 17v-5.764a1 1 0 00-.553-.894l-4-2z" />
              </svg>
            )}
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{seasonName}</h2>
            {!isNextSeason && userContext.seasonData.daysRemainingInCurrentSeason > 0 && (
              <p className="text-sm text-gray-600">
                {t('seasonal.daysRemaining', { count: userContext.seasonData.daysRemainingInCurrentSeason })}
              </p>
            )}
          </div>
        </div>
        
        <p className="text-gray-700">
          {isNextSeason
            ? t('seasonal.nextSeasonActivities', { season: seasonName })
            : t('seasonal.currentSeasonActivities', { season: seasonName })}
        </p>
      </div>
    );
  };
  
  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>{getSeasonalTitle()} | Retreat And Be</title>
        <meta
          name="description"
          content={getSeasonalDescription()}
        />
      </Helmet>
      
      <NavBar />
      
      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{getSeasonalTitle()}</h1>
            <p className="text-lg text-gray-600">{getSeasonalDescription()}</p>
          </div>
          
          {getSeasonSummary()}
          
          {!isNextSeason && (
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('seasonal.preferences')}</h2>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('seasonal.seasonFactor')} ({Math.round(seasonFactor * 100)}%)
                </label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={seasonFactor}
                  onChange={handleSeasonFactorChange}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>{t('seasonal.lessSeasonal')}</span>
                  <span>{t('seasonal.moreSeasonal')}</span>
                </div>
              </div>
              
              <div className="mb-4">
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={includeNextSeason}
                    onChange={handleIncludeNextSeasonChange}
                    className="rounded border-gray-300 text-retreat-green focus:ring-retreat-green"
                  />
                  <span className="ml-2 text-sm text-gray-700">{t('seasonal.includeNextSeason')}</span>
                </label>
              </div>
              
              <div className="flex justify-end">
                <button
                  onClick={handleRefresh}
                  className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                >
                  {t('seasonal.refresh')}
                </button>
              </div>
            </div>
          )}
          
          {/* Contenu principal */}
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner />
            </div>
          ) : error ? (
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-center">
                <button
                  onClick={handleRefresh}
                  className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                >
                  {t('seasonal.tryAgain')}
                </button>
              </div>
            </div>
          ) : recommendations.length === 0 ? (
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="text-center py-12">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {isNextSeason
                    ? t('seasonal.noNextSeasonRecommendations')
                    : t('seasonal.noCurrentSeasonRecommendations')}
                </h3>
                <p className="text-gray-500 mb-6">
                  {isNextSeason
                    ? t('seasonal.checkBackLater')
                    : t('seasonal.tryDifferentFilters')}
                </p>
                
                <button
                  onClick={handleRefresh}
                  className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                >
                  {t('seasonal.refresh')}
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recommendations.map((recommendation) => (
                <RetreatCard
                  key={recommendation.id}
                  retreat={recommendation}
                  isSeasonal={true}
                  seasonalBadge={userContext && userContext.seasonData
                    ? contextualRecommendationService.getSeasonName(
                        isNextSeason ? userContext.seasonData.nextSeason : userContext.seasonData.currentSeason
                      )
                    : ''}
                />
              ))}
            </div>
          )}
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SeasonalRecommendationPage;
