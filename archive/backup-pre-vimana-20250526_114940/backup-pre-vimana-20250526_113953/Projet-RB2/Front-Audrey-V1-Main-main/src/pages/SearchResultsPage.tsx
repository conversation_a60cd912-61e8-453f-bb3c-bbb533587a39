import React, { useState, useEffect, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import NavBarClient from '../components/organisms/NavBarClient/NavBarClient';
import Footer from '../components/ui/Footer';
import {
  AdjustmentsHorizontalIcon,
  MapPinIcon,
  CalendarDaysIcon,
  UserGroupIcon,
  HeartIcon,
} from '@heroicons/react/24/outline';

interface Retreat {
  id: string;
  title: string;
  location: string;
  price: number;
  duration: string;
  dates: string;
  image: string;
  rating: number;
  reviews: number;
  themes: string[];
  capacity: number;
}

const SearchResultsPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [retreats, setRetreats] = useState<Retreat[]>([]);
  const [loading, setLoading] = useState(true);
  const [favorites, setFavorites] = useState<string[]>([]);

  const selections = useMemo(() => location.state?.selections || {}, [location.state?.selections]);

  // Simuler le chargement des retraites (à remplacer par un appel API réel)
  useEffect(() => {
    const fetchRetreats = async () => {
      // Simulation d'un appel API
      setTimeout(() => {
        setRetreats([
          {
            id: '1',
            title: 'Retraite Yoga & Méditation en Provence',
            location: "Provence-Alpes-Côte d'Azur",
            price: 799,
            duration: '6 jours',
            dates: '15-20 juillet 2024',
            image: '/images/retreat-1.jpg',
            rating: 4.8,
            reviews: 24,
            themes: ['Yoga', 'Méditation', 'Bien-être'],
            capacity: 12,
          },
          // Ajoutez d'autres retraites ici
        ]);
        setLoading(false);
      }, 1000);
    };

    fetchRetreats();
  }, [selections]);

  const toggleFavorite = (retreatId: string) => {
    setFavorites((prev) =>
      prev.includes(retreatId) ? prev.filter((id) => id !== retreatId) : [...prev, retreatId]
    );
  };

  return (
    <div className='min-h-screen bg-gray-50'>
      <NavBarClient />

      <main className='pt-24 pb-16'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          {/* Header avec résumé des sélections */}
          <div className='mb-8'>
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className='bg-white rounded-lg shadow-lg p-6'
            >
              <div className='flex items-center justify-between'>
                <h1 className='text-2xl font-bold text-gray-900'>Retraites trouvées pour vous</h1>
                <button
                  onClick={() => setIsFilterOpen(!isFilterOpen)}
                  className='flex items-center gap-2 px-4 py-2 text-retreat-green hover:bg-retreat-green/5 rounded-lg'
                >
                  <AdjustmentsHorizontalIcon className='w-5 h-5' />
                  Filtres
                </button>
              </div>

              {/* Résumé des critères sélectionnés */}
              <div className='mt-4 flex flex-wrap gap-2'>
                {selections.experience && (
                  <span className='px-3 py-1 bg-retreat-green/10 text-retreat-green rounded-full text-sm'>
                    {selections.experience}
                  </span>
                )}
                {selections.themes?.map((theme: string) => (
                  <span
                    key={theme}
                    className='px-3 py-1 bg-retreat-green/10 text-retreat-green rounded-full text-sm'
                  >
                    {theme}
                  </span>
                ))}
                {/* Ajoutez d'autres critères ici */}
              </div>
            </motion.div>
          </div>

          {/* Contenu principal */}
          <div className='flex gap-8'>
            {/* Filtres (visible sur desktop) */}
            <motion.aside
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className='hidden lg:block w-64 bg-white rounded-lg shadow-lg p-6 h-fit'
            >
              <h2 className='text-lg font-semibold mb-4'>Affiner la recherche</h2>
              {/* Ajoutez vos filtres ici */}
            </motion.aside>

            {/* Liste des retraites */}
            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className='flex-1'>
              {loading ? (
                // Skeleton loader
                <div className='space-y-4'>
                  {[1, 2, 3].map((n) => (
                    <div key={n} className='bg-white rounded-lg shadow-lg h-64 animate-pulse' />
                  ))}
                </div>
              ) : (
                <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-6'>
                  {retreats.map((retreat) => (
                    <motion.div
                      key={retreat.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      whileHover={{ y: -4 }}
                      className='bg-white rounded-lg shadow-lg overflow-hidden'
                    >
                      <div className='flex flex-col lg:flex-row'>
                        {/* Image */}
                        <div className='relative w-full lg:w-72 h-48'>
                          <img
                            src={retreat.image}
                            alt={retreat.title}
                            className='w-full h-full object-cover'
                          />
                          <button
                            onClick={() => toggleFavorite(retreat.id)}
                            className='absolute top-4 right-4 p-2 bg-white rounded-full shadow-lg'
                          >
                            <HeartIcon
                              className={`w-5 h-5 ${
                                favorites.includes(retreat.id)
                                  ? 'text-red-500 fill-current'
                                  : 'text-gray-400'
                              }`}
                            />
                          </button>
                        </div>

                        {/* Contenu */}
                        <div className='flex-1 p-6'>
                          <div className='flex justify-between items-start'>
                            <div>
                              <h3 className='text-xl font-semibold text-gray-900'>
                                {retreat.title}
                              </h3>
                              <div className='mt-2 flex items-center gap-4 text-gray-600'>
                                <div className='flex items-center gap-1'>
                                  <MapPinIcon className='w-4 h-4' />
                                  <span>{retreat.location}</span>
                                </div>
                                <div className='flex items-center gap-1'>
                                  <CalendarDaysIcon className='w-4 h-4' />
                                  <span>{retreat.duration}</span>
                                </div>
                                <div className='flex items-center gap-1'>
                                  <UserGroupIcon className='w-4 h-4' />
                                  <span>{retreat.capacity} pers. max</span>
                                </div>
                              </div>
                            </div>
                            <div className='text-right'>
                              <div className='text-2xl font-bold text-retreat-green'>
                                {retreat.price}€
                              </div>
                              <div className='text-sm text-gray-500'>par personne</div>
                            </div>
                          </div>

                          {/* Thèmes */}
                          <div className='mt-4 flex flex-wrap gap-2'>
                            {retreat.themes.map((theme) => (
                              <span
                                key={theme}
                                className='px-3 py-1 bg-retreat-green/10 text-retreat-green rounded-full text-sm'
                              >
                                {theme}
                              </span>
                            ))}
                          </div>

                          {/* Reviews */}
                          <div className='mt-4 flex items-center justify-between'>
                            <div className='flex items-center gap-2'>
                              <div className='flex items-center'>
                                {[...Array(5)].map((_, i) => (
                                  <span
                                    key={i}
                                    className={`text-${
                                      i < Math.floor(retreat.rating) ? 'yellow' : 'gray'
                                    }-400`}
                                  >
                                    ★
                                  </span>
                                ))}
                              </div>
                              <span className='text-gray-600'>({retreat.reviews} avis)</span>
                            </div>
                            <button
                              onClick={() => navigate(`/retreat/${retreat.id}`)}
                              className='px-4 py-2 bg-retreat-green text-white rounded-lg hover:bg-opacity-90'
                            >
                              Voir les détails
                            </button>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </main>

      <Footer />

      {/* Modal des filtres (visible sur mobile) */}
      {isFilterOpen && (
        <div className='fixed inset-0 z-50 lg:hidden'>
          <div
            className='absolute inset-0 bg-black bg-opacity-50'
            onClick={() => setIsFilterOpen(false)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                setIsFilterOpen(false);
              }
            }}
            role='button'
            tabIndex={0}
            aria-label='Close filter modal'
          />
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            className='absolute right-0 top-0 h-full w-80 bg-white p-6'
          >
            <h2 className='text-lg font-semibold mb-4'>Filtres</h2>
            {/* Ajoutez vos filtres ici */}
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default SearchResultsPage;
