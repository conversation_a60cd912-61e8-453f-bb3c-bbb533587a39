import React from 'react';
import styled from 'styled-components';
import NavBarClient from '../components/organisms/NavBarClient/NavBarClient';
import Footer from '../components/ui/Footer';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
`;

const Title = styled.h1`
  color: #4a148c;
  margin-bottom: 2rem;
`;

const Section = styled.section`
  margin-bottom: 2rem;
`;

const PrivacyPolicy = () => {
  return (
    <>
      <NavBarClient />
      <Container>
        <Title>Politique de Confidentialité</Title>
        <Section>
          <h2>1. Collecte des informations</h2>
          <p>Nous collectons les informations suivantes :</p>
          <ul>
            <li>Nom et prénom</li>
            <li>Adresse e-mail</li>
            <li>Informations de contact</li>
          </ul>
        </Section>
        {/* Ajoutez d'autres sections selon vos besoins */}
      </Container>
      <Footer />
    </>
  );
};

export default PrivacyPolicy;
