import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  UserGroupIcon,
  ChartBarIcon,
  GlobeAltIcon,
  CakeIcon,
  SparklesIcon,
  HeartIcon,
  BeakerIcon,
  MusicalNoteIcon,
} from '@heroicons/react/24/outline';
import NavBarClient from '../components/organisms/NavBarClient/NavBarClient';
import Footer from '../components/ui/Footer';
import ScrollToTop from '../components/ui/ScrollToTop';

interface PartnerCategory {
  id: string;
  title: string;
  icon: any;
  description: string;
  benefits: string[];
  requirements: string[];
}

const partnerCategories: PartnerCategory[] = [
  {
    id: 'catering',
    title: 'Traiteur',
    icon: CakeIcon,
    description: 'Proposez vos services de restauration pour les retraites bien-être.',
    benefits: [
      'Accès à une clientèle premium',
      'Flexibilité des menus',
      'Visibilité sur la plateforme',
      'Gestion simplifiée des commandes',
    ],
    requirements: [
      'Certification HACCP',
      'Expérience en restauration bio/végétarienne',
      "Capacité à s'adapter aux régimes spéciaux",
      'Équipement de transport adapté',
    ],
  },
  {
    id: 'wellness',
    title: 'Praticien Bien-être',
    icon: SparklesIcon,
    description: 'Offrez vos services de massage et thérapies alternatives.',
    benefits: [
      'Clientèle internationale',
      'Tarification flexible',
      'Support marketing',
      'Formation continue',
    ],
    requirements: [
      'Diplôme reconnu',
      'Assurance professionnelle',
      'Expérience minimum 2 ans',
      'Matériel professionnel',
    ],
  },
  {
    id: 'yoga',
    title: 'Professeur de Yoga',
    icon: HeartIcon,
    description: 'Enseignez le yoga dans nos retraites.',
    benefits: [
      'Groupes de niveau adapté',
      'Environnement privilégié',
      'Tarification attractive',
      'Communauté de praticiens',
    ],
    requirements: [
      'Certification Yoga Alliance',
      'Spécialisation (Hatha, Vinyasa, etc.)',
      'Expérience en groupe',
      'Adaptabilité aux différents niveaux',
    ],
  },
  {
    id: 'nutrition',
    title: 'Nutritionniste',
    icon: BeakerIcon,
    description: 'Conseillez en nutrition et bien-être alimentaire.',
    benefits: [
      'Consultations individuelles et groupes',
      'Visibilité professionnelle',
      'Réseau de partenaires',
      'Formation continue',
    ],
    requirements: [
      'Diplôme en nutrition',
      'Expérience en conseil',
      'Connaissance des régimes spéciaux',
      'Approche holistique',
    ],
  },
  {
    id: 'music',
    title: 'Musicothérapeute',
    icon: MusicalNoteIcon,
    description: 'Proposez des séances de musicothérapie.',
    benefits: [
      'Séances individuelles et groupes',
      'Environnement calme',
      'Matériel fourni',
      'Tarification attractive',
    ],
    requirements: [
      'Formation en musicothérapie',
      'Instruments adaptés',
      'Expérience en groupe',
      'Approche thérapeutique',
    ],
  },
];

const BecomePartner: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    category: '',
    experience: '',
    description: '',
    certifications: '',
    availability: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Formulaire soumis:', formData);
  };

  return (
    <div className='min-h-screen bg-gray-100'>
      <header className='fixed top-0 left-0 right-0 bg-white shadow-sm z-50'>
        <NavBarClient />
      </header>

      <main className='pt-20 pb-12'>
        <div className='max-w-7xl mx-auto px-4'>
          {/* Hero Section */}
          <div className='text-center mb-12'>
            <h1 className='text-4xl font-bold text-gray-900 mb-4'>Devenez Partenaire</h1>
            <p className='text-xl text-gray-600 max-w-3xl mx-auto'>
              Rejoignez notre réseau de partenaires et développez votre activité en proposant vos
              services dans nos retraites bien-être.
            </p>
          </div>

          {/* Catégories de partenaires */}
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12'>
            {partnerCategories.map((category) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={`bg-white rounded-lg shadow-md p-6 cursor-pointer transition-all duration-300 ${
                  selectedCategory === category.id ? 'ring-2 ring-retreat-green' : ''
                }`}
                onClick={() => setSelectedCategory(category.id)}
              >
                <category.icon className='w-12 h-12 text-retreat-green mb-4' />
                <h3 className='text-lg font-semibold mb-2'>{category.title}</h3>
                <p className='text-gray-600 mb-4'>{category.description}</p>
                <div className='space-y-2'>
                  <h4 className='font-medium text-gray-900'>Avantages :</h4>
                  <ul className='list-disc list-inside text-gray-600'>
                    {category.benefits.map((benefit, index) => (
                      <li key={index}>{benefit}</li>
                    ))}
                  </ul>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Formulaire d'inscription */}
          <div className='bg-white rounded-2xl shadow-lg p-8 max-w-3xl mx-auto'>
            <h2 className='text-2xl font-bold text-gray-900 mb-6'>
              Inscrivez-vous comme Partenaire
            </h2>
            <form onSubmit={handleSubmit} className='space-y-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>
                    Nom complet
                  </label>
                  <input
                    type='text'
                    required
                    className='w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>Email</label>
                  <input
                    type='email'
                    required
                    className='w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>Téléphone</label>
                  <input
                    type='tel'
                    required
                    className='w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>Catégorie</label>
                  <select
                    required
                    className='w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                    value={formData.category}
                    onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                  >
                    <option value=''>Sélectionnez une catégorie</option>
                    {partnerCategories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.title}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-1'>Expérience</label>
                <textarea
                  required
                  rows={3}
                  className='w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                  value={formData.experience}
                  onChange={(e) => setFormData({ ...formData, experience: e.target.value })}
                  placeholder='Décrivez votre expérience professionnelle...'
                />
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-1'>
                  Description de vos services
                </label>
                <textarea
                  required
                  rows={4}
                  className='w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder='Décrivez vos services et votre approche...'
                />
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-1'>
                  Certifications et formations
                </label>
                <textarea
                  required
                  rows={3}
                  className='w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                  value={formData.certifications}
                  onChange={(e) => setFormData({ ...formData, certifications: e.target.value })}
                  placeholder='Listez vos certifications et formations...'
                />
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-1'>
                  Disponibilités
                </label>
                <textarea
                  required
                  rows={2}
                  className='w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                  value={formData.availability}
                  onChange={(e) => setFormData({ ...formData, availability: e.target.value })}
                  placeholder='Indiquez vos disponibilités...'
                />
              </div>

              <button
                type='submit'
                className='w-full px-6 py-3 bg-retreat-green text-white rounded-lg hover:bg-retreat-green-dark transition-colors'
              >
                Soumettre ma candidature
              </button>
            </form>
          </div>
        </div>
      </main>

      <footer>
        <Footer />
      </footer>
      <ScrollToTop />
    </div>
  );
};

export default BecomePartner;
