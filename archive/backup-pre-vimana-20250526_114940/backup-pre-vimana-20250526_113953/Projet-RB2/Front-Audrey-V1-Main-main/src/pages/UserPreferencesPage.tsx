import React, { useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuthContext } from '../hooks/useAuthContext';
import { PageLayout } from '../layouts/PageLayout';
import { <PERSON><PERSON>, <PERSON>bList, Tab, TabPanel } from '../components/ui/Tabs';
import { UserPreferencesPanel } from '../components/recommendation/UserPreferencesPanel';
import { ExternalContentFeed } from '../components/recommendation/ExternalContentFeed';
import { Alert } from '../components/ui/Alert';

const UserPreferencesPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuthContext();
  const [activeTab, setActiveTab] = useState<number>(0);
  const [preferencesUpdated, setPreferencesUpdated] = useState<boolean>(false);

  if (!user) {
    return (
      <PageLayout>
        <div className="container mx-auto px-4 py-8">
          <Alert
            variant="warning"
            title={t('common.unauthorized')}
            message={t('preferences.unauthorized')}
          />
        </div>
      </PageLayout>
    );
  }

  const handlePreferencesSaved = () => {
    setPreferencesUpdated(true);
    
    // Reset the flag after 5 seconds
    setTimeout(() => {
      setPreferencesUpdated(false);
    }, 5000);
  };

  return (
    <PageLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">{t('preferences.pageTitle')}</h1>
          <p className="text-gray-500 mt-2">{t('preferences.pageDescription')}</p>
        </div>
        
        {preferencesUpdated && (
          <Alert
            variant="success"
            title={t('preferences.updateSuccess.title')}
            message={t('preferences.updateSuccess.message')}
            className="mb-6"
          />
        )}
        
        <Tabs selectedIndex={activeTab} onChange={setActiveTab}>
          <TabList className="mb-6">
            <Tab>{t('preferences.tabs.preferences')}</Tab>
            <Tab>{t('preferences.tabs.recommendations')}</Tab>
          </TabList>
          
          <TabPanel>
            <UserPreferencesPanel onSave={handlePreferencesSaved} />
          </TabPanel>
          
          <TabPanel>
            <div className="mb-6">
              <h2 className="text-xl font-semibold">{t('externalContent.title')}</h2>
              <p className="text-gray-500 mt-2">{t('externalContent.description')}</p>
            </div>
            
            <ExternalContentFeed />
          </TabPanel>
        </Tabs>
      </div>
    </PageLayout>
  );
};

export default UserPreferencesPage;
