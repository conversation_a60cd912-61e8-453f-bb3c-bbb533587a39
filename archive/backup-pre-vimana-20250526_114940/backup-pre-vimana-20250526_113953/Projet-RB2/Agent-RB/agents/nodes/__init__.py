"""
Agent node implementations for the workflow graph.
Each node represents a specific agent in the multi-agent system.
"""

# Agents génériques du système multi-agent
from .coordinator_node import coordinator_node
from .planner_node import planner_node
from .supervisor_node import supervisor_node
from .researcher_node import researcher_node
from .coder_node import coder_node
from .browser_node import browser_node
from .reporter_node import reporter_node

# Agents spécifiques au domaine des retraites de bien-être
from .retreat_planner_node import retreat_planner_node
from .partner_matcher_node import partner_matcher_node
from .client_assistant_node import client_assistant_node

__all__ = [
    # Agents génériques
    "coordinator_node",
    "planner_node",
    "supervisor_node",
    "researcher_node",
    "coder_node",
    "browser_node",
    "reporter_node",

    # Agents spécifiques au domaine
    "retreat_planner_node",
    "partner_matcher_node",
    "client_assistant_node"
]
