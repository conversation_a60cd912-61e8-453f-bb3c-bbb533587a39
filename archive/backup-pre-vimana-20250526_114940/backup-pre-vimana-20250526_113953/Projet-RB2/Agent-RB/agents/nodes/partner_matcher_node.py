"""
Agent de mise en relation de partenaires.
Cet agent est responsable de trouver et de mettre en relation les partenaires professionnels
pour l'organisation de retraites de bien-être.
"""

import logging
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime

from src.graph.types import State
from src.utils.time_utils import get_timestamp
from src.utils.error_utils import handle_error, ErrorType
from src.config import load_config
from src.models.partner import Partner, PartnerType, PartnerStatus, PartnerSpecialty

logger = logging.getLogger(__name__)

async def partner_matcher_node(state: State) -> State:
    """
    Agent de mise en relation de partenaires.
    
    Cet agent est responsable de:
    - Analyser les besoins d'une retraite en termes de partenaires
    - Rechercher des partenaires adaptés dans la base de données
    - Évaluer la compatibilité des partenaires
    - Proposer des partenaires pour la retraite
    
    Args:
        state: L'état actuel du workflow
        
    Returns:
        État mis à jour du workflow
    """
    logger.info("Agent de mise en relation de partenaires en cours de traitement...")
    
    try:
        # Charger la configuration
        config = load_config()
        agent_config = config.get_agent_config("partner_matcher")
        
        # Enregistrer cette étape dans l'historique
        state.history.append({
            "agent": "partner_matcher",
            "action": "match_partners",
            "timestamp": get_timestamp()
        })
        
        # Récupérer les informations de la tâche
        task = state.task
        if not task:
            raise ValueError("Aucune tâche définie pour la mise en relation de partenaires")
        
        # Récupérer la retraite si elle existe
        retreat = state.results.get("retreat")
        if not retreat:
            logger.warning("Aucune retraite trouvée, création d'une recherche de partenaires générique")
        
        # Analyser les besoins en partenaires
        partner_requirements = _analyze_partner_requirements(retreat, task)
        logger.info(f"Besoins en partenaires identifiés: {len(partner_requirements)} types de partenaires requis")
        
        # Simuler la recherche de partenaires (dans un système réel, cela interrogerait une base de données)
        matched_partners = _find_matching_partners(partner_requirements, task)
        logger.info(f"Partenaires trouvés: {len(matched_partners)}")
        
        # Évaluer la compatibilité des partenaires
        evaluated_partners = _evaluate_partner_compatibility(matched_partners, retreat, task)
        
        # Mettre à jour l'état avec les partenaires trouvés
        state.results["partners"] = evaluated_partners
        
        # Si nous avons une retraite, mettre à jour la liste des partenaires
        if retreat:
            retreat["partners"] = [partner["id"] for partner in evaluated_partners]
            state.results["retreat"] = retreat
        
        # Définir l'agent suivant
        state.next = "supervisor"
        
        logger.info(f"Mise en relation de partenaires terminée. Agent suivant: {state.next}")
        
    except Exception as e:
        logger.error(f"Erreur dans l'agent de mise en relation de partenaires: {str(e)}")
        state.error = handle_error(e, context={"agent": "partner_matcher"}, error_type=ErrorType.EXECUTION_ERROR)
        state.next = "supervisor"  # Laisser le superviseur gérer l'erreur
    
    return state

def _analyze_partner_requirements(retreat: Optional[Dict[str, Any]], task: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Analyser les besoins en partenaires pour une retraite.
    
    Args:
        retreat: Dictionnaire représentant la retraite (peut être None)
        task: Informations de la tâche
        
    Returns:
        Liste des besoins en partenaires
    """
    requirements = []
    
    # Extraire les paramètres de la tâche
    params = task.get("parameters", {})
    
    # Si nous avons une retraite, analyser ses besoins
    if retreat:
        retreat_type = retreat.get("retreat_type", "yoga")
        location = retreat.get("location", {})
        activities = retreat.get("activities", [])
        start_date = datetime.fromisoformat(retreat.get("start_date"))
        end_date = datetime.fromisoformat(retreat.get("end_date"))
        
        # Besoin d'un organisateur de retraite
        requirements.append({
            "type": PartnerType.RETREAT_ORGANIZER.value,
            "priority": "high",
            "specialties": [retreat_type],
            "location": location,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        })
        
        # Besoin d'un hébergement
        requirements.append({
            "type": PartnerType.ACCOMMODATION.value,
            "priority": "high",
            "location": location,
            "capacity": retreat.get("capacity", 10),
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        })
        
        # Besoin de restauration
        requirements.append({
            "type": PartnerType.CATERING.value,
            "priority": "high",
            "location": location,
            "capacity": retreat.get("capacity", 10),
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        })
        
        # Besoins d'experts selon les activités
        for activity in activities:
            if activity in ["yoga", "meditation", "fitness"]:
                requirements.append({
                    "type": PartnerType.WELLNESS_EXPERT.value,
                    "priority": "medium",
                    "specialties": [activity],
                    "location": location,
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                })
        
        # Besoin de transport si spécifié
        if params.get("need_transport", False):
            requirements.append({
                "type": PartnerType.TRANSPORT.value,
                "priority": "medium",
                "location": location,
                "capacity": retreat.get("capacity", 10),
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            })
    
    # Si nous n'avons pas de retraite ou si des types de partenaires spécifiques sont demandés
    elif params.get("partner_types"):
        for partner_type in params.get("partner_types", []):
            try:
                partner_type_enum = PartnerType(partner_type)
                requirements.append({
                    "type": partner_type_enum.value,
                    "priority": "medium",
                    "specialties": params.get("specialties", []),
                    "location": params.get("location", {})
                })
            except ValueError:
                logger.warning(f"Type de partenaire non reconnu: {partner_type}")
    
    # Si aucun besoin spécifique n'est identifié, ajouter des besoins génériques
    if not requirements:
        requirements = [
            {
                "type": PartnerType.RETREAT_ORGANIZER.value,
                "priority": "medium",
                "specialties": params.get("specialties", ["yoga", "meditation"]),
                "location": params.get("location", {"country": "France"})
            },
            {
                "type": PartnerType.WELLNESS_EXPERT.value,
                "priority": "medium",
                "specialties": params.get("specialties", ["yoga", "meditation"]),
                "location": params.get("location", {"country": "France"})
            },
            {
                "type": PartnerType.ACCOMMODATION.value,
                "priority": "medium",
                "location": params.get("location", {"country": "France"}),
                "capacity": params.get("capacity", 10)
            }
        ]
    
    return requirements

def _find_matching_partners(requirements: List[Dict[str, Any]], task: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Trouver des partenaires correspondant aux besoins.
    
    Dans un système réel, cette fonction interrogerait une base de données.
    Ici, nous simulons des résultats.
    
    Args:
        requirements: Liste des besoins en partenaires
        task: Informations de la tâche
        
    Returns:
        Liste des partenaires correspondants
    """
    # Simuler une base de données de partenaires
    partners = []
    
    # Pour chaque besoin, créer des partenaires fictifs
    for i, req in enumerate(requirements):
        partner_type = req.get("type")
        specialties = req.get("specialties", [])
        location = req.get("location", {})
        
        # Créer 2-3 partenaires pour chaque besoin
        for j in range(1, 4):
            partner_id = f"partner_{uuid.uuid4().hex[:8]}"
            
            # Déterminer le nom en fonction du type
            if partner_type == PartnerType.RETREAT_ORGANIZER.value:
                name = f"Organisateur de Retraites {i+1}-{j}"
            elif partner_type == PartnerType.WELLNESS_EXPERT.value:
                name = f"Expert en Bien-être {i+1}-{j}"
                if specialties:
                    name = f"Expert en {specialties[0].capitalize()} {i+1}-{j}"
            elif partner_type == PartnerType.ACCOMMODATION.value:
                name = f"Hébergement {i+1}-{j}"
            elif partner_type == PartnerType.CATERING.value:
                name = f"Service de Restauration {i+1}-{j}"
            elif partner_type == PartnerType.TRANSPORT.value:
                name = f"Service de Transport {i+1}-{j}"
            else:
                name = f"Partenaire {i+1}-{j}"
            
            # Créer le partenaire
            partner = Partner(
                id=partner_id,
                name=name,
                email=f"contact@{partner_id}.com",
                partner_type=PartnerType(partner_type),
                status=PartnerStatus.ACTIVE,
                specialties=[PartnerSpecialty(s) for s in specialties if s in [e.value for e in PartnerSpecialty]],
                description=f"Description du partenaire {name}",
                location=location,
                languages=["fr", "en"],
                rating=4.0 + (j * 0.3) % 1.0  # Note entre 4.0 et 4.9
            )
            
            partners.append(partner.to_dict())
    
    return partners

def _evaluate_partner_compatibility(partners: List[Dict[str, Any]], retreat: Optional[Dict[str, Any]], task: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Évaluer la compatibilité des partenaires avec la retraite.
    
    Args:
        partners: Liste des partenaires
        retreat: Dictionnaire représentant la retraite (peut être None)
        task: Informations de la tâche
        
    Returns:
        Liste des partenaires évalués avec score de compatibilité
    """
    evaluated_partners = []
    
    # Extraire les paramètres de la tâche
    params = task.get("parameters", {})
    
    for partner in partners:
        # Calculer un score de compatibilité
        compatibility_score = 0.7  # Score de base
        
        # Si nous avons une retraite, évaluer la compatibilité
        if retreat:
            # Vérifier le type de retraite et les spécialités
            retreat_type = retreat.get("retreat_type")
            if retreat_type in [s for s in partner.get("specialties", [])]:
                compatibility_score += 0.1
            
            # Vérifier la localisation
            retreat_location = retreat.get("location", {})
            partner_location = partner.get("location", {})
            if retreat_location.get("country") == partner_location.get("country"):
                compatibility_score += 0.05
                if retreat_location.get("region") == partner_location.get("region"):
                    compatibility_score += 0.05
            
            # Vérifier les dates
            # (Dans un système réel, on vérifierait la disponibilité)
            
            # Vérifier la capacité pour l'hébergement
            if partner.get("partner_type") == PartnerType.ACCOMMODATION.value:
                if partner.get("capacity", 0) >= retreat.get("capacity", 0):
                    compatibility_score += 0.1
        
        # Ajouter le score de compatibilité
        partner["compatibility_score"] = min(1.0, compatibility_score)
        
        # Ajouter des notes sur la compatibilité
        compatibility_notes = []
        if compatibility_score >= 0.9:
            compatibility_notes.append("Excellente correspondance")
        elif compatibility_score >= 0.8:
            compatibility_notes.append("Très bonne correspondance")
        elif compatibility_score >= 0.7:
            compatibility_notes.append("Bonne correspondance")
        else:
            compatibility_notes.append("Correspondance moyenne")
        
        partner["compatibility_notes"] = compatibility_notes
        
        evaluated_partners.append(partner)
    
    # Trier par score de compatibilité
    evaluated_partners.sort(key=lambda p: p.get("compatibility_score", 0), reverse=True)
    
    return evaluated_partners
