"""
Reporter node implementation.
The reporter is responsible for generating reports and summaries.
"""

from src.graph.types import State
import logging

logger = logging.getLogger(__name__)

async def reporter_node(state: State) -> State:
    """
    Reporter node implementation.
    
    The reporter is responsible for:
    - Generating reports based on results
    - Summarizing findings
    - Creating documentation
    
    Args:
        state: The current workflow state
        
    Returns:
        Updated workflow state
    """
    logger.info("Reporter node processing...")
    
    # Record this step in history
    state.history.append({
        "agent": "reporter",
        "action": "generate_report",
        "timestamp": "timestamp_placeholder"  # In a real implementation, use actual timestamp
    })
    
    # Get research and code results
    research_results = state.results.get("research", {})
    code_results = state.results.get("code", {})
    browser_results = state.results.get("browser", {})
    
    # Simulate report generation
    report = {
        "title": "Task Execution Report",
        "summary": "This report summarizes the findings and results of the task execution.",
        "sections": [
            {
                "title": "Research Findings",
                "content": research_results.get("summary", "No research findings available")
            },
            {
                "title": "Implementation Details",
                "content": code_results.get("summary", "No implementation details available")
            },
            {
                "title": "Web Interactions",
                "content": "Summary of web interactions and data extraction" if browser_results else "No web interactions performed"
            }
        ],
        "conclusion": "The task was completed successfully with the following outcomes...",
        "format": "markdown"
    }
    
    # Update the state with the report
    state.results["report"] = report
    
    # Update the plan
    plan = state.context.get("plan", {})
    steps = plan.get("steps", [])
    
    # Mark the current step as completed
    for step in steps:
        if step["id"] == plan.get("current_step"):
            step["status"] = "completed"
            break
    
    # Set the next agent to be the supervisor
    state.next = "supervisor"
    
    logger.info(f"Reporter completed. Next agent: {state.next}")
    return state
