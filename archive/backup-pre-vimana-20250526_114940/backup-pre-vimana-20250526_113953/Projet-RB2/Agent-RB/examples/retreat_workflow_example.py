"""
Exemple d'utilisation du workflow de planification de retraites.
"""

import asyncio
import logging
import yaml
import json
from pathlib import Path

from src.graph.types import State
from src.graph.retreat_workflow import run_retreat_workflow

# Configurer le logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def main():
    """
    Exécuter un exemple de workflow de planification de retraites.
    """
    print("Démarrage du workflow de planification de retraites...")
    
    # Charger les données de la tâche depuis le fichier YAML
    task_path = Path(__file__).parent / "tasks" / "retreat_planning_task.yaml"
    with open(task_path, 'r') as f:
        task_data = yaml.safe_load(f)
    
    # Créer un état initial avec la tâche spécifique
    initial_state = State(
        task=task_data,
        context={
            "user": {
                "id": "user_123",
                "name": "Organisateur de Retraites"
            },
            "workflow_type": "retreat_planning"
        }
    )
    
    # Exécuter le workflow
    final_state = await run_retreat_workflow(initial_state)
    
    # Afficher les résultats
    print("\nWorkflow terminé !")
    print(f"État final: next={final_state.next}")
    print("\nHistorique:")
    for entry in final_state.history:
        print(f"  - {entry['agent']}: {entry['action']} ({entry['timestamp']})")
    
    # Afficher les détails de la retraite planifiée
    retreat = final_state.results.get("retreat")
    if retreat:
        print("\nDétails de la retraite planifiée:")
        print(f"  Titre: {retreat.get('title')}")
        print(f"  Type: {retreat.get('retreat_type')}")
        print(f"  Dates: {retreat.get('start_date')} au {retreat.get('end_date')}")
        print(f"  Lieu: {retreat.get('location', {}).get('country')}, {retreat.get('location', {}).get('region')}")
        print(f"  Capacité: {retreat.get('capacity')} participants")
        
        print("\n  Activités:")
        for activity in retreat.get('activities', []):
            print(f"    - {activity}")
        
        print("\n  Tarification:")
        pricing = retreat.get('pricing', {})
        print(f"    Prix de base: {pricing.get('base_price')} {pricing.get('currency')}")
        print(f"    Prix early bird: {pricing.get('early_bird_price')} {pricing.get('currency')} (jusqu'au {pricing.get('early_bird_deadline')})")
        
        print("\n  Programme (extrait):")
        for i, item in enumerate(retreat.get('schedule', [])[:5]):
            print(f"    Jour {item.get('day')}, {item.get('start_time')}-{item.get('end_time')}: {item.get('title')}")
        if len(retreat.get('schedule', [])) > 5:
            print(f"    ... et {len(retreat.get('schedule', [])) - 5} autres activités")
    else:
        print("\nAucune retraite n'a été planifiée.")
    
    # Sauvegarder les résultats dans un fichier JSON
    output_path = Path(__file__).parent / "results" / "retreat_planning_result.json"
    output_path.parent.mkdir(exist_ok=True)
    
    with open(output_path, 'w') as f:
        json.dump(final_state.to_dict(), f, indent=2)
    
    print(f"\nRésultats sauvegardés dans {output_path}")

if __name__ == "__main__":
    asyncio.run(main())
