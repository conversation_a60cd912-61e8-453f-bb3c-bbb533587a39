#!/usr/bin/env python3
"""
Exemple d'utilisation du monitoring.
"""
import logging
import sys
import json
import time
import random
from services.communication_enhanced import CommunicationService

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def generate_random_requests(num_requests=10):
    """
    Génère des requêtes aléatoires pour simuler du trafic.
    
    Args:
        num_requests: Nombre de requêtes à générer
    """
    logger.info(f"Génération de {num_requests} requêtes aléatoires...")
    
    services = ['superagent', 'agent_ia']
    superagent_endpoints = ['workflows/start', 'workflows/status/123', 'agents/execute']
    agent_ia_endpoints = ['analyze', 'generate', 'recommendations']
    methods = ['GET', 'POST']
    
    # Simuler des requêtes réussies
    for _ in range(num_requests):
        service = random.choice(services)
        
        if service == 'superagent':
            endpoint = random.choice(superagent_endpoints)
            method = 'POST' if endpoint in ['workflows/start', 'agents/execute'] else 'GET'
            
            try:
                if endpoint == 'workflows/start':
                    CommunicationService.start_workflow({
                        'name': 'test_workflow',
                        'input': {'test': 'data'}
                    })
                elif endpoint == 'workflows/status/123':
                    CommunicationService.get_workflow_status('123')
                elif endpoint == 'agents/execute':
                    CommunicationService.execute_agent({
                        'agent': 'test_agent',
                        'input': {'test': 'data'}
                    })
            except Exception as e:
                logger.error(f"Erreur lors de l'appel à {service}/{endpoint}: {str(e)}")
        
        elif service == 'agent_ia':
            endpoint = random.choice(agent_ia_endpoints)
            
            try:
                if endpoint == 'analyze':
                    CommunicationService.analyze_text("Texte à analyser")
                elif endpoint == 'generate':
                    CommunicationService.generate_response("Prompt pour la génération")
                elif endpoint == 'recommendations':
                    CommunicationService.get_recommendations({
                        'type': 'yoga',
                        'location': 'France'
                    })
            except Exception as e:
                logger.error(f"Erreur lors de l'appel à {service}/{endpoint}: {str(e)}")
        
        # Ajouter un délai aléatoire entre les requêtes
        time.sleep(random.uniform(0.1, 0.5))
    
    logger.info("Génération de requêtes terminée.")

def print_metrics():
    """Affiche les métriques collectées."""
    logger.info("Métriques collectées:")
    
    # Récupérer les métriques
    request_metrics = CommunicationService.get_request_metrics()
    circuit_breaker_metrics = CommunicationService.get_circuit_breaker_metrics()
    
    # Afficher les métriques globales
    logger.info(f"Métriques globales:")
    logger.info(f"  Nombre total de requêtes: {request_metrics['request_count']}")
    logger.info(f"  Nombre de requêtes réussies: {request_metrics['success_count']}")
    logger.info(f"  Nombre de requêtes échouées: {request_metrics['error_count']}")
    
    # Afficher les métriques par service
    for service, metrics in request_metrics.get('services', {}).items():
        logger.info(f"Métriques pour le service {service}:")
        logger.info(f"  Nombre total de requêtes: {metrics['request_count']}")
        logger.info(f"  Nombre de requêtes réussies: {metrics['success_count']}")
        logger.info(f"  Nombre de requêtes échouées: {metrics['error_count']}")
        
        # Afficher les métriques par endpoint
        for endpoint, endpoint_metrics in metrics.get('endpoints', {}).items():
            logger.info(f"  Endpoint {endpoint}:")
            logger.info(f"    Nombre total de requêtes: {endpoint_metrics['request_count']}")
            logger.info(f"    Nombre de requêtes réussies: {endpoint_metrics['success_count']}")
            logger.info(f"    Nombre de requêtes échouées: {endpoint_metrics['error_count']}")
            logger.info(f"    Temps de réponse moyen: {endpoint_metrics['avg_response_time']:.3f}s")
            logger.info(f"    Temps de réponse min: {endpoint_metrics['min_response_time']:.3f}s")
            logger.info(f"    Temps de réponse max: {endpoint_metrics['max_response_time']:.3f}s")
    
    # Afficher les métriques des circuit breakers
    logger.info(f"Métriques des circuit breakers:")
    for service, cb_metrics in circuit_breaker_metrics.items():
        logger.info(f"  Circuit breaker pour {service}:")
        logger.info(f"    État: {cb_metrics['state']}")
        logger.info(f"    Nombre d'échecs: {cb_metrics['failure_count']}")
        logger.info(f"    Seuil d'échecs: {cb_metrics['failure_threshold']}")
        logger.info(f"    Temps de récupération: {cb_metrics['recovery_timeout']}s")

def main():
    """Fonction principale."""
    logger.info("Démarrage de l'exemple de monitoring...")
    
    # Générer des requêtes aléatoires
    generate_random_requests(20)
    
    # Afficher les métriques
    print_metrics()
    
    logger.info("Exemple de monitoring terminé.")

if __name__ == "__main__":
    main()
