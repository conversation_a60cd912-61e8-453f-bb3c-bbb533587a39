#!/usr/bin/env python3
"""
Exemple d'utilisation des alertes.
"""
import logging
import sys
import json
import time
import random
from utils.alerting import Al<PERSON><PERSON>anager, AlertRule
from services.communication_with_tracing import CommunicationService

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_custom_alert_rules():
    """Crée des règles d'alerte personnalisées."""
    logger.info("Création de règles d'alerte personnalisées...")
    
    alert_manager = AlertManager.get_instance()
    
    # Règle pour surveiller le nombre de requêtes
    alert_manager.add_rule(AlertRule(
        name="high_request_count",
        description="Alerte si le nombre de requêtes dépasse un seuil",
        check_function=lambda: _check_request_count(50),  # 50 requêtes
        severity="info",
        cooldown=60  # 1 minute
    ))
    
    # Règle pour surveiller le temps de réponse maximal
    alert_manager.add_rule(AlertRule(
        name="max_response_time",
        description="Alerte si le temps de réponse maximal dépasse un seuil",
        check_function=lambda: _check_max_response_time(5.0),  # 5 secondes
        severity="warning",
        cooldown=120  # 2 minutes
    ))
    
    # Règle pour surveiller le ratio de requêtes réussies
    alert_manager.add_rule(AlertRule(
        name="success_ratio",
        description="Alerte si le ratio de requêtes réussies est trop bas",
        check_function=lambda: _check_success_ratio(0.9),  # 90% de succès
        severity="error",
        cooldown=180  # 3 minutes
    ))
    
    logger.info(f"Créé {len(alert_manager.get_rules())} règles d'alerte personnalisées")

def _check_request_count(threshold: int) -> bool:
    """
    Vérifie si le nombre total de requêtes dépasse un seuil.
    
    Args:
        threshold: Seuil de nombre de requêtes
        
    Returns:
        True si le nombre de requêtes dépasse le seuil, False sinon
    """
    metrics = CommunicationService.get_request_metrics()
    request_count = metrics.get('request_count', 0)
    
    logger.info(f"Nombre de requêtes: {request_count}, seuil: {threshold}")
    
    return request_count > threshold

def _check_max_response_time(threshold: float) -> bool:
    """
    Vérifie si le temps de réponse maximal dépasse un seuil.
    
    Args:
        threshold: Seuil de temps de réponse en secondes
        
    Returns:
        True si le temps de réponse maximal dépasse le seuil, False sinon
    """
    metrics = CommunicationService.get_request_metrics()
    
    max_time = 0.0
    
    for service_name, service_metrics in metrics.get('services', {}).items():
        for endpoint_name, endpoint_metrics in service_metrics.get('endpoints', {}).items():
            max_response_time = endpoint_metrics.get('max_response_time', 0.0)
            max_time = max(max_time, max_response_time)
    
    logger.info(f"Temps de réponse maximal: {max_time:.3f}s, seuil: {threshold:.3f}s")
    
    return max_time > threshold

def _check_success_ratio(threshold: float) -> bool:
    """
    Vérifie si le ratio de requêtes réussies est trop bas.
    
    Args:
        threshold: Seuil de ratio de succès (0.0 - 1.0)
        
    Returns:
        True si le ratio de succès est inférieur au seuil, False sinon
    """
    metrics = CommunicationService.get_request_metrics()
    
    request_count = metrics.get('request_count', 0)
    success_count = metrics.get('success_count', 0)
    
    if request_count == 0:
        return False
    
    success_ratio = success_count / request_count
    
    logger.info(f"Ratio de succès: {success_ratio:.2f}, seuil: {threshold:.2f}")
    
    return success_ratio < threshold

def simulate_requests_with_errors():
    """Simule des requêtes avec des erreurs pour déclencher des alertes."""
    logger.info("Simulation de requêtes avec des erreurs...")
    
    # Simuler des requêtes réussies et échouées
    for i in range(100):
        try:
            # 20% de chance d'échec
            if random.random() < 0.2:
                # Simuler une erreur
                raise Exception(f"Erreur simulée #{i+1}")
            
            # Simuler un temps de réponse variable
            response_time = random.uniform(0.1, 10.0)
            
            # Simuler une requête réussie
            logger.info(f"Requête #{i+1} réussie en {response_time:.3f}s")
            
            # Attendre un peu
            time.sleep(0.1)
        except Exception as e:
            logger.error(f"Requête #{i+1} échouée: {str(e)}")

def check_and_display_alerts():
    """Vérifie et affiche les alertes déclenchées."""
    logger.info("Vérification des alertes...")
    
    alert_manager = AlertManager.get_instance()
    triggered_alerts = alert_manager.check_rules()
    
    if triggered_alerts:
        logger.info(f"{len(triggered_alerts)} alertes déclenchées:")
        for alert in triggered_alerts:
            logger.info(f"  - {alert['name']} ({alert['severity']}): {alert['description']}")
    else:
        logger.info("Aucune alerte déclenchée")

def main():
    """Fonction principale."""
    logger.info("Démarrage de l'exemple d'alertes...")
    
    # Créer des règles d'alerte personnalisées
    create_custom_alert_rules()
    
    # Afficher les règles d'alerte
    alert_manager = AlertManager.get_instance()
    rules = alert_manager.get_rules()
    
    logger.info(f"{len(rules)} règles d'alerte configurées:")
    for rule in rules:
        logger.info(f"  - {rule['name']} ({rule['severity']}): {rule['description']}")
    
    # Simuler des requêtes avec des erreurs
    simulate_requests_with_errors()
    
    # Vérifier et afficher les alertes
    check_and_display_alerts()
    
    logger.info("Exemple d'alertes terminé.")

if __name__ == "__main__":
    main()
