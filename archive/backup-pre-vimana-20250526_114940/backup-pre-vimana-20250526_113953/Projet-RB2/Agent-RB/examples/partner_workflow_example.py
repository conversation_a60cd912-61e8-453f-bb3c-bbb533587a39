"""
Exemple d'utilisation du workflow de mise en relation de partenaires.
"""

import asyncio
import logging
import yaml
import json
from pathlib import Path

from src.graph.types import State
from src.graph.partner_workflow import run_partner_workflow

# Configurer le logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def main():
    """
    Exécuter un exemple de workflow de mise en relation de partenaires.
    """
    print("Démarrage du workflow de mise en relation de partenaires...")
    
    # Charger les données de la tâche depuis le fichier YAML
    task_path = Path(__file__).parent / "tasks" / "partner_matching_task.yaml"
    with open(task_path, 'r') as f:
        task_data = yaml.safe_load(f)
    
    # Créer un état initial avec la tâche spécifique
    initial_state = State(
        task=task_data,
        context={
            "user": {
                "id": "user_456",
                "name": "Organisateur de Retraites"
            },
            "workflow_type": "partner_matching"
        }
    )
    
    # Exécuter le workflow
    final_state = await run_partner_workflow(initial_state)
    
    # Afficher les résultats
    print("\nWorkflow terminé !")
    print(f"État final: next={final_state.next}")
    print("\nHistorique:")
    for entry in final_state.history:
        print(f"  - {entry['agent']}: {entry['action']} ({entry['timestamp']})")
    
    # Afficher les partenaires trouvés
    partners = final_state.results.get("partners", [])
    if partners:
        print(f"\nPartenaires trouvés: {len(partners)}")
        
        # Regrouper les partenaires par type
        partners_by_type = {}
        for partner in partners:
            partner_type = partner.get("partner_type")
            if partner_type not in partners_by_type:
                partners_by_type[partner_type] = []
            partners_by_type[partner_type].append(partner)
        
        # Afficher les partenaires par type
        for partner_type, type_partners in partners_by_type.items():
            print(f"\n  {partner_type.capitalize()} ({len(type_partners)}):")
            for partner in type_partners:
                compatibility = partner.get("compatibility_score", 0) * 100
                print(f"    - {partner.get('name')} (Compatibilité: {compatibility:.0f}%)")
                print(f"      Spécialités: {', '.join(partner.get('specialties', []))}")
                print(f"      Note: {partner.get('rating', 0)}/5")
                if partner.get("compatibility_notes"):
                    print(f"      Notes: {', '.join(partner.get('compatibility_notes'))}")
    else:
        print("\nAucun partenaire n'a été trouvé.")
    
    # Sauvegarder les résultats dans un fichier JSON
    output_path = Path(__file__).parent / "results" / "partner_matching_result.json"
    output_path.parent.mkdir(exist_ok=True)
    
    with open(output_path, 'w') as f:
        json.dump(final_state.to_dict(), f, indent=2)
    
    print(f"\nRésultats sauvegardés dans {output_path}")

if __name__ == "__main__":
    asyncio.run(main())
