"""
Exemple d'utilisation du workflow d'assistance client.
"""

import asyncio
import logging
import yaml
import json
from pathlib import Path

from src.graph.types import State
from src.graph.client_workflow import run_client_workflow

# Configurer le logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def main():
    """
    Exécuter un exemple de workflow d'assistance client.
    """
    print("Démarrage du workflow d'assistance client...")
    
    # Charger les données de la tâche depuis le fichier YAML
    task_path = Path(__file__).parent / "tasks" / "client_assistance_task.yaml"
    with open(task_path, 'r') as f:
        task_data = yaml.safe_load(f)
    
    # Créer un état initial avec la tâche spécifique
    initial_state = State(
        task=task_data,
        context={
            "user": {
                "id": "user_789",
                "name": "Agent de Service Client"
            },
            "workflow_type": "client_assistance"
        }
    )
    
    # Exécuter le workflow
    final_state = await run_client_workflow(initial_state)
    
    # Afficher les résultats
    print("\nWorkflow terminé !")
    print(f"État final: next={final_state.next}")
    print("\nHistorique:")
    for entry in final_state.history:
        print(f"  - {entry['agent']}: {entry['action']} ({entry['timestamp']})")
    
    # Afficher les recommandations
    recommendations = final_state.results.get("recommendations", [])
    if recommendations:
        print(f"\nRetraites recommandées: {len(recommendations)}")
        for i, retreat in enumerate(recommendations):
            match_score = retreat.get("match_score", 0) * 100
            print(f"\n  {i+1}. {retreat.get('title')} (Correspondance: {match_score:.0f}%)")
            print(f"     Type: {retreat.get('retreat_type')}")
            print(f"     Lieu: {retreat.get('location', {}).get('country')}, {retreat.get('location', {}).get('region')}")
            print(f"     Prix: {retreat.get('price')} €")
            print(f"     Activités: {', '.join(retreat.get('activities', []))}")
            print(f"     Raisons de la recommandation:")
            for reason in retreat.get("match_reasons", []):
                print(f"       - {reason}")
    
    # Afficher la réponse à une question si disponible
    answer = final_state.results.get("answer")
    if answer:
        print("\nRéponse à la question:")
        print(f"  Question: {answer.get('question')}")
        print(f"  Réponse: {answer.get('text')}")
        print(f"  Type: {answer.get('type')}")
        if answer.get("links"):
            print("  Liens utiles:")
            for link in answer.get("links"):
                print(f"    - {link.get('text')}: {link.get('url')}")
    
    # Afficher l'assistance à la réservation si disponible
    booking_assistance = final_state.results.get("booking_assistance")
    if booking_assistance:
        print("\nAssistance à la réservation:")
        print("  Étapes de réservation:")
        for step in booking_assistance.get("booking_steps", []):
            status_icon = "✓" if step.get("status") == "completed" else "○"
            print(f"    {status_icon} {step.get('step')}. {step.get('title')}")
        
        print("\n  Options d'hébergement disponibles:")
        for option in booking_assistance.get("available_accommodations", []):
            price_info = f"+{option.get('price')} €" if option.get('price') > 0 else "inclus"
            print(f"    - {option.get('name')} ({price_info}) - {option.get('availability')}")
        
        print(f"\n  Prochaines étapes: {booking_assistance.get('next_steps')}")
    
    # Sauvegarder les résultats dans un fichier JSON
    output_path = Path(__file__).parent / "results" / "client_assistance_result.json"
    output_path.parent.mkdir(exist_ok=True)
    
    with open(output_path, 'w') as f:
        json.dump(final_state.to_dict(), f, indent=2)
    
    print(f"\nRésultats sauvegardés dans {output_path}")

async def run_question_example():
    """
    Exécuter un exemple de workflow pour répondre à une question client.
    """
    print("\n\nDémarrage du workflow de réponse à une question client...")
    
    # Charger les données de la tâche depuis le fichier YAML
    task_path = Path(__file__).parent / "tasks" / "client_question_task.yaml"
    with open(task_path, 'r') as f:
        task_data = yaml.safe_load(f)
    
    # Créer un état initial avec la tâche spécifique
    initial_state = State(
        task=task_data,
        context={
            "user": {
                "id": "user_789",
                "name": "Agent de Service Client"
            },
            "workflow_type": "client_question"
        }
    )
    
    # Exécuter le workflow
    final_state = await run_client_workflow(initial_state)
    
    # Afficher la réponse à la question
    answer = final_state.results.get("answer")
    if answer:
        print("\nRéponse à la question:")
        print(f"  Question: {answer.get('question')}")
        print(f"  Réponse: {answer.get('text')}")
        print(f"  Type: {answer.get('type')}")
        if answer.get("links"):
            print("  Liens utiles:")
            for link in answer.get("links"):
                print(f"    - {link.get('text')}: {link.get('url')}")
    
    # Sauvegarder les résultats dans un fichier JSON
    output_path = Path(__file__).parent / "results" / "client_question_result.json"
    output_path.parent.mkdir(exist_ok=True)
    
    with open(output_path, 'w') as f:
        json.dump(final_state.to_dict(), f, indent=2)
    
    print(f"\nRésultats sauvegardés dans {output_path}")

if __name__ == "__main__":
    # Exécuter les deux exemples
    asyncio.run(main())
    asyncio.run(run_question_example())
