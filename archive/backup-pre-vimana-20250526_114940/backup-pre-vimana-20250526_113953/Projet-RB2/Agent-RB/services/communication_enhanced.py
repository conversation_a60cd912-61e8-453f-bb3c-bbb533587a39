"""
Module de communication avec les autres microservices.
"""
import os
import logging
import requests
import time
from typing import Dict, Any, Optional, Union
from utils.circuit_breaker import CircuitBreaker, CircuitBreakerError
from utils.monitoring import monitor_request, MetricsCollector
from utils.tracing import trace_request, inject_span_context

# Configuration du logging
logger = logging.getLogger(__name__)

# URLs des services
SUPERAGENT_SERVICE_URL = os.environ.get('SUPERAGENT_SERVICE_URL', 'http://localhost:5001')
AGENT_IA_SERVICE_URL = os.environ.get('AGENT_IA_SERVICE_URL', 'http://localhost:5002')

# Timeouts
CONNECT_TIMEOUT = int(os.environ.get('CONNECT_TIMEOUT', '5'))
READ_TIMEOUT = int(os.environ.get('READ_TIMEOUT', '30'))

# Retry configuration
MAX_RETRIES = int(os.environ.get('MAX_RETRIES', '3'))
RETRY_BACKOFF = float(os.environ.get('RETRY_BACKOFF', '0.5'))

class CommunicationService:
    """Service pour communiquer avec les autres microservices."""

    @staticmethod
    @monitor_request(service='superagent')
    @trace_request(name='call_superagent')
    def call_superagent(endpoint: str, method: str = 'GET', data: Optional[Dict[str, Any]] = None,
                       params: Optional[Dict[str, Any]] = None, retry: bool = True) -> Dict[str, Any]:
        """
        Appelle un endpoint du service superagent.

        Args:
            endpoint: Endpoint à appeler (sans le slash initial)
            method: Méthode HTTP (GET, POST, PUT, DELETE)
            data: Données à envoyer dans le corps de la requête
            params: Paramètres de requête
            retry: Si True, réessaie en cas d'échec

        Returns:
            Réponse du service

        Raises:
            requests.exceptions.RequestException: Si la requête échoue
            CircuitBreakerError: Si le circuit breaker est ouvert
        """
        url = f"{SUPERAGENT_SERVICE_URL}/{endpoint}"
        logger.info(f"Calling superagent: {method} {url}")

        # Utiliser un circuit breaker pour éviter les appels répétés à un service défaillant
        circuit_breaker = CircuitBreaker.get_instance('superagent')

        @circuit_breaker
        def make_request():
            response = requests.request(
                method=method,
                url=url,
                json=data,
                params=params,
                headers={"Content-Type": "application/json"},
                timeout=(CONNECT_TIMEOUT, READ_TIMEOUT)
            )
            response.raise_for_status()
            return response.json()

        try:
            return make_request()
        except (requests.exceptions.RequestException, CircuitBreakerError) as e:
            if retry and isinstance(e, requests.exceptions.RequestException):
                # Réessayer avec un backoff exponentiel
                for attempt in range(MAX_RETRIES):
                    try:
                        logger.warning(f"Retrying superagent call (attempt {attempt+1}/{MAX_RETRIES})")
                        time.sleep(RETRY_BACKOFF * (2 ** attempt))  # Backoff exponentiel
                        return make_request()
                    except (requests.exceptions.RequestException, CircuitBreakerError) as retry_e:
                        logger.error(f"Retry {attempt+1} failed: {str(retry_e)}")

            logger.error(f"Error calling superagent: {str(e)}")
            raise

    @staticmethod
    @monitor_request(service='agent_ia')
    @trace_request(name='call_agent_ia')
    def call_agent_ia(endpoint: str, method: str = 'GET', data: Optional[Dict[str, Any]] = None,
                     params: Optional[Dict[str, Any]] = None, retry: bool = True) -> Dict[str, Any]:
        """
        Appelle un endpoint du service Agent IA.

        Args:
            endpoint: Endpoint à appeler (sans le slash initial)
            method: Méthode HTTP (GET, POST, PUT, DELETE)
            data: Données à envoyer dans le corps de la requête
            params: Paramètres de requête
            retry: Si True, réessaie en cas d'échec

        Returns:
            Réponse du service

        Raises:
            requests.exceptions.RequestException: Si la requête échoue
            CircuitBreakerError: Si le circuit breaker est ouvert
        """
        url = f"{AGENT_IA_SERVICE_URL}/{endpoint}"
        logger.info(f"Calling Agent IA: {method} {url}")

        # Utiliser un circuit breaker pour éviter les appels répétés à un service défaillant
        circuit_breaker = CircuitBreaker.get_instance('agent_ia')

        @circuit_breaker
        def make_request():
            response = requests.request(
                method=method,
                url=url,
                json=data,
                params=params,
                headers={"Content-Type": "application/json"},
                timeout=(CONNECT_TIMEOUT, READ_TIMEOUT)
            )
            response.raise_for_status()
            return response.json()

        try:
            return make_request()
        except (requests.exceptions.RequestException, CircuitBreakerError) as e:
            if retry and isinstance(e, requests.exceptions.RequestException):
                # Réessayer avec un backoff exponentiel
                for attempt in range(MAX_RETRIES):
                    try:
                        logger.warning(f"Retrying Agent IA call (attempt {attempt+1}/{MAX_RETRIES})")
                        time.sleep(RETRY_BACKOFF * (2 ** attempt))  # Backoff exponentiel
                        return make_request()
                    except (requests.exceptions.RequestException, CircuitBreakerError) as retry_e:
                        logger.error(f"Retry {attempt+1} failed: {str(retry_e)}")

            logger.error(f"Error calling Agent IA: {str(e)}")
            raise

    # Méthodes spécifiques pour superagent

    @classmethod
    def start_workflow(cls, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Démarre un workflow dans superagent.

        Args:
            workflow_data: Données du workflow

        Returns:
            Réponse du service
        """
        return cls.call_superagent("workflows/start", method="POST", data=workflow_data)

    @classmethod
    def get_workflow_status(cls, workflow_id: str) -> Dict[str, Any]:
        """
        Récupère le statut d'un workflow.

        Args:
            workflow_id: ID du workflow

        Returns:
            Statut du workflow
        """
        return cls.call_superagent(f"workflows/status/{workflow_id}", method="GET")

    @classmethod
    def execute_agent(cls, agent_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Exécute un agent dans superagent.

        Args:
            agent_data: Données de l'agent

        Returns:
            Réponse du service
        """
        return cls.call_superagent("agents/execute", method="POST", data=agent_data)

    # Méthodes spécifiques pour Agent IA

    @classmethod
    def analyze_text(cls, text: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analyse un texte avec Agent IA.

        Args:
            text: Texte à analyser
            options: Options d'analyse

        Returns:
            Résultats de l'analyse
        """
        data = {"text": text}
        if options:
            data["options"] = options
        return cls.call_agent_ia("analyze", method="POST", data=data)

    @classmethod
    def generate_response(cls, prompt: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Génère une réponse avec Agent IA.

        Args:
            prompt: Prompt pour la génération
            options: Options de génération

        Returns:
            Réponse générée
        """
        data = {"prompt": prompt}
        if options:
            data["options"] = options
        return cls.call_agent_ia("generate", method="POST", data=data)

    @classmethod
    def get_recommendations(cls, preferences: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Récupère des recommandations personnalisées avec Agent IA.

        Args:
            preferences: Préférences de l'utilisateur
            options: Options de recommandation

        Returns:
            Recommandations
        """
        data = {"preferences": preferences}
        if options:
            data["options"] = options
        return cls.call_agent_ia("recommendations", method="POST", data=data)

    # Méthodes de vérification de santé

    @classmethod
    def check_superagent_health(cls) -> bool:
        """
        Vérifie si le service superagent est en bonne santé.

        Returns:
            True si le service est en bonne santé, False sinon
        """
        try:
            response = cls.call_superagent("health", method="GET", retry=False)
            return response.get("status") == "healthy"
        except (requests.exceptions.RequestException, CircuitBreakerError):
            return False

    @classmethod
    def check_agent_ia_health(cls) -> bool:
        """
        Vérifie si le service Agent IA est en bonne santé.

        Returns:
            True si le service est en bonne santé, False sinon
        """
        try:
            response = cls.call_agent_ia("health", method="GET", retry=False)
            return response.get("status") == "healthy"
        except (requests.exceptions.RequestException, CircuitBreakerError):
            return False

    # Méthodes de surveillance

    @classmethod
    def get_circuit_breaker_metrics(cls) -> Dict[str, Any]:
        """
        Récupère les métriques des circuit breakers.

        Returns:
            Métriques des circuit breakers
        """
        superagent_cb = CircuitBreaker.get_instance('superagent')
        agent_ia_cb = CircuitBreaker.get_instance('agent_ia')

        return {
            'superagent': superagent_cb.get_metrics(),
            'agent_ia': agent_ia_cb.get_metrics()
        }

    @classmethod
    def get_request_metrics(cls) -> Dict[str, Any]:
        """
        Récupère les métriques des requêtes.

        Returns:
            Métriques des requêtes
        """
        metrics_collector = MetricsCollector.get_instance()
        return metrics_collector.get_metrics()

    @classmethod
    def get_service_metrics(cls, service: str) -> Dict[str, Any]:
        """
        Récupère les métriques pour un service spécifique.

        Args:
            service: Nom du service

        Returns:
            Métriques du service
        """
        metrics_collector = MetricsCollector.get_instance()
        return metrics_collector.get_service_metrics(service)

    @classmethod
    def get_error_counts(cls, service: Optional[str] = None) -> Dict[str, Any]:
        """
        Récupère le nombre d'erreurs.

        Args:
            service: Nom du service (optionnel)

        Returns:
            Nombre d'erreurs
        """
        metrics_collector = MetricsCollector.get_instance()
        return metrics_collector.get_error_counts(service)
