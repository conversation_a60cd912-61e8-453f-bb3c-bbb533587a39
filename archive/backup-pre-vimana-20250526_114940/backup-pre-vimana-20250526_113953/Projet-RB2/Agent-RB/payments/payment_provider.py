"""
Interface pour les fournisseurs de paiement.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List

from .payment_status import PaymentStatus, PaymentMethod, PaymentType

class PaymentProvider(ABC):
    """
    Interface pour les fournisseurs de paiement.
    """

    @abstractmethod
    async def create_payment(self,
                           amount: float,
                           currency: str,
                           description: str,
                           customer_info: Dict[str, Any],
                           payment_type: PaymentType,
                           metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Crée un paiement.

        Args:
            amount: Montant du paiement
            currency: Devise du paiement
            description: Description du paiement
            customer_info: Informations sur le client
            payment_type: Type de paiement
            metadata: Métadonnées supplémentaires

        Returns:
            Informations sur le paiement créé
        """
        pass

    @abstractmethod
    async def get_payment_status(self, payment_id: str) -> PaymentStatus:
        """
        Récupère le statut d'un paiement.

        Args:
            payment_id: ID du paiement

        Returns:
            Statut du paiement
        """
        pass

    @abstractmethod
    async def refund_payment(self,
                           payment_id: str,
                           amount: Optional[float] = None,
                           reason: Optional[str] = None) -> Dict[str, Any]:
        """
        Rembourse un paiement.

        Args:
            payment_id: ID du paiement
            amount: Montant à rembourser (si None, rembourse le montant total)
            reason: Raison du remboursement

        Returns:
            Informations sur le remboursement
        """
        pass

    @abstractmethod
    async def create_payment_intent(self,
                                  amount: float,
                                  currency: str,
                                  description: str,
                                  customer_info: Dict[str, Any],
                                  payment_methods: Optional[List[PaymentMethod]] = None,
                                  metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Crée une intention de paiement.

        Args:
            amount: Montant du paiement
            currency: Devise du paiement
            description: Description du paiement
            customer_info: Informations sur le client
            payment_methods: Méthodes de paiement acceptées
            metadata: Métadonnées supplémentaires

        Returns:
            Informations sur l'intention de paiement
        """
        pass

    @abstractmethod
    async def get_payment_methods(self, customer_id: str) -> List[Dict[str, Any]]:
        """
        Récupère les méthodes de paiement d'un client.

        Args:
            customer_id: ID du client

        Returns:
            Liste des méthodes de paiement
        """
        pass
