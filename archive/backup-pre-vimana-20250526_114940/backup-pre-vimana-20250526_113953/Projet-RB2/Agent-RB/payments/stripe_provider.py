"""
Fournisseur de paiement Stripe.
"""

import os
import logging
from typing import Dict, Any, Optional, List

import stripe

from .payment_provider import PaymentProvider
from .payment_status import PaymentStatus, PaymentMethod, PaymentType

# Configuration du logging
logger = logging.getLogger(__name__)

class StripeProvider(PaymentProvider):
    """
    Fournisseur de paiement Stripe.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialise le fournisseur Stripe.
        
        Args:
            api_key: Clé API Stripe (optionnel, utilise la variable d'environnement STRIPE_API_KEY par défaut)
        """
        self.api_key = api_key or os.environ.get("STRIPE_API_KEY")
        
        # Initialiser Stripe
        stripe.api_key = self.api_key
        
        # En mode développement, ne pas effectuer de paiements réels
        self.dev_mode = os.environ.get("ENVIRONMENT", "development") == "development"
    
    async def create_payment(self, 
                           amount: float, 
                           currency: str, 
                           description: str, 
                           customer_info: Dict[str, Any],
                           payment_type: PaymentType,
                           metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Crée un paiement avec Stripe.
        
        Args:
            amount: Montant du paiement
            currency: Devise du paiement
            description: Description du paiement
            customer_info: Informations sur le client
            payment_type: Type de paiement
            metadata: Métadonnées supplémentaires
            
        Returns:
            Informations sur le paiement créé
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Création d'un paiement Stripe simulé: {amount} {currency}")
            return {
                "id": "py_dev_123456789",
                "amount": amount,
                "currency": currency,
                "description": description,
                "status": PaymentStatus.COMPLETED.value,
                "customer": customer_info.get("email"),
                "payment_type": payment_type.value,
                "payment_method": PaymentMethod.CREDIT_CARD.value,
                "created": 1234567890,
                "metadata": metadata or {}
            }
        
        try:
            # Créer ou récupérer le client Stripe
            customer = self._get_or_create_customer(customer_info)
            
            # Créer le paiement
            payment_intent = stripe.PaymentIntent.create(
                amount=int(amount * 100),  # Stripe utilise les centimes
                currency=currency,
                description=description,
                customer=customer.id,
                metadata=metadata or {},
                payment_method_types=["card"],
                confirm=True,
                return_url="https://retreatandbe.com/payment/success"
            )
            
            return {
                "id": payment_intent.id,
                "amount": amount,
                "currency": currency,
                "description": description,
                "status": self._map_stripe_status(payment_intent.status),
                "customer": customer.id,
                "payment_type": payment_type.value,
                "payment_method": PaymentMethod.CREDIT_CARD.value,
                "created": payment_intent.created,
                "client_secret": payment_intent.client_secret,
                "metadata": payment_intent.metadata
            }
        
        except stripe.error.StripeError as e:
            logger.error(f"Erreur Stripe lors de la création du paiement: {str(e)}")
            raise
    
    async def get_payment_status(self, payment_id: str) -> PaymentStatus:
        """
        Récupère le statut d'un paiement Stripe.
        
        Args:
            payment_id: ID du paiement
            
        Returns:
            Statut du paiement
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Récupération du statut d'un paiement Stripe simulé: {payment_id}")
            return PaymentStatus.COMPLETED
        
        try:
            # Récupérer le paiement
            payment_intent = stripe.PaymentIntent.retrieve(payment_id)
            
            # Mapper le statut Stripe au statut interne
            return self._map_stripe_status(payment_intent.status)
        
        except stripe.error.StripeError as e:
            logger.error(f"Erreur Stripe lors de la récupération du statut du paiement: {str(e)}")
            raise
    
    async def refund_payment(self, 
                           payment_id: str, 
                           amount: Optional[float] = None,
                           reason: Optional[str] = None) -> Dict[str, Any]:
        """
        Rembourse un paiement Stripe.
        
        Args:
            payment_id: ID du paiement
            amount: Montant à rembourser (si None, rembourse le montant total)
            reason: Raison du remboursement
            
        Returns:
            Informations sur le remboursement
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Remboursement d'un paiement Stripe simulé: {payment_id}")
            return {
                "id": "re_dev_123456789",
                "payment_id": payment_id,
                "amount": amount,
                "status": PaymentStatus.REFUNDED.value,
                "created": 1234567890
            }
        
        try:
            # Créer le remboursement
            refund_params = {
                "payment_intent": payment_id,
                "reason": reason or "requested_by_customer"
            }
            
            # Ajouter le montant si spécifié
            if amount is not None:
                refund_params["amount"] = int(amount * 100)  # Stripe utilise les centimes
            
            refund = stripe.Refund.create(**refund_params)
            
            return {
                "id": refund.id,
                "payment_id": payment_id,
                "amount": refund.amount / 100,  # Convertir les centimes en unités
                "status": self._map_stripe_refund_status(refund.status),
                "created": refund.created
            }
        
        except stripe.error.StripeError as e:
            logger.error(f"Erreur Stripe lors du remboursement du paiement: {str(e)}")
            raise
    
    async def create_payment_intent(self, 
                                  amount: float, 
                                  currency: str, 
                                  description: str,
                                  customer_info: Dict[str, Any],
                                  payment_methods: Optional[List[PaymentMethod]] = None,
                                  metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Crée une intention de paiement Stripe.
        
        Args:
            amount: Montant du paiement
            currency: Devise du paiement
            description: Description du paiement
            customer_info: Informations sur le client
            payment_methods: Méthodes de paiement acceptées
            metadata: Métadonnées supplémentaires
            
        Returns:
            Informations sur l'intention de paiement
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Création d'une intention de paiement Stripe simulée: {amount} {currency}")
            return {
                "id": "pi_dev_123456789",
                "client_secret": "pi_dev_123456789_secret_dev123456789",
                "amount": amount,
                "currency": currency,
                "description": description,
                "status": PaymentStatus.PENDING.value,
                "customer": customer_info.get("email"),
                "created": 1234567890,
                "metadata": metadata or {}
            }
        
        try:
            # Créer ou récupérer le client Stripe
            customer = self._get_or_create_customer(customer_info)
            
            # Mapper les méthodes de paiement
            payment_method_types = self._map_payment_methods(payment_methods)
            
            # Créer l'intention de paiement
            payment_intent = stripe.PaymentIntent.create(
                amount=int(amount * 100),  # Stripe utilise les centimes
                currency=currency,
                description=description,
                customer=customer.id,
                metadata=metadata or {},
                payment_method_types=payment_method_types
            )
            
            return {
                "id": payment_intent.id,
                "client_secret": payment_intent.client_secret,
                "amount": amount,
                "currency": currency,
                "description": description,
                "status": self._map_stripe_status(payment_intent.status),
                "customer": customer.id,
                "created": payment_intent.created,
                "metadata": payment_intent.metadata
            }
        
        except stripe.error.StripeError as e:
            logger.error(f"Erreur Stripe lors de la création de l'intention de paiement: {str(e)}")
            raise
    
    async def get_payment_methods(self, customer_id: str) -> List[Dict[str, Any]]:
        """
        Récupère les méthodes de paiement d'un client Stripe.
        
        Args:
            customer_id: ID du client
            
        Returns:
            Liste des méthodes de paiement
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Récupération des méthodes de paiement d'un client Stripe simulé: {customer_id}")
            return [
                {
                    "id": "pm_dev_123456789",
                    "type": PaymentMethod.CREDIT_CARD.value,
                    "card": {
                        "brand": "visa",
                        "last4": "4242",
                        "exp_month": 12,
                        "exp_year": 2025
                    }
                }
            ]
        
        try:
            # Récupérer les méthodes de paiement
            payment_methods = stripe.PaymentMethod.list(
                customer=customer_id,
                type="card"
            )
            
            # Formater les méthodes de paiement
            return [
                {
                    "id": pm.id,
                    "type": self._map_stripe_payment_method(pm.type),
                    "card": {
                        "brand": pm.card.brand,
                        "last4": pm.card.last4,
                        "exp_month": pm.card.exp_month,
                        "exp_year": pm.card.exp_year
                    } if hasattr(pm, "card") else None
                }
                for pm in payment_methods.data
            ]
        
        except stripe.error.StripeError as e:
            logger.error(f"Erreur Stripe lors de la récupération des méthodes de paiement: {str(e)}")
            raise
    
    def _get_or_create_customer(self, customer_info: Dict[str, Any]) -> stripe.Customer:
        """
        Récupère ou crée un client Stripe.
        
        Args:
            customer_info: Informations sur le client
            
        Returns:
            Client Stripe
        """
        # Vérifier si le client existe déjà
        email = customer_info.get("email")
        if not email:
            raise ValueError("L'email du client est requis")
        
        # Rechercher le client par email
        customers = stripe.Customer.list(email=email, limit=1)
        
        if customers.data:
            # Le client existe déjà
            return customers.data[0]
        
        # Créer un nouveau client
        return stripe.Customer.create(
            email=email,
            name=customer_info.get("name"),
            phone=customer_info.get("phone"),
            metadata=customer_info.get("metadata", {})
        )
    
    def _map_stripe_status(self, stripe_status: str) -> PaymentStatus:
        """
        Mappe un statut Stripe à un statut interne.
        
        Args:
            stripe_status: Statut Stripe
            
        Returns:
            Statut interne
        """
        status_map = {
            "requires_payment_method": PaymentStatus.PENDING,
            "requires_confirmation": PaymentStatus.PENDING,
            "requires_action": PaymentStatus.PENDING,
            "processing": PaymentStatus.PROCESSING,
            "requires_capture": PaymentStatus.PROCESSING,
            "succeeded": PaymentStatus.COMPLETED,
            "canceled": PaymentStatus.CANCELLED
        }
        
        return status_map.get(stripe_status, PaymentStatus.FAILED)
    
    def _map_stripe_refund_status(self, stripe_status: str) -> PaymentStatus:
        """
        Mappe un statut de remboursement Stripe à un statut interne.
        
        Args:
            stripe_status: Statut de remboursement Stripe
            
        Returns:
            Statut interne
        """
        status_map = {
            "pending": PaymentStatus.PROCESSING,
            "succeeded": PaymentStatus.REFUNDED,
            "failed": PaymentStatus.FAILED,
            "canceled": PaymentStatus.CANCELLED
        }
        
        return status_map.get(stripe_status, PaymentStatus.FAILED)
    
    def _map_payment_methods(self, payment_methods: Optional[List[PaymentMethod]]) -> List[str]:
        """
        Mappe des méthodes de paiement internes à des méthodes de paiement Stripe.
        
        Args:
            payment_methods: Méthodes de paiement internes
            
        Returns:
            Méthodes de paiement Stripe
        """
        if not payment_methods:
            return ["card"]
        
        method_map = {
            PaymentMethod.CREDIT_CARD: "card",
            PaymentMethod.BANK_TRANSFER: "sepa_debit",
            PaymentMethod.APPLE_PAY: "apple_pay",
            PaymentMethod.GOOGLE_PAY: "google_pay"
        }
        
        return [method_map.get(method, "card") for method in payment_methods if method in method_map]
    
    def _map_stripe_payment_method(self, stripe_method: str) -> PaymentMethod:
        """
        Mappe une méthode de paiement Stripe à une méthode de paiement interne.
        
        Args:
            stripe_method: Méthode de paiement Stripe
            
        Returns:
            Méthode de paiement interne
        """
        method_map = {
            "card": PaymentMethod.CREDIT_CARD,
            "sepa_debit": PaymentMethod.BANK_TRANSFER,
            "apple_pay": PaymentMethod.APPLE_PAY,
            "google_pay": PaymentMethod.GOOGLE_PAY
        }
        
        return method_map.get(stripe_method, PaymentMethod.OTHER)
