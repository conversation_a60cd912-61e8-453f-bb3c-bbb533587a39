# Intégration avec le Système de Recommandation

Ce document décrit l'intégration entre Agent-RB et le système de recommandation.

## Vue d'ensemble

Agent-RB s'intègre avec le système de recommandation pour fournir des recommandations personnalisées aux utilisateurs. Cette intégration permet de :

1. Récupérer des recommandations personnalisées pour un utilisateur
2. Récupérer des recommandations tendance
3. Récupérer des éléments similaires à un élément spécifié
4. Enregistrer les interactions des utilisateurs
5. Mettre à jour les préférences des utilisateurs

## Architecture

L'intégration est basée sur une architecture client-serveur, où Agent-RB agit comme client et le système de recommandation comme serveur. La communication se fait via des requêtes HTTP REST.

```
┌─────────────┐                 ┌─────────────────────┐
│             │                 │                     │
│   Agent-RB  │ ◄─── HTTP ────► │ Recommendation API  │
│             │                 │                     │
└─────────────┘                 └─────────────────────┘
```

## Configuration

La configuration de l'intégration se fait via des variables d'environnement :

```
RECOMMENDATION_SERVICE_URL=http://localhost:3001
RECOMMENDATION_TIMEOUT_MS=30000
RECOMMENDATION_MAX_RETRIES=3
RECOMMENDATION_RETRY_INTERVAL=1000
RECOMMENDATION_ENABLE_CACHE=true
RECOMMENDATION_CACHE_TTL=300
RECOMMENDATION_CACHE_MAX_ITEMS=1000
RECOMMENDATION_DEFAULT_STRATEGY=HYBRID
RECOMMENDATION_DEFAULT_HYBRID_METHOD=WEIGHTED
RECOMMENDATION_DEFAULT_LIMIT=10
RECOMMENDATION_AUTO_TRACK_INTERACTIONS=VIEW,LIKE,ENROLL
```

## Authentification

L'authentification entre Agent-RB et le système de recommandation se fait via des tokens JWT. Deux types de tokens sont utilisés :

1. **Token Utilisateur** : Pour les requêtes au nom d'un utilisateur spécifique
2. **Token Service** : Pour les requêtes génériques (ex: recommandations tendance)

## Endpoints

### Recommandations Personnalisées

```
GET /api/recommendations/personalized
```

Récupère des recommandations personnalisées pour l'utilisateur connecté.

#### Paramètres de requête

| Paramètre | Type   | Description                                     | Défaut   |
|-----------|--------|-------------------------------------------------|----------|
| type      | string | Type d'élément (RETREAT, PARTNER, COURSE)       | RETREAT  |
| limit     | number | Nombre maximum de recommandations               | 10       |
| page      | number | Numéro de page pour la pagination               | 1        |
| filters   | string | Filtres au format JSON                          | {}       |

#### Exemple de réponse

```json
{
  "items": [
    {
      "id": "60d21b4667d0d8992e610c85",
      "type": "RETREAT",
      "title": "Retraite de Yoga dans les Alpes",
      "description": "Une semaine de détente et de reconnexion avec la nature...",
      "score": 0.95,
      "sources": ["content-based", "collaborative"],
      "reasons": ["Basé sur vos préférences", "Similaire à vos activités précédentes"],
      "imageUrl": "https://example.com/images/retreat-123.jpg",
      "url": "https://retreatandbe.com/retreats/retraite-yoga-alpes",
      "metadata": {
        "category": "Yoga",
        "level": "INTERMEDIATE",
        "location": "Chamonix, France",
        "duration": 7,
        "tags": ["yoga", "meditation", "mountains"],
        "price": 1200,
        "currency": "EUR",
        "startDate": "2023-07-15",
        "endDate": "2023-07-22",
        "reviewCount": 42,
        "averageRating": 4.7,
        "available": true,
        "remainingSpots": 5
      }
    }
  ],
  "meta": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "totalPages": 10
  }
}
```

### Recommandations Tendance

```
GET /api/recommendations/trending
```

Récupère les éléments les plus populaires ou tendance.

#### Paramètres de requête

Mêmes paramètres que pour les recommandations personnalisées.

### Éléments Similaires

```
GET /api/recommendations/similar/:type/:itemId
```

Récupère des éléments similaires à un élément spécifié.

#### Paramètres de chemin

| Paramètre | Type   | Description                                     |
|-----------|--------|-------------------------------------------------|
| type      | string | Type d'élément (RETREAT, PARTNER, COURSE)       |
| itemId    | string | ID de l'élément de référence                    |

#### Paramètres de requête

Mêmes paramètres que pour les recommandations personnalisées.

### Enregistrer une Interaction

```
POST /api/recommendations/interactions
```

Enregistre une interaction utilisateur avec un élément.

#### Corps de la requête

```json
{
  "itemId": "60d21b4667d0d8992e610c85",
  "type": "RETREAT",
  "interactionType": "VIEW",
  "metadata": {
    "duration": 300,
    "progress": 0.5
  }
}
```

### Mettre à jour les Préférences

```
POST /api/recommendations/preferences
```

Met à jour les préférences de recommandation de l'utilisateur.

#### Corps de la requête

```json
{
  "recommendationStrategy": "HYBRID",
  "hybridMethod": "WEIGHTED",
  "preferredTypes": ["RETREAT", "COURSE"],
  "preferredCategories": ["Yoga", "Meditation"],
  "excludedCategories": ["Fitness"],
  "preferredLevels": ["BEGINNER", "INTERMEDIATE"],
  "preferredLocations": ["France", "Italy"],
  "preferredTags": ["yoga", "meditation", "wellness"],
  "maxRecommendations": 10,
  "includeMetadata": true
}
```

## Suivi des Interactions

Agent-RB peut suivre automatiquement certaines interactions utilisateur et les envoyer au système de recommandation. Les types d'interactions suivis automatiquement sont configurés via la variable d'environnement `RECOMMENDATION_AUTO_TRACK_INTERACTIONS`.

### Types d'Interactions

- **VIEW** : L'utilisateur a consulté un élément
- **LIKE** : L'utilisateur a aimé un élément
- **ENROLL** : L'utilisateur s'est inscrit à un élément
- **BOOKMARK** : L'utilisateur a ajouté un élément à ses favoris
- **SHARE** : L'utilisateur a partagé un élément
- **COMMENT** : L'utilisateur a commenté un élément
- **RATE** : L'utilisateur a noté un élément

## Gestion des Erreurs

Les erreurs sont gérées de manière cohérente avec des codes HTTP appropriés :

- **400 Bad Request** : Paramètres invalides
- **401 Unauthorized** : Authentification requise
- **403 Forbidden** : Accès refusé
- **404 Not Found** : Ressource non trouvée
- **500 Internal Server Error** : Erreur interne du serveur

## Bonnes Pratiques

1. **Mise en Cache** : Mettre en cache les recommandations pour améliorer les performances
2. **Retry Logic** : Implémenter une logique de retry pour les requêtes échouées
3. **Circuit Breaker** : Utiliser un circuit breaker pour éviter les cascades d'erreurs
4. **Monitoring** : Surveiller les performances et les erreurs
5. **Logging** : Enregistrer les requêtes et les réponses pour le débogage

## Exemples d'Utilisation

### Afficher des Recommandations Personnalisées

```typescript
// Dans un service ou un contrôleur
async function getPersonalizedRecommendations(userId: string) {
  try {
    const recommendations = await recommendationService.getPersonalizedRecommendations(
      userId,
      'RETREAT',
      { limit: 5 }
    );
    
    return recommendations.items;
  } catch (error) {
    console.error('Error getting recommendations:', error);
    return [];
  }
}
```

### Enregistrer une Interaction

```typescript
// Dans un service ou un contrôleur
async function recordViewInteraction(userId: string, itemId: string, type: string) {
  try {
    await recommendationService.recordInteraction(
      userId,
      itemId,
      type,
      'VIEW',
      { duration: 0 }
    );
    
    console.log('Interaction recorded successfully');
  } catch (error) {
    console.error('Error recording interaction:', error);
  }
}
```

## Dépannage

### Le Service de Recommandation est Inaccessible

1. Vérifier que le service de recommandation est en cours d'exécution
2. Vérifier la configuration de l'URL du service
3. Vérifier les logs pour les erreurs de connexion

### Les Recommandations ne Sont pas Pertinentes

1. Vérifier que les interactions sont correctement enregistrées
2. Vérifier que les préférences utilisateur sont à jour
3. Contacter l'équipe du système de recommandation pour un diagnostic approfondi
