#!/usr/bin/env python3
"""
Point d'entrée principal pour le microservice Agent-RB.
"""
import os
import json
import logging
import yaml
from flask import Flask, request, jsonify, send_from_directory, render_template_string
from flask_cors import CORS
import requests
from utils.alerting import AlertManager
from config.alert_rules import configure_alert_rules

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Création de l'application Flask
app = Flask(__name__)
CORS(app)

# Configuration des alertes
configure_alert_rules()
alert_manager = AlertManager.get_instance()
if os.environ.get('ALERTING_ENABLED', 'false').lower() == 'true':
    alert_manager.start()

# Configuration
ENVIRONMENT = os.environ.get('ENVIRONMENT', 'development')
SUPERAGENT_SERVICE_URL = os.environ.get('SUPERAGENT_SERVICE_URL', 'http://localhost:5001')
AGENT_IA_SERVICE_URL = os.environ.get('AGENT_IA_SERVICE_URL', 'http://localhost:5002')

# Routes pour la santé et la préparation
@app.route('/health', methods=['GET'])
def health_check():
    """Endpoint pour vérifier l'état de santé du service."""
    return jsonify({"status": "healthy"})

@app.route('/ready', methods=['GET'])
def ready_check():
    """Endpoint pour vérifier si le service est prêt."""
    return jsonify({"status": "ready"})

# Routes pour les métriques
@app.route('/metrics', methods=['GET'])
def get_metrics():
    """Endpoint pour récupérer les métriques du service."""
    from services.communication_with_tracing import CommunicationService
    
    metrics = {
        'circuit_breakers': CommunicationService.get_circuit_breaker_metrics(),
        'requests': CommunicationService.get_request_metrics()
    }
    
    return jsonify(metrics)

@app.route('/metrics/services/<service>', methods=['GET'])
def get_service_metrics(service):
    """Endpoint pour récupérer les métriques d'un service spécifique."""
    from services.communication_with_tracing import CommunicationService
    
    metrics = {
        'circuit_breaker': CommunicationService.get_circuit_breaker_metrics().get(service, {}),
        'requests': CommunicationService.get_service_metrics(service),
        'errors': CommunicationService.get_error_counts(service)
    }
    
    return jsonify(metrics)

# Routes pour les alertes
@app.route('/alerts', methods=['GET'])
def get_alerts():
    """Endpoint pour récupérer les règles d'alerte."""
    rules = alert_manager.get_rules()
    return jsonify(rules)

@app.route('/alerts/<name>', methods=['GET'])
def get_alert(name):
    """Endpoint pour récupérer une règle d'alerte spécifique."""
    rule = alert_manager.get_rule(name)
    if rule:
        return jsonify(rule.to_dict())
    return jsonify({'error': 'Alert rule not found'}), 404

@app.route('/alerts/<name>/enable', methods=['POST'])
def enable_alert(name):
    """Endpoint pour activer une règle d'alerte."""
    if alert_manager.enable_rule(name):
        return jsonify({'success': True, 'message': f'Alert rule {name} enabled'})
    return jsonify({'error': 'Alert rule not found'}), 404

@app.route('/alerts/<name>/disable', methods=['POST'])
def disable_alert(name):
    """Endpoint pour désactiver une règle d'alerte."""
    if alert_manager.disable_rule(name):
        return jsonify({'success': True, 'message': f'Alert rule {name} disabled'})
    return jsonify({'error': 'Alert rule not found'}), 404

@app.route('/alerts/check', methods=['POST'])
def check_alerts():
    """Endpoint pour vérifier les règles d'alerte."""
    triggered_alerts = alert_manager.check_rules()
    return jsonify({
        'success': True,
        'triggered_alerts': triggered_alerts,
        'count': len(triggered_alerts)
    })

# Routes pour les retraites
@app.route('/api/retreats', methods=['GET'])
def get_retreats():
    """Récupérer la liste des retraites avec filtrage."""
    # Exemple de données de retraite
    retreats = [
        {
            "id": "1",
            "name": "Yoga Retreat in Provence",
            "description": "A relaxing yoga retreat in the heart of Provence",
            "location": "Provence, France",
            "start_date": "2024-07-15",
            "end_date": "2024-07-22",
            "type": "yoga",
            "price": 1200.00
        },
        {
            "id": "2",
            "name": "Meditation in the Alps",
            "description": "Find inner peace in the beautiful Alps",
            "location": "Chamonix, France",
            "start_date": "2024-08-10",
            "end_date": "2024-08-17",
            "type": "meditation",
            "price": 1500.00
        }
    ]
    
    # Filtrage basé sur les paramètres de requête
    retreat_type = request.args.get('type')
    location = request.args.get('location')
    month = request.args.get('month')
    
    if retreat_type:
        retreats = [r for r in retreats if r['type'] == retreat_type]
    if location:
        retreats = [r for r in retreats if location.lower() in r['location'].lower()]
    if month:
        retreats = [r for r in retreats if r['start_date'].split('-')[1] == month]
    
    return jsonify({"success": True, "data": retreats})

@app.route('/api/retreats/<retreat_id>', methods=['GET'])
def get_retreat(retreat_id):
    """Récupérer les détails d'une retraite spécifique."""
    # Exemple de données de retraite
    retreat = {
        "id": retreat_id,
        "name": "Yoga Retreat in Provence",
        "description": "A relaxing yoga retreat in the heart of Provence",
        "location": "Provence, France",
        "start_date": "2024-07-15",
        "end_date": "2024-07-22",
        "type": "yoga",
        "price": 1200.00,
        "host": {
            "id": "host1",
            "name": "Marie Dupont",
            "bio": "Certified yoga instructor with 10 years of experience"
        },
        "activities": [
            {
                "id": "act1",
                "name": "Morning Yoga",
                "description": "Start your day with energizing yoga",
                "duration": 90
            },
            {
                "id": "act2",
                "name": "Meditation Session",
                "description": "Afternoon meditation to center yourself",
                "duration": 60
            }
        ]
    }
    
    return jsonify({"success": True, "data": retreat})

# Routes pour les partenaires
@app.route('/api/partners', methods=['GET'])
def get_partners():
    """Récupérer la liste des partenaires."""
    # Exemple de données de partenaires
    partners = [
        {
            "id": "partner1",
            "name": "Wellness Experts",
            "type": "host",
            "location": "Paris, France",
            "specialties": ["yoga", "meditation"]
        },
        {
            "id": "partner2",
            "name": "Mountain Retreats",
            "type": "venue",
            "location": "Alps, France",
            "specialties": ["hiking", "wellness"]
        }
    ]
    
    return jsonify({"success": True, "data": partners})

# Routes pour la communication avec superagent
@app.route('/api/workflows/start', methods=['POST'])
def start_workflow():
    """Démarrer un workflow dans superagent."""
    data = request.json
    
    try:
        # Appel au service superagent
        response = requests.post(
            f"{SUPERAGENT_SERVICE_URL}/workflows/start",
            json=data,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        return jsonify(response.json())
    except requests.exceptions.RequestException as e:
        logger.error(f"Error calling superagent service: {str(e)}")
        return jsonify({"success": False, "error": "Failed to start workflow"}), 500

# Routes pour la communication avec Agent IA
@app.route('/api/analyze', methods=['POST'])
def analyze_text():
    """Analyser du texte avec Agent IA."""
    data = request.json
    
    try:
        # Appel au service Agent IA
        response = requests.post(
            f"{AGENT_IA_SERVICE_URL}/analyze",
            json=data,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        return jsonify(response.json())
    except requests.exceptions.RequestException as e:
        logger.error(f"Error calling Agent IA service: {str(e)}")
        return jsonify({"success": False, "error": "Failed to analyze text"}), 500

# Route pour la documentation Swagger
@app.route('/docs', methods=['GET'])
def swagger_ui():
    """Endpoint pour afficher la documentation Swagger."""
    swagger_html = '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Agent-RB API Documentation</title>
        <meta charset="utf-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui.css" />
    </head>
    <body>
        <div id="swagger-ui"></div>
        <script src="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui-bundle.js" charset="UTF-8"> </script>
        <script src="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui-standalone-preset.js" charset="UTF-8"> </script>
        <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: "/docs/swagger.yaml",
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                layout: "StandaloneLayout"
            });
            window.ui = ui;
        };
        </script>
    </body>
    </html>
    '''
    return render_template_string(swagger_html)

@app.route('/docs/swagger.yaml', methods=['GET'])
def swagger_yaml():
    """Endpoint pour récupérer le fichier swagger.yaml."""
    with open('docs/swagger.yaml', 'r') as f:
        swagger_dict = yaml.safe_load(f)
    
    # Mettre à jour l'URL du serveur en fonction de l'environnement
    if ENVIRONMENT == 'production':
        swagger_dict['servers'][0]['url'] = request.url_root.rstrip('/')
    
    return jsonify(swagger_dict)

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=ENVIRONMENT == 'development')
