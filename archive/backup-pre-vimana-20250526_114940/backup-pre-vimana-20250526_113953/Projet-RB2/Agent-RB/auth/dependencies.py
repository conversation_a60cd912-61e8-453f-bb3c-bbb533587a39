"""
Dépendances pour l'authentification et l'autorisation.
"""

import logging
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer

from .jwt_auth import decode_access_token
from .models import User, UserRole, Permission, ROLE_PERMISSIONS
from src.database.repositories.user_repository import UserRepository

# Configuration du logging
logger = logging.getLogger(__name__)

# Configuration de OAuth2
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/auth/token")

# Créer le repository des utilisateurs
user_repo = UserRepository()

async def get_current_user(token: str = Depends(oauth2_scheme)) -> User:
    """
    Récupère l'utilisateur actuel à partir du token JWT.
    
    Args:
        token: Token JWT
        
    Returns:
        Utilisateur actuel
        
    Raises:
        HTTPException: Si le token est invalide ou l'utilisateur n'existe pas
    """
    # Décoder le token
    token_data = decode_access_token(token)
    if token_data is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token d'authentification invalide",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    # Récupérer l'utilisateur
    user = user_repo.get_by_id(token_data.sub)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Utilisateur non trouvé",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """
    Vérifie que l'utilisateur actuel est actif.
    
    Args:
        current_user: Utilisateur actuel
        
    Returns:
        Utilisateur actuel
        
    Raises:
        HTTPException: Si l'utilisateur est inactif
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Utilisateur inactif"
        )
    
    return current_user

async def get_current_admin_user(current_user: User = Depends(get_current_active_user)) -> User:
    """
    Vérifie que l'utilisateur actuel est un administrateur.
    
    Args:
        current_user: Utilisateur actuel
        
    Returns:
        Utilisateur actuel
        
    Raises:
        HTTPException: Si l'utilisateur n'est pas un administrateur
    """
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Accès réservé aux administrateurs"
        )
    
    return current_user

def has_permission(permission: Permission):
    """
    Vérifie qu'un utilisateur a une permission spécifique.
    
    Args:
        permission: Permission à vérifier
        
    Returns:
        Fonction de dépendance
    """
    async def check_permission(current_user: User = Depends(get_current_active_user)) -> User:
        # Récupérer les permissions du rôle de l'utilisateur
        user_permissions = ROLE_PERMISSIONS.get(current_user.role, [])
        
        # Vérifier si l'utilisateur a la permission
        if permission not in user_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission insuffisante: {permission}"
            )
        
        return current_user
    
    return check_permission

def is_owner_or_admin(resource_owner_id: str):
    """
    Vérifie qu'un utilisateur est le propriétaire d'une ressource ou un administrateur.
    
    Args:
        resource_owner_id: ID du propriétaire de la ressource
        
    Returns:
        Fonction de dépendance
    """
    async def check_ownership(current_user: User = Depends(get_current_active_user)) -> User:
        # Vérifier si l'utilisateur est le propriétaire ou un administrateur
        if current_user.id != resource_owner_id and current_user.role != UserRole.ADMIN:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Accès non autorisé à cette ressource"
            )
        
        return current_user
    
    return check_ownership
