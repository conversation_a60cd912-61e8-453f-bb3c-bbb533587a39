"""
Fonctions pour l'authentification JWT.
"""

import os
import jwt
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from passlib.context import CryptContext

from .models import TokenData, UserRole

# Configuration du logging
logger = logging.getLogger(__name__)

# Configuration de la cryptographie des mots de passe
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Clé secrète pour la signature des tokens JWT
SECRET_KEY = os.environ.get("JWT_SECRET_KEY", "your-secret-key-for-development-only")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def get_password_hash(password: str) -> str:
    """
    Génère un hash sécurisé pour un mot de passe.
    
    Args:
        password: Mot de passe en clair
        
    Returns:
        Hash du mot de passe
    """
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Vérifie si un mot de passe correspond à un hash.
    
    Args:
        plain_password: Mot de passe en clair
        hashed_password: Hash du mot de passe
        
    Returns:
        True si le mot de passe correspond, False sinon
    """
    return pwd_context.verify(plain_password, hashed_password)

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    Crée un token JWT.
    
    Args:
        data: Données à inclure dans le token
        expires_delta: Durée de validité du token
        
    Returns:
        Token JWT encodé
    """
    to_encode = data.copy()
    
    # Définir la date d'expiration
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    # Ajouter la date d'expiration aux données
    to_encode.update({"exp": expire})
    
    # Encoder le token
    try:
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    except Exception as e:
        logger.error(f"Erreur lors de la création du token JWT: {str(e)}")
        raise

def decode_access_token(token: str) -> Optional[TokenData]:
    """
    Décode un token JWT.
    
    Args:
        token: Token JWT encodé
        
    Returns:
        Données du token ou None si le token est invalide
    """
    try:
        # Décoder le token
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        # Extraire les données
        user_id = payload.get("sub")
        email = payload.get("email")
        role = payload.get("role")
        exp = payload.get("exp")
        
        # Vérifier que les données nécessaires sont présentes
        if user_id is None or email is None or role is None or exp is None:
            logger.warning("Données manquantes dans le token JWT")
            return None
        
        # Vérifier que le rôle est valide
        try:
            user_role = UserRole(role)
        except ValueError:
            logger.warning(f"Rôle invalide dans le token JWT: {role}")
            return None
        
        # Créer et retourner les données du token
        token_data = TokenData(
            sub=user_id,
            email=email,
            role=user_role,
            exp=exp
        )
        
        return token_data
    
    except jwt.ExpiredSignatureError:
        logger.warning("Token JWT expiré")
        return None
    
    except jwt.InvalidTokenError:
        logger.warning("Token JWT invalide")
        return None
    
    except Exception as e:
        logger.error(f"Erreur lors du décodage du token JWT: {str(e)}")
        return None
