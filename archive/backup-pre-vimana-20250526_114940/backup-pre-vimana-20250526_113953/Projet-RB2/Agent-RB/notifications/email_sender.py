"""
Service d'envoi d'emails.
"""

import logging
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON>ipart
from typing import Dict, Any, Optional

# Configuration du logging
logger = logging.getLogger(__name__)

class EmailSender:
    """
    Service d'envoi d'emails.
    """
    
    def __init__(self, 
                smtp_host: Optional[str] = None, 
                smtp_port: Optional[int] = None,
                smtp_user: Optional[str] = None,
                smtp_password: Optional[str] = None,
                from_email: Optional[str] = None,
                use_tls: bool = True):
        """
        Initialise le service d'envoi d'emails.
        
        Args:
            smtp_host: Hôte SMTP
            smtp_port: Port SMTP
            smtp_user: Utilisateur SMTP
            smtp_password: Mot de passe SMTP
            from_email: Email d'expéditeur
            use_tls: Utiliser TLS
        """
        self.smtp_host = smtp_host or os.environ.get("SMTP_HOST", "smtp.example.com")
        self.smtp_port = smtp_port or int(os.environ.get("SMTP_PORT", "587"))
        self.smtp_user = smtp_user or os.environ.get("SMTP_USER", "<EMAIL>")
        self.smtp_password = smtp_password or os.environ.get("SMTP_PASSWORD", "password")
        self.from_email = from_email or os.environ.get("FROM_EMAIL", "<EMAIL>")
        self.use_tls = use_tls
        
        # En mode développement, ne pas envoyer d'emails réels
        self.dev_mode = os.environ.get("ENVIRONMENT", "development") == "development"
    
    async def send_email(self, to_email: str, subject: str, body: str, html_body: Optional[str] = None) -> Dict[str, Any]:
        """
        Envoie un email.
        
        Args:
            to_email: Email du destinataire
            subject: Sujet de l'email
            body: Corps de l'email (texte)
            html_body: Corps de l'email (HTML, optionnel)
            
        Returns:
            Résultat de l'envoi
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Email qui serait envoyé à {to_email}: {subject}")
            logger.debug(f"[DEV MODE] Corps de l'email: {body}")
            return {
                "success": True,
                "message": "Email simulé en mode développement",
                "to": to_email,
                "subject": subject
            }
        
        try:
            # Créer le message
            msg = MIMEMultipart("alternative")
            msg["Subject"] = subject
            msg["From"] = self.from_email
            msg["To"] = to_email
            
            # Ajouter la version texte
            msg.attach(MIMEText(body, "plain"))
            
            # Ajouter la version HTML si fournie
            if html_body:
                msg.attach(MIMEText(html_body, "html"))
            
            # Envoyer l'email
            with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls()
                
                server.login(self.smtp_user, self.smtp_password)
                server.send_message(msg)
            
            logger.info(f"Email envoyé à {to_email}: {subject}")
            
            return {
                "success": True,
                "message": "Email envoyé avec succès",
                "to": to_email,
                "subject": subject
            }
        
        except Exception as e:
            logger.error(f"Erreur lors de l'envoi de l'email à {to_email}: {str(e)}")
            
            return {
                "success": False,
                "message": f"Erreur lors de l'envoi de l'email: {str(e)}",
                "to": to_email,
                "subject": subject,
                "error": str(e)
            }
