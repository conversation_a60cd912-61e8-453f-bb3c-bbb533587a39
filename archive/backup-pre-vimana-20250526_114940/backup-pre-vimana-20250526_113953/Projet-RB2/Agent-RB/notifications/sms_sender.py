"""
Service d'envoi de SMS.
"""

import logging
import os
from typing import Dict, Any, Optional

# Configuration du logging
logger = logging.getLogger(__name__)

class SMSSender:
    """
    Service d'envoi de SMS.
    """
    
    def __init__(self, 
                api_key: Optional[str] = None,
                api_secret: Optional[str] = None,
                from_number: Optional[str] = None):
        """
        Initialise le service d'envoi de SMS.
        
        Args:
            api_key: Clé API du service SMS
            api_secret: Secret API du service SMS
            from_number: Numéro d'expéditeur
        """
        self.api_key = api_key or os.environ.get("SMS_API_KEY", "api_key")
        self.api_secret = api_secret or os.environ.get("SMS_API_SECRET", "api_secret")
        self.from_number = from_number or os.environ.get("SMS_FROM_NUMBER", "+33600000000")
        
        # En mode développement, ne pas envoyer de SMS réels
        self.dev_mode = os.environ.get("ENVIRONMENT", "development") == "development"
    
    async def send_sms(self, to_number: str, message: str) -> Dict[str, Any]:
        """
        Envoie un SMS.
        
        Args:
            to_number: Numéro du destinataire
            message: Message à envoyer
            
        Returns:
            Résultat de l'envoi
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] SMS qui serait envoyé à {to_number}: {message}")
            return {
                "success": True,
                "message": "SMS simulé en mode développement",
                "to": to_number
            }
        
        try:
            # Ici, vous intégreriez un service SMS réel comme Twilio, Nexmo, etc.
            # Pour l'exemple, nous simulons l'envoi
            
            logger.info(f"SMS envoyé à {to_number}: {message[:20]}...")
            
            return {
                "success": True,
                "message": "SMS envoyé avec succès",
                "to": to_number
            }
        
        except Exception as e:
            logger.error(f"Erreur lors de l'envoi du SMS à {to_number}: {str(e)}")
            
            return {
                "success": False,
                "message": f"Erreur lors de l'envoi du SMS: {str(e)}",
                "to": to_number,
                "error": str(e)
            }
