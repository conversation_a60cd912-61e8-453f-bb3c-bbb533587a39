# Agent Workflow Graph

This directory contains the implementation of the agent workflow graph using LangGraph.

## Overview

The workflow graph defines the flow between different agent nodes and the conditions for transitioning between them. It uses LangGraph's `StateGraph` to create a directed graph where each node represents an agent and edges represent transitions between agents.

## Components

- **State**: Defined in `types.py`, this class holds the current state of the workflow, including the next agent to run, the current task, and any data needed by the agents.

- **Nodes**: Each node in the graph represents an agent with a specific responsibility:
  - **Coordinator**: Initializes the workflow and coordinates between agents
  - **Planner**: Creates a plan for the task
  - **Supervisor**: Monitors and directs the workflow
  - **Researcher**: Gathers information and context
  - **Coder**: Implements solutions and writes code
  - **Browser**: Handles web interactions and data extraction
  - **Reporter**: Generates reports and summaries

- **Graph**: The graph itself is defined in `workflow.py` and connects the nodes according to the workflow logic.

- **Configuration**: The workflow can be configured using YAML or JSON files. Default configuration is provided in `config/default_config.yaml`.

- **Utilities**: Helper functions for error handling, time operations, and other common tasks are provided in the `utils` package.

## Usage

### Using the API

To use the workflow graph programmatically, you can import the `run_workflow` function from `workflow.py` and call it with an optional initial state:

```python
from src.graph.workflow import run_workflow
from src.graph.types import State

# Create an initial state
initial_state = State(
    task={
        "id": "task_001",
        "type": "code_analysis",
        "description": "Analyze a Python codebase",
    }
)

# Run the workflow
final_state = await run_workflow(initial_state)

# Access the results
report = final_state.results.get("report", {})
```

See `src/examples/workflow_example.py` for a complete example.

### Using the CLI

You can also run workflows using the command-line interface:

```bash
# Run with default configuration
python -m src.cli

# Run with custom configuration and task
python -m src.cli --config path/to/config.yaml --task path/to/task.yaml

# Save results to a file
python -m src.cli --task path/to/task.yaml --output results.json

# Set logging level
python -m src.cli --log-level DEBUG
```

## Task Files

Task files define the work to be done by the workflow. They can be in YAML or JSON format:

```yaml
id: task_001
type: code_analysis
description: Analyze a Python codebase for security vulnerabilities
priority: high

parameters:
  language: python
  focus_areas:
    - input_validation
    - authentication
```

See examples in `src/examples/tasks/`.

## Configuration

The workflow can be configured using YAML or JSON files. The configuration includes settings for each agent, such as timeout values and agent-specific parameters.

```yaml
name: my_workflow
description: Custom workflow configuration
version: 1.0.0
max_steps: 100
logging_level: INFO

agents:
  - name: coordinator
    enabled: true
    timeout_seconds: 30
    parameters:
      priority: high
```

## Error Handling

The workflow includes robust error handling with support for recoverable and non-recoverable errors. The supervisor node is responsible for handling errors and deciding whether to continue or terminate the workflow.

## Extending the Workflow

To extend the workflow with new agents:

1. Create a new node implementation in `src/agents/nodes/`
2. Add the node to the exports in `src/agents/nodes/__init__.py`
3. Update the graph in `workflow.py` to include the new node and define its connections
4. Add configuration for the new agent in the configuration file

## Dependencies

- LangGraph: For creating and executing the workflow graph
- PyYAML: For parsing YAML configuration files
- Python 3.8+: For async/await support
