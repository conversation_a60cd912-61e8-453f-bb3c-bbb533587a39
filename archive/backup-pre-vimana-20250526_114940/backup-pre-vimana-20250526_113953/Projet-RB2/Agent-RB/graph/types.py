from typing import Dict, Any, Optional, List, Union

class State:
    """
    State class for the agent workflow graph.
    Holds the current state of the workflow, including the next agent to run,
    the current task, and any data needed by the agents.
    """
    def __init__(self, 
                 next: str = "coordinator",
                 task: Optional[Dict[str, Any]] = None,
                 context: Optional[Dict[str, Any]] = None,
                 results: Optional[Dict[str, Any]] = None,
                 history: Optional[List[Dict[str, Any]]] = None,
                 error: Optional[str] = None):
        """
        Initialize the workflow state.
        
        Args:
            next: The next agent to run in the workflow
            task: The current task details
            context: Additional context information
            results: Results from previous agent executions
            history: History of agent executions
            error: Any error that occurred during execution
        """
        self.next = next
        self.task = task or {}
        self.context = context or {}
        self.results = results or {}
        self.history = history or []
        self.error = error
        
    def __getitem__(self, key: str) -> Any:
        """
        Allow dictionary-like access to state attributes.
        
        Args:
            key: The attribute name to access
            
        Returns:
            The value of the attribute
        """
        return getattr(self, key)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the state to a dictionary.
        
        Returns:
            Dictionary representation of the state
        """
        return {
            "next": self.next,
            "task": self.task,
            "context": self.context,
            "results": self.results,
            "history": self.history,
            "error": self.error
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'State':
        """
        Create a State instance from a dictionary.
        
        Args:
            data: Dictionary containing state data
            
        Returns:
            A new State instance
        """
        return cls(
            next=data.get("next", "coordinator"),
            task=data.get("task", {}),
            context=data.get("context", {}),
            results=data.get("results", {}),
            history=data.get("history", []),
            error=data.get("error")
        )
