FROM python:3.9-slim

WORKDIR /app

# Copier les fichiers de dépendances
COPY requirements.txt .

# Installer les dépendances
RUN pip install --no-cache-dir -r requirements.txt

# Copier le code source
COPY . .

# Exposer le port sur lequel le service s'exécute
EXPOSE 5000

# Variable d'environnement pour indiquer l'environnement d'exécution
ENV ENVIRONMENT=production

# Commande pour démarrer le service
CMD ["python", "app.py"]
