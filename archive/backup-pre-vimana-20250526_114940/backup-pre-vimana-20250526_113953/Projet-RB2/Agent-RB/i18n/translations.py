"""
Fonctions de traduction.
"""

import os
import json
import logging
from typing import Dict, Any, Optional

# Configuration du logging
logger = logging.getLogger(__name__)

# Chemin vers les fichiers de traduction
TRANSLATIONS_DIR = os.path.join(os.path.dirname(__file__), "translations")

# Créer le répertoire des traductions s'il n'existe pas
os.makedirs(TRANSLATIONS_DIR, exist_ok=True)

# Cache des traductions
_translations_cache = {}

# Langues supportées
_supported_languages = {
    "fr": "Français",
    "en": "English",
    "es": "Español",
    "de": "Deutsch",
    "it": "Italiano"
}

def get_supported_languages() -> Dict[str, str]:
    """
    Récupère la liste des langues supportées.
    
    Returns:
        Dictionnaire des langues supportées (code: nom)
    """
    return _supported_languages

def _load_translations(language: str) -> Dict[str, str]:
    """
    Charge les traductions pour une langue.
    
    Args:
        language: Code de la langue
        
    Returns:
        Dictionnaire des traductions
    """
    # Vérifier si les traductions sont déjà en cache
    if language in _translations_cache:
        return _translations_cache[language]
    
    # Chemin vers le fichier de traduction
    translations_file = os.path.join(TRANSLATIONS_DIR, f"{language}.json")
    
    # Vérifier si le fichier existe
    if not os.path.exists(translations_file):
        # Créer un fichier de traduction vide
        if language == "fr":
            # Pour le français, créer un fichier avec des traductions par défaut
            translations = {
                "common.welcome": "Bienvenue sur Retreat & Be",
                "common.error": "Une erreur est survenue",
                "common.success": "Opération réussie",
                "common.cancel": "Annuler",
                "common.save": "Enregistrer",
                "common.delete": "Supprimer",
                "common.edit": "Modifier",
                "common.create": "Créer",
                "common.search": "Rechercher",
                "common.filter": "Filtrer",
                "common.sort": "Trier",
                "common.next": "Suivant",
                "common.previous": "Précédent",
                "common.loading": "Chargement...",
                "common.noResults": "Aucun résultat",
                "common.yes": "Oui",
                "common.no": "Non",
                "common.confirm": "Confirmer",
                "common.cancel": "Annuler",
                "common.close": "Fermer",
                "common.open": "Ouvrir",
                "common.more": "Plus",
                "common.less": "Moins",
                "common.all": "Tous",
                "common.none": "Aucun",
                "common.back": "Retour",
                "common.home": "Accueil",
                "common.settings": "Paramètres",
                "common.profile": "Profil",
                "common.logout": "Déconnexion",
                "common.login": "Connexion",
                "common.register": "Inscription",
                "common.forgotPassword": "Mot de passe oublié",
                "common.resetPassword": "Réinitialiser le mot de passe",
                "common.changePassword": "Changer le mot de passe",
                "common.password": "Mot de passe",
                "common.email": "Email",
                "common.phone": "Téléphone",
                "common.address": "Adresse",
                "common.city": "Ville",
                "common.country": "Pays",
                "common.postalCode": "Code postal",
                "common.firstName": "Prénom",
                "common.lastName": "Nom",
                "common.fullName": "Nom complet",
                "common.dateOfBirth": "Date de naissance",
                "common.gender": "Genre",
                "common.male": "Homme",
                "common.female": "Femme",
                "common.other": "Autre",
                "common.language": "Langue",
                "common.currency": "Devise",
                "common.price": "Prix",
                "common.total": "Total",
                "common.subtotal": "Sous-total",
                "common.tax": "Taxe",
                "common.discount": "Réduction",
                "common.quantity": "Quantité",
                "common.date": "Date",
                "common.time": "Heure",
                "common.duration": "Durée",
                "common.start": "Début",
                "common.end": "Fin",
                "common.status": "Statut",
                "common.active": "Actif",
                "common.inactive": "Inactif",
                "common.pending": "En attente",
                "common.completed": "Terminé",
                "common.cancelled": "Annulé",
                "common.confirmed": "Confirmé",
                "common.rejected": "Rejeté",
                "common.approved": "Approuvé",
                "common.draft": "Brouillon",
                "common.published": "Publié",
                "common.unpublished": "Non publié",
                "common.archived": "Archivé",
                "common.deleted": "Supprimé",
                "common.error": "Erreur",
                "common.warning": "Avertissement",
                "common.info": "Information",
                "common.success": "Succès",
                "retreat.title": "Retraite",
                "retreat.description": "Description",
                "retreat.type": "Type de retraite",
                "retreat.location": "Lieu",
                "retreat.startDate": "Date de début",
                "retreat.endDate": "Date de fin",
                "retreat.duration": "Durée",
                "retreat.capacity": "Capacité",
                "retreat.participants": "Participants",
                "retreat.price": "Prix",
                "retreat.included": "Inclus",
                "retreat.excluded": "Non inclus",
                "retreat.requirements": "Prérequis",
                "retreat.activities": "Activités",
                "retreat.schedule": "Programme",
                "retreat.accommodation": "Hébergement",
                "retreat.meals": "Repas",
                "retreat.transport": "Transport",
                "retreat.organizer": "Organisateur",
                "retreat.instructors": "Instructeurs",
                "retreat.partners": "Partenaires",
                "retreat.reviews": "Avis",
                "retreat.rating": "Note",
                "retreat.photos": "Photos",
                "retreat.video": "Vidéo",
                "retreat.map": "Carte",
                "retreat.directions": "Itinéraire",
                "retreat.nearbyPlaces": "Lieux à proximité",
                "retreat.weather": "Météo",
                "retreat.faq": "FAQ",
                "retreat.terms": "Conditions",
                "retreat.cancellationPolicy": "Politique d'annulation",
                "retreat.refundPolicy": "Politique de remboursement",
                "retreat.bookNow": "Réserver maintenant",
                "retreat.availability": "Disponibilité",
                "retreat.availableSpots": "Places disponibles",
                "retreat.soldOut": "Complet",
                "retreat.waitingList": "Liste d'attente",
                "retreat.joinWaitingList": "Rejoindre la liste d'attente",
                "retreat.share": "Partager",
                "retreat.addToCalendar": "Ajouter au calendrier",
                "retreat.downloadIcal": "Télécharger iCal",
                "retreat.addToGoogleCalendar": "Ajouter à Google Calendar",
                "retreat.getDirections": "Obtenir l'itinéraire",
                "retreat.viewOnMap": "Voir sur la carte",
                "booking.title": "Réservation",
                "booking.status": "Statut",
                "booking.date": "Date de réservation",
                "booking.participants": "Participants",
                "booking.accommodation": "Hébergement",
                "booking.roomType": "Type de chambre",
                "booking.singleRoom": "Chambre simple",
                "booking.doubleRoom": "Chambre double",
                "booking.sharedRoom": "Chambre partagée",
                "booking.dormitory": "Dortoir",
                "booking.tent": "Tente",
                "booking.meals": "Repas",
                "booking.transport": "Transport",
                "booking.extras": "Extras",
                "booking.specialRequests": "Demandes spéciales",
                "booking.totalPrice": "Prix total",
                "booking.deposit": "Acompte",
                "booking.remainingBalance": "Solde restant",
                "booking.paymentMethod": "Méthode de paiement",
                "booking.paymentStatus": "Statut du paiement",
                "booking.paid": "Payé",
                "booking.unpaid": "Non payé",
                "booking.partiallyPaid": "Partiellement payé",
                "booking.refunded": "Remboursé",
                "booking.payNow": "Payer maintenant",
                "booking.payDeposit": "Payer l'acompte",
                "booking.payRemainingBalance": "Payer le solde",
                "booking.cancel": "Annuler la réservation",
                "booking.modify": "Modifier la réservation",
                "booking.confirmCancellation": "Confirmer l'annulation",
                "booking.cancellationReason": "Raison de l'annulation",
                "booking.cancellationDate": "Date d'annulation",
                "booking.cancellationFee": "Frais d'annulation",
                "booking.refundAmount": "Montant du remboursement",
                "booking.invoice": "Facture",
                "booking.receipt": "Reçu",
                "booking.downloadInvoice": "Télécharger la facture",
                "booking.downloadReceipt": "Télécharger le reçu",
                "partner.title": "Partenaire",
                "partner.name": "Nom",
                "partner.type": "Type",
                "partner.specialties": "Spécialités",
                "partner.description": "Description",
                "partner.bio": "Biographie",
                "partner.website": "Site web",
                "partner.socialMedia": "Réseaux sociaux",
                "partner.email": "Email",
                "partner.phone": "Téléphone",
                "partner.address": "Adresse",
                "partner.location": "Lieu",
                "partner.languages": "Langues",
                "partner.certifications": "Certifications",
                "partner.experience": "Expérience",
                "partner.availability": "Disponibilité",
                "partner.pricing": "Tarifs",
                "partner.reviews": "Avis",
                "partner.rating": "Note",
                "partner.photos": "Photos",
                "partner.video": "Vidéo",
                "partner.retreats": "Retraites",
                "partner.upcomingRetreats": "Retraites à venir",
                "partner.pastRetreats": "Retraites passées",
                "partner.contact": "Contact",
                "partner.message": "Message",
                "partner.send": "Envoyer",
                "client.title": "Client",
                "client.profile": "Profil",
                "client.preferences": "Préférences",
                "client.retreatTypes": "Types de retraites",
                "client.locations": "Lieux",
                "client.budget": "Budget",
                "client.duration": "Durée",
                "client.activities": "Activités",
                "client.dietaryRestrictions": "Restrictions alimentaires",
                "client.healthConditions": "Conditions de santé",
                "client.emergencyContact": "Contact d'urgence",
                "client.bookings": "Réservations",
                "client.upcomingBookings": "Réservations à venir",
                "client.pastBookings": "Réservations passées",
                "client.cancelledBookings": "Réservations annulées",
                "client.wishlist": "Liste de souhaits",
                "client.notifications": "Notifications",
                "client.notificationSettings": "Paramètres de notification",
                "client.emailNotifications": "Notifications par email",
                "client.smsNotifications": "Notifications par SMS",
                "client.pushNotifications": "Notifications push",
                "payment.title": "Paiement",
                "payment.method": "Méthode de paiement",
                "payment.creditCard": "Carte de crédit",
                "payment.bankTransfer": "Virement bancaire",
                "payment.paypal": "PayPal",
                "payment.applePay": "Apple Pay",
                "payment.googlePay": "Google Pay",
                "payment.cash": "Espèces",
                "payment.other": "Autre",
                "payment.cardNumber": "Numéro de carte",
                "payment.cardHolder": "Titulaire de la carte",
                "payment.expiryDate": "Date d'expiration",
                "payment.cvv": "CVV",
                "payment.billingAddress": "Adresse de facturation",
                "payment.amount": "Montant",
                "payment.currency": "Devise",
                "payment.status": "Statut",
                "payment.date": "Date",
                "payment.reference": "Référence",
                "payment.description": "Description",
                "payment.fee": "Frais",
                "payment.tax": "Taxe",
                "payment.total": "Total",
                "payment.refund": "Remboursement",
                "payment.refundReason": "Raison du remboursement",
                "payment.refundAmount": "Montant du remboursement",
                "payment.refundDate": "Date du remboursement",
                "payment.refundStatus": "Statut du remboursement",
                "payment.receipt": "Reçu",
                "payment.invoice": "Facture",
                "payment.downloadReceipt": "Télécharger le reçu",
                "payment.downloadInvoice": "Télécharger la facture",
                "notification.title": "Notification",
                "notification.type": "Type",
                "notification.message": "Message",
                "notification.date": "Date",
                "notification.read": "Lu",
                "notification.unread": "Non lu",
                "notification.markAsRead": "Marquer comme lu",
                "notification.markAsUnread": "Marquer comme non lu",
                "notification.delete": "Supprimer",
                "notification.deleteAll": "Supprimer tout",
                "notification.settings": "Paramètres de notification",
                "notification.email": "Email",
                "notification.sms": "SMS",
                "notification.push": "Push",
                "notification.retreatCreated": "Nouvelle retraite créée",
                "notification.retreatUpdated": "Retraite mise à jour",
                "notification.retreatCancelled": "Retraite annulée",
                "notification.retreatReminder": "Rappel de retraite",
                "notification.bookingCreated": "Réservation créée",
                "notification.bookingConfirmed": "Réservation confirmée",
                "notification.bookingCancelled": "Réservation annulée",
                "notification.paymentReceived": "Paiement reçu",
                "notification.paymentFailed": "Paiement échoué",
                "notification.refundProcessed": "Remboursement traité",
                "error.required": "Ce champ est requis",
                "error.email": "Email invalide",
                "error.password": "Mot de passe invalide",
                "error.passwordMatch": "Les mots de passe ne correspondent pas",
                "error.minLength": "Doit contenir au moins {min} caractères",
                "error.maxLength": "Doit contenir au plus {max} caractères",
                "error.min": "Doit être supérieur ou égal à {min}",
                "error.max": "Doit être inférieur ou égal à {max}",
                "error.pattern": "Format invalide",
                "error.server": "Erreur serveur",
                "error.notFound": "Non trouvé",
                "error.unauthorized": "Non autorisé",
                "error.forbidden": "Interdit",
                "error.conflict": "Conflit",
                "error.badRequest": "Requête invalide",
                "error.timeout": "Délai d'attente dépassé",
                "error.offline": "Hors ligne",
                "error.unknown": "Erreur inconnue"
            }
            
            # Enregistrer les traductions
            with open(translations_file, "w", encoding="utf-8") as f:
                json.dump(translations, f, ensure_ascii=False, indent=2)
        else:
            # Pour les autres langues, créer un fichier vide
            translations = {}
            
            # Enregistrer les traductions
            with open(translations_file, "w", encoding="utf-8") as f:
                json.dump(translations, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Fichier de traduction créé pour '{language}'")
    else:
        # Charger les traductions depuis le fichier
        try:
            with open(translations_file, "r", encoding="utf-8") as f:
                translations = json.load(f)
        except Exception as e:
            logger.error(f"Erreur lors du chargement des traductions pour '{language}': {str(e)}")
            translations = {}
    
    # Mettre en cache les traductions
    _translations_cache[language] = translations
    
    return translations

def get_translation(key: str, language: str) -> str:
    """
    Récupère une traduction pour une clé et une langue.
    
    Args:
        key: Clé de traduction
        language: Code de la langue
        
    Returns:
        Texte traduit ou clé si non trouvé
    """
    # Charger les traductions
    translations = _load_translations(language)
    
    # Récupérer la traduction
    translation = translations.get(key)
    
    # Si la traduction n'existe pas, essayer avec le français
    if translation is None and language != "fr":
        translations_fr = _load_translations("fr")
        translation = translations_fr.get(key)
    
    # Si la traduction n'existe toujours pas, utiliser la clé
    if translation is None:
        translation = key
    
    return translation
