"""
Service d'internationalisation.
"""

import logging
import os
import json
from typing import Dict, Any, Optional, List

from .translations import get_translation, get_supported_languages

# Configuration du logging
logger = logging.getLogger(__name__)

class I18nService:
    """
    Service d'internationalisation pour le système de retraites de bien-être.
    """
    
    def __init__(self, default_language: str = "fr"):
        """
        Initialise le service d'internationalisation.
        
        Args:
            default_language: Langue par défaut (optionnel)
        """
        self.default_language = default_language
        self.supported_languages = get_supported_languages()
        
        # Vérifier que la langue par défaut est supportée
        if self.default_language not in self.supported_languages:
            logger.warning(f"Langue par défaut '{self.default_language}' non supportée. Utilisation de 'fr'.")
            self.default_language = "fr"
    
    def translate(self, key: str, language: Optional[str] = None, params: Optional[Dict[str, Any]] = None) -> str:
        """
        Traduit une clé dans la langue spécifiée.
        
        Args:
            key: Clé de traduction
            language: Langue cible (optionnel, utilise la langue par défaut si non spécifié)
            params: Paramètres de formatage (optionnel)
            
        Returns:
            Texte traduit
        """
        # Utiliser la langue par défaut si non spécifiée
        language = language or self.default_language
        
        # Vérifier que la langue est supportée
        if language not in self.supported_languages:
            logger.warning(f"Langue '{language}' non supportée. Utilisation de '{self.default_language}'.")
            language = self.default_language
        
        # Récupérer la traduction
        translation = get_translation(key, language)
        
        # Formater la traduction avec les paramètres
        if params:
            try:
                translation = translation.format(**params)
            except KeyError as e:
                logger.warning(f"Paramètre manquant pour la traduction '{key}': {str(e)}")
            except Exception as e:
                logger.error(f"Erreur lors du formatage de la traduction '{key}': {str(e)}")
        
        return translation
    
    def get_translations(self, language: Optional[str] = None) -> Dict[str, str]:
        """
        Récupère toutes les traductions pour une langue.
        
        Args:
            language: Langue cible (optionnel, utilise la langue par défaut si non spécifié)
            
        Returns:
            Dictionnaire des traductions
        """
        # Utiliser la langue par défaut si non spécifiée
        language = language or self.default_language
        
        # Vérifier que la langue est supportée
        if language not in self.supported_languages:
            logger.warning(f"Langue '{language}' non supportée. Utilisation de '{self.default_language}'.")
            language = self.default_language
        
        # Charger le fichier de traduction
        translations_file = os.path.join(os.path.dirname(__file__), "translations", f"{language}.json")
        
        try:
            with open(translations_file, "r", encoding="utf-8") as f:
                translations = json.load(f)
            
            return translations
        
        except Exception as e:
            logger.error(f"Erreur lors du chargement des traductions pour '{language}': {str(e)}")
            return {}
    
    def get_supported_languages(self) -> List[Dict[str, str]]:
        """
        Récupère la liste des langues supportées.
        
        Returns:
            Liste des langues supportées avec leur nom
        """
        return [
            {"code": code, "name": name}
            for code, name in self.supported_languages.items()
        ]
