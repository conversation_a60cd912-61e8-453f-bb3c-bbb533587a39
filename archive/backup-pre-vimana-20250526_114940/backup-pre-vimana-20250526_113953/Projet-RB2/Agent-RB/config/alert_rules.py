"""
Configuration des règles d'alerte pour surveiller les microservices.
"""
import logging
from typing import Dict, Any
from utils.alerting import <PERSON><PERSON><PERSON><PERSON><PERSON>, AlertRule
from services.communication_with_tracing import CommunicationService
from utils.circuit_breaker import CircuitBreaker

# Configuration du logging
logger = logging.getLogger(__name__)

def configure_alert_rules():
    """Configure les règles d'alerte pour surveiller les microservices."""
    alert_manager = AlertManager.get_instance()
    
    # Règle pour surveiller la santé de superagent
    alert_manager.add_rule(AlertRule(
        name="superagent_health",
        description="Vérifie si le service superagent est en bonne santé",
        check_function=lambda: not CommunicationService.check_superagent_health(),
        severity="error",
        cooldown=300  # 5 minutes
    ))
    
    # Règle pour surveiller la santé de Agent IA
    alert_manager.add_rule(AlertRule(
        name="agent_ia_health",
        description="Vérifie si le service Agent IA est en bonne santé",
        check_function=lambda: not CommunicationService.check_agent_ia_health(),
        severity="error",
        cooldown=300  # 5 minutes
    ))
    
    # Règle pour surveiller le circuit breaker de superagent
    alert_manager.add_rule(AlertRule(
        name="superagent_circuit_breaker",
        description="Vérifie si le circuit breaker de superagent est ouvert",
        check_function=lambda: CircuitBreaker.get_instance('superagent').get_state().value == 'open',
        severity="warning",
        cooldown=300  # 5 minutes
    ))
    
    # Règle pour surveiller le circuit breaker de Agent IA
    alert_manager.add_rule(AlertRule(
        name="agent_ia_circuit_breaker",
        description="Vérifie si le circuit breaker de Agent IA est ouvert",
        check_function=lambda: CircuitBreaker.get_instance('agent_ia').get_state().value == 'open',
        severity="warning",
        cooldown=300  # 5 minutes
    ))
    
    # Règle pour surveiller le taux d'erreur des requêtes vers superagent
    alert_manager.add_rule(AlertRule(
        name="superagent_error_rate",
        description="Vérifie si le taux d'erreur des requêtes vers superagent est trop élevé",
        check_function=lambda: _check_error_rate('superagent', 0.2),  # 20% d'erreurs
        severity="warning",
        cooldown=300  # 5 minutes
    ))
    
    # Règle pour surveiller le taux d'erreur des requêtes vers Agent IA
    alert_manager.add_rule(AlertRule(
        name="agent_ia_error_rate",
        description="Vérifie si le taux d'erreur des requêtes vers Agent IA est trop élevé",
        check_function=lambda: _check_error_rate('agent_ia', 0.2),  # 20% d'erreurs
        severity="warning",
        cooldown=300  # 5 minutes
    ))
    
    # Règle pour surveiller le temps de réponse moyen des requêtes vers superagent
    alert_manager.add_rule(AlertRule(
        name="superagent_response_time",
        description="Vérifie si le temps de réponse moyen des requêtes vers superagent est trop élevé",
        check_function=lambda: _check_response_time('superagent', 2.0),  # 2 secondes
        severity="warning",
        cooldown=300  # 5 minutes
    ))
    
    # Règle pour surveiller le temps de réponse moyen des requêtes vers Agent IA
    alert_manager.add_rule(AlertRule(
        name="agent_ia_response_time",
        description="Vérifie si le temps de réponse moyen des requêtes vers Agent IA est trop élevé",
        check_function=lambda: _check_response_time('agent_ia', 2.0),  # 2 secondes
        severity="warning",
        cooldown=300  # 5 minutes
    ))
    
    logger.info(f"Configured {len(alert_manager.get_rules())} alert rules")

def _check_error_rate(service: str, threshold: float) -> bool:
    """
    Vérifie si le taux d'erreur des requêtes vers un service est trop élevé.
    
    Args:
        service: Nom du service
        threshold: Seuil de taux d'erreur
        
    Returns:
        True si le taux d'erreur est supérieur au seuil, False sinon
    """
    metrics = CommunicationService.get_service_metrics(service)
    
    request_count = metrics.get('request_count', 0)
    error_count = metrics.get('error_count', 0)
    
    if request_count == 0:
        return False
    
    error_rate = error_count / request_count
    
    return error_rate > threshold

def _check_response_time(service: str, threshold: float) -> bool:
    """
    Vérifie si le temps de réponse moyen des requêtes vers un service est trop élevé.
    
    Args:
        service: Nom du service
        threshold: Seuil de temps de réponse en secondes
        
    Returns:
        True si le temps de réponse moyen est supérieur au seuil, False sinon
    """
    metrics = CommunicationService.get_service_metrics(service)
    
    endpoints = metrics.get('endpoints', {})
    
    # Calculer le temps de réponse moyen pour tous les endpoints
    total_time = 0.0
    total_count = 0
    
    for endpoint_metrics in endpoints.values():
        avg_response_time = endpoint_metrics.get('avg_response_time', 0.0)
        request_count = endpoint_metrics.get('request_count', 0)
        
        if request_count > 0:
            total_time += avg_response_time * request_count
            total_count += request_count
    
    if total_count == 0:
        return False
    
    avg_response_time = total_time / total_count
    
    return avg_response_time > threshold
