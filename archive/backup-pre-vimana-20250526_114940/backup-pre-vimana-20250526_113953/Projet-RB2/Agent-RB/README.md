# Système Multi-Agent pour Retraites de Bien-être

Ce système multi-agent est spécialisé pour la planification, l'organisation et la gestion de retraites de bien-être, ainsi que pour la mise en relation de professionnels (partenaires) et l'assistance aux clients.

## Architecture

Le système est basé sur une architecture multi-agent où différents agents spécialisés collaborent pour accomplir des tâches complexes. Il utilise LangGraph pour orchestrer les workflows entre les agents.

### Agents Spécialisés

- **Retreat Planner**: Planifie et organise des retraites de bien-être
- **Partner Matcher**: Trouve et met en relation des partenaires professionnels
- **Client Assistant**: Aide les clients à trouver des retraites et répond à leurs questions

### Workflows

Le système propose plusieurs workflows spécialisés:

1. **Workflow de Planification de Retraites**: Pour créer et organiser des retraites
2. **Workflow de Mise en Relation de Partenaires**: Pour trouver des partenaires adaptés
3. **Workflow d'Assistance Client**: Pour aider les clients et répondre à leurs questions

## Modèles de Données

Le système utilise des modèles de données spécifiques au domaine:

- **Partner**: Représente un partenaire professionnel (organisateur, expert, hébergement, etc.)
- **Retreat**: Représente une retraite de bien-être avec son programme, sa tarification, etc.
- **Client**: Représente un client avec ses préférences et son historique
- **Booking**: Représente une réservation pour une retraite

## Utilisation

### Planification de Retraites

```python
from src.graph.types import State
from src.graph.retreat_workflow import run_retreat_workflow

# Créer un état initial avec les détails de la retraite
initial_state = State(
    task={
        "type": "retreat_planning",
        "parameters": {
            "retreat_type": "yoga",
            "location": {"country": "France", "region": "Provence"},
            "duration": 7
        }
    }
)

# Exécuter le workflow
final_state = await run_retreat_workflow(initial_state)

# Accéder aux résultats
retreat = final_state.results.get("retreat")
```

### Recherche de Partenaires

```python
from src.graph.types import State
from src.graph.partner_workflow import run_partner_workflow

# Créer un état initial avec les critères de recherche
initial_state = State(
    task={
        "type": "partner_matching",
        "parameters": {
            "partner_types": ["wellness_expert", "accommodation"],
            "specialties": ["yoga", "meditation"],
            "location": {"country": "France", "region": "Provence"}
        }
    }
)

# Exécuter le workflow
final_state = await run_partner_workflow(initial_state)

# Accéder aux résultats
partners = final_state.results.get("partners")
```

### Assistance Client

```python
from src.graph.types import State
from src.graph.client_workflow import run_client_workflow

# Créer un état initial avec les préférences du client
initial_state = State(
    task={
        "type": "client_assistance",
        "assistance_type": "recommendation",
        "client": {
            "preferences": {
                "retreat_types": ["yoga", "meditation"],
                "locations": ["France", "Espagne"],
                "price_range": {"min": 800, "max": 1500}
            }
        }
    }
)

# Exécuter le workflow
final_state = await run_client_workflow(initial_state)

# Accéder aux résultats
recommendations = final_state.results.get("recommendations")
```

## Exemples

Des exemples complets sont disponibles dans le répertoire `examples/`:

- `retreat_workflow_example.py`: Exemple de planification de retraite
- `partner_workflow_example.py`: Exemple de recherche de partenaires
- `client_workflow_example.py`: Exemple d'assistance client

Pour exécuter tous les exemples:

```bash
./src/examples/run_all_examples.sh
```

## Configuration

Le système peut être configuré via des fichiers YAML ou JSON. La configuration par défaut se trouve dans `config/default_config.yaml`.

## Extension du Système

Pour étendre le système avec de nouvelles fonctionnalités:

1. Créer de nouveaux agents dans `agents/nodes/`
2. Ajouter les agents au fichier `agents/nodes/__init__.py`
3. Créer de nouveaux workflows dans `graph/`
4. Mettre à jour la configuration dans `config/default_config.yaml`
