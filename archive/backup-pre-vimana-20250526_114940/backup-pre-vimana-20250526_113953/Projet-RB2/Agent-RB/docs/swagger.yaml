openapi: 3.0.0
info:
  title: Agent-RB API
  description: API pour le service Agent-RB
  version: 1.0.0
  contact:
    name: Retreat And Be
    url: https://retreatandbe.com
servers:
  - url: http://localhost:5000
    description: Serveur de développement local
  - url: http://agent-rb-service:5000
    description: Serveur Kubernetes
paths:
  /health:
    get:
      summary: Vérifier l'état de santé du service
      description: Retourne l'état de santé du service
      operationId: healthCheck
      tags:
        - Sant<PERSON>
      responses:
        '200':
          description: Service en bonne santé
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: healthy
  /ready:
    get:
      summary: Vérifier si le service est prêt
      description: Retourne si le service est prêt à recevoir des requêtes
      operationId: readyCheck
      tags:
        - Santé
      responses:
        '200':
          description: Service prêt
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ready
  /metrics:
    get:
      summary: Récupérer les métriques du service
      description: Retourne les métriques collectées par le service
      operationId: getMetrics
      tags:
        - Métriques
      responses:
        '200':
          description: Métriques récupérées avec succès
          content:
            application/json:
              schema:
                type: object
                properties:
                  circuit_breakers:
                    type: object
                    description: Métriques des circuit breakers
                  requests:
                    type: object
                    description: Métriques des requêtes
  /metrics/services/{service}:
    get:
      summary: Récupérer les métriques d'un service spécifique
      description: Retourne les métriques collectées pour un service spécifique
      operationId: getServiceMetrics
      tags:
        - Métriques
      parameters:
        - name: service
          in: path
          description: Nom du service
          required: true
          schema:
            type: string
            enum: [superagent, agent_ia]
      responses:
        '200':
          description: Métriques récupérées avec succès
          content:
            application/json:
              schema:
                type: object
                properties:
                  circuit_breaker:
                    type: object
                    description: Métriques du circuit breaker
                  requests:
                    type: object
                    description: Métriques des requêtes
                  errors:
                    type: object
                    description: Nombre d'erreurs
  /api/retreats:
    get:
      summary: Récupérer la liste des retraites
      description: Retourne la liste des retraites avec filtrage
      operationId: getRetreats
      tags:
        - Retraites
      parameters:
        - name: type
          in: query
          description: Type de retraite
          required: false
          schema:
            type: string
        - name: location
          in: query
          description: Lieu de la retraite
          required: false
          schema:
            type: string
        - name: month
          in: query
          description: Mois de la retraite
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Liste des retraites récupérée avec succès
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Retreat'
  /api/retreats/{retreat_id}:
    get:
      summary: Récupérer les détails d'une retraite
      description: Retourne les détails d'une retraite spécifique
      operationId: getRetreat
      tags:
        - Retraites
      parameters:
        - name: retreat_id
          in: path
          description: ID de la retraite
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Détails de la retraite récupérés avec succès
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/RetreatDetail'
  /api/partners:
    get:
      summary: Récupérer la liste des partenaires
      description: Retourne la liste des partenaires
      operationId: getPartners
      tags:
        - Partenaires
      responses:
        '200':
          description: Liste des partenaires récupérée avec succès
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Partner'
  /api/workflows/start:
    post:
      summary: Démarrer un workflow
      description: Démarre un workflow dans superagent
      operationId: startWorkflow
      tags:
        - Workflows
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Nom du workflow
                  example: retreat_planning
                input:
                  type: object
                  description: Données d'entrée du workflow
      responses:
        '200':
          description: Workflow démarré avec succès
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  workflow_id:
                    type: string
                    example: 123e4567-e89b-12d3-a456-426614174000
        '500':
          description: Erreur lors du démarrage du workflow
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: string
                    example: Failed to start workflow
  /api/analyze:
    post:
      summary: Analyser du texte
      description: Analyse du texte avec Agent IA
      operationId: analyzeText
      tags:
        - IA
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                text:
                  type: string
                  description: Texte à analyser
                  example: Je cherche une retraite de yoga en Provence pour 7 jours en juillet.
      responses:
        '200':
          description: Texte analysé avec succès
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      entities:
                        type: object
                        description: Entités détectées
                      sentiment:
                        type: object
                        description: Analyse de sentiment
                      intent:
                        type: object
                        description: Intention détectée
        '500':
          description: Erreur lors de l'analyse du texte
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: string
                    example: Failed to analyze text
components:
  schemas:
    Retreat:
      type: object
      properties:
        id:
          type: string
          example: '1'
        name:
          type: string
          example: Yoga Retreat in Provence
        description:
          type: string
          example: A relaxing yoga retreat in the heart of Provence
        location:
          type: string
          example: Provence, France
        start_date:
          type: string
          format: date
          example: '2024-07-15'
        end_date:
          type: string
          format: date
          example: '2024-07-22'
        type:
          type: string
          example: yoga
        price:
          type: number
          format: float
          example: 1200.00
    RetreatDetail:
      type: object
      properties:
        id:
          type: string
          example: '1'
        name:
          type: string
          example: Yoga Retreat in Provence
        description:
          type: string
          example: A relaxing yoga retreat in the heart of Provence
        location:
          type: string
          example: Provence, France
        start_date:
          type: string
          format: date
          example: '2024-07-15'
        end_date:
          type: string
          format: date
          example: '2024-07-22'
        type:
          type: string
          example: yoga
        price:
          type: number
          format: float
          example: 1200.00
        host:
          type: object
          properties:
            id:
              type: string
              example: host1
            name:
              type: string
              example: Marie Dupont
            bio:
              type: string
              example: Certified yoga instructor with 10 years of experience
        activities:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                example: act1
              name:
                type: string
                example: Morning Yoga
              description:
                type: string
                example: Start your day with energizing yoga
              duration:
                type: integer
                example: 90
    Partner:
      type: object
      properties:
        id:
          type: string
          example: partner1
        name:
          type: string
          example: Wellness Experts
        type:
          type: string
          example: host
        location:
          type: string
          example: Paris, France
        specialties:
          type: array
          items:
            type: string
          example: [yoga, meditation]
