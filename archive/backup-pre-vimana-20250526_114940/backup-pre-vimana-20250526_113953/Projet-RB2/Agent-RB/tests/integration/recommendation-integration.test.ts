/**
 * Tests d'intégration pour l'intégration entre Agent-RB et le système de recommandation
 */

import { Test } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HttpModule, HttpService } from '@nestjs/axios';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { of } from 'rxjs';
import { RecommendationIntegrationService } from '../../src/services/recommendation-integration.service';
import { RecommendationController } from '../../src/controllers/recommendation.controller';

describe('Recommendation Integration (Integration)', () => {
  let app: INestApplication;
  let recommendationService: RecommendationIntegrationService;
  let httpService: HttpService;
  let jwtService: JwtService;

  // Mock des réponses HTTP
  const mockRecommendationsResponse = {
    items: [
      {
        id: 'retreat-1',
        type: 'RETREAT',
        title: 'Retraite de Yoga dans les Alpes',
        description: 'Une semaine de détente et de reconnexion avec la nature...',
        score: 0.95,
        sources: ['content-based', 'collaborative'],
        reasons: ['Basé sur vos préférences', 'Similaire à vos activités précédentes'],
        imageUrl: 'https://example.com/images/retreat-123.jpg',
        url: 'https://retreatandbe.com/retreats/retraite-yoga-alpes',
        metadata: {
          category: 'Yoga',
          level: 'INTERMEDIATE',
          location: 'Chamonix, France',
          duration: 7,
          tags: ['yoga', 'meditation', 'mountains'],
          price: 1200,
          currency: 'EUR',
          startDate: '2023-07-15',
          endDate: '2023-07-22',
          reviewCount: 42,
          averageRating: 4.7,
          available: true,
          remainingSpots: 5,
        },
      },
    ],
    meta: {
      total: 100,
      page: 1,
      limit: 10,
      totalPages: 10,
    },
  };

  beforeAll(async () => {
    // Créer un module de test
    const moduleRef = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        HttpModule,
        JwtModule.registerAsync({
          imports: [ConfigModule],
          useFactory: async (configService: ConfigService) => ({
            secret: configService.get<string>('JWT_SECRET') || 'test-secret',
            signOptions: {
              expiresIn: '1h',
            },
          }),
          inject: [ConfigService],
        }),
      ],
      controllers: [RecommendationController],
      providers: [RecommendationIntegrationService],
    })
      .overrideProvider(HttpService)
      .useValue({
        get: jest.fn(() => of({ data: mockRecommendationsResponse })),
        post: jest.fn(() => of({ data: { success: true } })),
      })
      .compile();

    // Créer l'application
    app = moduleRef.createNestApplication();
    await app.init();

    // Récupérer les services
    recommendationService = moduleRef.get<RecommendationIntegrationService>(RecommendationIntegrationService);
    httpService = moduleRef.get<HttpService>(HttpService);
    jwtService = moduleRef.get<JwtService>(JwtService);

    // Configurer les mocks
    jest.spyOn(httpService, 'get').mockImplementation(() => of({ data: mockRecommendationsResponse, status: 200, statusText: 'OK', headers: {}, config: {} }));
    jest.spyOn(httpService, 'post').mockImplementation(() => of({ data: { success: true }, status: 200, statusText: 'OK', headers: {}, config: {} }));
  });

  afterAll(async () => {
    await app.close();
  });

  describe('getPersonalizedRecommendations', () => {
    it('should return personalized recommendations', async () => {
      // Act
      const result = await recommendationService.getPersonalizedRecommendations('user-1', 'RETREAT');

      // Assert
      expect(result).toBeDefined();
      expect(result).toEqual(mockRecommendationsResponse);
      expect(httpService.get).toHaveBeenCalled();
    });

    it('should pass options to the recommendation service', async () => {
      // Arrange
      const options = {
        limit: 5,
        page: 2,
        filters: { category: 'Yoga' },
      };

      // Act
      await recommendationService.getPersonalizedRecommendations('user-1', 'RETREAT', options);

      // Assert
      expect(httpService.get).toHaveBeenCalled();
      const getCall = (httpService.get as jest.Mock).mock.calls[0];
      expect(getCall[0]).toContain('/api/v1/recommendations');
      expect(getCall[1].params).toEqual({
        type: 'RETREAT',
        limit: 5,
        page: 2,
        filters: { category: 'Yoga' },
      });
    });
  });

  describe('getTrendingRecommendations', () => {
    it('should return trending recommendations', async () => {
      // Act
      const result = await recommendationService.getTrendingRecommendations('RETREAT');

      // Assert
      expect(result).toBeDefined();
      expect(result).toEqual(mockRecommendationsResponse);
      expect(httpService.get).toHaveBeenCalled();
    });
  });

  describe('getSimilarItems', () => {
    it('should return similar items', async () => {
      // Act
      const result = await recommendationService.getSimilarItems('retreat-1', 'RETREAT');

      // Assert
      expect(result).toBeDefined();
      expect(result).toEqual(mockRecommendationsResponse);
      expect(httpService.get).toHaveBeenCalled();
    });
  });

  describe('recordInteraction', () => {
    it('should record an interaction', async () => {
      // Act
      const result = await recommendationService.recordInteraction(
        'user-1',
        'retreat-1',
        'RETREAT',
        'VIEW',
        { duration: 120 },
      );

      // Assert
      expect(result).toBeDefined();
      expect(result).toEqual({ success: true });
      expect(httpService.post).toHaveBeenCalled();
    });
  });

  describe('updatePreferences', () => {
    it('should update preferences', async () => {
      // Arrange
      const preferences = {
        preferredCategories: ['Yoga', 'Meditation'],
        preferredLevels: ['BEGINNER', 'INTERMEDIATE'],
      };

      // Act
      const result = await recommendationService.updatePreferences('user-1', preferences);

      // Assert
      expect(result).toBeDefined();
      expect(result).toEqual({ success: true });
      expect(httpService.post).toHaveBeenCalled();
    });
  });
});
