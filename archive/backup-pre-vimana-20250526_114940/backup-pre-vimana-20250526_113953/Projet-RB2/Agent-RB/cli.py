#!/usr/bin/env python
"""
Command-line interface for running workflows.
"""

import os
import sys
import json
import yaml
import asyncio
import argparse
import logging
from typing import Dict, Any, Optional

from src.graph.types import State
from src.graph.workflow import run_workflow
from src.config import load_config

def setup_logging(level: str = "INFO") -> None:
    """
    Set up logging configuration.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    """
    numeric_level = getattr(logging, level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f"Invalid log level: {level}")
    
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("workflow.log")
        ]
    )

def load_task_from_file(file_path: str) -> Dict[str, Any]:
    """
    Load task data from a file.
    
    Args:
        file_path: Path to the task file (JSON or YAML)
        
    Returns:
        Task data as a dictionary
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Task file not found: {file_path}")
    
    file_ext = os.path.splitext(file_path)[1].lower()
    
    if file_ext == '.json':
        with open(file_path, 'r') as f:
            return json.load(f)
    elif file_ext in ['.yaml', '.yml']:
        with open(file_path, 'r') as f:
            return yaml.safe_load(f)
    else:
        raise ValueError(f"Unsupported file format: {file_ext}")

async def main() -> None:
    """
    Main entry point for the CLI.
    """
    parser = argparse.ArgumentParser(description="Run agent workflows")
    
    parser.add_argument(
        "--config", 
        type=str, 
        help="Path to the workflow configuration file"
    )
    
    parser.add_argument(
        "--task", 
        type=str, 
        help="Path to the task definition file"
    )
    
    parser.add_argument(
        "--log-level", 
        type=str, 
        default="INFO", 
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="Logging level"
    )
    
    parser.add_argument(
        "--output", 
        type=str, 
        help="Path to save the workflow results"
    )
    
    args = parser.parse_args()
    
    # Set up logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    try:
        # Load configuration
        config = load_config(args.config)
        logger.info(f"Using workflow configuration: {config.name} (v{config.version})")
        
        # Create initial state
        initial_state = State()
        
        # Load task if provided
        if args.task:
            task_data = load_task_from_file(args.task)
            initial_state.task = task_data
            logger.info(f"Loaded task from {args.task}")
        
        # Run the workflow
        logger.info("Starting workflow execution...")
        final_state = await run_workflow(initial_state)
        
        # Print results
        print("\nWorkflow completed!")
        print(f"Final state: next={final_state.next}")
        print("\nHistory:")
        for entry in final_state.history:
            print(f"  - {entry['agent']}: {entry['action']} ({entry['timestamp']})")
        
        # Save results if output path is provided
        if args.output:
            output_dir = os.path.dirname(args.output)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                
            with open(args.output, 'w') as f:
                json.dump(final_state.to_dict(), f, indent=2)
            logger.info(f"Results saved to {args.output}")
        
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
