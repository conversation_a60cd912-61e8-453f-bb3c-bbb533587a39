"""
Routes pour les retraites.
"""

import logging
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, Path, Body, status

from src.models.retreat import Retreat, RetreatType, RetreatStatus
from src.database.repositories import RetreatRepository

# Configuration du logging
logger = logging.getLogger(__name__)

# C<PERSON>er le router
router = APIRouter()

# Créer le repository
retreat_repo = RetreatRepository()

@router.get("/", response_model=List[Retreat])
async def get_retreats(
    retreat_type: Optional[str] = Query(None, description="Type de retraite"),
    status: Optional[str] = Query(None, description="Statut de la retraite"),
    country: Optional[str] = Query(None, description="Pays de la retraite"),
    region: Optional[str] = Query(None, description="Région de la retraite"),
    activity: Optional[str] = Query(None, description="Activité proposée"),
    min_price: Optional[float] = Query(None, description="Prix minimum"),
    max_price: Optional[float] = Query(None, description="Prix maximum"),
    start_date: Optional[str] = Query(None, description="Date de début (format ISO)"),
    end_date: Optional[str] = Query(None, description="Date de fin (format ISO)"),
    limit: int = Query(100, description="Nombre maximum de résultats"),
    offset: int = Query(0, description="Décalage pour la pagination")
):
    """
    Récupère la liste des retraites avec filtrage optionnel.
    """
    try:
        # Appliquer les filtres
        if retreat_type:
            try:
                type_enum = RetreatType(retreat_type)
                retreats = retreat_repo.find_by_type(type_enum, limit, offset)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Type de retraite invalide: {retreat_type}"
                )
        elif status:
            try:
                status_enum = RetreatStatus(status)
                retreats = retreat_repo.find_by_status(status_enum, limit, offset)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Statut invalide: {status}"
                )
        elif country:
            retreats = retreat_repo.find_by_location(country, region, limit, offset)
        elif activity:
            retreats = retreat_repo.find_by_activity(activity, limit, offset)
        elif min_price is not None and max_price is not None:
            retreats = retreat_repo.find_by_price_range(min_price, max_price, limit, offset)
        elif start_date and end_date:
            try:
                start = datetime.fromisoformat(start_date)
                end = datetime.fromisoformat(end_date)
                retreats = retreat_repo.find_by_date_range(start, end, limit, offset)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Format de date invalide. Utilisez le format ISO (YYYY-MM-DDTHH:MM:SS)"
                )
        else:
            retreats = retreat_repo.get_all(limit, offset)
        
        return retreats
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des retraites: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la récupération des retraites"
        )

@router.get("/{retreat_id}", response_model=Retreat)
async def get_retreat(
    retreat_id: str = Path(..., description="ID de la retraite")
):
    """
    Récupère une retraite par son ID.
    """
    try:
        retreat = retreat_repo.get_by_id(retreat_id)
        if not retreat:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Retraite non trouvée: {retreat_id}"
            )
        
        return retreat
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la récupération de la retraite {retreat_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la récupération de la retraite {retreat_id}"
        )

@router.post("/", response_model=Retreat, status_code=status.HTTP_201_CREATED)
async def create_retreat(
    retreat: Retreat = Body(..., description="Retraite à créer")
):
    """
    Crée une nouvelle retraite.
    """
    try:
        # Créer la retraite
        created_retreat = retreat_repo.create(retreat)
        return created_retreat
    
    except Exception as e:
        logger.error(f"Erreur lors de la création de la retraite: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la création de la retraite"
        )

@router.put("/{retreat_id}", response_model=Retreat)
async def update_retreat(
    retreat_id: str = Path(..., description="ID de la retraite"),
    retreat: Retreat = Body(..., description="Retraite mise à jour")
):
    """
    Met à jour une retraite existante.
    """
    try:
        # Vérifier si la retraite existe
        existing_retreat = retreat_repo.get_by_id(retreat_id)
        if not existing_retreat:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Retraite non trouvée: {retreat_id}"
            )
        
        # Vérifier que l'ID correspond
        if retreat.id != retreat_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="L'ID de la retraite ne correspond pas à l'URL"
            )
        
        # Mettre à jour la retraite
        updated_retreat = retreat_repo.update(retreat)
        return updated_retreat
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour de la retraite {retreat_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la mise à jour de la retraite {retreat_id}"
        )

@router.delete("/{retreat_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_retreat(
    retreat_id: str = Path(..., description="ID de la retraite")
):
    """
    Supprime une retraite.
    """
    try:
        # Vérifier si la retraite existe
        existing_retreat = retreat_repo.get_by_id(retreat_id)
        if not existing_retreat:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Retraite non trouvée: {retreat_id}"
            )
        
        # Supprimer la retraite
        retreat_repo.delete(retreat_id)
        return None
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la suppression de la retraite {retreat_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la suppression de la retraite {retreat_id}"
        )

@router.get("/recommendations/{client_id}", response_model=List[Retreat])
async def get_retreat_recommendations(
    client_id: str = Path(..., description="ID du client"),
    limit: int = Query(10, description="Nombre maximum de résultats")
):
    """
    Récupère des recommandations de retraites pour un client.
    """
    try:
        from src.database.repositories import ClientRepository
        
        # Récupérer le client
        client_repo = ClientRepository()
        client = client_repo.get_by_id(client_id)
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Client non trouvé: {client_id}"
            )
        
        # Trouver des retraites adaptées
        retreats = retreat_repo.find_for_client(client.preferences.to_dict(), limit)
        
        return retreats
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la recherche de recommandations pour le client {client_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la recherche de recommandations pour le client {client_id}"
        )

@router.put("/{retreat_id}/status", response_model=Retreat)
async def update_retreat_status(
    retreat_id: str = Path(..., description="ID de la retraite"),
    status: str = Body(..., description="Nouveau statut")
):
    """
    Met à jour le statut d'une retraite.
    """
    try:
        # Vérifier si la retraite existe
        retreat = retreat_repo.get_by_id(retreat_id)
        if not retreat:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Retraite non trouvée: {retreat_id}"
            )
        
        # Vérifier que le statut est valide
        try:
            status_enum = RetreatStatus(status)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Statut invalide: {status}"
            )
        
        # Mettre à jour le statut
        retreat.status = status_enum
        updated_retreat = retreat_repo.update(retreat)
        
        return updated_retreat
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour du statut de la retraite {retreat_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la mise à jour du statut de la retraite {retreat_id}"
        )
