"""
Routes pour l'analyse de données.
"""

import logging
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, HTTPException, Depends, Query, Path, Body, status
from datetime import datetime

from src.auth.dependencies import get_current_active_user, get_current_admin_user
from src.auth.models import User
from src.integrations.analytics import GoogleAnalyticsService

# Configuration du logging
logger = logging.getLogger(__name__)

# C<PERSON>er le router
router = APIRouter()

# Créer le service d'analyse
analytics = GoogleAnalyticsService()

@router.post("/events", status_code=status.HTTP_200_OK)
async def track_event(
    event_name: str = Body(..., description="Nom de l'événement"),
    event_data: Dict[str, Any] = Body(..., description="Données de l'événement"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Enregistre un événement d'analyse.
    """
    try:
        # Enregistrer l'événement
        event = await analytics.track_event(
            event_name=event_name,
            event_data=event_data,
            user_id=current_user.id
        )
        
        return {
            "status": "success",
            "message": "Événement enregistré avec succès",
            "event": event
        }
    
    except Exception as e:
        logger.error(f"Erreur lors de l'enregistrement de l'événement: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de l'enregistrement de l'événement"
        )

@router.post("/page-views", status_code=status.HTTP_200_OK)
async def track_page_view(
    page_path: str = Body(..., description="Chemin de la page"),
    page_title: Optional[str] = Body(None, description="Titre de la page"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Enregistre une vue de page.
    """
    try:
        # Enregistrer la vue de page
        page_view = await analytics.track_page_view(
            page_path=page_path,
            page_title=page_title,
            user_id=current_user.id
        )
        
        return {
            "status": "success",
            "message": "Vue de page enregistrée avec succès",
            "page_view": page_view
        }
    
    except Exception as e:
        logger.error(f"Erreur lors de l'enregistrement de la vue de page: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de l'enregistrement de la vue de page"
        )

@router.post("/conversions", status_code=status.HTTP_200_OK)
async def track_conversion(
    conversion_name: str = Body(..., description="Nom de la conversion"),
    conversion_value: float = Body(..., description="Valeur de la conversion"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Enregistre une conversion.
    """
    try:
        # Enregistrer la conversion
        conversion = await analytics.track_conversion(
            conversion_name=conversion_name,
            conversion_value=conversion_value,
            user_id=current_user.id
        )
        
        return {
            "status": "success",
            "message": "Conversion enregistrée avec succès",
            "conversion": conversion
        }
    
    except Exception as e:
        logger.error(f"Erreur lors de l'enregistrement de la conversion: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de l'enregistrement de la conversion"
        )

@router.get("/events", status_code=status.HTTP_200_OK)
async def get_events(
    start_date: str = Query(..., description="Date de début (format ISO)"),
    end_date: str = Query(..., description="Date de fin (format ISO)"),
    event_name: Optional[str] = Query(None, description="Nom de l'événement"),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Récupère les événements dans une plage de dates.
    """
    try:
        # Convertir les dates
        start = datetime.fromisoformat(start_date)
        end = datetime.fromisoformat(end_date)
        
        # Récupérer les événements
        events = await analytics.get_events(
            start_date=start,
            end_date=end,
            event_name=event_name
        )
        
        return {
            "status": "success",
            "message": "Événements récupérés avec succès",
            "events": events
        }
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Format de date invalide: {str(e)}"
        )
    
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des événements: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la récupération des événements"
        )

@router.get("/page-views", status_code=status.HTTP_200_OK)
async def get_page_views(
    start_date: str = Query(..., description="Date de début (format ISO)"),
    end_date: str = Query(..., description="Date de fin (format ISO)"),
    page_path: Optional[str] = Query(None, description="Chemin de la page"),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Récupère les vues de page dans une plage de dates.
    """
    try:
        # Convertir les dates
        start = datetime.fromisoformat(start_date)
        end = datetime.fromisoformat(end_date)
        
        # Récupérer les vues de page
        page_views = await analytics.get_page_views(
            start_date=start,
            end_date=end,
            page_path=page_path
        )
        
        return {
            "status": "success",
            "message": "Vues de page récupérées avec succès",
            "page_views": page_views
        }
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Format de date invalide: {str(e)}"
        )
    
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des vues de page: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la récupération des vues de page"
        )

@router.get("/conversions", status_code=status.HTTP_200_OK)
async def get_conversions(
    start_date: str = Query(..., description="Date de début (format ISO)"),
    end_date: str = Query(..., description="Date de fin (format ISO)"),
    conversion_name: Optional[str] = Query(None, description="Nom de la conversion"),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Récupère les conversions dans une plage de dates.
    """
    try:
        # Convertir les dates
        start = datetime.fromisoformat(start_date)
        end = datetime.fromisoformat(end_date)
        
        # Récupérer les conversions
        conversions = await analytics.get_conversions(
            start_date=start,
            end_date=end,
            conversion_name=conversion_name
        )
        
        return {
            "status": "success",
            "message": "Conversions récupérées avec succès",
            "conversions": conversions
        }
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Format de date invalide: {str(e)}"
        )
    
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des conversions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la récupération des conversions"
        )
