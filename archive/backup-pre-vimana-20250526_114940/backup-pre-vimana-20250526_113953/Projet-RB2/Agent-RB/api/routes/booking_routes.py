"""
Routes pour les réservations.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, Path, Body, status

from src.models.booking import Booking, BookingStatus, PaymentStatus
from src.database.repositories import BookingRepository

# Configuration du logging
logger = logging.getLogger(__name__)

# C<PERSON>er le router
router = APIRouter()

# Créer le repository
booking_repo = BookingRepository()

@router.get("/", response_model=List[Booking])
async def get_bookings(
    client_id: Optional[str] = Query(None, description="ID du client"),
    retreat_id: Optional[str] = Query(None, description="ID de la retraite"),
    status: Optional[str] = Query(None, description="Statut de la réservation"),
    payment_status: Optional[str] = Query(None, description="Statut du paiement"),
    limit: int = Query(100, description="Nombre maximum de résultats"),
    offset: int = Query(0, description="Décalage pour la pagination")
):
    """
    Récupère la liste des réservations avec filtrage optionnel.
    """
    try:
        # Appliquer les filtres
        if client_id:
            bookings = booking_repo.find_by_client(client_id, limit, offset)
        elif retreat_id:
            bookings = booking_repo.find_by_retreat(retreat_id, limit, offset)
        elif status:
            try:
                status_enum = BookingStatus(status)
                bookings = booking_repo.find_by_status(status_enum, limit, offset)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Statut invalide: {status}"
                )
        elif payment_status:
            try:
                payment_status_enum = PaymentStatus(payment_status)
                bookings = booking_repo.find_by_payment_status(payment_status_enum, limit, offset)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Statut de paiement invalide: {payment_status}"
                )
        else:
            bookings = booking_repo.get_all(limit, offset)
        
        return bookings
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des réservations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la récupération des réservations"
        )

@router.get("/{booking_id}", response_model=Booking)
async def get_booking(
    booking_id: str = Path(..., description="ID de la réservation")
):
    """
    Récupère une réservation par son ID.
    """
    try:
        booking = booking_repo.get_by_id(booking_id)
        if not booking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Réservation non trouvée: {booking_id}"
            )
        
        return booking
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la récupération de la réservation {booking_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la récupération de la réservation {booking_id}"
        )

@router.post("/", response_model=Booking, status_code=status.HTTP_201_CREATED)
async def create_booking(
    booking: Booking = Body(..., description="Réservation à créer")
):
    """
    Crée une nouvelle réservation.
    """
    try:
        # Vérifier si le client existe
        from src.database.repositories import ClientRepository
        client_repo = ClientRepository()
        client = client_repo.get_by_id(booking.client_id)
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Client non trouvé: {booking.client_id}"
            )
        
        # Vérifier si la retraite existe
        from src.database.repositories import RetreatRepository
        retreat_repo = RetreatRepository()
        retreat = retreat_repo.get_by_id(booking.retreat_id)
        if not retreat:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Retraite non trouvée: {booking.retreat_id}"
            )
        
        # Vérifier si la retraite a des places disponibles
        if retreat.current_participants >= retreat.capacity:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"La retraite est complète"
            )
        
        # Créer la réservation
        created_booking = booking_repo.create(booking)
        
        # Mettre à jour le nombre de participants de la retraite
        retreat.current_participants += 1
        retreat_repo.update(retreat)
        
        # Mettre à jour les retraites à venir du client
        if booking.retreat_id not in client.upcoming_retreats:
            client.upcoming_retreats.append(booking.retreat_id)
            client_repo.update(client)
        
        return created_booking
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la création de la réservation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la création de la réservation"
        )

@router.put("/{booking_id}", response_model=Booking)
async def update_booking(
    booking_id: str = Path(..., description="ID de la réservation"),
    booking: Booking = Body(..., description="Réservation mise à jour")
):
    """
    Met à jour une réservation existante.
    """
    try:
        # Vérifier si la réservation existe
        existing_booking = booking_repo.get_by_id(booking_id)
        if not existing_booking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Réservation non trouvée: {booking_id}"
            )
        
        # Vérifier que l'ID correspond
        if booking.id != booking_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="L'ID de la réservation ne correspond pas à l'URL"
            )
        
        # Mettre à jour la réservation
        updated_booking = booking_repo.update(booking)
        return updated_booking
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour de la réservation {booking_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la mise à jour de la réservation {booking_id}"
        )

@router.put("/{booking_id}/status", response_model=Booking)
async def update_booking_status(
    booking_id: str = Path(..., description="ID de la réservation"),
    status: str = Body(..., description="Nouveau statut")
):
    """
    Met à jour le statut d'une réservation.
    """
    try:
        # Vérifier que le statut est valide
        try:
            status_enum = BookingStatus(status)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Statut invalide: {status}"
            )
        
        # Mettre à jour le statut
        updated_booking = booking_repo.update_status(booking_id, status_enum)
        if not updated_booking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Réservation non trouvée: {booking_id}"
            )
        
        return updated_booking
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour du statut de la réservation {booking_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la mise à jour du statut de la réservation {booking_id}"
        )

@router.put("/{booking_id}/payment-status", response_model=Booking)
async def update_payment_status(
    booking_id: str = Path(..., description="ID de la réservation"),
    payment_status: str = Body(..., description="Nouveau statut de paiement")
):
    """
    Met à jour le statut de paiement d'une réservation.
    """
    try:
        # Vérifier que le statut est valide
        try:
            payment_status_enum = PaymentStatus(payment_status)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Statut de paiement invalide: {payment_status}"
            )
        
        # Mettre à jour le statut de paiement
        updated_booking = booking_repo.update_payment_status(booking_id, payment_status_enum)
        if not updated_booking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Réservation non trouvée: {booking_id}"
            )
        
        return updated_booking
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour du statut de paiement de la réservation {booking_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la mise à jour du statut de paiement de la réservation {booking_id}"
        )

@router.post("/{booking_id}/payments", response_model=Booking)
async def add_payment(
    booking_id: str = Path(..., description="ID de la réservation"),
    payment: Dict[str, Any] = Body(..., description="Informations sur le paiement")
):
    """
    Ajoute un paiement à une réservation.
    """
    try:
        # Ajouter le paiement
        updated_booking = booking_repo.add_payment(booking_id, payment)
        if not updated_booking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Réservation non trouvée: {booking_id}"
            )
        
        return updated_booking
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de l'ajout d'un paiement à la réservation {booking_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de l'ajout d'un paiement à la réservation {booking_id}"
        )

@router.post("/{booking_id}/cancel", response_model=Booking)
async def cancel_booking(
    booking_id: str = Path(..., description="ID de la réservation"),
    reason: str = Body(..., description="Raison de l'annulation")
):
    """
    Annule une réservation.
    """
    try:
        # Annuler la réservation
        updated_booking = booking_repo.cancel_booking(booking_id, reason)
        if not updated_booking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Réservation non trouvée: {booking_id}"
            )
        
        # Mettre à jour le nombre de participants de la retraite
        from src.database.repositories import RetreatRepository
        retreat_repo = RetreatRepository()
        retreat = retreat_repo.get_by_id(updated_booking.retreat_id)
        if retreat and retreat.current_participants > 0:
            retreat.current_participants -= 1
            retreat_repo.update(retreat)
        
        # Mettre à jour les retraites à venir du client
        from src.database.repositories import ClientRepository
        client_repo = ClientRepository()
        client = client_repo.get_by_id(updated_booking.client_id)
        if client and updated_booking.retreat_id in client.upcoming_retreats:
            client.upcoming_retreats.remove(updated_booking.retreat_id)
            client_repo.update(client)
        
        return updated_booking
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de l'annulation de la réservation {booking_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de l'annulation de la réservation {booking_id}"
        )
