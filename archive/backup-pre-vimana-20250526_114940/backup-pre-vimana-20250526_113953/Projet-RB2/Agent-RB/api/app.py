"""
Application FastAPI pour l'API REST.
"""

import logging
from fastapi import FastAP<PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware

from src.database.connection import init_db
from .routes import (
    partner_router,
    retreat_router,
    client_router,
    booking_router,
    workflow_router
)

# Configuration du logging
logger = logging.getLogger(__name__)

def create_app() -> FastAPI:
    """
    Crée et configure l'application FastAPI.

    Returns:
        Application FastAPI configurée
    """
    # Initialiser la base de données
    init_db()

    # Créer l'application FastAPI
    app = FastAPI(
        title="API Retraites de Bien-être",
        description="API pour le système de retraites de bien-être",
        version="1.0.0"
    )

    # Configurer CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # À remplacer par les origines autorisées en production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Ajouter les routes
    app.include_router(auth_router, prefix="/api/auth", tags=["auth"])
    app.include_router(partner_router, prefix="/api/partners", tags=["partners"])
    app.include_router(retreat_router, prefix="/api/retreats", tags=["retreats"])
    app.include_router(client_router, prefix="/api/clients", tags=["clients"])
    app.include_router(booking_router, prefix="/api/bookings", tags=["bookings"])
    app.include_router(workflow_router, prefix="/api/workflows", tags=["workflows"])
    app.include_router(payment_router, prefix="/api/payments", tags=["payments"])
    app.include_router(integration_router, prefix="/api/integrations", tags=["integrations"])
    app.include_router(analytics_router, prefix="/api/analytics", tags=["analytics"])
    app.include_router(i18n_router, prefix="/api/i18n", tags=["i18n"])

    @app.get("/", tags=["root"])
    async def root():
        """
        Route racine de l'API.
        """
        return {
            "message": "Bienvenue sur l'API du système de retraites de bien-être",
            "version": "1.0.0",
            "documentation": "/docs"
        }

    @app.get("/health", tags=["health"])
    async def health_check():
        """
        Vérification de l'état de santé de l'API.
        """
        return {"status": "ok"}

    return app
