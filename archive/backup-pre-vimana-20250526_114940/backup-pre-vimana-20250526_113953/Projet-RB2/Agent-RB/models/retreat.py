"""
Modèle de données pour les retraites de bien-être.
"""

from enum import Enum
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

class RetreatType(str, Enum):
    """Types de retraites de bien-être."""
    YOGA = "yoga"
    MEDITATION = "meditation"
    WELLNESS = "wellness"
    FITNESS = "fitness"
    DETOX = "detox"
    SPIRITUAL = "spiritual"
    ADVENTURE = "adventure"
    CULTURAL = "cultural"
    NATURE = "nature"
    CREATIVE = "creative"
    CULINARY = "culinary"
    MIXED = "mixed"
    CUSTOM = "custom"

class RetreatStatus(str, Enum):
    """Statuts possibles pour une retraite."""
    DRAFT = "draft"               # Brouillon
    PENDING_APPROVAL = "pending"  # En attente d'approbation
    APPROVED = "approved"         # Approuvée
    PUBLISHED = "published"       # Publiée
    BOOKING = "booking"           # En cours de réservation
    CONFIRMED = "confirmed"       # Confirmée
    IN_PROGRESS = "in_progress"   # En cours
    COMPLETED = "completed"       # Terminée
    CANCELLED = "cancelled"       # Annulée

class RetreatActivity(str, Enum):
    """Activités proposées lors des retraites."""
    YOGA = "yoga"
    MEDITATION = "meditation"
    HIKING = "hiking"
    SWIMMING = "swimming"
    MASSAGE = "massage"
    COOKING = "cooking"
    WORKSHOP = "workshop"
    LECTURE = "lecture"
    DANCE = "dance"
    ART = "art"
    MUSIC = "music"
    THERAPY = "therapy"
    EXCURSION = "excursion"
    SILENCE = "silence"
    JOURNALING = "journaling"
    CEREMONY = "ceremony"
    RELAXATION = "relaxation"
    FITNESS = "fitness"
    OTHER = "other"

@dataclass
class RetreatScheduleItem:
    """Élément de programme d'une retraite."""
    day: int  # Jour de la retraite (1, 2, 3, etc.)
    start_time: str  # Format HH:MM
    end_time: str    # Format HH:MM
    activity: RetreatActivity
    title: str
    description: Optional[str] = None
    location: Optional[str] = None
    instructor_id: Optional[str] = None
    is_optional: bool = False

@dataclass
class RetreatPricing:
    """Informations de tarification pour une retraite."""
    currency: str = "EUR"
    base_price: float = 0.0
    early_bird_price: Optional[float] = None
    early_bird_deadline: Optional[datetime] = None
    deposit_amount: Optional[float] = None
    payment_deadline: Optional[datetime] = None
    includes: List[str] = field(default_factory=list)
    excludes: List[str] = field(default_factory=list)
    options: Dict[str, float] = field(default_factory=dict)

@dataclass
class RetreatAccommodation:
    """Informations sur l'hébergement d'une retraite."""
    name: str
    type: str  # hotel, villa, retreat_center, etc.
    description: Optional[str] = None
    amenities: List[str] = field(default_factory=list)
    room_types: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    address: Optional[Dict[str, Any]] = None
    location: Optional[Dict[str, float]] = None  # {latitude: float, longitude: float}
    photos: List[str] = field(default_factory=list)
    partner_id: Optional[str] = None

@dataclass
class Retreat:
    """
    Modèle de données pour une retraite de bien-être.
    """
    id: str
    title: str
    description: str
    retreat_type: RetreatType
    status: RetreatStatus = RetreatStatus.DRAFT
    start_date: datetime = field(default_factory=lambda: datetime.now() + timedelta(days=30))
    end_date: datetime = field(default_factory=lambda: datetime.now() + timedelta(days=37))
    location: Dict[str, Any] = field(default_factory=dict)
    activities: List[RetreatActivity] = field(default_factory=list)
    schedule: List[RetreatScheduleItem] = field(default_factory=list)
    pricing: RetreatPricing = field(default_factory=RetreatPricing)
    accommodation: Optional[RetreatAccommodation] = None
    capacity: int = 10
    min_participants: int = 4
    current_participants: int = 0
    language: str = "fr"
    additional_languages: List[str] = field(default_factory=list)
    level: str = "all"  # all, beginner, intermediate, advanced
    tags: List[str] = field(default_factory=list)
    included_services: List[str] = field(default_factory=list)
    excluded_services: List[str] = field(default_factory=list)
    requirements: List[str] = field(default_factory=list)
    photos: List[str] = field(default_factory=list)
    organizer_id: str = ""
    instructors: List[Dict[str, Any]] = field(default_factory=list)
    partners: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertir en dictionnaire."""
        return {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "retreat_type": self.retreat_type.value,
            "status": self.status.value,
            "start_date": self.start_date.isoformat(),
            "end_date": self.end_date.isoformat(),
            "location": self.location,
            "activities": [activity.value for activity in self.activities],
            "schedule": [
                {
                    "day": item.day,
                    "start_time": item.start_time,
                    "end_time": item.end_time,
                    "activity": item.activity.value,
                    "title": item.title,
                    "description": item.description,
                    "location": item.location,
                    "instructor_id": item.instructor_id,
                    "is_optional": item.is_optional
                }
                for item in self.schedule
            ],
            "pricing": {
                "currency": self.pricing.currency,
                "base_price": self.pricing.base_price,
                "early_bird_price": self.pricing.early_bird_price,
                "early_bird_deadline": self.pricing.early_bird_deadline.isoformat() if self.pricing.early_bird_deadline else None,
                "deposit_amount": self.pricing.deposit_amount,
                "payment_deadline": self.pricing.payment_deadline.isoformat() if self.pricing.payment_deadline else None,
                "includes": self.pricing.includes,
                "excludes": self.pricing.excludes,
                "options": self.pricing.options
            },
            "accommodation": {
                "name": self.accommodation.name,
                "type": self.accommodation.type,
                "description": self.accommodation.description,
                "amenities": self.accommodation.amenities,
                "room_types": self.accommodation.room_types,
                "address": self.accommodation.address,
                "location": self.accommodation.location,
                "photos": self.accommodation.photos,
                "partner_id": self.accommodation.partner_id
            } if self.accommodation else None,
            "capacity": self.capacity,
            "min_participants": self.min_participants,
            "current_participants": self.current_participants,
            "language": self.language,
            "additional_languages": self.additional_languages,
            "level": self.level,
            "tags": self.tags,
            "included_services": self.included_services,
            "excluded_services": self.excluded_services,
            "requirements": self.requirements,
            "photos": self.photos,
            "organizer_id": self.organizer_id,
            "instructors": self.instructors,
            "partners": self.partners,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Retreat':
        """Créer à partir d'un dictionnaire."""
        # Convertir les types énumérés
        retreat_type = RetreatType(data.get("retreat_type", RetreatType.YOGA.value))
        status = RetreatStatus(data.get("status", RetreatStatus.DRAFT.value))
        activities = [RetreatActivity(a) for a in data.get("activities", [])]
        
        # Convertir les dates
        start_date = datetime.fromisoformat(data.get("start_date")) if data.get("start_date") else (datetime.now() + timedelta(days=30))
        end_date = datetime.fromisoformat(data.get("end_date")) if data.get("end_date") else (datetime.now() + timedelta(days=37))
        created_at = datetime.fromisoformat(data.get("created_at")) if data.get("created_at") else datetime.now()
        updated_at = datetime.fromisoformat(data.get("updated_at")) if data.get("updated_at") else datetime.now()
        
        # Convertir le programme
        schedule = []
        for item_data in data.get("schedule", []):
            schedule.append(RetreatScheduleItem(
                day=item_data.get("day", 1),
                start_time=item_data.get("start_time", "09:00"),
                end_time=item_data.get("end_time", "10:00"),
                activity=RetreatActivity(item_data.get("activity", RetreatActivity.YOGA.value)),
                title=item_data.get("title", ""),
                description=item_data.get("description"),
                location=item_data.get("location"),
                instructor_id=item_data.get("instructor_id"),
                is_optional=item_data.get("is_optional", False)
            ))
        
        # Convertir la tarification
        pricing_data = data.get("pricing", {})
        pricing = RetreatPricing(
            currency=pricing_data.get("currency", "EUR"),
            base_price=pricing_data.get("base_price", 0.0),
            early_bird_price=pricing_data.get("early_bird_price"),
            early_bird_deadline=datetime.fromisoformat(pricing_data.get("early_bird_deadline")) if pricing_data.get("early_bird_deadline") else None,
            deposit_amount=pricing_data.get("deposit_amount"),
            payment_deadline=datetime.fromisoformat(pricing_data.get("payment_deadline")) if pricing_data.get("payment_deadline") else None,
            includes=pricing_data.get("includes", []),
            excludes=pricing_data.get("excludes", []),
            options=pricing_data.get("options", {})
        )
        
        # Convertir l'hébergement
        accommodation = None
        accommodation_data = data.get("accommodation")
        if accommodation_data:
            accommodation = RetreatAccommodation(
                name=accommodation_data.get("name", ""),
                type=accommodation_data.get("type", ""),
                description=accommodation_data.get("description"),
                amenities=accommodation_data.get("amenities", []),
                room_types=accommodation_data.get("room_types", {}),
                address=accommodation_data.get("address"),
                location=accommodation_data.get("location"),
                photos=accommodation_data.get("photos", []),
                partner_id=accommodation_data.get("partner_id")
            )
        
        return cls(
            id=data.get("id", ""),
            title=data.get("title", ""),
            description=data.get("description", ""),
            retreat_type=retreat_type,
            status=status,
            start_date=start_date,
            end_date=end_date,
            location=data.get("location", {}),
            activities=activities,
            schedule=schedule,
            pricing=pricing,
            accommodation=accommodation,
            capacity=data.get("capacity", 10),
            min_participants=data.get("min_participants", 4),
            current_participants=data.get("current_participants", 0),
            language=data.get("language", "fr"),
            additional_languages=data.get("additional_languages", []),
            level=data.get("level", "all"),
            tags=data.get("tags", []),
            included_services=data.get("included_services", []),
            excluded_services=data.get("excluded_services", []),
            requirements=data.get("requirements", []),
            photos=data.get("photos", []),
            organizer_id=data.get("organizer_id", ""),
            instructors=data.get("instructors", []),
            partners=data.get("partners", []),
            created_at=created_at,
            updated_at=updated_at
        )
