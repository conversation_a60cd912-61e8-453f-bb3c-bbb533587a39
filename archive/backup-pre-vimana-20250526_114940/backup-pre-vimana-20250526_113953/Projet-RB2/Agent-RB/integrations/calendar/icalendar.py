"""
Service de génération de fichiers iCalendar.
"""

import os
import logging
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime

from icalendar import Calendar, Event, vCalAddress, vText

from .calendar_service import CalendarService

# Configuration du logging
logger = logging.getLogger(__name__)

class ICalendarService(CalendarService):
    """
    Service de génération de fichiers iCalendar.
    """
    
    def __init__(self, output_dir: Optional[str] = None):
        """
        Initialise le service iCalendar.
        
        Args:
            output_dir: Répertoire de sortie pour les fichiers iCalendar (optionnel)
        """
        self.output_dir = output_dir or os.environ.get("ICAL_OUTPUT_DIR", "ical")
        
        # Créer le répertoire de sortie s'il n'existe pas
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Stocker les événements en mémoire
        self.events = {}
    
    async def create_event(self, 
                         title: str, 
                         start_time: datetime, 
                         end_time: datetime, 
                         description: Optional[str] = None,
                         location: Optional[str] = None,
                         attendees: Optional[List[str]] = None,
                         calendar_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Crée un événement iCalendar.
        
        Args:
            title: Titre de l'événement
            start_time: Date et heure de début
            end_time: Date et heure de fin
            description: Description de l'événement (optionnel)
            location: Lieu de l'événement (optionnel)
            attendees: Liste des adresses email des participants (optionnel)
            calendar_id: ID du calendrier (optionnel, ignoré pour iCalendar)
            
        Returns:
            Informations sur l'événement créé
        """
        try:
            # Générer un ID unique pour l'événement
            event_id = f"event_{uuid.uuid4().hex[:8]}"
            
            # Créer l'événement
            event_data = {
                "id": event_id,
                "title": title,
                "start_time": start_time,
                "end_time": end_time,
                "description": description,
                "location": location,
                "attendees": attendees,
                "created_at": datetime.now()
            }
            
            # Stocker l'événement en mémoire
            self.events[event_id] = event_data
            
            # Générer le fichier iCalendar
            ical_content = self._generate_ical(event_data)
            
            # Sauvegarder le fichier
            file_path = os.path.join(self.output_dir, f"{event_id}.ics")
            with open(file_path, 'w') as f:
                f.write(ical_content)
            
            # Formater la réponse
            return {
                "id": event_id,
                "title": title,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "description": description,
                "location": location,
                "attendees": attendees,
                "file_path": file_path
            }
        
        except Exception as e:
            logger.error(f"Erreur lors de la création de l'événement iCalendar: {str(e)}")
            raise
    
    async def update_event(self, 
                         event_id: str, 
                         title: Optional[str] = None,
                         start_time: Optional[datetime] = None, 
                         end_time: Optional[datetime] = None, 
                         description: Optional[str] = None,
                         location: Optional[str] = None,
                         attendees: Optional[List[str]] = None,
                         calendar_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Met à jour un événement iCalendar.
        
        Args:
            event_id: ID de l'événement
            title: Nouveau titre de l'événement (optionnel)
            start_time: Nouvelle date et heure de début (optionnel)
            end_time: Nouvelle date et heure de fin (optionnel)
            description: Nouvelle description de l'événement (optionnel)
            location: Nouveau lieu de l'événement (optionnel)
            attendees: Nouvelle liste des adresses email des participants (optionnel)
            calendar_id: ID du calendrier (optionnel, ignoré pour iCalendar)
            
        Returns:
            Informations sur l'événement mis à jour
        """
        try:
            # Vérifier que l'événement existe
            if event_id not in self.events:
                raise ValueError(f"Événement non trouvé: {event_id}")
            
            # Récupérer l'événement
            event_data = self.events[event_id]
            
            # Mettre à jour les champs
            if title is not None:
                event_data["title"] = title
            
            if start_time is not None:
                event_data["start_time"] = start_time
            
            if end_time is not None:
                event_data["end_time"] = end_time
            
            if description is not None:
                event_data["description"] = description
            
            if location is not None:
                event_data["location"] = location
            
            if attendees is not None:
                event_data["attendees"] = attendees
            
            # Mettre à jour la date de modification
            event_data["updated_at"] = datetime.now()
            
            # Générer le fichier iCalendar
            ical_content = self._generate_ical(event_data)
            
            # Sauvegarder le fichier
            file_path = os.path.join(self.output_dir, f"{event_id}.ics")
            with open(file_path, 'w') as f:
                f.write(ical_content)
            
            # Formater la réponse
            return {
                "id": event_id,
                "title": event_data["title"],
                "start_time": event_data["start_time"].isoformat(),
                "end_time": event_data["end_time"].isoformat(),
                "description": event_data.get("description"),
                "location": event_data.get("location"),
                "attendees": event_data.get("attendees"),
                "file_path": file_path
            }
        
        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour de l'événement iCalendar: {str(e)}")
            raise
    
    async def delete_event(self, 
                         event_id: str, 
                         calendar_id: Optional[str] = None) -> bool:
        """
        Supprime un événement iCalendar.
        
        Args:
            event_id: ID de l'événement
            calendar_id: ID du calendrier (optionnel, ignoré pour iCalendar)
            
        Returns:
            True si l'événement a été supprimé, False sinon
        """
        try:
            # Vérifier que l'événement existe
            if event_id not in self.events:
                return False
            
            # Supprimer l'événement de la mémoire
            del self.events[event_id]
            
            # Supprimer le fichier
            file_path = os.path.join(self.output_dir, f"{event_id}.ics")
            if os.path.exists(file_path):
                os.remove(file_path)
            
            return True
        
        except Exception as e:
            logger.error(f"Erreur lors de la suppression de l'événement iCalendar: {str(e)}")
            return False
    
    async def get_event(self, 
                      event_id: str, 
                      calendar_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Récupère les informations d'un événement iCalendar.
        
        Args:
            event_id: ID de l'événement
            calendar_id: ID du calendrier (optionnel, ignoré pour iCalendar)
            
        Returns:
            Informations sur l'événement
        """
        try:
            # Vérifier que l'événement existe
            if event_id not in self.events:
                raise ValueError(f"Événement non trouvé: {event_id}")
            
            # Récupérer l'événement
            event_data = self.events[event_id]
            
            # Formater la réponse
            return {
                "id": event_id,
                "title": event_data["title"],
                "start_time": event_data["start_time"].isoformat(),
                "end_time": event_data["end_time"].isoformat(),
                "description": event_data.get("description"),
                "location": event_data.get("location"),
                "attendees": event_data.get("attendees"),
                "file_path": os.path.join(self.output_dir, f"{event_id}.ics")
            }
        
        except Exception as e:
            logger.error(f"Erreur lors de la récupération de l'événement iCalendar: {str(e)}")
            raise
    
    async def list_events(self, 
                        start_time: Optional[datetime] = None, 
                        end_time: Optional[datetime] = None,
                        calendar_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Liste les événements iCalendar dans une plage de dates.
        
        Args:
            start_time: Date et heure de début (optionnel)
            end_time: Date et heure de fin (optionnel)
            calendar_id: ID du calendrier (optionnel, ignoré pour iCalendar)
            
        Returns:
            Liste des événements
        """
        try:
            # Filtrer les événements par date
            filtered_events = []
            
            for event_id, event_data in self.events.items():
                # Vérifier si l'événement est dans la plage de dates
                if start_time and event_data["end_time"] < start_time:
                    continue
                
                if end_time and event_data["start_time"] > end_time:
                    continue
                
                # Ajouter l'événement à la liste
                filtered_events.append({
                    "id": event_id,
                    "title": event_data["title"],
                    "start_time": event_data["start_time"].isoformat(),
                    "end_time": event_data["end_time"].isoformat(),
                    "description": event_data.get("description"),
                    "location": event_data.get("location"),
                    "attendees": event_data.get("attendees"),
                    "file_path": os.path.join(self.output_dir, f"{event_id}.ics")
                })
            
            return filtered_events
        
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des événements iCalendar: {str(e)}")
            raise
    
    async def generate_ical(self, 
                          event_id: str, 
                          calendar_id: Optional[str] = None) -> str:
        """
        Génère un fichier iCalendar pour un événement.
        
        Args:
            event_id: ID de l'événement
            calendar_id: ID du calendrier (optionnel, ignoré pour iCalendar)
            
        Returns:
            Contenu du fichier iCalendar
        """
        try:
            # Vérifier que l'événement existe
            if event_id not in self.events:
                raise ValueError(f"Événement non trouvé: {event_id}")
            
            # Récupérer l'événement
            event_data = self.events[event_id]
            
            # Générer le fichier iCalendar
            return self._generate_ical(event_data)
        
        except Exception as e:
            logger.error(f"Erreur lors de la génération du fichier iCalendar: {str(e)}")
            raise
    
    def _generate_ical(self, event_data: Dict[str, Any]) -> str:
        """
        Génère un fichier iCalendar à partir des données d'un événement.
        
        Args:
            event_data: Données de l'événement
            
        Returns:
            Contenu du fichier iCalendar
        """
        # Créer le calendrier
        cal = Calendar()
        cal.add('prodid', '-//Retreat & Be//Calendar//FR')
        cal.add('version', '2.0')
        
        # Créer l'événement
        event = Event()
        event.add('summary', event_data["title"])
        event.add('dtstart', event_data["start_time"])
        event.add('dtend', event_data["end_time"])
        event.add('dtstamp', datetime.now())
        event.add('uid', f"{event_data['id']}@retreatandbe.com")
        
        # Ajouter les champs optionnels
        if event_data.get("description"):
            event.add('description', event_data["description"])
        
        if event_data.get("location"):
            event.add('location', event_data["location"])
        
        # Ajouter les participants
        if event_data.get("attendees"):
            for attendee_email in event_data["attendees"]:
                attendee = vCalAddress(f'MAILTO:{attendee_email}')
                attendee.params['cn'] = vText(attendee_email)
                attendee.params['ROLE'] = vText('REQ-PARTICIPANT')
                event.add('attendee', attendee, encode=0)
        
        # Ajouter l'événement au calendrier
        cal.add_component(event)
        
        # Retourner le contenu du fichier iCalendar
        return cal.to_ical().decode('utf-8')
