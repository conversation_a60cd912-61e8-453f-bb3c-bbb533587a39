"""
Interface pour les services de calendrier.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from datetime import datetime

class CalendarService(ABC):
    """
    Interface pour les services de calendrier.
    """
    
    @abstractmethod
    async def create_event(self, 
                         title: str, 
                         start_time: datetime, 
                         end_time: datetime, 
                         description: Optional[str] = None,
                         location: Optional[str] = None,
                         attendees: Optional[List[str]] = None,
                         calendar_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Crée un événement dans le calendrier.
        
        Args:
            title: Titre de l'événement
            start_time: Date et heure de début
            end_time: Date et heure de fin
            description: Description de l'événement (optionnel)
            location: Lieu de l'événement (optionnel)
            attendees: Liste des adresses email des participants (optionnel)
            calendar_id: ID du calendrier (optionnel, utilise le calendrier par défaut si non spécifié)
            
        Returns:
            Informations sur l'événement créé
        """
        pass
    
    @abstractmethod
    async def update_event(self, 
                         event_id: str, 
                         title: Optional[str] = None,
                         start_time: Optional[datetime] = None, 
                         end_time: Optional[datetime] = None, 
                         description: Optional[str] = None,
                         location: Optional[str] = None,
                         attendees: Optional[List[str]] = None,
                         calendar_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Met à jour un événement dans le calendrier.
        
        Args:
            event_id: ID de l'événement
            title: Nouveau titre de l'événement (optionnel)
            start_time: Nouvelle date et heure de début (optionnel)
            end_time: Nouvelle date et heure de fin (optionnel)
            description: Nouvelle description de l'événement (optionnel)
            location: Nouveau lieu de l'événement (optionnel)
            attendees: Nouvelle liste des adresses email des participants (optionnel)
            calendar_id: ID du calendrier (optionnel, utilise le calendrier par défaut si non spécifié)
            
        Returns:
            Informations sur l'événement mis à jour
        """
        pass
    
    @abstractmethod
    async def delete_event(self, 
                         event_id: str, 
                         calendar_id: Optional[str] = None) -> bool:
        """
        Supprime un événement du calendrier.
        
        Args:
            event_id: ID de l'événement
            calendar_id: ID du calendrier (optionnel, utilise le calendrier par défaut si non spécifié)
            
        Returns:
            True si l'événement a été supprimé, False sinon
        """
        pass
    
    @abstractmethod
    async def get_event(self, 
                      event_id: str, 
                      calendar_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Récupère les informations d'un événement.
        
        Args:
            event_id: ID de l'événement
            calendar_id: ID du calendrier (optionnel, utilise le calendrier par défaut si non spécifié)
            
        Returns:
            Informations sur l'événement
        """
        pass
    
    @abstractmethod
    async def list_events(self, 
                        start_time: Optional[datetime] = None, 
                        end_time: Optional[datetime] = None,
                        calendar_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Liste les événements dans une plage de dates.
        
        Args:
            start_time: Date et heure de début (optionnel)
            end_time: Date et heure de fin (optionnel)
            calendar_id: ID du calendrier (optionnel, utilise le calendrier par défaut si non spécifié)
            
        Returns:
            Liste des événements
        """
        pass
    
    @abstractmethod
    async def generate_ical(self, 
                          event_id: str, 
                          calendar_id: Optional[str] = None) -> str:
        """
        Génère un fichier iCalendar pour un événement.
        
        Args:
            event_id: ID de l'événement
            calendar_id: ID du calendrier (optionnel, utilise le calendrier par défaut si non spécifié)
            
        Returns:
            Contenu du fichier iCalendar
        """
        pass
