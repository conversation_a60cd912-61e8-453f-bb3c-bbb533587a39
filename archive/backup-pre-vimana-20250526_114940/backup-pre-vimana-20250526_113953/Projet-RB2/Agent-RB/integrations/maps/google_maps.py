"""
Intégration avec Google Maps.
"""

import os
import logging
import urllib.parse
from typing import Dict, Any, List, Optional, Tuple

import googlemaps
from googlemaps.exceptions import ApiError

from .maps_service import MapsService

# Configuration du logging
logger = logging.getLogger(__name__)

class GoogleMapsService(MapsService):
    """
    Service d'intégration avec Google Maps.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialise le service Google Maps.
        
        Args:
            api_key: Clé API Google Maps (optionnel)
        """
        self.api_key = api_key or os.environ.get("GOOGLE_MAPS_API_KEY")
        self.client = None
        
        # En mode développement, ne pas effectuer d'appels réels à l'API
        self.dev_mode = os.environ.get("ENVIRONMENT", "development") == "development"
        
        if not self.dev_mode and self.api_key:
            self.client = googlemaps.Client(key=self.api_key)
    
    async def geocode(self, address: str) -> Dict[str, Any]:
        """
        Convertit une adresse en coordonnées géographiques avec Google Maps.
        
        Args:
            address: Adresse à géocoder
            
        Returns:
            Informations sur la localisation
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Géocodage d'une adresse simulé: {address}")
            
            # Simuler des coordonnées pour quelques adresses connues
            if "paris" in address.lower():
                return {
                    "address": address,
                    "latitude": 48.8566,
                    "longitude": 2.3522,
                    "country": "France",
                    "country_code": "FR",
                    "city": "Paris",
                    "postal_code": "75000",
                    "formatted_address": "Paris, France"
                }
            elif "provence" in address.lower():
                return {
                    "address": address,
                    "latitude": 43.5298,
                    "longitude": 5.4474,
                    "country": "France",
                    "country_code": "FR",
                    "city": "Aix-en-Provence",
                    "postal_code": "13100",
                    "formatted_address": "Aix-en-Provence, France"
                }
            else:
                return {
                    "address": address,
                    "latitude": 45.0,
                    "longitude": 2.0,
                    "country": "France",
                    "country_code": "FR",
                    "city": "Ville simulée",
                    "postal_code": "00000",
                    "formatted_address": "Ville simulée, France"
                }
        
        try:
            # Vérifier que le client est initialisé
            if not self.client:
                raise ValueError("Client Google Maps non initialisé")
            
            # Géocoder l'adresse
            results = self.client.geocode(address)
            
            if not results:
                raise ValueError(f"Aucun résultat trouvé pour l'adresse: {address}")
            
            # Récupérer le premier résultat
            result = results[0]
            
            # Extraire les informations
            location = result['geometry']['location']
            
            # Extraire les composants de l'adresse
            address_components = {}
            for component in result['address_components']:
                for component_type in component['types']:
                    address_components[component_type] = component['long_name']
                    if component_type == 'country':
                        address_components['country_code'] = component['short_name']
            
            # Formater la réponse
            return {
                "address": address,
                "latitude": location['lat'],
                "longitude": location['lng'],
                "country": address_components.get('country'),
                "country_code": address_components.get('country_code'),
                "city": address_components.get('locality'),
                "postal_code": address_components.get('postal_code'),
                "formatted_address": result['formatted_address']
            }
        
        except Exception as e:
            logger.error(f"Erreur lors du géocodage de l'adresse: {str(e)}")
            raise
    
    async def reverse_geocode(self, latitude: float, longitude: float) -> Dict[str, Any]:
        """
        Convertit des coordonnées géographiques en adresse avec Google Maps.
        
        Args:
            latitude: Latitude
            longitude: Longitude
            
        Returns:
            Informations sur l'adresse
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Géocodage inverse simulé: {latitude}, {longitude}")
            
            # Simuler une adresse pour quelques coordonnées connues
            if abs(latitude - 48.8566) < 0.1 and abs(longitude - 2.3522) < 0.1:
                return {
                    "latitude": latitude,
                    "longitude": longitude,
                    "country": "France",
                    "country_code": "FR",
                    "city": "Paris",
                    "postal_code": "75000",
                    "formatted_address": "Paris, France"
                }
            elif abs(latitude - 43.5298) < 0.1 and abs(longitude - 5.4474) < 0.1:
                return {
                    "latitude": latitude,
                    "longitude": longitude,
                    "country": "France",
                    "country_code": "FR",
                    "city": "Aix-en-Provence",
                    "postal_code": "13100",
                    "formatted_address": "Aix-en-Provence, France"
                }
            else:
                return {
                    "latitude": latitude,
                    "longitude": longitude,
                    "country": "France",
                    "country_code": "FR",
                    "city": "Ville simulée",
                    "postal_code": "00000",
                    "formatted_address": "Ville simulée, France"
                }
        
        try:
            # Vérifier que le client est initialisé
            if not self.client:
                raise ValueError("Client Google Maps non initialisé")
            
            # Géocoder les coordonnées
            results = self.client.reverse_geocode((latitude, longitude))
            
            if not results:
                raise ValueError(f"Aucun résultat trouvé pour les coordonnées: {latitude}, {longitude}")
            
            # Récupérer le premier résultat
            result = results[0]
            
            # Extraire les composants de l'adresse
            address_components = {}
            for component in result['address_components']:
                for component_type in component['types']:
                    address_components[component_type] = component['long_name']
                    if component_type == 'country':
                        address_components['country_code'] = component['short_name']
            
            # Formater la réponse
            return {
                "latitude": latitude,
                "longitude": longitude,
                "country": address_components.get('country'),
                "country_code": address_components.get('country_code'),
                "city": address_components.get('locality'),
                "postal_code": address_components.get('postal_code'),
                "formatted_address": result['formatted_address']
            }
        
        except Exception as e:
            logger.error(f"Erreur lors du géocodage inverse: {str(e)}")
            raise
    
    async def get_distance(self, 
                         origin: str, 
                         destination: str, 
                         mode: Optional[str] = None) -> Dict[str, Any]:
        """
        Calcule la distance et le temps de trajet entre deux adresses avec Google Maps.
        
        Args:
            origin: Adresse d'origine
            destination: Adresse de destination
            mode: Mode de transport (optionnel)
            
        Returns:
            Informations sur la distance et le temps de trajet
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Calcul de distance simulé: {origin} -> {destination}")
            
            # Simuler une distance pour quelques adresses connues
            if "paris" in origin.lower() and "provence" in destination.lower():
                return {
                    "origin": origin,
                    "destination": destination,
                    "distance": {
                        "value": 750000,
                        "text": "750 km"
                    },
                    "duration": {
                        "value": 25200,
                        "text": "7 heures"
                    },
                    "mode": mode or "driving"
                }
            else:
                return {
                    "origin": origin,
                    "destination": destination,
                    "distance": {
                        "value": 100000,
                        "text": "100 km"
                    },
                    "duration": {
                        "value": 3600,
                        "text": "1 heure"
                    },
                    "mode": mode or "driving"
                }
        
        try:
            # Vérifier que le client est initialisé
            if not self.client:
                raise ValueError("Client Google Maps non initialisé")
            
            # Préparer les paramètres
            params = {
                "origins": [origin],
                "destinations": [destination]
            }
            
            if mode:
                params["mode"] = mode
            
            # Calculer la distance
            result = self.client.distance_matrix(**params)
            
            if result['status'] != 'OK':
                raise ValueError(f"Erreur lors du calcul de la distance: {result['status']}")
            
            # Récupérer le premier résultat
            row = result['rows'][0]
            element = row['elements'][0]
            
            if element['status'] != 'OK':
                raise ValueError(f"Erreur lors du calcul de la distance: {element['status']}")
            
            # Formater la réponse
            return {
                "origin": origin,
                "destination": destination,
                "distance": element['distance'],
                "duration": element['duration'],
                "mode": mode or "driving"
            }
        
        except Exception as e:
            logger.error(f"Erreur lors du calcul de la distance: {str(e)}")
            raise
    
    async def get_places_nearby(self, 
                              location: str, 
                              radius: int = 1000, 
                              type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Recherche des lieux à proximité d'une adresse avec Google Maps.
        
        Args:
            location: Adresse ou coordonnées
            radius: Rayon de recherche en mètres
            type: Type de lieu (optionnel)
            
        Returns:
            Liste des lieux à proximité
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Recherche de lieux à proximité simulée: {location}")
            
            # Simuler des lieux pour quelques adresses connues
            if "paris" in location.lower():
                return [
                    {
                        "name": "Tour Eiffel",
                        "address": "Champ de Mars, 5 Avenue Anatole France, 75007 Paris, France",
                        "latitude": 48.8584,
                        "longitude": 2.2945,
                        "rating": 4.6,
                        "types": ["tourist_attraction", "point_of_interest"]
                    },
                    {
                        "name": "Musée du Louvre",
                        "address": "Rue de Rivoli, 75001 Paris, France",
                        "latitude": 48.8606,
                        "longitude": 2.3376,
                        "rating": 4.7,
                        "types": ["museum", "point_of_interest"]
                    }
                ]
            elif "provence" in location.lower():
                return [
                    {
                        "name": "Cours Mirabeau",
                        "address": "Cours Mirabeau, 13100 Aix-en-Provence, France",
                        "latitude": 43.5260,
                        "longitude": 5.4474,
                        "rating": 4.5,
                        "types": ["point_of_interest", "establishment"]
                    },
                    {
                        "name": "Cathédrale Saint-Sauveur",
                        "address": "34 Place des Martyrs de la Résistance, 13100 Aix-en-Provence, France",
                        "latitude": 43.5316,
                        "longitude": 5.4474,
                        "rating": 4.4,
                        "types": ["church", "point_of_interest"]
                    }
                ]
            else:
                return [
                    {
                        "name": "Lieu simulé 1",
                        "address": "Adresse simulée 1",
                        "latitude": 45.0,
                        "longitude": 2.0,
                        "rating": 4.0,
                        "types": ["point_of_interest"]
                    },
                    {
                        "name": "Lieu simulé 2",
                        "address": "Adresse simulée 2",
                        "latitude": 45.01,
                        "longitude": 2.01,
                        "rating": 4.1,
                        "types": ["point_of_interest"]
                    }
                ]
        
        try:
            # Vérifier que le client est initialisé
            if not self.client:
                raise ValueError("Client Google Maps non initialisé")
            
            # Convertir l'adresse en coordonnées si nécessaire
            if isinstance(location, str) and not ',' in location:
                geocode_result = await self.geocode(location)
                location = (geocode_result['latitude'], geocode_result['longitude'])
            
            # Préparer les paramètres
            params = {
                "location": location,
                "radius": radius
            }
            
            if type:
                params["type"] = type
            
            # Rechercher les lieux à proximité
            results = self.client.places_nearby(**params)
            
            if results['status'] != 'OK':
                raise ValueError(f"Erreur lors de la recherche de lieux à proximité: {results['status']}")
            
            # Formater la réponse
            places = []
            for result in results['results']:
                place = {
                    "name": result['name'],
                    "address": result.get('vicinity'),
                    "latitude": result['geometry']['location']['lat'],
                    "longitude": result['geometry']['location']['lng'],
                    "types": result.get('types', [])
                }
                
                if 'rating' in result:
                    place['rating'] = result['rating']
                
                places.append(place)
            
            return places
        
        except Exception as e:
            logger.error(f"Erreur lors de la recherche de lieux à proximité: {str(e)}")
            raise
    
    def generate_static_map_url(self, 
                              center: str, 
                              zoom: int = 14, 
                              size: Tuple[int, int] = (600, 400),
                              markers: Optional[List[Dict[str, Any]]] = None) -> str:
        """
        Génère une URL pour une carte statique Google Maps.
        
        Args:
            center: Centre de la carte (adresse ou coordonnées)
            zoom: Niveau de zoom
            size: Taille de la carte en pixels
            markers: Liste des marqueurs à afficher sur la carte (optionnel)
            
        Returns:
            URL de la carte statique
        """
        # Construire l'URL de base
        url = "https://maps.googleapis.com/maps/api/staticmap?"
        
        # Ajouter les paramètres
        params = {
            "center": center,
            "zoom": zoom,
            "size": f"{size[0]}x{size[1]}",
            "key": self.api_key
        }
        
        # Ajouter les marqueurs
        if markers:
            for i, marker in enumerate(markers):
                marker_str = ""
                
                if "color" in marker:
                    marker_str += f"color:{marker['color']}|"
                
                if "label" in marker:
                    marker_str += f"label:{marker['label']}|"
                
                if "location" in marker:
                    marker_str += marker["location"]
                
                params[f"markers{i}"] = marker_str
        
        # Construire l'URL
        url += urllib.parse.urlencode(params)
        
        return url
    
    def generate_directions_url(self, 
                              origin: str, 
                              destination: str, 
                              mode: Optional[str] = None) -> str:
        """
        Génère une URL pour un itinéraire Google Maps.
        
        Args:
            origin: Adresse d'origine
            destination: Adresse de destination
            mode: Mode de transport (optionnel)
            
        Returns:
            URL de l'itinéraire
        """
        # Construire l'URL de base
        url = "https://www.google.com/maps/dir/?api=1"
        
        # Ajouter les paramètres
        params = {
            "origin": origin,
            "destination": destination
        }
        
        if mode:
            params["travelmode"] = mode
        
        # Construire l'URL
        url += "&" + urllib.parse.urlencode(params)
        
        return url
