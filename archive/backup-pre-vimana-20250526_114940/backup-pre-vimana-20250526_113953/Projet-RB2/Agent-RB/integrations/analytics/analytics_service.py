"""
Interface pour les services d'analyse de données.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from datetime import datetime

class AnalyticsService(ABC):
    """
    Interface pour les services d'analyse de données.
    """
    
    @abstractmethod
    async def track_event(self, 
                        event_name: str, 
                        event_data: Dict[str, Any],
                        user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Enregistre un événement.
        
        Args:
            event_name: Nom de l'événement
            event_data: Données de l'événement
            user_id: ID de l'utilisateur (optionnel)
            
        Returns:
            Informations sur l'événement enregistré
        """
        pass
    
    @abstractmethod
    async def track_page_view(self, 
                            page_path: str, 
                            page_title: Optional[str] = None,
                            user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Enregistre une vue de page.
        
        Args:
            page_path: Chemin de la page
            page_title: Titre de la page (optionnel)
            user_id: ID de l'utilisateur (optionnel)
            
        Returns:
            Informations sur la vue de page enregistrée
        """
        pass
    
    @abstractmethod
    async def track_conversion(self, 
                             conversion_name: str, 
                             conversion_value: float,
                             user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Enregistre une conversion.
        
        Args:
            conversion_name: Nom de la conversion
            conversion_value: Valeur de la conversion
            user_id: ID de l'utilisateur (optionnel)
            
        Returns:
            Informations sur la conversion enregistrée
        """
        pass
    
    @abstractmethod
    async def get_events(self, 
                       start_date: datetime, 
                       end_date: datetime,
                       event_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Récupère les événements dans une plage de dates.
        
        Args:
            start_date: Date de début
            end_date: Date de fin
            event_name: Nom de l'événement (optionnel)
            
        Returns:
            Liste des événements
        """
        pass
    
    @abstractmethod
    async def get_page_views(self, 
                           start_date: datetime, 
                           end_date: datetime,
                           page_path: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Récupère les vues de page dans une plage de dates.
        
        Args:
            start_date: Date de début
            end_date: Date de fin
            page_path: Chemin de la page (optionnel)
            
        Returns:
            Liste des vues de page
        """
        pass
    
    @abstractmethod
    async def get_conversions(self, 
                            start_date: datetime, 
                            end_date: datetime,
                            conversion_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Récupère les conversions dans une plage de dates.
        
        Args:
            start_date: Date de début
            end_date: Date de fin
            conversion_name: Nom de la conversion (optionnel)
            
        Returns:
            Liste des conversions
        """
        pass
