#!/bin/bash

# Script pour exécuter les tests d'intégration de l'intégration avec le système de recommandation

# Définir les couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Exécution des tests d'intégration de l'intégration avec le système de recommandation ===${NC}"

# Vérifier si le fichier .env.test existe
if [ ! -f ".env.test" ]; then
  echo -e "${YELLOW}Création du fichier .env.test...${NC}"
  cp .env.example .env.test
  echo "JWT_SECRET=test-secret" >> .env.test
  echo "RECOMMENDATION_SERVICE_URL=http://localhost:3001" >> .env.test
  echo "RECOMMENDATION_TIMEOUT_MS=5000" >> .env.test
  echo -e "${GREEN}Fichier .env.test créé avec succès.${NC}"
fi

# Créer le répertoire pour les résultats s'il n'existe pas
mkdir -p coverage/integration

# Exécuter les tests d'intégration
echo -e "\n${YELLOW}Exécution des tests d'intégration de l'intégration avec le système de recommandation...${NC}"
npx jest tests/integration/recommendation-integration.test.ts --json --outputFile=coverage/integration/recommendation-integration.json || true

# Générer un rapport de couverture
echo -e "\n${YELLOW}Génération du rapport de couverture...${NC}"
npx jest tests/integration/recommendation-integration.test.ts --coverage || true

# Afficher un résumé
echo -e "\n${GREEN}=== Résumé des tests d'intégration ===${NC}"
echo -e "${YELLOW}Les résultats détaillés sont disponibles dans le dossier coverage/integration${NC}"
echo -e "${YELLOW}Le rapport de couverture est disponible dans le dossier coverage/lcov-report${NC}"

# Ouvrir le rapport de couverture dans le navigateur
if [ -f "coverage/lcov-report/index.html" ]; then
  echo -e "\n${YELLOW}Ouverture du rapport de couverture dans le navigateur...${NC}"
  if [[ "$OSTYPE" == "darwin"* ]]; then
    open coverage/lcov-report/index.html
  elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    xdg-open coverage/lcov-report/index.html
  elif [[ "$OSTYPE" == "cygwin" || "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    start coverage/lcov-report/index.html
  fi
fi
