"""
Repository pour l'accès aux données des retraites.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from src.models.retreat import Retreat, RetreatType, RetreatStatus
from src.database.connection import db_transaction
from .base_repository import BaseRepository

# Configuration du logging
logger = logging.getLogger(__name__)

class RetreatRepository(BaseRepository[Retreat]):
    """
    Repository pour l'accès aux données des retraites.
    """
    
    def __init__(self):
        """
        Initialise le repository des retraites.
        """
        super().__init__('retreats', Retreat)
    
    def find_by_type(self, retreat_type: RetreatType, limit: int = 100, offset: int = 0) -> List[Retreat]:
        """
        Recherche des retraites par type.
        
        Args:
            retreat_type: Type de retraite
            limit: Nombre maximum de retraites à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des retraites correspondantes
        """
        return self.find_by({'retreat_type': retreat_type.value}, limit, offset)
    
    def find_by_status(self, status: RetreatStatus, limit: int = 100, offset: int = 0) -> List[Retreat]:
        """
        Recherche des retraites par statut.
        
        Args:
            status: Statut de la retraite
            limit: Nombre maximum de retraites à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des retraites correspondantes
        """
        return self.find_by({'status': status.value}, limit, offset)
    
    def find_by_date_range(self, start_date: datetime, end_date: datetime, limit: int = 100, offset: int = 0) -> List[Retreat]:
        """
        Recherche des retraites dans une plage de dates.
        
        Args:
            start_date: Date de début
            end_date: Date de fin
            limit: Nombre maximum de retraites à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des retraites correspondantes
        """
        query = f"""
        SELECT * FROM {self.table_name}
        WHERE 
            (start_date >= ? AND start_date <= ?) OR
            (end_date >= ? AND end_date <= ?) OR
            (start_date <= ? AND end_date >= ?)
        ORDER BY start_date
        LIMIT ? OFFSET ?
        """
        
        start_str = start_date.isoformat()
        end_str = end_date.isoformat()
        
        with db_transaction() as conn:
            cursor = conn.execute(query, (
                start_str, end_str,
                start_str, end_str,
                start_str, start_str,
                limit, offset
            ))
            rows = cursor.fetchall()
        
        return [self._deserialize(dict(row)) for row in rows]
    
    def find_by_location(self, country: str, region: Optional[str] = None, limit: int = 100, offset: int = 0) -> List[Retreat]:
        """
        Recherche des retraites par localisation.
        
        Args:
            country: Pays
            region: Région (optionnel)
            limit: Nombre maximum de retraites à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des retraites correspondantes
        """
        if region:
            query = f"""
            SELECT * FROM {self.table_name}
            WHERE location LIKE ? AND location LIKE ?
            LIMIT ? OFFSET ?
            """
            params = (f'%"country":"{country}"%', f'%"region":"{region}"%', limit, offset)
        else:
            query = f"""
            SELECT * FROM {self.table_name}
            WHERE location LIKE ?
            LIMIT ? OFFSET ?
            """
            params = (f'%"country":"{country}"%', limit, offset)
        
        with db_transaction() as conn:
            cursor = conn.execute(query, params)
            rows = cursor.fetchall()
        
        return [self._deserialize(dict(row)) for row in rows]
    
    def find_by_activity(self, activity: str, limit: int = 100, offset: int = 0) -> List[Retreat]:
        """
        Recherche des retraites par activité.
        
        Args:
            activity: Activité recherchée
            limit: Nombre maximum de retraites à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des retraites correspondantes
        """
        query = f"""
        SELECT * FROM {self.table_name}
        WHERE activities LIKE ?
        LIMIT ? OFFSET ?
        """
        
        with db_transaction() as conn:
            cursor = conn.execute(query, (f'%"{activity}"%', limit, offset))
            rows = cursor.fetchall()
        
        return [self._deserialize(dict(row)) for row in rows]
    
    def find_by_price_range(self, min_price: float, max_price: float, limit: int = 100, offset: int = 0) -> List[Retreat]:
        """
        Recherche des retraites par plage de prix.
        
        Args:
            min_price: Prix minimum
            max_price: Prix maximum
            limit: Nombre maximum de retraites à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des retraites correspondantes
        """
        query = f"""
        SELECT * FROM {self.table_name}
        WHERE pricing LIKE ? AND pricing LIKE ?
        LIMIT ? OFFSET ?
        """
        
        with db_transaction() as conn:
            cursor = conn.execute(query, (
                f'%"base_price":%',
                f'%"currency":%',
                limit, offset
            ))
            rows = cursor.fetchall()
        
        # Filtrer les résultats en Python car le filtrage JSON en SQLite est limité
        filtered_rows = []
        for row in rows:
            try:
                pricing = self._deserialize(dict(row)).pricing
                base_price = pricing.base_price
                if min_price <= base_price <= max_price:
                    filtered_rows.append(row)
            except (KeyError, AttributeError, ValueError):
                continue
        
        return [self._deserialize(dict(row)) for row in filtered_rows[:limit]]
    
    def find_for_client(self, client_preferences: Dict[str, Any], limit: int = 10) -> List[Retreat]:
        """
        Recherche des retraites adaptées aux préférences d'un client.
        
        Args:
            client_preferences: Préférences du client
            limit: Nombre maximum de retraites à récupérer
            
        Returns:
            Liste des retraites correspondantes
        """
        # Extraire les préférences
        retreat_types = client_preferences.get('retreat_types', [])
        activities = client_preferences.get('activities', [])
        locations = client_preferences.get('locations', [])
        price_range = client_preferences.get('price_range', {})
        
        # Construire la requête de base
        query = f"""
        SELECT * FROM {self.table_name}
        WHERE status = 'published'
        """
        params = []
        
        # Ajouter les conditions selon les préférences
        if retreat_types:
            placeholders = ', '.join(['?' for _ in retreat_types])
            query += f" AND retreat_type IN ({placeholders})"
            params.extend(retreat_types)
        
        if locations:
            location_conditions = []
            for location in locations:
                location_conditions.append("location LIKE ?")
                params.append(f'%"{location}"%')
            
            if location_conditions:
                query += f" AND ({' OR '.join(location_conditions)})"
        
        # Ajouter la limite
        query += " ORDER BY start_date LIMIT ?"
        params.append(limit)
        
        with db_transaction() as conn:
            cursor = conn.execute(query, params)
            rows = cursor.fetchall()
        
        retreats = [self._deserialize(dict(row)) for row in rows]
        
        # Filtrer par activités et prix en Python
        filtered_retreats = []
        for retreat in retreats:
            # Vérifier les activités
            if activities:
                retreat_activities = retreat.activities
                if not any(activity in retreat_activities for activity in activities):
                    continue
            
            # Vérifier le prix
            if price_range:
                min_price = price_range.get('min', 0)
                max_price = price_range.get('max', float('inf'))
                
                if hasattr(retreat, 'pricing') and hasattr(retreat.pricing, 'base_price'):
                    base_price = retreat.pricing.base_price
                    if not (min_price <= base_price <= max_price):
                        continue
            
            filtered_retreats.append(retreat)
        
        return filtered_retreats
