"""
Repository de base pour l'accès aux données.
"""

import json
import logging
import sqlite3
from typing import Any, Dict, List, Optional, TypeVar, Generic, Type

from src.database.connection import db_transaction

# Type générique pour les modèles
T = TypeVar('T')

# Configuration du logging
logger = logging.getLogger(__name__)

class BaseRepository(Generic[T]):
    """
    Repository de base pour l'accès aux données.
    """
    
    def __init__(self, table_name: str, model_class: Type[T]):
        """
        Initialise le repository.
        
        Args:
            table_name: Nom de la table
            model_class: Classe du modèle
        """
        self.table_name = table_name
        self.model_class = model_class
    
    def _serialize(self, obj: T) -> Dict[str, Any]:
        """
        Sérialise un objet pour le stockage en base de données.
        
        Args:
            obj: Objet à sérialiser
            
        Returns:
            Dictionnaire représentant l'objet
        """
        if hasattr(obj, 'to_dict'):
            data = obj.to_dict()
        else:
            data = obj.__dict__
        
        # Convertir les listes et dictionnaires en JSON
        for key, value in data.items():
            if isinstance(value, (list, dict)):
                data[key] = json.dumps(value)
        
        return data
    
    def _deserialize(self, data: Dict[str, Any]) -> T:
        """
        Désérialise des données de la base de données en objet.
        
        Args:
            data: Données à désérialiser
            
        Returns:
            Objet désérialisé
        """
        # Convertir les chaînes JSON en listes et dictionnaires
        for key, value in data.items():
            if isinstance(value, str) and (value.startswith('[') or value.startswith('{')):
                try:
                    data[key] = json.loads(value)
                except json.JSONDecodeError:
                    pass
        
        if hasattr(self.model_class, 'from_dict'):
            return self.model_class.from_dict(data)
        else:
            return self.model_class(**data)
    
    def create(self, obj: T) -> T:
        """
        Crée un nouvel objet dans la base de données.
        
        Args:
            obj: Objet à créer
            
        Returns:
            Objet créé
        """
        data = self._serialize(obj)
        
        # Construire la requête SQL
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['?' for _ in data])
        query = f"INSERT INTO {self.table_name} ({columns}) VALUES ({placeholders})"
        
        with db_transaction() as conn:
            conn.execute(query, list(data.values()))
            logger.info(f"Objet créé dans la table {self.table_name}")
        
        return obj
    
    def get_by_id(self, id: str) -> Optional[T]:
        """
        Récupère un objet par son ID.
        
        Args:
            id: ID de l'objet
            
        Returns:
            Objet récupéré ou None si non trouvé
        """
        query = f"SELECT * FROM {self.table_name} WHERE id = ?"
        
        with db_transaction() as conn:
            cursor = conn.execute(query, (id,))
            row = cursor.fetchone()
        
        if row:
            return self._deserialize(dict(row))
        else:
            return None
    
    def get_all(self, limit: int = 100, offset: int = 0) -> List[T]:
        """
        Récupère tous les objets.
        
        Args:
            limit: Nombre maximum d'objets à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des objets
        """
        query = f"SELECT * FROM {self.table_name} LIMIT ? OFFSET ?"
        
        with db_transaction() as conn:
            cursor = conn.execute(query, (limit, offset))
            rows = cursor.fetchall()
        
        return [self._deserialize(dict(row)) for row in rows]
    
    def update(self, obj: T) -> T:
        """
        Met à jour un objet dans la base de données.
        
        Args:
            obj: Objet à mettre à jour
            
        Returns:
            Objet mis à jour
        """
        data = self._serialize(obj)
        
        # Extraire l'ID
        id_value = data.pop('id')
        
        # Construire la requête SQL
        set_clause = ', '.join([f"{key} = ?" for key in data.keys()])
        query = f"UPDATE {self.table_name} SET {set_clause} WHERE id = ?"
        
        with db_transaction() as conn:
            conn.execute(query, list(data.values()) + [id_value])
            logger.info(f"Objet mis à jour dans la table {self.table_name}")
        
        # Remettre l'ID dans les données
        data['id'] = id_value
        
        return obj
    
    def delete(self, id: str) -> bool:
        """
        Supprime un objet de la base de données.
        
        Args:
            id: ID de l'objet à supprimer
            
        Returns:
            True si l'objet a été supprimé, False sinon
        """
        query = f"DELETE FROM {self.table_name} WHERE id = ?"
        
        with db_transaction() as conn:
            cursor = conn.execute(query, (id,))
            deleted = cursor.rowcount > 0
        
        if deleted:
            logger.info(f"Objet supprimé de la table {self.table_name}")
        
        return deleted
    
    def find_by(self, criteria: Dict[str, Any], limit: int = 100, offset: int = 0) -> List[T]:
        """
        Recherche des objets selon des critères.
        
        Args:
            criteria: Critères de recherche
            limit: Nombre maximum d'objets à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des objets correspondants
        """
        if not criteria:
            return self.get_all(limit, offset)
        
        # Construire la clause WHERE
        where_clause = ' AND '.join([f"{key} = ?" for key in criteria.keys()])
        query = f"SELECT * FROM {self.table_name} WHERE {where_clause} LIMIT ? OFFSET ?"
        
        with db_transaction() as conn:
            cursor = conn.execute(query, list(criteria.values()) + [limit, offset])
            rows = cursor.fetchall()
        
        return [self._deserialize(dict(row)) for row in rows]
    
    def count(self, criteria: Optional[Dict[str, Any]] = None) -> int:
        """
        Compte le nombre d'objets correspondant aux critères.
        
        Args:
            criteria: Critères de recherche (optionnel)
            
        Returns:
            Nombre d'objets
        """
        if criteria:
            # Construire la clause WHERE
            where_clause = ' AND '.join([f"{key} = ?" for key in criteria.keys()])
            query = f"SELECT COUNT(*) FROM {self.table_name} WHERE {where_clause}"
            params = list(criteria.values())
        else:
            query = f"SELECT COUNT(*) FROM {self.table_name}"
            params = []
        
        with db_transaction() as conn:
            cursor = conn.execute(query, params)
            count = cursor.fetchone()[0]
        
        return count
