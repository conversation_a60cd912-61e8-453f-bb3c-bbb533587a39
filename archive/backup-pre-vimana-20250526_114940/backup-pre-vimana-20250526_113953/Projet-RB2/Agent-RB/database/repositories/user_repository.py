"""
Repository pour l'accès aux données des utilisateurs.
"""

import logging
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime

from src.auth.models import User, UserRole
from src.auth.jwt_auth import get_password_hash, verify_password
from src.database.connection import db_transaction
from src.database.repositories.base_repository import BaseRepository

# Configuration du logging
logger = logging.getLogger(__name__)

class UserRepository(BaseRepository[User]):
    """
    Repository pour l'accès aux données des utilisateurs.
    """
    
    def __init__(self):
        """
        Initialise le repository des utilisateurs.
        """
        super().__init__('users', User)
    
    def find_by_email(self, email: str) -> Optional[User]:
        """
        Recherche un utilisateur par son email.
        
        Args:
            email: Email de l'utilisateur
            
        Returns:
            Utilisateur correspondant ou None si non trouvé
        """
        users = self.find_by({'email': email}, limit=1)
        return users[0] if users else None
    
    def create_user(self, email: str, password: str, full_name: Optional[str] = None, role: UserRole = UserRole.CLIENT) -> User:
        """
        Crée un nouvel utilisateur.
        
        Args:
            email: Email de l'utilisateur
            password: Mot de passe de l'utilisateur
            full_name: Nom complet de l'utilisateur (optionnel)
            role: Rôle de l'utilisateur
            
        Returns:
            Utilisateur créé
            
        Raises:
            ValueError: Si un utilisateur avec cet email existe déjà
        """
        # Vérifier si un utilisateur avec cet email existe déjà
        existing_user = self.find_by_email(email)
        if existing_user:
            raise ValueError(f"Un utilisateur avec l'email {email} existe déjà")
        
        # Générer un hash du mot de passe
        hashed_password = get_password_hash(password)
        
        # Créer l'utilisateur
        now = datetime.now()
        user = User(
            id=f"user_{uuid.uuid4().hex[:8]}",
            email=email,
            full_name=full_name,
            is_active=True,
            role=role,
            created_at=now,
            updated_at=now
        )
        
        # Ajouter le mot de passe hashé
        user_data = self._serialize(user)
        user_data["password"] = hashed_password
        
        # Construire la requête SQL
        columns = ', '.join(user_data.keys())
        placeholders = ', '.join(['?' for _ in user_data])
        query = f"INSERT INTO {self.table_name} ({columns}) VALUES ({placeholders})"
        
        with db_transaction() as conn:
            conn.execute(query, list(user_data.values()))
            logger.info(f"Utilisateur créé: {email}")
        
        return user
    
    def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """
        Authentifie un utilisateur.
        
        Args:
            email: Email de l'utilisateur
            password: Mot de passe de l'utilisateur
            
        Returns:
            Utilisateur authentifié ou None si l'authentification échoue
        """
        # Récupérer l'utilisateur
        query = f"SELECT * FROM {self.table_name} WHERE email = ?"
        
        with db_transaction() as conn:
            cursor = conn.execute(query, (email,))
            row = cursor.fetchone()
        
        if not row:
            return None
        
        # Vérifier le mot de passe
        user_data = dict(row)
        hashed_password = user_data.get("password")
        
        if not hashed_password or not verify_password(password, hashed_password):
            return None
        
        # Créer l'utilisateur
        user_data.pop("password", None)  # Retirer le mot de passe des données
        user = self._deserialize(user_data)
        
        # Mettre à jour la date de dernière connexion
        user.last_login = datetime.now()
        self.update(user)
        
        return user
    
    def update_password(self, user_id: str, new_password: str) -> bool:
        """
        Met à jour le mot de passe d'un utilisateur.
        
        Args:
            user_id: ID de l'utilisateur
            new_password: Nouveau mot de passe
            
        Returns:
            True si la mise à jour a réussi, False sinon
        """
        # Générer un hash du nouveau mot de passe
        hashed_password = get_password_hash(new_password)
        
        # Mettre à jour le mot de passe
        query = f"UPDATE {self.table_name} SET password = ?, updated_at = ? WHERE id = ?"
        
        with db_transaction() as conn:
            cursor = conn.execute(query, (hashed_password, datetime.now().isoformat(), user_id))
            updated = cursor.rowcount > 0
        
        if updated:
            logger.info(f"Mot de passe mis à jour pour l'utilisateur {user_id}")
        
        return updated
    
    def update_role(self, user_id: str, new_role: UserRole) -> Optional[User]:
        """
        Met à jour le rôle d'un utilisateur.
        
        Args:
            user_id: ID de l'utilisateur
            new_role: Nouveau rôle
            
        Returns:
            Utilisateur mis à jour ou None si non trouvé
        """
        # Récupérer l'utilisateur
        user = self.get_by_id(user_id)
        if not user:
            return None
        
        # Mettre à jour le rôle
        user.role = new_role
        user.updated_at = datetime.now()
        
        # Sauvegarder les modifications
        return self.update(user)
    
    def deactivate_user(self, user_id: str) -> Optional[User]:
        """
        Désactive un utilisateur.
        
        Args:
            user_id: ID de l'utilisateur
            
        Returns:
            Utilisateur mis à jour ou None si non trouvé
        """
        # Récupérer l'utilisateur
        user = self.get_by_id(user_id)
        if not user:
            return None
        
        # Désactiver l'utilisateur
        user.is_active = False
        user.updated_at = datetime.now()
        
        # Sauvegarder les modifications
        return self.update(user)
    
    def activate_user(self, user_id: str) -> Optional[User]:
        """
        Active un utilisateur.
        
        Args:
            user_id: ID de l'utilisateur
            
        Returns:
            Utilisateur mis à jour ou None si non trouvé
        """
        # Récupérer l'utilisateur
        user = self.get_by_id(user_id)
        if not user:
            return None
        
        # Activer l'utilisateur
        user.is_active = True
        user.updated_at = datetime.now()
        
        # Sauvegarder les modifications
        return self.update(user)
