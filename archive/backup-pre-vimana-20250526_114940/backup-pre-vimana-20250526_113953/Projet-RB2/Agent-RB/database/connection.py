"""
Gestion des connexions à la base de données.
"""

import os
import logging
from typing import Any, Dict, Optional
import sqlite3
from contextlib import contextmanager

# Configuration du logging
logger = logging.getLogger(__name__)

# Chemin par défaut pour la base de données SQLite
DEFAULT_DB_PATH = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'retreat_db.sqlite')

# <PERSON><PERSON>er le répertoire data s'il n'existe pas
os.makedirs(os.path.dirname(DEFAULT_DB_PATH), exist_ok=True)

def get_db_connection(db_path: Optional[str] = None) -> sqlite3.Connection:
    """
    Établit une connexion à la base de données SQLite.

    Args:
        db_path: Chemin vers le fichier de base de données (optionnel)

    Returns:
        Connexion à la base de données
    """
    db_path = db_path or DEFAULT_DB_PATH

    try:
        # Connexion à la base de données avec row_factory pour obtenir des dictionnaires
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row

        # Activer les foreign keys
        conn.execute("PRAGMA foreign_keys = ON")

        logger.debug(f"Connexion établie à la base de données: {db_path}")
        return conn

    except sqlite3.Error as e:
        logger.error(f"Erreur lors de la connexion à la base de données: {str(e)}")
        raise

def close_db_connection(conn: sqlite3.Connection) -> None:
    """
    Ferme une connexion à la base de données.

    Args:
        conn: Connexion à la base de données
    """
    try:
        conn.close()
        logger.debug("Connexion à la base de données fermée")

    except sqlite3.Error as e:
        logger.error(f"Erreur lors de la fermeture de la connexion à la base de données: {str(e)}")
        raise

@contextmanager
def db_transaction(db_path: Optional[str] = None):
    """
    Gestionnaire de contexte pour les transactions de base de données.

    Args:
        db_path: Chemin vers le fichier de base de données (optionnel)

    Yields:
        Connexion à la base de données
    """
    conn = get_db_connection(db_path)
    try:
        yield conn
        conn.commit()
    except Exception as e:
        conn.rollback()
        logger.error(f"Transaction annulée: {str(e)}")
        raise
    finally:
        close_db_connection(conn)

def init_db(db_path: Optional[str] = None) -> None:
    """
    Initialise la base de données avec les tables nécessaires.

    Args:
        db_path: Chemin vers le fichier de base de données (optionnel)
    """
    db_path = db_path or DEFAULT_DB_PATH

    # Définition des tables
    tables = [
        """
        CREATE TABLE IF NOT EXISTS users (
            id TEXT PRIMARY KEY,
            email TEXT NOT NULL UNIQUE,
            password TEXT NOT NULL,
            full_name TEXT,
            is_active BOOLEAN NOT NULL DEFAULT 1,
            role TEXT NOT NULL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            last_login TEXT
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS partners (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT NOT NULL,
            phone TEXT,
            partner_type TEXT NOT NULL,
            status TEXT NOT NULL,
            specialties TEXT,
            description TEXT,
            bio TEXT,
            website TEXT,
            social_media TEXT,
            address TEXT,
            location TEXT,
            languages TEXT,
            certifications TEXT,
            availability TEXT,
            pricing TEXT,
            reviews TEXT,
            rating REAL DEFAULT 0.0,
            user_id TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS retreats (
            id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            description TEXT NOT NULL,
            retreat_type TEXT NOT NULL,
            status TEXT NOT NULL,
            start_date TEXT NOT NULL,
            end_date TEXT NOT NULL,
            location TEXT NOT NULL,
            activities TEXT,
            schedule TEXT,
            pricing TEXT,
            accommodation TEXT,
            capacity INTEGER DEFAULT 10,
            min_participants INTEGER DEFAULT 4,
            current_participants INTEGER DEFAULT 0,
            language TEXT DEFAULT 'fr',
            additional_languages TEXT,
            level TEXT DEFAULT 'all',
            tags TEXT,
            included_services TEXT,
            excluded_services TEXT,
            requirements TEXT,
            photos TEXT,
            organizer_id TEXT,
            instructors TEXT,
            partners TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (organizer_id) REFERENCES partners (id)
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS clients (
            id TEXT PRIMARY KEY,
            email TEXT NOT NULL,
            first_name TEXT,
            last_name TEXT,
            phone TEXT,
            status TEXT NOT NULL,
            preferences TEXT,
            profile_picture TEXT,
            bio TEXT,
            date_of_birth TEXT,
            address TEXT,
            emergency_contact TEXT,
            health_info TEXT,
            past_retreats TEXT,
            upcoming_retreats TEXT,
            wishlist TEXT,
            notifications_settings TEXT,
            user_id TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            last_login TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS bookings (
            id TEXT PRIMARY KEY,
            client_id TEXT NOT NULL,
            retreat_id TEXT NOT NULL,
            status TEXT NOT NULL,
            booking_date TEXT NOT NULL,
            participants TEXT,
            accommodation_type TEXT,
            room_preference TEXT,
            total_price REAL NOT NULL,
            currency TEXT DEFAULT 'EUR',
            payments TEXT,
            payment_status TEXT NOT NULL,
            special_requests TEXT,
            arrival_details TEXT,
            departure_details TEXT,
            add_ons TEXT,
            cancellation_policy TEXT,
            cancellation_reason TEXT,
            cancellation_date TEXT,
            notes TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (client_id) REFERENCES clients (id),
            FOREIGN KEY (retreat_id) REFERENCES retreats (id)
        )
        """
    ]

    with db_transaction(db_path) as conn:
        for table_sql in tables:
            conn.execute(table_sql)

        # Créer un utilisateur administrateur par défaut si la table est vide
        cursor = conn.execute("SELECT COUNT(*) FROM users")
        count = cursor.fetchone()[0]

        if count == 0:
            from src.auth.jwt_auth import get_password_hash
            from datetime import datetime

            # Créer un utilisateur administrateur par défaut
            admin_password = os.environ.get("ADMIN_PASSWORD", "admin123")
            hashed_password = get_password_hash(admin_password)
            now = datetime.now().isoformat()

            conn.execute(
                "INSERT INTO users (id, email, password, full_name, is_active, role, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                ("user_admin", "<EMAIL>", hashed_password, "Administrateur", True, "admin", now, now)
            )

            logger.info("Utilisateur administrateur créé avec succès")

        logger.info("Base de données initialisée avec succès")

if __name__ == "__main__":
    # Configurer le logging
    logging.basicConfig(level=logging.INFO)

    # Initialiser la base de données
    init_db()
    logger.info(f"Base de données créée à {DEFAULT_DB_PATH}")
