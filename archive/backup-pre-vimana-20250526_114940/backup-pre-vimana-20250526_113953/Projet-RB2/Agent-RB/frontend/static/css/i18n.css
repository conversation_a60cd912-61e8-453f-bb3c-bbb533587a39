/**
 * Styles pour l'internationalisation.
 */

/* Sélecteur de langue */
.language-selector-wrapper {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.language-selector-label {
  margin-right: 10px;
  font-weight: 500;
}

.language-selector {
  padding: 5px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  cursor: pointer;
}

.language-selector:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

/* Boutons de langue */
.language-buttons-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin: 10px 0;
}

.language-button {
  padding: 5px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.language-button:hover {
  background-color: #f5f5f5;
}

.language-button.active {
  background-color: #4a90e2;
  color: #fff;
  border-color: #4a90e2;
}

/* Drapeaux de langue */
.language-flags-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin: 10px 0;
}

.language-flag {
  padding: 2px;
  border: 1px solid transparent;
  border-radius: 4px;
  background-color: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
}

.language-flag:hover {
  background-color: #f5f5f5;
}

.language-flag.active {
  border-color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.1);
}

.language-flag img {
  display: block;
  border-radius: 2px;
}

/* Styles pour les éléments traduits */
[data-i18n-loading] {
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

[data-i18n-loaded] {
  opacity: 1;
}
