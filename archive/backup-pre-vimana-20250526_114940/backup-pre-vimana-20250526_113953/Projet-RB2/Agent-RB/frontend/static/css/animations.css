/**
 * Styles pour les animations et transitions.
 */

/* Variables */
:root {
  --animation-speed-slow: 1s;
  --animation-speed-normal: 0.5s;
  --animation-speed-fast: 0.3s;
  --animation-timing-function: ease-in-out;
  --transition-base: all 0.2s ease-in-out;
  --transition-slow: all 0.5s ease-in-out;
  --transition-fast: all 0.1s ease-in-out;
}

/* Transitions */
.transition-base {
  transition: var(--transition-base);
}

.transition-slow {
  transition: var(--transition-slow);
}

.transition-fast {
  transition: var(--transition-fast);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

@keyframes slideOutUp {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-100%);
  }
}

@keyframes slideOutDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

@keyframes zoomIn {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}

@keyframes zoomOut {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-30px);
  }
  60% {
    transform: translateY(-15px);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn var(--animation-speed-normal) var(--animation-timing-function);
}

.animate-fade-out {
  animation: fadeOut var(--animation-speed-normal) var(--animation-timing-function);
}

.animate-slide-in-left {
  animation: slideInLeft var(--animation-speed-normal) var(--animation-timing-function);
}

.animate-slide-in-right {
  animation: slideInRight var(--animation-speed-normal) var(--animation-timing-function);
}

.animate-slide-in-up {
  animation: slideInUp var(--animation-speed-normal) var(--animation-timing-function);
}

.animate-slide-in-down {
  animation: slideInDown var(--animation-speed-normal) var(--animation-timing-function);
}

.animate-slide-out-left {
  animation: slideOutLeft var(--animation-speed-normal) var(--animation-timing-function);
}

.animate-slide-out-right {
  animation: slideOutRight var(--animation-speed-normal) var(--animation-timing-function);
}

.animate-slide-out-up {
  animation: slideOutUp var(--animation-speed-normal) var(--animation-timing-function);
}

.animate-slide-out-down {
  animation: slideOutDown var(--animation-speed-normal) var(--animation-timing-function);
}

.animate-zoom-in {
  animation: zoomIn var(--animation-speed-normal) var(--animation-timing-function);
}

.animate-zoom-out {
  animation: zoomOut var(--animation-speed-normal) var(--animation-timing-function);
}

.animate-pulse {
  animation: pulse var(--animation-speed-slow) var(--animation-timing-function) infinite;
}

.animate-shake {
  animation: shake var(--animation-speed-normal) var(--animation-timing-function);
}

.animate-bounce {
  animation: bounce var(--animation-speed-slow) var(--animation-timing-function);
}

.animate-spin {
  animation: spin var(--animation-speed-normal) linear infinite;
}

/* Animation Speed Modifiers */
.animation-slow {
  animation-duration: var(--animation-speed-slow);
}

.animation-normal {
  animation-duration: var(--animation-speed-normal);
}

.animation-fast {
  animation-duration: var(--animation-speed-fast);
}

/* Animation Delay */
.animation-delay-100 {
  animation-delay: 0.1s;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-300 {
  animation-delay: 0.3s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-500 {
  animation-delay: 0.5s;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

/* Animation Iteration Count */
.animation-once {
  animation-iteration-count: 1;
}

.animation-twice {
  animation-iteration-count: 2;
}

.animation-infinite {
  animation-iteration-count: infinite;
}

/* Animation Direction */
.animation-alternate {
  animation-direction: alternate;
}

.animation-reverse {
  animation-direction: reverse;
}

/* Animation Fill Mode */
.animation-forwards {
  animation-fill-mode: forwards;
}

.animation-backwards {
  animation-fill-mode: backwards;
}

.animation-both {
  animation-fill-mode: both;
}

/* Hover Effects */
.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-shadow {
  transition: box-shadow 0.3s ease;
}

.hover-shadow:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.hover-rotate {
  transition: transform 0.3s ease;
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

.hover-brightness {
  transition: filter 0.3s ease;
}

.hover-brightness:hover {
  filter: brightness(1.1);
}

/* Page Transitions */
.page-transition-fade {
  opacity: 0;
  transition: opacity 0.5s ease;
}

.page-transition-fade.active {
  opacity: 1;
}

.page-transition-slide {
  transform: translateX(100%);
  transition: transform 0.5s ease;
}

.page-transition-slide.active {
  transform: translateX(0);
}

.page-transition-zoom {
  transform: scale(0.9);
  opacity: 0;
  transition: transform 0.5s ease, opacity 0.5s ease;
}

.page-transition-zoom.active {
  transform: scale(1);
  opacity: 1;
}

/* Loading Animations */
.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s linear infinite;
}

.loading-dots {
  display: inline-flex;
  align-items: center;
}

.loading-dots::after {
  content: "...";
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% {
    content: ".";
  }
  40% {
    content: "..";
  }
  60%, 100% {
    content: "...";
  }
}

.loading-pulse {
  display: inline-block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-primary);
  animation: pulse 1.5s infinite;
}

.loading-bar {
  position: relative;
  width: 100%;
  height: 4px;
  background-color: rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.loading-bar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 30%;
  height: 100%;
  background-color: var(--color-primary);
  animation: loading-bar 2s infinite;
}

@keyframes loading-bar {
  0% {
    left: -30%;
  }
  100% {
    left: 100%;
  }
}

/* Scroll Animations */
.scroll-fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.scroll-fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.scroll-slide-in-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.scroll-slide-in-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.scroll-slide-in-right {
  opacity: 0;
  transform: translateX(50px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.scroll-slide-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}

.scroll-zoom-in {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.scroll-zoom-in.visible {
  opacity: 1;
  transform: scale(1);
}

/* Staggered Animations */
.stagger-fade-in > * {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.stagger-fade-in.visible > *:nth-child(1) {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.1s;
}

.stagger-fade-in.visible > *:nth-child(2) {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.2s;
}

.stagger-fade-in.visible > *:nth-child(3) {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.3s;
}

.stagger-fade-in.visible > *:nth-child(4) {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.4s;
}

.stagger-fade-in.visible > *:nth-child(5) {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.5s;
}

.stagger-fade-in.visible > *:nth-child(n+6) {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.6s;
}

/* Utility Classes for Animations */
.no-animation {
  animation: none !important;
  transition: none !important;
}

.pause-animation {
  animation-play-state: paused !important;
}

.resume-animation {
  animation-play-state: running !important;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
