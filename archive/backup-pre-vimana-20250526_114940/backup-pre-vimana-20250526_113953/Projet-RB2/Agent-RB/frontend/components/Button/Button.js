/**
 * Composant Button réutilisable.
 */

/**
 * Crée un bouton personnalisé.
 * 
 * @param {Object} options - Options du bouton
 * @param {string} options.id - ID du bouton
 * @param {string} options.text - Texte du bouton
 * @param {string} options.type - Type du bouton (primary, secondary, danger, success, etc.)
 * @param {string} options.size - Taille du bouton (small, medium, large)
 * @param {boolean} options.disabled - Si le bouton est désactivé
 * @param {boolean} options.outline - Si le bouton est en mode outline
 * @param {string} options.icon - Nom de l'icône à afficher
 * @param {Function} options.onClick - Fonction à exécuter au clic
 * @param {string} options.i18nKey - Clé de traduction pour le texte du bouton
 * @returns {HTMLButtonElement} - Élément bouton
 */
export function createButton({
  id,
  text,
  type = 'primary',
  size = 'medium',
  disabled = false,
  outline = false,
  icon = null,
  onClick = null,
  i18nKey = null
}) {
  // Créer le bouton
  const button = document.createElement('button');
  
  // Définir l'ID
  if (id) {
    button.id = id;
  }
  
  // Définir les classes
  button.className = `btn btn-${type} btn-${size}`;
  
  if (outline) {
    button.classList.add('btn-outline');
  }
  
  if (disabled) {
    button.disabled = true;
    button.classList.add('btn-disabled');
  }
  
  // Ajouter l'icône si spécifiée
  if (icon) {
    const iconElement = document.createElement('span');
    iconElement.className = `icon icon-${icon}`;
    button.appendChild(iconElement);
    
    // Ajouter une classe pour l'espacement
    button.classList.add('btn-with-icon');
  }
  
  // Ajouter le texte
  const textSpan = document.createElement('span');
  textSpan.className = 'btn-text';
  
  if (i18nKey) {
    textSpan.setAttribute('data-i18n', i18nKey);
    textSpan.textContent = text || i18nKey;
  } else {
    textSpan.textContent = text;
  }
  
  button.appendChild(textSpan);
  
  // Ajouter l'événement de clic
  if (onClick) {
    button.addEventListener('click', onClick);
  }
  
  return button;
}

/**
 * Crée un groupe de boutons.
 * 
 * @param {Object} options - Options du groupe de boutons
 * @param {string} options.id - ID du groupe de boutons
 * @param {Array} options.buttons - Liste des options de boutons
 * @param {string} options.orientation - Orientation du groupe (horizontal, vertical)
 * @returns {HTMLDivElement} - Élément div contenant les boutons
 */
export function createButtonGroup({
  id,
  buttons = [],
  orientation = 'horizontal'
}) {
  // Créer le groupe de boutons
  const buttonGroup = document.createElement('div');
  
  // Définir l'ID
  if (id) {
    buttonGroup.id = id;
  }
  
  // Définir les classes
  buttonGroup.className = `btn-group btn-group-${orientation}`;
  
  // Ajouter les boutons
  buttons.forEach(buttonOptions => {
    const button = createButton(buttonOptions);
    buttonGroup.appendChild(button);
  });
  
  return buttonGroup;
}

/**
 * Crée un bouton de chargement.
 * 
 * @param {Object} options - Options du bouton
 * @param {string} options.id - ID du bouton
 * @param {string} options.text - Texte du bouton
 * @param {string} options.loadingText - Texte du bouton pendant le chargement
 * @param {string} options.type - Type du bouton (primary, secondary, danger, success, etc.)
 * @param {string} options.size - Taille du bouton (small, medium, large)
 * @param {Function} options.onClick - Fonction à exécuter au clic
 * @param {string} options.i18nKey - Clé de traduction pour le texte du bouton
 * @param {string} options.i18nLoadingKey - Clé de traduction pour le texte de chargement
 * @returns {HTMLButtonElement} - Élément bouton
 */
export function createLoadingButton({
  id,
  text,
  loadingText = 'Chargement...',
  type = 'primary',
  size = 'medium',
  onClick = null,
  i18nKey = null,
  i18nLoadingKey = 'common.loading'
}) {
  // Créer le bouton
  const button = createButton({
    id,
    text,
    type,
    size,
    icon: 'loading',
    onClick: async (event) => {
      // Désactiver le bouton pendant le chargement
      button.disabled = true;
      button.classList.add('btn-loading');
      
      // Changer le texte
      const textSpan = button.querySelector('.btn-text');
      const originalText = textSpan.textContent;
      
      if (i18nLoadingKey) {
        textSpan.setAttribute('data-i18n', i18nLoadingKey);
        textSpan.textContent = loadingText;
      } else {
        textSpan.textContent = loadingText;
      }
      
      try {
        // Exécuter la fonction de clic
        if (onClick) {
          await onClick(event);
        }
      } finally {
        // Réactiver le bouton
        button.disabled = false;
        button.classList.remove('btn-loading');
        
        // Restaurer le texte
        if (i18nKey) {
          textSpan.setAttribute('data-i18n', i18nKey);
        }
        
        textSpan.textContent = originalText;
      }
    },
    i18nKey
  });
  
  // Cacher l'icône de chargement par défaut
  const iconElement = button.querySelector('.icon-loading');
  if (iconElement) {
    iconElement.style.display = 'none';
    
    // Afficher l'icône pendant le chargement
    button.addEventListener('click', () => {
      iconElement.style.display = 'inline-block';
    });
  }
  
  return button;
}

export default {
  createButton,
  createButtonGroup,
  createLoadingButton
};
