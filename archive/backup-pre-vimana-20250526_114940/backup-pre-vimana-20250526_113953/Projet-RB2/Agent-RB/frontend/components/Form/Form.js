/**
 * Composants Form réutilisables.
 */

import { t } from '../../js/i18n.js';

/**
 * Crée un champ de formulaire.
 * 
 * @param {Object} options - Options du champ
 * @param {string} options.id - ID du champ
 * @param {string} options.name - Nom du champ
 * @param {string} options.label - Libellé du champ
 * @param {string} options.type - Type du champ (text, email, password, etc.)
 * @param {string} options.value - Valeur du champ
 * @param {string} options.placeholder - Placeholder du champ
 * @param {boolean} options.required - Si le champ est requis
 * @param {boolean} options.disabled - Si le champ est désactivé
 * @param {boolean} options.readonly - Si le champ est en lecture seule
 * @param {string} options.error - Message d'erreur
 * @param {string} options.help - Texte d'aide
 * @param {Function} options.onChange - Fonction à exécuter au changement
 * @param {Function} options.onBlur - Fonction à exécuter à la perte de focus
 * @param {Function} options.onFocus - Fonction à exécuter à la prise de focus
 * @param {string} options.i18nLabelKey - Clé de traduction pour le libellé
 * @param {string} options.i18nPlaceholderKey - Clé de traduction pour le placeholder
 * @param {string} options.i18nErrorKey - Clé de traduction pour le message d'erreur
 * @param {string} options.i18nHelpKey - Clé de traduction pour le texte d'aide
 * @returns {HTMLDivElement} - Élément div du champ
 */
export function createFormField({
  id,
  name,
  label,
  type = 'text',
  value = '',
  placeholder = '',
  required = false,
  disabled = false,
  readonly = false,
  error = '',
  help = '',
  onChange = null,
  onBlur = null,
  onFocus = null,
  i18nLabelKey = null,
  i18nPlaceholderKey = null,
  i18nErrorKey = null,
  i18nHelpKey = null
}) {
  // Créer le conteneur du champ
  const fieldContainer = document.createElement('div');
  fieldContainer.className = 'form-field';
  
  if (error) {
    fieldContainer.classList.add('form-field-error');
  }
  
  // Créer le libellé
  if (label) {
    const labelElement = document.createElement('label');
    labelElement.htmlFor = id;
    labelElement.className = 'form-label';
    
    if (i18nLabelKey) {
      labelElement.setAttribute('data-i18n', i18nLabelKey);
      labelElement.textContent = label;
    } else {
      labelElement.textContent = label;
    }
    
    if (required) {
      labelElement.classList.add('required');
      
      const requiredMark = document.createElement('span');
      requiredMark.className = 'required-mark';
      requiredMark.textContent = '*';
      labelElement.appendChild(requiredMark);
    }
    
    fieldContainer.appendChild(labelElement);
  }
  
  // Créer le champ
  let inputElement;
  
  if (type === 'textarea') {
    inputElement = document.createElement('textarea');
  } else {
    inputElement = document.createElement('input');
    inputElement.type = type;
  }
  
  inputElement.id = id;
  inputElement.name = name;
  inputElement.className = 'form-input';
  inputElement.value = value;
  
  if (placeholder) {
    if (i18nPlaceholderKey) {
      inputElement.setAttribute('data-i18n-placeholder', i18nPlaceholderKey);
      inputElement.placeholder = placeholder;
    } else {
      inputElement.placeholder = placeholder;
    }
  }
  
  if (required) {
    inputElement.required = true;
  }
  
  if (disabled) {
    inputElement.disabled = true;
  }
  
  if (readonly) {
    inputElement.readOnly = true;
  }
  
  // Ajouter les événements
  if (onChange) {
    inputElement.addEventListener('input', onChange);
  }
  
  if (onBlur) {
    inputElement.addEventListener('blur', onBlur);
  }
  
  if (onFocus) {
    inputElement.addEventListener('focus', onFocus);
  }
  
  fieldContainer.appendChild(inputElement);
  
  // Ajouter le message d'erreur
  if (error) {
    const errorElement = document.createElement('div');
    errorElement.className = 'form-error';
    
    if (i18nErrorKey) {
      errorElement.setAttribute('data-i18n', i18nErrorKey);
      errorElement.textContent = error;
    } else {
      errorElement.textContent = error;
    }
    
    fieldContainer.appendChild(errorElement);
  }
  
  // Ajouter le texte d'aide
  if (help) {
    const helpElement = document.createElement('div');
    helpElement.className = 'form-help';
    
    if (i18nHelpKey) {
      helpElement.setAttribute('data-i18n', i18nHelpKey);
      helpElement.textContent = help;
    } else {
      helpElement.textContent = help;
    }
    
    fieldContainer.appendChild(helpElement);
  }
  
  return fieldContainer;
}

/**
 * Crée un champ de sélection.
 * 
 * @param {Object} options - Options du champ
 * @param {string} options.id - ID du champ
 * @param {string} options.name - Nom du champ
 * @param {string} options.label - Libellé du champ
 * @param {Array} options.options - Options du champ
 * @param {string} options.value - Valeur sélectionnée
 * @param {boolean} options.required - Si le champ est requis
 * @param {boolean} options.disabled - Si le champ est désactivé
 * @param {string} options.error - Message d'erreur
 * @param {string} options.help - Texte d'aide
 * @param {Function} options.onChange - Fonction à exécuter au changement
 * @param {string} options.i18nLabelKey - Clé de traduction pour le libellé
 * @param {string} options.i18nErrorKey - Clé de traduction pour le message d'erreur
 * @param {string} options.i18nHelpKey - Clé de traduction pour le texte d'aide
 * @returns {HTMLDivElement} - Élément div du champ
 */
export function createSelectField({
  id,
  name,
  label,
  options = [],
  value = '',
  required = false,
  disabled = false,
  error = '',
  help = '',
  onChange = null,
  i18nLabelKey = null,
  i18nErrorKey = null,
  i18nHelpKey = null
}) {
  // Créer le conteneur du champ
  const fieldContainer = document.createElement('div');
  fieldContainer.className = 'form-field';
  
  if (error) {
    fieldContainer.classList.add('form-field-error');
  }
  
  // Créer le libellé
  if (label) {
    const labelElement = document.createElement('label');
    labelElement.htmlFor = id;
    labelElement.className = 'form-label';
    
    if (i18nLabelKey) {
      labelElement.setAttribute('data-i18n', i18nLabelKey);
      labelElement.textContent = label;
    } else {
      labelElement.textContent = label;
    }
    
    if (required) {
      labelElement.classList.add('required');
      
      const requiredMark = document.createElement('span');
      requiredMark.className = 'required-mark';
      requiredMark.textContent = '*';
      labelElement.appendChild(requiredMark);
    }
    
    fieldContainer.appendChild(labelElement);
  }
  
  // Créer le champ
  const selectElement = document.createElement('select');
  selectElement.id = id;
  selectElement.name = name;
  selectElement.className = 'form-select';
  
  if (required) {
    selectElement.required = true;
  }
  
  if (disabled) {
    selectElement.disabled = true;
  }
  
  // Ajouter les options
  options.forEach(option => {
    const optionElement = document.createElement('option');
    optionElement.value = option.value;
    
    if (option.i18nKey) {
      optionElement.setAttribute('data-i18n', option.i18nKey);
      optionElement.textContent = option.label;
    } else {
      optionElement.textContent = option.label;
    }
    
    if (option.value === value) {
      optionElement.selected = true;
    }
    
    selectElement.appendChild(optionElement);
  });
  
  // Ajouter l'événement de changement
  if (onChange) {
    selectElement.addEventListener('change', onChange);
  }
  
  fieldContainer.appendChild(selectElement);
  
  // Ajouter le message d'erreur
  if (error) {
    const errorElement = document.createElement('div');
    errorElement.className = 'form-error';
    
    if (i18nErrorKey) {
      errorElement.setAttribute('data-i18n', i18nErrorKey);
      errorElement.textContent = error;
    } else {
      errorElement.textContent = error;
    }
    
    fieldContainer.appendChild(errorElement);
  }
  
  // Ajouter le texte d'aide
  if (help) {
    const helpElement = document.createElement('div');
    helpElement.className = 'form-help';
    
    if (i18nHelpKey) {
      helpElement.setAttribute('data-i18n', i18nHelpKey);
      helpElement.textContent = help;
    } else {
      helpElement.textContent = help;
    }
    
    fieldContainer.appendChild(helpElement);
  }
  
  return fieldContainer;
}

/**
 * Crée un champ de case à cocher.
 * 
 * @param {Object} options - Options du champ
 * @param {string} options.id - ID du champ
 * @param {string} options.name - Nom du champ
 * @param {string} options.label - Libellé du champ
 * @param {boolean} options.checked - Si la case est cochée
 * @param {boolean} options.required - Si le champ est requis
 * @param {boolean} options.disabled - Si le champ est désactivé
 * @param {string} options.error - Message d'erreur
 * @param {string} options.help - Texte d'aide
 * @param {Function} options.onChange - Fonction à exécuter au changement
 * @param {string} options.i18nLabelKey - Clé de traduction pour le libellé
 * @param {string} options.i18nErrorKey - Clé de traduction pour le message d'erreur
 * @param {string} options.i18nHelpKey - Clé de traduction pour le texte d'aide
 * @returns {HTMLDivElement} - Élément div du champ
 */
export function createCheckboxField({
  id,
  name,
  label,
  checked = false,
  required = false,
  disabled = false,
  error = '',
  help = '',
  onChange = null,
  i18nLabelKey = null,
  i18nErrorKey = null,
  i18nHelpKey = null
}) {
  // Créer le conteneur du champ
  const fieldContainer = document.createElement('div');
  fieldContainer.className = 'form-field form-field-checkbox';
  
  if (error) {
    fieldContainer.classList.add('form-field-error');
  }
  
  // Créer le conteneur de la case à cocher
  const checkboxContainer = document.createElement('div');
  checkboxContainer.className = 'checkbox-container';
  
  // Créer la case à cocher
  const checkboxElement = document.createElement('input');
  checkboxElement.type = 'checkbox';
  checkboxElement.id = id;
  checkboxElement.name = name;
  checkboxElement.className = 'form-checkbox';
  checkboxElement.checked = checked;
  
  if (required) {
    checkboxElement.required = true;
  }
  
  if (disabled) {
    checkboxElement.disabled = true;
  }
  
  // Ajouter l'événement de changement
  if (onChange) {
    checkboxElement.addEventListener('change', onChange);
  }
  
  checkboxContainer.appendChild(checkboxElement);
  
  // Créer le libellé
  if (label) {
    const labelElement = document.createElement('label');
    labelElement.htmlFor = id;
    labelElement.className = 'form-label checkbox-label';
    
    if (i18nLabelKey) {
      labelElement.setAttribute('data-i18n', i18nLabelKey);
      labelElement.textContent = label;
    } else {
      labelElement.textContent = label;
    }
    
    if (required) {
      labelElement.classList.add('required');
      
      const requiredMark = document.createElement('span');
      requiredMark.className = 'required-mark';
      requiredMark.textContent = '*';
      labelElement.appendChild(requiredMark);
    }
    
    checkboxContainer.appendChild(labelElement);
  }
  
  fieldContainer.appendChild(checkboxContainer);
  
  // Ajouter le message d'erreur
  if (error) {
    const errorElement = document.createElement('div');
    errorElement.className = 'form-error';
    
    if (i18nErrorKey) {
      errorElement.setAttribute('data-i18n', i18nErrorKey);
      errorElement.textContent = error;
    } else {
      errorElement.textContent = error;
    }
    
    fieldContainer.appendChild(errorElement);
  }
  
  // Ajouter le texte d'aide
  if (help) {
    const helpElement = document.createElement('div');
    helpElement.className = 'form-help';
    
    if (i18nHelpKey) {
      helpElement.setAttribute('data-i18n', i18nHelpKey);
      helpElement.textContent = help;
    } else {
      helpElement.textContent = help;
    }
    
    fieldContainer.appendChild(helpElement);
  }
  
  return fieldContainer;
}

/**
 * Crée un formulaire.
 * 
 * @param {Object} options - Options du formulaire
 * @param {string} options.id - ID du formulaire
 * @param {string} options.action - Action du formulaire
 * @param {string} options.method - Méthode du formulaire
 * @param {Array} options.fields - Champs du formulaire
 * @param {Array} options.buttons - Boutons du formulaire
 * @param {Function} options.onSubmit - Fonction à exécuter à la soumission
 * @returns {HTMLFormElement} - Élément form du formulaire
 */
export function createForm({
  id,
  action = '',
  method = 'post',
  fields = [],
  buttons = [],
  onSubmit = null
}) {
  // Créer le formulaire
  const form = document.createElement('form');
  form.id = id;
  form.action = action;
  form.method = method;
  form.className = 'form';
  
  // Ajouter les champs
  fields.forEach(field => {
    let fieldElement;
    
    if (field.type === 'select') {
      fieldElement = createSelectField(field);
    } else if (field.type === 'checkbox') {
      fieldElement = createCheckboxField(field);
    } else {
      fieldElement = createFormField(field);
    }
    
    form.appendChild(fieldElement);
  });
  
  // Ajouter les boutons
  if (buttons.length > 0) {
    const buttonsContainer = document.createElement('div');
    buttonsContainer.className = 'form-buttons';
    
    buttons.forEach(buttonOptions => {
      const button = document.createElement('button');
      button.type = buttonOptions.type || 'button';
      button.className = `btn btn-${buttonOptions.variant || 'primary'}`;
      
      if (buttonOptions.i18nKey) {
        button.setAttribute('data-i18n', buttonOptions.i18nKey);
        button.textContent = buttonOptions.text;
      } else {
        button.textContent = buttonOptions.text;
      }
      
      if (buttonOptions.onClick) {
        button.addEventListener('click', buttonOptions.onClick);
      }
      
      buttonsContainer.appendChild(button);
    });
    
    form.appendChild(buttonsContainer);
  }
  
  // Ajouter l'événement de soumission
  if (onSubmit) {
    form.addEventListener('submit', async (event) => {
      event.preventDefault();
      
      // Valider le formulaire
      if (form.checkValidity()) {
        // Récupérer les données du formulaire
        const formData = new FormData(form);
        const data = {};
        
        for (const [key, value] of formData.entries()) {
          data[key] = value;
        }
        
        // Exécuter la fonction de soumission
        await onSubmit(data, event);
      } else {
        // Afficher les erreurs
        form.classList.add('form-invalid');
      }
    });
  }
  
  return form;
}

/**
 * Valide un champ de formulaire.
 * 
 * @param {HTMLInputElement|HTMLSelectElement|HTMLTextAreaElement} field - Champ à valider
 * @param {Object} options - Options de validation
 * @param {boolean} options.required - Si le champ est requis
 * @param {string} options.pattern - Pattern de validation
 * @param {number} options.minLength - Longueur minimale
 * @param {number} options.maxLength - Longueur maximale
 * @param {number} options.min - Valeur minimale
 * @param {number} options.max - Valeur maximale
 * @param {Function} options.customValidator - Fonction de validation personnalisée
 * @returns {Object} - Résultat de la validation
 */
export function validateField(field, options = {}) {
  const value = field.value;
  const result = {
    valid: true,
    error: ''
  };
  
  // Vérifier si le champ est requis
  if (options.required && !value) {
    result.valid = false;
    result.error = t('error.required');
    return result;
  }
  
  // Vérifier le pattern
  if (options.pattern && value && !new RegExp(options.pattern).test(value)) {
    result.valid = false;
    result.error = t('error.pattern');
    return result;
  }
  
  // Vérifier la longueur minimale
  if (options.minLength && value && value.length < options.minLength) {
    result.valid = false;
    result.error = t('error.minLength', { min: options.minLength });
    return result;
  }
  
  // Vérifier la longueur maximale
  if (options.maxLength && value && value.length > options.maxLength) {
    result.valid = false;
    result.error = t('error.maxLength', { max: options.maxLength });
    return result;
  }
  
  // Vérifier la valeur minimale
  if (options.min && value && parseFloat(value) < options.min) {
    result.valid = false;
    result.error = t('error.min', { min: options.min });
    return result;
  }
  
  // Vérifier la valeur maximale
  if (options.max && value && parseFloat(value) > options.max) {
    result.valid = false;
    result.error = t('error.max', { max: options.max });
    return result;
  }
  
  // Vérifier avec la fonction de validation personnalisée
  if (options.customValidator && value) {
    const customResult = options.customValidator(value);
    
    if (!customResult.valid) {
      result.valid = false;
      result.error = customResult.error;
      return result;
    }
  }
  
  return result;
}

export default {
  createFormField,
  createSelectField,
  createCheckboxField,
  createForm,
  validateField
};
