/**
 * Composant de sélection de langue.
 */

import { getLanguage, setLanguage, applyTranslations, getSupportedLanguages } from '../js/i18n.js';

/**
 * Crée un sélecteur de langue.
 * 
 * @param {string} containerId - ID du conteneur où ajouter le sélecteur
 * @returns {Promise} - Promise résolue lorsque le sélecteur est créé
 */
export async function createLanguageSelector(containerId = 'language-selector-container') {
  // Récupérer le conteneur
  const container = document.getElementById(containerId);
  
  if (!container) {
    console.warn(`Conteneur non trouvé: ${containerId}`);
    return;
  }
  
  // Récupérer la langue actuelle
  const currentLanguage = getLanguage();
  
  // Récupérer les langues supportées
  const languages = await getSupportedLanguages();
  
  // Créer le sélecteur
  const selector = document.createElement('select');
  selector.id = 'language-selector';
  selector.className = 'language-selector';
  
  // Ajouter les options
  languages.forEach(language => {
    const option = document.createElement('option');
    option.value = language.code;
    option.textContent = language.name;
    option.selected = language.code === currentLanguage;
    selector.appendChild(option);
  });
  
  // Ajouter l'événement de changement de langue
  selector.addEventListener('change', async (event) => {
    const language = event.target.value;
    
    // Changer la langue
    await setLanguage(language);
    
    // Appliquer les traductions
    applyTranslations();
  });
  
  // Créer le label
  const label = document.createElement('label');
  label.htmlFor = 'language-selector';
  label.textContent = 'Langue: ';
  label.className = 'language-selector-label';
  
  // Créer le wrapper
  const wrapper = document.createElement('div');
  wrapper.className = 'language-selector-wrapper';
  wrapper.appendChild(label);
  wrapper.appendChild(selector);
  
  // Ajouter le wrapper au conteneur
  container.appendChild(wrapper);
}

/**
 * Crée un sélecteur de langue sous forme de boutons.
 * 
 * @param {string} containerId - ID du conteneur où ajouter le sélecteur
 * @returns {Promise} - Promise résolue lorsque le sélecteur est créé
 */
export async function createLanguageButtons(containerId = 'language-buttons-container') {
  // Récupérer le conteneur
  const container = document.getElementById(containerId);
  
  if (!container) {
    console.warn(`Conteneur non trouvé: ${containerId}`);
    return;
  }
  
  // Récupérer la langue actuelle
  const currentLanguage = getLanguage();
  
  // Récupérer les langues supportées
  const languages = await getSupportedLanguages();
  
  // Créer le wrapper
  const wrapper = document.createElement('div');
  wrapper.className = 'language-buttons-wrapper';
  
  // Ajouter les boutons
  languages.forEach(language => {
    const button = document.createElement('button');
    button.type = 'button';
    button.className = `language-button ${language.code === currentLanguage ? 'active' : ''}`;
    button.dataset.language = language.code;
    button.textContent = language.name;
    
    // Ajouter l'événement de changement de langue
    button.addEventListener('click', async () => {
      // Changer la langue
      await setLanguage(language.code);
      
      // Mettre à jour les boutons
      document.querySelectorAll('.language-button').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.language === language.code);
      });
      
      // Appliquer les traductions
      applyTranslations();
    });
    
    wrapper.appendChild(button);
  });
  
  // Ajouter le wrapper au conteneur
  container.appendChild(wrapper);
}

/**
 * Crée un sélecteur de langue sous forme de drapeaux.
 * 
 * @param {string} containerId - ID du conteneur où ajouter le sélecteur
 * @returns {Promise} - Promise résolue lorsque le sélecteur est créé
 */
export async function createLanguageFlags(containerId = 'language-flags-container') {
  // Récupérer le conteneur
  const container = document.getElementById(containerId);
  
  if (!container) {
    console.warn(`Conteneur non trouvé: ${containerId}`);
    return;
  }
  
  // Récupérer la langue actuelle
  const currentLanguage = getLanguage();
  
  // Récupérer les langues supportées
  const languages = await getSupportedLanguages();
  
  // Mapping des codes de langue vers les codes de pays
  const countryMap = {
    fr: 'fr',
    en: 'gb',
    es: 'es',
    de: 'de',
    it: 'it'
  };
  
  // Créer le wrapper
  const wrapper = document.createElement('div');
  wrapper.className = 'language-flags-wrapper';
  
  // Ajouter les drapeaux
  languages.forEach(language => {
    const flag = document.createElement('button');
    flag.type = 'button';
    flag.className = `language-flag ${language.code === currentLanguage ? 'active' : ''}`;
    flag.dataset.language = language.code;
    flag.title = language.name;
    
    // Ajouter l'image du drapeau
    const img = document.createElement('img');
    img.src = `/static/images/flags/${countryMap[language.code] || language.code}.svg`;
    img.alt = language.name;
    img.width = 24;
    img.height = 16;
    flag.appendChild(img);
    
    // Ajouter l'événement de changement de langue
    flag.addEventListener('click', async () => {
      // Changer la langue
      await setLanguage(language.code);
      
      // Mettre à jour les drapeaux
      document.querySelectorAll('.language-flag').forEach(f => {
        f.classList.toggle('active', f.dataset.language === language.code);
      });
      
      // Appliquer les traductions
      applyTranslations();
    });
    
    wrapper.appendChild(flag);
  });
  
  // Ajouter le wrapper au conteneur
  container.appendChild(wrapper);
}
