<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exemple d'internationalisation</title>
    <link rel="stylesheet" href="/static/css/i18n.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 5px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        
        button {
            padding: 10px 15px;
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #3a80d2;
        }
        
        .language-options {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 data-i18n="common.welcome">Bienvenue sur Retreat & Be</h1>
        <div id="language-selector-container"></div>
    </div>
    
    <div class="section">
        <h2 data-i18n="retreat.title">Retraite</h2>
        <p data-i18n="retreat.description">Description</p>
        
        <div class="form-group">
            <label data-i18n="retreat.type" for="retreat-type">Type de retraite</label>
            <select id="retreat-type">
                <option value="yoga" data-i18n="retreat.type.yoga">Yoga</option>
                <option value="meditation" data-i18n="retreat.type.meditation">Méditation</option>
                <option value="wellness" data-i18n="retreat.type.wellness">Bien-être</option>
            </select>
        </div>
        
        <div class="form-group">
            <label data-i18n="retreat.location" for="retreat-location">Lieu</label>
            <input type="text" id="retreat-location" data-i18n-placeholder="retreat.location.placeholder">
        </div>
        
        <div class="form-group">
            <label data-i18n="retreat.startDate" for="retreat-start-date">Date de début</label>
            <input type="date" id="retreat-start-date">
        </div>
        
        <div class="form-group">
            <label data-i18n="retreat.endDate" for="retreat-end-date">Date de fin</label>
            <input type="date" id="retreat-end-date">
        </div>
        
        <button data-i18n="retreat.bookNow">Réserver maintenant</button>
    </div>
    
    <div class="section">
        <h2 data-i18n="booking.title">Réservation</h2>
        
        <div class="form-group">
            <label data-i18n="common.fullName" for="booking-name">Nom complet</label>
            <input type="text" id="booking-name" data-i18n-placeholder="common.fullName.placeholder">
        </div>
        
        <div class="form-group">
            <label data-i18n="common.email" for="booking-email">Email</label>
            <input type="email" id="booking-email" data-i18n-placeholder="common.email.placeholder">
        </div>
        
        <div class="form-group">
            <label data-i18n="booking.participants" for="booking-participants">Participants</label>
            <input type="number" id="booking-participants" min="1" value="1">
        </div>
        
        <div class="form-group">
            <label data-i18n="booking.roomType" for="booking-room-type">Type de chambre</label>
            <select id="booking-room-type">
                <option value="single" data-i18n="booking.singleRoom">Chambre simple</option>
                <option value="double" data-i18n="booking.doubleRoom">Chambre double</option>
                <option value="shared" data-i18n="booking.sharedRoom">Chambre partagée</option>
            </select>
        </div>
        
        <div class="form-group">
            <label data-i18n="booking.specialRequests" for="booking-special-requests">Demandes spéciales</label>
            <textarea id="booking-special-requests" data-i18n-placeholder="booking.specialRequests.placeholder"></textarea>
        </div>
        
        <button data-i18n="common.confirm">Confirmer</button>
    </div>
    
    <div class="language-options">
        <div>
            <h3>Sélecteur de langue (select)</h3>
            <div id="language-selector-container-2"></div>
        </div>
        
        <div>
            <h3>Sélecteur de langue (boutons)</h3>
            <div id="language-buttons-container"></div>
        </div>
        
        <div>
            <h3>Sélecteur de langue (drapeaux)</h3>
            <div id="language-flags-container"></div>
        </div>
    </div>
    
    <script type="module">
        import { init, applyTranslations } from '/js/i18n.js';
        import { createLanguageSelector, createLanguageButtons, createLanguageFlags } from '/components/LanguageSelector.js';
        
        // Initialiser le module d'internationalisation
        await init();
        
        // Créer les sélecteurs de langue
        await createLanguageSelector('language-selector-container');
        await createLanguageSelector('language-selector-container-2');
        await createLanguageButtons('language-buttons-container');
        await createLanguageFlags('language-flags-container');
        
        // Appliquer les traductions
        applyTranslations();
    </script>
</body>
</html>
