{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Retraites</h5>
                <p class="card-text display-4">{{ stats.retreat_count }}</p>
                <a href="/admin/retreats" class="btn btn-primary">Voir toutes les retraites</a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Partenaires</h5>
                <p class="card-text display-4">{{ stats.partner_count }}</p>
                <a href="/admin/partners" class="btn btn-primary">Voir tous les partenaires</a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Clients</h5>
                <p class="card-text display-4">{{ stats.client_count }}</p>
                <a href="/admin/clients" class="btn btn-primary">Voir tous les clients</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Réservations récentes</h5>
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Client</th>
                                <th>Retraite</th>
                                <th>Statut</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for booking in recent_bookings %}
                            <tr>
                                <td><a href="/admin/bookings/{{ booking.id }}">{{ booking.id }}</a></td>
                                <td>{{ booking.client_id }}</td>
                                <td>{{ booking.retreat_id }}</td>
                                <td>
                                    {% if booking.status == 'confirmed' %}
                                    <span class="badge bg-success">Confirmée</span>
                                    {% elif booking.status == 'pending' %}
                                    <span class="badge bg-warning">En attente</span>
                                    {% elif booking.status == 'cancelled' %}
                                    <span class="badge bg-danger">Annulée</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ booking.status }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ booking.booking_date }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <a href="/admin/bookings" class="btn btn-primary">Voir toutes les réservations</a>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Retraites récentes</h5>
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Titre</th>
                                <th>Type</th>
                                <th>Statut</th>
                                <th>Date de début</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for retreat in recent_retreats %}
                            <tr>
                                <td><a href="/admin/retreats/{{ retreat.id }}">{{ retreat.id }}</a></td>
                                <td>{{ retreat.title }}</td>
                                <td>{{ retreat.retreat_type }}</td>
                                <td>
                                    {% if retreat.status == 'published' %}
                                    <span class="badge bg-success">Publiée</span>
                                    {% elif retreat.status == 'draft' %}
                                    <span class="badge bg-warning">Brouillon</span>
                                    {% elif retreat.status == 'cancelled' %}
                                    <span class="badge bg-danger">Annulée</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ retreat.status }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ retreat.start_date }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <a href="/admin/retreats" class="btn btn-primary">Voir toutes les retraites</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Graphique des réservations par mois (à implémenter)
    // var ctx = document.getElementById('bookingsChart').getContext('2d');
    // var bookingsChart = new Chart(ctx, { ... });
</script>
{% endblock %}
