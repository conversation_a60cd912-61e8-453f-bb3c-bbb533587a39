"""
Routes pour la gestion des retraites dans l'interface d'administration.
"""

import logging
import uuid
from datetime import datetime
from fastapi import APIRouter, Depends, Request, HTTPException, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from pathlib import Path
from typing import Optional, List

from src.models.retreat import Retreat

from src.auth.dependencies import get_current_admin_user
from src.auth.models import User
from src.database.repositories.retreat_repository import RetreatRepository
from src.database.repositories.partner_repository import PartnerRepository
from src.models.retreat import RetreatStatus, RetreatType

# Configuration du logging
logger = logging.getLogger(__name__)

# Créer le router
router = APIRouter()

# Chemin vers les templates
templates_path = Path(__file__).parent.parent / "templates"
templates = Jinja2Templates(directory=str(templates_path))

# Créer les repositories
retreat_repo = RetreatRepository()
partner_repo = PartnerRepository()

@router.get("/", response_class=HTMLResponse)
async def list_retreats(
    request: Request,
    current_user: User = Depends(get_current_admin_user),
    status: Optional[str] = None,
    retreat_type: Optional[str] = None,
    page: int = 1,
    limit: int = 10
):
    """
    Liste des retraites.
    """
    try:
        # Calculer l'offset pour la pagination
        offset = (page - 1) * limit

        # Récupérer les retraites
        if status:
            retreats = retreat_repo.find_by_status(RetreatStatus(status), limit, offset)
            total = retreat_repo.count({"status": status})
        elif retreat_type:
            retreats = retreat_repo.find_by_type(RetreatType(retreat_type), limit, offset)
            total = retreat_repo.count({"retreat_type": retreat_type})
        else:
            retreats = retreat_repo.get_all(limit, offset)
            total = retreat_repo.count()

        # Calculer le nombre de pages
        pages = (total + limit - 1) // limit

        return templates.TemplateResponse(
            "retreats/list.html",
            {
                "request": request,
                "user": current_user,
                "title": "Retraites",
                "retreats": retreats,
                "status": status,
                "retreat_type": retreat_type,
                "page": page,
                "limit": limit,
                "total": total,
                "pages": pages,
                "statuses": [status.value for status in RetreatStatus],
                "retreat_types": [retreat_type.value for retreat_type in RetreatType]
            }
        )

    except Exception as e:
        logger.error(f"Erreur lors de l'affichage de la liste des retraites: {str(e)}")
        raise HTTPException(status_code=500, detail="Erreur lors de l'affichage de la liste des retraites")

@router.get("/{retreat_id}", response_class=HTMLResponse)
async def view_retreat(
    request: Request,
    retreat_id: str,
    current_user: User = Depends(get_current_admin_user)
):
    """
    Détails d'une retraite.
    """
    try:
        # Récupérer la retraite
        retreat = retreat_repo.get_by_id(retreat_id)
        if not retreat:
            raise HTTPException(status_code=404, detail="Retraite non trouvée")

        # Récupérer l'organisateur
        organizer = None
        if retreat.organizer_id:
            organizer = partner_repo.get_by_id(retreat.organizer_id)

        return templates.TemplateResponse(
            "retreats/view.html",
            {
                "request": request,
                "user": current_user,
                "title": f"Retraite: {retreat.title}",
                "retreat": retreat,
                "organizer": organizer
            }
        )

    except HTTPException:
        raise

    except Exception as e:
        logger.error(f"Erreur lors de l'affichage des détails de la retraite: {str(e)}")
        raise HTTPException(status_code=500, detail="Erreur lors de l'affichage des détails de la retraite")

@router.get("/edit/{retreat_id}", response_class=HTMLResponse)
async def edit_retreat_form(
    request: Request,
    retreat_id: str,
    current_user: User = Depends(get_current_admin_user)
):
    """
    Formulaire de modification d'une retraite.
    """
    try:
        # Récupérer la retraite
        retreat = retreat_repo.get_by_id(retreat_id)
        if not retreat:
            raise HTTPException(status_code=404, detail="Retraite non trouvée")

        # Récupérer les partenaires pour le sélecteur d'organisateur
        partners = partner_repo.find_by_type("retreat_organizer")

        return templates.TemplateResponse(
            "retreats/edit.html",
            {
                "request": request,
                "user": current_user,
                "title": f"Modifier la retraite: {retreat.title}",
                "retreat": retreat,
                "partners": partners,
                "statuses": [status.value for status in RetreatStatus],
                "retreat_types": [retreat_type.value for retreat_type in RetreatType]
            }
        )

    except HTTPException:
        raise

    except Exception as e:
        logger.error(f"Erreur lors de l'affichage du formulaire de modification de la retraite: {str(e)}")
        raise HTTPException(status_code=500, detail="Erreur lors de l'affichage du formulaire de modification de la retraite")

@router.post("/edit/{retreat_id}", response_class=HTMLResponse)
async def edit_retreat(
    request: Request,
    retreat_id: str,
    title: str = Form(...),
    description: str = Form(...),
    retreat_type: str = Form(...),
    status: str = Form(...),
    start_date: str = Form(...),
    end_date: str = Form(...),
    capacity: int = Form(...),
    organizer_id: Optional[str] = Form(None),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Modification d'une retraite.
    """
    try:
        # Récupérer la retraite
        retreat = retreat_repo.get_by_id(retreat_id)
        if not retreat:
            raise HTTPException(status_code=404, detail="Retraite non trouvée")

        # Mettre à jour les champs
        retreat.title = title
        retreat.description = description
        retreat.retreat_type = RetreatType(retreat_type)
        retreat.status = RetreatStatus(status)
        retreat.start_date = start_date
        retreat.end_date = end_date
        retreat.capacity = capacity
        retreat.organizer_id = organizer_id

        # Sauvegarder les modifications
        updated_retreat = retreat_repo.update(retreat)

        # Rediriger vers la page de détails
        return RedirectResponse(url=f"/admin/retreats/{retreat_id}", status_code=303)

    except HTTPException:
        raise

    except Exception as e:
        logger.error(f"Erreur lors de la modification de la retraite: {str(e)}")
        raise HTTPException(status_code=500, detail="Erreur lors de la modification de la retraite")

@router.get("/create", response_class=HTMLResponse)
async def create_retreat_form(
    request: Request,
    current_user: User = Depends(get_current_admin_user)
):
    """
    Formulaire de création d'une retraite.
    """
    try:
        # Récupérer les partenaires pour le sélecteur d'organisateur
        partners = partner_repo.find_by_type("retreat_organizer")

        return templates.TemplateResponse(
            "retreats/create.html",
            {
                "request": request,
                "user": current_user,
                "title": "Créer une retraite",
                "partners": partners,
                "statuses": [status.value for status in RetreatStatus],
                "retreat_types": [retreat_type.value for retreat_type in RetreatType]
            }
        )

    except Exception as e:
        logger.error(f"Erreur lors de l'affichage du formulaire de création de retraite: {str(e)}")
        raise HTTPException(status_code=500, detail="Erreur lors de l'affichage du formulaire de création de retraite")

@router.post("/create", response_class=HTMLResponse)
async def create_retreat(
    request: Request,
    title: str = Form(...),
    description: str = Form(...),
    retreat_type: str = Form(...),
    status: str = Form(...),
    start_date: str = Form(...),
    end_date: str = Form(...),
    capacity: int = Form(...),
    organizer_id: Optional[str] = Form(None),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Création d'une retraite.
    """
    try:
        # Créer la retraite
        retreat = Retreat(
            id=f"retreat_{uuid.uuid4().hex[:8]}",
            title=title,
            description=description,
            retreat_type=RetreatType(retreat_type),
            status=RetreatStatus(status),
            start_date=start_date,
            end_date=end_date,
            capacity=capacity,
            organizer_id=organizer_id,
            location={"country": "France", "region": "Provence"},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        # Sauvegarder la retraite
        created_retreat = retreat_repo.create(retreat)

        # Rediriger vers la page de détails
        return RedirectResponse(url=f"/admin/retreats/{created_retreat.id}", status_code=303)

    except Exception as e:
        logger.error(f"Erreur lors de la création de la retraite: {str(e)}")
        raise HTTPException(status_code=500, detail="Erreur lors de la création de la retraite")
