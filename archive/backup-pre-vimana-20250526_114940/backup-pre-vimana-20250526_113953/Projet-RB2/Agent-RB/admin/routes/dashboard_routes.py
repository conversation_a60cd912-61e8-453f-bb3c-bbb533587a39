"""
Routes pour le tableau de bord de l'interface d'administration.
"""

import logging
from fastapi import APIRouter, Depends, Request, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from pathlib import Path

from src.auth.dependencies import get_current_admin_user
from src.auth.models import User
from src.database.repositories.retreat_repository import RetreatRepository
from src.database.repositories.partner_repository import PartnerRepository
from src.database.repositories.client_repository import ClientRepository
from src.database.repositories.booking_repository import BookingRepository
from src.database.repositories.user_repository import UserRepository

# Configuration du logging
logger = logging.getLogger(__name__)

# Créer le router
router = APIRouter()

# Chemin vers les templates
templates_path = Path(__file__).parent.parent / "templates"
templates = Jinja2Templates(directory=str(templates_path))

# Créer les repositories
retreat_repo = RetreatRepository()
partner_repo = PartnerRepository()
client_repo = ClientRepository()
booking_repo = BookingRepository()
user_repo = UserRepository()

@router.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request, current_user: User = Depends(get_current_admin_user)):
    """
    Tableau de bord de l'interface d'administration.
    """
    try:
        # Récupérer les statistiques
        retreat_count = retreat_repo.count()
        partner_count = partner_repo.count()
        client_count = client_repo.count()
        booking_count = booking_repo.count()
        user_count = user_repo.count()
        
        # Récupérer les dernières réservations
        recent_bookings = booking_repo.get_all(limit=5)
        
        # Récupérer les dernières retraites
        recent_retreats = retreat_repo.get_all(limit=5)
        
        return templates.TemplateResponse(
            "dashboard.html",
            {
                "request": request,
                "user": current_user,
                "title": "Tableau de bord",
                "stats": {
                    "retreat_count": retreat_count,
                    "partner_count": partner_count,
                    "client_count": client_count,
                    "booking_count": booking_count,
                    "user_count": user_count
                },
                "recent_bookings": recent_bookings,
                "recent_retreats": recent_retreats
            }
        )
    
    except Exception as e:
        logger.error(f"Erreur lors de l'affichage du tableau de bord: {str(e)}")
        raise HTTPException(status_code=500, detail="Erreur lors de l'affichage du tableau de bord")
