import axios from 'axios';
import { io, Socket } from 'socket.io-client';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';
const SOCKET_URL = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3002';

export type NotificationType = 
  | 'like'
  | 'comment'
  | 'follow'
  | 'mention'
  | 'share'
  | 'collaboration_request'
  | 'collaboration_accepted'
  | 'collaboration_declined'
  | 'subscription'
  | 'tip'
  | 'payment'
  | 'payout'
  | 'message'
  | 'system'
  | 'content_published'
  | 'content_trending'
  | 'achievement';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  data?: {
    userId?: string;
    username?: string;
    name?: string;
    avatar?: string;
    contentId?: string;
    contentType?: 'video' | 'post' | 'story' | 'livestream';
    contentTitle?: string;
    thumbnailUrl?: string;
    amount?: number;
    currency?: string;
    collaborationId?: string;
    messageId?: string;
    achievementId?: string;
    achievementTitle?: string;
    url?: string;
    [key: string]: any;
  };
}

export interface NotificationSettings {
  enabled: boolean;
  pushEnabled: boolean;
  emailEnabled: boolean;
  inAppEnabled: boolean;
  types: {
    [key in NotificationType]?: {
      enabled: boolean;
      push: boolean;
      email: boolean;
      inApp: boolean;
    };
  };
  digestFrequency: 'never' | 'daily' | 'weekly';
  quietHoursEnabled: boolean;
  quietHoursStart: string; // HH:MM format
  quietHoursEnd: string; // HH:MM format
}

let socket: Socket | null = null;

// Initialize socket connection
export const initializeNotificationsSocket = (token: string): Socket => {
  if (socket) {
    socket.disconnect();
  }
  
  socket = io(`${SOCKET_URL}/notifications`, {
    auth: {
      token,
    },
  });
  
  socket.on('connect', () => {
    console.log('Connected to notifications socket');
  });
  
  socket.on('disconnect', () => {
    console.log('Disconnected from notifications socket');
  });
  
  socket.on('error', (error) => {
    console.error('Socket error:', error);
  });
  
  return socket;
};

// Disconnect socket
export const disconnectNotificationsSocket = (): void => {
  if (socket) {
    socket.disconnect();
    socket = null;
  }
};

// Get notifications
export const getNotifications = async (
  page: number = 1,
  limit: number = 20,
  filter?: NotificationType | 'unread'
): Promise<{
  notifications: Notification[];
  totalCount: number;
  unreadCount: number;
  page: number;
  totalPages: number;
}> => {
  try {
    const response = await axios.get(`${API_URL}/notifications`, {
      params: { page, limit, filter },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching notifications:', error);
    throw error;
  }
};

// Mark notification as read
export const markNotificationAsRead = async (
  notificationId: string
): Promise<Notification> => {
  try {
    const response = await axios.put(`${API_URL}/notifications/${notificationId}/read`);
    return response.data;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
};

// Mark all notifications as read
export const markAllNotificationsAsRead = async (): Promise<{
  success: boolean;
  count: number;
}> => {
  try {
    const response = await axios.put(`${API_URL}/notifications/read-all`);
    return response.data;
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    throw error;
  }
};

// Delete notification
export const deleteNotification = async (
  notificationId: string
): Promise<{ success: boolean }> => {
  try {
    const response = await axios.delete(`${API_URL}/notifications/${notificationId}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting notification:', error);
    throw error;
  }
};

// Delete all notifications
export const deleteAllNotifications = async (): Promise<{
  success: boolean;
  count: number;
}> => {
  try {
    const response = await axios.delete(`${API_URL}/notifications`);
    return response.data;
  } catch (error) {
    console.error('Error deleting all notifications:', error);
    throw error;
  }
};

// Get notification settings
export const getNotificationSettings = async (): Promise<NotificationSettings> => {
  try {
    const response = await axios.get(`${API_URL}/notifications/settings`);
    return response.data;
  } catch (error) {
    console.error('Error fetching notification settings:', error);
    throw error;
  }
};

// Update notification settings
export const updateNotificationSettings = async (
  settings: Partial<NotificationSettings>
): Promise<NotificationSettings> => {
  try {
    const response = await axios.put(`${API_URL}/notifications/settings`, settings);
    return response.data;
  } catch (error) {
    console.error('Error updating notification settings:', error);
    throw error;
  }
};

// Update notification type settings
export const updateNotificationTypeSettings = async (
  type: NotificationType,
  settings: {
    enabled?: boolean;
    push?: boolean;
    email?: boolean;
    inApp?: boolean;
  }
): Promise<NotificationSettings> => {
  try {
    const response = await axios.put(`${API_URL}/notifications/settings/types/${type}`, settings);
    return response.data;
  } catch (error) {
    console.error(`Error updating ${type} notification settings:`, error);
    throw error;
  }
};

// Register push notification device token
export const registerPushToken = async (
  token: string,
  deviceType: 'ios' | 'android' | 'web'
): Promise<{ success: boolean }> => {
  try {
    const response = await axios.post(`${API_URL}/notifications/push-token`, {
      token,
      deviceType,
    });
    return response.data;
  } catch (error) {
    console.error('Error registering push token:', error);
    throw error;
  }
};

// Unregister push notification device token
export const unregisterPushToken = async (
  token: string
): Promise<{ success: boolean }> => {
  try {
    const response = await axios.delete(`${API_URL}/notifications/push-token`, {
      data: { token },
    });
    return response.data;
  } catch (error) {
    console.error('Error unregistering push token:', error);
    throw error;
  }
};

// Test push notification
export const testPushNotification = async (): Promise<{ success: boolean }> => {
  try {
    const response = await axios.post(`${API_URL}/notifications/test-push`);
    return response.data;
  } catch (error) {
    console.error('Error sending test push notification:', error);
    throw error;
  }
};

// Get notification statistics
export const getNotificationStatistics = async (): Promise<{
  totalCount: number;
  unreadCount: number;
  typeCounts: {
    [key in NotificationType]?: number;
  };
  weeklyStats: Array<{
    date: string;
    count: number;
  }>;
}> => {
  try {
    const response = await axios.get(`${API_URL}/notifications/statistics`);
    return response.data;
  } catch (error) {
    console.error('Error fetching notification statistics:', error);
    throw error;
  }
};

// Subscribe to real-time notifications
export const subscribeToNotifications = (
  callback: (notification: Notification) => void
): (() => void) => {
  if (!socket) {
    throw new Error('Socket not initialized. Call initializeNotificationsSocket first.');
  }
  
  socket.on('notification', callback);
  
  return () => {
    socket.off('notification', callback);
  };
};

// Subscribe to notification count updates
export const subscribeToNotificationCount = (
  callback: (counts: { total: number; unread: number }) => void
): (() => void) => {
  if (!socket) {
    throw new Error('Socket not initialized. Call initializeNotificationsSocket first.');
  }
  
  socket.on('notification_count', callback);
  
  return () => {
    socket.off('notification_count', callback);
  };
};
