import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';

// Types
export type RecurrencePattern = 'none' | 'daily' | 'weekly' | 'monthly';

export interface RecurrenceSettings {
  pattern: RecurrencePattern;
  interval: number; // e.g., every 2 days, every 3 weeks
  daysOfWeek?: number[]; // 0 = Sunday, 1 = Monday, etc.
  dayOfMonth?: number; // 1-31
  endDate?: string; // ISO date string
  occurrences?: number; // Number of times to repeat
}

export interface ScheduledPost {
  id: string;
  title: string;
  description?: string;
  thumbnailUrl?: string;
  createdAt: string;
  scheduledDate: string;
  type: 'video' | 'post' | 'livestream' | 'blog';
  status: 'scheduled' | 'processing' | 'error';
  errorMessage?: string;
  recurrence?: RecurrenceSettings;
  nextOccurrence?: string; // Next scheduled date if recurring
  occurrenceCount?: number; // How many times it has been published
  parentScheduleId?: string; // For recurring instances, points to the original schedule
  stats?: {
    views?: number;
    likes?: number;
    comments?: number;
  };
}

// Archive a post
export const archivePost = async (postId: string): Promise<void> => {
  try {
    await axios.post(`${API_URL}/posts/${postId}/archive`);
  } catch (error) {
    console.error('Error archiving post:', error);
    throw error;
  }
};

// Restore an archived post
export const restorePost = async (postId: string): Promise<void> => {
  try {
    await axios.post(`${API_URL}/posts/${postId}/restore`);
  } catch (error) {
    console.error('Error restoring post:', error);
    throw error;
  }
};

// Delete a post
export const deletePost = async (postId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/posts/${postId}`);
  } catch (error) {
    console.error('Error deleting post:', error);
    throw error;
  }
};

// Get archived posts
export const getArchivedPosts = async (page = 1, limit = 10): Promise<any[]> => {
  try {
    const response = await axios.get(`${API_URL}/posts/archived`, {
      params: { page, limit },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching archived posts:', error);
    throw error;
  }
};

// Report a post
export const reportPost = async (postId: string, reason: string): Promise<void> => {
  try {
    await axios.post(`${API_URL}/posts/${postId}/report`, { reason });
  } catch (error) {
    console.error('Error reporting post:', error);
    throw error;
  }
};

// Update a post
export const updatePost = async (postId: string, data: any): Promise<any> => {
  try {
    const response = await axios.put(`${API_URL}/posts/${postId}`, data);
    return response.data;
  } catch (error) {
    console.error('Error updating post:', error);
    throw error;
  }
};

// Get post details
export const getPostDetails = async (postId: string): Promise<any> => {
  try {
    const response = await axios.get(`${API_URL}/posts/${postId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching post details:', error);
    throw error;
  }
};

// Like a post
export const likePost = async (postId: string): Promise<{ likes: number; isLiked: boolean }> => {
  try {
    const response = await axios.post(`${API_URL}/posts/${postId}/like`);
    return response.data;
  } catch (error) {
    console.error('Error liking post:', error);
    throw error;
  }
};

// Save a post
export const savePost = async (postId: string): Promise<{ isSaved: boolean }> => {
  try {
    const response = await axios.post(`${API_URL}/posts/${postId}/save`);
    return response.data;
  } catch (error) {
    console.error('Error saving post:', error);
    throw error;
  }
};

// Share a post
export const sharePost = async (postId: string, platform?: string): Promise<{ shares: number }> => {
  try {
    const response = await axios.post(`${API_URL}/posts/${postId}/share`, { platform });
    return response.data;
  } catch (error) {
    console.error('Error sharing post:', error);
    throw error;
  }
};

// Schedule a post for future publication
export const schedulePost = async (
  postId: string,
  scheduledDate: string,
  recurrence?: RecurrenceSettings
): Promise<ScheduledPost> => {
  try {
    const response = await axios.post(`${API_URL}/posts/${postId}/schedule`, {
      scheduledDate,
      recurrence
    });
    return response.data;
  } catch (error) {
    console.error('Error scheduling post:', error);
    throw error;
  }
};

// Schedule multiple posts for future publication
export const schedulePosts = async (
  postIds: string[],
  scheduledDate: string,
  recurrence?: RecurrenceSettings
): Promise<ScheduledPost[]> => {
  try {
    const response = await axios.post(`${API_URL}/posts/schedule-batch`, {
      postIds,
      scheduledDate,
      recurrence
    });
    return response.data;
  } catch (error) {
    console.error('Error scheduling posts:', error);
    throw error;
  }
};

// Update recurrence settings for a scheduled post
export const updateRecurrenceSettings = async (
  scheduleId: string,
  recurrence: RecurrenceSettings
): Promise<ScheduledPost> => {
  try {
    const response = await axios.put(`${API_URL}/posts/schedule/${scheduleId}/recurrence`, { recurrence });
    return response.data;
  } catch (error) {
    console.error('Error updating recurrence settings:', error);
    throw error;
  }
};

// Get scheduled posts
export const getScheduledPosts = async (page = 1, limit = 20): Promise<ScheduledPost[]> => {
  try {
    const response = await axios.get(`${API_URL}/posts/scheduled`, {
      params: { page, limit },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching scheduled posts:', error);
    throw error;
  }
};

// Cancel scheduled post
export const cancelScheduledPost = async (postId: string): Promise<void> => {
  try {
    await axios.post(`${API_URL}/posts/${postId}/cancel-schedule`);
  } catch (error) {
    console.error('Error canceling scheduled post:', error);
    throw error;
  }
};

// Cancel multiple scheduled posts
export const cancelScheduledPosts = async (postIds: string[]): Promise<void> => {
  try {
    await axios.post(`${API_URL}/posts/cancel-schedule-batch`, { postIds });
  } catch (error) {
    console.error('Error canceling scheduled posts:', error);
    throw error;
  }
};

// Publish scheduled post immediately
export const publishScheduledPost = async (postId: string): Promise<void> => {
  try {
    await axios.post(`${API_URL}/posts/${postId}/publish-now`);
  } catch (error) {
    console.error('Error publishing scheduled post:', error);
    throw error;
  }
};

// Publish multiple scheduled posts immediately
export const publishScheduledPosts = async (postIds: string[]): Promise<void> => {
  try {
    await axios.post(`${API_URL}/posts/publish-now-batch`, { postIds });
  } catch (error) {
    console.error('Error publishing scheduled posts:', error);
    throw error;
  }
};
