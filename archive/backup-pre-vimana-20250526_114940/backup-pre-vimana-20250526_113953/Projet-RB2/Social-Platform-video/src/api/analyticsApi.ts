import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';

export interface AnalyticsOverview {
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  totalShares: number;
  totalFollowers: number;
  newFollowers: number;
  viewsGrowth: number; // percentage
  likesGrowth: number; // percentage
  commentsGrowth: number; // percentage
  sharesGrowth: number; // percentage
  followersGrowth: number; // percentage
}

export interface ViewsData {
  date: string;
  views: number;
}

export interface EngagementData {
  date: string;
  likes: number;
  comments: number;
  shares: number;
}

export interface AudienceDemographics {
  ageGroups: Array<{
    group: string;
    percentage: number;
  }>;
  genders: Array<{
    gender: string;
    percentage: number;
  }>;
  locations: Array<{
    country: string;
    percentage: number;
  }>;
  interests: Array<{
    interest: string;
    percentage: number;
  }>;
}

export interface ContentPerformanceItem {
  id: string;
  title: string;
  thumbnailUrl: string;
  type: 'video' | 'post' | 'story';
  createdAt: string;
  stats: {
    views: number;
    likes: number;
    comments: number;
    shares: number;
    completionRate?: number; // for videos
    averageWatchTime?: number; // for videos, in seconds
  };
}

export interface RevenueData {
  date: string;
  amount: number;
  currency: string;
}

// Get analytics overview
export const getAnalyticsOverview = async (
  timeRange: 'day' | 'week' | 'month' | 'year' = 'week'
): Promise<AnalyticsOverview> => {
  try {
    const response = await axios.get(`${API_URL}/analytics/overview`, {
      params: { timeRange },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching analytics overview:', error);
    throw error;
  }
};

// Get views data
export const getViewsData = async (
  timeRange: 'day' | 'week' | 'month' | 'year' = 'week',
  granularity: 'hour' | 'day' | 'week' | 'month' = 'day'
): Promise<ViewsData[]> => {
  try {
    const response = await axios.get(`${API_URL}/analytics/views`, {
      params: { timeRange, granularity },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching views data:', error);
    throw error;
  }
};

// Get engagement data
export const getEngagementData = async (
  timeRange: 'day' | 'week' | 'month' | 'year' = 'week',
  granularity: 'hour' | 'day' | 'week' | 'month' = 'day'
): Promise<EngagementData[]> => {
  try {
    const response = await axios.get(`${API_URL}/analytics/engagement`, {
      params: { timeRange, granularity },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching engagement data:', error);
    throw error;
  }
};

// Get audience demographics
export const getAudienceDemographics = async (): Promise<AudienceDemographics> => {
  try {
    const response = await axios.get(`${API_URL}/analytics/audience`);
    return response.data;
  } catch (error) {
    console.error('Error fetching audience demographics:', error);
    throw error;
  }
};

// Get content performance
export const getContentPerformance = async (
  timeRange: 'day' | 'week' | 'month' | 'year' = 'month',
  contentType?: 'video' | 'post' | 'story',
  limit = 10,
  sortBy: 'views' | 'likes' | 'comments' | 'shares' | 'completionRate' = 'views'
): Promise<ContentPerformanceItem[]> => {
  try {
    const response = await axios.get(`${API_URL}/analytics/content`, {
      params: { timeRange, contentType, limit, sortBy },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching content performance:', error);
    throw error;
  }
};

// Get revenue data (for monetized accounts)
export const getRevenueData = async (
  timeRange: 'day' | 'week' | 'month' | 'year' = 'month',
  granularity: 'day' | 'week' | 'month' = 'day'
): Promise<RevenueData[]> => {
  try {
    const response = await axios.get(`${API_URL}/analytics/revenue`, {
      params: { timeRange, granularity },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching revenue data:', error);
    throw error;
  }
};

// Get detailed analytics for a specific content item
export const getContentItemAnalytics = async (
  contentId: string,
  contentType: 'video' | 'post' | 'story'
): Promise<{
  views: ViewsData[];
  engagement: EngagementData[];
  audience: AudienceDemographics;
  watchTimeDistribution?: Array<{ segment: string; percentage: number }>; // for videos
  referrers: Array<{ source: string; percentage: number }>;
}> => {
  try {
    const response = await axios.get(`${API_URL}/analytics/${contentType}s/${contentId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching content item analytics:', error);
    throw error;
  }
};

// Export analytics data
export const exportAnalyticsData = async (
  dataType: 'overview' | 'views' | 'engagement' | 'audience' | 'content' | 'revenue',
  timeRange: 'day' | 'week' | 'month' | 'year' = 'month',
  format: 'csv' | 'json' | 'pdf' = 'csv'
): Promise<Blob> => {
  try {
    const response = await axios.get(`${API_URL}/analytics/export`, {
      params: { dataType, timeRange, format },
      responseType: 'blob',
    });
    return response.data;
  } catch (error) {
    console.error('Error exporting analytics data:', error);
    throw error;
  }
};
