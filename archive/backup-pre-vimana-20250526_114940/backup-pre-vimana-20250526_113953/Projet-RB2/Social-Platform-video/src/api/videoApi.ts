import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';

export interface VideoMetadata {
  id: string;
  title: string;
  description: string;
  duration: number;
  thumbnailUrl: string;
  videoUrl: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  views: number;
  likes: number;
  shares: number;
  comments: number;
  tags: string[];
  category: string;
}

export interface VideoEditOptions {
  trim?: {
    start: number;
    end: number;
  };
  filters?: {
    brightness?: number;
    contrast?: number;
    saturation?: number;
    blur?: number;
    sepia?: number;
  };
  textOverlays?: Array<{
    text: string;
    position: { x: number; y: number };
    fontSize: number;
    color: string;
    startTime?: number;
    endTime?: number;
  }>;
  audioTrack?: {
    url: string;
    volume: number;
    startTime: number;
  };
}

// Upload a new video
export const uploadVideo = async (
  file: File,
  metadata: {
    title: string;
    description?: string;
    tags?: string[];
    category?: string;
  }
): Promise<{ id: string; uploadUrl: string }> => {
  try {
    // First, create a video entry and get an upload URL
    const response = await axios.post(`${API_URL}/videos`, metadata);
    
    // Then upload the file to the provided URL
    const { id, uploadUrl } = response.data;
    
    // Create form data for the file
    const formData = new FormData();
    formData.append('video', file);
    
    // Upload the file
    await axios.put(uploadUrl, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        // You can track upload progress here
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / (progressEvent.total || 1)
        );
        console.log(`Upload progress: ${percentCompleted}%`);
      },
    });
    
    return { id, uploadUrl };
  } catch (error) {
    console.error('Error uploading video:', error);
    throw error;
  }
};

// Get video details
export const getVideoDetails = async (videoId: string): Promise<VideoMetadata> => {
  try {
    const response = await axios.get(`${API_URL}/videos/${videoId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching video details:', error);
    throw error;
  }
};

// Update video metadata
export const updateVideoMetadata = async (
  videoId: string,
  metadata: {
    title?: string;
    description?: string;
    tags?: string[];
    category?: string;
    thumbnailUrl?: string;
  }
): Promise<VideoMetadata> => {
  try {
    const response = await axios.put(`${API_URL}/videos/${videoId}`, metadata);
    return response.data;
  } catch (error) {
    console.error('Error updating video metadata:', error);
    throw error;
  }
};

// Apply edits to a video
export const applyVideoEdits = async (
  videoId: string,
  editOptions: VideoEditOptions
): Promise<{ jobId: string }> => {
  try {
    const response = await axios.post(`${API_URL}/videos/${videoId}/edit`, editOptions);
    return response.data;
  } catch (error) {
    console.error('Error applying video edits:', error);
    throw error;
  }
};

// Check video processing status
export const checkVideoProcessingStatus = async (
  jobId: string
): Promise<{ status: 'pending' | 'processing' | 'completed' | 'failed'; progress?: number; result?: { videoUrl: string } }> => {
  try {
    const response = await axios.get(`${API_URL}/videos/jobs/${jobId}`);
    return response.data;
  } catch (error) {
    console.error('Error checking video processing status:', error);
    throw error;
  }
};

// Generate video thumbnail
export const generateVideoThumbnail = async (
  videoId: string,
  timeInSeconds: number
): Promise<{ thumbnailUrl: string }> => {
  try {
    const response = await axios.post(`${API_URL}/videos/${videoId}/thumbnail`, {
      timeInSeconds,
    });
    return response.data;
  } catch (error) {
    console.error('Error generating video thumbnail:', error);
    throw error;
  }
};

// Get available audio tracks
export const getAudioTracks = async (): Promise<Array<{ id: string; name: string; duration: number; url: string }>> => {
  try {
    const response = await axios.get(`${API_URL}/audio-tracks`);
    return response.data;
  } catch (error) {
    console.error('Error fetching audio tracks:', error);
    throw error;
  }
};

// Get video filters
export const getVideoFilters = async (): Promise<Array<{ id: string; name: string; preview: string }>> => {
  try {
    const response = await axios.get(`${API_URL}/video-filters`);
    return response.data;
  } catch (error) {
    console.error('Error fetching video filters:', error);
    throw error;
  }
};

// Get user videos
export const getUserVideos = async (userId: string): Promise<VideoMetadata[]> => {
  try {
    const response = await axios.get(`${API_URL}/users/${userId}/videos`);
    return response.data;
  } catch (error) {
    console.error('Error fetching user videos:', error);
    throw error;
  }
};
