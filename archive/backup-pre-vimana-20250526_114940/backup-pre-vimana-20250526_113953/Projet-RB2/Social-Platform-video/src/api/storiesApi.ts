import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';

export interface StoryItem {
  id: string;
  type: 'image' | 'video';
  url: string;
  thumbnailUrl?: string;
  duration?: number; // in seconds, for videos
  createdAt: string;
  expiresAt: string;
  seen: boolean;
}

export interface Story {
  id: string;
  userId: string;
  user: {
    id: string;
    name: string;
    username: string;
    avatar: string;
  };
  items: StoryItem[];
  createdAt: string;
  hasUnseen: boolean;
}

export interface StoryCreateData {
  type: 'image' | 'video';
  file: File;
  duration?: number; // for videos, in seconds
  expiresIn?: number; // in hours, default is 24
}

// Get stories feed (stories from followed users)
export const getStoriesFeed = async (): Promise<Story[]> => {
  try {
    const response = await axios.get(`${API_URL}/stories/feed`);
    return response.data;
  } catch (error) {
    console.error('Error fetching stories feed:', error);
    throw error;
  }
};

// Get stories for a specific user
export const getUserStories = async (userId: string): Promise<Story | null> => {
  try {
    const response = await axios.get(`${API_URL}/users/${userId}/stories`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      return null; // User has no stories
    }
    console.error('Error fetching user stories:', error);
    throw error;
  }
};

// Create a new story
export const createStory = async (data: StoryCreateData): Promise<StoryItem> => {
  try {
    // First, get a pre-signed upload URL
    const uploadResponse = await axios.post(`${API_URL}/stories/upload-url`, {
      type: data.type,
      contentType: data.file.type,
      expiresIn: data.expiresIn || 24,
    });

    const { uploadUrl, storyId } = uploadResponse.data;

    // Upload the file
    await axios.put(uploadUrl, data.file, {
      headers: {
        'Content-Type': data.file.type,
      },
    });

    // Confirm the upload and create the story
    const response = await axios.post(`${API_URL}/stories/${storyId}/confirm`, {
      duration: data.duration,
    });

    return response.data;
  } catch (error) {
    console.error('Error creating story:', error);
    throw error;
  }
};

// Delete a story
export const deleteStory = async (storyItemId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/stories/items/${storyItemId}`);
  } catch (error) {
    console.error('Error deleting story:', error);
    throw error;
  }
};

// Mark a story as seen
export const markStorySeen = async (storyItemId: string): Promise<void> => {
  try {
    await axios.post(`${API_URL}/stories/items/${storyItemId}/seen`);
  } catch (error) {
    console.error('Error marking story as seen:', error);
    throw error;
  }
};

// Get story viewers
export const getStoryViewers = async (storyItemId: string): Promise<Array<{
  id: string;
  name: string;
  username: string;
  avatar: string;
  seenAt: string;
}>> => {
  try {
    const response = await axios.get(`${API_URL}/stories/items/${storyItemId}/viewers`);
    return response.data;
  } catch (error) {
    console.error('Error fetching story viewers:', error);
    throw error;
  }
};

// Get story stats
export const getStoryStats = async (storyItemId: string): Promise<{
  views: number;
  uniqueViewers: number;
  completionRate: number; // percentage of viewers who watched the entire story
  averageViewDuration: number; // in seconds
}> => {
  try {
    const response = await axios.get(`${API_URL}/stories/items/${storyItemId}/stats`);
    return response.data;
  } catch (error) {
    console.error('Error fetching story stats:', error);
    throw error;
  }
};

// Add a reaction to a story
export const addStoryReaction = async (
  storyItemId: string,
  reaction: string
): Promise<void> => {
  try {
    await axios.post(`${API_URL}/stories/items/${storyItemId}/reactions`, {
      reaction,
    });
  } catch (error) {
    console.error('Error adding story reaction:', error);
    throw error;
  }
};

// Get story reactions
export const getStoryReactions = async (
  storyItemId: string
): Promise<Array<{
  reaction: string;
  count: number;
  users: Array<{
    id: string;
    name: string;
    username: string;
    avatar: string;
  }>;
}>> => {
  try {
    const response = await axios.get(`${API_URL}/stories/items/${storyItemId}/reactions`);
    return response.data;
  } catch (error) {
    console.error('Error fetching story reactions:', error);
    throw error;
  }
};

// Get archived stories
export const getArchivedStories = async (): Promise<Story[]> => {
  try {
    const response = await axios.get(`${API_URL}/stories/archived`);
    return response.data;
  } catch (error) {
    console.error('Error fetching archived stories:', error);
    throw error;
  }
};

// Create a story highlight
export interface StoryHighlightCreateData {
  title: string;
  coverUrl?: string;
  storyIds: string[];
}

export interface StoryHighlight {
  id: string;
  title: string;
  coverUrl: string;
  storyIds: string[];
  createdAt: string;
}

export const createStoryHighlight = async (data: StoryHighlightCreateData): Promise<StoryHighlight> => {
  try {
    const response = await axios.post(`${API_URL}/stories/highlights`, data);
    return response.data;
  } catch (error) {
    console.error('Error creating story highlight:', error);
    throw error;
  }
};

// Get story highlights
export const getStoryHighlights = async (userId: string): Promise<StoryHighlight[]> => {
  try {
    const response = await axios.get(`${API_URL}/users/${userId}/highlights`);
    return response.data;
  } catch (error) {
    console.error('Error fetching story highlights:', error);
    throw error;
  }
};

// Update story highlight
export const updateStoryHighlight = async (
  highlightId: string,
  data: Partial<StoryHighlightCreateData>
): Promise<StoryHighlight> => {
  try {
    const response = await axios.put(`${API_URL}/stories/highlights/${highlightId}`, data);
    return response.data;
  } catch (error) {
    console.error('Error updating story highlight:', error);
    throw error;
  }
};

// Delete story highlight
export const deleteStoryHighlight = async (highlightId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/stories/highlights/${highlightId}`);
  } catch (error) {
    console.error('Error deleting story highlight:', error);
    throw error;
  }
};
