import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';

export interface Collaborator {
  id: string;
  userId: string;
  username: string;
  name: string;
  avatar: string;
  role: 'owner' | 'editor' | 'viewer';
  joinedAt: string;
}

export interface CollaborationRequest {
  id: string;
  contentId: string;
  contentType: 'video' | 'post' | 'livestream';
  contentTitle: string;
  fromUserId: string;
  fromUsername: string;
  fromName: string;
  fromAvatar: string;
  toUserId: string;
  toUsername: string;
  toName: string;
  toAvatar: string;
  message: string;
  role: 'editor' | 'viewer';
  status: 'pending' | 'accepted' | 'declined';
  createdAt: string;
  updatedAt: string;
}

export interface CollaborativeContent {
  id: string;
  type: 'video' | 'post' | 'livestream';
  title: string;
  description: string;
  thumbnailUrl: string;
  status: 'draft' | 'published' | 'archived';
  visibility: 'public' | 'private' | 'unlisted';
  createdAt: string;
  updatedAt: string;
  owner: {
    id: string;
    username: string;
    name: string;
    avatar: string;
  };
  collaborators: Collaborator[];
}

export interface CollaborationActivity {
  id: string;
  contentId: string;
  contentType: 'video' | 'post' | 'livestream';
  userId: string;
  username: string;
  name: string;
  avatar: string;
  action: 'created' | 'edited' | 'commented' | 'published' | 'archived' | 'added_collaborator' | 'removed_collaborator' | 'changed_role';
  details: string;
  createdAt: string;
}

export interface Comment {
  id: string;
  contentId: string;
  userId: string;
  username: string;
  name: string;
  avatar: string;
  text: string;
  timestamp: number; // Video timestamp if applicable
  createdAt: string;
  updatedAt: string;
  isResolved: boolean;
}

// Get collaborative content
export const getCollaborativeContent = async (
  contentId: string,
  contentType: 'video' | 'post' | 'livestream'
): Promise<CollaborativeContent> => {
  try {
    const response = await axios.get(`${API_URL}/collaboration/content/${contentType}s/${contentId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching collaborative content:', error);
    throw error;
  }
};

// Get all collaborative content for the current user
export const getUserCollaborativeContent = async (
  status?: 'draft' | 'published' | 'archived',
  type?: 'video' | 'post' | 'livestream',
  role?: 'owner' | 'editor' | 'viewer'
): Promise<CollaborativeContent[]> => {
  try {
    const response = await axios.get(`${API_URL}/collaboration/content`, {
      params: { status, type, role },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching user collaborative content:', error);
    throw error;
  }
};

// Create collaborative content
export const createCollaborativeContent = async (
  type: 'video' | 'post' | 'livestream',
  title: string,
  description: string,
  thumbnailUrl?: string,
  visibility: 'public' | 'private' | 'unlisted' = 'private'
): Promise<CollaborativeContent> => {
  try {
    const response = await axios.post(`${API_URL}/collaboration/content`, {
      type,
      title,
      description,
      thumbnailUrl,
      visibility,
    });
    return response.data;
  } catch (error) {
    console.error('Error creating collaborative content:', error);
    throw error;
  }
};

// Update collaborative content
export const updateCollaborativeContent = async (
  contentId: string,
  contentType: 'video' | 'post' | 'livestream',
  updates: Partial<{
    title: string;
    description: string;
    thumbnailUrl: string;
    status: 'draft' | 'published' | 'archived';
    visibility: 'public' | 'private' | 'unlisted';
  }>
): Promise<CollaborativeContent> => {
  try {
    const response = await axios.put(
      `${API_URL}/collaboration/content/${contentType}s/${contentId}`,
      updates
    );
    return response.data;
  } catch (error) {
    console.error('Error updating collaborative content:', error);
    throw error;
  }
};

// Delete collaborative content
export const deleteCollaborativeContent = async (
  contentId: string,
  contentType: 'video' | 'post' | 'livestream'
): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/collaboration/content/${contentType}s/${contentId}`);
  } catch (error) {
    console.error('Error deleting collaborative content:', error);
    throw error;
  }
};

// Get collaborators for content
export const getCollaborators = async (
  contentId: string,
  contentType: 'video' | 'post' | 'livestream'
): Promise<Collaborator[]> => {
  try {
    const response = await axios.get(
      `${API_URL}/collaboration/content/${contentType}s/${contentId}/collaborators`
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching collaborators:', error);
    throw error;
  }
};

// Add collaborator to content
export const addCollaborator = async (
  contentId: string,
  contentType: 'video' | 'post' | 'livestream',
  userId: string,
  role: 'editor' | 'viewer'
): Promise<Collaborator> => {
  try {
    const response = await axios.post(
      `${API_URL}/collaboration/content/${contentType}s/${contentId}/collaborators`,
      { userId, role }
    );
    return response.data;
  } catch (error) {
    console.error('Error adding collaborator:', error);
    throw error;
  }
};

// Update collaborator role
export const updateCollaboratorRole = async (
  contentId: string,
  contentType: 'video' | 'post' | 'livestream',
  userId: string,
  role: 'editor' | 'viewer'
): Promise<Collaborator> => {
  try {
    const response = await axios.put(
      `${API_URL}/collaboration/content/${contentType}s/${contentId}/collaborators/${userId}`,
      { role }
    );
    return response.data;
  } catch (error) {
    console.error('Error updating collaborator role:', error);
    throw error;
  }
};

// Remove collaborator from content
export const removeCollaborator = async (
  contentId: string,
  contentType: 'video' | 'post' | 'livestream',
  userId: string
): Promise<void> => {
  try {
    await axios.delete(
      `${API_URL}/collaboration/content/${contentType}s/${contentId}/collaborators/${userId}`
    );
  } catch (error) {
    console.error('Error removing collaborator:', error);
    throw error;
  }
};

// Get collaboration requests
export const getCollaborationRequests = async (
  status: 'pending' | 'accepted' | 'declined' | 'all' = 'all',
  type: 'sent' | 'received' | 'all' = 'all'
): Promise<CollaborationRequest[]> => {
  try {
    const response = await axios.get(`${API_URL}/collaboration/requests`, {
      params: { status, type },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching collaboration requests:', error);
    throw error;
  }
};

// Send collaboration request
export const sendCollaborationRequest = async (
  contentId: string,
  contentType: 'video' | 'post' | 'livestream',
  toUserId: string,
  role: 'editor' | 'viewer',
  message: string
): Promise<CollaborationRequest> => {
  try {
    const response = await axios.post(`${API_URL}/collaboration/requests`, {
      contentId,
      contentType,
      toUserId,
      role,
      message,
    });
    return response.data;
  } catch (error) {
    console.error('Error sending collaboration request:', error);
    throw error;
  }
};

// Respond to collaboration request
export const respondToCollaborationRequest = async (
  requestId: string,
  accept: boolean
): Promise<CollaborationRequest> => {
  try {
    const response = await axios.put(`${API_URL}/collaboration/requests/${requestId}`, {
      status: accept ? 'accepted' : 'declined',
    });
    return response.data;
  } catch (error) {
    console.error('Error responding to collaboration request:', error);
    throw error;
  }
};

// Get collaboration activity
export const getCollaborationActivity = async (
  contentId: string,
  contentType: 'video' | 'post' | 'livestream'
): Promise<CollaborationActivity[]> => {
  try {
    const response = await axios.get(
      `${API_URL}/collaboration/content/${contentType}s/${contentId}/activity`
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching collaboration activity:', error);
    throw error;
  }
};

// Get collaboration comments
export const getCollaborationComments = async (
  contentId: string,
  contentType: 'video' | 'post' | 'livestream'
): Promise<Comment[]> => {
  try {
    const response = await axios.get(
      `${API_URL}/collaboration/content/${contentType}s/${contentId}/comments`
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching collaboration comments:', error);
    throw error;
  }
};

// Add collaboration comment
export const addCollaborationComment = async (
  contentId: string,
  contentType: 'video' | 'post' | 'livestream',
  text: string,
  timestamp?: number
): Promise<Comment> => {
  try {
    const response = await axios.post(
      `${API_URL}/collaboration/content/${contentType}s/${contentId}/comments`,
      { text, timestamp }
    );
    return response.data;
  } catch (error) {
    console.error('Error adding collaboration comment:', error);
    throw error;
  }
};

// Update collaboration comment
export const updateCollaborationComment = async (
  contentId: string,
  contentType: 'video' | 'post' | 'livestream',
  commentId: string,
  text: string
): Promise<Comment> => {
  try {
    const response = await axios.put(
      `${API_URL}/collaboration/content/${contentType}s/${contentId}/comments/${commentId}`,
      { text }
    );
    return response.data;
  } catch (error) {
    console.error('Error updating collaboration comment:', error);
    throw error;
  }
};

// Delete collaboration comment
export const deleteCollaborationComment = async (
  contentId: string,
  contentType: 'video' | 'post' | 'livestream',
  commentId: string
): Promise<void> => {
  try {
    await axios.delete(
      `${API_URL}/collaboration/content/${contentType}s/${contentId}/comments/${commentId}`
    );
  } catch (error) {
    console.error('Error deleting collaboration comment:', error);
    throw error;
  }
};

// Mark comment as resolved/unresolved
export const toggleCommentResolution = async (
  contentId: string,
  contentType: 'video' | 'post' | 'livestream',
  commentId: string,
  isResolved: boolean
): Promise<Comment> => {
  try {
    const response = await axios.put(
      `${API_URL}/collaboration/content/${contentType}s/${contentId}/comments/${commentId}/resolve`,
      { isResolved }
    );
    return response.data;
  } catch (error) {
    console.error('Error toggling comment resolution:', error);
    throw error;
  }
};

// Search for users to collaborate with
export const searchUsers = async (query: string): Promise<{
  id: string;
  username: string;
  name: string;
  avatar: string;
}[]> => {
  try {
    const response = await axios.get(`${API_URL}/users/search`, {
      params: { query },
    });
    return response.data;
  } catch (error) {
    console.error('Error searching users:', error);
    throw error;
  }
};
