import { useState, useCallback, useMemo, useEffect } from 'react';
import { Calendar, Views, dateFnsLocalizer } from 'react-big-calendar';
import withDragAndDrop from 'react-big-calendar/lib/addons/dragAndDrop';
import type { EventInteractionArgs } from 'react-big-calendar/lib/addons/dragAndDrop';
import { format, parseISO, startOfWeek, getDay, addMinutes, parseJSON } from 'date-fns';
import { fr } from 'date-fns/locale';
import 'react-big-calendar/lib/addons/dragAndDrop/styles.css';
import { CalendarFilters, CalendarFilters as CalendarFiltersType } from './CalendarFilters';
import { SavedFiltersManager } from './SavedFiltersManager';
import { ActiveFiltersBadges } from './ActiveFiltersBadges';
import { downloadICalendar } from '../../utils/calendarExport';
import { initializeDefaultFilters, getDefaultFilter } from '../../services/savedFiltersService';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import {
  Clock,
  Video,
  FileText,
  Image,
  Radio,
  Repeat,
  AlertCircle,
  Loader2,
  ChevronLeft,
  ChevronRight,
  Edit,
  Play,
  X,
  Info,
  Download,
  Filter,
  Bookmark
} from 'lucide-react';
import { ScheduledPost } from '../../api/contentApi';

// Setup localizer for react-big-calendar
const locales = {
  'fr': fr,
};

const localizer = dateFnsLocalizer({
  format,
  startOfWeek: () => startOfWeek(new Date(), { weekStartsOn: 1 }), // Start week on Monday
  getDay,
  locales,
});

// Create DnD Calendar
const DnDCalendar = withDragAndDrop<CalendarEvent, object>(Calendar);

// Event types for the calendar
interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  allDay?: boolean;
  resource?: ScheduledPost;
}

interface SlotInfo {
  start: Date;
  end: Date;
  slots: Date[];
  action: 'select' | 'click' | 'doubleClick';
}



interface ContentCalendarProps {
  scheduledItems: ScheduledPost[];
  onPublishNow: (itemIds: string[]) => Promise<void>;
  onReschedule: (itemId: string) => void;
  onCancel: (itemIds: string[]) => Promise<void>;
  onMoveEvent?: (itemId: string, newStart: Date) => Promise<void>;
  onSelectSlot?: (slotInfo: SlotInfo) => void;
  isLoading?: boolean;
  className?: string;
}

export function ContentCalendar({
  scheduledItems,
  onPublishNow,
  onReschedule,
  onCancel,
  onMoveEvent,
  onSelectSlot,
  isLoading = false,
  className = ''
}: ContentCalendarProps) {
  const [view, setView] = useState<string>(Views.MONTH);
  const [date, setDate] = useState<Date>(new Date());
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);
  const [isActionLoading, setIsActionLoading] = useState(false);
  const [filters, setFilters] = useState<CalendarFiltersType>({
    contentTypes: {
      video: true,
      post: true,
      livestream: true,
      blog: true
    },
    status: {
      scheduled: true,
      processing: true,
      error: true
    },
    recurrence: {
      oneTime: true,
      recurring: true
    },
    dateRange: {
      start: null,
      end: null
    }
  });

  const [showSavedFilters, setShowSavedFilters] = useState(false);

  // Initialize default filters on mount
  useEffect(() => {
    // Initialize default filters if none exist
    initializeDefaultFilters();

    // Apply default filter if one exists
    const defaultFilter = getDefaultFilter();
    if (defaultFilter) {
      setFilters(defaultFilter.filters);
    }
  }, []);

  // Filter scheduled items based on current filters
  const filteredItems = useMemo(() => {
    return scheduledItems.filter(item => {
      // Filter by content type
      if (!filters.contentTypes[item.type as keyof typeof filters.contentTypes]) {
        return false;
      }

      // Filter by status
      if (!filters.status[item.status as keyof typeof filters.status]) {
        return false;
      }

      // Filter by recurrence
      const isRecurring = !!item.recurrence;
      if (isRecurring && !filters.recurrence.recurring) {
        return false;
      }
      if (!isRecurring && !filters.recurrence.oneTime) {
        return false;
      }

      // Filter by date range
      if (filters.dateRange.start || filters.dateRange.end) {
        const itemDate = parseISO(item.scheduledDate);

        if (filters.dateRange.start) {
          const startDate = parseJSON(filters.dateRange.start);
          if (itemDate < startDate) {
            return false;
          }
        }

        if (filters.dateRange.end) {
          const endDate = parseJSON(filters.dateRange.end);
          if (itemDate > endDate) {
            return false;
          }
        }
      }

      return true;
    });
  }, [scheduledItems, filters]);

  // Convert filtered items to calendar events
  const events = useMemo(() => {
    return filteredItems.map(item => {
      const start = parseISO(item.scheduledDate);
      // For calendar display, set end time to 30 minutes after start
      const end = addMinutes(start, 30);

      return {
        id: item.id,
        title: item.title,
        start,
        end,
        allDay: false,
        resource: item
      };
    });
  }, [filteredItems]);

  // Handle event selection
  const handleSelectEvent = useCallback((event: CalendarEvent) => {
    setSelectedEvent(event);
  }, []);

  // Handle view change
  const handleViewChange = useCallback((newView: string) => {
    setView(newView);
  }, []);

  // Handle date change
  const handleNavigate = useCallback((newDate: Date) => {
    setDate(newDate);
  }, []);

  // Handle event drag and drop
  const handleEventDrop = useCallback(
    (args: EventInteractionArgs<CalendarEvent>) => {
      const { event, start } = args;

      if (!onMoveEvent) return;

      const startDate = typeof start === 'string' ? parseISO(start) : start;

      const confirmMove = window.confirm(
        `Are you sure you want to reschedule "${event.title}" to ${format(startDate, 'PPP à HH:mm')}?`
      );

      if (confirmMove) {
        setIsActionLoading(true);
        onMoveEvent(event.id, startDate)
          .then(() => {
            // Success notification handled by parent component
          })
          .catch((error) => {
            console.error('Error moving event:', error);
            window.alert('Failed to reschedule the content. Please try again.');
          })
          .finally(() => {
            setIsActionLoading(false);
          });
      }
    },
    [onMoveEvent]
  );

  // Handle slot selection (for creating new events)
  const handleSelectSlot = useCallback(
    (slotInfo: SlotInfo) => {
      if (!onSelectSlot) return;

      // Only handle click or select actions
      if (slotInfo.action !== 'click' && slotInfo.action !== 'select') return;

      // Pass the slot info to the parent component
      onSelectSlot(slotInfo);
    },
    [onSelectSlot]
  );

  // Handle filter change
  const handleFilterChange = useCallback((newFilters: CalendarFiltersType) => {
    setFilters(newFilters);
  }, []);

  // Reset filters to default
  const handleResetFilters = useCallback(() => {
    setFilters({
      contentTypes: {
        video: true,
        post: true,
        livestream: true,
        blog: true
      },
      status: {
        scheduled: true,
        processing: true,
        error: true
      },
      recurrence: {
        oneTime: true,
        recurring: true
      },
      dateRange: {
        start: null,
        end: null
      }
    });
  }, []);

  // Handle calendar export
  const handleExportCalendar = useCallback(() => {
    // Export all items or only filtered items based on user preference
    const itemsToExport = filteredItems;

    if (itemsToExport.length === 0) {
      alert('Aucun contenu à exporter. Veuillez ajuster vos filtres.');
      return;
    }

    try {
      downloadICalendar(itemsToExport);
    } catch (error) {
      console.error('Error exporting calendar:', error);
      alert('Une erreur est survenue lors de l\'exportation du calendrier.');
    }
  }, [filteredItems]);

  // Toggle saved filters panel
  const toggleSavedFilters = useCallback(() => {
    setShowSavedFilters(!showSavedFilters);
  }, [showSavedFilters]);

  // Handle remove specific filter
  const handleRemoveFilter = useCallback((filterType: string, filterValue: string) => {
    // Create a complete copy of the filters object with the correct type structure
    const newFilters: CalendarFilters = {
      contentTypes: { ...filters.contentTypes },
      status: { ...filters.status },
      recurrence: { ...filters.recurrence },
      dateRange: { ...filters.dateRange }
    };

    if (filterType === 'dateRange') {
      // Handle date range filters
      newFilters.dateRange = {
        ...newFilters.dateRange,
        [filterValue]: null
      };
    } else if (filterType === 'contentTypes') {
      // Handle content types filter
      newFilters.contentTypes = {
        ...newFilters.contentTypes,
        [filterValue]: true // Set to true to include this type
      };
    } else if (filterType === 'status') {
      // Handle status filter
      newFilters.status = {
        ...newFilters.status,
        [filterValue]: true // Set to true to include this type
      };
    } else if (filterType === 'recurrence') {
      // Handle recurrence filter
      newFilters.recurrence = {
        ...newFilters.recurrence,
        [filterValue]: true // Set to true to include this type
      };
    }

    setFilters(newFilters);
  }, [filters]);

  // Close event details
  const handleCloseDetails = () => {
    setSelectedEvent(null);
  };

  // Handle publish now
  const handlePublishNow = async (itemId: string) => {
    if (window.confirm('Are you sure you want to publish this content now?')) {
      setIsActionLoading(true);
      try {
        await onPublishNow([itemId]);
        setSelectedEvent(null);
      } catch (err) {
        console.error('Error publishing item:', err);
      } finally {
        setIsActionLoading(false);
      }
    }
  };

  // Handle cancel
  const handleCancel = async (itemId: string) => {
    if (window.confirm('Are you sure you want to cancel this scheduled publication?')) {
      setIsActionLoading(true);
      try {
        await onCancel([itemId]);
        setSelectedEvent(null);
      } catch (err) {
        console.error('Error canceling item:', err);
      } finally {
        setIsActionLoading(false);
      }
    }
  };

  // Get content type icon
  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video size={16} className="text-blue-500" />;
      case 'post':
        return <Image size={16} className="text-green-500" />;
      case 'livestream':
        return <Radio size={16} className="text-red-500" />;
      case 'blog':
        return <FileText size={16} className="text-purple-500" />;
      default:
        return <FileText size={16} className="text-gray-500" />;
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Clock size={16} className="text-blue-500" />;
      case 'processing':
        return <Loader2 size={16} className="text-yellow-500 animate-spin" />;
      case 'error':
        return <AlertCircle size={16} className="text-red-500" />;
      default:
        return <Clock size={16} className="text-gray-500" />;
    }
  };

  // Custom toolbar component
  const CustomToolbar = ({ label, onNavigate, onView }: any) => {
    return (
      <div className="flex items-center justify-between p-3 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <button
            type="button"
            onClick={() => onNavigate('TODAY')}
            className="px-3 py-1.5 bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200"
          >
            Aujourd'hui
          </button>
          <button
            type="button"
            onClick={() => onNavigate('PREV')}
            className="p-1.5 text-gray-600 hover:bg-gray-100 rounded-full"
          >
            <ChevronLeft size={18} />
          </button>
          <button
            type="button"
            onClick={() => onNavigate('NEXT')}
            className="p-1.5 text-gray-600 hover:bg-gray-100 rounded-full"
          >
            <ChevronRight size={18} />
          </button>
          <h3 className="text-lg font-medium text-gray-800 ml-2">{label}</h3>
        </div>

        <div className="flex space-x-2">
          <button
            type="button"
            onClick={() => onView('month')}
            className={`px-3 py-1.5 rounded-md ${
              view === 'month'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Mois
          </button>
          <button
            type="button"
            onClick={() => onView('week')}
            className={`px-3 py-1.5 rounded-md ${
              view === 'week'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Semaine
          </button>
          <button
            type="button"
            onClick={() => onView('day')}
            className={`px-3 py-1.5 rounded-md ${
              view === 'day'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Jour
          </button>
          <button
            type="button"
            onClick={() => onView('agenda')}
            className={`px-3 py-1.5 rounded-md ${
              view === 'agenda'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Agenda
          </button>
        </div>
      </div>
    );
  };

  // Custom event component
  const EventComponent = ({ event }: { event: CalendarEvent }) => {
    const item = event.resource as ScheduledPost;
    if (!item) return null;

    return (
      <div className={`flex items-center p-1 rounded text-xs overflow-hidden ${
        item.status === 'error'
          ? 'bg-red-100 border-l-2 border-red-500'
          : item.status === 'processing'
            ? 'bg-yellow-100 border-l-2 border-yellow-500'
            : 'bg-blue-100 border-l-2 border-blue-500'
      } ${onMoveEvent ? 'cursor-move hover:shadow-md transition-shadow duration-200' : ''}`}
      title={onMoveEvent ? "Drag to reschedule" : undefined}>
        <div className="flex-shrink-0 mr-1">
          {getContentTypeIcon(item.type)}
        </div>
        <div className="truncate flex-1">
          {event.title}
        </div>
        <div className="flex-shrink-0 flex items-center">
          {item.recurrence && (
            <div className="ml-1">
              <Repeat size={12} className="text-purple-500" />
            </div>
          )}
          {onMoveEvent && (
            <div className="ml-1 text-gray-400 opacity-50 group-hover:opacity-100">
              ⋮⋮
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {isLoading ? (
        <div className="py-20 flex flex-col items-center justify-center text-gray-500">
          <Loader2 size={32} className="animate-spin mb-2" />
          <p>Chargement du calendrier...</p>
        </div>
      ) : (
        <>
          <div className="mb-4 space-y-4">
            {/* Tips section */}
            <div className="space-y-2">
              {onMoveEvent && (
                <div className="p-3 bg-blue-50 rounded-md flex items-start">
                  <Info size={16} className="text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
                  <div className="text-sm text-blue-700">
                    <span className="font-medium">Astuce :</span> Vous pouvez faire glisser et déposer les événements pour les replanifier rapidement.
                  </div>
                </div>
              )}

              <div className="p-3 bg-purple-50 rounded-md flex items-start">
                <Bookmark size={16} className="text-purple-500 mt-0.5 mr-2 flex-shrink-0" />
                <div className="text-sm text-purple-700">
                  <span className="font-medium">Nouveau :</span> Vous pouvez désormais enregistrer vos filtres préférés pour y accéder rapidement.
                </div>
              </div>
            </div>

            {/* Filters section */}
            <div className="space-y-4">
              {/* Calendar Filters */}
              <CalendarFilters
                filters={filters}
                onChange={handleFilterChange}
                onReset={handleResetFilters}
                className={events.length !== scheduledItems.length ? 'border-blue-300 bg-blue-50' : ''}
              />

              {/* Saved Filters Manager (conditionally shown) */}
              {showSavedFilters && (
                <SavedFiltersManager
                  currentFilters={filters}
                  onApplyFilter={handleFilterChange}
                />
              )}
            </div>

            {/* Active filters badges */}
            <ActiveFiltersBadges
              filters={filters}
              onRemoveFilter={handleRemoveFilter}
              className="mb-2"
            />

            {/* Filter stats and actions */}
            <div className="flex justify-between items-center text-sm">
              <div className="text-gray-500">
                Affichage de {events.length} sur {scheduledItems.length} éléments
              </div>
              <div className="flex items-center space-x-4">
                {events.length !== scheduledItems.length && (
                  <button
                    type="button"
                    onClick={handleResetFilters}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    Réinitialiser les filtres
                  </button>
                )}

                <button
                  type="button"
                  onClick={toggleSavedFilters}
                  className={`flex items-center px-3 py-1.5 rounded-md ${
                    showSavedFilters
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                  }`}
                  title={showSavedFilters ? "Masquer les filtres enregistrés" : "Afficher les filtres enregistrés"}
                >
                  <Bookmark size={16} className="mr-1" />
                  {showSavedFilters ? 'Masquer' : 'Filtres enregistrés'}
                </button>

                <button
                  type="button"
                  onClick={handleExportCalendar}
                  className="flex items-center px-3 py-1.5 bg-green-100 text-green-800 rounded-md hover:bg-green-200"
                  disabled={events.length === 0}
                  title="Exporter le calendrier (iCal)"
                >
                  <Download size={16} className="mr-1" />
                  Exporter
                </button>
              </div>
            </div>
          </div>
          {events.length === 0 && scheduledItems.length > 0 ? (
            <div className="h-[700px] flex flex-col items-center justify-center bg-gray-50 rounded-lg border border-gray-200">
              <Filter size={48} className="text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-700 mb-2">Aucun contenu ne correspond à vos filtres</h3>
              <p className="text-gray-500 mb-4 text-center max-w-md">
                Essayez de modifier vos critères de filtrage pour voir plus de contenu.
              </p>
              <button
                type="button"
                onClick={handleResetFilters}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Réinitialiser les filtres
              </button>
            </div>
          ) : (
            <div className="h-[700px]">
              <DnDCalendar
            localizer={localizer}
            events={events}
            startAccessor="start"
            endAccessor="end"
            style={{ height: '100%' }}
            views={['month', 'week', 'day', 'agenda']}
            view={view as any}
            date={date}
            onView={handleViewChange}
            onNavigate={handleNavigate}
            onSelectEvent={handleSelectEvent}
            onEventDrop={handleEventDrop}
            onSelectSlot={handleSelectSlot}
            selectable={!!onSelectSlot}
            draggableAccessor={() => !!onMoveEvent}
            resizable={false}
            components={{
              toolbar: CustomToolbar,
              event: EventComponent as any,
            }}
            messages={{
              today: 'Aujourd\'hui',
              previous: 'Précédent',
              next: 'Suivant',
              month: 'Mois',
              week: 'Semaine',
              day: 'Jour',
              agenda: 'Agenda',
              date: 'Date',
              time: 'Heure',
              event: 'Événement',
              noEventsInRange: 'Aucun contenu planifié dans cette période',
              showMore: (total) => `+ ${total} autres`,
            }}
          />
              </div>
            )}
          </>
        )}

      {/* Event Details Modal */}
      {selectedEvent && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <div
              className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
              onClick={handleCloseDetails}
              aria-hidden="true"
            ></div>

            {/* Modal */}
            <div className="inline-block overflow-hidden text-left align-bottom transition-all transform bg-white rounded-lg shadow-xl sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="px-4 pt-5 pb-4 bg-white sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="flex items-center justify-center flex-shrink-0 w-12 h-12 mx-auto bg-blue-100 rounded-full sm:mx-0 sm:h-10 sm:w-10">
                    {getContentTypeIcon(selectedEvent.resource?.type || 'post')}
                  </div>

                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg font-medium leading-6 text-gray-900 flex items-center">
                      {selectedEvent.title}
                      <span className="ml-2">
                        {getStatusIcon(selectedEvent.resource?.status || 'scheduled')}
                      </span>
                      {selectedEvent.resource?.recurrence && (
                        <span className="ml-2">
                          <Repeat size={16} className="text-purple-500" />
                        </span>
                      )}
                    </h3>

                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        {format(selectedEvent.start, 'PPP à HH:mm')}
                      </p>

                      {selectedEvent.resource?.description && (
                        <p className="mt-2 text-sm text-gray-600">
                          {selectedEvent.resource.description}
                        </p>
                      )}

                      {selectedEvent.resource?.recurrence && (
                        <div className="mt-2 p-2 bg-purple-50 rounded-md">
                          <p className="text-xs font-medium text-purple-800 flex items-center">
                            <Repeat size={14} className="mr-1" />
                            Publication récurrente
                          </p>
                          <p className="text-xs text-purple-700 mt-1">
                            {selectedEvent.resource.recurrence.pattern === 'daily' &&
                              `Tous les ${selectedEvent.resource.recurrence.interval > 1 ? selectedEvent.resource.recurrence.interval : ''} jour${selectedEvent.resource.recurrence.interval > 1 ? 's' : ''}`}
                            {selectedEvent.resource.recurrence.pattern === 'weekly' &&
                              `Toutes les ${selectedEvent.resource.recurrence.interval > 1 ? selectedEvent.resource.recurrence.interval : ''} semaine${selectedEvent.resource.recurrence.interval > 1 ? 's' : ''}`}
                            {selectedEvent.resource.recurrence.pattern === 'monthly' &&
                              `Tous les ${selectedEvent.resource.recurrence.interval > 1 ? selectedEvent.resource.recurrence.interval : ''} mois`}
                          </p>
                        </div>
                      )}

                      {selectedEvent.resource?.status === 'error' && selectedEvent.resource?.errorMessage && (
                        <div className="mt-2 p-2 bg-red-50 rounded-md">
                          <p className="text-xs font-medium text-red-800 flex items-center">
                            <AlertCircle size={14} className="mr-1" />
                            Erreur
                          </p>
                          <p className="text-xs text-red-700 mt-1">
                            {selectedEvent.resource.errorMessage}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="px-4 py-3 bg-gray-50 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={() => onReschedule(selectedEvent.id)}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                  disabled={isActionLoading}
                >
                  <Edit size={16} className="mr-2" />
                  Replanifier
                </button>

                <button
                  type="button"
                  onClick={() => handlePublishNow(selectedEvent.id)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  disabled={isActionLoading}
                >
                  {isActionLoading ? (
                    <Loader2 size={16} className="mr-2 animate-spin" />
                  ) : (
                    <Play size={16} className="mr-2" />
                  )}
                  Publier maintenant
                </button>

                <button
                  type="button"
                  onClick={() => handleCancel(selectedEvent.id)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-red-600 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  disabled={isActionLoading}
                >
                  <X size={16} className="mr-2" />
                  Annuler
                </button>

                <button
                  type="button"
                  onClick={handleCloseDetails}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:w-auto sm:text-sm"
                  disabled={isActionLoading}
                >
                  Fermer
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
