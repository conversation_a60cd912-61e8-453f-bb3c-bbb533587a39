import { useState, useEffect } from 'react';
import { 
  Bookmark, 
  Plus, 
  Edit, 
  Trash2, 
  Star, 
  MoreHorizontal, 
  Check,
  X,
  Save,
  AlertCircle
} from 'lucide-react';
import { 
  getSavedFilters, 
  saveFilter, 
  deleteFilter, 
  setDefaultFilter,
  SavedFilter
} from '../../services/savedFiltersService';
import { CalendarFilters } from './CalendarFilters';

interface SavedFiltersManagerProps {
  currentFilters: CalendarFilters;
  onApplyFilter: (filters: CalendarFilters) => void;
  className?: string;
}

export function SavedFiltersManager({
  currentFilters,
  onApplyFilter,
  className = ''
}: SavedFiltersManagerProps) {
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [isEditing, setIsEditing] = useState<string | null>(null);
  const [newFilterName, setNewFilterName] = useState('');
  const [newFilterDescription, setNewFilterDescription] = useState('');
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Load saved filters on mount
  useEffect(() => {
    loadSavedFilters();
  }, []);
  
  // Load saved filters
  const loadSavedFilters = () => {
    const filters = getSavedFilters();
    setSavedFilters(filters);
  };
  
  // Handle apply filter
  const handleApplyFilter = (filter: SavedFilter) => {
    onApplyFilter(filter.filters);
    setActiveDropdown(null);
  };
  
  // Handle save current filter
  const handleSaveCurrentFilter = () => {
    if (!newFilterName.trim()) {
      setError('Veuillez entrer un nom pour ce filtre');
      return;
    }
    
    try {
      saveFilter(newFilterName, currentFilters, newFilterDescription);
      setNewFilterName('');
      setNewFilterDescription('');
      setIsCreating(false);
      setError(null);
      loadSavedFilters();
    } catch (err) {
      setError('Erreur lors de l\'enregistrement du filtre');
      console.error('Error saving filter:', err);
    }
  };
  
  // Handle update filter
  const handleUpdateFilter = (id: string) => {
    if (!newFilterName.trim()) {
      setError('Veuillez entrer un nom pour ce filtre');
      return;
    }
    
    try {
      const filter = savedFilters.find(f => f.id === id);
      if (!filter) return;
      
      saveFilter(newFilterName, filter.filters, newFilterDescription);
      setNewFilterName('');
      setNewFilterDescription('');
      setIsEditing(null);
      setError(null);
      loadSavedFilters();
    } catch (err) {
      setError('Erreur lors de la mise à jour du filtre');
      console.error('Error updating filter:', err);
    }
  };
  
  // Handle delete filter
  const handleDeleteFilter = (id: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce filtre ?')) {
      try {
        deleteFilter(id);
        loadSavedFilters();
        setActiveDropdown(null);
      } catch (err) {
        setError('Erreur lors de la suppression du filtre');
        console.error('Error deleting filter:', err);
      }
    }
  };
  
  // Handle set default filter
  const handleSetDefaultFilter = (id: string) => {
    try {
      setDefaultFilter(id);
      loadSavedFilters();
      setActiveDropdown(null);
    } catch (err) {
      setError('Erreur lors de la définition du filtre par défaut');
      console.error('Error setting default filter:', err);
    }
  };
  
  // Toggle dropdown
  const toggleDropdown = (id: string) => {
    if (activeDropdown === id) {
      setActiveDropdown(null);
    } else {
      setActiveDropdown(id);
    }
  };
  
  // Start editing filter
  const startEditingFilter = (filter: SavedFilter) => {
    setIsEditing(filter.id);
    setNewFilterName(filter.name);
    setNewFilterDescription(filter.description || '');
    setActiveDropdown(null);
  };
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="font-medium text-gray-900 flex items-center">
          <Bookmark size={18} className="mr-2 text-blue-500" />
          Filtres enregistrés
        </h3>
        
        {!isCreating && (
          <button
            type="button"
            onClick={() => {
              setIsCreating(true);
              setNewFilterName('');
              setNewFilterDescription('');
              setError(null);
            }}
            className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
            title="Enregistrer un nouveau filtre"
          >
            <Plus size={18} />
          </button>
        )}
      </div>
      
      <div className="p-4">
        {/* Error message */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm flex items-center">
            <AlertCircle size={16} className="mr-2 flex-shrink-0" />
            {error}
          </div>
        )}
        
        {/* Create new filter form */}
        {isCreating && (
          <div className="mb-4 p-3 bg-blue-50 rounded-md">
            <h4 className="text-sm font-medium text-blue-800 mb-2">Enregistrer le filtre actuel</h4>
            <div className="space-y-3">
              <div>
                <label className="block text-xs text-blue-700 mb-1">
                  Nom du filtre
                </label>
                <input
                  type="text"
                  value={newFilterName}
                  onChange={(e) => setNewFilterName(e.target.value)}
                  placeholder="Ex: Mes vidéos récurrentes"
                  className="w-full p-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-xs text-blue-700 mb-1">
                  Description (optionnelle)
                </label>
                <input
                  type="text"
                  value={newFilterDescription}
                  onChange={(e) => setNewFilterDescription(e.target.value)}
                  placeholder="Ex: Affiche uniquement mes vidéos récurrentes"
                  className="w-full p-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div className="flex justify-end space-x-2">
                <button
                  type="button"
                  onClick={() => {
                    setIsCreating(false);
                    setError(null);
                  }}
                  className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 flex items-center"
                >
                  <X size={16} className="mr-1" />
                  Annuler
                </button>
                
                <button
                  type="button"
                  onClick={handleSaveCurrentFilter}
                  className="px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                >
                  <Save size={16} className="mr-1" />
                  Enregistrer
                </button>
              </div>
            </div>
          </div>
        )}
        
        {/* Saved filters list */}
        {savedFilters.length === 0 ? (
          <div className="py-6 text-center text-gray-500">
            <Bookmark size={32} className="mx-auto mb-2 text-gray-300" />
            <p>Aucun filtre enregistré</p>
            <p className="text-sm mt-1">Enregistrez vos filtres préférés pour y accéder rapidement</p>
          </div>
        ) : (
          <ul className="space-y-2">
            {savedFilters.map(filter => (
              <li key={filter.id} className="relative">
                {isEditing === filter.id ? (
                  <div className="p-3 bg-blue-50 rounded-md">
                    <div className="space-y-3">
                      <div>
                        <label className="block text-xs text-blue-700 mb-1">
                          Nom du filtre
                        </label>
                        <input
                          type="text"
                          value={newFilterName}
                          onChange={(e) => setNewFilterName(e.target.value)}
                          className="w-full p-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-xs text-blue-700 mb-1">
                          Description (optionnelle)
                        </label>
                        <input
                          type="text"
                          value={newFilterDescription}
                          onChange={(e) => setNewFilterDescription(e.target.value)}
                          className="w-full p-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      
                      <div className="flex justify-end space-x-2">
                        <button
                          type="button"
                          onClick={() => {
                            setIsEditing(null);
                            setError(null);
                          }}
                          className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 flex items-center"
                        >
                          <X size={16} className="mr-1" />
                          Annuler
                        </button>
                        
                        <button
                          type="button"
                          onClick={() => handleUpdateFilter(filter.id)}
                          className="px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                        >
                          <Save size={16} className="mr-1" />
                          Mettre à jour
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div 
                    className={`p-3 rounded-md flex items-center justify-between ${
                      filter.isDefault ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'
                    }`}
                  >
                    <button
                      type="button"
                      onClick={() => handleApplyFilter(filter)}
                      className="flex-1 flex items-start text-left"
                    >
                      <div>
                        <div className="font-medium text-gray-900 flex items-center">
                          {filter.name}
                          {filter.isDefault && (
                            <Star size={16} className="ml-1 text-yellow-500" />
                          )}
                        </div>
                        {filter.description && (
                          <div className="text-xs text-gray-500 mt-0.5">
                            {filter.description}
                          </div>
                        )}
                      </div>
                    </button>
                    
                    <div className="relative">
                      <button
                        type="button"
                        onClick={() => toggleDropdown(filter.id)}
                        className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
                      >
                        <MoreHorizontal size={16} />
                      </button>
                      
                      {activeDropdown === filter.id && (
                        <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                          <div className="py-1">
                            <button
                              type="button"
                              onClick={() => startEditingFilter(filter)}
                              className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                            >
                              <Edit size={16} className="mr-2 text-gray-500" />
                              Modifier
                            </button>
                            
                            {!filter.isDefault && (
                              <button
                                type="button"
                                onClick={() => handleSetDefaultFilter(filter.id)}
                                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                              >
                                <Star size={16} className="mr-2 text-yellow-500" />
                                Définir par défaut
                              </button>
                            )}
                            
                            {!filter.isDefault && (
                              <button
                                type="button"
                                onClick={() => handleDeleteFilter(filter.id)}
                                className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 flex items-center"
                              >
                                <Trash2 size={16} className="mr-2" />
                                Supprimer
                              </button>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}
