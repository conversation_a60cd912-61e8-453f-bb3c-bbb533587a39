import { useState, useEffect } from 'react';
import { 
  BarChart, 
  Clock, 
  Calendar, 
  TrendingUp, 
  Users, 
  ThumbsUp, 
  MessageSquare, 
  Eye, 
  Lightbulb,
  Info,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { ScheduledPost } from '../../api/contentApi';

interface PublishingInsightsProps {
  scheduledItems: ScheduledPost[];
  className?: string;
}

interface EngagementData {
  day: string;
  hour: number;
  engagement: number;
}

interface BestTimeSlot {
  day: string;
  hour: number;
  score: number;
}

export function PublishingInsights({
  scheduledItems,
  className = ''
}: PublishingInsightsProps) {
  const [showDetails, setShowDetails] = useState(false);
  const [bestTimeSlots, setBestTimeSlots] = useState<BestTimeSlot[]>([]);
  const [engagementByHour, setEngagementByHour] = useState<Record<number, number>>({});
  const [engagementByDay, setEngagementByDay] = useState<Record<string, number>>({});
  
  // Mock engagement data - in a real app, this would come from analytics
  const mockEngagementData: EngagementData[] = [
    { day: 'Monday', hour: 9, engagement: 78 },
    { day: 'Monday', hour: 12, engagement: 85 },
    { day: 'Monday', hour: 18, engagement: 92 },
    { day: 'Tuesday', hour: 8, engagement: 65 },
    { day: 'Tuesday', hour: 12, engagement: 75 },
    { day: 'Tuesday', hour: 17, engagement: 88 },
    { day: 'Wednesday', hour: 9, engagement: 72 },
    { day: 'Wednesday', hour: 13, engagement: 82 },
    { day: 'Wednesday', hour: 19, engagement: 95 },
    { day: 'Thursday', hour: 10, engagement: 80 },
    { day: 'Thursday', hour: 14, engagement: 78 },
    { day: 'Thursday', hour: 18, engagement: 90 },
    { day: 'Friday', hour: 9, engagement: 70 },
    { day: 'Friday', hour: 12, engagement: 75 },
    { day: 'Friday', hour: 16, engagement: 85 },
    { day: 'Saturday', hour: 11, engagement: 92 },
    { day: 'Saturday', hour: 15, engagement: 88 },
    { day: 'Saturday', hour: 20, engagement: 82 },
    { day: 'Sunday', hour: 12, engagement: 95 },
    { day: 'Sunday', hour: 16, engagement: 90 },
    { day: 'Sunday', hour: 19, engagement: 85 },
  ];
  
  // Calculate best time slots and engagement metrics
  useEffect(() => {
    // Calculate best time slots
    const sortedByEngagement = [...mockEngagementData].sort((a, b) => b.engagement - a.engagement);
    setBestTimeSlots(sortedByEngagement.slice(0, 5).map(item => ({
      day: item.day,
      hour: item.hour,
      score: item.engagement
    })));
    
    // Calculate engagement by hour
    const hourData: Record<number, number> = {};
    mockEngagementData.forEach(item => {
      if (hourData[item.hour]) {
        hourData[item.hour] = (hourData[item.hour] + item.engagement) / 2;
      } else {
        hourData[item.hour] = item.engagement;
      }
    });
    setEngagementByHour(hourData);
    
    // Calculate engagement by day
    const dayData: Record<string, number> = {};
    mockEngagementData.forEach(item => {
      if (dayData[item.day]) {
        dayData[item.day] = (dayData[item.day] + item.engagement) / 2;
      } else {
        dayData[item.day] = item.engagement;
      }
    });
    setEngagementByDay(dayData);
  }, []);
  
  // Format hour for display
  const formatHour = (hour: number): string => {
    if (hour === 0) return '12 AM';
    if (hour === 12) return '12 PM';
    return hour < 12 ? `${hour} AM` : `${hour - 12} PM`;
  };
  
  // Get day color class
  const getDayColorClass = (day: string): string => {
    const colors: Record<string, string> = {
      'Monday': 'bg-blue-100 text-blue-800',
      'Tuesday': 'bg-green-100 text-green-800',
      'Wednesday': 'bg-purple-100 text-purple-800',
      'Thursday': 'bg-yellow-100 text-yellow-800',
      'Friday': 'bg-pink-100 text-pink-800',
      'Saturday': 'bg-indigo-100 text-indigo-800',
      'Sunday': 'bg-red-100 text-red-800',
    };
    return colors[day] || 'bg-gray-100 text-gray-800';
  };
  
  // Get engagement score color
  const getScoreColorClass = (score: number): string => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-gray-600';
  };
  
  // Get smart suggestions based on scheduled content and engagement data
  const getSmartSuggestions = (): string[] => {
    const suggestions: string[] = [];
    
    // Check if there are any scheduled posts
    if (scheduledItems.length === 0) {
      suggestions.push('You have no scheduled content. Consider planning your content in advance for better engagement.');
    }
    
    // Check if there are posts scheduled for best times
    const hasOptimalTimePost = scheduledItems.some(item => {
      const date = new Date(item.scheduledDate);
      const day = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][date.getDay()];
      const hour = date.getHours();
      
      return bestTimeSlots.some(slot => slot.day === day && Math.abs(slot.hour - hour) <= 1);
    });
    
    if (!hasOptimalTimePost && scheduledItems.length > 0) {
      suggestions.push('Consider scheduling some content during peak engagement times for better reach.');
    }
    
    // Check for content gaps
    if (scheduledItems.length >= 2) {
      const sortedItems = [...scheduledItems].sort((a, b) => 
        new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime()
      );
      
      for (let i = 0; i < sortedItems.length - 1; i++) {
        const current = new Date(sortedItems[i].scheduledDate);
        const next = new Date(sortedItems[i + 1].scheduledDate);
        const diffDays = (next.getTime() - current.getTime()) / (1000 * 60 * 60 * 24);
        
        if (diffDays > 3) {
          suggestions.push(`There's a ${Math.floor(diffDays)}-day gap in your content schedule. Consider adding content between ${current.toLocaleDateString()} and ${next.toLocaleDateString()}.`);
          break;
        }
      }
    }
    
    // Check for content type diversity
    const contentTypes = scheduledItems.map(item => item.type);
    const uniqueTypes = new Set(contentTypes);
    
    if (uniqueTypes.size === 1 && scheduledItems.length > 3) {
      suggestions.push(`All your scheduled content is of type "${contentTypes[0]}". Consider diversifying your content types for better engagement.`);
    }
    
    // Add general suggestions if we don't have many specific ones
    if (suggestions.length < 2) {
      suggestions.push('Sunday evenings show high engagement rates. Consider scheduling important content then.');
      suggestions.push('Engagement is typically higher on weekends. Plan your most important content accordingly.');
    }
    
    return suggestions;
  };
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div 
        className="p-4 border-b border-gray-200 flex justify-between items-center cursor-pointer"
        onClick={() => setShowDetails(!showDetails)}
      >
        <h3 className="font-semibold flex items-center">
          <BarChart size={18} className="mr-2 text-blue-500" />
          Publishing Insights & Recommendations
        </h3>
        
        <button className="text-gray-500 hover:text-gray-700">
          {showDetails ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
        </button>
      </div>
      
      {showDetails && (
        <div className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {/* Best Time Slots */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                <Clock size={16} className="mr-2 text-blue-500" />
                Best Times to Publish
              </h4>
              
              <div className="space-y-2">
                {bestTimeSlots.map((slot, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className={`px-2 py-0.5 rounded-full text-xs mr-2 ${getDayColorClass(slot.day)}`}>
                        {slot.day}
                      </span>
                      <span className="text-sm text-gray-600">
                        {formatHour(slot.hour)}
                      </span>
                    </div>
                    <div className={`text-sm font-medium ${getScoreColorClass(slot.score)}`}>
                      {slot.score}% engagement
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-3 text-xs text-gray-500 flex items-start">
                <Info size={12} className="mr-1 mt-0.5 flex-shrink-0" />
                <span>Based on historical engagement data from your audience</span>
              </div>
            </div>
            
            {/* Smart Suggestions */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-blue-700 mb-3 flex items-center">
                <Lightbulb size={16} className="mr-2 text-blue-500" />
                Smart Recommendations
              </h4>
              
              <ul className="space-y-2">
                {getSmartSuggestions().map((suggestion, index) => (
                  <li key={index} className="text-sm text-blue-700 flex items-start">
                    <span className="inline-block w-4 h-4 bg-blue-200 rounded-full text-blue-700 flex-shrink-0 mr-2 text-xs flex items-center justify-center mt-0.5">
                      {index + 1}
                    </span>
                    {suggestion}
                  </li>
                ))}
              </ul>
            </div>
          </div>
          
          {/* Engagement Charts */}
          <div className="border-t border-gray-200 pt-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <TrendingUp size={16} className="mr-2 text-green-500" />
              Engagement Patterns
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Day of Week Chart */}
              <div>
                <h5 className="text-xs font-medium text-gray-600 mb-2">Engagement by Day</h5>
                <div className="h-32 flex items-end space-x-1">
                  {Object.entries(engagementByDay).map(([day, value]) => (
                    <div key={day} className="flex flex-col items-center flex-1">
                      <div 
                        className="w-full bg-blue-400 rounded-t"
                        style={{ height: `${value * 0.3}%` }}
                      ></div>
                      <div className="text-xs text-gray-500 mt-1 truncate w-full text-center">
                        {day.substring(0, 3)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Hour of Day Chart */}
              <div>
                <h5 className="text-xs font-medium text-gray-600 mb-2">Engagement by Hour</h5>
                <div className="h-32 flex items-end space-x-1">
                  {Object.entries(engagementByHour)
                    .sort((a, b) => parseInt(a[0]) - parseInt(b[0]))
                    .filter(([hour]) => parseInt(hour) % 3 === 0 || parseInt(hour) === 12)
                    .map(([hour, value]) => (
                      <div key={hour} className="flex flex-col items-center flex-1">
                        <div 
                          className="w-full bg-green-400 rounded-t"
                          style={{ height: `${value * 0.3}%` }}
                        ></div>
                        <div className="text-xs text-gray-500 mt-1 truncate w-full text-center">
                          {formatHour(parseInt(hour))}
                        </div>
                      </div>
                    ))
                  }
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
