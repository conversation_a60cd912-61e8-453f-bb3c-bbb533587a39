import { useState, useEffect } from 'react';
import { 
  Archive, 
  RefreshCw, 
  Search, 
  X, 
  CheckSquare, 
  Square, 
  Trash2, 
  RotateCcw, 
  Filter, 
  ChevronDown, 
  ChevronUp, 
  Loader2, 
  AlertCircle,
  Clock,
  Eye,
  ThumbsUp,
  MessageSquare
} from 'lucide-react';
import { format, formatDistanceToNow } from 'date-fns';

export interface ContentItem {
  id: string;
  title: string;
  description?: string;
  thumbnailUrl?: string;
  createdAt: string;
  archivedAt?: string;
  type: 'video' | 'post' | 'livestream' | 'blog';
  stats?: {
    views?: number;
    likes?: number;
    comments?: number;
  };
}

interface ArchiveManagerProps {
  archivedItems: ContentItem[];
  onArchive: (itemIds: string[]) => Promise<void>;
  onRestore: (itemIds: string[]) => Promise<void>;
  onDelete: (itemIds: string[]) => Promise<void>;
  onRefresh: () => Promise<void>;
  isLoading?: boolean;
  className?: string;
}

export function ArchiveManager({
  archivedItems,
  onArchive,
  onRestore,
  onDelete,
  onRefresh,
  isLoading = false,
  className = ''
}: ArchiveManagerProps) {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredItems, setFilteredItems] = useState<ContentItem[]>([]);
  const [isActionLoading, setIsActionLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    type: 'all' as 'all' | 'video' | 'post' | 'livestream' | 'blog',
    sortBy: 'recent' as 'recent' | 'oldest' | 'popular'
  });
  
  // Filter and sort items
  useEffect(() => {
    let filtered = [...archivedItems];
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item => 
        item.title.toLowerCase().includes(query) || 
        (item.description && item.description.toLowerCase().includes(query))
      );
    }
    
    // Apply type filter
    if (filters.type !== 'all') {
      filtered = filtered.filter(item => item.type === filters.type);
    }
    
    // Apply sorting
    switch (filters.sortBy) {
      case 'recent':
        filtered.sort((a, b) => new Date(b.archivedAt || b.createdAt).getTime() - new Date(a.archivedAt || a.createdAt).getTime());
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.archivedAt || a.createdAt).getTime() - new Date(b.archivedAt || b.createdAt).getTime());
        break;
      case 'popular':
        filtered.sort((a, b) => {
          const aViews = a.stats?.views || 0;
          const bViews = b.stats?.views || 0;
          return bViews - aViews;
        });
        break;
    }
    
    setFilteredItems(filtered);
  }, [archivedItems, searchQuery, filters]);
  
  // Handle refresh
  const handleRefresh = async () => {
    setError(null);
    
    try {
      await onRefresh();
    } catch (err) {
      setError('Failed to refresh archived items');
      console.error('Error refreshing archived items:', err);
    }
  };
  
  // Handle select all
  const handleSelectAll = () => {
    if (selectedItems.length === filteredItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredItems.map(item => item.id));
    }
  };
  
  // Handle select item
  const handleSelectItem = (itemId: string) => {
    if (selectedItems.includes(itemId)) {
      setSelectedItems(selectedItems.filter(id => id !== itemId));
    } else {
      setSelectedItems([...selectedItems, itemId]);
    }
  };
  
  // Handle restore
  const handleRestore = async () => {
    if (selectedItems.length === 0) return;
    
    setIsActionLoading(true);
    setError(null);
    
    try {
      await onRestore(selectedItems);
      setSelectedItems([]);
    } catch (err) {
      setError('Failed to restore selected items');
      console.error('Error restoring items:', err);
    } finally {
      setIsActionLoading(false);
    }
  };
  
  // Handle delete
  const handleDelete = async () => {
    if (selectedItems.length === 0) return;
    
    if (!window.confirm(`Are you sure you want to permanently delete ${selectedItems.length} item(s)? This action cannot be undone.`)) {
      return;
    }
    
    setIsActionLoading(true);
    setError(null);
    
    try {
      await onDelete(selectedItems);
      setSelectedItems([]);
    } catch (err) {
      setError('Failed to delete selected items');
      console.error('Error deleting items:', err);
    } finally {
      setIsActionLoading(false);
    }
  };
  
  // Toggle filters
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };
  
  // Format view count
  const formatCount = (count: number = 0) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };
  
  // Get content type label
  const getContentTypeLabel = (type: ContentItem['type']) => {
    switch (type) {
      case 'video':
        return 'Video';
      case 'post':
        return 'Post';
      case 'livestream':
        return 'Livestream';
      case 'blog':
        return 'Blog';
      default:
        return 'Content';
    }
  };
  
  // Get content type color
  const getContentTypeColor = (type: ContentItem['type']) => {
    switch (type) {
      case 'video':
        return 'bg-blue-100 text-blue-800';
      case 'post':
        return 'bg-green-100 text-green-800';
      case 'livestream':
        return 'bg-red-100 text-red-800';
      case 'blog':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="font-semibold flex items-center">
          <Archive size={18} className="mr-2 text-gray-500" />
          Archived Content
        </h3>
        
        <button
          type="button"
          onClick={handleRefresh}
          className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
          disabled={isLoading}
          title="Refresh"
        >
          <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
        </button>
      </div>
      
      <div className="p-4">
        {/* Search and filters */}
        <div className="mb-4 space-y-3">
          <div className="flex">
            <div className="relative flex-1">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search archived content..."
                className="w-full p-2 pl-8 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                disabled={isLoading || isActionLoading}
              />
              <Search size={16} className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400" />
              
              {searchQuery && (
                <button
                  type="button"
                  onClick={() => setSearchQuery('')}
                  className="absolute right-2.5 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  disabled={isLoading || isActionLoading}
                >
                  <X size={16} />
                </button>
              )}
            </div>
            
            <button
              type="button"
              onClick={toggleFilters}
              className="ml-2 px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 flex items-center"
              disabled={isLoading || isActionLoading}
            >
              <Filter size={16} className="mr-1" />
              Filters
              {showFilters ? <ChevronUp size={16} className="ml-1" /> : <ChevronDown size={16} className="ml-1" />}
            </button>
          </div>
          
          {showFilters && (
            <div className="p-3 border border-gray-200 rounded-md bg-gray-50">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Content Type
                  </label>
                  <select
                    value={filters.type}
                    onChange={(e) => setFilters({ ...filters, type: e.target.value as any })}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    disabled={isLoading || isActionLoading}
                  >
                    <option value="all">All Types</option>
                    <option value="video">Videos</option>
                    <option value="post">Posts</option>
                    <option value="livestream">Livestreams</option>
                    <option value="blog">Blog Articles</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Sort By
                  </label>
                  <select
                    value={filters.sortBy}
                    onChange={(e) => setFilters({ ...filters, sortBy: e.target.value as any })}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    disabled={isLoading || isActionLoading}
                  >
                    <option value="recent">Recently Archived</option>
                    <option value="oldest">Oldest First</option>
                    <option value="popular">Most Popular</option>
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* Bulk actions */}
        {selectedItems.length > 0 && (
          <div className="mb-4 p-3 bg-blue-50 rounded-md flex items-center justify-between">
            <div className="text-sm text-blue-800">
              {selectedItems.length} item{selectedItems.length !== 1 ? 's' : ''} selected
            </div>
            
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={handleRestore}
                className="px-3 py-1.5 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center"
                disabled={isActionLoading}
              >
                {isActionLoading ? (
                  <Loader2 size={16} className="mr-1 animate-spin" />
                ) : (
                  <RotateCcw size={16} className="mr-1" />
                )}
                Restore
              </button>
              
              <button
                type="button"
                onClick={handleDelete}
                className="px-3 py-1.5 bg-red-500 text-white rounded-md hover:bg-red-600 flex items-center"
                disabled={isActionLoading}
              >
                <Trash2 size={16} className="mr-1" />
                Delete
              </button>
            </div>
          </div>
        )}
        
        {/* Error message */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm flex items-center">
            <AlertCircle size={16} className="mr-2 flex-shrink-0" />
            {error}
          </div>
        )}
        
        {/* Content list */}
        {isLoading ? (
          <div className="py-8 flex flex-col items-center justify-center text-gray-500">
            <Loader2 size={32} className="animate-spin mb-2" />
            <p>Loading archived content...</p>
          </div>
        ) : filteredItems.length === 0 ? (
          <div className="py-8 flex flex-col items-center justify-center text-gray-500">
            <Archive size={32} className="mb-2" />
            <p className="mb-1">No archived content found</p>
            <p className="text-sm text-gray-400">
              {searchQuery ? 'Try a different search term' : 'Items you archive will appear here'}
            </p>
          </div>
        ) : (
          <div className="space-y-1">
            {/* Header */}
            <div className="flex items-center p-2 bg-gray-50 rounded-md text-sm font-medium text-gray-700">
              <div className="w-8 flex-shrink-0">
                <button
                  type="button"
                  onClick={handleSelectAll}
                  className="text-gray-500 hover:text-gray-700"
                  disabled={isActionLoading}
                >
                  {selectedItems.length === filteredItems.length ? (
                    <CheckSquare size={18} className="text-blue-500" />
                  ) : (
                    <Square size={18} />
                  )}
                </button>
              </div>
              <div className="flex-1 min-w-0">Content</div>
              <div className="w-32 text-right">Stats</div>
              <div className="w-32 text-right">Archived</div>
            </div>
            
            {/* Items */}
            {filteredItems.map(item => (
              <div 
                key={item.id} 
                className={`flex items-center p-2 hover:bg-gray-50 rounded-md ${
                  selectedItems.includes(item.id) ? 'bg-blue-50' : ''
                }`}
              >
                <div className="w-8 flex-shrink-0">
                  <button
                    type="button"
                    onClick={() => handleSelectItem(item.id)}
                    className="text-gray-500 hover:text-gray-700"
                    disabled={isActionLoading}
                  >
                    {selectedItems.includes(item.id) ? (
                      <CheckSquare size={18} className="text-blue-500" />
                    ) : (
                      <Square size={18} />
                    )}
                  </button>
                </div>
                
                <div className="flex-1 min-w-0 flex items-center">
                  {item.thumbnailUrl && (
                    <div className="w-12 h-12 rounded overflow-hidden mr-3 flex-shrink-0">
                      <img 
                        src={item.thumbnailUrl} 
                        alt={item.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  
                  <div className="min-w-0">
                    <div className="font-medium text-gray-900 truncate">{item.title}</div>
                    <div className="flex items-center text-xs text-gray-500">
                      <span className={`px-1.5 py-0.5 rounded-full text-xs mr-2 ${getContentTypeColor(item.type)}`}>
                        {getContentTypeLabel(item.type)}
                      </span>
                      <span>
                        Created {formatDistanceToNow(new Date(item.createdAt), { addSuffix: true })}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="w-32 text-right text-xs text-gray-500">
                  {item.stats && (
                    <div className="space-y-1">
                      {item.stats.views !== undefined && (
                        <div className="flex items-center justify-end">
                          <Eye size={12} className="mr-1" />
                          {formatCount(item.stats.views)}
                        </div>
                      )}
                      {item.stats.likes !== undefined && (
                        <div className="flex items-center justify-end">
                          <ThumbsUp size={12} className="mr-1" />
                          {formatCount(item.stats.likes)}
                        </div>
                      )}
                      {item.stats.comments !== undefined && (
                        <div className="flex items-center justify-end">
                          <MessageSquare size={12} className="mr-1" />
                          {formatCount(item.stats.comments)}
                        </div>
                      )}
                    </div>
                  )}
                </div>
                
                <div className="w-32 text-right text-xs text-gray-500">
                  <div className="flex items-center justify-end">
                    <Clock size={12} className="mr-1" />
                    {item.archivedAt ? (
                      format(new Date(item.archivedAt), 'MMM d, yyyy')
                    ) : (
                      'Unknown'
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
