import { useState } from 'react';
import { 
  Filter, 
  Video, 
  FileText, 
  Image, 
  Radio, 
  Clock, 
  AlertCircle, 
  CheckCircle,
  Calendar,
  Repeat,
  ChevronDown,
  ChevronUp,
  X,
  Check
} from 'lucide-react';

export interface CalendarFilters {
  contentTypes: {
    video: boolean;
    post: boolean;
    livestream: boolean;
    blog: boolean;
  };
  status: {
    scheduled: boolean;
    processing: boolean;
    error: boolean;
  };
  recurrence: {
    oneTime: boolean;
    recurring: boolean;
  };
  dateRange: {
    start: string | null;
    end: string | null;
  };
}

interface CalendarFiltersProps {
  filters: CalendarFilters;
  onChange: (filters: CalendarFilters) => void;
  onReset: () => void;
  className?: string;
}

export function CalendarFilters({
  filters,
  onChange,
  onReset,
  className = ''
}: CalendarFiltersProps) {
  const [expanded, setExpanded] = useState(false);
  
  // Toggle expanded state
  const toggleExpanded = () => {
    setExpanded(!expanded);
  };
  
  // Handle content type change
  const handleContentTypeChange = (type: keyof CalendarFilters['contentTypes']) => {
    onChange({
      ...filters,
      contentTypes: {
        ...filters.contentTypes,
        [type]: !filters.contentTypes[type]
      }
    });
  };
  
  // Handle status change
  const handleStatusChange = (status: keyof CalendarFilters['status']) => {
    onChange({
      ...filters,
      status: {
        ...filters.status,
        [status]: !filters.status[status]
      }
    });
  };
  
  // Handle recurrence change
  const handleRecurrenceChange = (recurrence: keyof CalendarFilters['recurrence']) => {
    onChange({
      ...filters,
      recurrence: {
        ...filters.recurrence,
        [recurrence]: !filters.recurrence[recurrence]
      }
    });
  };
  
  // Handle date range change
  const handleDateRangeChange = (field: keyof CalendarFilters['dateRange'], value: string) => {
    onChange({
      ...filters,
      dateRange: {
        ...filters.dateRange,
        [field]: value || null
      }
    });
  };
  
  // Count active filters
  const countActiveFilters = (): number => {
    let count = 0;
    
    // Count content types
    Object.values(filters.contentTypes).forEach(value => {
      if (!value) count++; // Count deselected types as active filters
    });
    
    // Count status
    Object.values(filters.status).forEach(value => {
      if (!value) count++; // Count deselected statuses as active filters
    });
    
    // Count recurrence
    Object.values(filters.recurrence).forEach(value => {
      if (!value) count++; // Count deselected recurrence as active filters
    });
    
    // Count date range
    if (filters.dateRange.start) count++;
    if (filters.dateRange.end) count++;
    
    return count;
  };
  
  // Check if all filters are selected in a category
  const areAllSelected = (category: 'contentTypes' | 'status' | 'recurrence'): boolean => {
    return Object.values(filters[category]).every(value => value);
  };
  
  // Check if any filters are selected in a category
  const areAnySelected = (category: 'contentTypes' | 'status' | 'recurrence'): boolean => {
    return Object.values(filters[category]).some(value => value);
  };
  
  // Toggle all filters in a category
  const toggleAllInCategory = (category: 'contentTypes' | 'status' | 'recurrence', value: boolean) => {
    const newFilters = { ...filters };
    
    Object.keys(filters[category]).forEach(key => {
      newFilters[category][key as keyof typeof newFilters[typeof category]] = value;
    });
    
    onChange(newFilters);
  };
  
  const activeFilterCount = countActiveFilters();
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div 
        className="p-4 border-b border-gray-200 flex justify-between items-center cursor-pointer"
        onClick={toggleExpanded}
      >
        <div className="flex items-center">
          <Filter size={18} className="mr-2 text-gray-500" />
          <h3 className="font-medium text-gray-900">Filters</h3>
          {activeFilterCount > 0 && (
            <span className="ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs">
              {activeFilterCount}
            </span>
          )}
        </div>
        
        <div className="flex items-center">
          {activeFilterCount > 0 && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                onReset();
              }}
              className="mr-2 text-sm text-gray-500 hover:text-gray-700"
            >
              Reset
            </button>
          )}
          {expanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
        </div>
      </div>
      
      {expanded && (
        <div className="p-4 space-y-4">
          {/* Content Types */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-700">Content Types</h4>
              <button
                type="button"
                onClick={() => toggleAllInCategory('contentTypes', !areAllSelected('contentTypes'))}
                className="text-xs text-blue-600 hover:text-blue-800"
              >
                {areAllSelected('contentTypes') ? 'Deselect All' : 'Select All'}
              </button>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <button
                type="button"
                onClick={() => handleContentTypeChange('video')}
                className={`px-3 py-1.5 rounded-full text-sm flex items-center ${
                  filters.contentTypes.video
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-500'
                }`}
              >
                <Video size={16} className="mr-1" />
                Videos
                {filters.contentTypes.video ? (
                  <Check size={14} className="ml-1" />
                ) : (
                  <X size={14} className="ml-1" />
                )}
              </button>
              
              <button
                type="button"
                onClick={() => handleContentTypeChange('post')}
                className={`px-3 py-1.5 rounded-full text-sm flex items-center ${
                  filters.contentTypes.post
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-500'
                }`}
              >
                <Image size={16} className="mr-1" />
                Posts
                {filters.contentTypes.post ? (
                  <Check size={14} className="ml-1" />
                ) : (
                  <X size={14} className="ml-1" />
                )}
              </button>
              
              <button
                type="button"
                onClick={() => handleContentTypeChange('livestream')}
                className={`px-3 py-1.5 rounded-full text-sm flex items-center ${
                  filters.contentTypes.livestream
                    ? 'bg-red-100 text-red-800'
                    : 'bg-gray-100 text-gray-500'
                }`}
              >
                <Radio size={16} className="mr-1" />
                Livestreams
                {filters.contentTypes.livestream ? (
                  <Check size={14} className="ml-1" />
                ) : (
                  <X size={14} className="ml-1" />
                )}
              </button>
              
              <button
                type="button"
                onClick={() => handleContentTypeChange('blog')}
                className={`px-3 py-1.5 rounded-full text-sm flex items-center ${
                  filters.contentTypes.blog
                    ? 'bg-purple-100 text-purple-800'
                    : 'bg-gray-100 text-gray-500'
                }`}
              >
                <FileText size={16} className="mr-1" />
                Blogs
                {filters.contentTypes.blog ? (
                  <Check size={14} className="ml-1" />
                ) : (
                  <X size={14} className="ml-1" />
                )}
              </button>
            </div>
          </div>
          
          {/* Status */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-700">Status</h4>
              <button
                type="button"
                onClick={() => toggleAllInCategory('status', !areAllSelected('status'))}
                className="text-xs text-blue-600 hover:text-blue-800"
              >
                {areAllSelected('status') ? 'Deselect All' : 'Select All'}
              </button>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <button
                type="button"
                onClick={() => handleStatusChange('scheduled')}
                className={`px-3 py-1.5 rounded-full text-sm flex items-center ${
                  filters.status.scheduled
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-500'
                }`}
              >
                <Clock size={16} className="mr-1" />
                Scheduled
                {filters.status.scheduled ? (
                  <Check size={14} className="ml-1" />
                ) : (
                  <X size={14} className="ml-1" />
                )}
              </button>
              
              <button
                type="button"
                onClick={() => handleStatusChange('processing')}
                className={`px-3 py-1.5 rounded-full text-sm flex items-center ${
                  filters.status.processing
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-gray-100 text-gray-500'
                }`}
              >
                <Clock size={16} className="mr-1" />
                Processing
                {filters.status.processing ? (
                  <Check size={14} className="ml-1" />
                ) : (
                  <X size={14} className="ml-1" />
                )}
              </button>
              
              <button
                type="button"
                onClick={() => handleStatusChange('error')}
                className={`px-3 py-1.5 rounded-full text-sm flex items-center ${
                  filters.status.error
                    ? 'bg-red-100 text-red-800'
                    : 'bg-gray-100 text-gray-500'
                }`}
              >
                <AlertCircle size={16} className="mr-1" />
                Error
                {filters.status.error ? (
                  <Check size={14} className="ml-1" />
                ) : (
                  <X size={14} className="ml-1" />
                )}
              </button>
            </div>
          </div>
          
          {/* Recurrence */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-700">Recurrence</h4>
              <button
                type="button"
                onClick={() => toggleAllInCategory('recurrence', !areAllSelected('recurrence'))}
                className="text-xs text-blue-600 hover:text-blue-800"
              >
                {areAllSelected('recurrence') ? 'Deselect All' : 'Select All'}
              </button>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <button
                type="button"
                onClick={() => handleRecurrenceChange('oneTime')}
                className={`px-3 py-1.5 rounded-full text-sm flex items-center ${
                  filters.recurrence.oneTime
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-500'
                }`}
              >
                <Calendar size={16} className="mr-1" />
                One-time
                {filters.recurrence.oneTime ? (
                  <Check size={14} className="ml-1" />
                ) : (
                  <X size={14} className="ml-1" />
                )}
              </button>
              
              <button
                type="button"
                onClick={() => handleRecurrenceChange('recurring')}
                className={`px-3 py-1.5 rounded-full text-sm flex items-center ${
                  filters.recurrence.recurring
                    ? 'bg-purple-100 text-purple-800'
                    : 'bg-gray-100 text-gray-500'
                }`}
              >
                <Repeat size={16} className="mr-1" />
                Recurring
                {filters.recurrence.recurring ? (
                  <Check size={14} className="ml-1" />
                ) : (
                  <X size={14} className="ml-1" />
                )}
              </button>
            </div>
          </div>
          
          {/* Date Range */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Date Range</h4>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-xs text-gray-500 mb-1">Start Date</label>
                <input
                  type="date"
                  value={filters.dateRange.start || ''}
                  onChange={(e) => handleDateRangeChange('start', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-xs text-gray-500 mb-1">End Date</label>
                <input
                  type="date"
                  value={filters.dateRange.end || ''}
                  onChange={(e) => handleDateRangeChange('end', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
