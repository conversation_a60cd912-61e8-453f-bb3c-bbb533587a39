import { useState } from 'react';
import { useCollectionsStore } from '../../store/collections';
import { useSearchStore } from '../../store/search';
import { SearchResult } from '../../api/searchApi';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Textarea } from '../ui/textarea';
import { VideoCard } from '@/components/video-card';
import { PostCard } from '@/components/post-card';
import { LivestreamCard } from '@/components/livestream/LivestreamCard';
import { Loader2, Search } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface AddToCollectionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  collectionId: string;
  initialContentId?: string;
  initialContentType?: 'video' | 'post' | 'livestream';
}

export function AddToCollectionDialog({
  open,
  onOpenChange,
  collectionId,
  initialContentId,
  initialContentType,
}: AddToCollectionDialogProps) {
  const { addItemToCollection, isLoading } = useCollectionsStore();
  const { search, results: searchResults, isLoading: isSearching } = useSearchStore();
  
  const [activeTab, setActiveTab] = useState<'search' | 'url'>('search');
  const [searchQuery, setSearchQuery] = useState('');
  const [contentUrl, setContentUrl] = useState('');
  const [notes, setNotes] = useState('');
  const [selectedContent, setSelectedContent] = useState<SearchResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const { toast } = useToast();
  
  // Handle search
  const handleSearch = () => {
    if (searchQuery.trim()) {
      search({ query: searchQuery });
    }
  };
  
  // Handle content selection
  const handleSelectContent = (content: SearchResult) => {
    setSelectedContent(content);
  };
  
  // Handle add to collection
  const handleAddToCollection = async () => {
    try {
      setError(null);
      
      if (selectedContent) {
        // Add from search results
        await addItemToCollection(
          collectionId,
          selectedContent.id,
          selectedContent.type as 'video' | 'post' | 'livestream',
          notes
        );
        
        toast({
          title: 'Content added',
          description: 'The content has been added to your collection.',
        });
        
        // Reset form and close dialog
        setSearchQuery('');
        setSelectedContent(null);
        setNotes('');
        onOpenChange(false);
      } else if (initialContentId && initialContentType) {
        // Add from initial content (when adding directly from content page)
        await addItemToCollection(
          collectionId,
          initialContentId,
          initialContentType,
          notes
        );
        
        toast({
          title: 'Content added',
          description: 'The content has been added to your collection.',
        });
        
        // Reset form and close dialog
        setNotes('');
        onOpenChange(false);
      } else if (activeTab === 'url' && contentUrl.trim()) {
        // In a real app, you would validate and process the URL
        // For now, we'll just show an error
        setError('Adding content by URL is not implemented yet.');
      } else {
        setError('Please select content to add to your collection.');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add content to collection');
      
      toast({
        title: 'Error',
        description: 'Failed to add content to collection. Please try again.',
        variant: 'destructive',
      });
    }
  };
  
  // Handle dialog close
  const handleClose = () => {
    if (!isLoading) {
      setSearchQuery('');
      setContentUrl('');
      setNotes('');
      setSelectedContent(null);
      setError(null);
      onOpenChange(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add to collection</DialogTitle>
          <DialogDescription>
            Search for content or add by URL to your collection.
          </DialogDescription>
        </DialogHeader>
        
        {initialContentId && initialContentType ? (
          <div className="py-4">
            <p className="text-sm text-gray-500 mb-4">
              Add this content to your collection:
            </p>
            
            <div className="mb-4">
              {/* In a real app, you would fetch and display the content details */}
              <div className="p-4 border rounded-md bg-gray-50">
                <p className="font-medium">Selected content</p>
                <p className="text-sm text-gray-500">
                  Type: {initialContentType.charAt(0).toUpperCase() + initialContentType.slice(1)}
                </p>
              </div>
            </div>
            
            <div className="space-y-2">
              <label htmlFor="notes" className="text-sm font-medium">
                Notes (optional)
              </label>
              <Textarea
                id="notes"
                placeholder="Add notes about this content..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
              />
            </div>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'search' | 'url')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="search">Search</TabsTrigger>
              <TabsTrigger value="url">Add by URL</TabsTrigger>
            </TabsList>
            
            <TabsContent value="search" className="space-y-4 py-4">
              <div className="flex space-x-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    placeholder="Search for videos, posts, or livestreams..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleSearch();
                      }
                    }}
                  />
                </div>
                <Button onClick={handleSearch} disabled={!searchQuery.trim() || isSearching}>
                  {isSearching ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Search className="h-4 w-4 mr-2" />
                  )}
                  Search
                </Button>
              </div>
              
              <div className="space-y-4">
                {isSearching ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                  </div>
                ) : searchResults.length > 0 ? (
                  <div className="space-y-4 max-h-[400px] overflow-y-auto p-1">
                    {searchResults.map((result) => (
                      <div
                        key={result.id}
                        className={`border rounded-md p-2 cursor-pointer transition-colors ${
                          selectedContent?.id === result.id
                            ? 'border-green-500 bg-green-50'
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={() => handleSelectContent(result)}
                      >
                        {result.type === 'video' && (
                          <VideoCard video={result} compact />
                        )}
                        
                        {result.type === 'post' && (
                          <PostCard post={result} compact />
                        )}
                        
                        {result.type === 'livestream' && (
                          <LivestreamCard livestream={result} compact />
                        )}
                      </div>
                    ))}
                  </div>
                ) : searchQuery ? (
                  <div className="text-center py-8 text-gray-500">
                    No results found for "{searchQuery}"
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    Search for content to add to your collection
                  </div>
                )}
              </div>
              
              {selectedContent && (
                <div className="space-y-2">
                  <label htmlFor="notes" className="text-sm font-medium">
                    Notes (optional)
                  </label>
                  <Textarea
                    id="notes"
                    placeholder="Add notes about this content..."
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                  />
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="url" className="space-y-4 py-4">
              <div className="space-y-2">
                <label htmlFor="content-url" className="text-sm font-medium">
                  Content URL
                </label>
                <Input
                  id="content-url"
                  placeholder="https://example.com/video/123"
                  value={contentUrl}
                  onChange={(e) => setContentUrl(e.target.value)}
                />
                <p className="text-xs text-gray-500">
                  Enter the URL of a video, post, or livestream to add to your collection.
                </p>
              </div>
              
              <div className="space-y-2">
                <label htmlFor="url-notes" className="text-sm font-medium">
                  Notes (optional)
                </label>
                <Textarea
                  id="url-notes"
                  placeholder="Add notes about this content..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                />
              </div>
            </TabsContent>
          </Tabs>
        )}
        
        {error && (
          <div className="text-red-500 text-sm">{error}</div>
        )}
        
        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleAddToCollection} disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Add to Collection
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
