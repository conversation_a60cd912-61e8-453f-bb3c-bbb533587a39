import { useState } from 'react';
import { Collection } from '../../api/collectionsApi';
import { CollectionCard } from './CollectionCard';
import { Button } from '../ui/button';
import { Plus } from 'lucide-react';
import { CreateCollectionDialog } from './CreateCollectionDialog';
import { ShareCollectionDialog } from './ShareCollectionDialog';
import { DeleteCollectionDialog } from './DeleteCollectionDialog';
import { EditCollectionDialog } from './EditCollectionDialog';
import { useCollectionsStore } from '../../store/collections';

interface CollectionGridProps {
  collections: Collection[];
  isOwner?: boolean;
  showCreateButton?: boolean;
  emptyMessage?: string;
  className?: string;
}

export function CollectionGrid({
  collections,
  isOwner = false,
  showCreateButton = false,
  emptyMessage = 'No collections found',
  className = '',
}: CollectionGridProps) {
  const { startCreatingCollection } = useCollectionsStore();
  
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCollection, setSelectedCollection] = useState<Collection | null>(null);
  
  const handleEdit = (collection: Collection) => {
    setSelectedCollection(collection);
    setIsEditDialogOpen(true);
  };
  
  const handleShare = (collection: Collection) => {
    setSelectedCollection(collection);
    setIsShareDialogOpen(true);
  };
  
  const handleDelete = (collection: Collection) => {
    setSelectedCollection(collection);
    setIsDeleteDialogOpen(true);
  };
  
  const handleCreateNew = () => {
    startCreatingCollection();
    setIsCreateDialogOpen(true);
  };
  
  return (
    <div className={className}>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {showCreateButton && (
          <div className="bg-gray-50 rounded-lg border border-dashed border-gray-300 flex flex-col items-center justify-center p-6 h-full min-h-[240px]">
            <Button
              variant="outline"
              size="lg"
              className="rounded-full h-16 w-16 mb-4"
              onClick={handleCreateNew}
            >
              <Plus size={24} />
            </Button>
            <p className="text-gray-600 text-center">Create a new collection</p>
          </div>
        )}
        
        {collections.map((collection) => (
          <CollectionCard
            key={collection.id}
            collection={collection}
            isOwner={isOwner}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onShare={handleShare}
          />
        ))}
        
        {collections.length === 0 && !showCreateButton && (
          <div className="col-span-full flex flex-col items-center justify-center py-12 text-gray-500">
            <p>{emptyMessage}</p>
            {isOwner && (
              <Button
                variant="outline"
                size="sm"
                className="mt-4"
                onClick={handleCreateNew}
              >
                <Plus size={16} className="mr-2" />
                Create collection
              </Button>
            )}
          </div>
        )}
      </div>
      
      {/* Dialogs */}
      <CreateCollectionDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
      />
      
      {selectedCollection && (
        <>
          <EditCollectionDialog
            open={isEditDialogOpen}
            onOpenChange={setIsEditDialogOpen}
            collection={selectedCollection}
          />
          
          <ShareCollectionDialog
            open={isShareDialogOpen}
            onOpenChange={setIsShareDialogOpen}
            collection={selectedCollection}
          />
          
          <DeleteCollectionDialog
            open={isDeleteDialogOpen}
            onOpenChange={setIsDeleteDialogOpen}
            collection={selectedCollection}
          />
        </>
      )}
    </div>
  );
}
