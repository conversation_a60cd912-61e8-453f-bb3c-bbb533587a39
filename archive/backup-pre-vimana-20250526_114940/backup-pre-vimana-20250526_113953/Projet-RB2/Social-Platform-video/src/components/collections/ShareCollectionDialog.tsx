import { useState, useEffect } from 'react';
import { useCollectionsStore } from '../../store/collections';
import { Collection, User } from '../../api/collectionsApi';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Avatar } from '../ui/avatar';
import { Loader2, Copy, Check, X, Search, Plus } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ShareCollectionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  collection: Collection;
}

export function ShareCollectionDialog({
  open,
  onOpenChange,
  collection,
}: ShareCollectionDialogProps) {
  const { generateShareableLink, fetchCollaborators, addCollaboratorToCollection, removeCollaboratorFromCollection, shareableLink, collaborators, isLoading } = useCollectionsStore();
  const [activeTab, setActiveTab] = useState<'link' | 'collaborators'>('link');
  const [copied, setCopied] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const { toast } = useToast();
  
  // Fetch shareable link and collaborators when dialog opens
  useEffect(() => {
    if (open) {
      generateShareableLink(collection.id);
      fetchCollaborators(collection.id);
    }
  }, [open, collection.id, generateShareableLink, fetchCollaborators]);
  
  // Reset copied state when dialog closes
  useEffect(() => {
    if (!open) {
      setCopied(false);
      setSearchQuery('');
      setSearchResults([]);
    }
  }, [open]);
  
  // Handle copy link
  const handleCopyLink = () => {
    if (shareableLink) {
      navigator.clipboard.writeText(shareableLink);
      setCopied(true);
      
      toast({
        title: 'Link copied',
        description: 'The collection link has been copied to your clipboard.',
      });
      
      // Reset copied state after 2 seconds
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    }
  };
  
  // Handle search for collaborators
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    
    // In a real app, you would call an API to search for users
    // For now, we'll use mock data
    if (query.trim().length > 0) {
      // Mock search results
      const mockResults: User[] = [
        {
          id: 'user1',
          name: 'Emma Wilson',
          username: 'emmaw',
          avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
        },
        {
          id: 'user2',
          name: 'Michael Chen',
          username: 'michaelc',
          avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
        },
        {
          id: 'user3',
          name: 'Sophia Rodriguez',
          username: 'sophiar',
          avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
        },
      ].filter(
        (user) =>
          user.name.toLowerCase().includes(query.toLowerCase()) ||
          user.username.toLowerCase().includes(query.toLowerCase())
      );
      
      setSearchResults(mockResults);
    } else {
      setSearchResults([]);
    }
  };
  
  // Handle add collaborator
  const handleAddCollaborator = async (userId: string) => {
    try {
      await addCollaboratorToCollection(collection.id, userId);
      
      toast({
        title: 'Collaborator added',
        description: 'The user has been added as a collaborator.',
      });
      
      // Clear search
      setSearchQuery('');
      setSearchResults([]);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to add collaborator. Please try again.',
        variant: 'destructive',
      });
    }
  };
  
  // Handle remove collaborator
  const handleRemoveCollaborator = async (userId: string) => {
    try {
      await removeCollaboratorFromCollection(collection.id, userId);
      
      toast({
        title: 'Collaborator removed',
        description: 'The user has been removed as a collaborator.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to remove collaborator. Please try again.',
        variant: 'destructive',
      });
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Share collection</DialogTitle>
          <DialogDescription>
            Share your collection with others or add collaborators.
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'link' | 'collaborators')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="link">Share Link</TabsTrigger>
            <TabsTrigger value="collaborators" disabled={!collection.isCollaborative}>
              Collaborators
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="link" className="space-y-4 py-4">
            <div className="space-y-2">
              <p className="text-sm text-gray-500">
                {collection.isPublic
                  ? 'Anyone with the link can view this collection.'
                  : 'This collection is private. Make it public to share with others.'}
              </p>
              
              {!collection.isPublic && (
                <div className="bg-yellow-50 p-3 rounded-md text-sm text-yellow-800">
                  This collection is private. To share it, please edit the collection and make it public.
                </div>
              )}
              
              {collection.isPublic && (
                <div className="flex space-x-2">
                  <Input
                    value={shareableLink || 'Generating link...'}
                    readOnly
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleCopyLink}
                    disabled={!shareableLink || isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : copied ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="collaborators" className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  placeholder="Search for users..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                />
              </div>
              
              {searchQuery.trim().length > 0 && (
                <div className="border rounded-md overflow-hidden">
                  {searchResults.length === 0 ? (
                    <div className="p-4 text-center text-sm text-gray-500">
                      No users found
                    </div>
                  ) : (
                    <div className="divide-y">
                      {searchResults.map((user) => (
                        <div
                          key={user.id}
                          className="flex items-center justify-between p-3 hover:bg-gray-50"
                        >
                          <div className="flex items-center space-x-3">
                            <Avatar src={user.avatar} alt={user.name} />
                            <div>
                              <p className="font-medium">{user.name}</p>
                              <p className="text-sm text-gray-500">@{user.username}</p>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleAddCollaborator(user.id)}
                          >
                            <Plus className="h-4 w-4 mr-1" />
                            Add
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
              
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Current collaborators</h4>
                
                {collaborators.length === 0 ? (
                  <div className="text-center p-4 text-sm text-gray-500 border rounded-md">
                    No collaborators yet
                  </div>
                ) : (
                  <div className="border rounded-md overflow-hidden divide-y">
                    {collaborators.map((user) => (
                      <div
                        key={user.id}
                        className="flex items-center justify-between p-3 hover:bg-gray-50"
                      >
                        <div className="flex items-center space-x-3">
                          <Avatar src={user.avatar} alt={user.name} />
                          <div>
                            <p className="font-medium">{user.name}</p>
                            <p className="text-sm text-gray-500">@{user.username}</p>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveCollaborator(user.id)}
                          className="text-red-500 hover:text-red-600 hover:bg-red-50"
                        >
                          <X className="h-4 w-4 mr-1" />
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>
            Done
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
