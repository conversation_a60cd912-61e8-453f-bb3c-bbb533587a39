import { useEffect } from 'react';
import { ArrowUp, ArrowDown, Eye, Heart, MessageCircle, Share2, Users } from 'lucide-react';
import { PerformanceChart } from './PerformanceChart';
import { AudienceInsights } from './AudienceInsights';
import { ContentPerformance } from './ContentPerformance';
import { useAnalyticsStore } from '../../store/analytics';

interface AnalyticsDashboardProps {
  className?: string;
}

export function AnalyticsDashboard({ className = '' }: AnalyticsDashboardProps) {
  const {
    overview,
    viewsData,
    engagementData,
    audienceDemographics,
    contentPerformance,
    revenueData,
    isLoading,
    error,
    timeRange,
    granularity,
    contentTypeFilter,
    sortBy,
    fetchOverview,
    fetchViewsData,
    fetchEngagementData,
    fetchAudienceDemographics,
    fetchContentPerformance,
    fetchRevenueData,
    fetchContentItemAnalytics,
    exportData,
    setTimeRange,
    setGranularity,
    setContentTypeFilter,
    setSortBy,
  } = useAnalyticsStore();
  
  // Fetch data on mount
  useEffect(() => {
    fetchOverview();
    fetchViewsData();
    fetchEngagementData();
    fetchAudienceDemographics();
    fetchContentPerformance();
    fetchRevenueData();
  }, []);
  
  // Format number with K/M suffix
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };
  
  // Handle content item selection
  const handleContentSelect = (contentId: string, contentType: 'video' | 'post' | 'story') => {
    fetchContentItemAnalytics(contentId, contentType);
  };
  
  // Handle export
  const handleExport = async (dataType: 'overview' | 'views' | 'engagement' | 'audience' | 'content' | 'revenue', format: 'csv' | 'json' | 'pdf') => {
    try {
      const blob = await exportData(dataType, format);
      
      // Create a download link
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analytics_${dataType}_${timeRange}.${format}`;
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  };
  
  if (error) {
    return (
      <div className={`p-8 text-center text-red-500 ${className}`}>
        <p>Error: {error}</p>
        <button
          onClick={() => {
            fetchOverview();
            fetchViewsData();
            fetchEngagementData();
            fetchAudienceDemographics();
            fetchContentPerformance();
            fetchRevenueData();
          }}
          className="mt-4 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          Try Again
        </button>
      </div>
    );
  }
  
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {/* Views */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-500 flex items-center">
                <Eye size={16} className="mr-1" />
                Views
              </p>
              <h3 className="text-2xl font-bold mt-1">
                {overview ? formatNumber(overview.totalViews) : '-'}
              </h3>
            </div>
            {overview && (
              <div className={`flex items-center text-sm ${
                overview.viewsGrowth >= 0 ? 'text-green-500' : 'text-red-500'
              }`}>
                {overview.viewsGrowth >= 0 ? (
                  <ArrowUp size={16} className="mr-1" />
                ) : (
                  <ArrowDown size={16} className="mr-1" />
                )}
                {Math.abs(overview.viewsGrowth).toFixed(1)}%
              </div>
            )}
          </div>
        </div>
        
        {/* Likes */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-500 flex items-center">
                <Heart size={16} className="mr-1" />
                Likes
              </p>
              <h3 className="text-2xl font-bold mt-1">
                {overview ? formatNumber(overview.totalLikes) : '-'}
              </h3>
            </div>
            {overview && (
              <div className={`flex items-center text-sm ${
                overview.likesGrowth >= 0 ? 'text-green-500' : 'text-red-500'
              }`}>
                {overview.likesGrowth >= 0 ? (
                  <ArrowUp size={16} className="mr-1" />
                ) : (
                  <ArrowDown size={16} className="mr-1" />
                )}
                {Math.abs(overview.likesGrowth).toFixed(1)}%
              </div>
            )}
          </div>
        </div>
        
        {/* Comments */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-500 flex items-center">
                <MessageCircle size={16} className="mr-1" />
                Comments
              </p>
              <h3 className="text-2xl font-bold mt-1">
                {overview ? formatNumber(overview.totalComments) : '-'}
              </h3>
            </div>
            {overview && (
              <div className={`flex items-center text-sm ${
                overview.commentsGrowth >= 0 ? 'text-green-500' : 'text-red-500'
              }`}>
                {overview.commentsGrowth >= 0 ? (
                  <ArrowUp size={16} className="mr-1" />
                ) : (
                  <ArrowDown size={16} className="mr-1" />
                )}
                {Math.abs(overview.commentsGrowth).toFixed(1)}%
              </div>
            )}
          </div>
        </div>
        
        {/* Shares */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-500 flex items-center">
                <Share2 size={16} className="mr-1" />
                Shares
              </p>
              <h3 className="text-2xl font-bold mt-1">
                {overview ? formatNumber(overview.totalShares) : '-'}
              </h3>
            </div>
            {overview && (
              <div className={`flex items-center text-sm ${
                overview.sharesGrowth >= 0 ? 'text-green-500' : 'text-red-500'
              }`}>
                {overview.sharesGrowth >= 0 ? (
                  <ArrowUp size={16} className="mr-1" />
                ) : (
                  <ArrowDown size={16} className="mr-1" />
                )}
                {Math.abs(overview.sharesGrowth).toFixed(1)}%
              </div>
            )}
          </div>
        </div>
        
        {/* Followers */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-500 flex items-center">
                <Users size={16} className="mr-1" />
                Followers
              </p>
              <h3 className="text-2xl font-bold mt-1">
                {overview ? formatNumber(overview.totalFollowers) : '-'}
              </h3>
            </div>
            {overview && (
              <div className={`flex items-center text-sm ${
                overview.followersGrowth >= 0 ? 'text-green-500' : 'text-red-500'
              }`}>
                {overview.followersGrowth >= 0 ? (
                  <ArrowUp size={16} className="mr-1" />
                ) : (
                  <ArrowDown size={16} className="mr-1" />
                )}
                {Math.abs(overview.followersGrowth).toFixed(1)}%
              </div>
            )}
          </div>
          <div className="mt-2 text-xs text-gray-500">
            {overview ? `+${formatNumber(overview.newFollowers)} new` : ''}
          </div>
        </div>
      </div>
      
      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Views Chart */}
        <PerformanceChart
          title="Views"
          subtitle="Track your content views over time"
          type="views"
          data={viewsData}
          timeRange={timeRange}
          granularity={granularity}
          onTimeRangeChange={setTimeRange}
          onGranularityChange={setGranularity}
          onRefresh={fetchViewsData}
          onExport={(format) => handleExport('views', format)}
          isLoading={isLoading}
          chartType="area"
        />
        
        {/* Engagement Chart */}
        <PerformanceChart
          title="Engagement"
          subtitle="Track likes, comments, and shares"
          type="engagement"
          data={engagementData}
          timeRange={timeRange}
          granularity={granularity}
          onTimeRangeChange={setTimeRange}
          onGranularityChange={setGranularity}
          onRefresh={fetchEngagementData}
          onExport={(format) => handleExport('engagement', format)}
          isLoading={isLoading}
          chartType="line"
        />
      </div>
      
      {/* Audience Insights */}
      <AudienceInsights
        data={audienceDemographics}
        onRefresh={fetchAudienceDemographics}
        onExport={(format) => handleExport('audience', format)}
        isLoading={isLoading}
      />
      
      {/* Content Performance */}
      <ContentPerformance
        data={contentPerformance}
        onRefresh={fetchContentPerformance}
        onExport={(format) => handleExport('content', format)}
        onContentTypeChange={setContentTypeFilter}
        onSortByChange={setSortBy}
        contentTypeFilter={contentTypeFilter}
        sortBy={sortBy}
        isLoading={isLoading}
        onContentSelect={handleContentSelect}
      />
    </div>
  );
}
