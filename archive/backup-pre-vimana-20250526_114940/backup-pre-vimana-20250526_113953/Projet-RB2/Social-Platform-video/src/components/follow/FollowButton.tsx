import { useState } from 'react';
import { <PERSON>r<PERSON><PERSON>, <PERSON>r<PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';

interface FollowButtonProps {
  userId: string;
  initialIsFollowing: boolean;
  onFollow: (userId: string) => Promise<void>;
  onUnfollow: (userId: string) => Promise<void>;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
}

export function FollowButton({
  userId,
  initialIsFollowing,
  onFollow,
  onUnfollow,
  size = 'md',
  showText = true,
  className = '',
}: FollowButtonProps) {
  const [isFollowing, setIsFollowing] = useState(initialIsFollowing);
  const [isLoading, setIsLoading] = useState(false);
  
  const handleToggleFollow = async () => {
    setIsLoading(true);
    try {
      if (isFollowing) {
        await onUnfollow(userId);
      } else {
        await onFollow(userId);
      }
      setIsFollowing(!isFollowing);
    } catch (error) {
      console.error('Failed to toggle follow status:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Size configurations
  const sizeConfig = {
    sm: {
      button: 'px-2 py-1 text-xs',
      icon: 14,
    },
    md: {
      button: 'px-3 py-1.5 text-sm',
      icon: 16,
    },
    lg: {
      button: 'px-4 py-2 text-base',
      icon: 18,
    },
  };
  
  const { button, icon } = sizeConfig[size];
  
  return (
    <button
      onClick={handleToggleFollow}
      disabled={isLoading}
      className={`
        rounded-full font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2
        ${isFollowing 
          ? 'bg-gray-200 text-gray-800 hover:bg-gray-300' 
          : 'bg-green-500 text-white hover:bg-green-600'
        }
        ${button}
        ${className}
      `}
    >
      <div className="flex items-center justify-center space-x-1">
        {isLoading ? (
          <Loader2 size={icon} className="animate-spin" />
        ) : isFollowing ? (
          <UserCheck size={icon} />
        ) : (
          <UserPlus size={icon} />
        )}
        
        {showText && (
          <span>{isFollowing ? 'Following' : 'Follow'}</span>
        )}
      </div>
    </button>
  );
}
