import { useState } from 'react';
import { Search, X, ChevronDown } from 'lucide-react';
import { Avatar } from '../ui/avatar';
import { FollowButton } from './FollowButton';

export interface User {
  id: string;
  name: string;
  username: string;
  avatar: string;
  isFollowing: boolean;
  bio?: string;
}

interface FollowersListProps {
  users: User[];
  title: string;
  emptyMessage: string;
  onFollow: (userId: string) => Promise<void>;
  onUnfollow: (userId: string) => Promise<void>;
  onClose?: () => void;
  isModal?: boolean;
}

export function FollowersList({
  users,
  title,
  emptyMessage,
  onFollow,
  onUnfollow,
  onClose,
  isModal = false,
}: FollowersListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'recent' | 'alphabetical'>('recent');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  
  // Filter users based on search query
  const filteredUsers = users.filter((user) => {
    const query = searchQuery.toLowerCase();
    return (
      user.name.toLowerCase().includes(query) ||
      user.username.toLowerCase().includes(query)
    );
  });
  
  // Sort users based on selected sort option
  const sortedUsers = [...filteredUsers].sort((a, b) => {
    if (sortBy === 'alphabetical') {
      return a.name.localeCompare(b.name);
    }
    // Default is 'recent', but we don't have a timestamp in our mock data
    // In a real app, you would sort by the follow date
    return 0;
  });
  
  const toggleUserSelection = (userId: string) => {
    setSelectedUsers((prev) =>
      prev.includes(userId)
        ? prev.filter((id) => id !== userId)
        : [...prev, userId]
    );
  };
  
  const handleBatchAction = async (action: 'follow' | 'unfollow') => {
    const actionFn = action === 'follow' ? onFollow : onUnfollow;
    
    // Process each selected user sequentially
    for (const userId of selectedUsers) {
      try {
        await actionFn(userId);
      } catch (error) {
        console.error(`Failed to ${action} user ${userId}:`, error);
      }
    }
    
    // Clear selection after batch action
    setSelectedUsers([]);
  };
  
  return (
    <div className={`bg-white ${isModal ? 'rounded-lg shadow-lg' : ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <h2 className="text-lg font-semibold">{title}</h2>
        
        {isModal && onClose && (
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <X size={20} />
          </button>
        )}
      </div>
      
      {/* Search and filters */}
      <div className="p-4 border-b">
        <div className="relative">
          <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search users..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
          />
        </div>
        
        <div className="flex justify-between mt-3">
          <div className="flex items-center">
            <span className="text-sm text-gray-500 mr-2">Sort by:</span>
            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as 'recent' | 'alphabetical')}
                className="appearance-none bg-white border border-gray-300 rounded-md px-3 py-1 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <option value="recent">Recent</option>
                <option value="alphabetical">A-Z</option>
              </select>
              <ChevronDown size={14} className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 pointer-events-none" />
            </div>
          </div>
          
          {selectedUsers.length > 0 && (
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleBatchAction('follow')}
                className="px-3 py-1 text-xs bg-green-500 text-white rounded-full hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
              >
                Follow All
              </button>
              <button
                onClick={() => handleBatchAction('unfollow')}
                className="px-3 py-1 text-xs bg-gray-200 text-gray-800 rounded-full hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2"
              >
                Unfollow All
              </button>
              <button
                onClick={() => setSelectedUsers([])}
                className="px-3 py-1 text-xs bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2"
              >
                Clear ({selectedUsers.length})
              </button>
            </div>
          )}
        </div>
      </div>
      
      {/* User list */}
      {sortedUsers.length === 0 ? (
        <div className="p-8 text-center text-gray-500">
          <p>{emptyMessage}</p>
        </div>
      ) : (
        <div className="divide-y">
          {sortedUsers.map((user) => (
            <div key={user.id} className="flex items-center justify-between p-4 hover:bg-gray-50">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={selectedUsers.includes(user.id)}
                  onChange={() => toggleUserSelection(user.id)}
                  className="h-4 w-4 text-green-500 focus:ring-green-500 border-gray-300 rounded"
                />
                <Avatar src={user.avatar} alt={user.name} className="w-10 h-10" />
                <div>
                  <h3 className="font-medium">{user.name}</h3>
                  <p className="text-sm text-gray-500">@{user.username}</p>
                </div>
              </div>
              
              <FollowButton
                userId={user.id}
                initialIsFollowing={user.isFollowing}
                onFollow={onFollow}
                onUnfollow={onUnfollow}
                size="sm"
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
