import React, { useState } from 'react';
import { 
  SelectionProvider, 
  SelectableList, 
  SelectionToggle, 
  SelectionToolbar 
} from '../selection';
import { Post } from '../../types';
import { Trash2, Share2, Archive, Pin, Tag } from 'lucide-react';

// Props pour la liste de blogs avec sélection
interface BlogListWithSelectionProps {
  // Liste des articles de blog à afficher
  posts: Post[];
  // Classe CSS pour la liste
  className?: string;
  // Fonction appelée lorsqu'un article est cliqué
  onPostClick?: (post: Post) => void;
  // Actions personnalisées pour la barre d'outils de sélection
  selectionActions?: Array<{
    icon: React.ReactNode;
    label: string;
    onClick: (selectedPosts: Post[]) => void;
    disabled?: boolean;
    show?: (count: number) => boolean;
    textColor?: string;
  }>;
}

// Composant pour la liste de blogs avec sélection
export function BlogListWithSelection({
  posts,
  className = '',
  onPostClick,
  selectionActions,
}: BlogListWithSelectionProps) {
  // État pour stocker l'ID de l'article survolé
  const [hoveredPostId, setHoveredPostId] = useState<string | null>(null);

  // Actions par défaut pour la sélection d'articles
  const defaultActions = [
    {
      icon: <Trash2 size={18} />,
      label: 'Supprimer',
      onClick: (selectedPosts: Post[]) => {
        console.log('Supprimer articles:', selectedPosts);
        // Implémenter la logique de suppression ici
      },
      textColor: 'text-red-500',
    },
    {
      icon: <Share2 size={18} />,
      label: 'Partager',
      onClick: (selectedPosts: Post[]) => {
        console.log('Partager articles:', selectedPosts);
        // Implémenter la logique de partage ici
      },
    },
    {
      icon: <Archive size={18} />,
      label: 'Archiver',
      onClick: (selectedPosts: Post[]) => {
        console.log('Archiver articles:', selectedPosts);
        // Implémenter la logique d'archivage ici
      },
    },
    {
      icon: <Pin size={18} />,
      label: 'Épingler',
      onClick: (selectedPosts: Post[]) => {
        console.log('Épingler articles:', selectedPosts);
        // Implémenter la logique d'épinglage ici
      },
      show: (count: number) => count === 1, // On ne peut épingler qu'un seul article
    },
    {
      icon: <Tag size={18} />,
      label: 'Catégoriser',
      onClick: (selectedPosts: Post[]) => {
        console.log('Catégoriser articles:', selectedPosts);
        // Implémenter la logique de catégorisation ici
      },
    },
  ];

  // Fonction pour gérer le clic sur un article
  const handlePostClick = (post: Post) => {
    if (onPostClick) {
      onPostClick(post);
    }
  };

  // Fonction pour rendre un article de blog
  const renderBlogPost = (post: Post) => {
    const isHovered = hoveredPostId === post.id;
    
    return (
      <div 
        className={`
          relative p-4 border rounded-lg
          hover:shadow-md transition-all duration-200
          ${isHovered ? 'border-blue-200 bg-blue-50' : 'border-gray-200'}
        `}
        onMouseEnter={() => setHoveredPostId(post.id)}
        onMouseLeave={() => setHoveredPostId(null)}
        onClick={() => handlePostClick(post)}
      >
        <div className="flex items-start">
          {/* Vignette de vidéo (si disponible) */}
          {post.videoUrl && (
            <div className="relative w-24 h-16 mr-4 flex-shrink-0 rounded overflow-hidden">
              <img 
                src={post.user.avatar} // Utiliser une vignette de vidéo si disponible
                alt="Video thumbnail" 
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                <div className="w-8 h-8 rounded-full bg-white bg-opacity-70 flex items-center justify-center">
                  <svg className="w-4 h-4 text-gray-800" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M6.5 5.5L15 10l-8.5 4.5v-9z" />
                  </svg>
                </div>
              </div>
            </div>
          )}
          
          {/* Contenu de l'article */}
          <div className="flex-grow">
            <h3 className="font-medium text-gray-800 mb-1 line-clamp-2">
              {post.caption}
            </h3>
            
            <div className="flex items-center">
              <img 
                src={post.user.avatar} 
                alt={post.user.name} 
                className="w-6 h-6 rounded-full mr-2"
              />
              <span className="text-sm text-gray-600">
                {post.user.name}
              </span>
            </div>
            
            <div className="flex items-center justify-between mt-2">
              <div className="flex items-center text-xs text-gray-500">
                <span>{new Date(post.createdAt).toLocaleDateString()}</span>
                <span className="mx-1">•</span>
                <span>{post.comments} commentaires</span>
              </div>
              
              <div className="flex items-center text-xs">
                <span className="flex items-center text-gray-500">
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 12.5l-5.7 3 1.1-6.3-4.7-4.5 6.4-.9L10 0l2.9 5.8 6.4.9-4.7 4.5 1.1 6.3z" />
                  </svg>
                  {post.likes}
                </span>
              </div>
            </div>
            
            {post.hashtags && post.hashtags.length > 0 && (
              <div className="mt-2 flex flex-wrap gap-1">
                {post.hashtags.map((tag, index) => (
                  <span 
                    key={index} 
                    className="text-xs bg-gray-100 text-blue-600 rounded-full px-2 py-0.5"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <SelectionProvider<Post> initialMode="none">
      <div className="relative">
        {/* Bouton pour activer/désactiver la sélection */}
        <div className="mb-4 flex justify-end">
          <SelectionToggle />
        </div>
        
        {/* Liste d'articles sélectionnables */}
        <SelectableList<Post>
          items={posts}
          keyExtractor={(post) => post.id}
          renderItem={renderBlogPost}
          className={`space-y-4 ${className}`}
          itemClassName="relative"
          selectedItemClassName="ring-2 ring-blue-500 bg-blue-50"
        />
        
        {/* Barre d'outils de sélection */}
        <SelectionToolbar<Post>
          actions={selectionActions || defaultActions}
          position="bottom"
          fixed={true}
          className="border-t border-gray-200"
        />
      </div>
    </SelectionProvider>
  );
} 