import { useState } from 'react';
import { CommentItem, Comment } from './CommentItem';
import { CommentForm } from './CommentForm';

interface CommentListProps {
  postId: string;
  comments: Comment[];
  onAddComment: (content: { content: string }, parentId?: string) => void;
  onLikeComment: (commentId: string) => void;
}

export function CommentList({ 
  postId, 
  comments, 
  onAddComment, 
  onLikeComment 
}: CommentListProps) {
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'popular'>('newest');
  
  // Sort comments based on the selected sort option
  const sortedComments = [...comments].sort((a, b) => {
    if (sortBy === 'newest') {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    } else if (sortBy === 'oldest') {
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
    } else {
      // Popular - sort by likes
      return b.likes - a.likes;
    }
  });

  return (
    <div className="mt-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Comments ({comments.length})</h3>
        
        <div className="flex items-center">
          <span className="text-sm text-gray-500 mr-2">Sort by:</span>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'newest' | 'oldest' | 'popular')}
            className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            <option value="newest">Newest</option>
            <option value="oldest">Oldest</option>
            <option value="popular">Popular</option>
          </select>
        </div>
      </div>
      
      <div className="mb-6">
        <CommentForm 
          postId={postId} 
          onCommentSubmit={onAddComment} 
          placeholder="Add a comment..."
        />
      </div>
      
      {sortedComments.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>No comments yet. Be the first to comment!</p>
        </div>
      ) : (
        <div className="space-y-4">
          {sortedComments.map((comment) => (
            <CommentItem
              key={comment.id}
              comment={comment}
              onLike={onLikeComment}
              onReply={onAddComment}
            />
          ))}
        </div>
      )}
    </div>
  );
}
