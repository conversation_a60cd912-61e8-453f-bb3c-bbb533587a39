import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Send, Smile } from 'lucide-react';

const commentSchema = z.object({
  content: z.string().min(1, 'Comment cannot be empty').max(1000, 'Comment is too long'),
});

type CommentFormData = z.infer<typeof commentSchema>;

interface CommentFormProps {
  postId: string;
  parentId?: string;
  onCommentSubmit: (data: CommentFormData, parentId?: string) => void;
  placeholder?: string;
}

export function CommentForm({ 
  postId, 
  parentId, 
  onCommentSubmit, 
  placeholder = 'Add a comment...' 
}: CommentFormProps) {
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  
  const { 
    register, 
    handleSubmit, 
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting } 
  } = useForm<CommentFormData>({
    resolver: zod<PERSON><PERSON><PERSON>ver(commentSchema),
    defaultValues: {
      content: '',
    }
  });

  const content = watch('content');

  const onSubmit = async (data: CommentFormData) => {
    try {
      await onCommentSubmit(data, parentId);
      reset();
    } catch (error) {
      console.error('Failed to submit comment:', error);
    }
  };

  const insertEmoji = (emoji: string) => {
    setValue('content', `${content}${emoji}`);
  };

  // Mock emoji picker - in a real app, you'd use a proper emoji picker library
  const mockEmojis = ['😀', '😂', '😍', '🥰', '😎', '👍', '❤️', '🔥'];

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="w-full">
      <div className="relative">
        <textarea
          {...register('content')}
          className={`w-full p-3 pr-12 rounded-lg border ${
            errors.content ? 'border-red-500' : 'border-gray-300'
          } focus:outline-none focus:ring-2 focus:ring-green-500 resize-none min-h-[60px]`}
          placeholder={placeholder}
          rows={2}
        />
        
        <div className="absolute right-2 bottom-2 flex items-center space-x-2">
          <button
            type="button"
            onClick={() => setShowEmojiPicker(!showEmojiPicker)}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <Smile size={20} />
          </button>
          
          <button
            type="submit"
            disabled={isSubmitting || !content.trim()}
            className={`p-1.5 rounded-full ${
              content.trim() ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-500'
            } focus:outline-none`}
          >
            <Send size={18} />
          </button>
        </div>
        
        {showEmojiPicker && (
          <div className="absolute right-0 bottom-14 bg-white border border-gray-200 rounded-lg shadow-lg p-2 z-10">
            <div className="grid grid-cols-4 gap-2">
              {mockEmojis.map((emoji) => (
                <button
                  key={emoji}
                  type="button"
                  onClick={() => {
                    insertEmoji(emoji);
                    setShowEmojiPicker(false);
                  }}
                  className="text-xl hover:bg-gray-100 p-1 rounded"
                >
                  {emoji}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
      
      {errors.content && (
        <p className="mt-1 text-sm text-red-500">{errors.content.message}</p>
      )}
      
      <div className="mt-1 text-xs text-gray-500 flex justify-between">
        <span>{content.length}/1000 characters</span>
      </div>
    </form>
  );
}
