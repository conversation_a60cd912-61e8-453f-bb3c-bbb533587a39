import { useState, useEffect } from 'react';
import { useCollaborationStore } from '../../store/collaboration';
import { CollaborationRequest } from '../../api/collaborationApi';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Check, X, Clock, Video, FileText, Radio, Loader2, MessageSquare } from 'lucide-react';

interface CollaborationRequestsProps {
  className?: string;
}

export function CollaborationRequests({ className = '' }: CollaborationRequestsProps) {
  const {
    collaborationRequests,
    isLoading,
    error,
    fetchCollaborationRequests,
    respondToRequest,
  } = useCollaborationStore();
  
  const [activeTab, setActiveTab] = useState<'received' | 'sent'>('received');
  const [activeFilter, setActiveFilter] = useState<'pending' | 'accepted' | 'declined' | 'all'>('pending');
  const [expandedRequestId, setExpandedRequestId] = useState<string | null>(null);
  
  // Fetch requests on mount and when tab/filter changes
  useEffect(() => {
    fetchCollaborationRequests(activeFilter, activeTab);
  }, [activeTab, activeFilter, fetchCollaborationRequests]);
  
  // Filter requests based on active tab
  const filteredRequests = collaborationRequests.filter((request) => {
    if (activeTab === 'received') {
      return true; // The API already filters by type
    } else {
      return true; // The API already filters by type
    }
  });
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(date);
  };
  
  // Get content type icon
  const getContentTypeIcon = (type: 'video' | 'post' | 'livestream') => {
    switch (type) {
      case 'video':
        return <Video size={16} className="text-blue-500" />;
      case 'post':
        return <FileText size={16} className="text-green-500" />;
      case 'livestream':
        return <Radio size={16} className="text-red-500" />;
      default:
        return null;
    }
  };
  
  // Get status badge
  const getStatusBadge = (status: 'pending' | 'accepted' | 'declined') => {
    switch (status) {
      case 'pending':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock size={12} className="mr-1" />
            Pending
          </span>
        );
      case 'accepted':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <Check size={12} className="mr-1" />
            Accepted
          </span>
        );
      case 'declined':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <X size={12} className="mr-1" />
            Declined
          </span>
        );
      default:
        return null;
    }
  };
  
  // Handle responding to a request
  const handleRespondToRequest = async (requestId: string, accept: boolean) => {
    await respondToRequest(requestId, accept);
    // Refresh the list after response
    fetchCollaborationRequests(activeFilter, activeTab);
  };
  
  // Render request card
  const renderRequestCard = (request: CollaborationRequest) => {
    const isExpanded = expandedRequestId === request.id;
    const isPending = request.status === 'pending';
    
    return (
      <div
        key={request.id}
        className={`bg-white rounded-lg shadow-sm border ${
          isPending ? 'border-yellow-200' : request.status === 'accepted' ? 'border-green-200' : 'border-red-200'
        } overflow-hidden mb-4`}
      >
        <div
          className="p-4 cursor-pointer"
          onClick={() => setExpandedRequestId(isExpanded ? null : request.id)}
        >
          <div className="flex justify-between items-start">
            <div className="flex items-center">
              <div className="h-10 w-10 rounded-full overflow-hidden bg-gray-200 mr-3">
                <img
                  src={activeTab === 'received' ? request.fromAvatar : request.toAvatar}
                  alt={activeTab === 'received' ? request.fromName : request.toName}
                  className="h-full w-full object-cover"
                />
              </div>
              <div>
                <h3 className="text-sm font-medium">
                  {activeTab === 'received' ? request.fromName : request.toName}
                </h3>
                <p className="text-xs text-gray-500">
                  @{activeTab === 'received' ? request.fromUsername : request.toUsername}
                </p>
              </div>
            </div>
            
            <div className="flex items-center">
              {getStatusBadge(request.status)}
            </div>
          </div>
          
          <div className="mt-3">
            <div className="flex items-center text-sm text-gray-700">
              <span className="mr-2">Wants to collaborate on:</span>
              <div className="flex items-center">
                {getContentTypeIcon(request.contentType)}
                <span className="ml-1 font-medium">{request.contentTitle}</span>
              </div>
            </div>
            
            <div className="flex items-center text-xs text-gray-500 mt-1">
              <span className="mr-2">Role:</span>
              <span className="capitalize">{request.role}</span>
            </div>
            
            <div className="text-xs text-gray-500 mt-1">
              {formatDate(request.createdAt)}
            </div>
          </div>
        </div>
        
        {isExpanded && (
          <div className="px-4 pb-4 border-t border-gray-100 pt-3">
            {request.message && (
              <div className="mb-4">
                <h4 className="text-xs font-medium text-gray-500 mb-1 flex items-center">
                  <MessageSquare size={12} className="mr-1" />
                  Message
                </h4>
                <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-md">
                  {request.message}
                </p>
              </div>
            )}
            
            {activeTab === 'received' && isPending && (
              <div className="flex justify-end space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRespondToRequest(request.id, false);
                  }}
                  className="px-3 py-1.5 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 flex items-center"
                >
                  <X size={16} className="mr-1" />
                  Decline
                </button>
                
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRespondToRequest(request.id, true);
                  }}
                  className="px-3 py-1.5 text-sm bg-green-100 text-green-700 rounded-md hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center"
                >
                  <Check size={16} className="mr-1" />
                  Accept
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };
  
  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Collaboration Requests</h2>
      </div>
      
      <Tabs defaultValue="received" value={activeTab} onValueChange={(value) => setActiveTab(value as 'received' | 'sent')}>
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="received">Received</TabsTrigger>
          <TabsTrigger value="sent">Sent</TabsTrigger>
        </TabsList>
        
        <div className="flex mb-4 space-x-2">
          <button
            onClick={() => setActiveFilter('pending')}
            className={`px-3 py-1.5 text-sm rounded-md ${
              activeFilter === 'pending'
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Pending
          </button>
          
          <button
            onClick={() => setActiveFilter('accepted')}
            className={`px-3 py-1.5 text-sm rounded-md ${
              activeFilter === 'accepted'
                ? 'bg-green-100 text-green-800'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Accepted
          </button>
          
          <button
            onClick={() => setActiveFilter('declined')}
            className={`px-3 py-1.5 text-sm rounded-md ${
              activeFilter === 'declined'
                ? 'bg-red-100 text-red-800'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Declined
          </button>
          
          <button
            onClick={() => setActiveFilter('all')}
            className={`px-3 py-1.5 text-sm rounded-md ${
              activeFilter === 'all'
                ? 'bg-blue-100 text-blue-800'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            All
          </button>
        </div>
        
        <TabsContent value="received">
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <Loader2 size={24} className="animate-spin text-blue-500" />
            </div>
          ) : error ? (
            <div className="p-4 text-center text-red-500">
              <p>Error: {error}</p>
              <button
                onClick={() => fetchCollaborationRequests(activeFilter, activeTab)}
                className="mt-2 px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              >
                Try Again
              </button>
            </div>
          ) : filteredRequests.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
              <p className="text-gray-500">No {activeFilter !== 'all' ? activeFilter : ''} collaboration requests received.</p>
            </div>
          ) : (
            <div>
              {filteredRequests.map(renderRequestCard)}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="sent">
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <Loader2 size={24} className="animate-spin text-blue-500" />
            </div>
          ) : error ? (
            <div className="p-4 text-center text-red-500">
              <p>Error: {error}</p>
              <button
                onClick={() => fetchCollaborationRequests(activeFilter, activeTab)}
                className="mt-2 px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              >
                Try Again
              </button>
            </div>
          ) : filteredRequests.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
              <p className="text-gray-500">No {activeFilter !== 'all' ? activeFilter : ''} collaboration requests sent.</p>
            </div>
          ) : (
            <div>
              {filteredRequests.map(renderRequestCard)}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
