import { useState, useEffect, useRef } from 'react';
import { useCollaborationStore } from '../../store/collaboration';
import { CollaborativeContent, Collaborator, Comment } from '../../api/collaborationApi';
import { 
  Users, 
  MessageSquare, 
  Clock, 
  Save, 
  Eye, 
  Edit2, 
  Trash2, 
  CheckCircle, 
  Circle, 
  UserPlus, 
  Loader2,
  Video,
  FileText,
  Radio,
  Globe,
  Lock,
  Link
} from 'lucide-react';
import { CollaboratorSearch } from './CollaboratorSearch';

interface CollaborativeEditorProps {
  contentId: string;
  contentType: 'video' | 'post' | 'livestream';
  className?: string;
}

export function CollaborativeEditor({
  contentId,
  contentType,
  className = '',
}: CollaborativeEditorProps) {
  const {
    currentContent,
    collaborators,
    collaborationComments,
    isLoading,
    error,
    fetchContent,
    fetchCollaborators,
    fetchCollaborationComments,
    updateContent,
    addComment,
    updateComment,
    deleteComment,
    toggleCommentResolution,
    removeContentCollaborator,
    updateCollaboratorRole,
  } = useCollaborationStore();
  
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState<Partial<CollaborativeContent>>({});
  const [newComment, setNewComment] = useState('');
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [editedCommentText, setEditedCommentText] = useState('');
  const [showAddCollaborator, setShowAddCollaborator] = useState(false);
  const [activeTab, setActiveTab] = useState<'editor' | 'comments' | 'collaborators'>('editor');
  const [isSaving, setIsSaving] = useState(false);
  
  const commentsContainerRef = useRef<HTMLDivElement>(null);
  
  // Fetch content, collaborators, and comments on mount
  useEffect(() => {
    fetchContent(contentId, contentType);
    fetchCollaborators(contentId, contentType);
    fetchCollaborationComments(contentId, contentType);
  }, [contentId, contentType, fetchContent, fetchCollaborators, fetchCollaborationComments]);
  
  // Set edited content when current content changes
  useEffect(() => {
    if (currentContent) {
      setEditedContent({
        title: currentContent.title,
        description: currentContent.description,
        visibility: currentContent.visibility,
      });
    }
  }, [currentContent]);
  
  // Scroll to bottom of comments when new comments are added
  useEffect(() => {
    if (commentsContainerRef.current && activeTab === 'comments') {
      commentsContainerRef.current.scrollTop = commentsContainerRef.current.scrollHeight;
    }
  }, [collaborationComments, activeTab]);
  
  // Handle saving content changes
  const handleSaveContent = async () => {
    if (!currentContent || !editedContent.title || !editedContent.description) return;
    
    setIsSaving(true);
    
    try {
      await updateContent(contentId, contentType, editedContent);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving content:', error);
    } finally {
      setIsSaving(false);
    }
  };
  
  // Handle adding a comment
  const handleAddComment = async () => {
    if (!newComment.trim()) return;
    
    try {
      await addComment(contentId, contentType, newComment);
      setNewComment('');
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  };
  
  // Handle updating a comment
  const handleUpdateComment = async (commentId: string) => {
    if (!editedCommentText.trim()) return;
    
    try {
      await updateComment(contentId, contentType, commentId, editedCommentText);
      setEditingCommentId(null);
      setEditedCommentText('');
    } catch (error) {
      console.error('Error updating comment:', error);
    }
  };
  
  // Handle deleting a comment
  const handleDeleteComment = async (commentId: string) => {
    try {
      await deleteComment(contentId, contentType, commentId);
    } catch (error) {
      console.error('Error deleting comment:', error);
    }
  };
  
  // Handle toggling comment resolution
  const handleToggleResolution = async (commentId: string, isResolved: boolean) => {
    try {
      await toggleCommentResolution(contentId, contentType, commentId, !isResolved);
    } catch (error) {
      console.error('Error toggling comment resolution:', error);
    }
  };
  
  // Handle removing a collaborator
  const handleRemoveCollaborator = async (userId: string) => {
    try {
      await removeContentCollaborator(contentId, contentType, userId);
    } catch (error) {
      console.error('Error removing collaborator:', error);
    }
  };
  
  // Handle updating collaborator role
  const handleUpdateRole = async (userId: string, role: 'editor' | 'viewer') => {
    try {
      await updateCollaboratorRole(contentId, contentType, userId, role);
    } catch (error) {
      console.error('Error updating collaborator role:', error);
    }
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    }).format(date);
  };
  
  // Get content type icon
  const getContentTypeIcon = (type: 'video' | 'post' | 'livestream') => {
    switch (type) {
      case 'video':
        return <Video size={20} className="text-blue-500" />;
      case 'post':
        return <FileText size={20} className="text-green-500" />;
      case 'livestream':
        return <Radio size={20} className="text-red-500" />;
      default:
        return null;
    }
  };
  
  // Get visibility icon
  const getVisibilityIcon = (visibility: 'public' | 'private' | 'unlisted') => {
    switch (visibility) {
      case 'public':
        return <Globe size={16} className="text-green-500" />;
      case 'private':
        return <Lock size={16} className="text-red-500" />;
      case 'unlisted':
        return <Link size={16} className="text-yellow-500" />;
      default:
        return null;
    }
  };
  
  if (isLoading && !currentContent) {
    return (
      <div className={`flex justify-center items-center h-64 ${className}`}>
        <Loader2 size={32} className="animate-spin text-blue-500" />
      </div>
    );
  }
  
  if (error) {
    return (
      <div className={`p-8 text-center text-red-500 ${className}`}>
        <p>Error: {error}</p>
        <button
          onClick={() => fetchContent(contentId, contentType)}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Try Again
        </button>
      </div>
    );
  }
  
  if (!currentContent) {
    return (
      <div className={`p-8 text-center text-gray-500 ${className}`}>
        <p>Content not found or you don't have access to it.</p>
      </div>
    );
  }
  
  return (
    <div className={`bg-white rounded-lg shadow-md border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex justify-between items-start">
          <div className="flex items-center">
            {getContentTypeIcon(currentContent.type)}
            <h2 className="text-xl font-semibold ml-2">
              {isEditing ? (
                <input
                  type="text"
                  value={editedContent.title || ''}
                  onChange={(e) => setEditedContent({ ...editedContent, title: e.target.value })}
                  className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter title"
                />
              ) : (
                currentContent.title
              )}
            </h2>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="flex items-center text-sm text-gray-500">
              {getVisibilityIcon(currentContent.visibility)}
              <span className="ml-1 capitalize">{currentContent.visibility}</span>
            </div>
            
            {isEditing ? (
              <div className="flex space-x-2">
                <button
                  onClick={() => setIsEditing(false)}
                  className="px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                  Cancel
                </button>
                
                <button
                  onClick={handleSaveContent}
                  disabled={!editedContent.title || !editedContent.description || isSaving}
                  className="px-3 py-1.5 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
                >
                  {isSaving ? (
                    <>
                      <Loader2 size={16} className="animate-spin mr-1" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save size={16} className="mr-1" />
                      Save
                    </>
                  )}
                </button>
              </div>
            ) : (
              <button
                onClick={() => setIsEditing(true)}
                className="px-3 py-1.5 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center"
              >
                <Edit2 size={16} className="mr-1" />
                Edit
              </button>
            )}
          </div>
        </div>
        
        <div className="flex items-center text-sm text-gray-500 mt-1">
          <Clock size={14} className="mr-1" />
          Last updated: {formatDate(currentContent.updatedAt)}
        </div>
      </div>
      
      {/* Tabs */}
      <div className="flex border-b border-gray-200">
        <button
          onClick={() => setActiveTab('editor')}
          className={`flex-1 py-2 text-sm font-medium ${
            activeTab === 'editor'
              ? 'text-blue-500 border-b-2 border-blue-500'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <Edit2 size={16} className="inline mr-1" />
          Editor
        </button>
        
        <button
          onClick={() => setActiveTab('comments')}
          className={`flex-1 py-2 text-sm font-medium ${
            activeTab === 'comments'
              ? 'text-blue-500 border-b-2 border-blue-500'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <MessageSquare size={16} className="inline mr-1" />
          Comments ({collaborationComments.length})
        </button>
        
        <button
          onClick={() => setActiveTab('collaborators')}
          className={`flex-1 py-2 text-sm font-medium ${
            activeTab === 'collaborators'
              ? 'text-blue-500 border-b-2 border-blue-500'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <Users size={16} className="inline mr-1" />
          Collaborators ({collaborators.length})
        </button>
      </div>
      
      {/* Content */}
      <div className="p-4">
        {activeTab === 'editor' && (
          <div>
            {isEditing ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={editedContent.description || ''}
                    onChange={(e) => setEditedContent({ ...editedContent, description: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={6}
                    placeholder="Enter description"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Visibility
                  </label>
                  <div className="flex space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        checked={editedContent.visibility === 'public'}
                        onChange={() => setEditedContent({ ...editedContent, visibility: 'public' })}
                        className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 flex items-center">
                        <Globe size={14} className="text-green-500 mr-1" />
                        Public
                      </span>
                    </label>
                    
                    <label className="flex items-center">
                      <input
                        type="radio"
                        checked={editedContent.visibility === 'private'}
                        onChange={() => setEditedContent({ ...editedContent, visibility: 'private' })}
                        className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 flex items-center">
                        <Lock size={14} className="text-red-500 mr-1" />
                        Private
                      </span>
                    </label>
                    
                    <label className="flex items-center">
                      <input
                        type="radio"
                        checked={editedContent.visibility === 'unlisted'}
                        onChange={() => setEditedContent({ ...editedContent, visibility: 'unlisted' })}
                        className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 flex items-center">
                        <Link size={14} className="text-yellow-500 mr-1" />
                        Unlisted
                      </span>
                    </label>
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    {editedContent.visibility === 'public'
                      ? 'Anyone can find and view this content.'
                      : editedContent.visibility === 'private'
                      ? 'Only you and your collaborators can view this content.'
                      : 'Anyone with the link can view this content, but it won\'t appear in search results.'}
                  </p>
                </div>
              </div>
            ) : (
              <div className="prose max-w-none">
                <p>{currentContent.description}</p>
              </div>
            )}
          </div>
        )}
        
        {activeTab === 'comments' && (
          <div className="h-96 flex flex-col">
            <div
              ref={commentsContainerRef}
              className="flex-1 overflow-y-auto mb-4 space-y-4"
            >
              {collaborationComments.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No comments yet. Be the first to comment!</p>
                </div>
              ) : (
                collaborationComments.map((comment) => (
                  <div
                    key={comment.id}
                    className={`p-3 rounded-lg ${
                      comment.isResolved ? 'bg-gray-50' : 'bg-blue-50'
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex items-start">
                        <div className="h-8 w-8 rounded-full overflow-hidden bg-gray-200 mr-2">
                          <img
                            src={comment.avatar}
                            alt={comment.name}
                            className="h-full w-full object-cover"
                          />
                        </div>
                        <div>
                          <div className="flex items-center">
                            <span className="text-sm font-medium">{comment.name}</span>
                            <span className="text-xs text-gray-500 ml-2">
                              @{comment.username}
                            </span>
                          </div>
                          
                          {editingCommentId === comment.id ? (
                            <div className="mt-1">
                              <textarea
                                value={editedCommentText}
                                onChange={(e) => setEditedCommentText(e.target.value)}
                                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                rows={2}
                              />
                              <div className="flex justify-end mt-2 space-x-2">
                                <button
                                  onClick={() => {
                                    setEditingCommentId(null);
                                    setEditedCommentText('');
                                  }}
                                  className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none"
                                >
                                  Cancel
                                </button>
                                <button
                                  onClick={() => handleUpdateComment(comment.id)}
                                  className="px-2 py-1 text-xs bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none"
                                >
                                  Save
                                </button>
                              </div>
                            </div>
                          ) : (
                            <p className="text-sm mt-1">{comment.text}</p>
                          )}
                          
                          <div className="text-xs text-gray-500 mt-1">
                            {formatDate(comment.createdAt)}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex space-x-1">
                        <button
                          onClick={() => handleToggleResolution(comment.id, comment.isResolved)}
                          className={`p-1 rounded-full ${
                            comment.isResolved
                              ? 'text-green-500 hover:bg-green-100'
                              : 'text-gray-400 hover:bg-gray-100'
                          }`}
                          title={comment.isResolved ? 'Mark as unresolved' : 'Mark as resolved'}
                        >
                          {comment.isResolved ? (
                            <CheckCircle size={16} />
                          ) : (
                            <Circle size={16} />
                          )}
                        </button>
                        
                        <button
                          onClick={() => {
                            setEditingCommentId(comment.id);
                            setEditedCommentText(comment.text);
                          }}
                          className="p-1 text-gray-400 hover:text-blue-500 hover:bg-gray-100 rounded-full"
                          title="Edit comment"
                        >
                          <Edit2 size={16} />
                        </button>
                        
                        <button
                          onClick={() => handleDeleteComment(comment.id)}
                          className="p-1 text-gray-400 hover:text-red-500 hover:bg-gray-100 rounded-full"
                          title="Delete comment"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
            
            <div className="mt-auto">
              <div className="flex">
                <input
                  type="text"
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Add a comment..."
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleAddComment();
                    }
                  }}
                />
                <button
                  onClick={handleAddComment}
                  disabled={!newComment.trim()}
                  className="px-4 py-2 bg-blue-500 text-white rounded-r-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                >
                  Comment
                </button>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'collaborators' && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Collaborators</h3>
              
              <button
                onClick={() => setShowAddCollaborator(!showAddCollaborator)}
                className="px-3 py-1.5 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center"
              >
                {showAddCollaborator ? (
                  <>
                    <X size={16} className="mr-1" />
                    Cancel
                  </>
                ) : (
                  <>
                    <UserPlus size={16} className="mr-1" />
                    Add Collaborator
                  </>
                )}
              </button>
            </div>
            
            {showAddCollaborator && (
              <CollaboratorSearch
                contentId={contentId}
                contentType={contentType}
                contentTitle={currentContent.title}
                onClose={() => setShowAddCollaborator(false)}
                className="mb-4"
              />
            )}
            
            <div className="space-y-3">
              {/* Owner */}
              <div className="p-3 bg-blue-50 rounded-lg">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <div className="h-10 w-10 rounded-full overflow-hidden bg-gray-200 mr-3">
                      <img
                        src={currentContent.owner.avatar}
                        alt={currentContent.owner.name}
                        className="h-full w-full object-cover"
                      />
                    </div>
                    <div>
                      <div className="flex items-center">
                        <h4 className="text-sm font-medium">{currentContent.owner.name}</h4>
                        <span className="ml-2 px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full">
                          Owner
                        </span>
                      </div>
                      <p className="text-xs text-gray-500">@{currentContent.owner.username}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Collaborators */}
              {collaborators.length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  <p>No collaborators yet.</p>
                </div>
              ) : (
                collaborators.map((collaborator) => (
                  <div key={collaborator.id} className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="h-10 w-10 rounded-full overflow-hidden bg-gray-200 mr-3">
                          <img
                            src={collaborator.avatar}
                            alt={collaborator.name}
                            className="h-full w-full object-cover"
                          />
                        </div>
                        <div>
                          <div className="flex items-center">
                            <h4 className="text-sm font-medium">{collaborator.name}</h4>
                            <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                              collaborator.role === 'editor'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {collaborator.role === 'editor' ? 'Editor' : 'Viewer'}
                            </span>
                          </div>
                          <p className="text-xs text-gray-500">@{collaborator.username}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <select
                          value={collaborator.role}
                          onChange={(e) => handleUpdateRole(
                            collaborator.userId,
                            e.target.value as 'editor' | 'viewer'
                          )}
                          className="text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="editor">Editor</option>
                          <option value="viewer">Viewer</option>
                        </select>
                        
                        <button
                          onClick={() => handleRemoveCollaborator(collaborator.userId)}
                          className="p-1.5 text-gray-400 hover:text-red-500 hover:bg-gray-100 rounded-md"
                          title="Remove collaborator"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
