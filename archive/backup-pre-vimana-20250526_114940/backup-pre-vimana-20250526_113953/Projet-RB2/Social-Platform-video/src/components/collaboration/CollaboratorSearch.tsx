import { useState, useEffect, useRef } from 'react';
import { useCollaborationStore } from '../../store/collaboration';
import { Search, UserPlus, X, Loader2, MessageSquare } from 'lucide-react';

interface CollaboratorSearchProps {
  contentId: string;
  contentType: 'video' | 'post' | 'livestream';
  contentTitle: string;
  onClose: () => void;
  className?: string;
}

export function CollaboratorSearch({
  contentId,
  contentType,
  contentTitle,
  onClose,
  className = '',
}: CollaboratorSearchProps) {
  const {
    searchResults,
    isLoading,
    error,
    searchCollaborators,
    sendRequest,
    collaborators,
  } = useCollaborationStore();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [role, setRole] = useState<'editor' | 'viewer'>('editor');
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Focus search input on mount
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);
  
  // Debounce search
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    if (searchQuery.trim().length >= 2) {
      searchTimeoutRef.current = setTimeout(() => {
        searchCollaborators(searchQuery);
      }, 300);
    }
    
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchQuery, searchCollaborators]);
  
  // Filter out existing collaborators
  const filteredResults = searchResults.filter(
    (user) => !collaborators.some((collaborator) => collaborator.userId === user.id)
  );
  
  // Handle sending collaboration request
  const handleSendRequest = async () => {
    if (!selectedUserId || !message.trim()) return;
    
    setIsSending(true);
    
    try {
      await sendRequest(contentId, contentType, selectedUserId, role, message);
      setShowConfirmation(true);
      
      // Close the modal after a delay
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (error) {
      console.error('Error sending collaboration request:', error);
    } finally {
      setIsSending(false);
    }
  };
  
  return (
    <div className={`bg-white rounded-lg shadow-md border border-gray-200 p-4 ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium">Invite Collaborators</h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-500 focus:outline-none"
        >
          <X size={20} />
        </button>
      </div>
      
      {showConfirmation ? (
        <div className="text-center py-8">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <UserPlus size={32} className="text-green-500" />
          </div>
          <h4 className="text-xl font-semibold mb-2">Invitation Sent!</h4>
          <p className="text-gray-600">
            Your collaboration request has been sent. You'll be notified when they respond.
          </p>
        </div>
      ) : (
        <>
          {/* Search Input */}
          <div className="mb-4">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={16} className="text-gray-400" />
              </div>
              <input
                ref={searchInputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Search for users by name or username"
              />
            </div>
          </div>
          
          {/* Search Results */}
          {searchQuery.trim().length >= 2 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Search Results</h4>
              
              {isLoading ? (
                <div className="flex justify-center items-center h-20">
                  <Loader2 size={20} className="animate-spin text-blue-500" />
                </div>
              ) : error ? (
                <div className="text-center py-4 text-red-500">
                  <p>Error searching users.</p>
                  <button
                    onClick={() => searchCollaborators(searchQuery)}
                    className="mt-2 text-sm text-blue-500 hover:text-blue-600"
                  >
                    Try Again
                  </button>
                </div>
              ) : filteredResults.length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  <p>No users found matching "{searchQuery}".</p>
                </div>
              ) : (
                <div className="max-h-60 overflow-y-auto">
                  {filteredResults.map((user) => (
                    <div
                      key={user.id}
                      onClick={() => setSelectedUserId(user.id)}
                      className={`flex items-center p-3 rounded-md cursor-pointer ${
                        selectedUserId === user.id
                          ? 'bg-blue-50 border border-blue-200'
                          : 'hover:bg-gray-50 border border-transparent'
                      }`}
                    >
                      <div className="h-10 w-10 rounded-full overflow-hidden bg-gray-200 mr-3">
                        <img
                          src={user.avatar}
                          alt={user.name}
                          className="h-full w-full object-cover"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900">{user.name}</p>
                        <p className="text-xs text-gray-500">@{user.username}</p>
                      </div>
                      <div className="flex-shrink-0">
                        <div
                          className={`w-5 h-5 rounded-full border ${
                            selectedUserId === user.id
                              ? 'border-blue-500 bg-blue-500'
                              : 'border-gray-300'
                          }`}
                        >
                          {selectedUserId === user.id && (
                            <div className="w-2 h-2 mx-auto mt-1.5 bg-white rounded-full" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
          
          {/* Collaboration Details */}
          {selectedUserId && (
            <div className="border-t border-gray-200 pt-4 mt-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Collaboration Details</h4>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Content
                </label>
                <div className="text-sm bg-gray-50 p-3 rounded-md">
                  {contentTitle}
                </div>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Role
                </label>
                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      checked={role === 'editor'}
                      onChange={() => setRole('editor')}
                      className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Editor</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="radio"
                      checked={role === 'viewer'}
                      onChange={() => setRole('viewer')}
                      className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Viewer</span>
                  </label>
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  {role === 'editor'
                    ? 'Editors can make changes to the content.'
                    : 'Viewers can only view and comment on the content.'}
                </p>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                  <MessageSquare size={14} className="mr-1" />
                  Message (Required)
                </label>
                <textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  placeholder="Explain why you want to collaborate with this person..."
                />
              </div>
            </div>
          )}
          
          {/* Actions */}
          <div className="flex justify-end mt-4">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md mr-2 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Cancel
            </button>
            
            <button
              onClick={handleSendRequest}
              disabled={!selectedUserId || !message.trim() || isSending}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
            >
              {isSending ? (
                <>
                  <Loader2 size={16} className="animate-spin mr-2" />
                  Sending...
                </>
              ) : (
                <>
                  <UserPlus size={16} className="mr-2" />
                  Send Invitation
                </>
              )}
            </button>
          </div>
        </>
      )}
    </div>
  );
}
