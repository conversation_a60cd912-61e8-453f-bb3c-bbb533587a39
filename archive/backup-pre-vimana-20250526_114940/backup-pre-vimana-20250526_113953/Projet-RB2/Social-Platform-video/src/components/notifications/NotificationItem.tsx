import { useState } from 'react';
import { Link } from 'react-router-dom';
import { 
  Heart, 
  MessageCircle, 
  UserPlus, 
  Share2, 
  Users, 
  DollarSign, 
  Bell, 
  Award, 
  Video, 
  FileText, 
  Radio, 
  Check, 
  Trash2, 
  MoreVertical, 
  X
} from 'lucide-react';
import { useNotificationsStore } from '../../store/notifications';
import { Notification, NotificationType } from '../../api/notificationsApi';

interface NotificationItemProps {
  notification: Notification;
  onClick?: () => void;
  className?: string;
}

export function NotificationItem({
  notification,
  onClick,
  className = '',
}: NotificationItemProps) {
  const { markAsRead, removeNotification } = useNotificationsStore();
  
  const [showActions, setShowActions] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffSecs < 60) {
      return 'just now';
    } else if (diffMins < 60) {
      return `${diffMins}m ago`;
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else if (diffDays < 7) {
      return `${diffDays}d ago`;
    } else {
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric',
      }).format(date);
    }
  };
  
  // Get notification icon
  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case 'like':
        return <Heart size={16} className="text-red-500" />;
      case 'comment':
        return <MessageCircle size={16} className="text-blue-500" />;
      case 'follow':
        return <UserPlus size={16} className="text-green-500" />;
      case 'mention':
        return <MessageCircle size={16} className="text-purple-500" />;
      case 'share':
        return <Share2 size={16} className="text-blue-500" />;
      case 'collaboration_request':
      case 'collaboration_accepted':
      case 'collaboration_declined':
        return <Users size={16} className="text-indigo-500" />;
      case 'subscription':
        return <Users size={16} className="text-green-500" />;
      case 'tip':
      case 'payment':
      case 'payout':
        return <DollarSign size={16} className="text-green-500" />;
      case 'message':
        return <MessageCircle size={16} className="text-blue-500" />;
      case 'system':
        return <Bell size={16} className="text-gray-500" />;
      case 'content_published':
      case 'content_trending':
        return <Video size={16} className="text-red-500" />;
      case 'achievement':
        return <Award size={16} className="text-yellow-500" />;
      default:
        return <Bell size={16} className="text-gray-500" />;
    }
  };
  
  // Get content type icon
  const getContentTypeIcon = (contentType?: string) => {
    switch (contentType) {
      case 'video':
        return <Video size={14} className="text-blue-500" />;
      case 'post':
        return <FileText size={14} className="text-green-500" />;
      case 'livestream':
        return <Radio size={14} className="text-red-500" />;
      case 'story':
        return <FileText size={14} className="text-purple-500" />;
      default:
        return null;
    }
  };
  
  // Get notification URL
  const getNotificationUrl = () => {
    const { data } = notification;
    
    if (!data) return '/notifications';
    
    switch (notification.type) {
      case 'like':
      case 'comment':
      case 'mention':
      case 'share':
      case 'content_published':
      case 'content_trending':
        if (data.contentId && data.contentType) {
          return `/${data.contentType}s/${data.contentId}`;
        }
        break;
      case 'follow':
        if (data.userId) {
          return `/profile/${data.userId}`;
        }
        break;
      case 'collaboration_request':
      case 'collaboration_accepted':
      case 'collaboration_declined':
        if (data.collaborationId) {
          return `/collaboration/${data.contentType}s/${data.contentId}`;
        }
        break;
      case 'subscription':
      case 'tip':
      case 'payment':
      case 'payout':
        return '/monetization';
      case 'message':
        if (data.messageId) {
          return `/messages/${data.userId}`;
        }
        break;
      case 'achievement':
        if (data.achievementId) {
          return `/achievements/${data.achievementId}`;
        }
        break;
      case 'system':
        if (data.url) {
          return data.url;
        }
        break;
    }
    
    return '/notifications';
  };
  
  // Handle notification click
  const handleClick = () => {
    if (!notification.isRead) {
      markAsRead(notification.id);
    }
    
    if (onClick) {
      onClick();
    }
  };
  
  // Handle delete notification
  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    
    setIsDeleting(true);
    
    try {
      await removeNotification(notification.id);
    } catch (error) {
      console.error('Error deleting notification:', error);
    } finally {
      setIsDeleting(false);
    }
  };
  
  // Handle mark as read
  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    
    if (!notification.isRead) {
      markAsRead(notification.id);
    }
  };
  
  return (
    <Link
      to={getNotificationUrl()}
      className={`block relative ${
        notification.isRead ? 'bg-white' : 'bg-blue-50'
      } hover:bg-gray-50 border-b border-gray-200 last:border-b-0 transition-colors ${className}`}
      onClick={handleClick}
    >
      <div className="p-4 pr-10">
        <div className="flex">
          {/* Avatar or Icon */}
          <div className="flex-shrink-0 mr-3">
            {notification.data?.avatar ? (
              <div className="h-10 w-10 rounded-full overflow-hidden bg-gray-200">
                <img
                  src={notification.data.avatar}
                  alt={notification.data.name || 'User'}
                  className="h-full w-full object-cover"
                />
              </div>
            ) : (
              <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                {getNotificationIcon(notification.type)}
              </div>
            )}
          </div>
          
          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex justify-between items-start">
              <div>
                {notification.data?.name && (
                  <span className="font-medium">{notification.data.name}</span>
                )}
                <p className="text-sm text-gray-700">
                  {notification.message}
                </p>
                
                {/* Content preview */}
                {notification.data?.contentTitle && (
                  <div className="mt-1 text-xs text-gray-500 flex items-center">
                    {getContentTypeIcon(notification.data.contentType)}
                    <span className="ml-1">{notification.data.contentTitle}</span>
                  </div>
                )}
                
                {/* Payment amount */}
                {notification.data?.amount && notification.data?.currency && (
                  <div className="mt-1 text-sm font-medium text-green-600">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: notification.data.currency,
                    }).format(notification.data.amount)}
                  </div>
                )}
              </div>
              
              <span className="text-xs text-gray-500 whitespace-nowrap ml-2">
                {formatDate(notification.createdAt)}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Actions */}
      <div className="absolute top-0 right-0 h-full flex items-center">
        {showActions ? (
          <div className="flex items-center bg-white shadow-md rounded-l-md border border-gray-200 overflow-hidden">
            <button
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                setShowActions(false);
              }}
              className="p-2 text-gray-400 hover:text-gray-600"
              title="Close"
            >
              <X size={16} />
            </button>
            
            {!notification.isRead && (
              <button
                onClick={handleMarkAsRead}
                className="p-2 text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                title="Mark as read"
              >
                <Check size={16} />
              </button>
            )}
            
            <button
              onClick={handleDelete}
              disabled={isDeleting}
              className="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 disabled:opacity-50"
              title="Delete"
            >
              <Trash2 size={16} className={isDeleting ? 'animate-pulse' : ''} />
            </button>
          </div>
        ) : (
          <button
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              setShowActions(true);
            }}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full"
          >
            <MoreVertical size={16} />
          </button>
        )}
      </div>
    </Link>
  );
}
