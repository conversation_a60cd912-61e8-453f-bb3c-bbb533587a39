import { useState, useEffect } from 'react';
import { 
  Bell, 
  Heart, 
  MessageCircle, 
  UserPlus, 
  Share2, 
  Users, 
  DollarSign, 
  Award, 
  Video, 
  Mail, 
  Smartphone, 
  Clock, 
  Save, 
  Loader2, 
  AlertCircle
} from 'lucide-react';
import { useNotificationsStore } from '../../store/notifications';
import { NotificationType } from '../../api/notificationsApi';

interface NotificationSettingsProps {
  className?: string;
}

export function NotificationSettings({ className = '' }: NotificationSettingsProps) {
  const {
    notificationSettings,
    isLoading,
    error,
    fetchNotificationSettings,
    updateSettings,
    updateTypeSettings,
    sendTestPushNotification,
  } = useNotificationsStore();
  
  const [isSaving, setIsSaving] = useState(false);
  const [isSendingTest, setIsSendingTest] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  
  // Fetch notification settings on mount
  useEffect(() => {
    fetchNotificationSettings();
  }, [fetchNotificationSettings]);
  
  // Handle toggle main settings
  const handleToggleMainSetting = (
    setting: 'enabled' | 'pushEnabled' | 'emailEnabled' | 'inAppEnabled' | 'quietHoursEnabled'
  ) => {
    if (!notificationSettings) return;
    
    updateSettings({
      [setting]: !notificationSettings[setting],
    });
  };
  
  // Handle change digest frequency
  const handleChangeDigestFrequency = (
    frequency: 'never' | 'daily' | 'weekly'
  ) => {
    if (!notificationSettings) return;
    
    updateSettings({
      digestFrequency: frequency,
    });
  };
  
  // Handle change quiet hours
  const handleChangeQuietHours = (
    type: 'start' | 'end',
    value: string
  ) => {
    if (!notificationSettings) return;
    
    updateSettings({
      [type === 'start' ? 'quietHoursStart' : 'quietHoursEnd']: value,
    });
  };
  
  // Handle toggle notification type setting
  const handleToggleTypeSetting = (
    type: NotificationType,
    setting: 'enabled' | 'push' | 'email' | 'inApp'
  ) => {
    if (!notificationSettings) return;
    
    const currentSettings = notificationSettings.types[type] || {
      enabled: true,
      push: true,
      email: true,
      inApp: true,
    };
    
    updateTypeSettings(type, {
      [setting]: !currentSettings[setting],
    });
  };
  
  // Handle save all settings
  const handleSaveAllSettings = async () => {
    if (!notificationSettings) return;
    
    setIsSaving(true);
    
    try {
      await updateSettings(notificationSettings);
      setShowSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving notification settings:', error);
    } finally {
      setIsSaving(false);
    }
  };
  
  // Handle send test notification
  const handleSendTestNotification = async () => {
    setIsSendingTest(true);
    
    try {
      await sendTestPushNotification();
    } catch (error) {
      console.error('Error sending test notification:', error);
    } finally {
      setIsSendingTest(false);
    }
  };
  
  // Get notification type icon
  const getNotificationTypeIcon = (type: NotificationType) => {
    switch (type) {
      case 'like':
        return <Heart size={16} className="text-red-500" />;
      case 'comment':
        return <MessageCircle size={16} className="text-blue-500" />;
      case 'follow':
        return <UserPlus size={16} className="text-green-500" />;
      case 'mention':
        return <MessageCircle size={16} className="text-purple-500" />;
      case 'share':
        return <Share2 size={16} className="text-blue-500" />;
      case 'collaboration_request':
      case 'collaboration_accepted':
      case 'collaboration_declined':
        return <Users size={16} className="text-indigo-500" />;
      case 'subscription':
        return <Users size={16} className="text-green-500" />;
      case 'tip':
      case 'payment':
      case 'payout':
        return <DollarSign size={16} className="text-green-500" />;
      case 'message':
        return <MessageCircle size={16} className="text-blue-500" />;
      case 'system':
        return <Bell size={16} className="text-gray-500" />;
      case 'content_published':
      case 'content_trending':
        return <Video size={16} className="text-red-500" />;
      case 'achievement':
        return <Award size={16} className="text-yellow-500" />;
      default:
        return <Bell size={16} className="text-gray-500" />;
    }
  };
  
  // Get notification type display name
  const getNotificationTypeDisplayName = (type: NotificationType) => {
    switch (type) {
      case 'like':
        return 'Likes';
      case 'comment':
        return 'Comments';
      case 'follow':
        return 'Follows';
      case 'mention':
        return 'Mentions';
      case 'share':
        return 'Shares';
      case 'collaboration_request':
        return 'Collaboration Requests';
      case 'collaboration_accepted':
        return 'Collaboration Accepted';
      case 'collaboration_declined':
        return 'Collaboration Declined';
      case 'subscription':
        return 'Subscriptions';
      case 'tip':
        return 'Tips';
      case 'payment':
        return 'Payments';
      case 'payout':
        return 'Payouts';
      case 'message':
        return 'Messages';
      case 'system':
        return 'System Notifications';
      case 'content_published':
        return 'Content Published';
      case 'content_trending':
        return 'Content Trending';
      case 'achievement':
        return 'Achievements';
      default:
        return type.replace('_', ' ');
    }
  };
  
  // Get notification type description
  const getNotificationTypeDescription = (type: NotificationType) => {
    switch (type) {
      case 'like':
        return 'When someone likes your content';
      case 'comment':
        return 'When someone comments on your content';
      case 'follow':
        return 'When someone follows you';
      case 'mention':
        return 'When someone mentions you in a comment or post';
      case 'share':
        return 'When someone shares your content';
      case 'collaboration_request':
        return 'When someone invites you to collaborate';
      case 'collaboration_accepted':
        return 'When someone accepts your collaboration request';
      case 'collaboration_declined':
        return 'When someone declines your collaboration request';
      case 'subscription':
        return 'When someone subscribes to your channel';
      case 'tip':
        return 'When someone sends you a tip';
      case 'payment':
        return 'Payment notifications';
      case 'payout':
        return 'Payout notifications';
      case 'message':
        return 'When you receive a new message';
      case 'system':
        return 'Important system notifications';
      case 'content_published':
        return 'When your content is published';
      case 'content_trending':
        return 'When your content is trending';
      case 'achievement':
        return 'When you earn an achievement';
      default:
        return '';
    }
  };
  
  // All notification types
  const notificationTypes: NotificationType[] = [
    'like',
    'comment',
    'follow',
    'mention',
    'share',
    'collaboration_request',
    'collaboration_accepted',
    'collaboration_declined',
    'subscription',
    'tip',
    'payment',
    'payout',
    'message',
    'content_published',
    'content_trending',
    'achievement',
    'system',
  ];
  
  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Notification Settings</h2>
        
        <div className="flex space-x-2">
          <button
            onClick={handleSendTestNotification}
            disabled={isSendingTest || !notificationSettings?.pushEnabled}
            className="px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
          >
            {isSendingTest ? (
              <>
                <Loader2 size={16} className="animate-spin mr-1" />
                Sending...
              </>
            ) : (
              <>
                <Bell size={16} className="mr-1" />
                Test Notification
              </>
            )}
          </button>
          
          <button
            onClick={handleSaveAllSettings}
            disabled={isSaving || !notificationSettings}
            className="px-3 py-1.5 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
          >
            {isSaving ? (
              <>
                <Loader2 size={16} className="animate-spin mr-1" />
                Saving...
              </>
            ) : (
              <>
                <Save size={16} className="mr-1" />
                Save Settings
              </>
            )}
          </button>
        </div>
      </div>
      
      {showSuccess && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md flex items-start">
          <div className="flex-shrink-0">
            <Check size={20} className="text-green-500" />
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium">Settings saved successfully!</p>
          </div>
        </div>
      )}
      
      {isLoading && !notificationSettings ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 size={32} className="animate-spin text-blue-500" />
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
          <div className="flex-shrink-0">
            <AlertCircle size={20} className="text-red-500" />
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium">Error: {error}</p>
            <button
              onClick={() => fetchNotificationSettings()}
              className="mt-2 text-sm underline"
            >
              Try Again
            </button>
          </div>
        </div>
      ) : notificationSettings ? (
        <div className="space-y-8">
          {/* General Settings */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h3 className="text-lg font-medium mb-4">General Settings</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label htmlFor="notifications-enabled" className="font-medium">
                    Enable Notifications
                  </label>
                  <p className="text-sm text-gray-500">
                    Turn on/off all notifications
                  </p>
                </div>
                <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out">
                  <input
                    type="checkbox"
                    id="notifications-enabled"
                    className="opacity-0 w-0 h-0"
                    checked={notificationSettings.enabled}
                    onChange={() => handleToggleMainSetting('enabled')}
                  />
                  <label
                    htmlFor="notifications-enabled"
                    className={`absolute cursor-pointer top-0 left-0 right-0 bottom-0 rounded-full ${
                      notificationSettings.enabled ? 'bg-blue-500' : 'bg-gray-300'
                    }`}
                  >
                    <span
                      className={`absolute left-1 bottom-1 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out ${
                        notificationSettings.enabled ? 'transform translate-x-6' : ''
                      }`}
                    ></span>
                  </label>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Smartphone size={20} className="text-gray-500 mr-2" />
                  <div>
                    <label htmlFor="push-enabled" className="font-medium">
                      Push Notifications
                    </label>
                    <p className="text-sm text-gray-500">
                      Receive notifications on your device
                    </p>
                  </div>
                </div>
                <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out">
                  <input
                    type="checkbox"
                    id="push-enabled"
                    className="opacity-0 w-0 h-0"
                    checked={notificationSettings.pushEnabled}
                    onChange={() => handleToggleMainSetting('pushEnabled')}
                    disabled={!notificationSettings.enabled}
                  />
                  <label
                    htmlFor="push-enabled"
                    className={`absolute cursor-pointer top-0 left-0 right-0 bottom-0 rounded-full ${
                      notificationSettings.pushEnabled && notificationSettings.enabled
                        ? 'bg-blue-500'
                        : 'bg-gray-300'
                    } ${!notificationSettings.enabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    <span
                      className={`absolute left-1 bottom-1 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out ${
                        notificationSettings.pushEnabled && notificationSettings.enabled
                          ? 'transform translate-x-6'
                          : ''
                      }`}
                    ></span>
                  </label>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Mail size={20} className="text-gray-500 mr-2" />
                  <div>
                    <label htmlFor="email-enabled" className="font-medium">
                      Email Notifications
                    </label>
                    <p className="text-sm text-gray-500">
                      Receive notifications via email
                    </p>
                  </div>
                </div>
                <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out">
                  <input
                    type="checkbox"
                    id="email-enabled"
                    className="opacity-0 w-0 h-0"
                    checked={notificationSettings.emailEnabled}
                    onChange={() => handleToggleMainSetting('emailEnabled')}
                    disabled={!notificationSettings.enabled}
                  />
                  <label
                    htmlFor="email-enabled"
                    className={`absolute cursor-pointer top-0 left-0 right-0 bottom-0 rounded-full ${
                      notificationSettings.emailEnabled && notificationSettings.enabled
                        ? 'bg-blue-500'
                        : 'bg-gray-300'
                    } ${!notificationSettings.enabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    <span
                      className={`absolute left-1 bottom-1 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out ${
                        notificationSettings.emailEnabled && notificationSettings.enabled
                          ? 'transform translate-x-6'
                          : ''
                      }`}
                    ></span>
                  </label>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Bell size={20} className="text-gray-500 mr-2" />
                  <div>
                    <label htmlFor="inapp-enabled" className="font-medium">
                      In-App Notifications
                    </label>
                    <p className="text-sm text-gray-500">
                      Receive notifications within the app
                    </p>
                  </div>
                </div>
                <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out">
                  <input
                    type="checkbox"
                    id="inapp-enabled"
                    className="opacity-0 w-0 h-0"
                    checked={notificationSettings.inAppEnabled}
                    onChange={() => handleToggleMainSetting('inAppEnabled')}
                    disabled={!notificationSettings.enabled}
                  />
                  <label
                    htmlFor="inapp-enabled"
                    className={`absolute cursor-pointer top-0 left-0 right-0 bottom-0 rounded-full ${
                      notificationSettings.inAppEnabled && notificationSettings.enabled
                        ? 'bg-blue-500'
                        : 'bg-gray-300'
                    } ${!notificationSettings.enabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    <span
                      className={`absolute left-1 bottom-1 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out ${
                        notificationSettings.inAppEnabled && notificationSettings.enabled
                          ? 'transform translate-x-6'
                          : ''
                      }`}
                    ></span>
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          {/* Email Digest */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h3 className="text-lg font-medium mb-4">Email Digest</h3>
            
            <div className="space-y-4">
              <div>
                <label className="font-medium">Digest Frequency</label>
                <p className="text-sm text-gray-500 mb-2">
                  How often would you like to receive email digests
                </p>
                
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      checked={notificationSettings.digestFrequency === 'never'}
                      onChange={() => handleChangeDigestFrequency('never')}
                      className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                      disabled={!notificationSettings.enabled || !notificationSettings.emailEnabled}
                    />
                    <span className="ml-2 text-sm text-gray-700">Never</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="radio"
                      checked={notificationSettings.digestFrequency === 'daily'}
                      onChange={() => handleChangeDigestFrequency('daily')}
                      className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                      disabled={!notificationSettings.enabled || !notificationSettings.emailEnabled}
                    />
                    <span className="ml-2 text-sm text-gray-700">Daily</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="radio"
                      checked={notificationSettings.digestFrequency === 'weekly'}
                      onChange={() => handleChangeDigestFrequency('weekly')}
                      className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                      disabled={!notificationSettings.enabled || !notificationSettings.emailEnabled}
                    />
                    <span className="ml-2 text-sm text-gray-700">Weekly</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          {/* Quiet Hours */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Clock size={20} className="text-gray-500 mr-2" />
                <h3 className="text-lg font-medium">Quiet Hours</h3>
              </div>
              
              <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out">
                <input
                  type="checkbox"
                  id="quiet-hours-enabled"
                  className="opacity-0 w-0 h-0"
                  checked={notificationSettings.quietHoursEnabled}
                  onChange={() => handleToggleMainSetting('quietHoursEnabled')}
                  disabled={!notificationSettings.enabled}
                />
                <label
                  htmlFor="quiet-hours-enabled"
                  className={`absolute cursor-pointer top-0 left-0 right-0 bottom-0 rounded-full ${
                    notificationSettings.quietHoursEnabled && notificationSettings.enabled
                      ? 'bg-blue-500'
                      : 'bg-gray-300'
                  } ${!notificationSettings.enabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <span
                    className={`absolute left-1 bottom-1 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out ${
                      notificationSettings.quietHoursEnabled && notificationSettings.enabled
                        ? 'transform translate-x-6'
                        : ''
                    }`}
                  ></span>
                </label>
              </div>
            </div>
            
            <p className="text-sm text-gray-500 mb-4">
              Don't send notifications during these hours
            </p>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Start Time
                </label>
                <input
                  type="time"
                  value={notificationSettings.quietHoursStart}
                  onChange={(e) => handleChangeQuietHours('start', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!notificationSettings.enabled || !notificationSettings.quietHoursEnabled}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  End Time
                </label>
                <input
                  type="time"
                  value={notificationSettings.quietHoursEnd}
                  onChange={(e) => handleChangeQuietHours('end', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!notificationSettings.enabled || !notificationSettings.quietHoursEnabled}
                />
              </div>
            </div>
          </div>
          
          {/* Notification Types */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h3 className="text-lg font-medium mb-4">Notification Types</h3>
            
            <div className="space-y-6">
              {notificationTypes.map((type) => {
                const typeSettings = notificationSettings.types[type] || {
                  enabled: true,
                  push: true,
                  email: true,
                  inApp: true,
                };
                
                return (
                  <div key={type} className="border-b border-gray-200 pb-4 last:border-b-0 last:pb-0">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        {getNotificationTypeIcon(type)}
                        <div className="ml-2">
                          <h4 className="font-medium">{getNotificationTypeDisplayName(type)}</h4>
                          <p className="text-xs text-gray-500">{getNotificationTypeDescription(type)}</p>
                        </div>
                      </div>
                      
                      <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out">
                        <input
                          type="checkbox"
                          id={`${type}-enabled`}
                          className="opacity-0 w-0 h-0"
                          checked={typeSettings.enabled}
                          onChange={() => handleToggleTypeSetting(type, 'enabled')}
                          disabled={!notificationSettings.enabled}
                        />
                        <label
                          htmlFor={`${type}-enabled`}
                          className={`absolute cursor-pointer top-0 left-0 right-0 bottom-0 rounded-full ${
                            typeSettings.enabled && notificationSettings.enabled
                              ? 'bg-blue-500'
                              : 'bg-gray-300'
                          } ${!notificationSettings.enabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          <span
                            className={`absolute left-1 bottom-1 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out ${
                              typeSettings.enabled && notificationSettings.enabled
                                ? 'transform translate-x-6'
                                : ''
                            }`}
                          ></span>
                        </label>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-2 mt-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={typeSettings.push}
                          onChange={() => handleToggleTypeSetting(type, 'push')}
                          className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                          disabled={!notificationSettings.enabled || !typeSettings.enabled || !notificationSettings.pushEnabled}
                        />
                        <span className="ml-2 text-xs text-gray-700">Push</span>
                      </label>
                      
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={typeSettings.email}
                          onChange={() => handleToggleTypeSetting(type, 'email')}
                          className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                          disabled={!notificationSettings.enabled || !typeSettings.enabled || !notificationSettings.emailEnabled}
                        />
                        <span className="ml-2 text-xs text-gray-700">Email</span>
                      </label>
                      
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={typeSettings.inApp}
                          onChange={() => handleToggleTypeSetting(type, 'inApp')}
                          className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                          disabled={!notificationSettings.enabled || !typeSettings.enabled || !notificationSettings.inAppEnabled}
                        />
                        <span className="ml-2 text-xs text-gray-700">In-App</span>
                      </label>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      ) : null}
    </div>
  );
}
