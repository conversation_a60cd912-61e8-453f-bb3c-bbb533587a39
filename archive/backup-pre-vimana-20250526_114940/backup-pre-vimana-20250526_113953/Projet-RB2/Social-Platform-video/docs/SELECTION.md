# Fonctionnalités de Sélection

Ce document décrit comment utiliser les fonctionnalités de sélection implémentées dans le microservice Social-Platform-Video.

## Aperçu

Le système de sélection permet aux utilisateurs de sélectionner un ou plusieurs éléments (vidéos, posts, commentaires, etc.) pour effectuer des actions groupées. L'implémentation est générique et réutilisable pour différents types de contenu.

## Architecture

L'architecture de sélection est basée sur les principes suivants :

1. **Contexte partagé** : Un contexte React gère l'état de sélection pour un groupe d'éléments
2. **Composants réutilisables** : Les composants de sélection sont génériques et utilisables avec n'importe quel type de données
3. **Transparence des types** : L'utilisation des génériques TypeScript garantit la sécurité des types
4. **Extensibilité** : Le système peut être facilement étendu pour prendre en charge de nouveaux types d'éléments et d'actions

## Composants

### SelectionProvider

Fournit un contexte pour gérer l'état de sélection.

```tsx
import { SelectionProvider } from '../components/selection';

// Dans votre composant
return (
  <SelectionProvider<Video> initialMode="none">
    {/* Vos composants ici */}
  </SelectionProvider>
);
```

Options:
- `initialMode`: Mode de sélection initial (`'none'`, `'single'` ou `'multiple'`)

### useSelection

Hook pour accéder au contexte de sélection dans n'importe quel composant enfant.

```tsx
import { useSelection } from '../components/selection';

function MyComponent() {
  const { 
    selectedItems,
    selectionMode,
    toggleSelection,
    clearSelection,
    isSelected
  } = useSelection<Video>();
  
  // Utiliser les fonctions et valeurs du contexte
}
```

### SelectableItem

Composant qui enveloppe un élément et lui ajoute des fonctionnalités de sélection.

```tsx
import { SelectableItem } from '../components/selection';

// Dans votre composant
return (
  <SelectableItem<Video>
    id={video.id}
    item={video}
    className="border rounded p-4"
    selectedClassName="ring-2 ring-blue-500 bg-blue-50"
    onSelectionChange={(isSelected) => console.log('Sélection changée:', isSelected)}
  >
    {/* Contenu de l'élément */}
    <h3>{video.title}</h3>
  </SelectableItem>
);
```

### SelectableGrid

Composant qui affiche une grille d'éléments sélectionnables.

```tsx
import { SelectableGrid } from '../components/selection';

// Dans votre composant
return (
  <SelectableGrid<Video>
    items={videos}
    keyExtractor={(video) => video.id}
    renderItem={(video, isSelected) => (
      <div>
        <h3>{video.title}</h3>
        {isSelected && <span>✓</span>}
      </div>
    )}
    columns={{ sm: 1, md: 2, lg: 3, xl: 4 }}
    gap="1rem"
    className="mt-4"
  />
);
```

### SelectableList

Composant qui affiche une liste d'éléments sélectionnables.

```tsx
import { SelectableList } from '../components/selection';

// Dans votre composant
return (
  <SelectableList<Post>
    items={posts}
    keyExtractor={(post) => post.id}
    renderItem={(post) => (
      <div>
        <h3>{post.caption}</h3>
        <p>{post.user.name}</p>
      </div>
    )}
    className="space-y-4"
  />
);
```

### SelectionToggle

Bouton pour activer/désactiver le mode de sélection.

```tsx
import { SelectionToggle } from '../components/selection';

// Dans votre composant
return (
  <SelectionToggle 
    activeText="Terminé"
    inactiveText="Sélectionner"
    mode="multiple"
    onChange={(isActive, mode) => console.log('Mode:', mode, 'Actif:', isActive)}
  />
);
```

### SelectionToolbar

Barre d'outils qui apparaît lorsque des éléments sont sélectionnés.

```tsx
import { SelectionToolbar } from '../components/selection';
import { Trash2, Share2, FolderPlus } from 'lucide-react';

// Dans votre composant
const actions = [
  {
    icon: <Trash2 size={18} />,
    label: 'Supprimer',
    onClick: (selectedItems) => console.log('Supprimer:', selectedItems),
    textColor: 'text-red-500',
  },
  {
    icon: <Share2 size={18} />,
    label: 'Partager',
    onClick: (selectedItems) => console.log('Partager:', selectedItems),
  },
  {
    icon: <FolderPlus size={18} />,
    label: 'Ajouter à la collection',
    onClick: (selectedItems) => console.log('Ajouter à la collection:', selectedItems),
    show: (count) => count > 0,
  },
];

return (
  <SelectionToolbar<Video>
    actions={actions}
    position="bottom"
    fixed={true}
    className="border-t border-gray-200"
  />
);
```

## Exemples d'utilisation

### Grille de vidéos avec sélection

Le composant `VideoGridWithSelection` montre comment utiliser la sélection avec une grille de vidéos.

```tsx
import { VideoGridWithSelection } from '../components/video/VideoGridWithSelection';

function VideoPage() {
  const videos = [...]; // Vos données de vidéos
  
  return (
    <VideoGridWithSelection
      videos={videos}
      onVideoClick={(video) => console.log('Vidéo cliquée:', video)}
      columns={{ sm: 1, md: 2, lg: 3, xl: 4 }}
    />
  );
}
```

### Liste de blogs avec sélection

Le composant `BlogListWithSelection` montre comment utiliser la sélection avec une liste d'articles de blog.

```tsx
import { BlogListWithSelection } from '../components/content/BlogListWithSelection';

function BlogPage() {
  const posts = [...]; // Vos données d'articles
  
  return (
    <BlogListWithSelection
      posts={posts}
      onPostClick={(post) => console.log('Article cliqué:', post)}
    />
  );
}
```

## Personnalisation

### Actions personnalisées

Vous pouvez définir des actions personnalisées pour la barre d'outils de sélection :

```tsx
const customActions = [
  {
    icon: <Download size={18} />,
    label: 'Télécharger',
    onClick: (selectedItems) => handleDownload(selectedItems),
    show: (count) => count > 0 && count <= 5, // Limite le téléchargement à 5 éléments maximum
  },
  // Autres actions...
];

return (
  <VideoGridWithSelection
    videos={videos}
    selectionActions={customActions}
  />
);
```

### Indicateurs de sélection personnalisés

Vous pouvez personnaliser l'apparence des indicateurs de sélection :

```tsx
<SelectableItem<Video>
  id={video.id}
  item={video}
  renderSelectionIndicator={(isSelected) => (
    isSelected ? (
      <div className="absolute top-2 right-2 z-10 bg-green-500 text-white rounded-full p-1">
        <Check size={16} />
      </div>
    ) : null
  )}
>
  {/* Contenu de l'élément */}
</SelectableItem>
```

## Bonnes pratiques

1. **Utilisez des types précis** : Définissez toujours le type générique pour les composants de sélection
2. **Gérez l'état de sélection** : Assurez-vous que les interactions sont désactivées ou modifiées en mode sélection
3. **Limitez la portée** : Utilisez un `SelectionProvider` distinct pour chaque groupe d'éléments sélectionnables
4. **Accessibilité** : Assurez-vous que les fonctionnalités de sélection sont accessibles au clavier et aux lecteurs d'écran
5. **Performance** : Pour les grandes listes, considérez l'utilisation de techniques de virtualisation avec la sélection 