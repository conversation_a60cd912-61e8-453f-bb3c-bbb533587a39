apiVersion: v1
kind: Secret
metadata:
  name: agent-uiux-secrets
  namespace: retreat-and-be
  labels:
    app: agent-uiux
    component: ai-agent
    tier: application
type: Opaque
data:
  # Base64 encoded secrets
  # To encode: echo -n "your-secret" | base64
  
  # JWT Secret for authentication
  jwt-secret: eW91ci1qd3Qtc2VjcmV0LWhlcmU=  # your-jwt-secret-here
  
  # OpenAI API Key (optional)
  openai-api-key: ""  # Add your base64 encoded OpenAI API key here
  
  # Figma API Token (optional)
  figma-api-token: ""  # Add your base64 encoded Figma API token here
  
  # Google Analytics API Key (optional)
  google-analytics-api-key: ""  # Add your base64 encoded GA API key here
  
  # Hotjar API Key (optional)
  hotjar-api-key: ""  # Add your base64 encoded Hotjar API key here

---
apiVersion: v1
kind: Secret
metadata:
  name: agent-uiux-tls
  namespace: retreat-and-be
  labels:
    app: agent-uiux
    component: ai-agent
    tier: application
type: kubernetes.io/tls
data:
  # TLS certificate and key for HTTPS (if needed)
  tls.crt: ""  # Add your base64 encoded certificate here
  tls.key: ""  # Add your base64 encoded private key here
