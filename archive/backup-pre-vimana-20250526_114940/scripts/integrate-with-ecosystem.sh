#!/bin/bash

# =============================================================================
# SCRIPT D'INTÉGRATION - AGENT SECURITY AVEC L'ÉCOSYSTÈME
# =============================================================================
# Description: Intègre l'Agent Security avec les autres composants
# Version: 1.0.0
# =============================================================================

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ECOSYSTEM_ROOT="$(dirname "$(dirname "$PROJECT_ROOT")")"

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Intégration avec Cortex Central
integrate_cortex_central() {
    log_info "Intégration avec Cortex Central..."
    
    local cortex_config="$ECOSYSTEM_ROOT/cortex-central/config/agents.json"
    
    if [ -f "$cortex_config" ]; then
        # Ajouter l'Agent Security à la configuration du Cortex
        cat > /tmp/security_agent_config.json << EOF
{
  "id": "agent-security",
  "name": "Agent Security",
  "type": "security",
  "version": "1.0.0",
  "status": "active",
  "capabilities": [
    "vulnerability-scanning",
    "compliance-checking",
    "security-monitoring",
    "incident-response",
    "access-control",
    "encryption-management",
    "audit-logging"
  ],
  "endpoints": {
    "health": "http://agent-security:8080/health",
    "metrics": "http://agent-security:9090/metrics",
    "api": "http://agent-security:3000/api"
  },
  "communication": {
    "kafka": {
      "topics": ["security-events", "security-alerts", "compliance-reports"]
    }
  },
  "priority": "high",
  "dependencies": ["weaviate", "kafka", "redis"]
}
EOF
        
        # Fusionner avec la configuration existante
        jq '. + {"agents": (.agents + [input])}' "$cortex_config" /tmp/security_agent_config.json > /tmp/updated_config.json
        mv /tmp/updated_config.json "$cortex_config"
        
        log_success "Agent Security ajouté à Cortex Central"
    else
        log_warning "Configuration Cortex Central non trouvée"
    fi
}

# Intégration avec Agent Backend
integrate_backend() {
    log_info "Intégration avec Agent Backend..."
    
    local backend_dir="$ECOSYSTEM_ROOT/Projet-RB2/Backend-NestJS"
    
    if [ -d "$backend_dir" ]; then
        # Créer les routes de sécurité
        cat > "$backend_dir/src/security/security.controller.ts" << EOF
import { Controller, Get, Post, Body, UseGuards } from '@nestjs/common';
import { SecurityService } from './security.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@Controller('security')
@UseGuards(JwtAuthGuard)
export class SecurityController {
  constructor(private readonly securityService: SecurityService) {}

  @Get('status')
  async getSecurityStatus() {
    return await this.securityService.getSecurityStatus();
  }

  @Get('vulnerabilities')
  async getVulnerabilities() {
    return await this.securityService.getVulnerabilities();
  }

  @Get('compliance')
  async getComplianceStatus() {
    return await this.securityService.getComplianceStatus();
  }

  @Post('scan')
  async triggerScan(@Body() scanRequest: any) {
    return await this.securityService.triggerScan(scanRequest);
  }
}
EOF
        
        log_success "Routes de sécurité ajoutées au Backend"
    else
        log_warning "Répertoire Backend non trouvé"
    fi
}

# Intégration avec Agent Frontend
integrate_frontend() {
    log_info "Intégration avec Agent Frontend..."
    
    local frontend_dir="$ECOSYSTEM_ROOT/Front-Audrey-V1-Main-main"
    
    if [ -d "$frontend_dir" ]; then
        # Créer le composant de sécurité
        mkdir -p "$frontend_dir/src/components/Security"
        
        cat > "$frontend_dir/src/components/Security/SecurityDashboard.tsx" << EOF
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

interface SecurityStatus {
  overall: 'secure' | 'warning' | 'critical';
  vulnerabilities: number;
  complianceScore: number;
  lastScan: string;
}

export const SecurityDashboard: React.FC = () => {
  const [securityStatus, setSecurityStatus] = useState<SecurityStatus | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSecurityStatus();
  }, []);

  const fetchSecurityStatus = async () => {
    try {
      const response = await fetch('/api/security/status');
      const data = await response.json();
      setSecurityStatus(data);
    } catch (error) {
      console.error('Erreur lors du chargement du statut de sécurité:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div>Chargement du statut de sécurité...</div>;
  }

  if (!securityStatus) {
    return <Alert><AlertDescription>Impossible de charger le statut de sécurité</AlertDescription></Alert>;
  }

  const getStatusIcon = () => {
    switch (securityStatus.overall) {
      case 'secure': return <CheckCircle className="text-green-500" />;
      case 'warning': return <AlertTriangle className="text-yellow-500" />;
      case 'critical': return <XCircle className="text-red-500" />;
    }
  };

  const getStatusColor = () => {
    switch (securityStatus.overall) {
      case 'secure': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'critical': return 'bg-red-100 text-red-800';
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Statut de Sécurité
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getStatusIcon()}
              <Badge className={getStatusColor()}>
                {securityStatus.overall.toUpperCase()}
              </Badge>
            </div>
            <div className="text-sm text-gray-500">
              Dernière analyse: {new Date(securityStatus.lastScan).toLocaleString()}
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Vulnérabilités</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {securityStatus.vulnerabilities}
            </div>
            <div className="text-sm text-gray-500">
              Vulnérabilités détectées
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Score de Conformité</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {securityStatus.complianceScore}%
            </div>
            <div className="text-sm text-gray-500">
              Conformité réglementaire
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
EOF
        
        log_success "Composant de sécurité ajouté au Frontend"
    else
        log_warning "Répertoire Frontend non trouvé"
    fi
}

# Configuration des topics Kafka
setup_kafka_topics() {
    log_info "Configuration des topics Kafka..."
    
    local topics=(
        "security-events"
        "security-alerts"
        "compliance-reports"
        "vulnerability-scans"
        "audit-logs"
    )
    
    for topic in "${topics[@]}"; do
        if command -v kafka-topics.sh &> /dev/null; then
            kafka-topics.sh --create \
                --topic "$topic" \
                --bootstrap-server localhost:9092 \
                --partitions 3 \
                --replication-factor 1 \
                --if-not-exists
            log_info "Topic Kafka créé: $topic"
        else
            log_warning "kafka-topics.sh non disponible - création manuelle requise pour: $topic"
        fi
    done
    
    log_success "Topics Kafka configurés"
}

# Mise à jour du docker-compose
update_docker_compose() {
    log_info "Mise à jour du docker-compose..."
    
    local compose_file="$ECOSYSTEM_ROOT/docker-compose.yml"
    
    if [ -f "$compose_file" ]; then
        # Ajouter le service Agent Security
        cat >> "$compose_file" << EOF

  agent-security:
    build: ./agents/security
    container_name: agent-security
    restart: unless-stopped
    ports:
      - "3003:3000"
      - "8083:8080"
      - "9093:9090"
    environment:
      - NODE_ENV=production
      - KAFKA_BROKERS=kafka:9092
      - WEAVIATE_HOST=weaviate
      - REDIS_HOST=redis
    depends_on:
      - kafka
      - weaviate
      - redis
    networks:
      - retreat-and-be-network
    volumes:
      - ./logs/security:/var/log/agent-security
      - ./data/security:/app/data
EOF
        
        log_success "Service Agent Security ajouté au docker-compose"
    else
        log_warning "Fichier docker-compose.yml non trouvé"
    fi
}

# Création des scripts de monitoring
create_monitoring_scripts() {
    log_info "Création des scripts de monitoring..."
    
    mkdir -p "$PROJECT_ROOT/monitoring"
    
    # Script de vérification de santé
    cat > "$PROJECT_ROOT/monitoring/health-check.sh" << EOF
#!/bin/bash

# Vérification de santé de l'Agent Security
HEALTH_URL="http://localhost:8080/health"
METRICS_URL="http://localhost:9090/metrics"

echo "🏥 Vérification de santé de l'Agent Security..."

# Test de santé
if curl -f "\$HEALTH_URL" &> /dev/null; then
    echo "✅ Agent Security opérationnel"
else
    echo "❌ Agent Security non disponible"
    exit 1
fi

# Test des métriques
if curl -f "\$METRICS_URL" &> /dev/null; then
    echo "📊 Métriques disponibles"
else
    echo "⚠️ Métriques non disponibles"
fi

echo "🔒 Vérification terminée"
EOF
    
    chmod +x "$PROJECT_ROOT/monitoring/health-check.sh"
    
    log_success "Scripts de monitoring créés"
}

# Fonction principale
main() {
    echo "🔗 ===== INTÉGRATION DE L'AGENT SECURITY ====="
    echo ""
    
    integrate_cortex_central
    integrate_backend
    integrate_frontend
    setup_kafka_topics
    update_docker_compose
    create_monitoring_scripts
    
    echo ""
    echo "🎉 Intégration de l'Agent Security terminée !"
    echo ""
    echo "📋 Prochaines étapes:"
    echo "  1. Redémarrer les services avec: docker-compose up -d"
    echo "  2. Vérifier la santé: ./monitoring/health-check.sh"
    echo "  3. Consulter les métriques: http://localhost:9093/metrics"
    echo "  4. Accéder au dashboard: http://localhost:3003"
}

# Exécution
main "$@"
