#!/bin/bash

# =============================================================================
# SCRIPT DE DÉPLOIEMENT - AGENT SECURITY
# =============================================================================
# Description: Déploie l'Agent Security dans l'environnement de production
# Version: 1.0.0
# Auteur: Agent Security Team
# =============================================================================

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$PROJECT_ROOT/logs/deploy_security_${TIMESTAMP}.log"

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonctions utilitaires
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Vérification des prérequis
check_prerequisites() {
    log_info "Vérification des prérequis de déploiement..."
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js n'est pas installé"
        exit 1
    fi
    
    # Vérifier npm
    if ! command -v npm &> /dev/null; then
        log_error "npm n'est pas installé"
        exit 1
    fi
    
    # Vérifier Docker
    if ! command -v docker &> /dev/null; then
        log_warning "Docker n'est pas installé - déploiement local uniquement"
    fi
    
    # Vérifier kubectl
    if ! command -v kubectl &> /dev/null; then
        log_warning "kubectl n'est pas installé - pas de déploiement Kubernetes"
    fi
    
    log_success "Prérequis vérifiés"
}

# Construction de l'application
build_application() {
    log_info "Construction de l'Agent Security..."
    
    cd "$PROJECT_ROOT"
    
    # Installation des dépendances
    npm ci --production
    
    # Construction TypeScript
    npx tsc --build
    
    log_success "Application construite avec succès"
}

# Création des fichiers de configuration
create_config_files() {
    log_info "Création des fichiers de configuration..."
    
    # Configuration de production
    cat > "$PROJECT_ROOT/config/production.json" << EOF
{
  "environment": "production",
  "security": {
    "encryption": {
      "algorithm": "aes-256-gcm",
      "keyRotationInterval": "7d"
    },
    "audit": {
      "enabled": true,
      "retentionDays": 365
    },
    "compliance": {
      "frameworks": ["SOC2", "ISO27001", "GDPR"],
      "scanInterval": "24h"
    }
  },
  "monitoring": {
    "enabled": true,
    "metricsPort": 9090,
    "healthCheckPort": 8080
  },
  "communication": {
    "kafka": {
      "brokers": ["kafka:9092"],
      "topics": {
        "security": "security-events",
        "alerts": "security-alerts",
        "compliance": "compliance-reports"
      }
    }
  },
  "memory": {
    "weaviate": {
      "host": "weaviate",
      "port": 8080,
      "scheme": "http"
    }
  }
}
EOF
    
    log_success "Fichiers de configuration créés"
}

# Création du Dockerfile
create_dockerfile() {
    log_info "Création du Dockerfile..."
    
    cat > "$PROJECT_ROOT/Dockerfile" << EOF
FROM node:18-alpine

# Métadonnées
LABEL maintainer="Agent Security Team"
LABEL version="1.0.0"
LABEL description="Agent Security - Sécurité et Conformité"

# Variables d'environnement
ENV NODE_ENV=production
ENV PORT=3000

# Création de l'utilisateur non-root
RUN addgroup -g 1001 -S nodejs
RUN adduser -S security -u 1001

# Répertoire de travail
WORKDIR /app

# Copie des fichiers de dépendances
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copie du code source
COPY --chown=security:nodejs . .

# Construction de l'application
RUN npm run build

# Exposition du port
EXPOSE 3000 8080 9090

# Changement d'utilisateur
USER security

# Commande de démarrage
CMD ["node", "dist/index.js"]
EOF
    
    log_success "Dockerfile créé"
}

# Création des manifestes Kubernetes
create_k8s_manifests() {
    log_info "Création des manifestes Kubernetes..."
    
    mkdir -p "$PROJECT_ROOT/k8s"
    
    # Deployment
    cat > "$PROJECT_ROOT/k8s/deployment.yaml" << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-security
  namespace: retreat-and-be
  labels:
    app: agent-security
    component: security
spec:
  replicas: 2
  selector:
    matchLabels:
      app: agent-security
  template:
    metadata:
      labels:
        app: agent-security
        component: security
    spec:
      containers:
      - name: agent-security
        image: retreat-and-be/agent-security:latest
        ports:
        - containerPort: 3000
          name: http
        - containerPort: 8080
          name: health
        - containerPort: 9090
          name: metrics
        env:
        - name: NODE_ENV
          value: "production"
        - name: KAFKA_BROKERS
          value: "kafka:9092"
        - name: WEAVIATE_HOST
          value: "weaviate"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
EOF
    
    # Service
    cat > "$PROJECT_ROOT/k8s/service.yaml" << EOF
apiVersion: v1
kind: Service
metadata:
  name: agent-security
  namespace: retreat-and-be
  labels:
    app: agent-security
spec:
  selector:
    app: agent-security
  ports:
  - name: http
    port: 3000
    targetPort: 3000
  - name: health
    port: 8080
    targetPort: 8080
  - name: metrics
    port: 9090
    targetPort: 9090
  type: ClusterIP
EOF
    
    log_success "Manifestes Kubernetes créés"
}

# Déploiement Docker
deploy_docker() {
    log_info "Déploiement Docker..."
    
    cd "$PROJECT_ROOT"
    
    # Construction de l'image
    docker build -t retreat-and-be/agent-security:latest .
    
    # Démarrage du conteneur
    docker run -d \
        --name agent-security \
        --restart unless-stopped \
        -p 3000:3000 \
        -p 8080:8080 \
        -p 9090:9090 \
        -e NODE_ENV=production \
        retreat-and-be/agent-security:latest
    
    log_success "Agent Security déployé avec Docker"
}

# Déploiement Kubernetes
deploy_kubernetes() {
    log_info "Déploiement Kubernetes..."
    
    cd "$PROJECT_ROOT"
    
    # Application des manifestes
    kubectl apply -f k8s/
    
    # Attendre que le déploiement soit prêt
    kubectl rollout status deployment/agent-security -n retreat-and-be
    
    log_success "Agent Security déployé sur Kubernetes"
}

# Vérification du déploiement
verify_deployment() {
    log_info "Vérification du déploiement..."
    
    # Test de santé
    local health_url="http://localhost:8080/health"
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f "$health_url" &> /dev/null; then
            log_success "Agent Security est opérationnel"
            return 0
        fi
        
        log_info "Tentative $attempt/$max_attempts - En attente..."
        sleep 10
        ((attempt++))
    done
    
    log_error "Échec de la vérification du déploiement"
    return 1
}

# Fonction principale
main() {
    echo "🔒 ===== DÉPLOIEMENT DE L'AGENT SECURITY ====="
    echo ""
    
    # Créer le répertoire de logs
    mkdir -p "$PROJECT_ROOT/logs"
    
    # Exécution des étapes
    check_prerequisites
    build_application
    create_config_files
    create_dockerfile
    create_k8s_manifests
    
    # Choix du type de déploiement
    if command -v kubectl &> /dev/null && kubectl cluster-info &> /dev/null; then
        deploy_kubernetes
    elif command -v docker &> /dev/null; then
        deploy_docker
    else
        log_warning "Aucun environnement de déploiement disponible"
        log_info "Application construite et prête pour déploiement manuel"
    fi
    
    # Vérification
    verify_deployment
    
    echo ""
    echo "🎉 Déploiement de l'Agent Security terminé avec succès !"
    echo "📊 Métriques disponibles sur: http://localhost:9090/metrics"
    echo "🏥 Santé de l'application: http://localhost:8080/health"
    echo "📋 Logs de déploiement: $LOG_FILE"
}

# Exécution du script
main "$@"
