#!/bin/bash

# Script de déploiement pour l'Agent UI/UX Design Thinking
# Retreat And Be - Living AI Organism Architecture v3.8

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AGENT_NAME="agent-uiux"
AGENT_PORT="3005"
DOCKER_IMAGE="retreat-and-be/agent-uiux:latest"
HEALTH_ENDPOINT="http://localhost:${AGENT_PORT}/health"

# Fonctions utilitaires
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification des prérequis
check_prerequisites() {
    log_info "Vérification des prérequis..."
    
    # Vérifier Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker n'est pas installé"
        exit 1
    fi
    
    # Vérifier Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose n'est pas installé"
        exit 1
    fi
    
    # Vérifier que nous sommes dans le bon répertoire
    if [ ! -f "docker-compose.v3.8.yml" ]; then
        log_error "Fichier docker-compose.v3.8.yml non trouvé. Exécutez ce script depuis la racine du projet."
        exit 1
    fi
    
    # Vérifier que le répertoire de l'agent existe
    if [ ! -d "agents/uiux" ]; then
        log_error "Répertoire agents/uiux non trouvé"
        exit 1
    fi
    
    log_success "Prérequis vérifiés"
}

# Installation des dépendances
install_dependencies() {
    log_info "Installation des dépendances de l'agent UI/UX..."
    
    cd agents/uiux
    
    # Vérifier si package.json existe
    if [ ! -f "package.json" ]; then
        log_error "package.json non trouvé dans agents/uiux"
        exit 1
    fi
    
    # Installer les dépendances
    npm ci --only=production
    
    # Créer le répertoire logs s'il n'existe pas
    mkdir -p logs
    
    cd ../..
    log_success "Dépendances installées"
}

# Construction de l'image Docker
build_image() {
    log_info "Construction de l'image Docker pour l'agent UI/UX..."
    
    # Construire l'image
    docker build -t ${DOCKER_IMAGE} ./agents/uiux/
    
    log_success "Image Docker construite : ${DOCKER_IMAGE}"
}

# Vérification de l'infrastructure
check_infrastructure() {
    log_info "Vérification de l'infrastructure requise..."
    
    # Vérifier si les services requis sont en cours d'exécution
    required_services=("kafka" "weaviate" "redis")
    
    for service in "${required_services[@]}"; do
        if ! docker-compose -f docker-compose.v3.8.yml ps | grep -q "${service}.*Up"; then
            log_warning "Service ${service} n'est pas en cours d'exécution"
            log_info "Démarrage de ${service}..."
            docker-compose -f docker-compose.v3.8.yml up -d ${service}
            
            # Attendre que le service soit prêt
            sleep 10
        fi
    done
    
    log_success "Infrastructure vérifiée"
}

# Déploiement de l'agent
deploy_agent() {
    log_info "Déploiement de l'agent UI/UX..."
    
    # Arrêter l'agent s'il est déjà en cours d'exécution
    if docker-compose -f docker-compose.v3.8.yml ps | grep -q "${AGENT_NAME}.*Up"; then
        log_info "Arrêt de l'agent existant..."
        docker-compose -f docker-compose.v3.8.yml stop ${AGENT_NAME}
        docker-compose -f docker-compose.v3.8.yml rm -f ${AGENT_NAME}
    fi
    
    # Démarrer l'agent
    docker-compose -f docker-compose.v3.8.yml up -d ${AGENT_NAME}
    
    log_success "Agent UI/UX déployé"
}

# Vérification de la santé
health_check() {
    log_info "Vérification de la santé de l'agent..."
    
    # Attendre que l'agent soit prêt
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s ${HEALTH_ENDPOINT} > /dev/null 2>&1; then
            log_success "Agent UI/UX est en bonne santé"
            
            # Afficher les informations de santé
            log_info "Informations de santé :"
            curl -s ${HEALTH_ENDPOINT} | jq '.' || curl -s ${HEALTH_ENDPOINT}
            return 0
        fi
        
        log_info "Tentative ${attempt}/${max_attempts} - En attente de la disponibilité de l'agent..."
        sleep 5
        ((attempt++))
    done
    
    log_error "L'agent n'est pas disponible après ${max_attempts} tentatives"
    return 1
}

# Tests de base
run_basic_tests() {
    log_info "Exécution des tests de base..."
    
    # Test de l'endpoint d'information
    if curl -f -s "http://localhost:${AGENT_PORT}/api/info" > /dev/null; then
        log_success "Endpoint /api/info accessible"
    else
        log_error "Endpoint /api/info non accessible"
        return 1
    fi
    
    # Test de l'endpoint ready
    if curl -f -s "http://localhost:${AGENT_PORT}/ready" > /dev/null; then
        log_success "Endpoint /ready accessible"
    else
        log_error "Endpoint /ready non accessible"
        return 1
    fi
    
    log_success "Tests de base réussis"
}

# Affichage des logs
show_logs() {
    log_info "Affichage des logs de l'agent..."
    docker-compose -f docker-compose.v3.8.yml logs --tail=50 ${AGENT_NAME}
}

# Affichage des informations de déploiement
show_deployment_info() {
    log_success "=== DÉPLOIEMENT TERMINÉ ==="
    echo ""
    log_info "Agent UI/UX Design Thinking déployé avec succès !"
    echo ""
    log_info "📋 Informations de déploiement :"
    echo "  • Nom du service : ${AGENT_NAME}"
    echo "  • Port : ${AGENT_PORT}"
    echo "  • Image Docker : ${DOCKER_IMAGE}"
    echo ""
    log_info "🌐 Endpoints disponibles :"
    echo "  • Health Check : http://localhost:${AGENT_PORT}/health"
    echo "  • Ready Check : http://localhost:${AGENT_PORT}/ready"
    echo "  • Info Agent : http://localhost:${AGENT_PORT}/api/info"
    echo "  • API Design : http://localhost:${AGENT_PORT}/api/design"
    echo "  • API Research : http://localhost:${AGENT_PORT}/api/research"
    echo "  • API Personas : http://localhost:${AGENT_PORT}/api/personas"
    echo "  • API Validation : http://localhost:${AGENT_PORT}/api/validate"
    echo ""
    log_info "🔧 Commandes utiles :"
    echo "  • Voir les logs : docker-compose -f docker-compose.v3.8.yml logs ${AGENT_NAME}"
    echo "  • Redémarrer : docker-compose -f docker-compose.v3.8.yml restart ${AGENT_NAME}"
    echo "  • Arrêter : docker-compose -f docker-compose.v3.8.yml stop ${AGENT_NAME}"
    echo ""
    log_info "📚 Documentation : agents/uiux/README.md"
}

# Fonction principale
main() {
    log_info "=== DÉPLOIEMENT AGENT UI/UX DESIGN THINKING ==="
    echo ""
    
    # Vérifier les arguments
    if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Afficher cette aide"
        echo "  --logs         Afficher les logs après le déploiement"
        echo "  --no-build     Ignorer la construction de l'image Docker"
        echo "  --no-tests     Ignorer les tests de base"
        echo ""
        exit 0
    fi
    
    # Exécuter les étapes de déploiement
    check_prerequisites
    
    if [ "$1" != "--no-build" ]; then
        install_dependencies
        build_image
    fi
    
    check_infrastructure
    deploy_agent
    
    if health_check; then
        if [ "$1" != "--no-tests" ]; then
            run_basic_tests
        fi
        
        show_deployment_info
        
        if [ "$1" = "--logs" ]; then
            echo ""
            show_logs
        fi
    else
        log_error "Échec du déploiement - l'agent n'est pas en bonne santé"
        show_logs
        exit 1
    fi
}

# Gestion des signaux
trap 'log_error "Déploiement interrompu"; exit 1' INT TERM

# Exécution du script principal
main "$@"
