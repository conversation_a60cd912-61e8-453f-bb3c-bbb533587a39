#!/usr/bin/env node

/**
 * 🕉️ Script de Validation Cosmique
 * Teste tous les composants du Framework Trimurti
 */

const fs = require('fs');
const path = require('path');
const http = require('http');

console.log('🕉️ ========================================');
console.log('🔍 VALIDATION FRAMEWORK TRIMURTI');
console.log('🕉️ ========================================');

// Tests de validation
const validationTests = [
  {
    name: 'Composants Trimurti',
    test: validateTrimurtiComponents
  },
  {
    name: 'Interface Next.js',
    test: validateNextJsInterface
  },
  {
    name: 'Configuration Cosmique',
    test: validateCosmicConfiguration
  },
  {
    name: 'Manifeste Cosmique',
    test: validateCosmicManifest
  },
  {
    name: '<PERSON><PERSON><PERSON> Divins',
    test: validateDivineScripts
  }
];

async function runValidation() {
  console.log('\n🧪 Début des tests de validation...\n');
  
  let passedTests = 0;
  let totalTests = validationTests.length;
  
  for (const test of validationTests) {
    try {
      console.log(`🔍 Test: ${test.name}...`);
      const result = await test.test();
      
      if (result.success) {
        console.log(`✅ ${test.name} - SUCCÈS`);
        if (result.details) {
          result.details.forEach(detail => console.log(`   ✓ ${detail}`));
        }
        passedTests++;
      } else {
        console.log(`❌ ${test.name} - ÉCHEC`);
        if (result.errors) {
          result.errors.forEach(error => console.log(`   ✗ ${error}`));
        }
      }
      console.log('');
    } catch (error) {
      console.log(`❌ ${test.name} - ERREUR: ${error.message}\n`);
    }
  }
  
  // Résumé final
  console.log('🕉️ ========================================');
  console.log('📊 RÉSUMÉ DE LA VALIDATION');
  console.log('🕉️ ========================================');
  
  const successRate = (passedTests / totalTests) * 100;
  console.log(`\n📈 Tests réussis: ${passedTests}/${totalTests} (${successRate.toFixed(1)}%)`);
  
  if (successRate === 100) {
    console.log('\n🎉 VALIDATION COMPLÈTE RÉUSSIE !');
    console.log('✨ Framework Trimurti parfaitement opérationnel');
    console.log('🐒 Hanuman est prêt pour la mission cosmique');
    console.log('\n🕉️ AUM HANUMATE NAMAHA - Bénédiction divine confirmée ! 🙏');
  } else if (successRate >= 80) {
    console.log('\n⚠️  VALIDATION PARTIELLEMENT RÉUSSIE');
    console.log('🔧 Quelques ajustements cosmiques nécessaires');
    console.log('🐒 Hanuman nécessite des bénédictions supplémentaires');
  } else {
    console.log('\n❌ VALIDATION ÉCHOUÉE');
    console.log('🚨 Intervention divine requise');
    console.log('🐒 Hanuman a besoin de soins cosmiques urgents');
  }
  
  console.log('\n🕉️ ========================================');
}

function validateTrimurtiComponents() {
  const components = [
    'services/TrimurtiController.ts',
    'hanuman_trimurti_dashboard.tsx',
    'hanuman_central_hub.tsx'
  ];
  
  const details = [];
  const errors = [];
  
  components.forEach(component => {
    const filePath = path.join(process.cwd(), component);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Vérifications spécifiques
      if (component.includes('TrimurtiController')) {
        if (content.includes('class TrimurtiController')) {
          details.push('TrimurtiController classe définie');
        } else {
          errors.push('TrimurtiController classe manquante');
        }
        
        if (content.includes('brahma') && content.includes('vishnu') && content.includes('shiva')) {
          details.push('Trois principes cosmiques présents');
        } else {
          errors.push('Principes cosmiques incomplets');
        }
      }
      
      if (component.includes('trimurti_dashboard')) {
        if (content.includes('HanumanTrimurtiDashboard')) {
          details.push('Dashboard Trimurti composant défini');
        } else {
          errors.push('Dashboard Trimurti composant manquant');
        }
        
        if (content.includes('mandala') || content.includes('Mandala')) {
          details.push('Mandala cosmique intégré');
        } else {
          errors.push('Mandala cosmique manquant');
        }
      }
      
      details.push(`${component} présent et valide`);
    } else {
      errors.push(`${component} manquant`);
    }
  });
  
  return {
    success: errors.length === 0,
    details,
    errors
  };
}

function validateNextJsInterface() {
  const nextjsFiles = [
    'app/page.tsx',
    'app/layout.tsx',
    'app/globals.css',
    'package.json'
  ];
  
  const details = [];
  const errors = [];
  
  nextjsFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      details.push(`${file} présent`);
      
      if (file === 'package.json') {
        const packageJson = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        const requiredDeps = ['react', 'next', 'framer-motion', 'lucide-react'];
        
        requiredDeps.forEach(dep => {
          if (packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]) {
            details.push(`Dépendance ${dep} installée`);
          } else {
            errors.push(`Dépendance ${dep} manquante`);
          }
        });
      }
      
      if (file === 'app/page.tsx') {
        const content = fs.readFileSync(filePath, 'utf8');
        if (content.includes('HanumanCentralHub')) {
          details.push('Hub Central intégré dans page principale');
        } else {
          errors.push('Hub Central non intégré');
        }
      }
    } else {
      errors.push(`${file} manquant`);
    }
  });
  
  return {
    success: errors.length === 0,
    details,
    errors
  };
}

function validateCosmicConfiguration() {
  const details = [];
  const errors = [];
  
  // Vérifier la configuration Tailwind
  const tailwindConfig = path.join(process.cwd(), 'tailwind.config.js');
  if (fs.existsSync(tailwindConfig)) {
    details.push('Configuration Tailwind présente');
  } else {
    errors.push('Configuration Tailwind manquante');
  }
  
  // Vérifier les styles cosmiques
  const globalsCss = path.join(process.cwd(), 'app/globals.css');
  if (fs.existsSync(globalsCss)) {
    const content = fs.readFileSync(globalsCss, 'utf8');
    
    if (content.includes('divine') || content.includes('cosmic')) {
      details.push('Styles cosmiques intégrés');
    } else {
      errors.push('Styles cosmiques manquants');
    }
    
    if (content.includes('432') || content.includes('1.618')) {
      details.push('Fréquences sacrées configurées');
    } else {
      errors.push('Fréquences sacrées manquantes');
    }
  }
  
  // Vérifier la configuration Next.js
  const nextConfig = path.join(process.cwd(), 'next.config.ts');
  if (fs.existsSync(nextConfig)) {
    details.push('Configuration Next.js présente');
  } else {
    errors.push('Configuration Next.js manquante');
  }
  
  return {
    success: errors.length === 0,
    details,
    errors
  };
}

function validateCosmicManifest() {
  const manifestPath = path.join(process.cwd(), 'cosmic-manifest.json');
  const details = [];
  const errors = [];
  
  if (fs.existsSync(manifestPath)) {
    try {
      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
      
      // Vérifications du manifeste
      const requiredFields = [
        'name', 'version', 'framework', 'mission',
        'consciousness', 'agents', 'cycles', 'mantras'
      ];
      
      requiredFields.forEach(field => {
        if (manifest[field]) {
          details.push(`Champ ${field} présent`);
        } else {
          errors.push(`Champ ${field} manquant`);
        }
      });
      
      // Vérifier les agents par affinité
      if (manifest.agents) {
        ['brahma', 'vishnu', 'shiva'].forEach(principle => {
          if (manifest.agents[principle] && Array.isArray(manifest.agents[principle])) {
            details.push(`Agents ${principle} configurés (${manifest.agents[principle].length})`);
          } else {
            errors.push(`Agents ${principle} non configurés`);
          }
        });
      }
      
      // Vérifier les mantras
      if (manifest.mantras) {
        ['brahma', 'vishnu', 'shiva', 'hanuman'].forEach(principle => {
          if (manifest.mantras[principle]) {
            details.push(`Mantra ${principle} défini`);
          } else {
            errors.push(`Mantra ${principle} manquant`);
          }
        });
      }
      
      details.push('Manifeste cosmique valide');
    } catch (error) {
      errors.push(`Erreur parsing manifeste: ${error.message}`);
    }
  } else {
    errors.push('Manifeste cosmique manquant');
  }
  
  return {
    success: errors.length === 0,
    details,
    errors
  };
}

function validateDivineScripts() {
  const scripts = [
    'scripts/divine-blessing.js',
    'scripts/cosmic-validation.js'
  ];
  
  const details = [];
  const errors = [];
  
  scripts.forEach(script => {
    const scriptPath = path.join(process.cwd(), script);
    if (fs.existsSync(scriptPath)) {
      details.push(`${script} présent`);
      
      // Vérifier les permissions d'exécution
      try {
        const stats = fs.statSync(scriptPath);
        if (stats.mode & parseInt('111', 8)) {
          details.push(`${script} exécutable`);
        } else {
          errors.push(`${script} non exécutable`);
        }
      } catch (error) {
        errors.push(`Erreur vérification permissions ${script}`);
      }
    } else {
      errors.push(`${script} manquant`);
    }
  });
  
  return {
    success: errors.length === 0,
    details,
    errors
  };
}

// Fonction pour tester la connexion au serveur Next.js
function checkNextJsServer() {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:3000', (res) => {
      resolve({
        success: true,
        status: res.statusCode,
        message: 'Serveur Next.js accessible'
      });
    });
    
    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message,
        message: 'Serveur Next.js non accessible'
      });
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        success: false,
        error: 'Timeout',
        message: 'Serveur Next.js timeout'
      });
    });
  });
}

// Exécution principale
if (require.main === module) {
  runValidation().catch(console.error);
}

module.exports = {
  runValidation,
  validateTrimurtiComponents,
  validateNextJsInterface,
  validateCosmicConfiguration,
  validateCosmicManifest,
  validateDivineScripts
};
