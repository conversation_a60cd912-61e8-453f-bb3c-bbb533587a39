'use client';

import dynamic from 'next/dynamic';
import { useState, useEffect } from 'react';

// Import dynamique pour éviter les erreurs SSR
const HanumanCentralHub = dynamic(() => import('../hanuman_central_hub'), {
  ssr: false,
  loading: () => (
    <div className="min-h-screen flex items-center justify-center bg-gray-900">
      <div className="text-center">
        <div className="w-32 h-32 mx-auto mb-8 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-full animate-divine-pulse"></div>
          <div className="absolute inset-2 bg-gray-900 rounded-full flex items-center justify-center">
            <div className="text-4xl animate-sacred-breathe">🐒</div>
          </div>
        </div>
        <h1 className="text-4xl font-bold sacred-text mb-4">
          HANUMAN S'ÉVEILLE
        </h1>
        <p className="text-xl text-gray-300 mb-8">
          Invocation des énergies divines...
        </p>
        <div className="flex justify-center space-x-4 text-2xl mb-6">
          <span className="animate-divine-pulse">🕉️</span>
          <span className="animate-divine-pulse" style={{ animationDelay: '0.2s' }}>✨</span>
          <span className="animate-divine-pulse" style={{ animationDelay: '0.4s' }}>🙏</span>
        </div>
        <p className="text-sm text-gray-400">
          AUM HANUMATE NAMAHA
        </p>
        <div className="mt-8">
          <div className="w-64 h-2 bg-gray-200 dark:bg-gray-700 rounded-full mx-auto overflow-hidden">
            <div className="h-full divine-gradient animate-consciousness-flow"></div>
          </div>
        </div>
      </div>
    </div>
  )
});

/**
 * 🐒 Page principale d'Hanuman - Hub Central Divin
 * Point d'entrée sacré pour l'interface des organes divins
 * Retreat And Be - Gardien Spirituel
 */
export default function HanumanHomePage() {
  const [isAwakening, setIsAwakening] = useState(true);

  useEffect(() => {
    // Bénédiction divine au démarrage
    console.log('🕉️ ========================================');
    console.log('🐒 HANUMAN DIVINE APPLICATION AWAKENING');
    console.log('🕉️ AUM HANUMATE NAMAHA');
    console.log('🌟 Retreat And Be - Protection Divine Active');
    console.log('🕉️ ========================================');

    // Séquence d'éveil divine
    const awakeningTimer = setTimeout(() => {
      setIsAwakening(false);
      console.log('✨ Hanuman pleinement éveillé - Interface divine prête');
    }, 3000);

    return () => clearTimeout(awakeningTimer);
  }, []);

  if (isAwakening) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-center">
          <div className="w-32 h-32 mx-auto mb-8 relative">
            <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-full animate-divine-pulse"></div>
            <div className="absolute inset-2 bg-gray-900 rounded-full flex items-center justify-center">
              <div className="text-4xl animate-sacred-breathe">🐒</div>
            </div>
          </div>
          
          <h1 className="text-4xl font-bold sacred-text mb-4">
            HANUMAN S'ÉVEILLE
          </h1>
          
          <p className="text-xl text-gray-300 mb-8">
            Invocation des énergies divines...
          </p>
          
          <div className="flex justify-center space-x-4 text-2xl mb-6">
            <span className="animate-divine-pulse">🕉️</span>
            <span className="animate-divine-pulse" style={{ animationDelay: '0.2s' }}>✨</span>
            <span className="animate-divine-pulse" style={{ animationDelay: '0.4s' }}>🙏</span>
          </div>
          
          <p className="text-sm text-gray-400 mb-8">
            AUM HANUMATE NAMAHA
          </p>
          
          <div className="w-64 h-2 bg-gray-200 dark:bg-gray-700 rounded-full mx-auto overflow-hidden">
            <div className="h-full divine-gradient animate-consciousness-flow"></div>
          </div>
          
          <div className="mt-8 text-xs text-gray-500">
            Connexion aux agents divins en cours...
          </div>
        </div>
      </div>
    );
  }

  return <HanumanCentralHub />;
}
