#!/usr/bin/env node

/**
 * 🔍 SCRIPT DE VALIDATION SPRINT 1 - SANDBOX HANUMAN
 * ==================================================
 * 
 * Ce script valide que tous les composants du Sprint 1 sont correctement implémentés
 * et fonctionnent selon les spécifications.
 */

const fs = require('fs');
const path = require('path');

class Sprint1Validator {
  constructor() {
    this.results = [];
    this.sandboxPath = './hanuman_sandbox';
  }

  /**
   * Lance toutes les validations
   */
  async validateAll() {
    console.log('🔍 VALIDATION SPRINT 1 - SANDBOX HANUMAN');
    console.log('=========================================\n');

    // Validation de la structure des fichiers
    this.validateFileStructure();
    
    // Validation des composants TypeScript
    this.validateTypeScriptComponents();
    
    // Validation de la configuration
    this.validateConfiguration();
    
    // Validation de la documentation
    this.validateDocumentation();
    
    // Validation des tests
    this.validateTests();
    
    // Validation de l'intégration
    this.validateIntegration();

    // Affichage des résultats
    this.displayResults();
  }

  /**
   * Valide la structure des fichiers
   */
  validateFileStructure() {
    console.log('📁 Validation de la structure des fichiers...');

    const requiredFiles = [
      'hanuman_sandbox/index.ts',
      'hanuman_sandbox/README.md',
      'hanuman_sandbox/package.json',
      'hanuman_sandbox/tsconfig.json',
      'hanuman_sandbox/start_sandbox.ts',
      'hanuman_sandbox/infrastructure/sandbox_infrastructure.ts',
      'hanuman_sandbox/environments/environment_manager.tsx',
      'hanuman_sandbox/security/sandbox_security.ts',
      'hanuman_sandbox/interfaces/sandbox_management_interface.tsx',
      'hanuman_sandbox/tests/infrastructure_tests.ts',
      'ROADMAP_HANUMAN_SANDBOX.md',
      'SPRINT_1_COMPLETION_REPORT.md'
    ];

    const requiredDirectories = [
      'hanuman_sandbox',
      'hanuman_sandbox/infrastructure',
      'hanuman_sandbox/environments',
      'hanuman_sandbox/security',
      'hanuman_sandbox/interfaces',
      'hanuman_sandbox/tests'
    ];

    // Vérifier les répertoires
    for (const dir of requiredDirectories) {
      if (fs.existsSync(dir) && fs.statSync(dir).isDirectory()) {
        this.addResult('File Structure', 'PASS', `Répertoire trouvé: ${dir}`);
      } else {
        this.addResult('File Structure', 'FAIL', `Répertoire manquant: ${dir}`);
      }
    }

    // Vérifier les fichiers
    for (const file of requiredFiles) {
      if (fs.existsSync(file) && fs.statSync(file).isFile()) {
        this.addResult('File Structure', 'PASS', `Fichier trouvé: ${file}`);
      } else {
        this.addResult('File Structure', 'FAIL', `Fichier manquant: ${file}`);
      }
    }
  }

  /**
   * Valide les composants TypeScript
   */
  validateTypeScriptComponents() {
    console.log('🔧 Validation des composants TypeScript...');

    const components = [
      {
        file: 'hanuman_sandbox/infrastructure/sandbox_infrastructure.ts',
        expectedClasses: ['SandboxInfrastructure'],
        expectedInterfaces: ['SandboxContainer', 'SandboxNamespace', 'SandboxNetwork']
      },
      {
        file: 'hanuman_sandbox/security/sandbox_security.ts',
        expectedClasses: ['SandboxSecurity'],
        expectedInterfaces: ['SecurityPolicy', 'SecurityIncident', 'SecurityMetrics']
      },
      {
        file: 'hanuman_sandbox/environments/environment_manager.tsx',
        expectedClasses: ['EnvironmentManager'],
        expectedInterfaces: ['Environment', 'EnvironmentConfig']
      },
      {
        file: 'hanuman_sandbox/interfaces/sandbox_management_interface.tsx',
        expectedClasses: ['SandboxManagementInterface'],
        expectedInterfaces: ['DashboardStats', 'SystemAlert']
      },
      {
        file: 'hanuman_sandbox/tests/infrastructure_tests.ts',
        expectedClasses: ['InfrastructureTests'],
        expectedInterfaces: []
      }
    ];

    for (const component of components) {
      if (fs.existsSync(component.file)) {
        const content = fs.readFileSync(component.file, 'utf8');
        
        // Vérifier les classes
        for (const className of component.expectedClasses) {
          if (content.includes(`class ${className}`) || content.includes(`export class ${className}`)) {
            this.addResult('TypeScript Components', 'PASS', `Classe trouvée: ${className} dans ${component.file}`);
          } else {
            this.addResult('TypeScript Components', 'FAIL', `Classe manquante: ${className} dans ${component.file}`);
          }
        }

        // Vérifier les interfaces
        for (const interfaceName of component.expectedInterfaces) {
          if (content.includes(`interface ${interfaceName}`) || content.includes(`export interface ${interfaceName}`)) {
            this.addResult('TypeScript Components', 'PASS', `Interface trouvée: ${interfaceName} dans ${component.file}`);
          } else {
            this.addResult('TypeScript Components', 'FAIL', `Interface manquante: ${interfaceName} dans ${component.file}`);
          }
        }

        // Vérifier la syntaxe de base
        if (content.includes('import') && content.includes('export')) {
          this.addResult('TypeScript Components', 'PASS', `Syntaxe ES6 correcte dans ${component.file}`);
        } else {
          this.addResult('TypeScript Components', 'WARNING', `Syntaxe ES6 potentiellement incorrecte dans ${component.file}`);
        }
      }
    }
  }

  /**
   * Valide la configuration
   */
  validateConfiguration() {
    console.log('⚙️ Validation de la configuration...');

    // Vérifier package.json
    if (fs.existsSync('hanuman_sandbox/package.json')) {
      try {
        const packageJson = JSON.parse(fs.readFileSync('hanuman_sandbox/package.json', 'utf8'));
        
        if (packageJson.name === 'hanuman-sandbox') {
          this.addResult('Configuration', 'PASS', 'Nom du package correct');
        } else {
          this.addResult('Configuration', 'FAIL', `Nom du package incorrect: ${packageJson.name}`);
        }

        if (packageJson.scripts && packageJson.scripts.dev && packageJson.scripts.test) {
          this.addResult('Configuration', 'PASS', 'Scripts npm configurés');
        } else {
          this.addResult('Configuration', 'FAIL', 'Scripts npm manquants');
        }

        if (packageJson.dependencies && packageJson.dependencies.react) {
          this.addResult('Configuration', 'PASS', 'Dépendances React configurées');
        } else {
          this.addResult('Configuration', 'WARNING', 'Dépendances React manquantes');
        }
      } catch (error) {
        this.addResult('Configuration', 'FAIL', `Erreur de parsing package.json: ${error}`);
      }
    }

    // Vérifier tsconfig.json
    if (fs.existsSync('hanuman_sandbox/tsconfig.json')) {
      try {
        const tsConfig = JSON.parse(fs.readFileSync('hanuman_sandbox/tsconfig.json', 'utf8'));
        
        if (tsConfig.compilerOptions && tsConfig.compilerOptions.target) {
          this.addResult('Configuration', 'PASS', 'Configuration TypeScript valide');
        } else {
          this.addResult('Configuration', 'FAIL', 'Configuration TypeScript invalide');
        }

        if (tsConfig.compilerOptions.jsx === 'react-jsx') {
          this.addResult('Configuration', 'PASS', 'Support JSX configuré');
        } else {
          this.addResult('Configuration', 'WARNING', 'Support JSX non configuré');
        }
      } catch (error) {
        this.addResult('Configuration', 'FAIL', `Erreur de parsing tsconfig.json: ${error}`);
      }
    }
  }

  /**
   * Valide la documentation
   */
  validateDocumentation() {
    console.log('📚 Validation de la documentation...');

    // Vérifier README.md
    if (fs.existsSync('hanuman_sandbox/README.md')) {
      const readme = fs.readFileSync('hanuman_sandbox/README.md', 'utf8');
      
      const requiredSections = [
        '## 🎯 Vue d\'ensemble',
        '## ✨ Fonctionnalités Principales',
        '## 🚀 Installation et Configuration',
        '## 📖 Guide d\'utilisation',
        '## 🏛️ Architecture'
      ];

      for (const section of requiredSections) {
        if (readme.includes(section)) {
          this.addResult('Documentation', 'PASS', `Section trouvée: ${section}`);
        } else {
          this.addResult('Documentation', 'WARNING', `Section manquante: ${section}`);
        }
      }

      if (readme.length > 5000) {
        this.addResult('Documentation', 'PASS', 'README complet et détaillé');
      } else {
        this.addResult('Documentation', 'WARNING', 'README potentiellement incomplet');
      }
    }

    // Vérifier ROADMAP_HANUMAN_SANDBOX.md
    if (fs.existsSync('ROADMAP_HANUMAN_SANDBOX.md')) {
      const roadmap = fs.readFileSync('ROADMAP_HANUMAN_SANDBOX.md', 'utf8');
      
      if (roadmap.includes('✅ TERMINÉ')) {
        this.addResult('Documentation', 'PASS', 'Sprint 1 marqué comme terminé dans le roadmap');
      } else {
        this.addResult('Documentation', 'WARNING', 'Sprint 1 non marqué comme terminé');
      }

      if (roadmap.includes('ACCOMPLISSEMENTS SPRINT 1')) {
        this.addResult('Documentation', 'PASS', 'Accomplissements documentés');
      } else {
        this.addResult('Documentation', 'WARNING', 'Accomplissements non documentés');
      }
    }

    // Vérifier SPRINT_1_COMPLETION_REPORT.md
    if (fs.existsSync('SPRINT_1_COMPLETION_REPORT.md')) {
      const report = fs.readFileSync('SPRINT_1_COMPLETION_REPORT.md', 'utf8');
      
      if (report.includes('## ✅ Objectifs Atteints') && report.includes('## 📁 Livrables Créés')) {
        this.addResult('Documentation', 'PASS', 'Rapport de completion complet');
      } else {
        this.addResult('Documentation', 'WARNING', 'Rapport de completion incomplet');
      }
    }
  }

  /**
   * Valide les tests
   */
  validateTests() {
    console.log('🧪 Validation des tests...');

    if (fs.existsSync('hanuman_sandbox/tests/infrastructure_tests.ts')) {
      const testFile = fs.readFileSync('hanuman_sandbox/tests/infrastructure_tests.ts', 'utf8');
      
      const expectedTests = [
        'testInfrastructureInitialization',
        'testContainerCreation',
        'testContainerIsolation',
        'testSecurityPolicies',
        'testResourceManagement',
        'testNetworkIsolation',
        'testStorageEncryption',
        'testMonitoringSystem',
        'testIncidentDetection',
        'testContainerCleanup'
      ];

      for (const test of expectedTests) {
        if (testFile.includes(test)) {
          this.addResult('Tests', 'PASS', `Test trouvé: ${test}`);
        } else {
          this.addResult('Tests', 'FAIL', `Test manquant: ${test}`);
        }
      }

      if (testFile.includes('runAllTests') && testFile.includes('export')) {
        this.addResult('Tests', 'PASS', 'Framework de tests exportable');
      } else {
        this.addResult('Tests', 'WARNING', 'Framework de tests non exportable');
      }
    }
  }

  /**
   * Valide l'intégration
   */
  validateIntegration() {
    console.log('🔗 Validation de l\'intégration...');

    // Vérifier l'index principal
    if (fs.existsSync('hanuman_sandbox/index.ts')) {
      const indexFile = fs.readFileSync('hanuman_sandbox/index.ts', 'utf8');
      
      if (indexFile.includes('HanumanSandbox') && indexFile.includes('export')) {
        this.addResult('Integration', 'PASS', 'Classe principale exportée');
      } else {
        this.addResult('Integration', 'FAIL', 'Classe principale non exportée');
      }

      if (indexFile.includes('HanumanOrganOrchestrator')) {
        this.addResult('Integration', 'PASS', 'Intégration avec l\'orchestrateur');
      } else {
        this.addResult('Integration', 'FAIL', 'Intégration avec l\'orchestrateur manquante');
      }

      const expectedExports = [
        'SandboxInfrastructure',
        'SandboxSecurity',
        'EnvironmentManager',
        'SandboxManagementInterface'
      ];

      for (const exportName of expectedExports) {
        if (indexFile.includes(`export { ${exportName} }`) || indexFile.includes(`export * from`)) {
          this.addResult('Integration', 'PASS', `Export trouvé: ${exportName}`);
        } else {
          this.addResult('Integration', 'WARNING', `Export potentiellement manquant: ${exportName}`);
        }
      }
    }

    // Vérifier le script de démarrage
    if (fs.existsSync('hanuman_sandbox/start_sandbox.ts')) {
      const startScript = fs.readFileSync('hanuman_sandbox/start_sandbox.ts', 'utf8');
      
      if (startScript.includes('demonstrateSandbox') && startScript.includes('main')) {
        this.addResult('Integration', 'PASS', 'Script de démarrage fonctionnel');
      } else {
        this.addResult('Integration', 'WARNING', 'Script de démarrage potentiellement non fonctionnel');
      }
    }
  }

  /**
   * Ajoute un résultat de validation
   */
  addResult(component, status, message, details) {
    this.results.push({ component, status, message, details });
  }

  /**
   * Affiche les résultats de validation
   */
  displayResults() {
    console.log('\n📊 RÉSULTATS DE VALIDATION');
    console.log('===========================\n');

    const groupedResults = this.groupResultsByComponent();
    
    for (const [component, results] of Object.entries(groupedResults)) {
      console.log(`🔍 ${component}:`);
      
      for (const result of results) {
        const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
        console.log(`   ${icon} ${result.message}`);
        
        if (result.details) {
          for (const detail of result.details) {
            console.log(`      - ${detail}`);
          }
        }
      }
      console.log('');
    }

    // Statistiques globales
    const totalResults = this.results.length;
    const passCount = this.results.filter(r => r.status === 'PASS').length;
    const failCount = this.results.filter(r => r.status === 'FAIL').length;
    const warningCount = this.results.filter(r => r.status === 'WARNING').length;

    console.log('📈 STATISTIQUES GLOBALES');
    console.log('=========================');
    console.log(`✅ Tests réussis: ${passCount}`);
    console.log(`❌ Tests échoués: ${failCount}`);
    console.log(`⚠️ Avertissements: ${warningCount}`);
    console.log(`📊 Total: ${totalResults}`);
    console.log(`🎯 Taux de réussite: ${((passCount / totalResults) * 100).toFixed(1)}%`);

    // Verdict final
    console.log('\n🏆 VERDICT FINAL');
    console.log('================');
    
    if (failCount === 0) {
      if (warningCount === 0) {
        console.log('🎉 EXCELLENT ! Sprint 1 parfaitement implémenté !');
      } else {
        console.log('✅ TRÈS BIEN ! Sprint 1 implémenté avec quelques améliorations possibles.');
      }
    } else if (failCount <= 3) {
      console.log('⚠️ BIEN ! Sprint 1 largement implémenté, quelques corrections nécessaires.');
    } else {
      console.log('❌ ATTENTION ! Sprint 1 partiellement implémenté, corrections importantes nécessaires.');
    }

    console.log('\n🏗️✨ "Validation Sprint 1 terminée !" ✨🏗️');
  }

  /**
   * Groupe les résultats par composant
   */
  groupResultsByComponent() {
    const grouped = {};
    
    for (const result of this.results) {
      if (!grouped[result.component]) {
        grouped[result.component] = [];
      }
      grouped[result.component].push(result);
    }
    
    return grouped;
  }
}

/**
 * Point d'entrée principal
 */
async function main() {
  const validator = new Sprint1Validator();
  await validator.validateAll();
}

// Lancer la validation
main().catch(error => {
  console.error('💥 Erreur lors de la validation:', error);
  process.exit(1);
});
