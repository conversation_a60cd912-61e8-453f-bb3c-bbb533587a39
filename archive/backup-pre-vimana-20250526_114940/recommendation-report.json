{"timestamp": "2023-05-23T10:56:23.456Z", "sessionId": "1684842983456abc123", "period": {"startDate": "2023-04-23T10:56:23.456Z", "endDate": "2023-05-23T10:56:23.456Z", "days": 30}, "metrics": {"period": {"startDate": "2023-04-23T10:56:23.456Z", "endDate": "2023-05-23T10:56:23.456Z"}, "recommendation": {"accuracy": {"value": 87.5, "trend": "increasing", "change": 2.3}, "clickThroughRate": {"value": 12.8, "trend": "stable", "change": 0.2}, "conversionRate": {"value": 3.2, "trend": "increasing", "change": 0.5}, "userSatisfaction": {"value": 4.2, "trend": "increasing", "change": 0.3}}, "performance": {"averageResponseTime": {"value": 120, "trend": "decreasing", "change": -15}, "p95ResponseTime": {"value": 350, "trend": "decreasing", "change": -30}, "errorRate": {"value": 0.5, "trend": "decreasing", "change": -0.2}}, "usage": {"totalRequests": 125000, "uniqueUsers": 15000, "recommendationsPerUser": 8.3, "topCategories": [{"name": "Yoga", "percentage": 35}, {"name": "Méditation", "percentage": 25}, {"name": "Bien-être", "percentage": 20}, {"name": "Nutrition", "percentage": 15}, {"name": "Fitness", "percentage": 5}]}, "comparison": {"period": {"startDate": "2023-03-24T10:56:23.456Z", "endDate": "2023-04-22T10:56:23.456Z"}, "recommendation": {"accuracy": {"value": 85.2, "trend": "increasing", "change": 1.8}, "clickThroughRate": {"value": 12.6, "trend": "stable", "change": 0.1}, "conversionRate": {"value": 2.7, "trend": "stable", "change": 0.0}, "userSatisfaction": {"value": 3.9, "trend": "stable", "change": 0.1}}, "performance": {"averageResponseTime": {"value": 135, "trend": "decreasing", "change": -10}, "p95ResponseTime": {"value": 380, "trend": "decreasing", "change": -20}, "errorRate": {"value": 0.7, "trend": "decreasing", "change": -0.1}}, "usage": {"totalRequests": 115000, "uniqueUsers": 14000, "recommendationsPerUser": 8.2}}, "benchmarks": {"industryAverage": {"accuracy": 82.0, "clickThroughRate": 10.5, "conversionRate": 2.5, "responseTime": 150}, "topPerformers": {"accuracy": 92.0, "clickThroughRate": 15.0, "conversionRate": 4.0, "responseTime": 100}}}, "analysis": {"performanceStatus": "Good", "recommendations": ["Améliorer la précision des recommandations en affinant les algorithmes de filtrage collaboratif", "Optimiser les temps de réponse en mettant en cache les recommandations fréquentes", "Ré<PERSON><PERSON> le taux d'erreur en améliorant la gestion des exceptions et la validation des entrées", "Augmenter le taux de clic en améliorant la présentation visuelle des recommandations", "Améliorer le taux de conversion en personnalisant davantage les recommandations selon le profil utilisateur"], "statistics": {"tests": {"totalTests": 1250, "successRate": 99.5, "averageResponseTime": 120, "minResponseTime": 50, "maxResponseTime": 450, "p95ResponseTime": 350}}}, "meta": {"generatedBy": "Recommendation Performance Report Tool", "version": "1.0.0"}}