# Agent Performance - Système d'Optimisation Intelligent 🚀

## Vue d'ensemble

L'Agent Performance est un composant autonome du système d'agents distribués qui se spécialise dans l'optimisation et le monitoring des performances. Il fait partie de l'écosystème "Organisme Cognitif Distribué" et communique avec les autres agents via Kafka pour fournir des analyses et recommandations de performance en temps réel.

## 🎯 Fonctionnalités Principales

### 🔍 Benchmarking Automatisé
- **Tests Lighthouse** : Audits de performance web complets
- **Tests de charge** : Utilisation d'Autocannon et K6
- **Profiling** : Analyse CPU/mémoire avec Clinic.js
- **Core Web Vitals** : Mesure des métriques essentielles

### 💡 Optimisation Intelligente
- **Analyse de code** : Détection de goulots d'étranglement
- **Recommandations IA** : Suggestions basées sur des patterns
- **Optimisation architecture** : Conseils d'amélioration structurelle
- **Patterns d'optimisation** : Base de connaissances évolutive

### 📊 Monitoring Temps Réel
- **Métriques Prometheus** : Collecte et exposition de métriques
- **Alertes intelligentes** : Détection d'anomalies automatique
- **Dashboards** : Visualisation avec Grafana
- **Surveillance continue** : Monitoring 24/7

### 🧠 Mémoire Vectorielle
- **Stockage Weaviate** : Patterns et historique des performances
- **Recherche sémantique** : Recommandations contextuelles
- **Apprentissage continu** : Amélioration des suggestions

## 🏗️ Architecture

```
Agent Performance
├── Core/
│   └── PerformanceAgent.ts      # Orchestrateur principal
├── Engines/
│   ├── BenchmarkEngine.ts       # Moteur de benchmarking
│   ├── OptimizationAdvisor.ts   # Conseiller en optimisation
│   └── MonitoringIntegrator.ts  # Intégration monitoring
├── Communication/
│   └── KafkaCommunication.ts    # Communication synaptique
├── Memory/
│   └── WeaviateMemory.ts        # Mémoire vectorielle
└── API/
    ├── server.ts                # Serveur REST
    └── validators.ts            # Validation des requêtes
```

## 🚀 Installation et Démarrage

### Prérequis
- Node.js 18+
- Docker et Docker Compose
- npm ou yarn

### Installation Rapide

```bash
# Cloner le projet
git clone <repository-url>
cd agents/performance

# Installer les dépendances
npm install

# Copier la configuration
cp .env.example .env

# Démarrer l'infrastructure
docker-compose up -d

# Build et démarrage
npm run build
npm start
```

### Démarrage du Système Complet

```bash
# Démarrer tous les agents et l'infrastructure
./scripts/start-full-system.sh

# Avec tests d'intégration
./scripts/start-full-system.sh --test

# Arrêter le système
./scripts/stop-full-system.sh
```

## 📡 API REST

### Endpoints Principaux

#### Health Check
```http
GET /health
```

#### Lancer un Benchmark
```http
POST /benchmark
Content-Type: application/json

{
  "type": "lighthouse",
  "target": {
    "url": "https://example.com"
  },
  "configuration": {
    "lighthouse": {
      "categories": ["performance", "accessibility"],
      "formFactor": "desktop"
    }
  }
}
```

#### Demander des Optimisations
```http
POST /optimize
Content-Type: application/json

{
  "target": {
    "service": "web-app"
  },
  "scope": ["frontend", "backend"],
  "constraints": {
    "budget": 1000,
    "timeline": "urgent",
    "technologies": ["react", "nodejs"]
  },
  "goals": {
    "performance": 80,
    "scalability": 70
  }
}
```

#### Configurer le Monitoring
```http
POST /monitor
Content-Type: application/json

{
  "targets": ["https://app.example.com"],
  "metrics": ["response_time", "cpu_usage", "memory_usage"],
  "thresholds": {
    "response_time": 1000,
    "cpu_usage": 80,
    "memory_usage": 85
  },
  "interval": 30000
}
```

#### Métriques Prometheus
```http
GET /metrics
```

#### Statut de l'Agent
```http
GET /status
```

## 🔧 Configuration

### Variables d'Environnement

```bash
# Serveur
PORT=3007
NODE_ENV=production
LOG_LEVEL=info

# Kafka
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=performance-agent
KAFKA_GROUP_ID=performance-agent-group

# Weaviate
WEAVIATE_HOST=localhost:8080
WEAVIATE_SCHEME=http

# Monitoring
MONITORING_INTERVAL=30000
THRESHOLD_RESPONSE_TIME=1000
THRESHOLD_CPU_USAGE=80
THRESHOLD_MEMORY_USAGE=85

# Benchmarking
LIGHTHOUSE_ENABLED=true
LOAD_TESTING_ENABLED=true
PROFILING_ENABLED=true
```

## 🧪 Tests

### Tests d'Intégration
```bash
# Tests de communication inter-agents
node scripts/test-agent-integration.js

# Tests de workflow end-to-end
node scripts/test-end-to-end-workflow.js

# Tests unitaires
npm test

# Tests avec couverture
npm run test:coverage
```

### Scénarios de Test

1. **Workflow d'Optimisation Complète**
   - Analyse UI/UX → Benchmark → Audit sécurité → Tests QA → Recommandations

2. **Monitoring Continu**
   - Configuration → Collecte métriques → Alertes → Rapports

3. **Test de Résilience**
   - Charge système → Gestion d'erreurs → Récupération

## 📊 Monitoring et Observabilité

### Métriques Clés
- `system_cpu_usage_percent` : Utilisation CPU
- `system_memory_usage_bytes` : Utilisation mémoire
- `http_request_duration_ms` : Temps de réponse HTTP
- `http_requests_total` : Nombre total de requêtes
- `http_errors_total` : Nombre total d'erreurs

### Dashboards Grafana
- Performance système
- Métriques d'application
- Alertes et incidents
- Tendances d'optimisation

### Logs Structurés
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "info",
  "service": "PerformanceAgent",
  "message": "Benchmark terminé",
  "benchmarkId": "benchmark-123",
  "duration": 45000,
  "recommendations": 5
}
```

## 🔗 Communication Inter-Agents

### Topics Kafka
- `performance.requests` : Demandes de performance
- `performance.results` : Résultats d'analyse
- `performance.alerts` : Alertes de performance
- `performance.recommendations` : Recommandations

### Messages Types
```javascript
// Demande de benchmark
{
  type: 'benchmark_request',
  payload: { /* BenchmarkRequest */ },
  agentId: 'cortex-central',
  replyTo: 'performance.results'
}

// Résultat de benchmark
{
  type: 'benchmark_result',
  payload: { /* BenchmarkResult */ },
  agentId: 'performance-agent'
}
```

## 🛠️ Développement

### Structure du Code
```
src/
├── core/           # Logique métier principale
├── engines/        # Moteurs spécialisés
├── communication/ # Communication Kafka
├── memory/        # Intégration Weaviate
├── api/           # API REST
├── types/         # Types TypeScript
└── utils/         # Utilitaires
```

### Ajout de Nouveaux Patterns d'Optimisation
```typescript
const newPattern: OptimizationPattern = {
  id: 'lazy-loading-images',
  name: 'Lazy Loading des Images',
  description: 'Charger les images à la demande',
  category: 'performance',
  applicability: {
    technologies: ['react', 'vue', 'angular'],
    architectures: ['spa', 'pwa'],
    contexts: ['large-images', 'long-pages']
  },
  implementation: {
    before: '<img src="image.jpg" alt="Description">',
    after: '<img loading="lazy" src="image.jpg" alt="Description">',
    steps: [
      'Identifier les images non critiques',
      'Ajouter l\'attribut loading="lazy"',
      'Tester la performance'
    ]
  },
  impact: {
    performance: 70,
    maintainability: 90,
    scalability: 60
  }
};
```

## 🔒 Sécurité

- **Validation des entrées** : Joi schemas
- **Rate limiting** : Protection contre les abus
- **Headers sécurisés** : Helmet.js
- **Utilisateur non-root** : Container sécurisé
- **Secrets management** : Variables d'environnement

## 📚 Documentation API

Documentation complète disponible à :
```
GET /api-docs
```

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/amazing-feature`)
3. Commit les changements (`git commit -m 'Add amazing feature'`)
4. Push vers la branche (`git push origin feature/amazing-feature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🆘 Support

- **Issues** : GitHub Issues
- **Documentation** : `/api-docs` endpoint
- **Logs** : Répertoire `./logs/`
- **Monitoring** : Grafana dashboard

---

**Agent Performance** - Partie du système d'agents distribués Retreat And Be
Optimisation intelligente • Monitoring temps réel • Communication synaptique
