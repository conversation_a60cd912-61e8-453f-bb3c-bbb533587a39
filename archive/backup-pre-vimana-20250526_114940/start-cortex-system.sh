#!/bin/bash

# 🧠 Script de Démarrage du Système Nerveux Distribué Cortex Central
# Retreat And Be - Living AI Organism Architecture v3.8

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.cortex.yml"
PROJECT_NAME="cortex-system"
HEALTH_CHECK_TIMEOUT=300 # 5 minutes

# Fonction d'affichage avec style
print_header() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                    🧠 CORTEX CENTRAL SYSTEM STARTUP 🧠                      ║"
    echo "║                     Living AI Organism Architecture v3.8                    ║"
    echo "║                              Retreat And Be                                  ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_step() {
    echo -e "${CYAN}[$(date '+%H:%M:%S')] $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Vérification des prérequis
check_prerequisites() {
    print_step "Vérification des prérequis..."

    # Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker n'est pas installé"
        exit 1
    fi

    # Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose n'est pas installé"
        exit 1
    fi

    # Vérification de l'espace disque (minimum 10GB)
    available_space=$(df . | tail -1 | awk '{print $4}')
    if [ "$available_space" -lt 10485760 ]; then # 10GB en KB
        print_warning "Espace disque faible (< 10GB disponible)"
    fi

    # Vérification de la mémoire (minimum 8GB)
    total_memory=$(free -m | awk 'NR==2{print $2}')
    if [ "$total_memory" -lt 8192 ]; then # 8GB en MB
        print_warning "Mémoire système faible (< 8GB disponible)"
    fi

    print_success "Prérequis vérifiés"
}

# Création des répertoires nécessaires
create_directories() {
    print_step "Création des répertoires de configuration..."

    mkdir -p monitoring/prometheus
    mkdir -p monitoring/grafana/dashboards
    mkdir -p monitoring/grafana/datasources
    mkdir -p logs
    mkdir -p data

    # Configuration Prometheus
    cat > monitoring/prometheus/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'cortex-central'
    static_configs:
      - targets: ['cortex-central:8080']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'agent-frontend'
    static_configs:
      - targets: ['agent-frontend:3002']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'agent-backend'
    static_configs:
      - targets: ['agent-backend:3003']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'agent-uiux'
    static_configs:
      - targets: ['agent-uiux:3004']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'agent-qa'
    static_configs:
      - targets: ['agent-qa:3008']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'agent-devops'
    static_configs:
      - targets: ['agent-devops:3009']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka:9101']
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
EOF

    # Configuration Grafana datasource
    cat > monitoring/grafana/datasources/prometheus.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
EOF

    print_success "Répertoires créés"
}

# Nettoyage des conteneurs existants
cleanup_containers() {
    print_step "Nettoyage des conteneurs existants..."

    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME down --remove-orphans 2>/dev/null || true

    # Nettoyage des volumes orphelins
    docker volume prune -f 2>/dev/null || true

    print_success "Nettoyage terminé"
}

# Construction des images
build_images() {
    print_step "Construction des images Docker..."

    # Build en parallèle pour accélérer
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME build --parallel

    print_success "Images construites"
}

# Démarrage des services d'infrastructure
start_infrastructure() {
    print_step "Démarrage des services d'infrastructure..."

    # Démarrage des services de base
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d \
        zookeeper \
        kafka \
        redis \
        weaviate \
        prometheus \
        grafana

    print_step "Attente de la disponibilité des services d'infrastructure..."

    # Attente Kafka
    wait_for_service "kafka" "9092" "Kafka"

    # Attente Redis
    wait_for_service "redis" "6379" "Redis"

    # Attente Weaviate
    wait_for_service "weaviate" "8080" "Weaviate"

    print_success "Services d'infrastructure démarrés"
}

# Démarrage du Cortex Central
start_cortex_central() {
    print_step "Démarrage du Cortex Central..."

    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d cortex-central

    # Attente de la disponibilité du Cortex Central
    wait_for_service "cortex-central" "8080" "Cortex Central"

    print_success "Cortex Central démarré"
}

# Démarrage des agents
start_agents() {
    print_step "Démarrage des agents spécialisés..."

    # Démarrage séquentiel des agents pour éviter la surcharge
    agents=("agent-frontend" "agent-backend" "agent-uiux" "agent-qa" "agent-devops" "agent-security")

    for agent in "${agents[@]}"; do
        print_step "Démarrage de $agent..."
        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d $agent
        sleep 10 # Délai entre les démarrages
    done

    print_success "Agents démarrés"
}

# Démarrage du dashboard
start_dashboard() {
    print_step "Démarrage du dashboard..."

    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d cortex-dashboard

    print_success "Dashboard démarré"
}

# Fonction d'attente pour un service
wait_for_service() {
    local service=$1
    local port=$2
    local name=$3
    local timeout=$HEALTH_CHECK_TIMEOUT
    local counter=0

    print_step "Attente de $name..."

    while [ $counter -lt $timeout ]; do
        if docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME exec -T $service nc -z localhost $port 2>/dev/null; then
            print_success "$name est disponible"
            return 0
        fi

        sleep 2
        counter=$((counter + 2))

        if [ $((counter % 30)) -eq 0 ]; then
            print_step "Attente de $name... (${counter}s/${timeout}s)"
        fi
    done

    print_error "Timeout: $name n'est pas devenu disponible"
    return 1
}

# Vérification de la santé du système
check_system_health() {
    print_step "Vérification de la santé du système..."

    # Vérification du Cortex Central
    if curl -f http://localhost:8080/health >/dev/null 2>&1; then
        print_success "Cortex Central : Healthy"
    else
        print_error "Cortex Central : Unhealthy"
    fi

    # Vérification des agents
    agents=(
        "agent-frontend:3002"
        "agent-backend:3003"
        "agent-uiux:3004"
        "agent-qa:3008"
        "agent-devops:3009"
        "agent-security:3007"
    )

    for agent_port in "${agents[@]}"; do
        agent=$(echo $agent_port | cut -d: -f1)
        port=$(echo $agent_port | cut -d: -f2)

        if curl -f http://localhost:$port/health >/dev/null 2>&1; then
            print_success "$agent : Healthy"
        else
            print_warning "$agent : Unhealthy (peut être en cours de démarrage)"
        fi
    done

    # Vérification des services de monitoring
    if curl -f http://localhost:9090/-/healthy >/dev/null 2>&1; then
        print_success "Prometheus : Healthy"
    else
        print_warning "Prometheus : Unhealthy"
    fi

    if curl -f http://localhost:3001/api/health >/dev/null 2>&1; then
        print_success "Grafana : Healthy"
    else
        print_warning "Grafana : Unhealthy"
    fi
}

# Affichage des informations de connexion
show_connection_info() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                           🎉 SYSTÈME DÉMARRÉ 🎉                             ║"
    echo "╠══════════════════════════════════════════════════════════════════════════════╣"
    echo "║                                                                              ║"
    echo "║  🧠 Cortex Central:        http://localhost:8080                            ║"
    echo "║  📊 Dashboard:             http://localhost:3000                            ║"
    echo "║  📈 Prometheus:            http://localhost:9090                            ║"
    echo "║  📊 Grafana:               http://localhost:3001 (admin/cortex-admin)      ║"
    echo "║                                                                              ║"
    echo "║  🎨 Agent Frontend:        http://localhost:3002                            ║"
    echo "║  🔧 Agent Backend:         http://localhost:3003                            ║"
    echo "║  🎨 Agent UI/UX:           http://localhost:3004                            ║"
    echo "║  🧪 Agent QA:              http://localhost:3008                            ║"
    echo "║  🚀 Agent DevOps:          http://localhost:3009                            ║"
    echo "║  🔒 Agent Security:        http://localhost:3007                            ║"
    echo "║                                                                              ║"
    echo "║  📡 API Documentation:     http://localhost:8080/api/docs                   ║"
    echo "║  🔍 Health Check:          http://localhost:8080/health                     ║"
    echo "║  📊 System Status:         http://localhost:8080/cortex/dashboard           ║"
    echo "║                                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Fonction principale
main() {
    print_header

    # Vérifications préliminaires
    check_prerequisites

    # Préparation
    create_directories
    cleanup_containers

    # Construction
    build_images

    # Démarrage séquentiel
    start_infrastructure
    start_cortex_central
    start_agents
    start_dashboard

    # Vérifications finales
    sleep 30 # Attente pour que tous les services se stabilisent
    check_system_health

    # Informations de connexion
    show_connection_info

    print_success "Système Cortex Central démarré avec succès!"
    print_step "Utilisez 'docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME logs -f' pour voir les logs"
    print_step "Utilisez 'docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME down' pour arrêter le système"
}

# Gestion des signaux
trap 'print_error "Interruption détectée. Arrêt en cours..."; docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME down; exit 1' INT TERM

# Exécution
main "$@"
