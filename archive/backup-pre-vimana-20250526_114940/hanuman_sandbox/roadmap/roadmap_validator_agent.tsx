import React, { useState, useEffect, useCallback } from 'react';
import { EventEmitter } from 'events';
import { RoadmapProject, RoadmapSprint, RoadmapTask, RoadmapDeliverable } from './roadmap_generator';

/**
 * Agent Vérificateur de Roadmap
 * Valide obligatoirement les roadmaps avant déploiement
 */

// Types pour la validation
export interface RoadmapValidation {
  id: string;
  roadmapId: string;
  status: 'pending' | 'in_progress' | 'approved' | 'rejected' | 'blocked';
  validationType: 'pre_development' | 'sprint_completion' | 'pre_deployment' | 'post_deployment';
  validationRules: ValidationRule[];
  results: ValidationResult[];
  blockers: ValidationBlocker[];
  recommendations: string[];
  validatedBy?: string;
  validatedAt?: Date;
  approvalRequired: boolean;
  stakeholderApprovals: StakeholderApproval[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ValidationRule {
  id: string;
  name: string;
  description: string;
  category: 'completeness' | 'quality' | 'compliance' | 'security' | 'performance' | 'documentation';
  severity: 'info' | 'warning' | 'error' | 'critical';
  required: boolean;
  automated: boolean;
  validator: (roadmap: RoadmapProject) => ValidationResult;
}

export interface ValidationResult {
  ruleId: string;
  status: 'passed' | 'failed' | 'warning' | 'skipped';
  message: string;
  details: string;
  evidence?: string[];
  suggestions: string[];
  impact: 'low' | 'medium' | 'high' | 'critical';
  blocksDeployment: boolean;
}

export interface ValidationBlocker {
  id: string;
  type: 'missing_deliverable' | 'incomplete_sprint' | 'failed_test' | 'security_issue' | 'approval_pending';
  severity: 'warning' | 'error' | 'critical';
  description: string;
  resolution: string;
  owner: string;
  dueDate?: Date;
  resolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
}

export interface StakeholderApproval {
  stakeholder: string;
  role: string;
  required: boolean;
  status: 'pending' | 'approved' | 'rejected';
  approvedAt?: Date;
  comments?: string;
  conditions?: string[];
}

export interface ValidationConfig {
  enableAutomaticValidation: boolean;
  requireStakeholderApproval: boolean;
  blockDeploymentOnErrors: boolean;
  allowWarningOverride: boolean;
  validationTimeout: number;
  requiredApprovers: string[];
  criticalValidationRules: string[];
}

export class RoadmapValidatorAgent extends EventEmitter {
  private validations: Map<string, RoadmapValidation> = new Map();
  private validationRules: Map<string, ValidationRule> = new Map();
  private config: ValidationConfig;
  private activeValidations: Set<string> = new Set();

  constructor(config: ValidationConfig) {
    super();
    this.config = config;
    this.initializeValidationRules();
  }

  /**
   * Initialise les règles de validation
   */
  private initializeValidationRules(): void {
    const rules: ValidationRule[] = [
      {
        id: 'roadmap_completeness',
        name: 'Complétude de la Roadmap',
        description: 'Vérifier que tous les éléments obligatoires de la roadmap sont présents',
        category: 'completeness',
        severity: 'critical',
        required: true,
        automated: true,
        validator: (roadmap: RoadmapProject) => {
          const missing: string[] = [];
          
          if (!roadmap.name || roadmap.name.trim() === '') missing.push('Nom du projet');
          if (!roadmap.description || roadmap.description.trim() === '') missing.push('Description');
          if (roadmap.sprints.length === 0) missing.push('Sprints');
          if (roadmap.acceptanceCriteria.length === 0) missing.push('Critères d\'acceptation');
          if (roadmap.technicalRequirements.length === 0) missing.push('Exigences techniques');
          
          return {
            ruleId: 'roadmap_completeness',
            status: missing.length === 0 ? 'passed' : 'failed',
            message: missing.length === 0 ? 'Roadmap complète' : `Éléments manquants: ${missing.join(', ')}`,
            details: missing.length === 0 ? 'Tous les éléments obligatoires sont présents' : `${missing.length} éléments manquants`,
            suggestions: missing.map(item => `Ajouter: ${item}`),
            impact: 'critical',
            blocksDeployment: missing.length > 0
          };
        }
      },
      {
        id: 'sprint_structure',
        name: 'Structure des Sprints',
        description: 'Vérifier que chaque sprint a une structure valide',
        category: 'quality',
        severity: 'error',
        required: true,
        automated: true,
        validator: (roadmap: RoadmapProject) => {
          const issues: string[] = [];
          
          roadmap.sprints.forEach((sprint, index) => {
            if (!sprint.name) issues.push(`Sprint ${index + 1}: Nom manquant`);
            if (!sprint.description) issues.push(`Sprint ${index + 1}: Description manquante`);
            if (sprint.objectives.length === 0) issues.push(`Sprint ${index + 1}: Objectifs manquants`);
            if (sprint.tasks.length === 0) issues.push(`Sprint ${index + 1}: Tâches manquantes`);
            if (sprint.deliverables.length === 0) issues.push(`Sprint ${index + 1}: Livrables manquants`);
            if (sprint.duration <= 0) issues.push(`Sprint ${index + 1}: Durée invalide`);
          });
          
          return {
            ruleId: 'sprint_structure',
            status: issues.length === 0 ? 'passed' : 'failed',
            message: issues.length === 0 ? 'Structure des sprints valide' : `${issues.length} problèmes détectés`,
            details: issues.join('; '),
            suggestions: issues.map(issue => `Corriger: ${issue}`),
            impact: 'high',
            blocksDeployment: issues.length > 0
          };
        }
      },
      {
        id: 'task_completeness',
        name: 'Complétude des Tâches',
        description: 'Vérifier que toutes les tâches sont complètement définies',
        category: 'completeness',
        severity: 'error',
        required: true,
        automated: true,
        validator: (roadmap: RoadmapProject) => {
          const issues: string[] = [];
          
          roadmap.sprints.forEach((sprint, sprintIndex) => {
            sprint.tasks.forEach((task, taskIndex) => {
              if (!task.name) issues.push(`Sprint ${sprintIndex + 1}, Tâche ${taskIndex + 1}: Nom manquant`);
              if (!task.description) issues.push(`Sprint ${sprintIndex + 1}, Tâche ${taskIndex + 1}: Description manquante`);
              if (task.estimatedHours <= 0) issues.push(`Sprint ${sprintIndex + 1}, Tâche ${taskIndex + 1}: Estimation manquante`);
              if (task.acceptanceCriteria.length === 0) issues.push(`Sprint ${sprintIndex + 1}, Tâche ${taskIndex + 1}: Critères d'acceptation manquants`);
            });
          });
          
          return {
            ruleId: 'task_completeness',
            status: issues.length === 0 ? 'passed' : 'failed',
            message: issues.length === 0 ? 'Tâches complètement définies' : `${issues.length} tâches incomplètes`,
            details: issues.join('; '),
            suggestions: issues.map(issue => `Compléter: ${issue}`),
            impact: 'high',
            blocksDeployment: issues.length > 0
          };
        }
      },
      {
        id: 'deliverable_validation',
        name: 'Validation des Livrables',
        description: 'Vérifier que tous les livrables ont des critères de validation',
        category: 'quality',
        severity: 'warning',
        required: true,
        automated: true,
        validator: (roadmap: RoadmapProject) => {
          const issues: string[] = [];
          
          roadmap.sprints.forEach((sprint, sprintIndex) => {
            sprint.deliverables.forEach((deliverable, deliverableIndex) => {
              if (!deliverable.name) issues.push(`Sprint ${sprintIndex + 1}, Livrable ${deliverableIndex + 1}: Nom manquant`);
              if (!deliverable.path) issues.push(`Sprint ${sprintIndex + 1}, Livrable ${deliverableIndex + 1}: Chemin manquant`);
              if (deliverable.validationCriteria.length === 0) issues.push(`Sprint ${sprintIndex + 1}, Livrable ${deliverableIndex + 1}: Critères de validation manquants`);
            });
          });
          
          return {
            ruleId: 'deliverable_validation',
            status: issues.length === 0 ? 'passed' : 'warning',
            message: issues.length === 0 ? 'Livrables correctement définis' : `${issues.length} livrables à améliorer`,
            details: issues.join('; '),
            suggestions: issues.map(issue => `Améliorer: ${issue}`),
            impact: 'medium',
            blocksDeployment: false
          };
        }
      },
      {
        id: 'risk_assessment',
        name: 'Évaluation des Risques',
        description: 'Vérifier que les risques sont identifiés et ont des plans de mitigation',
        category: 'compliance',
        severity: 'warning',
        required: true,
        automated: true,
        validator: (roadmap: RoadmapProject) => {
          const issues: string[] = [];
          
          if (roadmap.risks.length === 0) {
            issues.push('Aucun risque identifié');
          } else {
            roadmap.risks.forEach((risk, index) => {
              if (!risk.mitigation) issues.push(`Risque ${index + 1}: Plan de mitigation manquant`);
              if (!risk.contingency) issues.push(`Risque ${index + 1}: Plan de contingence manquant`);
              if (!risk.owner) issues.push(`Risque ${index + 1}: Responsable manquant`);
            });
          }
          
          return {
            ruleId: 'risk_assessment',
            status: issues.length === 0 ? 'passed' : 'warning',
            message: issues.length === 0 ? 'Risques correctement évalués' : `${issues.length} problèmes dans l'évaluation des risques`,
            details: issues.join('; '),
            suggestions: issues.map(issue => `Corriger: ${issue}`),
            impact: 'medium',
            blocksDeployment: false
          };
        }
      },
      {
        id: 'security_requirements',
        name: 'Exigences de Sécurité',
        description: 'Vérifier que les aspects sécurité sont pris en compte',
        category: 'security',
        severity: 'error',
        required: true,
        automated: true,
        validator: (roadmap: RoadmapProject) => {
          const securityKeywords = ['sécurité', 'security', 'authentification', 'autorisation', 'chiffrement', 'audit'];
          const hasSecurityRequirements = roadmap.technicalRequirements.some(req => 
            securityKeywords.some(keyword => req.toLowerCase().includes(keyword))
          );
          
          const hasSecurityTasks = roadmap.sprints.some(sprint =>
            sprint.tasks.some(task => 
              securityKeywords.some(keyword => 
                task.name.toLowerCase().includes(keyword) || 
                task.description.toLowerCase().includes(keyword)
              )
            )
          );
          
          const issues: string[] = [];
          if (!hasSecurityRequirements) issues.push('Aucune exigence de sécurité identifiée');
          if (!hasSecurityTasks) issues.push('Aucune tâche de sécurité planifiée');
          
          return {
            ruleId: 'security_requirements',
            status: issues.length === 0 ? 'passed' : 'warning',
            message: issues.length === 0 ? 'Aspects sécurité pris en compte' : 'Aspects sécurité à renforcer',
            details: issues.join('; '),
            suggestions: [
              'Ajouter des exigences de sécurité spécifiques',
              'Planifier des tâches d\'audit de sécurité',
              'Inclure des tests de sécurité'
            ],
            impact: 'high',
            blocksDeployment: roadmap.type === 'security' && issues.length > 0
          };
        }
      },
      {
        id: 'documentation_requirements',
        name: 'Exigences de Documentation',
        description: 'Vérifier que la documentation est planifiée',
        category: 'documentation',
        severity: 'warning',
        required: false,
        automated: true,
        validator: (roadmap: RoadmapProject) => {
          const hasDocumentationTasks = roadmap.sprints.some(sprint =>
            sprint.tasks.some(task => task.type === 'documentation')
          );
          
          const hasDocumentationDeliverables = roadmap.sprints.some(sprint =>
            sprint.deliverables.some(deliverable => deliverable.type === 'documentation')
          );
          
          const issues: string[] = [];
          if (!hasDocumentationTasks) issues.push('Aucune tâche de documentation planifiée');
          if (!hasDocumentationDeliverables) issues.push('Aucun livrable de documentation prévu');
          
          return {
            ruleId: 'documentation_requirements',
            status: issues.length === 0 ? 'passed' : 'warning',
            message: issues.length === 0 ? 'Documentation planifiée' : 'Documentation à améliorer',
            details: issues.join('; '),
            suggestions: [
              'Ajouter des tâches de documentation',
              'Planifier la mise à jour de la documentation existante',
              'Inclure la documentation utilisateur'
            ],
            impact: 'low',
            blocksDeployment: false
          };
        }
      }
    ];

    rules.forEach(rule => {
      this.validationRules.set(rule.id, rule);
    });
  }

  /**
   * Valide une roadmap complète
   */
  async validateRoadmap(
    roadmap: RoadmapProject,
    validationType: 'pre_development' | 'sprint_completion' | 'pre_deployment' | 'post_deployment' = 'pre_development'
  ): Promise<RoadmapValidation> {
    const validationId = `validation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const validation: RoadmapValidation = {
      id: validationId,
      roadmapId: roadmap.id,
      status: 'pending',
      validationType,
      validationRules: Array.from(this.validationRules.values()),
      results: [],
      blockers: [],
      recommendations: [],
      approvalRequired: this.config.requireStakeholderApproval,
      stakeholderApprovals: this.generateStakeholderApprovals(roadmap),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.validations.set(validationId, validation);
    this.activeValidations.add(validationId);

    try {
      this.emit('validation:started', { validation, roadmap });
      
      validation.status = 'in_progress';
      
      // Exécuter toutes les règles de validation
      for (const rule of this.validationRules.values()) {
        if (this.shouldApplyRule(rule, validationType)) {
          const result = rule.validator(roadmap);
          validation.results.push(result);
          
          // Créer des blockers pour les erreurs critiques
          if (result.blocksDeployment) {
            validation.blockers.push({
              id: `blocker_${result.ruleId}_${Date.now()}`,
              type: this.getBlockerType(result.ruleId),
              severity: result.impact === 'critical' ? 'critical' : 'error',
              description: result.message,
              resolution: result.suggestions.join('; '),
              owner: 'project-manager',
              resolved: false
            });
          }
        }
      }
      
      // Générer des recommandations
      validation.recommendations = this.generateRecommendations(validation.results);
      
      // Déterminer le statut final
      const hasBlockers = validation.blockers.filter(b => !b.resolved).length > 0;
      const hasCriticalErrors = validation.results.some(r => r.status === 'failed' && r.impact === 'critical');
      
      if (hasBlockers || hasCriticalErrors) {
        validation.status = 'blocked';
      } else if (validation.approvalRequired && validation.stakeholderApprovals.some(a => a.required && a.status === 'pending')) {
        validation.status = 'pending';
      } else {
        validation.status = 'approved';
      }
      
      validation.updatedAt = new Date();
      
      this.emit('validation:completed', { validation, roadmap });
      
      return validation;

    } catch (error) {
      validation.status = 'rejected';
      validation.updatedAt = new Date();
      this.emit('validation:error', { validation, roadmap, error });
      throw error;

    } finally {
      this.activeValidations.delete(validationId);
    }
  }

  /**
   * Vérifie si une roadmap peut être déployée
   */
  canDeploy(roadmapId: string): { canDeploy: boolean; reasons: string[] } {
    const roadmapValidations = Array.from(this.validations.values())
      .filter(v => v.roadmapId === roadmapId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    
    if (roadmapValidations.length === 0) {
      return {
        canDeploy: false,
        reasons: ['Aucune validation trouvée pour cette roadmap']
      };
    }
    
    const latestValidation = roadmapValidations[0];
    const reasons: string[] = [];
    
    // Vérifier le statut de validation
    if (latestValidation.status !== 'approved') {
      reasons.push(`Statut de validation: ${latestValidation.status}`);
    }
    
    // Vérifier les blockers non résolus
    const unresolvedBlockers = latestValidation.blockers.filter(b => !b.resolved);
    if (unresolvedBlockers.length > 0) {
      reasons.push(`${unresolvedBlockers.length} blockers non résolus`);
    }
    
    // Vérifier les approbations requises
    const pendingApprovals = latestValidation.stakeholderApprovals.filter(a => a.required && a.status === 'pending');
    if (pendingApprovals.length > 0) {
      reasons.push(`${pendingApprovals.length} approbations en attente`);
    }
    
    // Vérifier les erreurs critiques
    const criticalErrors = latestValidation.results.filter(r => r.status === 'failed' && r.impact === 'critical');
    if (criticalErrors.length > 0) {
      reasons.push(`${criticalErrors.length} erreurs critiques`);
    }
    
    return {
      canDeploy: reasons.length === 0,
      reasons
    };
  }

  /**
   * Approuve une validation par un stakeholder
   */
  async approveValidation(validationId: string, stakeholder: string, comments?: string): Promise<void> {
    const validation = this.validations.get(validationId);
    if (!validation) {
      throw new Error(`Validation non trouvée: ${validationId}`);
    }
    
    const approval = validation.stakeholderApprovals.find(a => a.stakeholder === stakeholder);
    if (!approval) {
      throw new Error(`Stakeholder non autorisé: ${stakeholder}`);
    }
    
    approval.status = 'approved';
    approval.approvedAt = new Date();
    approval.comments = comments;
    
    // Vérifier si toutes les approbations requises sont obtenues
    const allRequiredApproved = validation.stakeholderApprovals
      .filter(a => a.required)
      .every(a => a.status === 'approved');
    
    if (allRequiredApproved && validation.status === 'pending') {
      validation.status = 'approved';
      validation.validatedAt = new Date();
    }
    
    validation.updatedAt = new Date();
    
    this.emit('validation:approved', { validation, stakeholder, comments });
  }

  /**
   * Résout un blocker
   */
  async resolveBlocker(validationId: string, blockerId: string, resolvedBy: string): Promise<void> {
    const validation = this.validations.get(validationId);
    if (!validation) {
      throw new Error(`Validation non trouvée: ${validationId}`);
    }
    
    const blocker = validation.blockers.find(b => b.id === blockerId);
    if (!blocker) {
      throw new Error(`Blocker non trouvé: ${blockerId}`);
    }
    
    blocker.resolved = true;
    blocker.resolvedAt = new Date();
    blocker.resolvedBy = resolvedBy;
    
    // Vérifier si tous les blockers sont résolus
    const allBlockersResolved = validation.blockers.every(b => b.resolved);
    
    if (allBlockersResolved && validation.status === 'blocked') {
      validation.status = validation.approvalRequired ? 'pending' : 'approved';
    }
    
    validation.updatedAt = new Date();
    
    this.emit('blocker:resolved', { validation, blocker, resolvedBy });
  }

  // Méthodes utilitaires privées
  private shouldApplyRule(rule: ValidationRule, validationType: string): boolean {
    // Toutes les règles s'appliquent pour la validation pré-développement
    if (validationType === 'pre_development') return true;
    
    // Règles spécifiques selon le type de validation
    switch (validationType) {
      case 'sprint_completion':
        return ['completeness', 'quality'].includes(rule.category);
      case 'pre_deployment':
        return ['security', 'compliance', 'quality'].includes(rule.category);
      case 'post_deployment':
        return ['performance', 'documentation'].includes(rule.category);
      default:
        return true;
    }
  }

  private getBlockerType(ruleId: string): ValidationBlocker['type'] {
    switch (ruleId) {
      case 'roadmap_completeness':
      case 'deliverable_validation':
        return 'missing_deliverable';
      case 'sprint_structure':
      case 'task_completeness':
        return 'incomplete_sprint';
      case 'security_requirements':
        return 'security_issue';
      default:
        return 'failed_test';
    }
  }

  private generateStakeholderApprovals(roadmap: RoadmapProject): StakeholderApproval[] {
    const approvals: StakeholderApproval[] = [];
    
    // Approbations par défaut
    if (this.config.requiredApprovers.length > 0) {
      this.config.requiredApprovers.forEach(approver => {
        approvals.push({
          stakeholder: approver,
          role: 'approver',
          required: true,
          status: 'pending'
        });
      });
    }
    
    // Approbations spécifiques selon le type de projet
    if (roadmap.type === 'security') {
      approvals.push({
        stakeholder: 'security-officer',
        role: 'Security Officer',
        required: true,
        status: 'pending'
      });
    }
    
    if (roadmap.priority === 'critical') {
      approvals.push({
        stakeholder: 'cto',
        role: 'CTO',
        required: true,
        status: 'pending'
      });
    }
    
    return approvals;
  }

  private generateRecommendations(results: ValidationResult[]): string[] {
    const recommendations: string[] = [];
    
    const failedResults = results.filter(r => r.status === 'failed');
    const warningResults = results.filter(r => r.status === 'warning');
    
    if (failedResults.length > 0) {
      recommendations.push(`Corriger ${failedResults.length} erreurs critiques avant de continuer`);
    }
    
    if (warningResults.length > 0) {
      recommendations.push(`Examiner ${warningResults.length} avertissements pour améliorer la qualité`);
    }
    
    // Recommandations spécifiques
    const securityIssues = results.filter(r => r.ruleId === 'security_requirements' && r.status !== 'passed');
    if (securityIssues.length > 0) {
      recommendations.push('Renforcer les aspects sécurité du projet');
    }
    
    const documentationIssues = results.filter(r => r.ruleId === 'documentation_requirements' && r.status !== 'passed');
    if (documentationIssues.length > 0) {
      recommendations.push('Améliorer la planification de la documentation');
    }
    
    return recommendations;
  }

  // Getters
  getValidations(): RoadmapValidation[] {
    return Array.from(this.validations.values());
  }

  getValidation(id: string): RoadmapValidation | undefined {
    return this.validations.get(id);
  }

  getValidationRules(): ValidationRule[] {
    return Array.from(this.validationRules.values());
  }

  getActiveValidations(): RoadmapValidation[] {
    return Array.from(this.validations.values()).filter(v => 
      this.activeValidations.has(v.id)
    );
  }
}
