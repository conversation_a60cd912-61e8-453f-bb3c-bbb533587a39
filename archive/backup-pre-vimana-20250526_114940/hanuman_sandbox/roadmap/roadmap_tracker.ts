import { EventEmitter } from 'events';
import { RoadmapProject, RoadmapSprint, RoadmapTask } from './roadmap_generator';
import { RoadmapValidation } from './roadmap_validator_agent';

/**
 * Système de Suivi et Monitoring des Roadmaps
 * Suit l'avancement des projets et sprints en temps réel
 */

// Types pour le suivi
export interface RoadmapProgress {
  roadmapId: string;
  overallProgress: number; // 0-100
  currentSprint: number;
  sprintProgress: SprintProgress[];
  milestones: Milestone[];
  metrics: ProgressMetrics;
  timeline: TimelineEvent[];
  alerts: ProgressAlert[];
  lastUpdated: Date;
}

export interface SprintProgress {
  sprintId: string;
  sprintNumber: number;
  status: 'not_started' | 'in_progress' | 'completed' | 'blocked' | 'cancelled';
  progress: number; // 0-100
  startDate?: Date;
  endDate?: Date;
  actualStartDate?: Date;
  actualEndDate?: Date;
  estimatedCompletion?: Date;
  tasksCompleted: number;
  tasksTotal: number;
  deliverablesCompleted: number;
  deliverablesTotal: number;
  blockers: string[];
  velocity: number; // tâches par jour
  burndownData: BurndownPoint[];
}

export interface BurndownPoint {
  date: Date;
  remainingTasks: number;
  remainingHours: number;
  idealRemaining: number;
}

export interface Milestone {
  id: string;
  name: string;
  description: string;
  targetDate: Date;
  actualDate?: Date;
  status: 'upcoming' | 'at_risk' | 'completed' | 'missed';
  dependencies: string[];
  criticalPath: boolean;
}

export interface ProgressMetrics {
  velocityTrend: number; // tâches par jour
  qualityScore: number; // 0-100
  riskScore: number; // 0-100
  scheduleVariance: number; // jours d'avance/retard
  budgetVariance?: number; // % d'écart budget
  teamEfficiency: number; // 0-100
  blockerResolutionTime: number; // heures moyennes
  changeRequestCount: number;
  defectRate: number; // défauts par 100 tâches
}

export interface TimelineEvent {
  id: string;
  timestamp: Date;
  type: 'sprint_started' | 'sprint_completed' | 'task_completed' | 'blocker_added' | 'blocker_resolved' | 'milestone_reached' | 'risk_identified' | 'change_request';
  description: string;
  impact: 'low' | 'medium' | 'high' | 'critical';
  relatedEntity: string; // ID du sprint, tâche, etc.
  metadata?: any;
}

export interface ProgressAlert {
  id: string;
  type: 'schedule_delay' | 'quality_issue' | 'blocker_critical' | 'milestone_at_risk' | 'velocity_drop' | 'budget_overrun';
  severity: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  description: string;
  recommendation: string;
  createdAt: Date;
  acknowledged: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
  resolved: boolean;
  resolvedAt?: Date;
}

export interface TrackerConfig {
  enableRealTimeTracking: boolean;
  alertThresholds: {
    scheduleDelayDays: number;
    velocityDropPercent: number;
    qualityScoreMin: number;
    blockerResolutionHours: number;
  };
  updateInterval: number; // millisecondes
  enablePredictiveAnalytics: boolean;
  enableAutomaticAlerts: boolean;
}

export class RoadmapTracker extends EventEmitter {
  private progressData: Map<string, RoadmapProgress> = new Map();
  private config: TrackerConfig;
  private updateTimer?: NodeJS.Timeout;
  private activeProjects: Set<string> = new Set();

  constructor(config: TrackerConfig) {
    super();
    this.config = config;
    
    if (config.enableRealTimeTracking) {
      this.startRealTimeTracking();
    }
  }

  /**
   * Démarre le suivi en temps réel
   */
  private startRealTimeTracking(): void {
    this.updateTimer = setInterval(() => {
      this.updateAllProgress();
    }, this.config.updateInterval);
  }

  /**
   * Arrête le suivi en temps réel
   */
  stopRealTimeTracking(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = undefined;
    }
  }

  /**
   * Initialise le suivi d'une roadmap
   */
  async startTracking(roadmap: RoadmapProject): Promise<RoadmapProgress> {
    const progress: RoadmapProgress = {
      roadmapId: roadmap.id,
      overallProgress: 0,
      currentSprint: 1,
      sprintProgress: this.initializeSprintProgress(roadmap.sprints),
      milestones: this.generateMilestones(roadmap),
      metrics: this.initializeMetrics(),
      timeline: [
        {
          id: `timeline_start_${Date.now()}`,
          timestamp: new Date(),
          type: 'sprint_started',
          description: `Démarrage du suivi de la roadmap: ${roadmap.name}`,
          impact: 'medium',
          relatedEntity: roadmap.id
        }
      ],
      alerts: [],
      lastUpdated: new Date()
    };

    this.progressData.set(roadmap.id, progress);
    this.activeProjects.add(roadmap.id);

    this.emit('tracking:started', { roadmap, progress });
    return progress;
  }

  /**
   * Met à jour le progrès d'une tâche
   */
  async updateTaskProgress(
    roadmapId: string,
    sprintId: string,
    taskId: string,
    status: 'todo' | 'in_progress' | 'review' | 'testing' | 'done' | 'blocked',
    actualHours?: number
  ): Promise<void> {
    const progress = this.progressData.get(roadmapId);
    if (!progress) {
      throw new Error(`Roadmap non suivie: ${roadmapId}`);
    }

    const sprintProgress = progress.sprintProgress.find(sp => sp.sprintId === sprintId);
    if (!sprintProgress) {
      throw new Error(`Sprint non trouvé: ${sprintId}`);
    }

    // Mettre à jour les métriques du sprint
    if (status === 'done') {
      sprintProgress.tasksCompleted++;
      
      // Ajouter un événement timeline
      progress.timeline.push({
        id: `timeline_task_${Date.now()}`,
        timestamp: new Date(),
        type: 'task_completed',
        description: `Tâche complétée dans le sprint ${sprintProgress.sprintNumber}`,
        impact: 'low',
        relatedEntity: taskId
      });
    }

    // Recalculer le progrès du sprint
    sprintProgress.progress = (sprintProgress.tasksCompleted / sprintProgress.tasksTotal) * 100;

    // Mettre à jour les données de burndown
    this.updateBurndownData(sprintProgress);

    // Recalculer le progrès global
    this.calculateOverallProgress(progress);

    // Vérifier les alertes
    await this.checkAlerts(progress);

    progress.lastUpdated = new Date();

    this.emit('task:updated', { roadmapId, sprintId, taskId, status, progress });
  }

  /**
   * Marque un sprint comme démarré
   */
  async startSprint(roadmapId: string, sprintId: string): Promise<void> {
    const progress = this.progressData.get(roadmapId);
    if (!progress) {
      throw new Error(`Roadmap non suivie: ${roadmapId}`);
    }

    const sprintProgress = progress.sprintProgress.find(sp => sp.sprintId === sprintId);
    if (!sprintProgress) {
      throw new Error(`Sprint non trouvé: ${sprintId}`);
    }

    sprintProgress.status = 'in_progress';
    sprintProgress.actualStartDate = new Date();

    // Mettre à jour le sprint courant
    progress.currentSprint = sprintProgress.sprintNumber;

    // Ajouter un événement timeline
    progress.timeline.push({
      id: `timeline_sprint_start_${Date.now()}`,
      timestamp: new Date(),
      type: 'sprint_started',
      description: `Démarrage du Sprint ${sprintProgress.sprintNumber}`,
      impact: 'medium',
      relatedEntity: sprintId
    });

    progress.lastUpdated = new Date();

    this.emit('sprint:started', { roadmapId, sprintId, progress });
  }

  /**
   * Marque un sprint comme terminé
   */
  async completeSprint(roadmapId: string, sprintId: string): Promise<void> {
    const progress = this.progressData.get(roadmapId);
    if (!progress) {
      throw new Error(`Roadmap non suivie: ${roadmapId}`);
    }

    const sprintProgress = progress.sprintProgress.find(sp => sp.sprintId === sprintId);
    if (!sprintProgress) {
      throw new Error(`Sprint non trouvé: ${sprintId}`);
    }

    sprintProgress.status = 'completed';
    sprintProgress.actualEndDate = new Date();
    sprintProgress.progress = 100;

    // Calculer la vélocité du sprint
    if (sprintProgress.actualStartDate && sprintProgress.actualEndDate) {
      const durationDays = (sprintProgress.actualEndDate.getTime() - sprintProgress.actualStartDate.getTime()) / (1000 * 60 * 60 * 24);
      sprintProgress.velocity = sprintProgress.tasksCompleted / durationDays;
    }

    // Mettre à jour les métriques globales
    this.updateMetrics(progress, sprintProgress);

    // Ajouter un événement timeline
    progress.timeline.push({
      id: `timeline_sprint_complete_${Date.now()}`,
      timestamp: new Date(),
      type: 'sprint_completed',
      description: `Sprint ${sprintProgress.sprintNumber} terminé`,
      impact: 'high',
      relatedEntity: sprintId
    });

    // Vérifier les milestones
    await this.checkMilestones(progress);

    // Recalculer le progrès global
    this.calculateOverallProgress(progress);

    progress.lastUpdated = new Date();

    this.emit('sprint:completed', { roadmapId, sprintId, progress });
  }

  /**
   * Ajoute un blocker
   */
  async addBlocker(roadmapId: string, sprintId: string, description: string, severity: 'low' | 'medium' | 'high' | 'critical'): Promise<void> {
    const progress = this.progressData.get(roadmapId);
    if (!progress) {
      throw new Error(`Roadmap non suivie: ${roadmapId}`);
    }

    const sprintProgress = progress.sprintProgress.find(sp => sp.sprintId === sprintId);
    if (!sprintProgress) {
      throw new Error(`Sprint non trouvé: ${sprintId}`);
    }

    sprintProgress.blockers.push(description);

    // Ajouter une alerte si critique
    if (severity === 'critical') {
      progress.alerts.push({
        id: `alert_blocker_${Date.now()}`,
        type: 'blocker_critical',
        severity: 'critical',
        title: 'Blocker Critique Détecté',
        description: `Blocker critique dans le Sprint ${sprintProgress.sprintNumber}: ${description}`,
        recommendation: 'Résoudre immédiatement ce blocker pour éviter les retards',
        createdAt: new Date(),
        acknowledged: false,
        resolved: false
      });
    }

    // Ajouter un événement timeline
    progress.timeline.push({
      id: `timeline_blocker_${Date.now()}`,
      timestamp: new Date(),
      type: 'blocker_added',
      description: `Blocker ajouté: ${description}`,
      impact: severity === 'critical' ? 'critical' : 'medium',
      relatedEntity: sprintId
    });

    progress.lastUpdated = new Date();

    this.emit('blocker:added', { roadmapId, sprintId, description, severity, progress });
  }

  /**
   * Résout un blocker
   */
  async resolveBlocker(roadmapId: string, sprintId: string, blockerDescription: string): Promise<void> {
    const progress = this.progressData.get(roadmapId);
    if (!progress) {
      throw new Error(`Roadmap non suivie: ${roadmapId}`);
    }

    const sprintProgress = progress.sprintProgress.find(sp => sp.sprintId === sprintId);
    if (!sprintProgress) {
      throw new Error(`Sprint non trouvé: ${sprintId}`);
    }

    const blockerIndex = sprintProgress.blockers.indexOf(blockerDescription);
    if (blockerIndex > -1) {
      sprintProgress.blockers.splice(blockerIndex, 1);
    }

    // Résoudre les alertes liées
    progress.alerts.forEach(alert => {
      if (alert.type === 'blocker_critical' && alert.description.includes(blockerDescription)) {
        alert.resolved = true;
        alert.resolvedAt = new Date();
      }
    });

    // Ajouter un événement timeline
    progress.timeline.push({
      id: `timeline_blocker_resolved_${Date.now()}`,
      timestamp: new Date(),
      type: 'blocker_resolved',
      description: `Blocker résolu: ${blockerDescription}`,
      impact: 'medium',
      relatedEntity: sprintId
    });

    progress.lastUpdated = new Date();

    this.emit('blocker:resolved', { roadmapId, sprintId, blockerDescription, progress });
  }

  /**
   * Génère un rapport de progrès
   */
  generateProgressReport(roadmapId: string): ProgressReport {
    const progress = this.progressData.get(roadmapId);
    if (!progress) {
      throw new Error(`Roadmap non suivie: ${roadmapId}`);
    }

    const completedSprints = progress.sprintProgress.filter(sp => sp.status === 'completed').length;
    const totalSprints = progress.sprintProgress.length;
    const currentSprint = progress.sprintProgress.find(sp => sp.status === 'in_progress');
    const upcomingMilestones = progress.milestones.filter(m => m.status === 'upcoming').slice(0, 3);
    const criticalAlerts = progress.alerts.filter(a => a.severity === 'critical' && !a.resolved);

    return {
      roadmapId,
      summary: {
        overallProgress: progress.overallProgress,
        sprintsCompleted: completedSprints,
        totalSprints,
        currentSprintNumber: progress.currentSprint,
        estimatedCompletion: this.calculateEstimatedCompletion(progress)
      },
      currentStatus: {
        currentSprint: currentSprint ? {
          number: currentSprint.sprintNumber,
          progress: currentSprint.progress,
          blockers: currentSprint.blockers.length,
          velocity: currentSprint.velocity
        } : null,
        upcomingMilestones,
        criticalAlerts: criticalAlerts.length
      },
      metrics: progress.metrics,
      trends: this.calculateTrends(progress),
      recommendations: this.generateRecommendations(progress)
    };
  }

  // Méthodes utilitaires privées
  private initializeSprintProgress(sprints: RoadmapSprint[]): SprintProgress[] {
    return sprints.map((sprint, index) => ({
      sprintId: sprint.id,
      sprintNumber: index + 1,
      status: 'not_started',
      progress: 0,
      tasksCompleted: 0,
      tasksTotal: sprint.tasks.length,
      deliverablesCompleted: 0,
      deliverablesTotal: sprint.deliverables.length,
      blockers: [],
      velocity: 0,
      burndownData: []
    }));
  }

  private generateMilestones(roadmap: RoadmapProject): Milestone[] {
    const milestones: Milestone[] = [];
    let currentDate = new Date();

    roadmap.sprints.forEach((sprint, index) => {
      currentDate = new Date(currentDate.getTime() + sprint.duration * 24 * 60 * 60 * 1000);
      
      milestones.push({
        id: `milestone_sprint_${index + 1}`,
        name: `Fin du ${sprint.name}`,
        description: `Completion du sprint ${index + 1}`,
        targetDate: new Date(currentDate),
        status: 'upcoming',
        dependencies: index > 0 ? [`milestone_sprint_${index}`] : [],
        criticalPath: true
      });
    });

    return milestones;
  }

  private initializeMetrics(): ProgressMetrics {
    return {
      velocityTrend: 0,
      qualityScore: 100,
      riskScore: 0,
      scheduleVariance: 0,
      teamEfficiency: 100,
      blockerResolutionTime: 0,
      changeRequestCount: 0,
      defectRate: 0
    };
  }

  private updateBurndownData(sprintProgress: SprintProgress): void {
    const today = new Date();
    const remainingTasks = sprintProgress.tasksTotal - sprintProgress.tasksCompleted;
    
    // Calculer l'idéal basé sur la durée du sprint
    const sprintDuration = 10; // jours par défaut
    const daysPassed = sprintProgress.actualStartDate 
      ? Math.floor((today.getTime() - sprintProgress.actualStartDate.getTime()) / (1000 * 60 * 60 * 24))
      : 0;
    const idealRemaining = Math.max(0, sprintProgress.tasksTotal - (sprintProgress.tasksTotal / sprintDuration) * daysPassed);

    sprintProgress.burndownData.push({
      date: new Date(today),
      remainingTasks,
      remainingHours: remainingTasks * 8, // estimation
      idealRemaining
    });

    // Garder seulement les 30 derniers points
    if (sprintProgress.burndownData.length > 30) {
      sprintProgress.burndownData = sprintProgress.burndownData.slice(-30);
    }
  }

  private calculateOverallProgress(progress: RoadmapProgress): void {
    const totalSprints = progress.sprintProgress.length;
    const completedSprints = progress.sprintProgress.filter(sp => sp.status === 'completed').length;
    const currentSprintProgress = progress.sprintProgress.find(sp => sp.status === 'in_progress')?.progress || 0;
    
    progress.overallProgress = ((completedSprints + currentSprintProgress / 100) / totalSprints) * 100;
  }

  private async checkAlerts(progress: RoadmapProgress): Promise<void> {
    if (!this.config.enableAutomaticAlerts) return;

    // Vérifier les retards de planning
    const currentSprint = progress.sprintProgress.find(sp => sp.status === 'in_progress');
    if (currentSprint && currentSprint.estimatedCompletion) {
      const delay = (currentSprint.estimatedCompletion.getTime() - Date.now()) / (1000 * 60 * 60 * 24);
      if (delay > this.config.alertThresholds.scheduleDelayDays) {
        progress.alerts.push({
          id: `alert_delay_${Date.now()}`,
          type: 'schedule_delay',
          severity: 'warning',
          title: 'Retard de Planning Détecté',
          description: `Le sprint courant risque d'avoir ${Math.ceil(delay)} jours de retard`,
          recommendation: 'Revoir la planification et identifier les optimisations possibles',
          createdAt: new Date(),
          acknowledged: false,
          resolved: false
        });
      }
    }

    // Vérifier la chute de vélocité
    if (progress.metrics.velocityTrend < -this.config.alertThresholds.velocityDropPercent) {
      progress.alerts.push({
        id: `alert_velocity_${Date.now()}`,
        type: 'velocity_drop',
        severity: 'warning',
        title: 'Chute de Vélocité',
        description: `La vélocité a chuté de ${Math.abs(progress.metrics.velocityTrend)}%`,
        recommendation: 'Analyser les causes de la baisse de productivité',
        createdAt: new Date(),
        acknowledged: false,
        resolved: false
      });
    }
  }

  private async checkMilestones(progress: RoadmapProgress): Promise<void> {
    const today = new Date();
    
    progress.milestones.forEach(milestone => {
      if (milestone.status === 'upcoming' && milestone.targetDate <= today) {
        milestone.status = 'completed';
        milestone.actualDate = today;
        
        progress.timeline.push({
          id: `timeline_milestone_${Date.now()}`,
          timestamp: new Date(),
          type: 'milestone_reached',
          description: `Milestone atteint: ${milestone.name}`,
          impact: 'high',
          relatedEntity: milestone.id
        });
      }
    });
  }

  private updateMetrics(progress: RoadmapProgress, completedSprint: SprintProgress): void {
    // Mettre à jour la vélocité moyenne
    const completedSprints = progress.sprintProgress.filter(sp => sp.status === 'completed');
    if (completedSprints.length > 0) {
      const avgVelocity = completedSprints.reduce((sum, sp) => sum + sp.velocity, 0) / completedSprints.length;
      progress.metrics.velocityTrend = avgVelocity;
    }

    // Mettre à jour l'efficacité de l'équipe
    const totalTasks = progress.sprintProgress.reduce((sum, sp) => sum + sp.tasksTotal, 0);
    const completedTasks = progress.sprintProgress.reduce((sum, sp) => sum + sp.tasksCompleted, 0);
    progress.metrics.teamEfficiency = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 100;

    // Mettre à jour le score de risque basé sur les blockers
    const totalBlockers = progress.sprintProgress.reduce((sum, sp) => sum + sp.blockers.length, 0);
    progress.metrics.riskScore = Math.min(100, totalBlockers * 10);
  }

  private updateAllProgress(): void {
    for (const roadmapId of this.activeProjects) {
      const progress = this.progressData.get(roadmapId);
      if (progress) {
        this.calculateOverallProgress(progress);
        this.checkAlerts(progress);
        progress.lastUpdated = new Date();
      }
    }
  }

  private calculateEstimatedCompletion(progress: RoadmapProgress): Date {
    const remainingSprints = progress.sprintProgress.filter(sp => sp.status === 'not_started' || sp.status === 'in_progress');
    const avgSprintDuration = 10; // jours
    const estimatedDays = remainingSprints.length * avgSprintDuration;
    
    return new Date(Date.now() + estimatedDays * 24 * 60 * 60 * 1000);
  }

  private calculateTrends(progress: RoadmapProgress): any {
    // Calculer les tendances basées sur l'historique
    return {
      velocityTrend: progress.metrics.velocityTrend,
      qualityTrend: 0, // À implémenter
      scheduleVariance: progress.metrics.scheduleVariance
    };
  }

  private generateRecommendations(progress: RoadmapProgress): string[] {
    const recommendations: string[] = [];
    
    if (progress.metrics.riskScore > 50) {
      recommendations.push('Réduire le nombre de blockers actifs');
    }
    
    if (progress.metrics.velocityTrend < 0) {
      recommendations.push('Analyser et améliorer la vélocité de l\'équipe');
    }
    
    if (progress.overallProgress < 50 && progress.currentSprint > progress.sprintProgress.length / 2) {
      recommendations.push('Revoir la planification pour respecter les délais');
    }
    
    return recommendations;
  }

  // Getters
  getProgress(roadmapId: string): RoadmapProgress | undefined {
    return this.progressData.get(roadmapId);
  }

  getAllProgress(): RoadmapProgress[] {
    return Array.from(this.progressData.values());
  }

  getActiveProjects(): string[] {
    return Array.from(this.activeProjects);
  }
}

// Interfaces supplémentaires
export interface ProgressReport {
  roadmapId: string;
  summary: {
    overallProgress: number;
    sprintsCompleted: number;
    totalSprints: number;
    currentSprintNumber: number;
    estimatedCompletion: Date;
  };
  currentStatus: {
    currentSprint: {
      number: number;
      progress: number;
      blockers: number;
      velocity: number;
    } | null;
    upcomingMilestones: Milestone[];
    criticalAlerts: number;
  };
  metrics: ProgressMetrics;
  trends: any;
  recommendations: string[];
}
