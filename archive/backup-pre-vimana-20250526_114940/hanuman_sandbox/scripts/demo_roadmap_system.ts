#!/usr/bin/env ts-node

/**
 * Script de Démonstration - Système de Roadmap Obligatoire
 * Démontre le processus complet de création, validation et contrôle de déploiement
 */

import { RoadmapGenerator, RoadmapGeneratorConfig, ProjectAnalysisInput } from '../roadmap/roadmap_generator';
import { RoadmapValidatorAgent, ValidationConfig } from '../roadmap/roadmap_validator_agent';
import { RoadmapTracker, TrackerConfig } from '../roadmap/roadmap_tracker';
import { DeploymentGateKeeper, GateKeeperConfig, DeploymentRequest } from '../roadmap/deployment_gate_keeper';

// Configuration des couleurs pour l'affichage
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message: string, color: keyof typeof colors = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title: string) {
  console.log('\n' + '='.repeat(70));
  log(`🗺️ ${title}`, 'cyan');
  console.log('='.repeat(70));
}

function logSubSection(title: string) {
  console.log('\n' + '-'.repeat(50));
  log(`📋 ${title}`, 'yellow');
  console.log('-'.repeat(50));
}

async function delay(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function demonstrateRoadmapSystem() {
  logSection('DÉMONSTRATION SYSTÈME DE ROADMAP OBLIGATOIRE');
  
  log('🚀 Initialisation du système de roadmap...', 'bright');
  
  // Configuration des composants
  const generatorConfig: RoadmapGeneratorConfig = {
    defaultCreator: 'demo-system',
    defaultRiskOwner: 'project-manager',
    enableAutoApproval: false,
    requireStakeholderApproval: true,
    maxProjectDuration: 90,
    estimationBuffer: 1.2
  };

  const validatorConfig: ValidationConfig = {
    enableAutomaticValidation: true,
    requireStakeholderApproval: true,
    blockDeploymentOnErrors: true,
    allowWarningOverride: false,
    validationTimeout: 30000,
    requiredApprovers: ['project-manager', 'tech-lead'],
    criticalValidationRules: ['roadmap_completeness', 'security_requirements']
  };

  const trackerConfig: TrackerConfig = {
    enableRealTimeTracking: true,
    alertThresholds: {
      scheduleDelayDays: 2,
      velocityDropPercent: 20,
      qualityScoreMin: 80,
      blockerResolutionHours: 24
    },
    updateInterval: 5000,
    enablePredictiveAnalytics: true,
    enableAutomaticAlerts: true
  };

  const gateKeeperConfig: GateKeeperConfig = {
    enableStrictMode: true,
    allowEmergencyOverride: true,
    emergencyOverrideRoles: ['cto', 'security-officer'],
    requireRoadmapForProduction: true,
    requireValidationForStaging: true,
    requireProgressTracking: true,
    autoCreateRoadmapForMissingProjects: false,
    notificationChannels: ['email', 'slack']
  };

  // Initialisation des composants
  const roadmapGenerator = new RoadmapGenerator(generatorConfig);
  const roadmapValidator = new RoadmapValidatorAgent(validatorConfig);
  const roadmapTracker = new RoadmapTracker(trackerConfig);
  const deploymentGateKeeper = new DeploymentGateKeeper(
    gateKeeperConfig,
    roadmapValidator,
    roadmapTracker,
    roadmapGenerator
  );

  log('✅ Tous les composants initialisés avec succès!', 'green');

  // 1. Démonstration de la Génération de Roadmap
  logSubSection('Génération Automatique de Roadmap');
  
  log('📋 Création d\'un projet exemple...', 'blue');
  
  const projectInput: ProjectAnalysisInput = {
    name: 'Système de Chat Temps Réel',
    description: 'Implémentation d\'un système de chat en temps réel avec notifications push et historique des messages',
    type: 'feature',
    scope: ['frontend', 'backend', 'websockets', 'notifications'],
    technicalStack: ['React', 'TypeScript', 'Node.js', 'Socket.io', 'Redis', 'PostgreSQL'],
    integrations: ['user-service', 'notification-service', 'auth-service'],
    requirements: [
      'Chat en temps réel entre utilisateurs',
      'Historique persistant des messages',
      'Notifications push en temps réel',
      'Support des fichiers et médias',
      'Modération automatique du contenu',
      'Chiffrement end-to-end',
      'Interface responsive mobile et desktop'
    ],
    constraints: [
      'Performance: latence < 100ms',
      'Sécurité: chiffrement obligatoire',
      'Scalabilité: support 10k utilisateurs simultanés',
      'Conformité RGPD'
    ],
    team: ['dev-frontend', 'dev-backend', 'qa-engineer', 'security-expert', 'ux-designer']
  };

  const roadmap = await roadmapGenerator.generateRoadmap(projectInput);
  
  log(`✅ Roadmap générée: ${roadmap.name}`, 'green');
  log(`📊 ID: ${roadmap.id}`, 'cyan');
  log(`🎯 Type: ${roadmap.type}`, 'magenta');
  log(`⚡ Priorité: ${roadmap.priority}`, 'yellow');
  log(`🔧 Complexité: ${roadmap.complexity}`, 'blue');
  log(`📅 Durée estimée: ${roadmap.estimatedDuration} jours`, 'cyan');
  log(`🏃 Sprints: ${roadmap.sprints.length}`, 'magenta');
  log(`⚠️ Risques identifiés: ${roadmap.risks.length}`, 'yellow');

  // Afficher les sprints
  log('\n📋 Sprints planifiés:', 'bright');
  roadmap.sprints.forEach((sprint, index) => {
    log(`  ${index + 1}. ${sprint.name} (${sprint.duration} jours)`, 'cyan');
    log(`     📝 ${sprint.description}`, 'magenta');
    log(`     🎯 Objectifs: ${sprint.objectives.length}`, 'yellow');
    log(`     📋 Tâches: ${sprint.tasks.length}`, 'blue');
    log(`     📦 Livrables: ${sprint.deliverables.length}`, 'green');
  });

  // 2. Démonstration de la Validation
  logSubSection('Validation Automatique de la Roadmap');
  
  log('🔍 Lancement de la validation automatique...', 'blue');
  
  const validation = await roadmapValidator.validateRoadmap(roadmap, 'pre_development');
  
  log(`📊 Validation créée: ${validation.id}`, 'green');
  log(`🔄 Statut: ${validation.status}`, validation.status === 'approved' ? 'green' : 'yellow');
  log(`📋 Règles appliquées: ${validation.validationRules.length}`, 'cyan');
  log(`✅ Résultats: ${validation.results.length}`, 'magenta');

  // Afficher les résultats de validation
  log('\n📊 Résultats de validation:', 'bright');
  validation.results.forEach(result => {
    const statusIcon = result.status === 'passed' ? '✅' : result.status === 'failed' ? '❌' : '⚠️';
    const statusColor = result.status === 'passed' ? 'green' : result.status === 'failed' ? 'red' : 'yellow';
    log(`  ${statusIcon} ${result.ruleId}: ${result.message}`, statusColor);
    if (result.suggestions.length > 0) {
      log(`     💡 Suggestions: ${result.suggestions.join(', ')}`, 'blue');
    }
  });

  // Afficher les blockers s'il y en a
  if (validation.blockers.length > 0) {
    log('\n🚫 Blockers détectés:', 'red');
    validation.blockers.forEach(blocker => {
      log(`  • ${blocker.description} (${blocker.severity})`, 'red');
      log(`    🔧 Résolution: ${blocker.resolution}`, 'yellow');
    });
  }

  // Simuler les approbations si nécessaire
  if (validation.status === 'pending') {
    log('\n✅ Simulation des approbations...', 'blue');
    for (const approval of validation.stakeholderApprovals) {
      if (approval.required && approval.status === 'pending') {
        await roadmapValidator.approveValidation(
          validation.id, 
          approval.stakeholder, 
          'Approbation automatique pour démonstration'
        );
        log(`  ✅ Approuvé par ${approval.stakeholder}`, 'green');
      }
    }
  }

  // 3. Démonstration du Suivi
  logSubSection('Démarrage du Suivi de Progrès');
  
  log('📈 Initialisation du suivi de progrès...', 'blue');
  
  const progress = await roadmapTracker.startTracking(roadmap);
  
  log(`📊 Suivi démarré pour: ${roadmap.name}`, 'green');
  log(`🎯 Progrès global: ${Math.round(progress.overallProgress)}%`, 'cyan');
  log(`🏃 Sprint actuel: ${progress.currentSprint}/${progress.sprintProgress.length}`, 'magenta');
  log(`📊 Métriques initialisées`, 'yellow');

  // Simuler quelques mises à jour de progrès
  log('\n🔄 Simulation de l\'avancement du projet...', 'blue');
  
  // Démarrer le premier sprint
  const firstSprint = roadmap.sprints[0];
  await roadmapTracker.startSprint(roadmap.id, firstSprint.id);
  log(`🚀 Sprint 1 démarré: ${firstSprint.name}`, 'green');

  // Simuler la completion de quelques tâches
  await delay(1000);
  await roadmapTracker.updateTaskProgress(roadmap.id, firstSprint.id, 'task_1', 'done');
  log(`✅ Tâche 1 complétée`, 'green');

  await delay(500);
  await roadmapTracker.updateTaskProgress(roadmap.id, firstSprint.id, 'task_2', 'in_progress');
  log(`🔄 Tâche 2 en cours`, 'yellow');

  // Ajouter un blocker pour démonstration
  await roadmapTracker.addBlocker(roadmap.id, firstSprint.id, 'Dépendance externe non disponible', 'medium');
  log(`⚠️ Blocker ajouté: Dépendance externe`, 'yellow');

  // 4. Démonstration du Contrôle de Déploiement
  logSubSection('Contrôle de Déploiement Automatique');
  
  log('🚪 Test du système de contrôle de déploiement...', 'blue');

  // Créer une demande de déploiement
  const deploymentRequest: DeploymentRequest = {
    id: `deploy_${Date.now()}`,
    projectId: 'chat-system',
    roadmapId: roadmap.id,
    environment: 'production',
    requestedBy: 'dev-team',
    requestedAt: new Date(),
    changes: [
      {
        type: 'feature',
        description: 'Nouveau système de chat temps réel',
        files: ['src/chat/', 'api/chat/', 'websocket/'],
        impact: 'high',
        tested: true,
        reviewed: true
      },
      {
        type: 'security',
        description: 'Implémentation du chiffrement end-to-end',
        files: ['src/crypto/', 'api/security/'],
        impact: 'critical',
        tested: true,
        reviewed: true
      }
    ],
    metadata: { approved: true }
  };

  log(`📋 Demande de déploiement créée: ${deploymentRequest.id}`, 'cyan');
  log(`🎯 Environnement: ${deploymentRequest.environment}`, 'magenta');
  log(`📦 Changements: ${deploymentRequest.changes.length}`, 'yellow');

  // Évaluer la demande
  const decision = await deploymentGateKeeper.evaluateDeployment(deploymentRequest);
  
  log(`\n📊 Décision de déploiement: ${decision.decision}`, 
    decision.decision === 'approved' ? 'green' : 
    decision.decision === 'conditional' ? 'yellow' : 'red');
  log(`🎯 Score global: ${decision.overallScore}/100`, 'cyan');

  // Afficher les résultats des portes
  log('\n🚪 Résultats des portes de contrôle:', 'bright');
  for (const [gateId, result] of decision.gateResults.entries()) {
    const statusIcon = result.passed ? '✅' : '❌';
    const statusColor = result.passed ? 'green' : 'red';
    log(`  ${statusIcon} ${gateId}: ${result.message}`, statusColor);
    if (result.blockers.length > 0) {
      log(`     🚫 Blockers: ${result.blockers.join(', ')}`, 'red');
    }
    if (result.warnings.length > 0) {
      log(`     ⚠️ Warnings: ${result.warnings.join(', ')}`, 'yellow');
    }
  }

  // Afficher les blockers globaux
  if (decision.blockers.length > 0) {
    log('\n🚫 Blockers de déploiement:', 'red');
    decision.blockers.forEach(blocker => {
      log(`  • ${blocker}`, 'red');
    });
  }

  // Afficher les recommandations
  if (decision.recommendations.length > 0) {
    log('\n💡 Recommandations:', 'blue');
    decision.recommendations.forEach(rec => {
      log(`  • ${rec}`, 'blue');
    });
  }

  // 5. Test du Système sans Roadmap
  logSubSection('Test de Blocage sans Roadmap');
  
  log('🚫 Test d\'un déploiement sans roadmap...', 'blue');

  const unauthorizedRequest: DeploymentRequest = {
    id: `deploy_unauthorized_${Date.now()}`,
    projectId: 'unknown-project',
    environment: 'production',
    requestedBy: 'rogue-developer',
    requestedAt: new Date(),
    changes: [
      {
        type: 'feature',
        description: 'Changement non documenté',
        files: ['src/unknown/'],
        impact: 'medium',
        tested: false,
        reviewed: false
      }
    ],
    metadata: {}
  };

  const unauthorizedDecision = await deploymentGateKeeper.evaluateDeployment(unauthorizedRequest);
  
  log(`📊 Décision: ${unauthorizedDecision.decision}`, 'red');
  log(`🚫 Blockers: ${unauthorizedDecision.blockers.length}`, 'red');
  
  if (unauthorizedDecision.blockers.length > 0) {
    log('🚫 Déploiement correctement bloqué:', 'green');
    unauthorizedDecision.blockers.forEach(blocker => {
      log(`  • ${blocker}`, 'red');
    });
  }

  // 6. Démonstration de l'Override d'Urgence
  logSubSection('Système d\'Override d\'Urgence');
  
  log('🆘 Test du système d\'override d\'urgence...', 'blue');

  try {
    const overriddenDecision = await deploymentGateKeeper.overrideDeployment(
      unauthorizedRequest.id,
      'cto',
      'Déploiement d\'urgence pour correction critique',
      'cto'
    );
    
    log(`✅ Override réussi par CTO`, 'green');
    log(`📊 Nouvelle décision: ${overriddenDecision.decision}`, 'green');
    log(`⏰ Expire le: ${overriddenDecision.expiresAt?.toLocaleString()}`, 'yellow');
    
  } catch (error) {
    log(`❌ Override échoué: ${error instanceof Error ? error.message : 'Erreur inconnue'}`, 'red');
  }

  // 7. Génération de Rapport de Conformité
  logSubSection('Rapport de Conformité');
  
  log('📊 Génération du rapport de conformité...', 'blue');

  const complianceReport = deploymentGateKeeper.generateComplianceReport({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 jours
    end: new Date()
  });

  log(`📋 Période: ${complianceReport.period.start.toLocaleDateString()} - ${complianceReport.period.end.toLocaleDateString()}`, 'cyan');
  log(`📊 Déploiements total: ${complianceReport.totalDeployments}`, 'magenta');
  log(`✅ Approuvés: ${complianceReport.approvedDeployments}`, 'green');
  log(`❌ Rejetés: ${complianceReport.rejectedDeployments}`, 'red');
  log(`🆘 Overrides: ${complianceReport.overriddenDeployments}`, 'yellow');
  log(`📈 Taux de conformité: ${complianceReport.complianceRate.toFixed(1)}%`, 'cyan');

  // 8. Résumé des Capacités
  logSubSection('Résumé des Capacités du Système');
  
  log('🎯 Fonctionnalités Implémentées:', 'bright');
  log('  ✅ Génération automatique de roadmaps détaillées', 'green');
  log('  🔍 Validation multi-niveaux avec règles configurables', 'green');
  log('  📈 Suivi en temps réel du progrès des projets', 'green');
  log('  🚪 Contrôle automatique des déploiements', 'green');
  log('  🚫 Blocage des déploiements non conformes', 'green');
  log('  🆘 Système d\'override d\'urgence contrôlé', 'green');
  log('  📊 Rapports de conformité et audit trail', 'green');
  log('  🔒 Sécurité et gouvernance renforcées', 'green');

  log('\n🔄 Processus Obligatoire:', 'bright');
  log('  1️⃣ Création systématique de roadmap pour chaque évolution', 'cyan');
  log('  2️⃣ Validation automatique avec approbations requises', 'cyan');
  log('  3️⃣ Suivi continu du progrès et des métriques', 'cyan');
  log('  4️⃣ Contrôle automatique avant chaque déploiement', 'cyan');
  log('  5️⃣ Blocage des déploiements non conformes', 'cyan');
  log('  6️⃣ Audit trail complet et rapports de conformité', 'cyan');

  log('\n🎯 Avantages Organisationnels:', 'bright');
  log('  📋 Processus standardisé et reproductible', 'blue');
  log('  🔒 Conformité réglementaire automatique', 'blue');
  log('  📊 Visibilité complète sur tous les projets', 'blue');
  log('  ⚡ Réduction des erreurs et des reprises', 'blue');
  log('  🚀 Déploiements sûrs et contrôlés', 'blue');
  log('  📈 Amélioration continue de la qualité', 'blue');

  logSection('DÉMONSTRATION TERMINÉE AVEC SUCCÈS');
  
  log('🎉 Le Système de Roadmap Obligatoire est opérationnel!', 'green');
  log('🗺️ Toutes les évolutions suivront désormais un processus structuré', 'cyan');
  log('🚫 Aucun déploiement non conforme ne sera autorisé', 'blue');
  log('📊 Conformité et gouvernance garanties', 'magenta');
  
  console.log('\n' + '🗺️'.repeat(25));
  log('SYSTÈME DE ROADMAP OBLIGATOIRE : IMPLÉMENTÉ ET OPÉRATIONNEL', 'bright');
  console.log('🗺️'.repeat(25));
}

// Gestion des erreurs
process.on('unhandledRejection', (reason, promise) => {
  log(`❌ Erreur non gérée: ${reason}`, 'red');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  log(`❌ Exception non capturée: ${error.message}`, 'red');
  process.exit(1);
});

// Exécution de la démonstration
if (require.main === module) {
  demonstrateRoadmapSystem().catch(error => {
    log(`❌ Erreur lors de la démonstration: ${error.message}`, 'red');
    process.exit(1);
  });
}

export { demonstrateRoadmapSystem };
