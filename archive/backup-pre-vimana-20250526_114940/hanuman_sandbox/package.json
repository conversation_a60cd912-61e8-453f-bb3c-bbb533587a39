{"name": "hanuman-sandbox", "version": "1.0.0", "description": "🏗️ Environnement de développement et test sécurisé pour les agents d'Hanuman", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "ts-node start_sandbox.ts", "start": "node dist/start_sandbox.js", "test": "ts-node tests/infrastructure_tests.ts", "test:infrastructure": "ts-node -e \"import('./tests/infrastructure_tests').then(m => m.runSandboxTests())\"", "test:security": "ts-node tests/security_tests.ts", "test:qa": "ts-node tests/qa_sprint5_tests.ts", "test:deployment": "ts-node tests/deployment_sprint6_tests.ts", "test:performance": "npm run test", "test:all": "npm run test:infrastructure && npm run test:security && npm run test:qa && npm run test:deployment", "demo": "ts-node start_sandbox.ts --demo", "demo:test-only": "ts-node start_sandbox.ts --test-only", "demo:sprint6": "ts-node scripts/demo_sprint6_deployment.ts", "demo:deployment": "ts-node scripts/demo_sprint6_deployment.ts", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "clean": "rm -rf dist", "watch": "tsc --watch", "docs": "typedoc --out docs src", "prepare": "npm run build"}, "keywords": ["hanuman", "sandbox", "development", "testing", "security", "containers", "isolation", "microservices", "ai-agents"], "author": "Hanuman Development Team", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "events": "^3.3.0"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0", "ts-node": "^10.9.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "typedoc": "^0.25.0"}, "peerDependencies": {"hanuman-orchestrator": "*"}, "files": ["dist/**/*", "README.md", "package.json"], "repository": {"type": "git", "url": "git+https://github.com/hanuman-project/sandbox.git"}, "bugs": {"url": "https://github.com/hanuman-project/sandbox/issues"}, "homepage": "https://github.com/hanuman-project/sandbox#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "config": {"sandbox": {"defaultSecurityLevel": "medium", "maxContainers": 50, "enableMonitoring": true, "enableSecurity": true}}}